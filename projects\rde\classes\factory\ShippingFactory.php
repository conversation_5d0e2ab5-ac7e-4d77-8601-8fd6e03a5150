<?php
  require_once(DIR_CLASSES . 'Shipping.php');

  class ShippingFactory extends Shipping {

    public static function calculate($basket_products, $countrycode) {

      $weight = 0;
      $hasA = false;
      $hasB = false;
      foreach ($basket_products as $prodvault) {
        $product = $prodvault["product"];

        $lengths = ProductOption::getOptionValue($product->id, "lengths");
        if ($countrycode == "nl" && $lengths != false && $lengths != "") {
          //standaard maat raamdorpelelement
          return Config::get("QUOTATION_DEFAULT_FREIGHTCOSTS");
        }

        if ($product->shipping_cat == "A") {
          $hasA = true;
        }
        elseif ($product->shipping_cat == "B") {
          $hasB = true;
        }
        $weight += $product->weight * $prodvault["size"];
      }

//      pd($weight);

      $send_costs = ShippingCountry::find_by(['country_code' => $countrycode]);
      if (!$send_costs) {
        ResponseHelper::redirectError("Het is niet mogelijk om vanuit dit land te bestellen: " . $countrycode);
      }
      if ($hasB) { //er is een pakket, dan pakket berekening gebruiken.
        if ($weight > 750000) { //gratis verzending bij 750 kg of meer
          return 0;
        }
        return $send_costs->getShippingCosts("b", $weight);
      }
      elseif ($hasA) { //er is enkel briefpost
        return $send_costs->getShippingCosts("a", $weight);
      }

      return 0;
    }

  }