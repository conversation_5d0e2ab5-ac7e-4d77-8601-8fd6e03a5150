<div class="contenttxt">
  <h1>Zoeken</h1>
  <?php if($searchstr==""): ?>
    U heeft geen zoekterm ingevoerd.
  <?php else: ?>
    <div id="search">
      <form method="get" action="/nl/zoeken">
        <div class="form-row">
          <input type="text" class="form-input col-4-3" name="topsearch" value="<?php echo escapeForInput($searchstr) ?>" placeholder="Zoeken..." autocomplete="off">
          <button class="btn col2">
            <i class="fa fa-search ft-white"></i>
          </button>
        </div>
      </form>
    </div>
    <br/>
    <br/>
    <?php if(count($items) == 0): ?>
      Er zijn geen pagina's gevonden met dit zoekwoord.
    <?php else: ?>
      <div id="search_result">
        <?php foreach ($items as $item): ?>
          <div onclick="location.href='<?php echo $item['link'] ?>';" class="search_item">
            <h2><?php echo $item['title']; ?></h2>
            <?php if(isset($item['desc']) && $item['desc']!=""): ?>
              <div class="teaser"><?php echo $item['desc']; ?>...<br/></div>
            <?php endif; ?>
            <a class="readmore" title="<?php echo escapeForInput($item['title']) ?>" href="<?php echo $item['link'] ?>">Lees meer op <i><?php echo ($item['title']) ?> <i
                  class="fa fa-chevron-right"></i> </i></a>
          </div>
        <?php endforeach ?>
      </div>
    <?php endif; ?>
  <?php endif; ?>

    <Br/><Br/>
    <?php echo __("Doorzoek de website met google:") ?>
     <style>
        input.gsc-input, .gsc-input-box, .gsc-input-box-hover, .gsc-input-box-focus, .gsc-search-button
        {
          box-sizing: content-box;
          line-height: normal;
        }
        .gsc-tabsArea div
        {
        overflow: visible;
      }
    .cse .gsc-control-cse, .gsc-control-cse {
      padding: 1em 0;
        }
        .gsc-adBlock {
      display: none;
    }
      </style>
    <script>
      (function() {
        var cx = '001002066207762068899:zp88ie77nre';
        var gcse = document.createElement('script');
        gcse.type = 'text/javascript';
        gcse.async = true;
        gcse.src = 'https://cse.google.com/cse.js?cx=' + cx;
        var s = document.getElementsByTagName('script')[0];
        s.parentNode.insertBefore(gcse, s);
      })();
    </script>
    <gcse:search></gcse:search>
</div>