<?php

  AppModel::loadBaseClass('BaseStoneColors');

  class StoneColorsModel extends BaseStoneColors {

    public function getFullname() {
      $name = $this->name;
      if ($this->short != "") {
        $name .= " - " . $this->short;
      }
      return $name;
    }

    public static function getColors($brandId = "") {
      if ($brandId != "") {
        return StoneColors::find_all_by(["brandId" => $brandId], "ORDER BY brandId, name");
      }
      return StoneColors::find_all("ORDER BY brandId, name");
    }

    /**
     * Ophalen kleuren
     * @param int[]|bool $brandIds
     * @param string|bool $type
     * @return StoneColors[]
     */
    public static function getDisplayColors($brandIds = false, $type = false): array {

      $query = "SELECT stone_colors.* FROM " . StoneColors::getTablename() . " ";
      $query .= "JOIN " . Stones::getTablename() . " ON stones.colorId = stone_colors.colorId ";
      $query .= "WHERE stone_colors.display='true' AND stones.display='true' ";
//    $query .= "AND stone_colors.colorId=21 ";
      if ($type !== false) {
        $query .= "AND stones.type='" . $type . "' ";
      }
      if ($brandIds !== false) {
        $query .= "AND stone_colors.brandId IN (" . implode(",", $brandIds) . ") ";
      }
      $query .= "GROUP BY stone_colors.colorId ";
      $query .= "ORDER BY stone_colors.glaced ASC,stone_colors.common DESC,stone_colors.name ASC ";
//    echo $query;

      $result = DBConn::db_link()->query($query);
      $sois = [];
      while ($row = $result->fetch_row()) {
        $soi = new StoneColors();
        $soi->hydrate($row);
        $soi->from_db = true;
        $sois[$soi->colorId] = $soi;
      }

      return $sois;

    }


    public function removeImageFile() {
      if ($this->filename != "") {
        if (file_exists(DIR_UPLOADS . 'stonecolors/' . $this->filename)) {
          unlink(DIR_UPLOADS . 'stonecolors/' . $this->filename);
        }
        $this->filename = null;
      }
    }


    public function destroy() {
      $this->removeImageFile();
      parent::destroy();
    }


  }