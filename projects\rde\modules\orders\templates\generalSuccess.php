<?php TemplateHelper::includePartial('_tabs.php', 'orders', compact(["quotation", "quotation_extra", "company", "sandboxuser"])); ?>
<style>
  #google_maps {
    display: block !important;
  }

  .form {
    width: 100%;
  }

  .general-container {
    display: flex;
    width: 100% !important;
    flex-wrap: wrap;
  }

  .small > td {
    height: 5% !important;
  }

  .default_table {
    width: 530px;
  }

  .default_table tr.dataTableRow td {
    padding: 4px;
  }

  input[type=text], select, textarea {
    width: 300px !important;
  }

  .dataTableHeadingRow > td {
    min-width: 115px;
    white-space: nowrap;
  }

  .default_table tr.dataTableRow td {
    border-bottom: 0;
  }

  #internNotes {
    height: 130px;
  }

  #productionNotes {
    height: 60px;
  }

  #customerNotes {
    height: 60px;
  }

  #vueapp input[type=checkbox] {
    width: 1em;
  }

  #vueapp input[type=number] {
    width: 7em;
    text-align: right;
  }

  .last-col {
    width: 50px !important;
    padding: 0 !important;
  }
  .price-col {
    padding-right: 15px !important;
    text-align: right;
  }
</style>

<div class="form">
  <form method="post" style="width: 75vw" class="">
  <div class="general-container">
    <div class="table-container" style="margin-right: 10px;">
      <table class="default_table dataTableNoBorder">
        <tr class="dataTableHeadingRow">
          <td>Eigenschap</td>
          <td>Waarde</td>
        </tr>
        <tr class="dataTableRow">
          <td class="head">Offerte datum</td>
          <td><?php echo date('d-m-Y', strtotime($quotation->quotationDate)); ?></td>
        </tr>
        <?php
          foreach ($form->getElements() as $key => $element):
            $element->renderRow();
            if ($key == 'payedFlag') break;
          endforeach;
        ?>
      </table>
    </div>

    <div class="table-container">
      <table style="height: fit-content;" class="default_table dataTableNoBorder">
        <tr class="dataTableHeadingRow">
          <td>Eigenschap</td>
          <td>Waarde</td>
        </tr>
        <?php
          foreach ($form->getElements() as $key => $element):
            if ($key == 'statusId' || $key == 'quotationDate' || $key == 'productionDate' || $key == 'dueDateWeek' || $key == 'dueDate' || $key == 'urgencyFlag' || $key == 'onHoldFlag' || $key == 'payedFlag') continue;
            $element->renderRow();
            if ($key == 'internNotes') break;
          endforeach;
        ?>
      </table>
    </div>

    <table style="margin-top: 10px; margin-left: 10px;">
      <tr class="dataTableRow">
        <td style="height: 310px">
          <?php if ($quotation->statusId == Status::STATUS_ORDER): ?>
            <?php if ($company && $company->payInAdvance == 0): ?>
              <input type="submit" name="approved" value="Akkoord" id="approveButton" style="margin-bottom: 10px;" />
            <?php else: ?>
              <p><strong style="color: red;">Moet vooruitbetalen</strong></p>
            <?php endif; ?>
          <?php endif; ?>
          <div id="google_maps" style="width: 500px; height: 300px; display: none;"></div>
          <input id="pac-input" type="text" placeholder="<?php echo __('Ga naar een locatie'); ?>" style="display: none;" />
        </td>
      </tr>
      <tr class="dataTableRow" style="display: flex; flex-direction: column;">
        <td><?php echo $quotation->street . ' ' . $quotation->nr . ' ' . $quotation->ext ?></td>
        <td style="margin-top: 10px;"><?php echo $quotation->zipcode . ' ' . $quotation->domestic ?></td>
        <td style="color: red; margin-top: 10px; max-width: 500px;"><?php echo nl2br(htmlspecialchars($quotation->dispatchAppointment ?? '')); ?></td>
      </tr>
    </table>
  </div>
  <div class="general-container">
    <table style="height: fit-content; margin-right: 10px;" class="default_table dataTableNoBorder">
      <tr class="dataTableHeadingRow">
        <td>Eigenschap</td>
        <td>Waarde</td>
      </tr>
      <?php
        foreach ($form->getElements() as $key => $element):
          if (in_array($key, ['projectName', 'projectReference'])) $element->renderRow();
        endforeach;
      ?>
      <tr class="dataTableRow">
        <td class="head">Merk</td>
        <td><?php echo $quotation->brand ? $quotation->brand->name : 'Onbekend' ?></td>
      </tr>
      <?php if ($quotation->ralColor): ?>
        <tr class="dataTableRow">
          <td class="head">Ral Kleur</td>
          <td><?php echo $quotation->ralColor ?></td>
        </tr>
      <?php else: ?>
        <tr class="dataTableRow">
          <td class="head">Kleur</td>
          <td><?php echo $quotation->stoneColor ? $quotation->stoneColor->name : 'Onbekend' ?></td>
        </tr>
      <?php endif; ?>
      <tr class="dataTableRow">
        <td class="head">Afmeting</td>
        <td><?php echo $quotation->stoneSize ? $quotation->stoneSize->name : 'Onbekend' ?></td>
      </tr>
      <tr class="dataTableRow">
        <td class="head">Eindsteen</td>
        <td><?php echo $quotation->getEndstoneName() ?></td>
      </tr>
    </table>

    <table style="height: fit-content;" class="default_table dataTableNoBorder">
      <tr class="dataTableHeadingRow">
        <td>Eigenschap</td>
        <td>Waarde</td>
      </tr>
      <tr class="dataTableRow">
        <td class="head">Aantal mm korter</td>
        <td><?php echo $quotation->shorter ?></td>
      </tr>
      <?php
        foreach ($form->getElements() as $key => $element):
          if (in_array($key, ['productionNotes', 'customerNotes'])) $element->renderRow();
        endforeach;
      ?>
      <tr class="dataTableRow">
        <td class="head">Type gekozen maat</td>
        <td><?php echo $quotation->heartClickSizeText ?></td>
      </tr>
    </table>
  </div>

  <?php if ($elements && count($elements)): ?>
    <div class="general-container">
      <table style="height: fit-content; width: 100%;" class="default_table dataTableNoBorder">
        <tr class="dataTableHeadingRow">
          <td>Merk</td>
          <td>Aantal</td>
          <td>Kozijn in mm</td>
          <td>Halfverstek</td>
          <td>Vlagkozijn</td>
          <td>Elementen in mm</td>
          <td>Stenen</td>
          <td>2x zaagwerk</td>
          <td>Steentotaal</td>
          <td class="price-col">Prijs</td>
          <td class="last-col"></td>
        </tr>
        <?php foreach ($elements as $element): ?>
          <tr class="dataTableRow">
            <td>
              <input
                style="width: 12em !important; margin-right: 10px;"
                type="text"
                name="referenceName[<?php echo $element->elementId ?>]"
                value="<?php echo htmlspecialchars($element->referenceName) ?>"
              />
            </td>
            <td><?php echo $element->amount ?></td>
            <td><?php echo $element->inputLength ?></td>
            <td><?php echo $element->leftMitreId ? 'Ja' : 'Nee' ?></td>
            <td><?php echo $element->flagWindowSide !== 'none' ? 'Ja' : 'Nee'  ?></td>
            <td><?php echo $element->elementLengthTotal ?></td>
            <td><?php echo $element->stoneCount ?></td>
            <td><?php echo $element->sawSize ?></td>
            <td><?php echo $element->totalStones ?></td>
            <td class="price-col"><?php echo $element->totalPrice ?></td>
            <td class="last-col"></td>
          </tr>
        <?php endforeach;?>
        <tr class="dataTableRow">
          <td></td>
          <td><strong><?php echo $totalAmount ?></strong></td>
          <td></td><td></td><td></td>
          <td><strong><?php echo $totalLength ?></strong></td>
          <td></td><td></td>
          <td><strong><?php echo $totalStones ?></strong></td>
          <td class="price-col"><strong><?php echo $totalPrice ?></strong></td>
        </tr>
      </table>
    </div>
  <?php endif; ?>

    <div class="general-container" id="vueapp" style="justify-content: end;">
      <table class="default_table dataTableNoBorder">
        <tr class="dataTableHeadingRow">
          <?php if ($quotation->createdVia === 'webshop'): ?>
            <td></td>
          <?php endif; ?>
          <td>Tonen productie staat	</td>
          <td>Aantal</td>
          <td>Stuk prijs</td>
          <td>Product naam</td>
          <td class="price-col">Totaal prijs</td>
          <td class="last-col"></td>
        </tr>
        <tr class="dataTableRow" v-for="(project, index) in projects" :key="project.projectId" :class="{ flagged: project.flaggedForDeletion }">
          <?php if ($quotation->createdVia === 'webshop'): ?>
            <td><label>Webshop</label></td>
          <?php endif; ?>
          <td>
            <input :name="`projects[${index}][projectId]`" type="hidden" :value="project.projectId">
            <input :name="`projects[${index}][flaggedForDeletion]`" type="hidden" :value="project.flaggedForDeletion">
            <input :name="`projects[${index}][orderNr]`" type="hidden" :value="project.orderNr">
            <input :name="`projects[${index}][showOnProductionPage]`" type="checkbox" :checked="project.showOnProductionPage == 1" @change="toggleProject(project)">
          </td>
          <td><input @change="refreshProjects" :name="`projects[${index}][size]`" :readonly="!project.showOnProductionPage" type="number" step="0.01" v-model="project.size"></td>
          <td><input @change="refreshProjects" :name="`projects[${index}][pieceprice]`" :readonly="!project.showOnProductionPage" type="number" step="0.01" v-model="project.pieceprice" @blur="project.pieceprice = parseFloat(project.pieceprice).toFixed(2)"></td>
          <td><input :name="`projects[${index}][name]`" type="text" v-model="project.name" style="width: 30em !important;"></td>
          <td class="price-col"><input :name="`projects[${index}][euro]`" readonly type="number" step="0.01" :value="parseFloat(project.size * project.pieceprice).toFixed(2)"></td>
          <td class="last-col">
            <a
              v-if="project.projectId"
              :href="`?action=deleteProject&projectId=${project.projectId}&quotationId=<?php echo $quotation->quotationId ?>`"
              class="gsd-svg-icon-a gsd-delete" title="Verwijder"
              data-gsd-text="Wilt u dit item verwijderen?"
            >
              <?php echo IconHelper::getRemove() ?>
            </a>
            <a
              v-else
               @click="removeProject(project)"
              class="gsd-svg-icon-a"
              title="Verwijder"
            >
              <?php echo IconHelper::getRemove() ?>
            </a>
            <a class="gsd-svg-icon-a" @click="addNewProject()"><?php echo IconHelper::getAdd() ?></a>
          </td>
        </tr>
        <tr class="dataTableRow">
          <td></td><td></td><td></td>
          <td>Afgesproken vrachtwagenkosten</td>
          <td class="price-col"><input name="specialFreightCostPrice" type="number" step="0.01" v-model="specialFreightCostPrice" @blur="specialFreightCostPrice = parseFloat(specialFreightCostPrice).toFixed(2)"></td>
          <td class="last-col"><input name="specialFreightCost" type="checkbox" v-model="specialFreightCost"></td>
        </tr>
        <tr class="dataTableRow">
          <td></td><td></td><td></td>
          <td>Vrachtkosten Webshop</td>
          <td class="price-col"><?php echo StringHelper::getPriceComma($quotation->freightCosts) ?></td>
        </tr>
        <?php if($buildingMaterialTradingFlag): ?>
          <tr class="dataTableRow">
            <td></td><td></td><td></td>
            <td>Bouwmaterialenhandel alleen verlijmen</td>
            <td class="price-col">-<?php echo StringHelper::getPriceComma($totalPrice - $quotation->mattingOnlyGlue) ?></td>
            <td class="last-col"><input name="mattingOnlyGlueFlag" type="checkbox" v-model="mattingOnlyGlueFlag"></td>
          </tr>
          <tr class="dataTableRow">
            <td></td><td></td><td></td>
            <td>Bouwmaterialenhandel afhaal korting</td>
            <td class="price-col"><?php echo StringHelper::getPriceComma($quotation->mattingRemovalDiscount) ?></td>
            <td class="last-col"><input name="mattingRemovalDiscountFlag" type="checkbox" v-model="mattingRemovalDiscountFlag" :disabled="mattingOnlyGlueFlag"></td>
          </tr>
        <?php else: ?>
          <tr class="dataTableRow">
            <td></td><td></td><td></td>
            <td>Montage Raamdorpelelementen</td>
            <td class="price-col"><?php echo StringHelper::getPriceComma($quotation->mountingPriceXMeters) ?></td>
            <td class="last-col"><input name="mountingFlag" type="checkbox" v-model="mountingFlag"></td>
          </tr>
        <?php endif; ?>
        <tr class="dataTableRow">
          <td></td><td></td><td></td>
          <td>Klant betaald factuur contant (incl 21% btw)</td>
          <td></td>
          <td class="last-col"><input name="cashPayment" type="checkbox" v-model="cashPayment" :disabled="mattingOnlyGlueFlag"></td>
        </tr>
        <tr class="dataTableRow">
          <td></td><td></td><td></td>
          <td><strong>Totaal</strong></td>
          <td class="price-col"><strong>{{ totalPrice }}</strong></td>
        </tr>
      </table>
    </div>

    <br>

    <div>
      <input type="hidden" id="city" value="<?php echo $quotation->domestic ?>">
      <input type="hidden" id="country" value="<?php echo $quotation->country ?>">
      <input type="hidden" id="zip" value="<?php echo $quotation->zipcode ?>">
    </div>
    <input type="submit" name="go" value="Opslaan" class="gsd-btn gsd-btn-primary"/>
    <input type="submit" name="go_list" value="Opslaan en naar lijst" class="gsd-btn gsd-btn-secondary"/>
  </form>
</div>


<script type="text/javascript">
  // Vue projects and prices script
  const { createApp, ref, computed, watch, onMounted } = Vue
  const app = createApp({
    setup() {
      const projects = ref(<?php echo json_encode($projects, JSON_HEX_TAG | JSON_NUMERIC_CHECK); ?>);

      // !! converts the flags (0 or 1) to javascript booleans
      const specialFreightCost = ref(!!<?php echo $quotation->specialFreightCost ?>);
      const mattingOnlyGlueFlag = ref(!!<?php echo $quotation->mattingOnlyGlueFlag ?>);
      const mattingRemovalDiscountFlag = ref(!!<?php echo $quotation->mattingRemovalDiscountFlag ?>);
      const mountingFlag = ref(!!<?php echo $quotation->mountingFlag ?>);
      const cashPayment = ref(!!<?php echo $quotation->cashPayment ?>);

      const specialFreightCostPrice = ref(<?php echo $quotation->specialFreightCostPrice ?>);

      // Total price including elements, projects, and optional discounts
      const totalPrice = computed(() => {
        let total = 0
        // total projects price
        total += projects.value.reduce((acc, project) => acc + (project.euro ? parseFloat(project.euro) : 0), total)

        // total elements price
        total += <?php echo $totalPrice ?>;

        if (specialFreightCost.value) total += Number(specialFreightCostPrice.value);
        if (mattingOnlyGlueFlag.value) total -= <?php echo $totalPrice - $quotation->mattingOnlyGlue ?? 0 ?>;
        if (mattingRemovalDiscountFlag.value) total += <?php echo $quotation->mattingRemovalDiscount ?? 0 ?>;
        if (mountingFlag.value) total += <?php echo $quotation->mountingPriceXMeters ?? 0 ?>;
        if (cashPayment.value) total *= 1.21 // btw;

        // round to 2 decimals
        return Math.round(total * 100) / 100
      })

      function resetFlags(currentFlag, flagsToReset) {
        if (currentFlag.value) {
          flagsToReset.forEach(flag => flag.value = false);
        }
      }

      // reset flags when one of them is checked, so only one can be checked at a time
      watch(mattingOnlyGlueFlag, () => resetFlags(mattingOnlyGlueFlag, [mattingRemovalDiscountFlag, cashPayment]));
      watch(mattingRemovalDiscountFlag, () => resetFlags(mattingRemovalDiscountFlag, [mattingOnlyGlueFlag, cashPayment]));
      watch(cashPayment, () => resetFlags(cashPayment, [mattingOnlyGlueFlag, mattingRemovalDiscountFlag]));

      function refreshProjects() {
        // refresh the projects list to recalculate the total price
        projects.value = projects.value.map(project => {
          project.euro = parseFloat(project.size * project.pieceprice).toFixed(2)
          return project
        })
      }

      function toggleProject(project) {
        project.showOnProductionPage = project.showOnProductionPage ? 0 : 1
      }

      function removeProject(project) {
        const index = projects.value.indexOf(project)
        projects.value.splice(index, 1)

        // we always want to have at least one (empty) project
        if (projects.value.length === 0) addNewProject()
      }

      function addNewProject() {
        projects.value.push({
          projectId: null,
          showOnProductionPage: 0,
          size: 0,
          pieceprice: 0,
          name: '',
          euro: 0,
          orderNr: projects.value.length + 1
        });
      }

      onMounted(() => {
        // add a new project when there are no projects
        if (projects.value.length === 0) {
          addNewProject()
        }
      })

      return {
        projects,
        refreshProjects,
        toggleProject,
        removeProject,
        addNewProject,
        totalPrice,
        specialFreightCost,
        mattingOnlyGlueFlag,
        mattingRemovalDiscountFlag,
        mountingFlag,
        cashPayment,
        specialFreightCostPrice,
      }
    }
  })
  app.mount('#vueapp')

  // Google maps script
  let map;
  let marker;
  const startup = true;

  async function initmap() {
    // Import the Advanced Marker library
    const { AdvancedMarkerElement } = await google.maps.importLibrary("marker");

    const mapOptions = {
      center: new google.maps.LatLng(52.2, 5.5),
      zoom: 17,
      scrollwheel: false,
      mapTypeId: google.maps.MapTypeId.ROADMAP,
      mapId: "route_map_id" // Required for Advanced Markers
    };

    map = new google.maps.Map(document.getElementById("google_maps"), mapOptions);

    <?php if(isset($address) && $address && $address->latitude != "" && $address->longitude != "") : ?>
    const latlng = new google.maps.LatLng(<?php echo $address->latitude; ?>, <?php echo $address->longitude; ?>);

    marker = new AdvancedMarkerElement({
      position: latlng,
      map: map,
      gmpDraggable: true
    });
    map.setCenter(latlng);

    marker.addListener('drag', function() {
      const position = marker.position;
      $("#lat").val(position.lat);
      $("#lng").val(position.lng);
    });
    <?php endif; ?>
  }

  $(document).ready(function(){
    initmap();
  });
</script>