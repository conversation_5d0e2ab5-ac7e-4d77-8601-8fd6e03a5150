<?php TemplateHelper::includePartial('_tabs.php', 'stones'); ?>

<div class="box">
    <form method="post" action="<?php echo reconstructQueryAdd() ?>">
      <input type="text" name="el_search" value="<?php echo $_SESSION['el_search'] ?>" placeholder="Zoeken op elementlengte..."/>
      <input type="submit" name="go" id="go" value="Zoeken" />

      <a href="?action=shortelementedit" class="gsd-btn gsd-btn-primary">Toevoegen nieuw kort element</a>
    </form>
  </div>

  <?php $pager->writePreviousNext(); ?>

  <?php if(count($items)==0): ?>
    <br/>
    Er zijn nog geen items gevonden.
  <?php else: ?>
    <table class="default_table" style="width: auto;">
      <tr class="dataTableHeadingRow">
        <td>Element lengte</td>
        <td>Aantal stenen</td>
        <td>Aantal passtenen</td>
        <td>Lengte passteen</td>
        <td style="width: 70px;">Actie</td>
      </tr>
      <?php
        /** @var SmallElements $item */
        foreach($items as $item): ?>
        <tr class="dataTableRow trhover">
          <td><?php echo $item->elementLength ?></td>
          <td><?php echo $item->stoneAmount ?></td>
          <td><?php echo $item->fitStoneAmount ?></td>
          <td><?php echo $item->fitStoneLength ?></td>
          <td>
            <?php echo BtnHelper::getEdit(reconstructQueryAdd(['pageId']).'action=shortelementedit&id='.$item->seId) ?>
          </td>
        </tr>
      <?php endforeach; ?>
    </table>
  <?php endif; ?>
