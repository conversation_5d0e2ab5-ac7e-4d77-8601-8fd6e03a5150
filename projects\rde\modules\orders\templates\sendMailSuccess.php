<script src="/gsdfw/includes/ckeditor4/ckeditor.js?v=1"></script>
<script src="/gsdfw/includes/jsscripts/ckeditor4.js?v=1"></script>

<div>
  <h2>Verstuur aangepaste offerte</h2>
</div>
<form id="quot-form" method="post">
  <div>
    <select id="mail-template" class="mail-template" name="mailTemplate">
      <?php foreach ($mailTemplates as $template): ?>
        <option value="<?php echo $template->id ?>" data-content="<?php echo htmlspecialchars($template->contentString->content) ?>">
          <?php echo $template->name ?>
        </option>
      <?php endforeach; ?>
    </select>
  </div>
  <div class="editor-container">
    <textarea class="ckeditor" id="content" name="content"></textarea>
  </div>

  <input style="margin-top: 10px;" type="submit" name="go" value="Verzenden" class="gsd-btn gsd-btn-primary"/>
</form>

<script>
  $(document).ready(function () {

    ckeditorInit();
    CKEDITOR.config['contentsCss'] = CKEDITOR.config['contentsCss'].concat([<?php echo getCkeditorStylesheets() ?>]);
    ckeditorScaytNl();
    ckeditorSimple();

    CKEDITOR.on('instanceReady', function (event) {
      if (event.editor.name === 'content') {
        initializeEditorContent();
      }
    });

    // check if there is a preferred mail template and set it to the editor
    function initializeEditorContent() {
      const preferredOption = $('#mail-template option[value="<?php echo $preferedMailTemplate?->id ?? 0; ?>"]');
      if (preferredOption.length) {
        const preferredContent = preferredOption.data('content');
        preferredOption.prop('selected', true);
        CKEDITOR.instances['content'].setData(preferredContent);
      }
    }

    $('#ckeditor').value += '234';
    $('#mail-template').change( function () {
      // get the content of the selected option and set it to the editor
      const content = $('#mail-template option:selected').data('content');
      CKEDITOR.instances['content'].setData(content);
    });

    // Ensure CKEditor content is included in form submission
    $('#quot-form').submit(function () {
      $('#content').val(CKEDITOR.instances['content'].getData());
    });
  });
</script>

<style>
  .editor-container {
    margin-top: 20px;
  }
</style>
