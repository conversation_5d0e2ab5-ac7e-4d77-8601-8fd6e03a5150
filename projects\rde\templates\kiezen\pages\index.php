<?php

  include(DIR_INCLUDES . "headercode_frontend.inc.php");
  include("navigation_site.php");
  include("_leftmenu.php");

  $url_raamdorpel = "https://www.raamdorpel.nl/projects/rde/templates/frontend/";

  if(!isset($seo_title) || $seo_title=="") {
    $seo_title = Navigation::getItem($pageId)->getName();
  }
  if(isset($seo_description) && $seo_description != null) {
    $seo_description = StringHelper::cleanAndEscape($seo_description);
  }

  Context::addMetatag("description",$seo_description, true);
  Context::addMetatag("author", "Raamdorpelelementen BV", true);
  Context::addMetatag("copyright", "Raamdorpelelementen BV", true);
  Context::addMetatag("robots", "All", true);

?>
<!DOCTYPE html>
<html dir="ltr" lang="nl-NL">
<head>
  <meta charset="utf-8"/>

  <title><?php echo escapeSafe($seo_title); ?></title>

  <?php Context::printMetatags(); ?>

  <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no">
  <link rel="icon" type="image/x-ico" href="<?php echo $site->getTemplateUrl() ?>images/favicon.ico"/>

  <?php if(!DEVELOPMENT): ?>
    <!-- Global site tag (gtag.js) - Google Analytics -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=UA-21057398-2"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());

      gtag('config', 'UA-21057398-2');
    </script>

  <?php endif; ?>

  <link href='https://cdnjs.cloudflare.com/ajax/libs/simple-line-icons/2.4.1/css/simple-line-icons.min.css' rel='stylesheet' type='text/css'/>
  <link href='https://fonts.googleapis.com/css?family=Roboto:300,400,400i,700,900' rel='stylesheet' type='text/css'/>
  <link href='https://maxcdn.bootstrapcdn.com/font-awesome/4.4.0/css/font-awesome.min.css' rel='stylesheet' type='text/css'/>

  <script type="text/javascript" src="<?php echo $url_raamdorpel ?>dist/deploy<?php echo !DEVELOPMENT?'.min':'' ?>.js"></script>
  <link rel="stylesheet" type="text/css" href="<?php echo $url_raamdorpel ?>dist/main<?php echo !DEVELOPMENT?'.min':'' ?>.css">
  <link rel="stylesheet" href="<?php echo $site->getTemplateUrl(); ?>style/style.css"/>

  <?php Context::printStylesheets(); ?>

  <script src="https://cdn.jsdelivr.net/npm/promise-polyfill@7.1.0/dist/promise.min.js"></script>
  <script src="/gsdfw/includes/jsscripts/jquery/jquery.validation.min.js"></script>
  <?php echo TemplateHelper::includeJavascript('/gsdfw/includes/jsscripts/general', true); ?>

  <?php Context::printJavascripts(); ?>

</head>

<body>

  <div class="wrapper">

    <header class="header">

      <div class="header-top">

        <div class="wrap clearfix">

            <a href="/" class="logo_kiezen">
              <img src="<?php echo $site->getTemplateUrl() ?>images/logo.png" alt="Raamdorpel"/>
            </a>

        </div>

      </div>

      <div class="header-bottom bg-primary">

        <div class="wrap clearfix">

          <nav class="nav hidden-s hidden-xs">
            <?php writeNav('291',Navigation::getInstance(),'mainmenu'); ?>
          </nav>

          <div class="mobile-menu hidden visible-s visible-xs pull-left">
            <ul id="mobilemenu">
              <li class="haschild">
                <a href="#" class="mobile_icon">
                  <i class="icon-user"></i>
                </a>
              </li>
              <li>
                <a class="mobile_icon" href="<?php echo PageMap::getUrl("M_BASKET") ?>">
                  <i class="icon-basket"></i>
                </a>
              </li>
            </ul>
          </div>

          <div class="mobile-menu hidden visible-s visible-xs pull-right">

            <button data-toggle-offscreen=".mobile-menu-container" class="c-hamburger c-hamburger--htx">
              <span>toggle menu</span>
            </button>
          </div>

        </div>

      </div>

    </header>


    <div class="mobile-menu-container bg-dk hidden">
      <?php writeNav('291',Navigation::getInstance(),"mobilemenu"); ?>
    </div>

    <div class="container-wrapper">

      <?php if($pageId==292): //homepage ?>

        <?php
          MessageFlashCoordinator::setClassByType("alert","alert alert-danger alerttop");
          MessageFlashCoordinator::setClassByType("default","alert alert-success alerttop");
          MessageFlashCoordinator::showMessages();

          if(isset($_SESSION['flash_message_swal'])):
            $_SESSION['flash_message_swal']->showMessage();
            unset($_SESSION['flash_message_swal']);
          endif;
        ?>

        <?php include($current_action->getTemplatePath()); ?>

      <?php else: ?>

        <?php if(isset($landing)): ?>
          <?php TemplateHelper::includePartial("_topslidersmall.php",'site',["site"=>$site,'titel'=>$page->content->title]) ?>
        <?php endif; ?>

        <div class="container p-t-md">

          <div class="wrap">

            <div class="row">
                <div class="col9 col8-s col12-xs pull-right p-b-lg content">

                  <div class="breadcrumb-bar p-b">
                    <?php echo BreadCrumbs::writeBreadcrumbs() ?>
                  </div>

                  <?php
                    MessageFlashCoordinator::setClassByType("alert","alert alert-danger alerttop");
                    MessageFlashCoordinator::setClassByType("default","alert alert-success alerttop");
                    MessageFlashCoordinator::showMessages();
                  ?>

                  <?php
                    if(isset($_SESSION['flash_message_swal'])):
                      $_SESSION['flash_message_swal']->showMessage();
                      unset($_SESSION['flash_message_swal']);
                    endif;
                  ?>

                  <?php include($current_action->getTemplatePath()); ?>

                </div>

                <aside class="col3 col4-s col12-xs p-b-lg">

                  <div class="sidebar">
                    <?php if(isset($page)): ?>
                      <div class="widget bg-lt p-md">
                        <div class="widget_content">
                          <?php
                            $parentId = $page->parent_id;
                            if($parentId==291) {
                              $parentId = $page->id;
                            }
                            writeLeftNav($parentId,Navigation::getInstance());
                          ?>
                        </div>
                      </div>
                    <?php endif; ?>

                    <div class='widgetBar' id='widgetBar-1'>
                      <div id='widget_23' class='widget width-1-1 first-child last-child'>
                        <div class='widget-inner'>
                          <div class='widget_header'><h3><span>Contact</span></h3></div>
                          <div class='widget_content'>
                            <p>Neem contact op met uw dealer voor de mogelijkheden</p>
                          </div>
                          <div class='widget_footer'></div>
                        </div>
                      </div>
                    </div>

                  </div>
                </aside>

            </div>

          </div>

        </div>

      <?php endif; ?>


      <?php TemplateHelper::includePartial("_kiezenfooter.php","site",["site"=>$site]) ?>

    </div>

  </div>


  <div class="scrollToTop hidden visible-s visible-xs"><i class="fa fa-chevron-up"></i></div>

  <div id="c-cookies">
    <div class="cookies">
      <div class="cookies_wrapper">
        <p class="cookies_infotext"><?php echo __("Raamdorpelkiezen.nl maakt gebruik van cookies. Bezoek je onze site, dan ga je akkoord met het plaatsen van") ?> <a class="cc-privacy-link" href="/privacyverklaring" target="_blank"> Cookies</a>.
          <a class="cc-btn-close" href="#" id="acceptcookies"><i class="fa fa-times" aria-hidden="true"></i> AKKOORD</a>
        </p>
      </div>
    </div>
  </div>


  <script>
    $(document).ready( function() {

      if(!hasAcceptedCookies()) {
        $("#c-cookies").show();
        $("#acceptcookies").click(function(e) {
          e.preventDefault();
          setAcceptsCookies();
          $("#c-cookies").hide();
        });
      }

    });
  </script>

</body>
</html>