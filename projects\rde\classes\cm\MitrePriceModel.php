<?php

  AppModel::loadBaseClass('BaseMitrePrice');

  class MitrePriceModel extends BaseMitrePrice {

    /**
     * Verstekprijs per verstek voor natuurstenen en betonnen raamdorpels.
     * @return mixed
     */
    public static function getCurrent() {
      $found = self::find_by([], "WHERE validFrom <= '" . date("Y-m-d") . "' AND validTo >= '" . date("Y-m-d") . "' ");
      return $found->price;
    }

  }