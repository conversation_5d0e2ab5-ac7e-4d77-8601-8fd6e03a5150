<?php

  require_once 'apiCmsActions.php';
  require_once 'apiRouteActions.php';
  require_once 'apiStoneActions.php';
  require_once 'apiGpsbuddyActions.php';
  require_once 'apiSortActions.php';
  require_once 'apiShippingActions.php';
  require_once 'apiPickupActions.php';
  require_once 'apiDamageActions.php';
  require_once 'apiProductionActions.php';

  class apiRdeActions extends apiActions {

    use apiCmsActions;
    use apiGpsbuddyActions;
    use apiSortActions;
    use apiRouteActions;
    use apiStoneActions;
    use apiShippingActions;
    use apiPickupActions;
    use apiDamageActions;
    use apiProductionActions;

    public function preExecute() {

      parent::preExecute();

      if (gsdApiSession::getUser()) {
        //ik heb een ProductionEmployee, check if app is locked
        if (isset(gsdApiSession::getUser()->worker_id) && WorkerProfile::hasProfileByWorkerId(gsdApiSession::getUser()->worker_id, "lockapp")) {
          RestUtils::sendResponse(401, json_encode([
            'status'  => "UNAUTHORIZED",
            "data"    => [],
            "message" => "U heeft geen rechten. Sluit af en log opnieuw in.",
          ]));
        }
      }
    }

    public function executeAuthenticate() {

      $request_vars = $this->data->getData();
      $datacode = $request_vars['datacode'];

      if ($datacode == "") {
        RestUtils::sendResponseError("Onbekende gebruiker.");
      }

      $worker = Worker::login($datacode);
      if (!$worker) { //try login by passwordNr
        $worker = Worker::loginByNr($datacode);
      }

      if ($worker) {
        $data = new stdClass();
        $data->employeeId = $worker->external_id;
        $data->name = $worker->getNaam();
        $data->rights = $worker->getAppRights();

        RestUtils::sendResponseOK("Gebruiker gevonden", [$data]);

      }
      RestUtils::sendResponseError("Onbekende gebruiker.");

    }

    //region GET

    /**
     * Lijst met offertes gegroeppeerd per bedrijven
     *
     */
    public function executeGetQuotationsGrouped() {

      $request_vars = $this->data->getData();
      $variant = $request_vars['variant'] ?? false;

      //ophalen alle geproduceerde orders, gegroeppeerd per klant
      $query = "SELECT Q.companyId, Q.userId, Q.stoneId, C.name, SUM(Q.meters) as 'totalMeters', Q.userId, Q.quotationId, Q.dueDate, Q.createdVia ";
      $query .= "FROM " . Quotations::getTablename() . " Q ";
      $query .= "LEFT JOIN " . CrmCompanies::getTablename() . " C ON C.companyId = Q.companyId ";
      $query .= "WHERE (Q.statusId=40 OR (Q.statusId=35 AND Q.createdVia='webshop')) ";
      if ($variant !== false) {
        if ($variant == "webshop") {
          $query .= "AND Q.createdVia='webshop' ";
        }
        else {
          $query .= "AND Q.createdVia!='webshop' ";
        }
      }
      $query .= "GROUP BY Q.companyId, Q.dueDate ";
      $query .= "ORDER BY Q.dueDate, C.name ";

      $result = DBConn::db_link()->query($query);
      $dataArr = [];
      $stones = [];
      while ($oData = $result->fetch_object()) {
        if ($oData->companyId == "") {
          $oData->companyId = -1;
          $oData->name = "";
        }

        //fallback for app to old offerteVariant
        $oData->offerteVariant = "";
        if (!isset($stones[$oData->stoneId])) {
          $stones[$oData->stoneId] = Stones::find_by(["stoneId" => $oData->stoneId]);
        }
        if ($stones[$oData->stoneId] != "") {
          $oData->offerteVariant = $stones[$oData->stoneId]->getMaterial() . "_" . $stones[$oData->stoneId]->getType();
        }

        $query1 = "SELECT gpsbuddy_routes.date FROM " . GpsbuddyRde::getTablename() . " ";
        $query1 .= "JOIN " . GpsbuddyRoutes::getTablename() . " ON gpsbuddy_rde.routeId = gpsbuddy_routes.routeId ";
        $query1 .= "WHERE gpsbuddy_rde.quotationId=" . $oData->quotationId;
        $oResult1 = DBConn::db_link()->query($query1);
        $gpsbuddy_route_row = $oResult1->fetch_object();
        if ($gpsbuddy_route_row) { //als er een routedatum is, dan deze gebruiken voor productiedatum. Anders terugvallen op standaard duedate
          $oData->dueDate = $gpsbuddy_route_row->date;
        }
        $key = $oData->dueDate . '_' . $oData->companyId;
        if ($oData->companyId == -1) {
          $key = $oData->dueDate . '_U_' . $oData->userId;
        }

        if (isset($dataArr[$key])) {
          //samenvoegen, dezelfde klant op dezelde dag. Zijn gesplitst door ook naar routedatum te kijken.
          $dataArr[$key]->totalMeters += $oData->totalMeters;
        }
        else {
          $dataArr[$key] = $oData;
        }
      }


      usort($dataArr, function ($a, $b) {
        if ($a->createdVia != $b->createdVia) {
          if ($a->createdVia == "webshop") {
            return -1;
          }
          elseif ($b->createdVia == "webshop") {
            return 1;
          }
        }
        if ($a->dueDate == $b->dueDate) {
          return strcmp($a->name, $b->name);
        }
        return strtotime($a->dueDate) - strtotime($b->dueDate);
      });

      //order pick date is due date minus 1.
      //if dueDate is on monday, picking date would be friday
      foreach ($dataArr as $dataRow) {
        //if dueDate is on saturday (6), sunday (7) or monday (1) => picking date is friday
        if (in_array(date('N', strtotime($dataRow->{'dueDate'})), [6, 7, 1])) {
          $dataRow->dueDate = date('Y-m-d', strtotime("LAST FRIDAY", strtotime($dataRow->{'dueDate'})));
        }
        else {
          $dataRow->dueDate = date('Y-m-d', strtotime("-1 DAY", strtotime($dataRow->{'dueDate'})));
        }

        if ($dataRow->userId != "") {
          $person_o = SandboxUsers::find_by(["userId" => $dataRow->userId]);
          $dataRow->userName = $person_o->firstName . ' ' . $person_o->lastName;
        }
      }

      RestUtils::sendResponseOK("ORDER PICKING LIST SUCCESFULLY RETRIEVED", $dataArr);

    }

    /**
     * @deprecated 25-04-2019
     */
    public function executeGetQuotationsByCompanyId() {
      $this->executeGetQuotationsByCompanyOrUserId();
    }

    /**
     * Lijst met offertes van één bedrijf
     *
     */
    public function executeGetQuotationsByCompanyOrUserId() {
      $request_vars = $this->data->getData();
      $companyId = $request_vars['companyId'];
      $userId = isset($request_vars['userId']) ? $request_vars['userId'] : false;

      $debug = $this->version >= 1.4;
      $debug = false;

      //ophalen alle geproduceerde orders, gegroeppeerd per klant
      $query = "SELECT Q.quotationId, Q.quotationNumber, Q.quotationVersion, Q.quotationPart, Q.companyId, Q.userId, COM.name, Q.meters, Q.noRackQuotations, Q.noContainerQuotations, Q.noRackNoContainerQuotations, Q.palletQuotations, Q.afhalenQuotations, Q.statusId, Q.weight_webshop, ";
      $query .= "QE.addressDeliveryId, QE.addressSplit, QE.addressSplitInfo, ";
      $query .= "C.containerId, C.containerNumber  ";
      $query .= "FROM " . Quotations::getTablename() . " Q ";
      $query .= "LEFT JOIN " . CrmCompanies::getTablename() . " COM ON COM.companyId = Q.companyId ";
      $query .= "LEFT JOIN " . QuotationsExtra::getTablename() . " QE ON QE.quotationId = Q.quotationId ";
      $query .= "LEFT JOIN " . ContainersQuotations::getTablename() . " CQ ON CQ.quotationId = Q.quotationId ";
      $query .= "LEFT JOIN " . Containers::getTablename() . " C ON C.containerId = CQ.containerId ";
      $query .= "WHERE (Q.statusId IN (40, 50) OR (Q.statusId=35 AND Q.createdVia='webshop')) "; //40, 50
      if ($companyId == -1) {
        $query .= "AND Q.userId= " . $userId . " ";
      }
      else {
        $query .= "AND Q.companyId = " . $companyId . " ";
      }
      $query .= "ORDER BY Q.quotationNumber ";


      $dataArr = [];
      $result = DBConn::db_link()->query($query);
      while ($oData = $result->fetch_object()) {
        //          pd($oData);
        foreach (get_object_vars($oData) as $key => $val) {
          if ($oData->{$key} === null) {
            $oData->{$key} = "";
          }
          elseif ($key == "quotationPart") {
            $oData->{$key} = RdeHelper::asciiIntToAlpha($val);
          }
        }

        $query1 = "SELECT MAX(EQ.enddate) as maxEnddate FROM " . EmployeeQuotation::getTablename() . " EQ WHERE EQ.quotationId=" . $oData->quotationId;
        $oResult1 = DBConn::db_link()->query($query1);
        $employee = $oResult1->fetch_object();

        $oData->updateDate = '';
        if ($employee) {
          $oData->updateDate = $employee->maxEnddate;
        }

        //multiple employee_quotation rows
        $row = null;
        if (isset($dataArr[$oData->quotationId])) {
          $row = $dataArr[$oData->quotationId];
        }
        else {
          $row = $oData;
          $row->containers = [];
        }
        if ($oData->containerId != "") {
          $cont = [
            'containerId'     => $oData->containerId,
            'containerNumber' => $oData->containerNumber,
          ];
          $row->containers[] = $cont;
        }
        unset($row->containerId, $row->containerNumber);

        if ($row->companyId == "")
          $row->companyId = -1;

        $dataArr[$oData->quotationId] = $row;
      }

      //      pd($dataArr);

      //alle in te pakken, ingepakte orders opgehaald
      //nu berekenen waar welke order in welke bakken moeten komen.

      /*
       * 1. a Heeft <addressDeliveryId> al een baknummer?
       *    b Is AddressSplit NULL?
       *
       *  do: attach this order to baknumber
       *
       * 2. is AddressSplit gevuld?
       *
       *  do: attach this order to AddressSplit char.
       *
       * 3. addressDeliveryId zit nog niet in een bak en AddressSplit is niet gevuld. Dan zelf de eerst volgende letter bepalen.
       */

      $packedOrders = [];
      $ordersToPack = [];
      $ordersToPackWithoutLetter = [];

      $lettersTaken = [];
      $lettersTakenCount = -1;

      //ingepakte orders afsplitsen van in te pakken orders
      foreach ($dataArr as $dataRow) {
        usort($dataRow->containers, "apiRdeActions::sortContainers");
        if ($dataRow->statusId == Status::STATUS_PACKED) { //ingepakt
          $packedOrders[] = $dataRow;
        }
        elseif ($dataRow->statusId == Status::STATUS_PRODUCED || $dataRow->statusId == Status::STATUS_PREPARED) { //geproduceerd
          $dataRow->{'dryTime'} = (time() - strtotime($dataRow->{'updateDate'})); //in seconden
          $ordersToPack[] = $dataRow;
        }
        $dataRow->rackCode = '';
        $rackQuote = GeneratedRackidQuotationids::find_by(["quotationId" => $dataRow->quotationId]);
        if ($rackQuote) {
          $rack = GeneratedRackIds::find_by(["rackId" => $rackQuote->rackId]);
          if ($rack) {
            $dataRow->rackCode = $rack->rackCode;
          }
        }
      }

      if ($debug) {
        //        pd($packedOrders);
      }

      //baknummer, split nummer toewijzen
      foreach ($ordersToPack as $toPack) {
        if ($debug)
          pd($toPack->quotationId . " ->  " . $toPack->{'quotationNumber'} . '-' . $toPack->{'quotationVersion'} . '-' . $toPack->{'quotationPart'} . ": " . $toPack->{'addressDeliveryId'} . " - " . $toPack->{'addressSplit'});

        //sit 1.
        //checken of deze addressDeliveryId al in een bak zit, kijken in $packedOrders
        $allready_packed = false;
        $containers_wherein = false;
        foreach ($packedOrders as $packedOrder) {
          foreach ($packedOrder->containers as $lcont) {
            //alleen indezelfde back wanneer nummer hetzelfde is.
            if ($debug) {
              pd("ROBERT " . ($packedOrder->addressDeliveryId == $toPack->addressDeliveryId) . " " . $toPack->addressSplit . "!=" . $packedOrder->addressSplit);
            }
            if ($packedOrder->addressDeliveryId == $toPack->addressDeliveryId && ($toPack->addressSplit == $packedOrder->addressSplit || $toPack->addressSplit == "")) {
              $allready_packed = $packedOrder->addressSplit;
              $containers_wherein = $packedOrder->containers;
              break;
            }
          }
          if ($allready_packed)
            break;
        }

        if ($debug)
          pd("allreadypacked: " . ($allready_packed !== false ? $allready_packed : 'nee'));

        if ($allready_packed !== false) {
          if ($debug)
            pd("SIT 1.");
          $toPack->containers = $containers_wherein;
          $toPack->addressSplit = $allready_packed;

          if (!isset($lettersTaken[$toPack->addressDeliveryId][$toPack->addressSplit])) {
            $lettersTaken[$toPack->addressDeliveryId][$toPack->addressSplit] = $toPack->addressSplit;

            if ($debug)
              pd(RdeHelper::AlphaToInt($toPack->addressSplit));
            if (RdeHelper::AlphaToInt($toPack->addressSplit) > $lettersTakenCount) {
              $lettersTakenCount = RdeHelper::AlphaToInt($toPack->addressSplit);
              $lettersTakenCount++;
            }
          }

          //if($debug) pd($toPack->containers . ": " . $toPack->addressSplit);
        }
        //sit 2. addressSplit vantevoren bepaald, even noteren welke letters al bezet zijn voor welk afleveradresId
        elseif ($allready_packed === false && $toPack->addressSplit != "") {
          if ($debug)
            pd("SIT 2.");

          if (!isset($lettersTaken[$toPack->addressDeliveryId][$toPack->addressSplit])) {
            $lettersTaken[$toPack->addressDeliveryId][$toPack->addressSplit] = $toPack->addressSplit;

            if ($debug)
              pd(RdeHelper::AlphaToInt($toPack->addressSplit));
            if (RdeHelper::AlphaToInt($toPack->addressSplit) > $lettersTakenCount) {
              $lettersTakenCount = RdeHelper::AlphaToInt($toPack->addressSplit);
              //              pd($lettersTakenCount . " " . RdeHelper::intToAlpha($lettersTakenCount));
              $lettersTakenCount++;
            }

          }
        }
        //sit 3.
        else {
          if ($debug)
            pd("SIT 3.");
          $ordersToPackWithoutLetter[] = $toPack;
        }
        if ($debug)
          pd('------------------------------------');
      }

      if ($debug)
        pd("orderToPackWithoutLetter " . $lettersTakenCount);
      foreach ($ordersToPackWithoutLetter as $toPack) {
        if ($debug)
          pd($toPack->addressDeliveryId);

        if ($debug)
          pd($lettersTaken);

        if (!isset($lettersTaken[$toPack->addressDeliveryId])) {
          if ($debug)
            pd("SIT 3A: dit afleveradres bevat nog geen letter, wijs eerst volgende toe");
          if ($debug)
            pd("nextletter offset: " . $lettersTakenCount);
          if ($lettersTakenCount < 0)
            $lettersTakenCount = 0;

          $nextLetter = RdeHelper::intToAlpha($lettersTakenCount);
          if ($debug)
            pd("nextletter: " . $nextLetter);

          $toPack->addressSplit = $nextLetter;

          //add to lettersTaken
          $lettersTaken[$toPack->addressDeliveryId][$toPack->addressSplit] = $toPack->addressSplit;
          $lettersTakenCount++;
        }
        //er is een letter gevonden voor dit afleveradresid, deze order mag hieraan toegevoegd worden.
        elseif (count($lettersTaken[$toPack->addressDeliveryId]) == 1) {
          if ($debug)
            pd("SIT 3B: Add existing letter (single letter for this addressDeliveryId)");
          foreach ($dataArr as $lOrder) {
            if ($toPack->addressDeliveryId == $lOrder) {
              $toPack->containers = $lOrder->containers;
              break;
            }
          }
          $toPack->addressSplit = reset($lettersTaken[$toPack->addressDeliveryId]);
        }
        //meerdere letters bij hetzelfde afleveradresId, volgens mij nieuwe letter
        else {
          if ($debug)
            pd("SIT 3C: Add new letter (multiple letters for this addressDeliveryId)");
          $nextLetter = RdeHelper::intToAlpha($lettersTakenCount);
          if ($debug)
            pd("nextletter: " . $nextLetter);

          $toPack->addressSplit = $nextLetter;

          //add to lettersTaken
          $lettersTaken[$toPack->addressDeliveryId][$toPack->addressSplit] = $toPack->addressSplit;
          $lettersTakenCount++;
        }
      }
      //sort

      foreach ($ordersToPack as $op) {
        $op->containerSort = 0;
        foreach ($op->containers as $lcont) {
          $op->containerSort += (int)$lcont['containerNumber'];
        }
        //trim($op->containerSort, "-");

        $op->hasWebshop = 0;
        $op->hasElements = 0;

        //showOnProductionPage vlagetje word in productie benoemt tot webshop bestelling.
        $projects = Projects::find_all_by(["quotationId" => $op->quotationId, "showOnProductionPage" => 1]);
        if (count($projects) > 0) {
          $op->hasWebshop = 1;
          foreach ($projects as $project) {
            if ($project->product_id != "") {
              $pr = Product::find_by_id($project->product_id);
              $op->weight_webshop += $pr->weight;

              //dit is een standaard lengte element
              //markeren als elementen bestelling, niet als webshop
              if ($pr->discountgroup_id == Discountgroup::CODE_STONE_STANDARD_SIZE) {
                $op->hasElements = 1;
                $op->hasWebshop = 0;
              }

            }
          }
        }

        if ($op->hasElements == 0 && OrderElements::find_by(["quotationId" => $op->quotationId])) {
          //deze quotation heeft ook elementen.
          $op->hasElements = 1;
        }

      }


      usort($ordersToPack, "apiRdeActions::sortOrders");


      if ($debug) {
        pd("orderToPackWithoutLetter done");
        pd($ordersToPack);
        ResponseHelper::exit();
      }


      RestUtils::sendResponseOK("ORDER LIST SUCCESFULLY RETRIEVED", $ordersToPack);
    }

    /**
     * Get companyid and company name by ordernumber
     *
     */
    public function executeGetCompanyIdAndNameByOrderNumber() {
      $rdata = $this->data->getData();
      $orderNumber = $rdata['orderNumber'];

//      $orderNumber = '17.94742-0';

      $DEBUG = false;//$this->version>1.1;

      $split = explode("-", $orderNumber);
      if (count($split) < 2) {
        RestUtils::sendResponseError("ERROR INVALID ORDERNUMBER: " . $orderNumber);
      }
      $quotationNumber = trim($split[0]);
      $quotationVersion = trim($split[1]);
      $quotationPart = "";
      if (isset($split[2]) && trim($split[2]) != "") {
        $quotationPart = RdeHelper::AlphaToAsciiInt(trim($split[2]));
      }

      if ($DEBUG) {
        pd("quotationNumber: " . $quotationNumber);
        pd("quotationVersion: " . $quotationVersion);
        pd("quotationPart: " . $quotationPart);
      }

      $query = "SELECT Q.companyId, C.name AS 'companyName', Q.userId, Q.quotationId ";
      $query .= "FROM " . Quotations::getTablename() . " Q ";
      $query .= "LEFT JOIN " . CrmCompanies::getTablename() . " C ON C.companyId = Q.companyId ";
      //        $query .= "WHERE Q.statusId=40 "; //40, 50
      $query .= "WHERE Q.quotationNumber = '" . escapeForDB($quotationNumber) . "' AND Q.quotationVersion = '" . escapeForDB($quotationVersion) . "' ";
      if ($quotationPart != "") {
        $query .= "AND Q.quotationPart = '" . $quotationPart . "' ";
      }

      $oResult = DBConn::db_link()->query($query);
      $oData = false;
      if ($oData = $oResult->fetch_object()) {
        $oData->orderNumber = $orderNumber;
        if ($oData->companyId == "") {
          $oData->companyId = -1;
          $oData->companyName = "";
          $oData->companyEmail = "";
        }
      }

      if (!$oData) {
        RestUtils::sendResponseError("ERROR RETRIEVING COMPANYID FROM ORDERNUMBER: " . $orderNumber);
      }
      else {
        //also include given orderNumber
        RestUtils::sendResponseOK("COMPANYID AND NAME SUCCESFULL RETRIEVED FROM ORDERNUMBER", [$oData]);
      }
    }

    /**
     * Get Containernumber by tagId (mac-address formatted xx:xx:xx:xx:xx)
     *
     * @internal param $tagId
     */
    public function executeGetContainerNumberByTagId() {
      $rdata = $this->data->getData();
      $tagId = $rdata['tagId'];

      $query = "SELECT C.containerId, C.containerNumber, C.inStock, C.actionRemark ";
      $query .= "FROM " . Containers::getTablename() . " C ";
      $query .= "JOIN " . ContainerTag::getTablename() . " CT ON C.containerId = CT.containerId ";
      $query .= "WHERE CT.tagId = '" . $tagId . "' ";
      //logToFile("mysql",$query);

      $result = DBConn::db_link()->query($query);
      $oData = $result->fetch_object();

      if ($oData) {

        if ($oData->inStock == "N" && isset($rdata['nothomemail'])) {
          //indien container niet op de zaak is, dan emailen. (bij het laden van orders in een bak die niet op de zaak is.)
          MailsFactory::containerNotHome($oData->containerNumber);
        }

        $oData->tagId = $tagId;
        RestUtils::sendResponseOK("CONTAINERNUMBER SUCCESFULL RETRIEVED FROM TAGID", [$oData]);

        return;
      }

      RestUtils::sendResponseError("Er is geen container gekoppeld aan deze tag: " . $tagId);
    }

    /**
     * Orders zijn geladen in vrachtwagen / orders staan klaar voor transport per vrachtwagen.
     * Oftewel Laden app verwerken.
     *
     * @param bool $timestamp
     * @param bool $routeids
     * @param bool $return
     * @return bool
     *
     */
    public function executeSetPacked($timestamp = false, $truckid = false, $routeids = false, $return = false) {

      $request_vars = $this->data->getData();

      if (!$return) {
        $timestamp = $request_vars['date'];
        $truckid = $request_vars['truck_id'];
        $routeids = $request_vars['routeids'];
      }

      $go = true;

      $date = date('Y-m-d', $timestamp / 1000); //convert to php date
      $routes = $this->executeGetRouteItems($timestamp, $truckid, true);

      $routeids = explode("_", substr($routeids, 0, strlen($routeids) - 1));

      logToFile('packed', $date . ' routeids: ' . print_r($routeids, true) . ', aantal in route: ' . count($routes));
      foreach ($routes as $k => $route) {
        if (!in_array($route->routeId, $routeids)) {
          if (!$go)
            pd("Unset: " . $route->routeId);
          unset($routes[$k]);
        }
      }

      if (count($routes) == 0) {
        logToFile('packed', "GEEN ROUTES PACKED?");
      }
      else {

        $quotation_ids_50 = [];
        foreach ($routes as $route) {
          foreach ($route->quotations as $quotation) {
            if ($quotation->statusId == Status::STATUS_PACKED) {
              $quotation_ids_50[] = $quotation->quotationId;
            }
          }
        }

        //update stati 50 naar 53
        if (count($quotation_ids_50) > 0) {
          $query = "UPDATE " . Quotations::getTablename() . " ";
          $query .= "SET statusId=" . Status::STATUS_LOADED . " ";
          $query .= "WHERE statusId=" . Status::STATUS_PACKED . " AND quotationId IN (" . implode(",", $quotation_ids_50) . ") ";
          //logToFile("mysql", $query);
          if ($go) {
            DBConn::db_link()->query($query);
            QuotationStatus::registerBatch($quotation_ids_50, Status::STATUS_LOADED, gsdApiSession::getUserId());
          }
        }

        foreach ($routes as $route) {

          //tja...we pakken de eerste quotation van de route. Hier staan gegevens op welke we op de vrachtbon zetten.
          //eigenlijk hoor dit spul op gpsroutes thuis.
          $quotationIds = [];
          foreach ($route->quotations as $quotation) {
            $quotationIds[] = $quotation->quotationId;
          }

          $cargo_receipt = new CargoReceipt();
          if (isset($route->cargoReceiptId) && $route->cargoReceiptId != "" && $route->cargoReceiptId != 0) {
            $cargo_receipt = CargoReceipt::find_by(["cargoReceiptId" => $route->cargoReceiptId]);
          }

          $quotations = AppModel::mapObjectIds(Quotations::find_all_by(["quotationId" => $quotationIds]), "quotationId");
          $quotation = false;
          foreach ($quotations as $lq) {
            $quotation = $lq;
            break;
          }

          if ($quotation) {

            $person = SandboxUsers::find_by(["userId" => $quotation->userId]);

            $offertetxt = '';
            $retour_op_offerte_ar = [];
            $containers_near_ar = [];
            if ($cargo_receipt->cargoReceiptId != "") {
              //er is een cargo_receipt, en er staan al retour bakken op. Zorgen dat deze blijven staan.
              if ($cargo_receipt->staticNumberRetour != "") {
                $bakken = explode("\n", trim($cargo_receipt->staticNumberRetour));
                if (count($bakken) > 0) {
                  $retour_op_offerte_ar += $bakken;
                }
              }
              if ($cargo_receipt->staticNumberNear != "") {
                $bakken = explode("\n", trim($cargo_receipt->staticNumberNear));
                if (count($bakken) > 0) {
                  $containers_near_ar += $bakken;
                }
              }
            }


            $quot_cont = [];
            $all_container_nrs = [];
            foreach ($route->containers as $c) {
              foreach ($c->quotations as $quotationId) {
                if (!isset($quot_cont[$quotationId])) {
                  $quot_cont[$quotationId] = [];
                }
                $quot_cont[$quotationId][] = $c->containerNumber;
                $all_container_nrs[] = $c->containerNumber;
              }
            }

            foreach ($route->quotations as $l_q) {
              $l_quotation = $quotations[$l_q->quotationId];
              $name = $l_quotation->quotationNumber . '-' . $l_quotation->quotationVersion;
              if ($l_quotation->quotationPart != "") {
                $name .= '-' . $l_quotation->quotationPart; //is als letter zichtbaar
              }

              if (isset($quot_cont[$l_quotation->quotationId])) {
                sort($quot_cont[$l_quotation->quotationId]);
                if ($l_quotation->statusId >= 55 && ($route->deliverDate == "" || strtotime($route->deliverDate) < strtotime($date))) {
                  //deze order heeft status bezorgd EN hij is afgeleverd voor de datum van deze route, dus dit is een oppik actie
                  //hij heeft nog niet status bezorgd, dus containers die bij laden retour zijn genomen staan niet in dit lijstje.
                  //UITGEZET. Word gezet vanuit app bij bezorgen
                  //$retour_op_offerte_ar += $quot_cont[$l_quotation->quotationId]; //merge bakken

                }
                else {
                  $offertetxt .= $name . ' - ' . implode(", ", $quot_cont[$l_quotation->quotationId]) . "\n";
                }
              }
              else {
                $offertetxt .= $name . "\n";
              }
            }

            //-----------------alle bakken opzoeken------------------
            $containers_far = '';
            if (count($route->containers) > 0 || count($route->containers_other) > 0) {
              foreach ($route->containers as $cont) {
                $containers_near_ar[] = $cont->containerNumber;
              }
              foreach ($route->containers_other as $cont) {
                if ($cont->distance == 'near') {
                  $containers_near_ar[] = $cont->containerNumber;
                }
                else { //far
                  $containers_far .= $cont->containerNumber . "\n";
                }
              }
            }

            $containers_near = '';
            if (count($containers_near_ar) > 0) {
              $containers_near_ar = array_merge($containers_near_ar, $retour_op_offerte_ar);//welke retour zijn genomen ook bij near zetten.
              $containers_near_ar = array_unique($containers_near_ar);
              sort($containers_near_ar);
              $containers_near .= implode("\n", $containers_near_ar);
            }

            $containers_retour = ''; //word gezet vanuit app
            if (count($retour_op_offerte_ar) > 0) {
              $retour_op_offerte_ar = array_unique($retour_op_offerte_ar);
              sort($retour_op_offerte_ar);
              $containers_retour .= implode("\n", $retour_op_offerte_ar);
            }

            //-----------------EINDE alle bakken opzoeken------------------
            if (!$go) {
              //pd($route);
              pd($quotation->domestic . ' ' . $route->deliverDate . ' ' . $route->routeId);
              if ($offertetxt != "")
                pd("Offerte: " . $offertetxt);
              if ($containers_retour != "")
                pd("Retour: " . $containers_retour);
              if ($containers_near != "")
                pd("Dichtbij: " . $containers_near);
              if ($containers_far != "")
                pd("Verweg: " . $containers_far);
              pd('-');
            }

            $senderInstructions = "";
            if ($route->extraInfo != "") {
              $senderInstructions .= $route->extraInfo;
              $senderInstructions .= "\n";
            }
            $senderInstructions .= 'Let op: U dient de raamdorpelelementen rechtop te dragen, zoals ze ook verpakt zijn in rek of bak. Langere lengtes dienen door 2 personen gedragen en gelegd te worden.';
            $senderInstructions = substr($senderInstructions, 0, 255); //zorgen dat deze niet te lang word.

            $fields = [
              'companyId'                  => ($route->companyId == "" || $route->companyId == -1) ? null : $route->companyId,
              'cargoReceiptType'           => $offertetxt == "" ? 'pickup' : 'standard',
              'shippingAgentName'          => 'Raamdorpelelementen BV',
              'shippingAgentStreet'        => 'Raambrug',
              'shippingAgentNumber'        => '9',
              'shippingAgentExt'           => '',
              'shippingAgentZip'           => '5531AG',
              'shippingAgentCity'          => 'Bladel',
              'shippingAgentPhone'         => '0497360791',
              'destinationName'            => $route->companyName,
              'destinationStreet'          => $quotation->street,
              'destinationNumber'          => $quotation->nr,
              'destinationExt'             => $quotation->ext,
              'destinationZip'             => $quotation->zipcode,
              'destinationCity'            => $quotation->domestic,
              'city3'                      => $quotation->domestic,
              'country3'                   => 'Nederland',
              'city4'                      => $quotation->domestic,
              'country4'                   => 'Nederland',
              'dateOfToday'                => date('Y-m-d'),
              'documentsAttached'          => 'Specificatielijst raamdorpelelementen',
              'marksAndNums'               => trim($offertetxt), //de te bezorgen offertes / de op te halen offertes
              //              'numberOfPackages'           => '',
              //              'methodOfPacking'            => '',
              //              'natureOfTheGoods'           => '',
              'staticNumberNear'           => $containers_near, //bakken in de buurt van klant
              'staticNumberFar'            => $containers_far, //bakken ver weg van klant
              'staticNumberRetour'         => $containers_retour, //bakken retour
              //              'gross'                      => '',
              //              'volume'                     => '',
              'destinationContact'         => $person ? $person->firstName . ' ' . $person->lastName : '',
              'destinationPhone'           => $person ? $person->phone : '',
              'destinationMobile'          => $person ? $person->mobile : '',
              'consigneeExecutor'          => $quotation->executor,
              'consigneeExecutorMobile'    => $quotation->executorMobile,
              'consigneeExecutorMail'      => $quotation->executorMail,
              'senderInstructions'         => $senderInstructions,
              //              'carriagePaid'               => '',
              //              'carriageForward'            => '',
              //              'cashOnDelivery'             => '',
              //              'carrierName'                => '',
              //              'carrierStreet'              => '',
              //              'carrierStreetNumber'        => null,
              //              'carrierExt'                 => '',
              //              'carrierZip'                 => '',
              //              'carrierCity'                => '',
              //              'carrierNumberplate'         => '',
              //              'successiveCarriers'         => '',
              'carrierDispatchAppointment' => $quotation->dispatchAppointment,
              //              'specialAgreements'          => '',
              //              'toBePaidBy'                 => '',
              'establishedCity'            => 'Bladel',
              'establishedDate'            => date('Y-m-d H:i:s'),
              //              'signatureSenderName'        => '',
              //              'signatureSenderFilename'    => '',
              //              'signatureSenderPlace'       => '',
              //              'signatureSenderDate'        => '',
              //              'signatureCarrierName'       => '',
              //              'signatureCarrierFilename'   => '',
              //              'signatureCarrierPlace'      => '',
              //              'signatureCarrierDate'       => '',
              //              'signatureCarrierMail'       => '',
              //              'signatureConsigneeName'     => '',
              //              'signatureConsigneeFilename' => '',
              //              'signatureConsigneePlace'    => '',
              //              'signatureConsigneeDateTime' => '',
              //              'signatureConsigneeMail'     => '',
              //              'cmr'                        => '',
              //              'avc'                        => '',
              //              'carrierCode'                => '',
            ];

            //          pd($fields);
            foreach ($fields as $key => $val) {
              $cargo_receipt->{$key} = $val;
            }
            if ($go) {
              $cargo_receipt->save();

              $gr = GpsbuddyRoutes::find_by(["routeId" => $route->routeId]);
              if ($gr && $gr->cargoReceiptId != $cargo_receipt->cargoReceiptId) {
                $gr->cargoReceiptId = $cargo_receipt->cargoReceiptId;
                $gr->save();
              }

              CargoReceiptQuotation::updateForCargoReceipt($cargo_receipt->cargoReceiptId, $quotationIds);
            }

          }
        }

        if (!$go) {
          ResponseHelper::exit();
        }
      }

      if ($return) {
        return true;
      }

      RestUtils::sendResponseOK("PACKED DONE");

    }


    /**
     * Get picking companies
     *
     * @param bool $return
     * @return array
     */
    public function executeGetCompanies($return = false) {

      $query = "SELECT *, crm_companies.companyId as compId, crm_companies.email as compEmail, crm_persons.email as persEmail ";
      $query .= "FROM " . CrmCompanies::getTablename() . " ";
      $query .= "LEFT JOIN  " . CrmPersons::getTablename() . " ON crm_persons.companyId=crm_companies.companyId ";
      $query .= "WHERE categoryId=14 ";
      $query .= "ORDER BY crm_companies.name ";
      //      echo $query;

      $companies = [];
      $oResult3 = DBConn::db_link()->query($query);
      while ($person_o = $oResult3->fetch_object()) {
        $company = new stdClass();
        if (isset($companies[$person_o->compId])) {
          $company = $companies[$person_o->compId];
        }
        else {
          $company->companyId = $person_o->compId;
          $company->name = $person_o->name;
          $company->email = $person_o->compEmail;
          $company->carrierCode = $person_o->carrierCode;

          if ($return) {
            $query4 = "SELECT * FROM " . CrmAddresses::getTablename() . " WHERE addressId = " . $person_o->visitAddressId;
            $oResult4 = DBConn::db_link()->query($query4);
            $company->visitaddress = $oResult4->fetch_object();
          }

          $company->persons = [];
        }

        if ($person_o->personId != "") {

          $person = [];
          $person['id'] = $person_o->personId;
          $person['personid'] = $person_o->personId;
          $person['name'] = $person_o->firstName . ' ' . $person_o->lastName;
          $person['phone'] = $person_o->phone;
          $person['mobile'] = $person_o->mobile;
          $person['email'] = $person_o->persEmail;
          $person['type'] = 'other';

          $company->persons[] = $person;
        }
        $companies[$person_o->compId] = $company;
      }

      if ($return) {
        return $companies;
      }

      $dataArr = [];
      foreach ($companies as $comp) {
        $dataArr[] = $comp;
      }

      RestUtils::sendResponseOK("COMPANIES SUCCESFULLY RETRIEVED", $dataArr);

    }

    public function executeGetContainersNearby() {

      $request_vars = $this->data->getData();
      $lat = $request_vars['lat'];
      $lng = $request_vars['lng'];

      if (!isset($request_vars['lat']) || $request_vars['lat'] == "") {
        RestUtils::sendResponseError("Onbekende latitude longitude");
      }

      $container_quots = ContainersQuotations::find_all("WHERE returnDate IS NULL");
      $quotation_ids = [];
      foreach ($container_quots as $c_q) {
        $quotation_ids[] = $c_q->quotationId;
      }
      //$quots = ContainersQuotations::find_all_by(["quotationId",$quotation_ids]);

      $values = [];
      if (count($quotation_ids) > 0) {
        //ophalen alle klanten met deze quotation Ids
        $query = "SELECT Q.companyId, Q.userId, if(C.name IS NULL,sandbox_users.companyName,C.name) as name  ";
        $query .= ", AD.addressId, AD.street, AD.nr, AD.extension, AD.zipcode, AD.domestic, AD.latitude, AD.longitude ";
        $query .= ",( 6371 * acos( cos( radians(" . $lat . ") ) * cos( radians( AD.latitude ) ) * cos( radians( AD.longitude ) - radians(" . $lng . ") ) + sin( radians(" . $lat . ") ) * sin( radians( AD.latitude ) ) ) ) AS distance ";
        $query .= "FROM " . Quotations::getTablename() . " Q ";
        $query .= "JOIN " . QuotationsExtra::getTablename() . " QE ON QE.quotationId = Q.quotationId ";
        $query .= "JOIN " . CrmAddresses::getTablename() . " AD ON AD.addressId = QE.addressDeliveryId ";
        $query .= "LEFT JOIN " . CrmCompanies::getTablename() . " C ON C.companyId = Q.companyId ";
        $query .= "LEFT JOIN " . SandboxUsers::getTablename() . " ON sandbox_users.userId = Q.userId ";
        $query .= "WHERE Q.quotationId IN (" . implode(",", $quotation_ids) . ") AND AD.zipcode!='5531PP' ";
        $query .= "GROUP BY QE.addressDeliveryId "; //companyId is zero with particulier
        $query .= "ORDER BY distance ";

        $oResult = DBConn::db_link()->query($query);
        while ($row = $oResult->fetch_assoc()) {
          $row["distance"] = number_format($row["distance"], 2);
          $values[] = $row;
        }
      }

      RestUtils::sendResponseOK("CONTAINERS NEARBY RECIEVED", $values);

    }

    /**
     * Haal bestand op.
     */
    public function executeGetFile() {
      $request_vars = $this->data->getData();

      $fileId = $request_vars['fileid'];

      $query5 = "SELECT * FROM " . Files::getTablename() . " ";
      $query5 .= "WHERE files.fileId=" . escapeForDB($fileId);
      $oResult5 = DBConn::db_link()->query($query5);
      $file = $oResult5->fetch_object();


      if ($file) {
        $path = DIR_ROOT_HTTPDOCS . $file->folder . '/' . $file->filename;
        if (file_exists($path)) {
          header('Content-Type: application/octet-stream');
          header("Content-Transfer-Encoding: Binary");
          header('Content-Disposition: attachment; filename="' . $file->title . '.pdf"');
          header('Cache-Control: private, max-age=0, must-revalidate');
          header('Pragma: public');

          echo file_get_contents($path);
          ResponseHelper::exit();
        }
      }
      echo 'Bestand niet gevonden, of u heeft geen rechten.';
      ResponseHelper::exit();
    }


    /**
     * @param $userIds
     * @param $zipcode
     * @param $date
     * @param $ignore_container_nrs
     * @return array
     */
    public static function getContainersNearFar($companyId, $userIds, $zipcode, $date, $containernrs_picked_up) {

      $arrayQuotationIdsNear = [];
      $arrayQuotationIdsFar = [];

      if ($companyId != 0) {
        $user_query = "SELECT userId FROM " . Quotations::getTablename() . " WHERE companyId = '" . $companyId . "' GROUP BY userId";
        $oResult3 = DBConn::db_link()->query($user_query);
        while ($l_user = $oResult3->fetch_object()) {
          $userIds[$l_user->userId] = $l_user->userId;
        }
      }

      if (count($userIds) == 0) return [];

      $user_query = "SELECT quotationId, zipcode FROM " . Quotations::getTablename() . " WHERE userId IN (" . implode(',', $userIds) . ")";
      $oResult3 = DBConn::db_link()->query($user_query);
      $currentzip4 = substr($zipcode, 0, 4);
      while ($obj = $oResult3->fetch_object()) {
        $zip4numbers2 = substr($obj->zipcode, 0, 4);
        if ($currentzip4 == $zip4numbers2) {
          $arrayQuotationIdsNear[] = $obj->quotationId;
        }
        else {
          $arrayQuotationIdsFar[] = $obj->quotationId;
        }
      }

      if (count($arrayQuotationIdsNear) > 0 || count($arrayQuotationIdsFar) > 0) {
        $l_query = "SELECT c.*,cq.quotationId FROM " . ContainersQuotations::getTablename() . " cq ";
        $l_query .= "JOIN " . Containers::getTablename() . " c ON cq.containerId = c.containerId ";
        $l_query .= "WHERE cq.quotationId IN (" . implode(',', array_merge($arrayQuotationIdsNear, $arrayQuotationIdsFar)) . ") ";
        $l_query .= "AND cq.deliverDate<'" . $date . "'  ";
        $l_query .= "AND (cq.returndate IS NULL OR cq.returndate>'" . $date . "') ";
        $oResult3 = DBConn::db_link()->query($l_query);

        $containerNumbersNear = [];
        $containerNumbersFar = [];
        while ($obj = $oResult3->fetch_object()) {
          if (!in_array($obj->containerNumber, $containernrs_picked_up)) { //bakken van deze quotation, worden alleen gebruikt bij deze quotation
            if (in_array($obj->quotationId, $arrayQuotationIdsNear)) {
              unset($obj->quotationId);
              $obj->distance = 'near';
              $containerNumbersNear[$obj->containerId] = $obj;
            }
            else {
              unset($obj->quotationId);
              $obj->distance = 'far';
              $containerNumbersFar[$obj->containerId] = $obj;
            }
          }
        }

        usort($containerNumbersNear, "apiRdeActions::sortContainersObj");
        usort($containerNumbersFar, "apiRdeActions::sortContainersObj");

        return array_merge($containerNumbersNear, $containerNumbersFar);

      }

    }

    public function executeAttachTagToContainer() {
      $request_vars = $this->data->getData();
      $oClsAttachTagToContainer = new clsAttachTagToContainer($request_vars);
      $oClsAttachTagToContainer->attachTagToContainer();
    }

    public function executeGetemployees() {
      $employees = [];
      foreach (ProductionEmployees::find_all_by(['working' => 1], "ORDER BY name") as $employee) {
        $data = new stdClass();
        $data->id = $employee->employeeId;
        $data->name = $employee->name;
        $employees[] = $data;
      }

      RestUtils::sendResponseOK("Medewerkers gevonden", $employees);

    }

    public function executeGetmontage() {

      $montages = Montage::find_all_by(["done" => 0], "ORDER BY plandate");
      foreach ($montages as $k => $montage) {
        $crmaddress = CrmAddresses::find_by(["addressId" => $montage->company_address_id]);
        $adr = new stdClass();
        $adr->street = $crmaddress->street;
        $adr->nr = $crmaddress->nr;
        $adr->extension = $crmaddress->extension;
        $adr->zipcode = $crmaddress->zipcode;
        $adr->domestic = $crmaddress->domestic;
        $montage->address = $adr;
      }

      RestUtils::sendResponseOK("Montage items gevonden", AppModel::plainObjects($montages, ["address"]));

    }


    /**
     * Get companyid and company name by ordernumber OR tagid/ordernumber
     */
    public function executeGetCompanyInfo() {
      $rdata = $this->data->getData();

      $root_data = new stdClass();
      $customers = [];

      if (isset($rdata['orderNumber'])) {

        $orderNumber = $rdata['orderNumber'];
        $split = explode("-", $orderNumber);
        if (count($split) < 2) {
          RestUtils::sendResponseError("ERROR INVALID ORDERNUMBER: " . $orderNumber);
        }
        $quotationNumber = trim($split[0]);
        $quotationVersion = trim($split[1]);
        $quotationPart = "";
        if (isset($split[2]) && trim($split[2]) != "") {
          $quotationPart = RdeHelper::AlphaToAsciiInt(trim($split[2]));
        }

        $query = "SELECT Q.companyId, C.name AS 'companyName', Q.userId, Q.quotationId, quotations_extra.addressDeliveryId ";
        $query .= "FROM " . Quotations::getTablename() . " Q ";
        $query .= "JOIN " . QuotationsExtra::getTablename() . " ON quotations_extra.quotationId = Q.quotationId ";
        $query .= "LEFT JOIN " . CrmCompanies::getTablename() . " C ON C.companyId = Q.companyId ";
        //        $query .= "WHERE Q.statusId IN (40) "; //40 => in te pakken, 50,51 => ingepakt
        $query .= "WHERE Q.quotationNumber = '" . escapeForDB($quotationNumber) . "' AND Q.quotationVersion = '" . escapeForDB($quotationVersion) . "' ";
        if ($quotationPart != "") {
          $query .= "AND Q.quotationPart = '" . $quotationPart . "' ";
        }

        $oResult = DBConn::db_link()->query($query);
        if ($oData = $oResult->fetch_object()) {
          $root_data->quotationId = $oData->quotationId;
          $root_data->orderNumber = $orderNumber;

          //afleveradres
          $root_data->addressDeliveryId = $oData->addressDeliveryId;
          if ($root_data->addressDeliveryId != "") {
            $crmaddress = CrmAddresses::find_by(['addressId' => $root_data->addressDeliveryId]);
            $root_data->address = $crmaddress->getAddressFormatted();
          }

          $val = new stdClass();
          $val->companyId = $oData->companyId;
          $val->userId = $oData->userId;
          $customers[] = $val;
        }

      }
      elseif (isset($rdata['tagId']) || isset($rdata['containerNumber'])) {

        $query = "SELECT C.containerId, C.containerNumber, C.inStock ";
        $query .= "FROM " . Containers::getTablename() . " C ";
        if (isset($rdata['tagId'])) {
          $tagId = $rdata['tagId'];
          $query .= "JOIN " . ContainerTag::getTablename() . " CT ON C.containerId = CT.containerId ";
          $query .= "WHERE CT.tagId = '" . $tagId . "' ";
        }
        else {
          $containerNumber = $rdata['containerNumber'];
          $query .= "WHERE C.containerNumber = '" . $containerNumber . "' ";
        }


        //logToFile("mysql",$query);

        $result = DBConn::db_link()->query($query);
        $container_scanned = $result->fetch_object();
        if (!$container_scanned) {
          RestUtils::sendResponseError("Onbekende bestelling of container.");
        }
        else {

          $root_data->containerId = $container_scanned->containerId;
          $root_data->containerNumber = $container_scanned->containerNumber;

          //alle bedrijven die nu in deze container liggen.
          $query = "SELECT Q.companyId, Q.userId, quotations_extra.addressDeliveryId  ";
          $query .= "FROM " . ContainersQuotations::getTablename() . " CQ ";
          $query .= "LEFT JOIN " . Quotations::getTablename() . " Q ON CQ.quotationId = Q.quotationId ";
          $query .= "LEFT JOIN " . QuotationsExtra::getTablename() . " ON quotations_extra.quotationId = Q.quotationId ";
          $query .= "WHERE CQ.containerId=" . $container_scanned->containerId . " ";
          $query .= "AND Q.statusId>=20 AND Q.statusId<55 "; //besteld, maar nog niet bezorgd
          $query .= "AND CQ.returnDate IS NULL "; //nog niet terug
          $query .= "GROUP BY Q.companyId ";

          $result = DBConn::db_link()->query($query);
          while ($oData = $result->fetch_object()) {
            $val = new stdClass();
            $val->companyId = $oData->companyId;
            $val->userId = $oData->userId;
            $customers[] = $val;

            if ($oData->addressDeliveryId != "") {
              $root_data->addressDeliveryId = $oData->addressDeliveryId;
              if ($root_data->addressDeliveryId != "") {
                $crmaddress = CrmAddresses::find_by(['addressId' => $oData->addressDeliveryId]);
                $root_data->address = $crmaddress->getAddressFormatted();
              }

            }

          }

        }
      }

      if (count($customers) == 0) {
        RestUtils::sendResponseError("Geen informatie gevonden.");
      }

      $root_data->customers = [];
      foreach ($customers as $cust) {

        $data = new stdClass();

        $containers_about = [];
        $containers_other = [];
        $all_quotations = [];
        $all_quotations_unique = [];

        if ($cust->companyId != "" && $cust->companyId != "0") {

          //alle actieve quotations ophalen met hun containers van deze klant
          //ophalen alle geproduceerde orders, gegroeppeerd per klant
          $query = "SELECT Q.quotationId, Q.quotationNumber, Q.quotationVersion, Q.quotationPart, Q.statusId, Q.projectName, Q.projectReference, C.containerId, C.containerNumber  ";
          $query .= "FROM " . Quotations::getTablename() . " Q ";
          $query .= "LEFT JOIN " . ContainersQuotations::getTablename() . " CQ ON CQ.quotationId = Q.quotationId ";
          $query .= "LEFT JOIN " . Containers::getTablename() . " C ON C.containerId = CQ.containerId ";
          $query .= "WHERE Q.statusId>=20 AND Q.statusId<55 "; //besteld, maar nog niet bezorgd
          $query .= "AND Q.companyId = " . $cust->companyId . " ";
          if ($cust->companyId == "") {
            $query .= "AND Q.userId= " . $cust->userId . " ";
          }
          else {
            $query .= "AND Q.companyId = " . $cust->companyId . " ";
          }
          $query .= "ORDER BY Q.quotationNumber ";

          $result = DBConn::db_link()->query($query);
          while ($oData = $result->fetch_object()) {
            $all_quotations[] = $oData;

            if (!isset($all_quotations_unique[$oData->quotationId])) {
              $new = clone $oData;
              unset($new->containerId, $new->containerNumber);
              $all_quotations_unique[$oData->quotationId] = $new;
            }

          }
        }

        //search containerNr of scanned quotations
        $containerId = false;
        if (isset($root_data->containerId)) {
          $containerId = $root_data->containerId;
        }
        else {
          foreach ($all_quotations as $quot) {
            if ($quot->quotationId == $root_data->quotationId) {
              $containerId = $quot->containerId;
              break;
            }
          }
        }

        //groepeer orders
        foreach ($all_quotations as $quot) {
          if (($containerId != "" && $quot->containerId == $containerId) || (isset($data->quotationId) && $data->quotationId == $quot->quotationId)) { //dit is de container waar het om gaat.
            if (!isset($containers_about[$quot->containerId])) {
              $containers_about[$quot->containerId] = new stdClass();
              $containers_about[$quot->containerId]->containerId = $quot->containerId;
              $containers_about[$quot->containerId]->containerNumber = $quot->containerNumber;
              $containers_about[$quot->containerId]->quotations = [];
            }
            $containers_about[$quot->containerId]->quotations[] = $quot->quotationId;
          }
          else {
            if (!isset($containers_other[$quot->containerId])) {
              $containers_other[$quot->containerId] = new stdClass();
              $containers_other[$quot->containerId]->containerId = $quot->containerId;
              $containers_other[$quot->containerId]->containerNumber = $quot->containerNumber;
              $containers_other[$quot->containerId]->quotations = [];
            }
            $containers_other[$quot->containerId]->quotations[] = $quot->quotationId;
          }
        }

        $data->containers_about = $containers_about;
        $data->containers_other = $containers_other;
        $data->quotations = $all_quotations_unique;
        $data->persons = [];
        $userIds = [];

        if (isset($cust->companyId)) {


          $query2 = "SELECT * FROM " . CrmCompanies::getTablename() . " WHERE companyId = " . $cust->companyId;
          $oResult2 = DBConn::db_link()->query($query2);
          $company = $oResult2->fetch_object();
          $data->companyName = $company->name;

          //alle personen gekoppeld bij dit bedrijf
          $query3 = "SELECT * FROM " . CrmPersons::getTablename() . " WHERE flagForDeletion=0 AND companyId = " . $cust->companyId;
          $oResult3 = DBConn::db_link()->query($query3);
          while ($person_o = $oResult3->fetch_object()) {
            $person = [];
            $person['id'] = ($person_o->personId + 1000000);
            $person['personid'] = $person_o->personId;
            $person['userid'] = 0;
            $person['name'] = $person_o->firstName . ' ' . $person_o->lastName;
            $person['phone'] = $person_o->phone;
            $person['mobile'] = $person_o->mobile;
            $person['email'] = $person_o->email;
            $person['type'] = 'other';
            $data->persons[] = $person;
          }

          //sandboxuser gekoppeld aan bestelling
          $person_o = SandboxUsers::find_by(["userId" => $cust->userId]);
          if ($person_o) {
            $found_main_user = false;
            foreach ($data->persons as $k => $p) {
              if ($p['personid'] != "" && $p['personid'] == $person_o->personId) {
                $data->persons[$k]['type'] = 'contact';
                $data->persons[$k]['userid'] = $person_o->userId;
                $found_main_user = true;
                break;
              }
            }
            if (!$found_main_user) {
              $person = [];
              $person['id'] = $person_o->userId;
              $person['personid'] = ($person_o->personId == "" ? 0 : $person_o->personId);
              $person['userid'] = $person_o->userId;
              $person['name'] = $person_o->firstName . ' ' . $person_o->lastName;
              $person['phone'] = $person_o->phone;
              $person['mobile'] = $person_o->mobile;
              $person['email'] = $person_o->email;
              $person['type'] = 'contact';
              $data->persons[] = $person;
              if ($person_o->userId != "")
                $userIds[$person_o->userId] = $person_o->userId;
            }
          }

          if (isset($data->executorPersonId) && $data->executorPersonId != "") {
            $person_o = CrmPersons::find_by(["personId" => $data->executorPersonId]);
            if ($person_o) {
              $found_executor = false;
              foreach ($data->persons as $k => $p) {
                if ($p) {
                  $data->persons[$k]['type'] = 'executor';
                  $found_executor = true;
                  break;
                }
              }
              if (!$found_executor) {
                $person = [];
                $person['id'] = ($person_o->personId + 1000000);
                $person['personid'] = $person_o->personId;
                $person['userid'] = 0;
                $person['name'] = $person_o->firstName . ' ' . $person_o->lastName;
                $person['phone'] = $person_o->phone;
                $person['mobile'] = $person_o->mobile;
                $person['email'] = $person_o->email;
                $person['type'] = 'executor';
                $data->persons[] = $person;
              }
            }


          }

        }
        else { //geen company gekoppeld. Dus nog particuliere prijzen (kan wel een bedrijf zijn natuurlijk)

          $person_o = SandboxUsers::find_by(["userId" => $cust->userId]);

          $person = [];
          $person['id'] = $person_o->userId;
          $person['personid'] = ($person_o->personId == "" ? 0 : $person_o->personId);
          $person['userid'] = $person_o->userId;
          $person['name'] = $person_o->firstName . ' ' . $person_o->lastName;
          $person['phone'] = $person_o->phone;
          $person['mobile'] = $person_o->mobile;
          $person['email'] = $person_o->email;
          $person['type'] = 'contact';
          if ($person_o->userId != "")
            $userIds[$person_o->userId] = $person_o->userId;
          $data->persons[] = $person;
        }

        usort($data->persons, "apiRdeActions::sortPersons");

        $data->containers_about = array_values($data->containers_about);
        $data->containers_other = array_values($data->containers_other);
        $data->quotations = array_values($data->quotations);

        $root_data->customers[] = $data;
      }

      //also include given orderNumber
      RestUtils::sendResponseOK("COMPANYID AND NAME SUCCESFULL RETRIEVED FROM ORDERNUMBER", [$root_data]);
    }

    /**
     * Get companyid and company name by ordernumber OR tagid/ordernumber
     */
    public function executeGetCompanyByTag() {
      $rdata = $this->data->getData();

      $root_data = new stdClass();
      $customers = [];

      $query = "SELECT C.containerId, C.containerNumber, C.inStock ";
      $query .= "FROM " . Containers::getTablename() . " C ";
      if (isset($rdata['tagId'])) {
        $tagId = $rdata['tagId'];
        $query .= "JOIN " . ContainerTag::getTablename() . " CT ON C.containerId = CT.containerId ";
        $query .= "WHERE CT.tagId = '" . $tagId . "' ";
      }
      else {
        $containerNumber = $rdata['containerNumber'];
        $query .= "WHERE C.containerNumber = '" . $containerNumber . "' ";
      }


      //logToFile("mysql",$query);

      $result = DBConn::db_link()->query($query);
      $container_scanned = $result->fetch_object();
      if (!$container_scanned) {
        RestUtils::sendResponseError("Onbekende container.");
      }
      else {

        $root_data->containerId = $container_scanned->containerId;
        $root_data->containerNumber = $container_scanned->containerNumber;

        //alle bedrijven die nu in deze container liggen.
        $query = "SELECT Q.companyId, Q.userId ";
        $query .= "FROM " . ContainersQuotations::getTablename() . " CQ ";
        $query .= "LEFT JOIN " . Quotations::getTablename() . " Q ON CQ.quotationId = Q.quotationId ";
        $query .= "WHERE CQ.containerId=" . $container_scanned->containerId . " ";
        $query .= "AND CQ.returnDate IS NULL "; //nog niet terug
        $query .= "ORDER BY containerQuotationId DESC LIMIT 1 "; //kan altijd maar 1 klant zijn

        $result = DBConn::db_link()->query($query);
        $customer = false;
        while ($oData = $result->fetch_object()) {
          $root_data->companyId = $oData->companyId;
          $root_data->userId = $oData->userId;
        }
      }

      if (!isset($root_data->companyId)) {
        RestUtils::sendResponseError("Geen bestellingen/containers gevonden.");
      }

      //also include given orderNumber
      RestUtils::sendResponseOK("COMPANYID AND NAME SUCCESFULL RETRIEVED FROM ORDERNUMBER", [$root_data]);
    }

    public function executeSetorderproduced() {
      $rdata = $this->data->getData();

      if (!isset($rdata['orderNumber'])) {
        RestUtils::sendResponseError("Ordernummer niet gezet.");
      }

      $orderNumber = $rdata['orderNumber'];
      $split = explode("-", $orderNumber);
      if (count($split) < 2) {
        RestUtils::sendResponseError("ERROR INVALID ORDERNUMBER: " . $orderNumber);
      }
      $quotationNumber = trim($split[0]);
      $quotationVersion = trim($split[1]);
      $quotationPart = "";
      if (isset($split[2]) && trim($split[2]) != "") {
        $quotationPart = RdeHelper::AlphaToAsciiInt(trim($split[2]));
      }
      $filt = [
        "quotationNumber"  => $quotationNumber,
        "quotationVersion" => $quotationVersion,
      ];
      if ($quotationPart != "") {
        $filt["quotationPart"] = $quotationPart;
      }

      $quotation = Quotations::find_by($filt);
      if (!$quotation) {
        RestUtils::sendResponseError("Bestelling niet gevonden met bestelnummer " . $orderNumber);
      }

      if ($quotation->statusId != Status::STATUS_PREPARED && $quotation->statusId != Status::STATUS_IN_PRODUCTION) {
        RestUtils::sendResponseError("Bestelling heeft niet status 'Voorbereid print' of 'In productie'. Niks aangepast. Bestelnummer " . $quotationNumber);
      }

      $oldStatus = $quotation->statusId;
      $quotation->statusId = Status::STATUS_PRODUCED;
      if ($quotation->produceDate == "") {
        $quotation->produceDate = date("Y-m-d");
      }
      $quotation->save();

      $eq = new EmployeeQuotation();
      $eq->quotationId = $quotation->quotationId;
      $eq->employeeId = gsdApiSession::getUserId();
      $eq->enddate = date('Y-m-d H:i:s');
      $eq->save();

      if ($rdata['rack'] != "") {

        $rack = GeneratedRackIds::find_by(["rackScanCode" => $rdata['rack']]);
        if ($rack) {
          $grie = new GeneratedRackIdsEmployees();
          $grie->employeeId = gsdApiSession::getUserId();
          $grie->rackId = $rack->rackId;
          $grie->save();
        }
      }

      if ($oldStatus == Status::STATUS_PREPARED && in_array($quotation->brandId, StoneBrands::getBretiBrandIds())) {
        //de status was STATUS_PREPARED word STATUS_PRODUCED en is een merk dat door Breti word gemaakt. Dus vergeten op 38 (STATUS_IN_PRODUCTION) te zetten.
        MailsFactory::sendBretiDoneEmail($quotation);
      }

      //also include given orderNumber
      RestUtils::sendResponseOK("Bestelling op geproduceerd gezet.", ["ordernummer" => $orderNumber]);
    }


    //endregion GET
    //region POST

    /**
     * Klikken op knop inpakken > overzicht > knop inpakken
     */
    public function executeSaveAddressSplit() {
      $request_vars = $this->data->getData();
      //opslaan addressSplit

      $addressSplitRows = RestUtils::getPostvaluesByKey("addressSplitRows", $request_vars);
      $packingDate = RestUtils::getPostvaluesByKey('packingDate', $request_vars);

      if ($addressSplitRows !== false && count($addressSplitRows) > 0 && $packingDate !== false) {

        $quotationIds = [];
        $quotationSplitIds = [];
        foreach ($addressSplitRows as $obj_value) {
          $quotationIds[] = $obj_value->quotationId;
          $quotationSplitIds[$obj_value->quotationId] = $obj_value->addressSplit;
        }

        if (count($quotationIds) > 0) {

          $quotations = Quotations::find_all_by(["quotationId" => $quotationIds, "statusId" => [35, 40]]);
          foreach ($quotations as $quotation) {
            $quotation->packingDate = $packingDate;
            if ($quotation->priceDate == "0000-00-00" || $quotation->priceDate == "") {
              $quotation->priceDate = $quotation->quotationDate;
            }
            $quotation->save();

            $quotations_extra = QuotationsExtra::find_by(["quotationId" => $quotation->quotationId]);
            if ($quotations_extra->addressSplit != $quotationSplitIds[$quotation->quotationId]) {
              $quotations_extra->addressSplit = $quotationSplitIds[$quotation->quotationId];
              if ($quotations_extra->stoneDeliveryDate == "0000-00-00") {
                $quotations_extra->stoneDeliveryDate = null;
              }
              $quotations_extra->save();
            }
          }
          RestUtils::sendResponseOK("saveAddressSplit successfully saved");

        }
      }

      RestUtils::sendResponseError("ACTION POST : saveAddressSplit : ERROR OCCURRED DURING SAVING");

    }

    /**
     * Orders koppelen aan containers.
     * Oftewel Inpakken app verwerken.
     */
    public function executeAttachOrdersToContainers() {
      $request_vars = $this->data->getData();

      $dataRows = RestUtils::getPostvaluesByKey("rows", $request_vars);


      if (empty($dataRows)) {
        logToFile("attachOrdersToContainers", print_r($dataRows, true));
        RestUtils::sendResponseError("Geen data ontvangen in AttachOrdersToContainers.");
      }

      $quotations = [];
      $container_ids = [];

      foreach ($dataRows as $obj_value) {
        if (isset($obj_value->quotationId) && $obj_value->quotationId != "" && isset($obj_value->containers)) {

          $quotation = Quotations::find_by(["quotationId" => $obj_value->quotationId, "statusId" => [35, 40]]);  //35, 40 (35 is van webshop)
          if ($quotation) {

            if (isset($obj_value->weight_webshop) && $obj_value->weight_webshop != "0" && is_numeric($obj_value->weight_webshop)) {
              $quotation->weight_webshop = $obj_value->weight_webshop;
              //save is done later on
            }
            if (isset($obj_value->webshop_only) && $obj_value->webshop_only == "1") {
              $quotation->webshop_only = true;
            }

            foreach ($obj_value->containers as $contValue) {
              $cq = new ContainersQuotations();
              $cq->quotationId = $obj_value->quotationId;
              $cq->containerId = $contValue->containerId;
              $cq->save();

              $container_ids[$contValue->containerId] = $contValue->containerId;
            }

            $quotations[$quotation->quotationId] = $quotation;

          }

        }
      }

      //update container inStock to wait W
      if (count($container_ids) > 0) {
        foreach (Containers::find_all_by(["containerId" => $container_ids]) as $c) {
          if ($c->containerNumber == "0000") continue;
          $c->inStock = 'W';
          $c->save();
        }
      }

      //updaten status van quotations
      $quotation_ids = [];
      foreach ($quotations as $quotation) {

        $newstatus = Status::STATUS_PACKED;
        if (isset($quotation->webshop_only) && $quotation->webshop_only) $newstatus = Status::STATUS_DELIVERED; //alleen webshop bestelling direct naar geleverd.

        $quotation->statusId = $newstatus;
        $quotation->save();
        QuotationStatus::register($quotation->quotationId, $quotation->statusId, gsdApiSession::getUserId());
        $quotation_ids[] = $quotation->quotationId;
      }

      //wanneer container word opgehaald, direct email verzenden
      MailsFactory::containerPickup($quotation_ids);

      RestUtils::sendResponseOK("attachOrdersToContainers successfully saved");


    }

    /**
     * Save container images
     *
     * @param stdClass[] $request_vars
     */
    public function executeUploadContainerImages() {
      $request_vars = $this->data->getData();

      $base_path = DIR_ROOT_HTTPDOCS . "filesystem/raamdorpel/clients/container/";
      $success = true;

      $count = isset($request_vars['containerId']) ? count($request_vars['containerId']) : 0;

      logToFile("uploadContainerImage", print_r($_FILES, true));
      logToFile("uploadContainerImage", print_r($request_vars, true));

      for ($i = 0; $i < $count; $i++) {
        //loop over elke container heen, en maak ContainerImage aan.

        if (!isset($_FILES['containerImage']['name'][$i])) {
          continue;
        }

        $containerId = $request_vars['containerId'][$i];
        $quotationIds = $request_vars['quotationIds'][$i];

        $imageplaced = false;
        foreach ($quotationIds as $quotationId) {

          $filename = $_FILES['containerImage']['name'][$i];
          if (!$imageplaced) {
            $target_path = $base_path . $containerId . "/";
            if (!file_exists($target_path)) { //create path if it doesn't exists
              mkdir($target_path);
            }
            $target_path .= $filename;
            move_uploaded_file($_FILES['containerImage']['tmp_name'][$i], $target_path);

            ImageHelper::compress($target_path);//verklein afbeelding

            $imageplaced = true;
          }

          $cim = new ContainerImg();
          $cim->containerId = $containerId;
          $cim->quotationId = $quotationId;
          $cim->filename = $filename;
          $cim->statusPhotoId = 1;
          $cim->save();

          logToFile("uploadContainerImage", "Image succesvol gekoppeld");
        }
      }

      if ($success) {
        RestUtils::sendResponseOK("Afbeeldingen succesvol verzonden");
      }
      else {
        RestUtils::sendResponseError("Er is een fout opgetreden tijdens het uploaden van de afbeeldingen");
      }
    }

    /**
     * Retour containers.
     */
    public function executeSetContainersRetour() {

      $request_vars = $this->data->getData();
      logToFile('setContainersRetour', print_r($request_vars, true));

      //cargo receipt niet updaten bij container retour app / wel bij laden container retour
      $update_cargo_receipt = (isset($_GET["updatecargoreceipt"]) && $_GET["updatecargoreceipt"] == "1" || $this->version < 1.6);

      $dataRows = RestUtils::getPostvaluesByKey("rows", $request_vars);
      //      $truckId = RestUtils::getPostvaluesByKey("truckId", $request_vars);
      //      $date = date('Y-m-d',RestUtils::getPostvaluesByKey("date", $request_vars)/1000); //convert to php timestamp

      if ($dataRows !== false && count($dataRows) > 0) {

        $container_ids = [];
        foreach ($dataRows as $obj_value) {
          $containerId = $obj_value->containerId;
          $containerNumber = $obj_value->containerNumber;

          $container_ids[] = $containerId;

          if ($update_cargo_receipt) {
            //zet de bakken op de cargo_receipt
            //ophalen van een quotation van deze container, welke nog niet retour is.
            $query = "SELECT quotationId ";
            $query .= "FROM " . ContainersQuotations::getTablename() . " ";
            $query .= "WHERE containerId=" . $containerId . " AND returnDate IS NULL ";
            $query .= "LIMIT 1 ";
            $oResult = DBConn::db_link()->query($query);
            if ($quotationRow = $oResult->fetch_object()) {

              $quotationId = $quotationRow->quotationId;

              //pak route van deze quotation
              $query1 = "SELECT gpsbuddy_routes.routeId, gpsbuddy_routes.cargoReceiptId FROM " . GpsbuddyRde::getTablename() . " ";
              $query1 .= "JOIN " . GpsbuddyRoutes::getTablename() . " ON gpsbuddy_rde.routeId = gpsbuddy_routes.routeId ";
              $query1 .= "WHERE gpsbuddy_rde.quotationId=" . $quotationId . " ";
              $oResult1 = DBConn::db_link()->query($query1);
              $gpsbuddy_route_row = $oResult1->fetch_object();

              //            $gpsbuddy_route_row = new stdClass();
              //            $gpsbuddy_route_row->cargoReceiptId = 1912;
              if ($gpsbuddy_route_row && !empty($gpsbuddy_route_row->cargoReceiptId)) {

                $cargo_receipt = CargoReceipt::find_by(["cargoReceiptId" => $gpsbuddy_route_row->cargoReceiptId]);

                if ($cargo_receipt) {

                  $retour_op_offerte_ar = [];
                  if ($cargo_receipt->staticNumberRetour != "") {
                    $bakken = explode("\n", $cargo_receipt->staticNumberRetour);
                    if (count($bakken) > 0) {
                      $retour_op_offerte_ar += $bakken;
                    }
                  }
                  $retour_op_offerte_ar[] = $containerNumber;

                  if (count($retour_op_offerte_ar) > 0) {
                    $retour_op_offerte_ar = array_unique($retour_op_offerte_ar);
                    sort($retour_op_offerte_ar);
                    $containers_retour = implode("\n", $retour_op_offerte_ar);

                    //zet deze container op de cargo_receipt erbij
                    $cargo_receipt->staticNumberRetour = $containers_retour;
                    $cargo_receipt->save();
                  }
                }
              }
            }
          }

        }


        if (count($container_ids) > 0) {

          //aller container_quotations van deze container returnDate zetten. De container is immers terug.
          foreach (ContainersQuotations::find_all_by(["containerId" => $container_ids], "AND returnDate IS NULL") as $cq) {
            if ($cq->deliverDate == "" || $cq->deliverDate == null) {
              $cq->deliverDate = date('Y-m-d');
              $cq->deliverUserId = gsdApiSession::getUserId();
            }
            $cq->returnUserId = gsdApiSession::getUserId();
            $cq->returnDate = date('Y-m-d');
            $cq->save();
          }

          //zet de geretourneerde containers in stock.
          $containers = Containers::find_all_by(["containerId" => $container_ids]);
          foreach ($containers as $c) {
            $c->inStock = 'Y';
            $c->save();
          }

          if (!$update_cargo_receipt) {
            //geen update cargo, dan email.
            MailsFactory::containerRetourApp($containers);
          }

        }


        RestUtils::sendResponseOK("setContainersRetour: success");

        return;
      }

      RestUtils::sendResponseError("ACTION POST : setContainersRetour: ERROR OCCURRED DURING SAVING");

    }

    /**
     * Retour containers.
     */
    public function executeSetContainersMaintenance() {

      $request_vars = $this->data->getData();
      logToFile('setContainersMaintenance', print_r($request_vars, true));

      $data = RestUtils::getPostvaluesByKey("data", $request_vars);

      if ($data->containerId != "") {

        $cont = Containers::find_by(["containerId" => $data->containerId]);
        if ($cont) {
          $cont->action = 1;
          $cont->actionRemark = $data->actionRemark;
          $cont->save();
        }

        RestUtils::sendResponseOK("setContainersMaintenance: success");

        return;
      }

      RestUtils::sendResponseError("ACTION POST : setContainersMaintenance: ERROR OCCURRED DURING SAVING");

    }

    /**
     * Send message
     */
    public function executeSendMessage() {

      $request_vars = $this->data->getData();
      logToFile('message', print_r($request_vars, true));
      logToFile("message", print_r($_FILES, true));

      $msg = $request_vars["message"];

      $files = [];
      if (isset($_FILES["images"])) {
        foreach ($_FILES["images"]["name"] as $i => $filename) {
          $target_path = DIR_TEMP . $filename;
          move_uploaded_file($_FILES['images']['tmp_name'][$i], $target_path);
          $files[] = $target_path;
        }
      }
      $subject = 'Bericht vanuit App';
      if (isset($request_vars["subject"]) && $request_vars["subject"] != "") {
        $subject = $request_vars["subject"];
      }
      $message = "Bericht vanuit App:<br/><br/>" . nl2br($msg);

      $gsd_mailer = GsdMailer::build(MAIL_FROM, $subject, $message);
      $gsd_mailer->setFiles($files);
      $gsd_mailer->send();

      RestUtils::sendResponseOK("message: success");

    }

    /**
     * Send vgm
     */
    public function executeSetVgm() {

      $request_vars = $this->data->getData();

      $json = RestUtils::getPostvaluesByKey("json", $request_vars);

      logToFile('vgm', print_r($json, true));

      $vgm = new Vgm();
      $vgm->employee_id = gsdApiSession::getUserId();
      $vgm->customer_user_id = $json->customer_user_id;
      $vgm->customer_name = $json->customer_name;
      $vgm->quotation_id = $json->quotation_id;
      $vgm->partner = $json->partner;
      $vgm->date = getTSFromStr($json->date);
      $vgm->employees = $json->employees;
      $vgm->location = $json->location;
      $vgm->job = $json->job;
      $vgm->improvement = $json->improvement == 1 ? 1 : 0;
      $vgm->save();

      foreach ($json->vgm_checks as $in) {
        $vgmcheck = new VgmCheck();
        $vgmcheck->code = $in->code;
        $vgmcheck->check = $in->check;
        if (isset($in->remark)) {
          $vgmcheck->remark = $in->remark;
        }
        $vgmcheck->vgm_id = $vgm->id;
        $vgmcheck->save();
        $vgm->checks[] = $vgmcheck;
      }

      $subject = 'VGM-werkplekinspectielijst ingevuld';
      $message = "<style>td {vertical-align: top;}</style>";
      $message .= "VGM-werkplekinspectielijst ingevuld:<br/><br/>";
      $message .= "<table>";
      $message .= "<tr><td>Datum inspectieronde:</td><td>" . date("d-m-Y", strtotime($vgm->date)) . "</td></tr>";
      $message .= "<tr><td>Inspectieronde door:</td><td>" . gsdApiSession::getUser()->name . "</td></tr>";
      $message .= "<tr><td>Vergezeld door:</td><td>" . $vgm->partner . "</td></tr>";
      $message .= "<tr><td>Werknemers:</td><td>" . $vgm->employees . "</td></tr>";
      $message .= "<tr><td>Opdrachtgever:</td><td>" . $vgm->customer_name . "</td></tr>";
      $message .= "<tr><td>Locatie:</td><td>" . $vgm->location . "</td></tr>";
      $message .= "<tr><td>Uit te voeren werk:</td><td>" . $vgm->job . "</td></tr>";

      $message .= "<tr><td>Onderwerpen:</td><td><table>";
      foreach ($vgm->checks as $check) {
        if ($check->check > 0) {
          $message .= "<tr><td>" . VgmCheck::$codes[$check->code] . "</td><td>" . $check->getCheckDesc() . "</td><td>" . $check->remark . "</td></tr>";
        }
      }
      $message .= "</table></td></tr>";
      $message .= "<tr><td>Verbeterrapport noodzakelijk:</td><td>" . ($vgm->improvement == 1 ? 'Ja' : 'Nee') . "</td></tr>";


      $message .= "</table>";


      $gsd_mailer = GsdMailer::build(MAIL_FROM, $subject, $message);
      $gsd_mailer->send();

      RestUtils::sendResponseOK("message: success");

    }

    /**
     * Send vgm
     */
    public function executeSendMontages() {

      $request_vars = $this->data->getData();

      $json = RestUtils::getPostvaluesByKey("json", $request_vars);

      //logToFile('montage',print_r($request_vars, true));
      logToFile('montage', print_r($json, true));

      foreach ($json as $montage_in) {
        $montage = Montage::find_by_id($montage_in->id);
        if (!$montage) {
          logToFile('montage', "Montage niet gevonden!? " . print_r($montage_in, true));
          continue;
        }

        //alles leegmaken.
        foreach (MontageQuotation::find_all_by(["montage_id" => $montage->id]) as $mq) {
          MontageQuotationEmpl::delete_by(["montage_quotation_id" => $mq->id]);
          $mq->destroy();
        }

        $montage->quotations = [];
        foreach ($montage_in->montage_quotation as $montage_quotation_in) {
          $mq = new MontageQuotation();
          $mq->montage_id = $montage->id;
          $mq->quotation_id = $montage_quotation_in->quotation_id;
          $mq->specie = $montage_quotation_in->specie ?? 0;
          $mq->specie_dikte = $montage_quotation_in->specie_dikte ?? 0;
          $mq->meters = $montage_quotation_in->meters ?? 0;
          $mq->arbeidsduur = $montage_quotation_in->arbeidsduur;
          $mq->bereikbaar_voor = $montage_quotation_in->bereikbaar_voor;
          $mq->bereikbaar_achter = $montage_quotation_in->bereikbaar_achter;
          $mq->slopen = $montage_quotation_in->slopen;
          $mq->voegen = $montage_quotation_in->voegen;
          if (isset($montage_quotation_in->remark)) $mq->remark = $montage_quotation_in->remark;
          $mq->save();

          $mq->employees = [];
          foreach ($montage_quotation_in->montage_quotation_empl as $montage_quotation_empl_in) {
            $mqe = new MontageQuotationEmpl();
            $mqe->montage_quotation_id = $mq->id;
            $mqe->employee_id = $montage_quotation_empl_in->employee_id;
            $mqe->save();
            $mq->employees[] = $mqe;
          }

          $montage->quotations[] = $mq;

        }

        $company = CrmCompanies::find_by(["companyId" => $montage->company_id]);

        $subject = 'Montage afgerond bij ' . $company->name;
        $message = "<style>td {vertical-align: top;}</style>";
        $message .= "Montage afgerond bij " . $company->name . ".<br/><br/>";
        $message .= "<table>";
        foreach ($montage->quotations as $quot) {

          $query2 = "SELECT * FROM " . Quotations::getTablename() . " WHERE quotations.quotationId = " . $quot->quotation_id;
          $oResult2 = DBConn::db_link()->query($query2);
          $quotation = $oResult2->fetch_object();

          $name = $quotation->quotationNumber . '-' . $quotation->quotationVersion;
          if ($quotation->quotationPart != "") {
            $name .= '-' . $quotation->quotationPart; //is als letter zichtbaar
          }
          $message .= "<tr><td>Bestelling:</td><td>" . $name . "</td></tr>";
          $message .= "<tr><td>Speciezakken:</td><td>" . $quot->specie . "</td></tr>";
          $message .= "<tr><td>Speciedikte:</td><td>" . $quot->specie_dikte . " cm</td></tr>";
          $message .= "<tr><td>Raamdorpels:</td><td>" . $quot->specie_dikte . " m</td></tr>";
          $message .= "<tr><td>Arbeid:</td><td>" . $quot->arbeidsduur . " uren</td></tr>";
          $message .= "<tr><td>Bereikbaarheid voor:</td><td>" . $quot->bereikbaar_voor . "</td></tr>";
          $message .= "<tr><td>Bereikbaarheid achter:</td><td>" . $quot->bereikbaar_achter . "</td></tr>";
          $message .= "<tr><td>Slopen:</td><td>" . $quot->slopen . "</td></tr>";
          $message .= "<tr><td>Voegen:</td><td>" . $quot->voegen . "</td></tr>";
          $message .= "<tr><td>Medewerkers:</td><td>";
          foreach ($mq->employees as $employee) {
            $empl = ProductionEmployees::find_by(["employeeId" => $employee->employee_id]);
            if ($empl) {
              $message .= $empl->name . "<br/>";
            }
          }
          $message .= "</td></tr>";
          $message .= "<tr><td></td><td></td></tr>";
        }
        $message .= "</table>";


        $gsd_mailer = GsdMailer::build(MAIL_FROM, $subject, $message);
        $gsd_mailer->send();


        $montage->done = 1;
        $montage->save();
      }

      RestUtils::sendResponseOK("Montage verzonden.");

    }


    //endregion

  }