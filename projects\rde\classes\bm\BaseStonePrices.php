<?php
class BaseStonePrices extends AppModel
{
  const DB_NAME = 'rde_b1mo';
  const TABLE_NAME = 'stone_prices';
  const OM_CLASS_NAME = 'StonePrices';
  const columns = ['id', 'stoneId', 'price', 'price_m2', 'price_piece', 'price_mitre', 'validFrom', 'validTo'];
  const field_structure = [
    'id'                          => ['type' => 'int', 'length' => '5', 'null' => false],
    'stoneId'                     => ['type' => 'int', 'length' => '4', 'null' => false],
    'price'                       => ['type' => 'decimal', 'length' => '6,2', 'null' => true],
    'price_m2'                    => ['type' => 'decimal', 'length' => '6,2', 'null' => true],
    'price_piece'                 => ['type' => 'decimal', 'length' => '6,2', 'null' => true],
    'price_mitre'                 => ['type' => 'decimal', 'length' => '6,2', 'null' => true],
    'validFrom'                   => ['type' => 'date', 'length' => '', 'null' => false],
    'validTo'                     => ['type' => 'date', 'length' => '', 'null' => false],
  ];

  protected static $primary_key = ['id'];
  protected $auto_increment = 'id';

  public $id, $stoneId, $price, $price_m2, $price_piece, $price_mitre, $validFrom, $validTo;

  function __construct($obj_arr = [], &$error_codes = []) {
    parent::__construct();
    $this->setDefaults();
    if (!empty($obj_arr)) {
      $this->from_array($obj_arr);
      $this->valid_required_fields($error_codes);
    }
  }

  /**
  * Set default object values
  **/
  public function setDefaults() {
    $this->validTo = '9999-12-31';
  }

  /**
   * @param array $conditions (required)
   * @param string $raw_sql (optional)
   * @return StonePrices[]
   **/
  public static function find_all_like($conditions, $raw_sql = '') {
    return parent::generic_find_all_like($conditions, static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param array $conditions (required)
   * @param string $raw_sql (optional)
   * @return StonePrices[]
   **/
  public static function find_all_by($conditions, $raw_sql = '') {
    return parent::generic_find_all_by($conditions, static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param string $raw_sql (optional)
   * @return StonePrices[]
   **/
  public static function find_all($raw_sql = '') {
    return parent::generic_find_all(static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param array $conditions (required)
   * @param string $raw_sql (optional)
   * @return StonePrices
   **/
  public static function find_by($conditions, $raw_sql = '') {
    return parent::generic_find_by($conditions, static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param string $id (required)
   * @param string $raw_sql (optional)
   * @return StonePrices
   **/
  public static function find_by_id($id, $raw_sql = '') {
    return parent::generic_find_by_id($id, static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param array $conditions (required)
   * @param string $raw_sql (optional)
   * @return int
   **/
  public static function count_all_by($conditions, $raw_sql = '') {
    return parent::generic_count_all_by($conditions, static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param array $conditions (required)
   * @param string $raw_sql (optional)
   * @return bool
   **/
  public static function delete_by($conditions, $raw_sql = '') {
    return parent::generic_delete_by($conditions, static::OM_CLASS_NAME, $raw_sql);
  }

}