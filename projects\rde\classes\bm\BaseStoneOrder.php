<?php
class BaseStoneOrder extends AppModel
{
  const DB_NAME = 'rde_b1mo';
  const TABLE_NAME = 'stone_order';
  const OM_CLASS_NAME = 'StoneOrder';
  const columns = ['id', 'brand_id', 'status', 'senddate', 'receiveddate', 'emailtext'];
  const field_structure = [
    'id'                          => ['type' => 'mediumint', 'length' => '8', 'null' => false],
    'brand_id'                    => ['type' => 'tinyint', 'length' => '2', 'null' => false],
    'status'                      => ['type' => 'varchar', 'length' => '30', 'null' => false],
    'senddate'                    => ['type' => 'datetime', 'length' => '', 'null' => true],
    'receiveddate'                => ['type' => 'datetime', 'length' => '', 'null' => true],
    'emailtext'                   => ['type' => 'text', 'length' => '', 'null' => true],
  ];

  protected static $primary_key = ['id'];
  protected $auto_increment = 'id';

  public $id, $brand_id, $status, $senddate, $receiveddate, $emailtext;

  function __construct($obj_arr = [], &$error_codes = []) {
    parent::__construct();
    $this->setDefaults();
    if (!empty($obj_arr)) {
      $this->from_array($obj_arr);
      $this->valid_required_fields($error_codes);
    }
  }

  public function valid(&$error_codes = []) {
    $errors = [];
    foreach(self::columns as $column) {
      if ($column != $this->auto_increment)
      {
        call_user_func_array([$this, 'v_' . $column], [&$errors]);
      }
    }
    if(empty($errors)) {
      return true;
    }
    $error_codes = array_merge($error_codes, $errors);
    return false;
  }

  public function valid_required_fields(&$error_codes = []) {
    $errors = [];
    foreach (self::columns as $column) {
      $function_name = "v_$column";
      if ($column!=$this->auto_increment) {
        call_user_func_array([$this, $function_name], [&$errors]);
      }
    }
    if (empty($errors)) {
      return true;
    }
    $error_codes = array_merge($error_codes, $errors);
    return false;
  }

  public function setDefaults() {
    $this->status = 'new';
  }



  public function v_id(&$error_codes = []) {
    if (!is_null($this->id) && strlen($this->id) > 0 && self::valid_mediumint($this->id, '8')) {
      return true;
    }
    $error_codes[] = 'id';
    return false;
  }

  public function v_brand_id(&$error_codes = []) {
    if (!is_null($this->brand_id) && strlen($this->brand_id) > 0 && self::valid_tinyint($this->brand_id, '2')) {
      return true;
    }
    $error_codes[] = 'brand_id';
    return false;
  }

  public function v_status(&$error_codes = []) {
    if (!is_null($this->status) && strlen($this->status) > 0 && self::valid_varchar($this->status, '30')) {
      return true;
    }
    $error_codes[] = 'status';
    return false;
  }

  public function v_senddate(&$error_codes = []) {
    if (is_null($this->senddate) || strlen($this->senddate) == 0 || self::valid_datetime($this->senddate)) {
      return true;
    }
    $error_codes[] = 'senddate';
    return false;
  }

  public function v_receiveddate(&$error_codes = []) {
    if (is_null($this->receiveddate) || strlen($this->receiveddate) == 0 || self::valid_datetime($this->receiveddate)) {
      return true;
    }
    $error_codes[] = 'receiveddate';
    return false;
  }

  public function v_emailtext(&$error_codes = []) {
    if (is_null($this->emailtext) || strlen($this->emailtext) == 0 || self::valid_text($this->emailtext)) {
      return true;
    }
    $error_codes[] = 'emailtext';
    return false;
  }

  /**
  *@param array $conditions (required)
  *@param String $raw_sql (optional)
  *@return StoneOrder[]
  **/
  public static function find_all_like($conditions, $raw_sql = '') { return parent::generic_find_all_like($conditions, static::OM_CLASS_NAME, $raw_sql); }
  /**
  *@param array $conditions (required)
  *@param String $raw_sql (optional)
  *@return StoneOrder[]
  **/
  public static function find_all_by($conditions, $raw_sql = '') { return parent::generic_find_all_by($conditions, static::OM_CLASS_NAME, $raw_sql); }
  /**
  *@param String $raw_sql (optional)
  *@return StoneOrder[]
  **/
  public static function find_all($raw_sql = '') { return parent::generic_find_all(static::OM_CLASS_NAME, $raw_sql); }
  /**
  *@param array $conditions (required)
  *@param String $raw_sql (optional)
  *@return StoneOrder
  **/
  public static function find_by($conditions, $raw_sql = '') { return parent::generic_find_by($conditions, static::OM_CLASS_NAME, $raw_sql); }
  /**
  *@param String $id (required)
  *@param String $raw_sql (optional)
  *@return StoneOrder
  **/
  public static function find_by_id($id, $raw_sql = '') { return parent::generic_find_by_id($id, static::OM_CLASS_NAME, $raw_sql); }
  /**
  *@param array $conditions (required)
  *@param String $raw_sql (optional)
  *@return int
  **/
  public static function count_all_by($conditions, $raw_sql = '') { return parent::generic_count_all_by($conditions, static::OM_CLASS_NAME, $raw_sql); }
  /**
  *@param array $conditions (required)
  *@param String $raw_sql (optional)
  *@return bool
  **/
  public static function delete_by($conditions, $raw_sql = '') { return parent::generic_delete_by($conditions, static::OM_CLASS_NAME, $raw_sql); }
}