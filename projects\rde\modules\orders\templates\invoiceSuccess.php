<?php TemplateHelper::includePartial('_tabs.php', 'orders', compact(["quotation", "quotation_extra", "company", "sandboxuser"])); ?>
<br/>

<form class="edit-form" method="post" enctype="multipart/form-data">
  <div>
    <table class="default_table">
      <tr class="dataTableHeadingRow">
        <td>Eigenschap</td>
        <td>Waarde</td>
      </tr>
      <tr class="dataTableRow">
        <td>Gem. betaal termijn</td>
        <td><?php echo $quotation->averagePaymentTerm ?></td>
      </tr>
      <tr class="dataTableRow">
        <td>Uitvoerdersbon</td>
        <td><input type="checkbox" name="executorTicket" value="1" <?php if ($quotation_extra->executorTicket==1) echo 'checked' ?> ></td>
      </tr>
      <tr class="dataTableRow">
        <td>Notities algemeen</td>
        <td><?php echo $invoiceParty?->invoicePartyNotes; ?></td>
      </tr>
      <tr class="dataTableRow">
        <td>Factuurmelding</td>
        <td><input type="checkbox" name="quoteInvoiceAlertFlag" value="1" <?php if ($quotation_extra->quoteInvoiceAlertFlag==1) echo 'checked' ?> ></td>
      </tr>
      <tr class="dataTableRow">
        <td>Factuurmelding info</td>
        <td><textarea name="quoteInvoiceAlertInfo"><?php echo $quotation_extra->quoteInvoiceAlertInfo ?></textarea></td>
      </tr>
      <?php if ($cargoReceipt): ?>
        <tr class="dataTableRow">
          <td>Speciale Overeenkomsten gelezen</td>
          <td><input type="checkbox" name="specialAgreementsRead" value="1" <?php if ($cargoReceipt->specialAgreementsRead == 1) echo 'checked' ?> ></td>
        </tr>
        <tr class="dataTableRow">
          <td>Speciale Overeenkomsten</td>
          <td><?php echo $cargoReceipt->specialAgreements ?></td>
        </tr>
      <?php else: ?>
        <tr class="dataTableRow">
          <td>Vrachtbon</td>
          <td>Er is geen vrachtbon gekoppeld aan deze bestelling</td>
        </tr>
      <?php endif; ?>
      <?php if (count($files)): ?>
        <tr class="dataTableRow">
          <td>
            Bijlage
          </td>
          <td class="appendix">
            <?php foreach ($files as $file): ?>
              <div>
                <a target="_blank" href="<?php echo reconstructQueryAdd() . "&action=openAppendix&file_id=$file->fileId" ?>"><?php echo $file->filename ?></a>
                <?php echo BtnHelper::getRemove(reconstructQueryAdd(['pageId']) . "action=deleteAppendix&file_id=$file->fileId", __('Verwijder bijlage')) ?>
              </div>
            <?php endforeach; ?>
          </td>
        </tr>
      <?php endif; ?>
      <tr class="dataTableRow">
        <td>Bijlage uploaden</td>
        <td class="upload-appendix"><?php echo $uploader->getInputs(); ?></td>
      </tr>
    </table>
  </div>
  <input type="submit" name="go" value="Opslaan" class="gsd-btn gsd-btn-primary"/>
  <input type="submit" name="go_list" value="Opslaan en naar lijst" class="gsd-btn gsd-btn-secondary"/>
</form>

<br/>

<h2>Facturen</h2>

<?php $dataTable2->render(); ?>

<script type="text/javascript">

  $(document).ready(function () {
    <?php echo $dataTable2->renderJSconfig() ?>
  });

</script>

