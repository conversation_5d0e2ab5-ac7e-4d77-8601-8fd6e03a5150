<h1>Factureren</h1>

<table class="default_table default_table_center quotation_table visible">
  <tr class="dataTableHeadingRow">
    <td>Naam project</td>
    <td>Offerte nummer</td>
    <td>Merk</td>
    <td>Model</td>
    <td>Meters</td>
    <td>Opmerkingen</td>
    <td>PDF</td>
  </tr>
  <?php
    /** @var Quotations $quotation */
    foreach ($quotations as $quotation): ?>
    <tr class="dataTableRow trhover">
      <td><?php echo $quotation->projectName ?></td>
      <td><?php echo $quotation->quotationNumber ?></td>
      <td><?php echo $quotation->brands[$quotation->brandId]->name ?></td>
      <td><?php echo $quotation->size->name ?></td>
      <td><?php echo $quotation->meters ?></td>
      <td><?php echo $quotation->productionNotes ?></td>
      <td><?php echo BtnHelper::getPrintPDF('?action=downloadpdf&id=' . $quotation->quotationId, __('Bekijk pdf'), '_blank') ?></td>
    </tr>
  <?php endforeach; ?>
</table>
<br>
<div style="display: flex;">
  <h4 style="margin-right: 10px;">Bekijk en download hier de volledige pdf (deze PDF graag meesturen met de factuur):</h4>
  <div style="display: flex; align-items: center;"><?php echo BtnHelper::getPrintPDF('?action=downloadbretipdf&quotation_ids=' . $quotation_ids . '&nr=' . $breti_invoice_number, __('Bekijk pdf'), '_blank') ?></div>
</div>
<br>
<form method="post">
  <input type="submit" class="gsd-btn gsd-btn-primary" name="create_invoice" value="<?php echo __('Factureren') ?>">
</form>
