<?php

  class mapRdeActions extends gsdActions {

    public function executeMap() {
      $this->template_wrapper_clear = true;
    }

    public function executeGetQuotationsAndCompanies() {
      $status = $_GET['status'] ?? [Status::STATUS_ORDER, Status::STATUS_CHECKED, Status::STATUS_PREPARED, Status::STATUS_IN_PRODUCTION, Status::STATUS_PRODUCED, Status::STATUS_PACKED, Status::STATUS_LOADED, Status::STATUS_DELIVERED];

      $statusFilter = DbHelper::getSqlIn('statusId', $status);
      $dueDateFilter = 'dueDate > DATE_ADD(NOW(), INTERVAL -1 WEEK)';

      $quotations = Quotations::find_all("WHERE $statusFilter AND $dueDateFilter");

      $grouped = [];
      foreach ($quotations as $quotation) {
        $grouped[$quotation->companyId][] = $quotation;
      }
      ResponseHelper::exitAsJson($grouped);
    }

    public function executeGetCompaniesWithAddress() {
      $input = RequestHelper::getInputFileContents();

      $companies = array_map(function($companyId) {
        $company = CrmCompanies::find_by(['companyId' => $companyId]);
        if ($company) {
          $company->address = CrmAddresses::find_by(['addressId' => $company->visitAddressId]);
        }
        return $company;
      }, $input->companyIds ?? []);

      ResponseHelper::exitAsJson($companies);
    }

    public function executeGetQuotationsByCompany() {
      $companyId = $_GET['companyId'] ?? null;
      $quotations = $companyId ?
        Quotations::find_all_by(['companyId' => $companyId], 'ORDER BY quotationDate DESC LIMIT 5 ') : [];

      ResponseHelper::exitAsJson($quotations);
    }
    public function executeGetStatuses() {
      ResponseHelper::exitAsJson([
        Status::STATUS_ORDER => "Opdracht",
        Status::STATUS_CHECKED => "Voorbereid",
        Status::STATUS_PREPARED => "Voorbereid print",
        Status::STATUS_IN_PRODUCTION => "In productie",
        Status::STATUS_PRODUCED => "Geproduceerd",
        Status::STATUS_PACKED => "Verzendklaar",
        Status::STATUS_LOADED => "Geladen",
        Status::STATUS_DELIVERED => "Geleverd",
      ]);
    }
    public function executeGetGoogleMapsApiKey() {
      ResponseHelper::exitAsJson(LocationHelper::getGoogleMapsKey());
    }
  }