<?php
class BaseSparepartsPrices extends AppModel
{
  const DB_NAME = 'rde_b1mo';
  const TABLE_NAME = 'spareparts_prices';
  const OM_CLASS_NAME = 'SparepartsPrices';
  const columns = ['id', 'partId', 'scaleType', 'amountFrom', 'amountTo', 'validFrom', 'validTo', 'value'];
  const field_structure = [
    'id'                          => ['type' => 'int', 'length' => '3', 'null' => false],
    'partId'                      => ['type' => 'int', 'length' => '2', 'null' => false],
    'scaleType'                   => ['type' => 'int', 'length' => '1', 'null' => false],
    'amountFrom'                  => ['type' => 'int', 'length' => '10', 'null' => true],
    'amountTo'                    => ['type' => 'int', 'length' => '10', 'null' => true],
    'validFrom'                   => ['type' => 'date', 'length' => '', 'null' => false],
    'validTo'                     => ['type' => 'date', 'length' => '', 'null' => false],
    'value'                       => ['type' => 'decimal', 'length' => '5,2', 'null' => false],
  ];

  protected static $primary_key = ['id'];
  protected $auto_increment = 'id';

  public $id, $partId, $scaleType, $amountFrom, $amountTo, $validFrom, $validTo, $value;

  function __construct($obj_arr = [], &$error_codes = []) {
    parent::__construct();
    $this->setDefaults();
    if (!empty($obj_arr)) {
      $this->from_array($obj_arr);
      $this->valid_required_fields($error_codes);
    }
  }

  /**
  * Set default object values
  **/
  public function setDefaults() {
    $this->scaleType = 0;
    $this->validTo = '9999-12-31';
  }

  /**
   * @param array $conditions (required)
   * @param string $raw_sql (optional)
   * @return SparepartsPrices[]
   **/
  public static function find_all_like($conditions, $raw_sql = '') {
    return parent::generic_find_all_like($conditions, static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param array $conditions (required)
   * @param string $raw_sql (optional)
   * @return SparepartsPrices[]
   **/
  public static function find_all_by($conditions, $raw_sql = '') {
    return parent::generic_find_all_by($conditions, static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param string $raw_sql (optional)
   * @return SparepartsPrices[]
   **/
  public static function find_all($raw_sql = '') {
    return parent::generic_find_all(static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param array $conditions (required)
   * @param string $raw_sql (optional)
   * @return SparepartsPrices
   **/
  public static function find_by($conditions, $raw_sql = '') {
    return parent::generic_find_by($conditions, static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param string $id (required)
   * @param string $raw_sql (optional)
   * @return SparepartsPrices
   **/
  public static function find_by_id($id, $raw_sql = '') {
    return parent::generic_find_by_id($id, static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param array $conditions (required)
   * @param string $raw_sql (optional)
   * @return int
   **/
  public static function count_all_by($conditions, $raw_sql = '') {
    return parent::generic_count_all_by($conditions, static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param array $conditions (required)
   * @param string $raw_sql (optional)
   * @return bool
   **/
  public static function delete_by($conditions, $raw_sql = '') {
    return parent::generic_delete_by($conditions, static::OM_CLASS_NAME, $raw_sql);
  }

}