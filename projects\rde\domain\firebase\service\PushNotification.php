<?php

  namespace domain\firebase\service;

  use Context;
  use CrmCompanies;
  use CrmPersons;
  use Google\Auth\Credentials\ServiceAccountCredentials;
  use Google\Auth\HttpHandler\HttpHandlerFactory;
  use PageMap;
  use SandboxUsers;
  use StringHelper;
  use UserProfile;

  class PushNotification {

    private $token;
    private $projectId;

    public const RINGING = 'ringing'; // gaat over
    public const ANSWERED = 'answered'; // opgenomen
    public const ONHANGUP = 'onhangup'; // ophangen door beller of klant
    public const INPROGRESS = 'in-progress'; // opgenomen
    public const ENDED = 'ended'; // beeindigd
    public const CANCELLED = 'cancelled'; // geannuleerd

    public const STATUSES = [
      self::RINGING => 'Inkomend gesprek',
      self::ANSWERED, self::INPROGRESS, self::CANCELLED =>"Beëindigd gesprek",
      self::ONHANGUP, self::ENDED => "Beantwoord gesprek",
    ];

    public function __construct($projectId) {
      $credentials = new ServiceAccountCredentials(
        "https://www.googleapis.com/auth/firebase.messaging",
        \Config::get("FIREBASE_PV_KEY"),
      );

      $this->token = $credentials->fetchAuthToken(HttpHandlerFactory::build())['access_token'];
      $this->projectId = $projectId;
    }

    public static function getTokenByUserId($userId) {
      return UserProfile::find_by(['user_id' => $userId, 'code' => 'FIREBASE-token']);
    }

    /**
     * @return UserProfile[]
     */
    public static function getAllTokensWithUsernames(): array {
      $tokens = UserProfile::find_all_by(['code' => 'FIREBASE-token']);
      foreach ($tokens as $token) {
        $user = \User::find_by_id($token->user_id);
        $token->userName = $user ? $user->getNaam() : 'Onbekend';
      }

      return $tokens;
    }

    /**
     * @param $token
     * @param $userId
     * @return void
     * @throws \GsdException
     * @throws \GsdDbException
     */
    public static function saveToken($token, $userId): void {
      $userProfile = UserProfile::find_by(['user_id' => $userId, 'code' => 'FIREBASE-token']);
      if (!$userProfile) {
        $userProfile = new UserProfile();
        $userProfile->user_id = $userId;
        $userProfile->type = UserProfile::TYPE_OTHER;
        $userProfile->code = "FIREBASE-token";
      }

      // overwrite value everytime for now.
      $userProfile->value = $token;
      $userProfile->save();
    }

    /**
     * @param $tokenId
     * @return bool
     * @throws \GsdDbException
     * @throws \GsdException
     */
    public static function deleteToken($tokenId): bool {
      $token = UserProfile::find_by_id($tokenId);
      if (!$token) return false;

      return $token->destroy();
    }

    /**
     * @param $deviceToken
     * @param $notificationTitle
     * @param $notificationText
     * @param $link
     * @return void
     */
    public function sendPushNotification($deviceToken, $notificationTitle, $notificationText, $link): void {
      $ch = curl_init("https://fcm.googleapis.com/v1/projects/" . $this->projectId . "/messages:send");

      curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/json',
        'Authorization: Bearer ' . $this->token,
      ]);

      curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode([
        "message" => [
          "name"         => "push-notification",
          "token"        => $deviceToken,
          "notification" => [
            "title" => $notificationTitle,
            "body"  => $notificationText,
          ],
          "webpush"      => [
            "headers" => [
              "TTL" => "600",
            ],
            "fcm_options" => [
              "link" => $link,
            ],
          ],
        ],
      ]));

      curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "post");
      curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

      $response = curl_exec($ch);
//      dump(json_decode($response)->name);

      curl_close($ch);
    }

    public static function getMessage($callerID, $extension = ''): array {

      $body = "Nummer: " . $callerID . "\n";
      if ($extension != "") {
        $body .= "Extensie: " . $extension . "\n";
      }

      // Replace +31 with 0 if it appears at the beginning
      $callerID = preg_replace('/^\+31/', '0', $callerID);
      // Sanitize the callerID
      $searchValue = escapeForDB(StringHelper::cleanPhone($callerID));
      $searchQuery = $searchValue ? "WHERE phone LIKE '%$searchValue%' OR fax LIKE '%$searchValue%' LIMIT 20" : '';

      $calledAt = date('Y-m-d\TH:i:s');

      // Get the backend site, we have to explicitly set the url because the voip callback url is not the same as the site url
      $url = "https://beheer.raamdorpel.nl/nl/inkomend-gesprek?phone=$searchValue&calledAt=$calledAt";

      // a caller could be a registered person, a company or a sandbox user
      $companies = CrmCompanies::find_all_by([], $searchQuery);
      $persons = CrmPersons::find_all_by([], $searchQuery);
      $susers = SandboxUsers::find_all_by([], $searchQuery);

      $potentialCallers = array_merge($companies, $persons, $susers);

      if (count($potentialCallers)) {
        $caller = $potentialCallers[0];

        // get name, which could either be getNaam() or caller->name
        $name = method_exists($caller, 'getNaam') ? $caller->getNaam() : $caller->name;
        $body .= 'Naam: ' . $name . "\n";
        if (isset($caller->company) && $caller->company->name != "") {
          $body .= ' - ' . $caller->company->name;
        }

        if (count($potentialCallers) > 1) {
          $body .= "\nOf " . (count($potentialCallers) - 1) . ' andere personen/bedrijven met dit nummer.';
        }
      }

      return [
        'url'     => $url,
        'message' => $body,
      ];
    }

    public static function sendTestPushNotification($userId) {
      $pushNotification = new self("raamdorpel-push-notificaties");

      $deviceToken = UserProfile::find_by(['user_id' => $userId, 'code' => "FIREBASE-token"]);
      if (!$deviceToken) return false;

      $message = self::getMessage("0497-36 07 91"); // raamdorpel phone

      $pushNotification->sendPushNotification(
        $deviceToken->value,
        "Test notificatie",
        $message['message'],
        $message['url'],
      );

      return true;
    }

  }