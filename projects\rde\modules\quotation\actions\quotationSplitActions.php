<?php

  use Gsd\Form\Elements\Element;
  use Gsd\Form\Elements\Select;
  use Gsd\Form\Elements\Textarea;
  use Gsd\Form\Form;

  trait quotationSplitActions {

    private function maysplit() {
      $this->checkAuthentication();
      $quotation = Quotations::getById($_GET["id"]);
      if (!$quotation) {
        MessageFlashCoordinator::addMessageAlert(__("Bestelling niet geopend. U heeft geen rechten om deze bestelling te bewerken."));
        ResponseHelper::redirect(reconstructQueryAdd());
      }
      if (!$quotation->maySplit()) {
        MessageFlashCoordinator::addMessageAlert(__("Bestelling niet geopend. U kun alleen bestellingen splitsen die status Bestelling hebben."));
        ResponseHelper::redirect(reconstructQueryAdd());
      }
      $this->quotation = $quotation;
    }

    public function executeSplit1() {

      $this->maysplit();

      if (isset($_GET["splitreset"])) {
        unset($_SESSION["split"]);
      }

      if (!isset($_SESSION["split"]["size"])) {
        $_SESSION["split"]["size"] = 2;
      }

      if (isset($_POST["next"])) {
        if ($_SESSION["split"]["size"] != $_POST["size"]) {
          //aantal gewijzigd unset split
          $_SESSION["split"]["size"] = $_POST["size"];
          unset($_SESSION["split"]["form"]);
        }
        ResponseHelper::redirect(reconstructQueryAdd(["id"]) . "action=split2");
      }

      $elements = OrderElements::find_all_by(["quotationId" => $this->quotation->quotationId]);
      $product_count = 0;
      foreach ($elements as $element) {
        $product_count += $element->amount;
      }
      $this->maxsplitcount = $product_count > 9 ? 9 : $product_count;
    }

    public function executeSplit2() {
      $this->maysplit();

      /** @var Quotations $quotation */
      $quotation = $this->quotation;
      $elements = OrderElements::find_all_by(["quotationId" => $quotation->quotationId]);
      $letters = preg_split('#(?<=.)(?=.)#s', "ABCDEFGHIJKLMNOPQRSTUVWXYZ");
      $quotation_part = $quotation->getQuotationPartAlpha();
      $lettersToSplit = [];
      $lettersToUse = false;
      if ($quotation_part == "") {
        $lettersToSplit = $letters;
      }
      else {
        foreach ($letters as $letter) {
          if ($lettersToUse) {
            $lettersToSplit[] = $letter;
          }
          elseif ($letter == $quotation_part) {
            $lettersToSplit[] = $letter;
            $lettersToUse = true;
          }
        }
      }

      [$dueDate, $dueDateWeek, $weeks, $dueweek] = $this->determineDeliveryWeeks();

      $parts = $_SESSION["split"]["size"];
      if (isset($_SESSION["split"]["form"])) {
        $form = $_SESSION["split"]["form"];
      }
      else {
        $form = new Form();
        for ($tel = 0; $tel < $parts; $tel++) {
          foreach ($elements as $element) {
            $key = "amount_" . $tel . "_" . $element->elementId;
            $value = 0;
            if ($tel == 0) {
              $value = $element->amount;
            }
            $input = new Element("", $key, $value);
            $input->addClass("form-input");
            $input->setStyle("width: 50px;text-align: center;");
            $input->setReadonly(true);
            $input->setForceSetOnPost(true);
            $form->addElement($input);
          }

          $key = "remark_" . $tel;
          $textarea = new Textarea("Opmerking", $key, $quotation->customerNotes);
          $textarea->addClass("form-input");
          $form->addElement($textarea);

          $weekselect = new Select("Leverweek", 'week_' . $tel, $dueweek);
          $weekselect->setStyle("width: 230px;");
          $weekselect->addClass("weekselect");
          foreach ($weeks as $nr => $name) {
            $option = new \Gsd\Form\Elements\Option($nr, $name['name']);
            if ($name['closed']) {
              $option->addAtribute("disabled", "disabled");
            }
            $weekselect->addOption($option);
          }
          $form->addElement($weekselect);
        }
      }

      if (isset($_POST["next"]) || isset($_POST["prev"])) {
        $form->setElementsValue($_POST);

        $elementcount_per_quotation = [];
        foreach ($form->getElements() as $element) {
          if (strpos($element->getName(), "amount_") !== false) {
            $vals = explode("_", $element->getName());
            if (!isset($elementcount_per_quotation[$vals[1]])) {
              $elementcount_per_quotation[$vals[1]] = 0;
            }
            $elementcount_per_quotation[$vals[1]] += intval($element->getValue());
          }
        }

        foreach ($elementcount_per_quotation as $nr => $sum) {
          if ($sum == 0) {
            $form->addError("U heeft één of meerdere deelleveringen, met geen enkel product.<br/>Wijzig het aantal deelleveringen in de vorige stap.");
            break;
          }
        }

        if (isset($_POST["prev"])) {
          ResponseHelper::redirect(reconstructQueryAdd(["id"]) . "action=split1");
        }

        if ($form->isValid()) {
          $_SESSION["split"]["form"] = $form;
          $_SESSION["split"]["letters"] = $lettersToSplit;
          ResponseHelper::redirect(reconstructQueryAdd(["id"]) . "action=split3");
        }
      }

      $this->parts = $parts;
      $this->form = $form;
      $this->elements = $elements;
      $this->lettersToSplit = $lettersToSplit;
      $this->dueweek = $dueweek;
    }

    public function executeSplit3() {
      $this->maysplit();

      if (isset($_POST["prev"])) {
        ResponseHelper::redirect(reconstructQueryAdd(["id"]) . "action=split2");
      }
      /** @var Quotations $quotation */
      $quotation = $this->quotation;
      $quotation_extra = QuotationsExtra::find_by(["quotationId" => $quotation->quotationId]);
      $elements = AppModel::mapObjectIds(OrderElements::find_all_by(["quotationId" => $quotation->quotationId]), "elementId");

      $parts = $_SESSION["split"]["size"];
      $form = $_SESSION["split"]["form"];
      $lettersToSplit = $_SESSION["split"]["letters"];

      $quotation_element_amounts = [];
      foreach ($form->getElements() as $element) {
        if (strpos($element->getName(), "amount_") !== false && $element->getValue() != 0) {
          $vals = explode("_", $element->getName());
          $quotation_element_amounts[$vals[1]][$vals[2]] = $element->getValue();
        }
      }

      if (isset($_POST["next"])) {

        [$dueDate, $dueDateWeek, $weeks, $dueweek] = $this->determineDeliveryWeeks();

        for ($tel = 0; $tel < $parts; $tel++) {

          $new_q = new Quotations($quotation->asArray());
          $new_q->from_db = false;
          $new_q->quotationId = null;
          $new_q->invoiceId = null;
          $new_q->invoiceNumber = null;
          $new_q->invoiceDate = null;
          $new_q->produceDate = null;
          $new_q->packingDate = null;
          $new_q->mollie_id = null;
          $new_q->cashPayment = 0;
          $new_q->cashPaymentPrice = 0;
          $new_q->noRackQuotations = 0;
          $new_q->noContainerQuotations = 0;
          $new_q->noRackNoContainerQuotations = 0;
          $new_q->palletQuotations = 0;
          $new_q->payedFlag = 0;
          $new_q->proformaFlag = 0;

          $new_q->statusId = Status::STATUS_ORDER;
          $new_q->quotationDate = date("Y-m-d");
          $new_q->customerNotes = $form->getElement("remark_" . $tel)->getValue();
          $new_q->quotationPart = RdeHelper::AlphaToAsciiInt($lettersToSplit[$tel]);

          //leverdatum/leverweek
          $new_q->dueDate = $dueDate;
          $new_q->dueDateWeek = $dueDateWeek;
          $weeknr = intval($form->getElement("week_" . $tel)->getValue());
          if ($weeknr < $dueweek) { //spoedlevering, niet leverweek aanpassen
            $weeknrstr = substr($weeknr, 4) . ' - ' . substr($weeknr, 0, 4);
            $new_q->customerNotes .= "\nLet op: er is gekozen voor een levering in week " . $weeknrstr . ".\nNa onze controle, kunt u in uw account bij offertes de definitieve leverweek inzien.\n" . $quotation->customerNotes;

            $minweeks = $dueweek - $weeknr;
            $year = substr($weeknr, 0, 4);
            if ($year != $new_q->getDueDate("Y")) {
              $minweeks -= 100 - DateTimeHelper::getTotalWeeksInYear($year); //in het gezet weeknummer jaar. 52/53 weken in een jaar, deze extra verwijderen.
            }
            $newtime = strtotime('-' . $minweeks . ' WEEKS', intval($new_q->getDueDate("U")));
            $new_q->dueDate = date('Y-m-d', $newtime);
            $new_q->dueDateWeek = date('W', $newtime);

          }
          elseif ($weeknr > $dueweek) { //levering later als standaard, dan leverweek aanpassen aan geselecteerde week
            $extraweeks = $weeknr - $dueweek;
            $year = substr($weeknr, 0, 4);
            if ($year != $new_q->getDueDate("Y")) {
              $extraweeks -= 100 - DateTimeHelper::getTotalWeeksInYear($new_q->getDueDate("Y")); //in het vorige jaar. 52/53 weken in een jaar, deze extra verwijderen.
            }
            $newtime = strtotime('+' . $extraweeks . ' WEEKS', intval($new_q->getDueDate("U")));
            $new_q->dueDate = date('Y-m-d', $newtime);
            $new_q->dueDateWeek = date('W', $newtime);
          }

          /** @var OrderElements $element */
          $new_elements = [];
          foreach ($elements as $element) {
            if (!isset($quotation_element_amounts[$tel][$element->elementId])) continue;
            $new_e = new OrderElements($element->asArray());
            $new_e->from = false;
            $new_e->elementId = null;
            $new_e->amount = $quotation_element_amounts[$tel][$element->elementId];
            $new_e->totalPrice = $new_e->amount * $new_e->elementPrice;

            //order_element_parts
            $oep = OrderElementparts::find_by(["elementId" => $element->elementId]);
            if ($oep) {
              $new_oep = new OrderElementparts($oep->asArray());
              $new_oep->from_db = false;
              $new_oep->orderElementPartId = null;
              $new_oep->elementId = null;
              $new_oep->quotationId = null;
              $new_e->order_element_part = $new_oep;
            }
            $new_elements[] = $new_e;
          }

          $elementTotals = OrderElements::calculateTotals($new_elements);

          $new_q->projectValue = $elementTotals["projectValue"];
          $new_q->meters = round(($elementTotals["fullLength"] / 1000), 2);
          $new_q->metersMuch = $quotation->meters >= 90 ? 'true' : 'false';
          $new_q->weight = $elementTotals["weight"];
          $new_q->stoneAmount = $elementTotals["stoneAmount"];


          $production_order = ProductionOrder::find_by(["quotationId" => $quotation->quotationId]);
          $new_production_order = false;
          if ($production_order) {
            $new_production_order = new ProductionOrder($production_order->asArray());
            $new_production_order->from_db = false;
            $new_production_order->prodOrderId = null;
            $new_production_order->quotationId = null;
          }

          $custom_stone = QuotationsCustomStone::find_by(["quotationId" => $quotation->quotationId]);
          $new_custom_stone = false;
          if ($custom_stone) {
            $new_custom_stone = new QuotationsCustomStone();
            $new_custom_stone->depth = $custom_stone->depth;
            $new_custom_stone->thickness = $custom_stone->thickness;
            $new_custom_stone->height = $custom_stone->height;
            $new_custom_stone->width_click = $custom_stone->width_click;
            $new_custom_stone->height_click = $custom_stone->height_click;
          }

          //quotations extra
          $new_qe = new QuotationsExtra($quotation_extra->asArray());
          $new_qe->from_db = false;
          $new_qe->id = null;
          $new_qe->prod_cm_per_hour = null;
          $new_qe->prod_employee_id = null;

          $new_qe->totalLeftEndStones = $elementTotals["totalLeftEndStones"];
          $new_qe->totalRightEndStones = $elementTotals["totalRightEndStones"];
          $new_qe->totalLeftEndStonesGrooves = $elementTotals["totalLeftEndStonesGrooves"];
          $new_qe->totalRightEndStonesGrooves = $elementTotals["totalRightEndStonesGrooves"];
          $new_qe->totalMiddlesStones = $elementTotals["totalMiddlesStones"];


          $new_q->save();
          $new_qe->quotationId = $new_q->quotationId;
          $new_qe->save();
          /** @var OrderElements $ne */
          foreach ($new_elements as $ne) {
            $ne->quotationId = $new_q->quotationId;
            $ne->save();
            if (isset($new_e->order_element_part)) { //er is een order_element_part
              $new_e->order_element_part->elementId = $ne->elementId;
              $new_e->order_element_part->quotationId = $ne->quotationId;
              $new_e->order_element_part->save();
            }
          }
          if ($new_production_order) {
            $new_production_order->quotationId = $new_q->quotationId;
            $new_production_order->save();
          }
          if ($new_custom_stone) {
            $new_custom_stone->quotationId = $new_q->quotationId;
            $new_custom_stone->save();
          }

        }

        if ($quotation->quotationPart == "") {
          //eerste keer splitsen van deze bestelling. Bron bestelling gaat naar status 70
          $quotation->statusId = Status::STATUS_SPLIT;
          $quotation->save();
        }
        else {
          //de bron bestelling is een deellevering
          $quotation->destroy();
        }

        unset($_SESSION["split"]);
        MessageFlashCoordinator::addMessage("Offerte " . $quotation->getQuotationNumberFull() . " is succesvol gesplitst in " . $parts . " deelleveringen.");
        ResponseHelper::redirect(reconstructQueryAdd());

      }


      $this->parts = $parts;
      $this->lettersToSplit = $lettersToSplit;
      $this->elements = $elements;
      $this->form = $form;
      $this->quotation_element_amounts = $quotation_element_amounts;
    }

  }