#search {
  display: inline-block;
  vertical-align: middle;
  line-height: 12px;
}

.nav.action {
  vertical-align: middle;
}

#search .form-row {
  margin: 0;
}

#search .btn i {
  margin: 0;
}

#search_result {
}

#search_result .teaser {
  margin-bottom: 10px;
}

#search_result .search_item {
  background-color: #EFEFEF;
  padding: 20px;
  margin-bottom: 10px;
}

#search_result .highlight {
  color: #CE000C;
}

.x-scroller {
  overflow-x: auto;
}

.breadcrumb-bar ol {
  list-style: none;
  padding: 0;
  margin: 0;
}

.breadcrumb-bar ol li {
  display: inline-block;
}

.breadcrumb-bar ol li:after {
  content: '\/';
  padding: 0 3px;
}

.breadcrumb-bar ol li:last-child:after {
  display: none;
}

.contenttxt h1, .headerfade h1 {
  font-size: 24px;
  font-weight: 900;
  margin: 15px 0;
}

.contenttxt h2, #tags_result h2 {
  margin: 15px 0;
}

.contenttxt h3, #tags_result h2 {
  margin: 10px 0;
}

.contenttxt h1:first-child, .contenttxt h2:first-child {
  margin-top: 0;
}

.contenttxt img {
  height: auto !important;
}

.contenttxt ul {
  padding-left: 25px;
}

#tags_result > div {
  margin-bottom: 15px;
  border-top: 1px solid #dddddd;
  padding-top: 5px;
}

.hidden {
  display: none;
}

.bold {
  font-weight: bold;
}

.form-input {
  padding: 0 15px;
}

.inputerror {
  color: #e9322d;
  border: 1px solid #e9322d !important;
}

.error {
  color: Red;
}

.alert {
  padding: 10px;
  margin-bottom: 10px;
  text-shadow: 0 1px 0 rgba(255, 255, 255, 0.5);
  border-radius: 4px;
  border: 1px solid transparent;
}

.alert .fa:first-child {
  padding-right: 5px;
}

tr.alert td {
  color: red;
}

label.error {
  color: #e9322d !important;
  font-weight: bold;
}

.form-input.error {
  border-color: #e9322d !important;
}

.form-row {
  margin: 0 0 0.2em 0;
}


.btn[disabled], #prev, .disabled, .btn-secondary {
  background-color: grey;
}


.alert-dismissable, .alert-dismissible {
  padding-right: 35px;
}

.alert.alert-danger {
  background-color: #F2DEDE;
  border-color: #EED3D7;
  color: #B94A48;
}

.alert.alert-warning {
  color: #8a6d3b;
  background-color: #fcf8e3;
  border-color: #faebcc;
}

.alert.alert-success {
  color: black;
  background-color: #d9edf7;
  border-color: #bce8f1;
}

.alert .alert-link {
  font-weight: 700;
}

.alert-danger .alert-link {
  color: #843534;
}

.alert-warning .alert-link {
  color: #66512c;
}

.alert-success .alert-link {
  color: #245269;
}

div.errorbox ul,
div.warningbox ul,
div.alert ul {
  padding: 0 20px;
  margin: 5px 0 0 0;
}

.inputerror {
  border: 1px solid red !important;
}

.messagered {
  padding: 10px;
  margin: 5px 0;
  text-shadow: 0 1px 0 rgba(255, 255, 255, 0.5);
  background-color: #F2DEDE;
  border: 1px solid #EED3D7;
  border-radius: 4px;
  color: #B94A48;
  font-size: 12px;
}

.message {
  padding: 10px;
  margin: 5px 0;
  background-color: #E6F1F6;
  border: 1px solid #BCE8F1;
  border-radius: 4px;
  color: #29235C;
  font-size: 12px;
}

.header-bar-link {
  color: #ffffff;
  margin-top: 2px;
}

.header-bar-link:hover,
.header-bar-link:focus {
  color: #F4F4F5;
  text-decoration: underline;
}

.header-bar-link img {
  margin-top: -3px;
  margin-right: 2px;
}

.question-mark {
  display: inline-block;
  float: left;
  margin-top: 8px;
  text-align: center;
  color: #888888;
  font-size: 20px;
}

.question-mark-inline {
  display: inline-block;
  text-align: center;
  color: #888888;
  font-size: 20px;
}

.passwordrow {
  display: none;
}

#offertetable .fa {
  font-size: 18px;
}

#startnewquotation {
  float: right;
  font-size: 18px;
  display: inline-block;
  padding-top: 2px;
  padding-right: 10px;
}

#popupbg {
  overflow: auto;
  overflow-y: scroll;
  position: fixed;
  bottom: 0;
  right: 0;
  width: auto;
  height: auto;
  top: 0;
  left: 0;
  z-index: 900;
  background: #0e0e0ebd;
  display: none;
}

::placeholder {
  color: #ccc;
  opacity: 1;
}

#legenda {
  display: none;
}

#legenda_top {
  float: right;
  margin-top: -30px;
}

#legenda_table {
}

#legenda_table td {
  padding: 3px 5px;
}

.label-line-height {
  line-height: 2em !important
}

.quotation_info_row {
  display: none;
  background-color: #f9f9f9;
}

.quotation_info_row td {
  position: relative;
}

.show_info_rb {
  position: absolute;
  right: 0;
  bottom: 0;
  padding: 10px;
}

.delete_rb {
  position: absolute;
  width: 155px;
  right: 0;
  top: 33px;
  padding: 10px;
}

.pdf_rb {
  position: absolute;
  width: 155px;
  right: 0;
  top: 0;
  padding: 10px;
}

.invoicelate td {
  color: #ce000c;
  /*font-weight: bold;*/
}

div.icondot {
  background-color: #ce000c;
  z-index: 10;
  position: absolute;
  color: white;
  text-align: center;
  padding: 0;
  font-weight: bold;
  border-radius: 15px;
  font-size: 12px;
  right: 0;
  top: 3px;
  width: 20px;
  height: 20px;
  line-height: 21px;
}

tr.imessage_unread td {
  font-weight: bold;
}

hr {
  border-top: 1px solid #EBEBEB;
}

#mainnews {
  background-color: #EBEBEB;
  padding: 20px 15px 1px 15px;
  margin-bottom: 25px;
}

ul.pagination {
  margin: 0;
  padding: 0;
}

.pagination > li {
  display: inline-block;
}

.pagination-block {
  padding: 0 0 15px 0;
}

.pagination > li > a, .pagination > li > span {
  position: relative;
  float: left;
  padding: 6px 12px;
  margin-left: -1px;
  line-height: 1.42857143;
  color: #0e0e0ebd;
  text-decoration: none;
  background-color: #fff;
  border: 1px solid #ddd;
}

.pagination > .active > a,
.pagination > .active > span,
.pagination > .active > a:hover,
.pagination > .active > span:hover,
.pagination > .active > a:focus,
.pagination > .active > span:focus {
  background: #ce000c;
  border-color: #ce000c;
  color: white;
}

.pagination > li > a:focus, .pagination > li > a:hover, .pagination > li > span:focus, .pagination > li > span:hover {
  z-index: 3;
  color: #0e0e0ebd;
  background-color: #eee;
  border-color: #ddd;
}

.pagination .prev, .pagination .next {
  width: 43.5px;
  text-align: center;
}

.prev.disabled, .next.disabled {
  color: #ddd;
}

.prev.disabled:hover, .next.disabled:hover {
  background-color: white;
  color: #ddd;
}

#prev_top {
  margin-right: 15px;
  font-size: 25px;
}

#next_top {
  margin-left: 15px;
  font-size: 25px;
}

.trhover:hover {
  background-color: #f9f9f9;
}

.category_show {
  color: #333;
  display: block;
}

aside {
  padding-bottom: 15px;
}

.sidebar .widget_content ul.dropdown-menu {
  padding-left: 10px;
}

.sidebar .widget_content ul li.current a {
  color: #333;
}

.sidebar .widget_content ul li.current > a {
  color: #ce000c;
  font-weight: 500;
}

.submenutoggle {
  display: none;
  background: #ebebeb;
  padding: 12px 15px 10px 15px;
  position: relative;
  color: black;
  font-weight: 500;
  border-bottom: 1px solid white;
}

.submenutoggle .c-hamburger {
  display: inline-block;
  position: absolute;
  right: 10px;
  top: 4px;
  margin: 0;
  float: right;
  color: black;
}

.submenutoggle .c-hamburger span::before, .submenutoggle .c-hamburger span::after, .submenutoggle .c-hamburger span {
  background-color: black;
}

.submenutoggle .c-hamburger--htx.is-active span {
  background: none;
}

.sidebar.submenu-active {
  visibility: visible;
  opacity: 1;
  height: auto;
}

.select:after {
  top: 1px;
  background-color: unset;
}

.select option[disabled] {
  color: #ccc;
}

.slider_usp {
  margin: 0;
  padding: 0;
  padding-top: 10px;
  list-style: none;
  color: white;
  font-weight: 400;
}

.slider_usp li {
  padding: 5px 0;
  font-size: 18px;
}

.service-text {
  min-height: 96px;
  padding-bottom: 30px;
}

.gallery-item .title {
  padding: 5px 0;
}

#usps h2:before {
  content: "\f00c"; /* this is your text. You can also use UTF-8 character codes as I do here */
  font-family: FontAwesome;
  position: relative;
  margin: 0 10px 0 0;
  font-weight: normal;
  color: #CE000C;
}

#widgetBar-1 {
  padding: 0 15px;
}

.gallery-item {
  padding-bottom: 40px;
}

.gallery-item img {
  border: 1px solid #e0e0e0;
}

#gmap {
  height: 360px;
  width: 100%;
  margin-bottom: 15px;
}

#gdirections {
  width: 480px;
}

/* Resposive Youtube */
#youtube .col-md-6 {
  padding-top: 30px;
}

.youtube-container {
  position: relative;
  padding-bottom: 56.25%;
  padding-top: 30px;
  height: 0;
  overflow: hidden;
  width: 100%;
}

.youtube-container iframe,
.youtube-container object,
.youtube-container embed {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

/* Resposive Vimeo */
.vimeo-container {
  position: relative;
  padding-bottom: 56.25%;
  padding-top: 30px;
  height: 0;
  overflow: hidden;
  width: 100%;
}

.vimeo-container iframe,
.vimeo-container object,
.vimeo-container embed {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

/********************* CONTACT MAP *********************/
#gmap {
  position: relative;
  padding-bottom: 56.25%;
  padding-top: 30px;
  height: 0;
  overflow: hidden;
}

#gmap iframe,
#gmap object,
#gmap embed {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

#gdirections {
  width: 100%;
}

.adp-text {
  padding: 15px 30px;
}

.adp-marker {
  margin-left: -30px;
}

.header .contact .icon-sm {
  font-size: 28px;
  display: inline-block;
  min-width: 40px;
}

.header-item {
  display: flex;
  align-items: center;
}

.header .contact .icon-sm i {
  color: #ce000c;
}

.header .icon-content-sm {
  display: inline-block;
}

.header .basket_txt {
  padding-left: 15px;
}

/*.nav.action > ul li.haschild:hover > a {*/
/*  box-shadow: 0 -1px 1px rgba(0, 0, 0, 0.3)*/
/*}*/

.nav.action > ul li {
  height: 58px;
}

.nav.action > ul li a {
  height: 58px;
  line-height: 58px;
}

.nav.action > ul li.haschild > a:after {
  color: #777777;
}

.hcart a, .hcart li {
  height: auto !important;
  padding: 0 15px;
}

.hcart a.btn {
  color: white !important;
  margin-bottom: 5px;
  text-align: center;
}

.nav > ul li .child li a.hchart_product_link {
  line-height: 20px;
  padding: 0;
}

.hcart .table {
  margin-bottom: 10px;
}

.hcart .text-right {
  text-align: right;
}

a.mobile_icon {
  font-size: 23px !important;
  padding: 0 15px 0 0;
}

/* TAG CLOUD*/
#tags {
  margin: 0 1em 0 0;
  padding: 0;
}

#tags li {
  margin: 0;
  padding: 0;
  list-style: none;
}

#tags li a {
  text-decoration: none;
  padding: 0 4px 0 0;
}

#tags li a:hover {
  color: #CE000C;
}

.tag1 {
  font-size: 100%;
}

.tag2 {
  font-size: 100%;
}

.tag3 {
  font-size: 100%;
}

.tag4 {
  font-size: 100%;
}

.tag5 {
  font-size: 100%;
}

.link-structuur {
  padding-top: 20px;
}

.link-structuur ul {
  padding-left: 0;
}

.link-structuur ul li {
  display: inline-block;
  list-style-type: none;
  padding-right: 10px;
}

.klantevertellen-wrapper iframe {
  border: 1px solid #ebebeb;
  border-radius: 5px;
  max-width: 100%;
  width: 185px;
  height: 222px;
  margin-left: 15px;
}

.review_star {
  position: absolute;
  z-index: 2;
  right: 3px;
}

.review_star span.review_star_star {
  font-size: 66px;
  color: #F9B403;
  line-height: 35px;
  /*transform: rotate(-90deg);*/
  display: block;
  text-shadow: 1px 1px 0 rgba(0, 0, 0, .15);
}

.review_star span.review_star_score {
  font-size: 14px;
  position: absolute;
  color: black;
  padding-top: 11px;
  display: inline-block;
  text-align: center;
  width: 100%;
  font-weight: bold;
  top: 0;
}

.homereview_item {
  padding: 0 0 15px 0;
}

.homereview_item p {
  padding-bottom: 0;
}

.homereview_item > div {
  position: relative;
  padding: 15px 45px 15px 15px;
  background: #a6a3a3;
}

.homereview_title {
  padding: 1rem;
  margin: 0;
  font-size: 1.7rem;
  line-height: 1.7rem;
  z-index: 1;
  position: relative;
  font-weight: 700;
  color: white;
}

.homereview_teaser {
  z-index: 1;
  padding: 1rem;
  position: relative;
  color: white;
  font-size: 1.2rem;
}

.scroller-x {
  overflow-x: auto;
}

.header .contact .icon-sm.icon-whatsapp {
  min-width: auto;
}

.icon-whatsapp img {
  width: 28px;
  margin-top: 13px;
}

.mobile_icon.icon-whatsapp img {
  width: 25px;
  margin: 4px 0 0 5px;
}

.rdecontact {
  display: flex;
  justify-content: space-between;
}


#message_development {
  background-color: #f44336;
  text-align: center;
  color: white;
  font-weight: bold;
  padding: 5px;
}

#message_development {
  background-color: #f44336;
  text-align: center;
  color: white;
  font-weight: bold;
  padding: 5px;
  display: block;
}