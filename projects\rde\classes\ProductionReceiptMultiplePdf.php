<?php

class ProductionReceiptMultiplePdf extends GSDPDF {

  public function __construct($quotation_ids_arr, $breti_invoice_number = false, $download = false) {

    parent::__construct();

    $this->quotation_ids_arr = $quotation_ids_arr;
    $this->breti_invoice_number = $breti_invoice_number;
    $this->download = $download;
    $this->isAdmin = false;
  }

  private function includePdfs($quotation_ids_arr) {
    foreach ($quotation_ids_arr as $quotation_id) {
      // Determine which PDF type(s) to include based on product types
      $hasWebshopProducts = Projects::find_by(['quotationId' => $quotation_id, 'showOnProductionPage' => '1']);
      $hasStoneElementProducts = OrderElements::find_by(['quotationId' => $quotation_id]);

      if ($hasStoneElementProducts) {
        // Include stone element products PDF
        $this->includeProductionPdf($quotation_id);
      }

      if ($hasWebshopProducts) {
        // Include webshop products PDF
        $this->includeWebshopPdf($quotation_id);
      }

      if (!$hasStoneElementProducts && !$hasWebshopProducts) {
        // Include an empty PDF if no products found
        $this->includeEmptyPdf($quotation_id);
      }
    }
  }

  private function includeProductionPdf($quotation_id) {
    $productionPdf = new ProductionReceiptPdf($quotation_id);

    // When the user is not an admin, and it's a Breti invoice, the supplier should be external
    $isExternalSupplier = !$this->isAdmin && $this->breti_invoice_number;
    $productionPdf->setSupplierExternal($isExternalSupplier);

    $productionPdf->setMultiple(true);
    $filename = $productionPdf->generate();

    $filepath = DIR_TEMP . $filename;

    // Include all pages from this PDF
    $this->importPdfPages($filepath);

    // Clean up temporary file
    if (file_exists($filepath)) {
      unlink($filepath);
    }
  }

  private function includeWebshopPdf($quotation_id) {
    $webshopPdf = new ProductionReceiptWebshopPdf($quotation_id);

    // Set the same properties as for production PDF if needed
    if ($this->breti_invoice_number) {
      $isAdmin = $_SESSION['userObject']->usergroup == User::USERGROUP_SUPERADMIN || $_SESSION['userObject']->usergroup == User::USERGROUP_ADMIN;
      $isExternalSupplier = !$isAdmin && $this->breti_invoice_number;
      if (method_exists($webshopPdf, 'setSupplierExternal')) {
        $webshopPdf->setSupplierExternal($isExternalSupplier);
      }
    }

    $webshopPdf->setMultiple(true);
    $filename = $webshopPdf->generate();

    $filepath = DIR_TEMP . $filename;

    // Include all pages from this PDF
    $this->importPdfPages($filepath);

    // Clean up temporary file
    if (file_exists($filepath)) {
      unlink($filepath);
    }
  }

  private function includeEmptyPdf($quotation_id) {
    $emptyPdf = new GSDPDF();
    $emptyPdf->AddPage();
    $emptyPdf->SetFont('Arial', 'B', 16);
    $emptyPdf->Cell(0, 10, 'Geen producten gevonden voor deze offerte', 0, 1, 'C');
    $emptyPdf->SetFont('Arial', '', 12);
    $emptyPdf->Cell(0, 10, 'Offerte ID: ' . $quotation_id, 0, 1, 'C');

    $filename = 'empty_production_receipt_' . $quotation_id . '.pdf';
    $emptyPdf->Output('F', DIR_TEMP . $filename);

    $filepath = DIR_TEMP . $filename;

    // Include all pages from this PDF
    $this->importPdfPages($filepath);

    // Clean up temporary file
    if (file_exists($filepath)) {
      unlink($filepath);
    }
  }

  private function importPdfPages($filepath) {
    global $tplidx;
    $pagecount = $this->setSourceFile($filepath);
    for ($tel = 1; $tel <= $pagecount; $tel++) {
      $tplidx = $this->importPage($tel);
      $this->addPage();
      $this->useTemplate($tplidx, 0, 0);
    }
  }

  public function generate() {
    $this->includePdfs($this->quotation_ids_arr);

    $filename = 'offertes_' . $this->breti_invoice_number .'.pdf';
    if (!$this->breti_invoice_number) {
      $filename = 'offertes_' . implode('_',$this->quotation_ids_arr) .'.pdf';
    }

    if ($this->download) {
      $this->Output("D", $filename);
    }
    else {
      $this->Output("I", $filename);
    }

//    $this->Output("F", DIR_TEMP . $filename);

    return $filename;
  }

  public function setIsAdmin($isAdmin) {
    $this->isAdmin = $isAdmin;
  }
}