<?php
class BaseStoneSizes extends AppModel
{
  const DB_NAME = 'rde_b1mo';
  const TABLE_NAME = 'stone_sizes';
  const OM_CLASS_NAME = 'StoneSizes';
  const columns = ['sizeId', 'brandId', 'short', 'name', 'length', 'width', 'height', 'click', 'wall_min', 'wall_max', 'display', 'common'];
  const field_structure = [
    'sizeId'                      => ['type' => 'int', 'length' => '3', 'null' => false],
    'brandId'                     => ['type' => 'tinyint', 'length' => '2', 'null' => false],
    'short'                       => ['type' => 'varchar', 'length' => '25', 'null' => false],
    'name'                        => ['type' => 'varchar', 'length' => '150', 'null' => false],
    'length'                      => ['type' => 'decimal', 'length' => '4,1', 'null' => false],
    'width'                       => ['type' => 'decimal', 'length' => '4,1', 'null' => false],
    'height'                      => ['type' => 'decimal', 'length' => '4,1', 'null' => false],
    'click'                       => ['type' => 'decimal', 'length' => '3,2', 'null' => false],
    'wall_min'                    => ['type' => 'smallint', 'length' => '3', 'null' => true],
    'wall_max'                    => ['type' => 'smallint', 'length' => '3', 'null' => true],
    'display'                     => ['type' => 'enum', 'length' => '2', 'null' => false, 'enums' => ['true','false']],
    'common'                      => ['type' => 'enum', 'length' => '2', 'null' => false, 'enums' => ['true','false']],
  ];

  protected static $primary_key = ['sizeId'];
  protected $auto_increment = 'sizeId';

  public $sizeId, $brandId, $short, $name, $length, $width, $height, $click, $wall_min, $wall_max, $display, $common;

  function __construct($obj_arr = [], &$error_codes = []) {
    parent::__construct();
    $this->setDefaults();
    if (!empty($obj_arr)) {
      $this->from_array($obj_arr);
      $this->valid_required_fields($error_codes);
    }
  }

  /**
  * Set default object values
  **/
  public function setDefaults() {
    $this->brandId = 1;
    $this->width = 105.0;
    $this->click = 2.00;
    $this->display = 'false';
    $this->common = 'true';
  }

  /**
   * @param array $conditions (required)
   * @param string $raw_sql (optional)
   * @return StoneSizes[]
   **/
  public static function find_all_like($conditions, $raw_sql = '') {
    return parent::generic_find_all_like($conditions, static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param array $conditions (required)
   * @param string $raw_sql (optional)
   * @return StoneSizes[]
   **/
  public static function find_all_by($conditions, $raw_sql = '') {
    return parent::generic_find_all_by($conditions, static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param string $raw_sql (optional)
   * @return StoneSizes[]
   **/
  public static function find_all($raw_sql = '') {
    return parent::generic_find_all(static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param array $conditions (required)
   * @param string $raw_sql (optional)
   * @return StoneSizes
   **/
  public static function find_by($conditions, $raw_sql = '') {
    return parent::generic_find_by($conditions, static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param string $id (required)
   * @param string $raw_sql (optional)
   * @return StoneSizes
   **/
  public static function find_by_id($id, $raw_sql = '') {
    return parent::generic_find_by_id($id, static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param array $conditions (required)
   * @param string $raw_sql (optional)
   * @return int
   **/
  public static function count_all_by($conditions, $raw_sql = '') {
    return parent::generic_count_all_by($conditions, static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param array $conditions (required)
   * @param string $raw_sql (optional)
   * @return bool
   **/
  public static function delete_by($conditions, $raw_sql = '') {
    return parent::generic_delete_by($conditions, static::OM_CLASS_NAME, $raw_sql);
  }

}