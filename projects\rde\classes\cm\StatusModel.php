<?php

  AppModel::loadBaseClass('BaseStatus');

  class StatusModel extends BaseStatus {

    const STATUS_NEW = 10;
    const STATUS_ORDER = 20;
    const STATUS_CHECKED = 30;
    const STATUS_PREPARED = 35;
    const STATUS_IN_PRODUCTION = 38;
    const STATUS_PRODUCED = 40;
    const STATUS_PACKED = 50;
    const STATUS_LOADED = 53;
    const STATUS_DELIVERED = 55;
    const STATUS_INVOICED = 60;
    const STATUS_PAID = 62;
    const STATUS_NOT_PAID = 61;
    const STATUS_BANKRUPT = 63;
    const STATUS_SPLIT = 70;
    const STATUS_REMOVED = 80;

    const STATI = [
      self::STATUS_NEW           => 'Offerte',
      self::STATUS_ORDER         => 'Opdracht',
      self::STATUS_CHECKED       => 'Akkoord',
      self::STATUS_PREPARED      => 'Voorbereid print',
      self::STATUS_IN_PRODUCTION => 'In productie',
      self::STATUS_PRODUCED      => 'Geproduceerd',
      self::STATUS_PACKED        => 'Verzendklaar',
      self::STATUS_LOADED        => 'Geladen',
      self::STATUS_DELIVERED     => 'Geleverd',
      self::STATUS_INVOICED      => 'Gefactureerd',
      self::STATUS_PAID          => 'Betaald',
      self::STATUS_NOT_PAID      => 'Nog niet betaald na 3de aanmaning',
      self::STATUS_BANKRUPT      => 'Failliet',
      self::STATUS_SPLIT         => 'Gesplitst',
      self::STATUS_REMOVED       => 'Verwijderd',
    ];


    public static function getName($statusId) {

      switch ($statusId) {
        case self::STATUS_NEW:
          return "Offerte";
        case self::STATUS_ORDER:
          return "Besteld";
        case self::STATUS_CHECKED:
          return "Akkoord";
        case self::STATUS_PREPARED:
        case self::STATUS_IN_PRODUCTION:
          if (Config::isTrue("IS_BACKEND")) {
            return "Geproduceerd leverancier";
          }
          return "Voorbereid";
        case self::STATUS_PRODUCED:
          return "Geproduceerd";
        case self::STATUS_PACKED:
          return "Ingepakt";
        case self::STATUS_LOADED:
          return "Geladen";
        case self::STATUS_DELIVERED:
        case self::STATUS_INVOICED:
        case self::STATUS_NOT_PAID:
        case self::STATUS_BANKRUPT:
          return "Geleverd";
        case self::STATUS_PAID:
          return "Betaald";
        case self::STATUS_SPLIT:
          return "Gesplitst";
        case self::STATUS_REMOVED:
          return "Te verwijderen";//??
      }
      return "";
    }

    public static function getIconClass($statusId) {
      return "quotation_icon_" . $statusId;
    }

    public static function getIconHTML($statusId, $extraTitle = "") {
      $icondesc = Status::getName($statusId);
      $icondesc .= $extraTitle;
      $str = '<div class="quotation_icon qtipa ' . Status::getIconClass($statusId) . ' ';
      if ($statusId == Status::STATUS_PAID) {
        $str .= 'fa fa-check';
      }
      elseif ($statusId == Status::STATUS_SPLIT) {
        $str .= 'fa fa-bolt';
      }
      $str .= '" alt="' . $icondesc . '" title="' . $icondesc . '" >';
      $str .= '</div>';
      return $str;
    }

  }