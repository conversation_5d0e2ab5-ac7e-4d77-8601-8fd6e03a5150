<?php

  use domain\quotations\service\GetBrandIdsByStone;
  use domain\quotations\service\GetStoneCategoryInfo;
  use domain\stones\service\StoneSiteOverview;
  use gsdfw\domain\klantenvertellen\service\Klantenvertellen;

  require_once 'siteKiezenActions.php';

  class siteRdeActions extends siteActions {

    use siteKiezenActions;

    public function preExecute() {
      parent::preExecute();
      $this->cats = Category::getShopCats();
    }

    public function executeHome() {

      if (isset($_GET["p"])) {
        ResponseHelper::redirect("/");
      }

      $services = [];
      $lp = Page::getPageAndContent(230);
      if ($lp) $services[] = $lp;
      $lp = Page::getPageAndContent(329);
      if ($lp) {
        $lp->content->title = "Natuursteen";
        $services[] = $lp;
      }
      $lp = Page::getPageAndContent(330);
      if ($lp) {
        $lp->content->title = "Beton";
        $services[] = $lp;
      }

      $this->services = $services;
    }

    public function executeModels() {
      parent::executeDefault();

      $quotation = new Quotations();
      $quotation->brandId = "";
      $quotation->colorId = "";
      $quotation->sizeId = "";
      $quotation->endstone = "";
      $quotation->shorter = "";
      $quotation->offerteVariant = "keramische_raamdorpel";

      $brand_filt = ["display" => "true"];
      if (SandboxUsers::isAdmin()) {
        $brand_filt = [];
      }
      $this->brands = StoneBrands::find_all_by($brand_filt, "ORDER BY displayOrder");
      $this->colors = AppModel::plainObjects(StoneColors::getDisplayColors([1, 2]));
      $this->sizes = AppModel::plainObjects(StoneSizes::getDisplayStonesizesWithstone([1, 2]), ["stone"]);
      $this->step = 1;
      $this->quotation = $quotation;
      $this->errors = [];

      Context::addJavascript('/gsdfw/includes/jsscripts/vuejs/vue.3.1.5' . (DEVELOPMENT ? '' : '.min') . '.js');

    }


    public function executeNeggemaattabel() {
      parent::executeDefault();

      $aFiles = [];
      $sMaterial = '';

      if ((isset($_GET['kozijn'])) && ($_GET['kozijn'] == 'hout' || $_GET['kozijn'] == 'kunststof') && (ctype_digit($_GET['neg']) && $_GET['neg'] >= 0 && $_GET['neg'] <= 195)) {

        if ($_GET['kozijn'] == 'hout') {
          $sMaterial = 'hout';
        }
        elseif ($_GET['kozijn'] == 'kunststof') {
          $sMaterial = 'kunststof';
        }

        $sPath = DIR_UPLOADS . 'neggemaattabel/' . $sMaterial . '/';
        $iFile = 0;

        # 1. Collect files
        if (is_dir($sPath)) {
          if ($rDir = @opendir($sPath)) {


            while (($sFile = readdir($rDir)) !== false) {
              if (filetype($sPath . $sFile) == 'file') {
                #preg_match_all("/neg (?P<neg>[0-9]{1,3})mm model (?P<length>[0-9]{1,3})x(?P<width>[0-9]{1,3})x(?P<depth>[0-9]{1,2}) (?P<click>[2|5]{1})cm klik\.pdf$/", $sFile, $aMatches);
                preg_match_all("/neg (?P<neg>" . intval($_GET['neg']) . ")mm model St\. Joris (?P<length>[0-9]{1,3})x(?P<width>[0-9]{1,3})x(?P<depth>[0-9]{1,2}) (?P<click>[2|5]{1})cm klik\.pdf$/", $sFile, $aMatches);

                #	echo "<p>{$aMatches['neg'][0]} vs ".intval($_GET['neg'])."</p>";

                if (isset($aMatches['neg'][0]) && $aMatches['neg'][0] != '' && intval($aMatches['neg'][0]) == intval($_GET['neg'])) {
                  #if($aMatches['neg'][0] === intval($_GET['neg'])) {
                  $aFiles[$iFile]['filename'] = $sFile;
                  $aFiles[$iFile]['length'] = $aMatches['length'][0];
                  $aFiles[$iFile]['width'] = $aMatches['width'][0];
                  $aFiles[$iFile]['depth'] = $aMatches['depth'][0];
                  $aFiles[$iFile]['click'] = $aMatches['click'][0];

                  $iFile++;
                }

                # Colorline
                preg_match_all("/neg (?P<neg>" . intval($_GET['neg']) . ")mm model St\. Joris Color-Line RD 170\.pdf$/", $sFile, $aMatches2);

                #if($aMatches2['neg'][0] === intval($_GET['neg'])) {
                if (isset($aMatches2['neg'][0]) && $aMatches2['neg'][0] != '' && intval($aMatches2['neg'][0]) === intval($_GET['neg'])) {
                  $aFiles[$iFile]['filename'] = $sFile;
                  $aFiles[$iFile]['length'] = 170;
                  $aFiles[$iFile]['width'] = 105;
                  $aFiles[$iFile]['depth'] = 50;
                  $aFiles[$iFile]['click'] = 'n.v.t.';

                  $iFile++;
                }
              }
            }

            closedir($rDir);
          }
        }

        #2. Sort files (if more then one file)
        if (count($aFiles) > 1) {
          $aLength = [];
          $aWidth = [];
          $aDepth = [];
          $aClick = [];

          foreach ($aFiles as $iKey => $aRow) {
            $aLength[$iKey] = $aRow['length'];
            $aWidth[$iKey] = $aRow['width'];
            $aDepth[$iKey] = $aRow['depth'];
            $aClick[$iKey] = $aRow['click'];

          }
          array_multisort($aLength, SORT_ASC, $aWidth, SORT_ASC, $aDepth, SORT_ASC, $aClick, SORT_ASC, $aFiles);
        }
      }

      $this->aFiles = $aFiles;
      $this->sMaterial = $sMaterial;
    }


    public function executeContact() {
      $errors = [];

      if (isset($_POST['send'])) {

        ValidationHelper::isSpamFreePost();

        if (empty($_POST['naam'])) {
          $errors['naam'] = __("Naam");
        }

        if (!ValidationHelper::isEmail($_POST['email'])) {
          $errors['emailadres'] = __("E-mailadres");
        }

        if (empty(trim($_POST['vraagopmerking'])) || strlen($_POST['vraagopmerking']) < 15) {
          $errors['vraagopmerking'] = __("Vraag/opmerking is leeg of te kort. Formuleer uw vraag duidelijk.");
        }
        elseif ((isset($_POST['vraagopmerking']) && ValidationHelper::containsHttp($_POST['vraagopmerking']))) {
          $errors['vraagopmerking'] = __("U mag geen links gebruiken in het vraag/opmerking veld.");
        }

        if (!$this->validateRecaptcha()) {
          $errors[] = "Helaas, u bent gedetecteerd als robot. Probeer het nogmaals.";
        }

        if (empty($errors)) {
          $subject = "Contact aanvraag vanaf website " . $_SERVER['HTTP_HOST'];
          $fields = [
            'name'           => 'Naam',
            'email'          => 'E-mailadres',
            'phone'          => 'Telefoon',
            'Vraagopmerking' => 'Vragen/opmerkingen',
          ];
          $message = '<table>';
          foreach ($_POST as $key => $value) {
            if ($key != 'send' && $key != 'g-recaptcha-response') {
              $message .= '<tr><td>' . (isset($fields[$key]) ? $fields[$key] : ucfirst($key)) . ": </td><td>" . nl2br($value) . "</td></tr>";
            }
          }
          $message .= '</table>';

          $gsd_mailer = GsdMailer::build(MAIL_FROM, $subject, $message);
          $gsd_mailer->setReplyTo($_POST['email']);
          $gsd_mailer->send();

          ResponseHelper::redirectMessage(__("Uw aanvraag is ontvangen.<br/>Wij nemen zo spoedig mogelijk contact met u op."), reconstructQuery());

        }
      }
      $this->errors = $errors;
    }

    public function executeSearch() {
      $searchstr = "";
      if (isset($_GET["topsearch"])) {
        $searchstr = trim($_GET["topsearch"]);
        Searched::add($searchstr);
      }

      $items = [];

      if ($searchstr != "") {

        foreach (Product::getProductsBySearch($searchstr, $_SESSION['lang']) as $product) {
          $item = [];
          $item['title'] = highlight($product->content->name, $searchstr);
          $item['link'] = $product->getShopUrl();
          $item['desc'] = highlight(substr(strip_tags($product->content->description), 0, 100), $searchstr);
          $rank = 15 * StringHelper::substri_count($product->content->name, $searchstr);
          $rank += StringHelper::substri_count($product->content->description, $searchstr);
          $item['rank'] = $rank;
          $items[] = $item;
        }

        foreach (Page::getPagesBySearch($searchstr, $_SESSION['lang'], true, $this->site->id) as $page) {
          $item = [];
          $item['link'] = $page->getUrl();
          $item['title'] = highlight($page->content->title, $searchstr);
          $item['desc'] = highlight(substr(strip_tags($page->content->content1), 0, 100), $searchstr);
          $rank = 15 * StringHelper::substri_count($page->content->title, $searchstr);
          $rank += StringHelper::substri_count($page->content->content1, $searchstr);
          $item['rank'] = $rank;
          $items[] = $item;
        }
      }

      usort($items, function ($a, $b) {
        return $b['rank'] - $a['rank'];
      });

      $this->items = $items;
      $this->searchstr = $searchstr;
    }

    public function executeDefault() {
      parent::executeDefault();
      if ($this->pageId == 278 || $this->page->parent_id == 278) {
        $this->template = "landingSuccess.php";
        $this->landing = true;
        Context::addStylesheetVersion('/projects/rde/templates/frontend/style/grizzly_landingpage_style');

        $errors = [];
        if (isset($_POST["go"])) {

          ValidationHelper::isSpamFreePost();

          if ($_POST['naam'] == "") {
            $errors['naam'] = __("Naam");
          }
          if ($_POST['bedrijfsnaam'] == "") {
            $errors['bedrijfsnaam'] = __("Bedrijfsnaam");
          }
          if (((isset($_POST['email']) && $_POST['email'] != "")) && !ValidationHelper::isEmail($_POST['email'])) {
            $errors['emailadres'] = __("E-mailadres");
          }
          if ($_POST['telefoonnummer'] == "") {
            $errors['telefoonnummer'] = __("Telefoonnummer");
          }
          if ($_POST['vraag'] == "" || strlen($_POST['vraag']) < 15) {
            $errors['vraag'] = __("Vraag is leeg of te kort. Formuleer uw vraag duidelijk.");
          }

          if (!$this->validateRecaptcha()) {
            $errors[] = "Helaas, u bent gedetecteerd als robot. Probeer het nogmaals.";
          }

          if (count($errors) == 0) {
            //send mail
            $subject = "Contact aanvraag vanaf website " . $_SERVER['HTTP_HOST'];
            $fields = [
              'email'          => 'E-mailadres',
              'Vraagopmerking' => 'Vragen/opmerkingen',
            ];
            $message = '<table>';
            foreach ($_POST as $key => $value) {
              if ($key != 'go' && $key != 'keyid' && $key != 'g-recaptcha-response') {
                $message .= '<tr><td>' . (isset($fields[$key]) ? $fields[$key] : ucfirst($key)) . ": </td><td>" . nl2br($value) . "</td></tr>";
              }
            }
            $message .= '</table>';

            $gsd_mailer = GsdMailer::build(MAIL_FROM, $subject, $message);
            $gsd_mailer->setReplyTo($_POST['email']);
            $gsd_mailer->send();

            if (count($errors) == 0) {
              $_SESSION['flash_message'] = __("Uw aanvraag is ontvangen.<br/>Wij nemen zo spoedig mogelijk contact met u op.");
              ResponseHelper::redirect(reconstructQuery());
            }

          }
        }

        $this->errors = $errors;
      }
    }

    public function executeKlantenvertellen() {
      $this->executeDefault();

      $kv = new Klantenvertellen("9eggwwhsy4s2ec2", "Raamdorpelelementen B.V.", "0031497360791", "Raambrug 9", "5531 AG", "Bladel", "NL", Context::getSiteDomain(true) . "/projects/rde/templates/frontend/images/logo_social.png");
      $kv->loadXML();
      $this->kv = $kv;
    }

    public function executeReviewsignout() {
      parent::executeDefault();

      $cre = CustomerRatingEmails::find_by(["id" => $_GET["id"], "randomCode" => $_GET["code"]]);
      if (!$cre) {
        MessageFlashCoordinator::addMessageAlert("Uw uitschrijfcode is onjuist. Zorg dat u de complete url kopiërt naar de browser.");
        ResponseHelper::redirect("/");
      }
      if ($cre->inActive == 1) {
        MessageFlashCoordinator::addMessageAlert("U heeft zich reeds uitgeschreven van onze review emails.");
        ResponseHelper::redirect("/");
      }
      $cre->inActive = 1;
      $cre->save();

    }

    public function executestoneoverview() {
    }


    /**
     * Opbouwen json met waarden
     * @return void
     */
    public function executeStoneoverviewvalues() {
      header('Content-Type: application/json');
      $stoneSiteOverview = new StoneSiteOverview(isset($_GET["material"]) ? $_GET["material"] : Stones::MATERIAL_NATUURSTEEN);
      echo $stoneSiteOverview->getValues();
      ResponseHelper::exit();
    }

  }