<?php

  class NavigationBuilderFactory extends NavigationBuilder {

    public static function buildBackend() {

      parent::buildBackend();

      $nav = Navigation::getInstance();

      if ($nav->getItem("M_HOME") != null) $nav->getItem("M_HOME")->setShow(false); //hide home

      $nav->addPMItem('M_RDE_DASHBOARD', 'M_TOP');

      $nav->addPMItem('M_BEDRIJVENGIDS', 'M_TOP');
      $nav->addPMItem('M_BEDRIJVENGIDS_LIST', 'M_BEDRIJVENGIDS');
      $nav->addPMItem('M_BEDRIJVENGIDS_EDIT', 'M_BEDRIJVENGIDS_LIST');
      $nav->addPMItem('M_BEDRIJVENGIDS_PERSONS', 'M_BEDRIJVENGIDS_LIST');
      $nav->addPMItem('M_BEDRIJVENGIDS_FINANCIAL', 'M_BEDRIJVENGIDS_LIST');
      $nav->addPMItem('M_BEDRIJVENGIDS_LOCATIONS', 'M_BED<PERSON>JVENGIDS_LIST');
      $nav->addPMItem('M_BEDRIJVENGIDS_SETTINGS', 'M_BEDRIJVENGIDS_LIST');
      $nav->addPMItem('M_BEDRIJVENGIDS_DOCUMENTS', 'M_BEDRIJVENGIDS_LIST');
      $nav->addPMItem('M_BEDRIJVENGIDS_OTHER', 'M_BEDRIJVENGIDS_LIST');
      $nav->addPMItem('M_SANDBOXUSERS', 'M_BEDRIJVENGIDS');
      $nav->addPMItem('M_BEDRIJVENGIDS_EXPORT', 'M_BEDRIJVENGIDS');

      if (Privilege::hasRight('M_BEDRIJVENGIDS_EDIT')) $nav->getItem('M_BEDRIJVENGIDS_EDIT')->setShowMainMenu(false);
      if (Privilege::hasRight('M_BEDRIJVENGIDS_PERSONS')) $nav->getItem('M_BEDRIJVENGIDS_PERSONS')->setShowMainMenu(false);
      if (Privilege::hasRight('M_BEDRIJVENGIDS_FINANCIAL')) $nav->getItem('M_BEDRIJVENGIDS_FINANCIAL')->setShowMainMenu(false);
      if (Privilege::hasRight('M_BEDRIJVENGIDS_LOCATIONS')) $nav->getItem('M_BEDRIJVENGIDS_LOCATIONS')->setShowMainMenu(false);
      if (Privilege::hasRight('M_BEDRIJVENGIDS_SETTINGS')) $nav->getItem('M_BEDRIJVENGIDS_SETTINGS')->setShowMainMenu(false);
      if (Privilege::hasRight('M_BEDRIJVENGIDS_DOCUMENTS')) $nav->getItem('M_BEDRIJVENGIDS_DOCUMENTS')->setShowMainMenu(false);
      if (Privilege::hasRight('M_BEDRIJVENGIDS_OTHER')) $nav->getItem('M_BEDRIJVENGIDS_OTHER')->setShowMainMenu(false);

      $nav->addPMItem('M_REVIEWS', 'M_OTHER');
      $nav->addPMItem('M_QUOT_PHOTOS', 'M_OTHER');
      $nav->addPMItem('M_INBOX', 'M_OTHER');
      $nav->addPMItem('M_DAMAGE_STATS', 'M_OTHER');
      $nav->addPMItem('M_DAMAGE', 'M_OTHER');
      $nav->addPMItem('M_PUSH_NOTIFICATIONS', 'M_OTHER');

      if (Privilege::hasRight('M_PLANNING')) {
        $nav->addPMItem('M_PLANNING', 'M_OTHER');
        $nav->addPMItem('M_PLANNING_OV', 'M_PLANNING');
        $nav->addPMItem('M_PLANNING_HOURS', 'M_PLANNING');

        if (Privilege::hasRight('M_PLANNING_OV')) $nav->getItem('M_PLANNING_OV')->setShowMainMenu(false);
        if (Privilege::hasRight('M_PLANNING_HOURS')) $nav->getItem('M_PLANNING_HOURS')->setShowMainMenu(false);
      }

      if (Privilege::hasRight('M_KOPPELINGEN')) {
        if (Privilege::hasRight('M_MULTIVERS_EDIT')) $nav->getItem('M_MULTIVERS_EDIT')->setShowMainMenu(false);
      }

      $nav->addPMItem('M_RDE_STATUS_OVERVIEW', 'M_OTHER');


      $nav->addPMItem('M_SUPPLIER_STOCK_OV', 'M_TOP');
      $nav->addPMItem('M_SUPPLIER_STOCK', 'M_SUPPLIER_STOCK_OV');
      $nav->addPMItem('M_SUPPLIER_NEEDEDSTONES', 'M_SUPPLIER_STOCK_OV');
      $nav->addPMItem('M_SUPPLIER_ORDER', 'M_SUPPLIER_STOCK_OV');
      $nav->addPMItem('M_SUPPLIER_ORDERS', 'M_SUPPLIER_STOCK_OV');
      $nav->addPMItem('M_SUPPLIER_ORDERS_ORDERED', 'M_SUPPLIER_STOCK_OV');
      $nav->addPMItem('M_SUPPLIER_ORDERS_DELIVERED', 'M_SUPPLIER_STOCK_OV');
      $nav->addPMItem('M_SUPPLIER_ORDERSPRODUCT', 'M_SUPPLIER_STOCK_OV');


      $nav->addPMItem('M_INVOICES_CREATE', 'M_INVOICES');
      $nav->addPMItem('M_INVOICES_RDE', 'M_INVOICES');
      $nav->addPMItem('M_INVOICES_REMINDERS', 'M_INVOICES');
      $nav->addPMItem('M_INVOICES_NIETINBAAR', 'M_INVOICES');
      $nav->addPMItem('M_INVOICES_INVOICE_PAID', 'M_INVOICES');
      $nav->addPMItem('M_INVOICES_MOLLIE', 'M_INVOICES');
      $nav->addPMItem('M_INVOICES_MV_SYNC', 'M_INVOICES');
      $nav->addPMItem('M_INVOICES_NON_PAYMENT', 'M_INVOICES');
      $nav->addPMItem('M_INVOICES_NOT_SEND', 'M_INVOICES');

      $nav->addPMItem('M_STATS_OVERVIEW', 'M_STATS');
      $nav->addPMItem('M_STATS_STONES_SPEED', 'M_STATS');
      $nav->addPMItem('M_STATS_EMPLOYEES_SPEED', 'M_STATS');
      $nav->addPMItem('M_SUSCPICIOUS_QUOTATIONS', 'M_STATS');
      $nav->addPMItem('M_RDE_LARGEWORKLIST', 'M_STATS');


      $nav->addPMItem('M_RDE_SETTINGS', 'M_TOP');
      $nav->addPMItem('M_STONES_SIZES', 'M_RDE_SETTINGS');
      $nav->addPMItem('M_STONES_COLORS', 'M_RDE_SETTINGS');
      $nav->addPMItem('M_STONES_STONES', 'M_RDE_SETTINGS');
      $nav->addPMItem('M_STONES_CATEGORIES', 'M_RDE_SETTINGS');
      $nav->addPMItem('M_WINDOWSILLS', 'M_RDE_SETTINGS');
      $nav->addPMItem('M_SHORTELEMENTS', 'M_RDE_SETTINGS');
      $nav->addPMItem('M_MITRES', 'M_RDE_SETTINGS');


      $nav->addPMItem('M_RDE_PRICES', 'M_TOP');

      $nav->addPMItem('M_STONEPRICES', 'M_RDE_PRICES');
      $nav->addPMItem('M_STONEPRICES_BRETI', 'M_RDE_PRICES');
      $nav->addPMItem('M_STONEPRICES_BRETI_NEW', 'M_RDE_PRICES');
      $nav->addPMItem('M_GLUEPRICES', 'M_RDE_PRICES');
      $nav->addPMItem('M_WINDOWSILLPRICES', 'M_RDE_PRICES');
      $nav->addPMItem('M_MITREFACTORS', 'M_RDE_PRICES');
      $nav->addPMItem('M_CUSTOMERCATS', 'M_RDE_PRICES');
      $nav->addPMItem('M_PAYMENTTERMS', 'M_RDE_PRICES');

      $nav->addPMItem('M_SUPPLIER_ORDERS_EXT_ORDER', 'M_TOP');
      $nav->addPMItem('M_SUPPLIER_ORDERS_EXT_PRODUCTION', 'M_TOP');
      $nav->addPMItem('M_SUPPLIER_ORDERS_EXT_PICKUP', 'M_TOP');
      if (DEVELOPMENT) {
        $nav->addPMItem('M_SUPPLIER_ORDERS_EXT_INVOICES', 'M_TOP');
      }
      $nav->addPMItem('M_SUPPLIER_ORDERS_EXT_DELIVERED', 'M_TOP');
      if (DEVELOPMENT) {
        $nav->addPMItem('M_SUPPLIER_ORDERS_EXT_PRICES', 'M_TOP');
      }

      $nav->addPMItem('M_DOCUMENTS', 'M_TOP');
      $nav->addPMItem('M_DOCUMENTS_SANDBOXDOCS', 'M_DOCUMENTS');
      $nav->addPMItem('M_DOCUMENTS_SANDBOXDOCS2', 'M_DOCUMENTS');
      $nav->addPMItem('M_DOCUMENTS_OLD_QUOTATIONS', 'M_DOCUMENTS');
      $nav->addPMItem('M_DOCUMENTS_OLD_INVOICES', 'M_DOCUMENTS');
      $nav->addPMItem('M_DOCUMENTS_DOCS', 'M_DOCUMENTS');

      $nav->addPMItem('M_PRODUCTION', 'M_TOP');
      $nav->addPMItem('M_PRODUCTION_WORK', 'M_PRODUCTION');
      $nav->addPMItem('M_PRODUCTION_CHANGE_WIDTH', 'M_PRODUCTION');
      $nav->addPMItem('M_PRODUCTION_PRINT_RACK', 'M_PRODUCTION');
      $nav->addPMItem('M_PRODUCTION_WEBSHOP', 'M_PRODUCTION');


      $nav->addPMItem('M_RDE_ORDERS', 'M_TOP');
      $nav->addPMItem('M_RDE_ORDERS_LIST', 'M_RDE_ORDERS');
      $nav->addPMItem('M_RDE_ORDERS_STATUS_COMMISSION', 'M_RDE_ORDERS');
      $nav->addPMItem('M_RDE_ORDERS_DELIVERY_WEEK', 'M_RDE_ORDERS');
      $nav->addPMItem('M_RDE_ORDERS_WEBSHOP', 'M_RDE_ORDERS');
      $nav->addPMItem('M_RDE_ORDERS_PRIVATE', 'M_RDE_ORDERS');
      $nav->addPMItem('M_RDE_ORDERS_NO_COMPANY', 'M_RDE_ORDERS');
      $nav->addPMItem('M_RDE_ORDERS_SPECIAL', 'M_RDE_ORDERS');
      $nav->addPMItem('M_RDE_ORDERS_NO_DELIVERY_ADDRESS', 'M_RDE_ORDERS');
      $nav->addPMItem('M_RDE_ORDERS_CONCRETE', 'M_RDE_ORDERS');
      $nav->addPMItem('M_RDE_ORDERS_CONTAINERS_LIST', 'M_RDE_ORDERS');
      $nav->addPMItem('M_RDE_ORDERS_NATURESTONE_B', 'M_RDE_ORDERS');
      $nav->addPMItem('M_RDE_ORDERS_NATURESTONE_C', 'M_RDE_ORDERS');
      $nav->addPMItem('M_RDE_ORDERS_ISOSILL', 'M_RDE_ORDERS');
//      $nav->addPMItem('M_RDE_ORDERS_CONTAINERS_OVERVIEW', 'M_RDE_ORDERS_CONTAINERS_LIST');
      $nav->addPMItem('M_RDE_ORDERS_GENERAL', 'M_RDE_ORDERS_LIST');
      $nav->addPMItem('M_RDE_ORDERS_CONTACT', 'M_RDE_ORDERS_LIST');
      $nav->addPMItem('M_RDE_ORDERS_PRODUCTION', 'M_RDE_ORDERS_LIST');
      $nav->addPMItem('M_RDE_ORDERS_PRODUCTION_SIZES', 'M_RDE_ORDERS_LIST');
      $nav->addPMItem('M_RDE_ORDERS_DELIVERY', 'M_RDE_ORDERS_LIST');
      $nav->addPMItem('M_RDE_ORDERS_INVOICE', 'M_RDE_ORDERS_LIST');
      $nav->addPMItem('M_RDE_ORDERS_OTHER', 'M_RDE_ORDERS_LIST');
      $nav->addPMItem('M_RDE_ORDERS_MAIL', 'M_RDE_ORDERS_LIST');
      $nav->addPMItem('M_RDE_ORDERS_MAIL_QUOTATION', 'M_RDE_ORDERS_MAIL', false);
      $nav->addPMItem('M_RDE_ORDERS_ROUTE', 'M_RDE_ORDERS_LIST');
      $nav->addPMItem('M_RDE_ORDERS_CONTAINERS', 'M_RDE_ORDERS_LIST');
      $nav->addPMItem('M_RDE_ORDERS_IMAGES', 'M_RDE_ORDERS_LIST');
      $nav->addPMItem('M_INCOMING_CALL', 'ROOT', false);

      $nav->addPMItem('M_RDE_MAP', 'M_TOP');

      if (Privilege::hasRight('M_RDE_ORDERS_GENERAL')) $nav->getItem('M_RDE_ORDERS_GENERAL')->setShowMainMenu(false);
      if (Privilege::hasRight('M_RDE_ORDERS_CONTACT')) $nav->getItem('M_RDE_ORDERS_CONTACT')->setShowMainMenu(false);
      if (Privilege::hasRight('M_RDE_ORDERS_PRODUCTION')) $nav->getItem('M_RDE_ORDERS_PRODUCTION')->setShowMainMenu(false);
      if (Privilege::hasRight('M_RDE_ORDERS_PRODUCTION_SIZES')) $nav->getItem('M_RDE_ORDERS_PRODUCTION_SIZES')->setShowMainMenu(false);
      if (Privilege::hasRight('M_RDE_ORDERS_DELIVERY')) $nav->getItem('M_RDE_ORDERS_DELIVERY')->setShowMainMenu(false);
      if (Privilege::hasRight('M_RDE_ORDERS_INVOICE')) $nav->getItem('M_RDE_ORDERS_INVOICE')->setShowMainMenu(false);
      if (Privilege::hasRight('M_RDE_ORDERS_OTHER')) $nav->getItem('M_RDE_ORDERS_OTHER')->setShowMainMenu(false);
      if (Privilege::hasRight('M_RDE_ORDERS_MAIL')) $nav->getItem('M_RDE_ORDERS_MAIL')->setShowMainMenu(false);
      if (Privilege::hasRight('M_RDE_ORDERS_ROUTE')) $nav->getItem('M_RDE_ORDERS_ROUTE')->setShowMainMenu(false);
      if (Privilege::hasRight('M_RDE_ORDERS_CONTAINERS')) $nav->getItem('M_RDE_ORDERS_CONTAINERS')->setShowMainMenu(false);
      if (Privilege::hasRight('M_RDE_ORDERS_IMAGES')) $nav->getItem('M_RDE_ORDERS_IMAGES')->setShowMainMenu(false);

      //volgorde aanpassen


      $nav->moveItemsByArray([
        'M_BEDRIJVENGIDS',
        'M_RDE_ORDERS',
        'M_PRODUCTION',
        'M_RDE_MAP',
        'M_INVOICES',
        'M_DOCUMENTS',
        'M_SUPPLIER_STOCK_OV',
        'M_HOURS',
        'M_RDE_PRICES',
        'M_RDE_SETTINGS',
        'M_OTHER',
      ]);

      //verplaats maillog naar subitem
      $nav->moveItemAfterPageId('M_KOPPELINGEN', 'M_MAIL_LOG');


//      $nav->moveItemAfterPageId('M_RDE_DASHBOARD', 'M_HOME');
//      if (Privilege::hasRight('M_CATALOG')) {
//        $nav->moveItemAfterPageId('M_BEDRIJVENGIDS', 'M_CATALOG');
//      }
//      if (Privilege::hasRight('M_CATALOG')) {
//        $nav->moveItemAfterPageId('M_INVOICES', 'M_CATALOG');
//      }
//      $nav->moveItemAfterPageId('M_ORGANISATIONS', 'M_BEDRIJVENGIDS');
//      $nav->moveItemAfterPageId('M_OTHER_SETTINGS', 'M_DAMAGE');
//      $nav->moveItemAfterPageId('M_MAIL_LOG', 'M_OTHER_SETTINGS');
//      if (Privilege::hasRight('M_SUPPLIER_STOCK_OV')) {
//        $nav->moveItemAfterPageId('M_SUPPLIER_STOCK_OV', 'M_CATALOG');
//      }
//      if (Privilege::hasRight('M_STATS_SEARCHED')) {
//        $nav->moveItemAfterPageId('M_STATS_SEARCHED', 'M_SUSCPICIOUS_QUOTATIONS');
//      }
//
//      $nav->moveItemAfterPageId('M_KOPPELINGEN', 'M_MAIL_LOG');
//      $nav->moveItemAfterPageId('M_OTHER', 'M_DOCUMENTS');
//      $nav->moveItemAfterPageId('M_STATS', 'M_DOCUMENTS');
//
//      if(Privilege::hasRight('M_RDE_ORDERS')) {
//        $nav->moveItemAfterPageId('M_RDE_ORDERS', 'M_BEDRIJVENGIDS');
//      }


    }

  }