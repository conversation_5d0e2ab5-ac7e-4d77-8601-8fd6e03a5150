<?php TemplateHelper::includePartial('_tabs.php', 'prices'); ?>

<h3>Bewerk verstek factors</h3>

<?php writeErrors($form->getErrors(), true); ?>

<form method="post">
  <table class="default_table" style="width: auto;">
    <tr class="dataTableHeadingRow">
      <td>Item</td>
      <td>Instelling</td>
    </tr>
    <tr class="dataTableRow trhover">
      <td>Geldig vanaf</td>
      <td>
        01-01-<?php echo $_GET["yearfrom"] ?>
      </td>
    </tr>
  </table>
  <br/>

  <table class="default_table" style="width: auto;">
    <tr class="dataTableHeadingRow">
      <td>Formaat cm</td>
      <td>Nieuwe factor <?php echo showHelpButton("Als dit veld een waarde bevat, betekent dit dat er al een factor bekend is welke geldig is vanaf deze datum. Je kunt deze factor hier wijzigen.","Nieuwe factor") ?></td>
      <td>Huidige factor <?php echo showHelpButton("Dit is factor welk op dit moment gebruikt word voor nieuwe offertes.","Huidige factor") ?></td>
    </tr>
    <?php
      /** @var MitrePrices $item */
      foreach($mitres as $item): ?>
        <tr class="dataTableRow trhover">
          <td><?php echo $item->stoneLength ?></td>
          <td><?php $form->getElement("factor_".$item->stoneLength)->render() ?></td>
          <td ><?php echo $item->factor ?></td>
        </tr>
      <?php endforeach; ?>
  </table>

  <br/>
  <input type="submit" name="go" value="Opslaan" />
  <input type="submit" name="go_list" value="Opslaan en naar lijst" />

</form>
<script type="text/javascript">
  $(document).ready(function () {
    $(".price").focus(function() {
      $(this).select();
    });


    $(".price").change( function() {
      if($(this).val()!="") {
        var val = $(this).val();
        //console.log(val);
        val = decimalNL(val,2);
        $(this).val(val);
      }
    });

  });

</script>
<style>
  input.price,input.buyprice, input.factor, input.currentprice {
    width: 80px;
    text-align: right;
  }
</style>