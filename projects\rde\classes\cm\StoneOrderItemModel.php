<?php

  AppModel::loadBaseClass('BaseStoneOrderItem');

  class StoneOrderItemModel extends BaseStoneOrderItem {

    public static function getToOrder() {
      $query = "SELECT sum(size) as amount, stone_id FROM " . StoneOrderItem::getTablename() . " WHERE senddate IS NULL GROUP BY stone_id ";

      $result = DBConn::db_link()->query($query);
      $stonesOrdered = [];
      while ($row = $result->fetch_assoc()) {
        $stonesOrdered[$row["stone_id"]] = $row["amount"];
      }
      return $stonesOrdered;
    }

    public static function getOrderedNotDelivered() {
      $query = "SELECT sum(size-receivedsize) as amount,stone_id FROM " . StoneOrderItem::getTablename() . " WHERE NOT senddate IS NULL AND receiveddate IS NULL GROUP BY stone_id ";

      $result = DBConn::db_link()->query($query);
      $stonesOrdered = [];
      while ($row = $result->fetch_assoc()) {
        $stonesOrdered[$row["stone_id"]] = $row["amount"];
      }
      return $stonesOrdered;
    }

    public function getSenddate($format = "d-m-Y H:i") {
      if ($this->senddate != "") {
        return date($format, strtotime($this->senddate));
      }
      return '';
    }

    public function getSupplierreadydate($format = "d-m-Y") {
      if ($this->supplierreadydate != "") {
        return date($format, strtotime($this->supplierreadydate));
      }
      return '';
    }

    public function getReceiveddate($format = "d-m-Y H:i") {
      if ($this->receiveddate != "") {
        return date($format, strtotime($this->receiveddate));
      }
      return '';
    }

    public function getRefname() {
      if ($this->name != "") return $this->name;
      if (!isset($this->quotations)) {
        $this->quotations = StoneOrderItemQuotation::getQuotationsByItem($this->id);
      }
      return Quotations::getNames($this->quotations);
    }

    public static function getWithStones($stone_order_id) {
      $query = "SELECT * FROM " . StoneOrderItem::getTablename() . " ";
      $query .= "JOIN " . Stones::getTablename() . " ON stones.stoneId = stone_order_item.stone_id ";
      $query .= "WHERE stone_order_id=" . $stone_order_id . " ";
      $query .= "ORDER BY stones.name";

      $result = DBConn::db_link()->query($query);
      $sois = [];
      while ($row = $result->fetch_row()) {
        $soi = new StoneOrderItem();
        $soi->hydrate($row);
        $soi->from_db = true;

        $stone = new Stones();
        $stone->hydrate($row, count(StoneOrderItem::columns));
        $stone->from_db = true;
        $soi->stone = $stone;
        $sois[$soi->id] = $soi;
      }

      return $sois;
    }

    /**
     * Zijn alle stenen voor een order ontvangen? Zet dan dit op de quotation
     * @param $quotationIds
     */
    public static function checkReceivedQuotations($quotationIds) {
      if (count($quotationIds) > 0) {
        $received = [];
        foreach (StoneOrderItemQuotation::find_all_by(["quotation_id" => $quotationIds]) as $soiq) {
          $soi = StoneOrderItem::find_by_id($soiq->stone_order_item_id);
          if (isset($received[$soiq->quotation_id]) && $received[$soiq->quotation_id] == false) continue; //hij is nog niet helemaal received
          if ($soi->receiveddate != "") {
            $received[$soiq->quotation_id] = true;
          }
          else {
            $received[$soiq->quotation_id] = false;
          }
        }

        foreach ($received as $quotId => $value) {
          if ($value === true) {
            QuotationsExtra::manualStoneDeliverydate($quotId);
          }
        }
      }
    }

    /**
     * @param string $brandId
     * @return StoneOrderItem[]
     */
    public static function getStonesToRecieve($brandId = "") {
      $query = "WHERE NOT senddate IS NULL AND receiveddate IS NULL ";
      $stoneorderitems = StoneOrderItem::find_all($query);

      foreach ($stoneorderitems as $k => $item) {
        $item->quotations = StoneOrderItemQuotation::getQuotationsByItem($item->id);
        $item->quotationNames = Quotations::getNames($item->quotations);
        $item->stone = Stones::find_by(["stoneId" => $item->stone_id]);

        if ($brandId != "" && $item->stone->brandId != $brandId) {
          unset($stoneorderitems[$k]);
        }

      }

      usort($stoneorderitems, function ($a, $b) {
        return strcmp($a->stone->name, $b->stone->name);
      });

      //even opnieuw mappen na sort
      return AppModel::mapObjectIds($stoneorderitems);
    }


  }