<?php

  AppModel::loadBaseClass('BaseStones');

  class StonesModel extends BaseStones {

    const TYPE_RAAMDORPEL = "raamdorpel";
    const TYPE_MUURAFDEKKER = "muurafdekker";
    const TYPE_SPEKBAND = "spekband";
    const TYPE_VENSTERBANK = "vensterbank";
    const TYPE_BALKJES = "balkjes";
    const TYPES = [
      Stones::TYPE_RAAMDORPEL   => "Raamdorpel",
      Stones::TYPE_MUURAFDEKKER => "Muurafdekker",
      Stones::TYPE_SPEKBAND     => "Spekband",
      Stones::TYPE_VENSTERBANK  => "Vensterbank",
      Stones::TYPE_BALKJES      => "Balkjes",
    ];

    const MATERIAL_KERAMIEK = "keramiek";
    const MATERIAL_BETON = "beton";
    const MATERIAL_NATUURSTEEN = "natuursteen";
    const MATERIAL_ISOSILL = "isosill";
    const MATERIALS = [
      Stones::MATERIAL_KERAMIEK    => "Keramiek",
      Stones::MATERIAL_BETON       => "Beton",
      Stones::MATERIAL_NATUURSTEEN => "Natuursteen",
      Stones::MATERIAL_ISOSILL     => "IsoSill",
    ];

    const STEENOPSLAGGROEPEN = [
      "A", "B", "C", "D",
    ];

    const ENDSTONE_FALSE = "false";
    const ENDSTONE_LEFT = "left";
    const ENDSTONE_RIGHT = "right";
    const ENDSTONE_LEFTG = "leftg";
    const ENDSTONE_RIGHTG = "rightg";
    const ENDSTONE_FLAT = "flat";
    const ENDSTONE_STANDINGSIDE = "standingside";
    const ENDSTONE_STUC = "stuc";

    const ENDSTONES = [
      self::ENDSTONE_FALSE  => "Nee",
      self::ENDSTONE_LEFT   => "Links",
      self::ENDSTONE_RIGHT  => "Rechts",
      self::ENDSTONE_LEFTG  => "Links groef",
      self::ENDSTONE_RIGHTG => "Rechts groef",
      self::ENDSTONE_FLAT   => "Vlak",
      self::ENDSTONE_STANDINGSIDE => "Opstaande zijkant",
      self::ENDSTONE_STUC   => "Stucprofiel",
    ];

    /**
     * get sibelings
     * @param $stones
     * @return mixed
     */
    public static function addSibelings($stones) {
      foreach ($stones as $stone) {
        $others = Stones::find_all_by(['brandId' => $stone->brandId, 'colorId' => $stone->colorId, 'sizeId' => $stone->sizeId], "AND stoneId!=" . $stone->stoneId);
        foreach ($others as $other) {
          $other->toOrder = 0;
          $stone->sibelings[$other->endstone] = $other;
//        $stone->totals["stock"] += $other->stock;
//        $stone->totals["minAmountStones"] += $other->minAmountStones;
        }

      }
      return $stones;
    }

    /**
     * @return Stones []
     */
    public function getSibelings() {
      return AppModel::mapObjectIds(Stones::find_all_by(['brandId' => $this->brandId, 'colorId' => $this->colorId, 'sizeId' => $this->sizeId], "AND stoneId!=" . $this->stoneId), "endstone");
    }

    /**
     * @param Stones|boolean $stone1
     * @param Stones|boolean $stone2
     * @return bool
     */
    public static function areSiblings($stone1, $stone2) {
      if ($stone1 === false || $stone2 === false) return false;

      if ($stone1->brandId == $stone2->brandId && $stone1->colorId == $stone2->colorId && $stone1->sizeId == $stone2->sizeId) {
        return true;
      }
      return false;
    }

    /**
     * Voorraad aanpassen naar
     * @param $size
     */
    public function changeStock($stock, $save = true) {
      if ($stock != $this->stock) {
        logToFile("stock", "Voorraad aangepast: " . $this->stoneId . " van " . $this->stock . " naar " . $stock);
        $this->stock = $stock;
        if ($save) {
          $this->save();
        }
      }
    }

    /**
     * Voorraad ophogen met
     * @param $size
     */
    public function addStock($size) {
      if ($size != 0) {
        logToFile("stock", "Voorraad aangepast: " . $this->stoneId . " van " . $this->stock . " naar " . ($this->stock + $size));
        $this->stock += $size;
        $this->save();
      }
    }

    public static function getStonesToOrder($pager = false, $search = '', $brandid = '', $shortage = true, $hasorder = false) {
      $stones = [];
      $order_item_quotations = [];
      $stoneIds = [];

      $query = "SELECT * FROM " . Stones::getTablename() . " ";
      $filt = " JOIN " . StoneColors::getTablename() . " ON stones.colorId=stone_colors.colorId ";
      $filt .= " WHERE stones.endstone='false' ";
      if ($search) {
        $search = escapeForDB($search);
        $filt .= " AND (";
        $filt .= "stone_colors.short LIKE '%" . $search . "%' OR ";
        $filt .= "stone_colors.name LIKE '%" . $search . "%' OR ";
        $filt .= "stones.short LIKE '%" . $search . "%' OR ";
        $filt .= "stones.name LIKE '%" . $search . "%' ";
        $filt .= ")";
      }
      if ($brandid != "") {
        $filt .= " AND stone_colors.brandId = " . $brandid . " ";
      }

      if ($pager) {
        $count = "SELECT COUNT(stones.stoneId) AS count FROM " . Stones::getTablename() . " " . $filt;
        $result = DBConn::db_link()->query($count);
        $row = $result->fetch_assoc();
        if ($row) {
          $pager->count = $row['count'];
        }
      }
      $filt .= 'ORDER BY stones.name';
      if ($pager) {
        $filt .= $pager->getLimitQuery();
      }
      $result = DBConn::db_link()->query($query . $filt);
      $alreadyOrdered = StoneOrderItem::getOrderedNotDelivered();
      $onOrderlist = StoneOrderItem::getToOrder();

      while ($row = $result->fetch_row()) {
        $stone = new Stones();
        $stone->hydrate($row);
        $stone->from_db = true;
        $color = new StoneColors();
        $color->hydrate($row, count(Stones::columns));
        $color->from_db = true;
        $stone->color = $color;
        $stone->alreadyOrdered = 0;
        if (isset($alreadyOrdered[$stone->stoneId])) {
          $stone->alreadyOrdered = $alreadyOrdered[$stone->stoneId];
        }
        $stone->onOrderlist = 0;
        if (isset($onOrderlist[$stone->stoneId])) {
          $stone->onOrderlist = $onOrderlist[$stone->stoneId];
        }

        $stone->totals = [];
        $stone->totals["totalMiddlesStones"] = 0;
        $stone->totals["totalLeftEndStones"] = 0;
        $stone->totals["totalRightEndStones"] = 0;
        $stone->totals["totalEndStonesGrooves"] = 0;
        $stone->totals["sumorder"] = 0;
        $stone->totals["sum"] = 0;
        $stone->totals["stock"] = $stone->stock; //in addsibbelings children stock is added
        $stone->totals["minAmountStones"] = $stone->minAmountStones; //in addsibbelings children stock is added
        $stone->totals["alreadyOrdered"] = $stone->alreadyOrdered; //in addsibbelings children stock is added
        $stone->orders = [];
        $stone->toOrder = 0;
        $stone->sibelings = [];

        $stones[] = $stone;
        $stoneIds[] = $stone->stoneId;
      }


      //ophalen de te producteren orders
      if (count($stoneIds) > 0) {

        $stones = AppModel::mapObjectIds(Stones::addSibelings($stones), 'stoneId');
        foreach ($stones as $stone) {
          foreach ($stone->sibelings as $sib) {
            $sib->alreadyOrdered = 0;
            if (isset($alreadyOrdered[$sib->stoneId])) {
              $sib->alreadyOrdered = $alreadyOrdered[$sib->stoneId];
              $stone->totals["alreadyOrdered"] += $sib->alreadyOrdered;
            }
            $sib->onOrderlist = 0;
            if (isset($onOrderlist[$sib->stoneId])) {
              $sib->onOrderlist = $onOrderlist[$sib->stoneId];
              $stone->totals["alreadyOrdered"] += $sib->onOrderlist;
            }
          }
        }

        $query = "SELECT quotations.stoneId, quotations.quotationId, quotations.quotationNumber,quotations.quotationPart,quotations.quotationVersion,`totalLeftEndStones`, `totalRightEndStones`, `totalLeftEndStonesGrooves`, `totalRightEndStonesGrooves`, `totalMiddlesStones` FROM " . Quotations::getTablename() . "  ";
        $query .= "JOIN " . QuotationsExtra::getTablename() . " ON quotations_extra.quotationId = quotations.quotationId ";
        $query .= "WHERE statusId>=20 AND statusId<40 ";
        $query .= "AND stoneId IN (" . implode(",", $stoneIds) . ") ";

        $result = DBConn::db_link()->query($query);

        $quotationIds = [];
        while ($row = $result->fetch_assoc()) {
          $vals = [];
          $name = $row['quotationNumber'] . '-' . $row['quotationVersion'];
          if ($row['quotationPart'] != "") {
            $name .= '-' . RdeHelper::asciiIntToAlpha($row['quotationPart']);
          }
          $vals['quotationId'] = $row['quotationId'];
          $quotationIds[$vals['quotationId']] = $vals['quotationId'];
          $vals['name'] = $name;
          $vals['stone_id'] = $row['stoneId'];

          $cstone = $stones[$row['stoneId']];

          $vals['totalMiddlesStones'] = $row["totalMiddlesStones"];
          $cstone->totals["totalMiddlesStones"] += $row["totalMiddlesStones"];
          $vals['totalLeftEndStones'] = $row["totalLeftEndStones"];
          $cstone->totals["totalLeftEndStones"] += $row["totalLeftEndStones"];
          $vals['totalRightEndStones'] = $row["totalRightEndStones"];
          $cstone->totals["totalRightEndStones"] += $row["totalRightEndStones"];
          $vals['totalEndStonesGrooves'] = $row["totalLeftEndStonesGrooves"] + $row["totalRightEndStonesGrooves"];
          $cstone->totals["totalEndStonesGrooves"] += $row["totalLeftEndStonesGrooves"] + $row["totalRightEndStonesGrooves"];
          $vals['sum'] = $row["totalMiddlesStones"] + $row["totalLeftEndStones"] + $row["totalRightEndStones"] + $row["totalLeftEndStonesGrooves"] + $row["totalRightEndStonesGrooves"];

          $cstone->totals["sumorder"] += $vals['sum'];

          //te bestellen
          $cstone->toOrder += $row["totalMiddlesStones"];

          if (isset($cstone->sibelings[Stones::ENDSTONE_LEFT])) {
            $cstone->sibelings[Stones::ENDSTONE_LEFT]->toOrder += $row["totalLeftEndStones"];
          }
          if (isset($cstone->sibelings[Stones::ENDSTONE_RIGHT])) {
            $cstone->sibelings[Stones::ENDSTONE_RIGHT]->toOrder += $row["totalRightEndStones"];
          }
          if (isset($cstone->sibelings[Stones::ENDSTONE_LEFTG])) { //rightg word genegeerd, we werken altijd met leftg
            $cstone->sibelings[Stones::ENDSTONE_LEFTG]->toOrder += $row["totalLeftEndStonesGrooves"] + $row["totalRightEndStonesGrooves"];
          }

          $cstone->orders[] = $vals;

        }


        foreach ($stones as $k => $stone) {
          $stone->hasShortage = false;
          if ($stone->toOrder > 0) {
            $ordersToOrder = $stone->toOrder;
            $stone->toOrder = $stone->toOrder - $stone->stock - $stone->alreadyOrdered - $stone->onOrderlist;
            if ($stone->toOrder > 0) {
              $stone->hasShortage = true;
            }
            elseif ($ordersToOrder > 0 && $stone->stock + $stone->alreadyOrdered + $stone->onOrderlist - $ordersToOrder < $stone->minAmountStones) {
              //voorraad marge regeling
              $stone->hasShortage = true;
            }
          }
          if ($stone->toOrder > 0) {
            $stone->totals["sum"] += $stone->toOrder;
          }

          foreach ($stone->sibelings as $endstonekey => $child) {
            if ($child->toOrder > 0) {
              $ordersToOrder = $child->toOrder;
              $child->toOrder = $child->toOrder - $child->stock - $child->alreadyOrdered - $child->onOrderlist;
              if ($child->toOrder > 0) {
                $stone->hasShortage = true;
              }
              elseif ($ordersToOrder > 0 && $child->stock + $child->alreadyOrdered + $child->onOrderlist - $ordersToOrder < $child->minAmountStones) {
                //voorraad marge regeling
                $stone->hasShortage = true;
              }
            }
            if ($child->toOrder > 0) {
              $stone->totals["sum"] += $child->toOrder;
            }
          }

        }
        if (count($quotationIds) > 0) {
          foreach (StoneOrderItemQuotation::find_all_by(["quotation_id" => $quotationIds]) as $soiq) {
            $order_item_quotations[$soiq->quotation_id] = $soiq->quotation_id;
          }
        }
      }

      //zijn alle bestellingen verwerkt?
      foreach ($stones as $k => $stone) {
        //alleen shortage? Verwijder anderen
        if ($hasorder && count($stone->orders) == 0) {
          unset($stones[$k]);
        }
        elseif ($shortage) {
          if (!$stone->hasShortage) {
            unset($stones[$k]);
          }
          else {
            $allOrdered = true;
            foreach ($stone->orders as $order) {
              if (!isset($order_item_quotations[$order["quotationId"]])) {
                $allOrdered = false;
                break;
              }
            }
            if ($allOrdered) {
              unset($stones[$k]);
            }
          }
        }
      }

      return [
        'stones'                => $stones,
        'order_item_quotations' => $order_item_quotations,
      ];

    }

    /**
     * Alle stenen ophalen welke zichtbaar zijn. Groeven rechts niet, is hetzelfde als links
     * @return Stones[]
     */
    public static function getDisplayStones() {
      return Stones::find_all_by(["display" => "true", "type" => ["raamdorpel", "spekband"]], "AND endstone!='rightg'");
    }

    /**
     * @param $material
     * @return []
     */
    public static function getStoneIdsByMaterial($material, $key = 'stoneId') {
      return ArrayHelper::getValuesOfNestedObjectsAsArray(Stones::find_all_by(["material" => $material]), $key);
    }

    public static function getStoneIdsByType($type, $key = 'stoneId') {
      return ArrayHelper::getValuesOfNestedObjectsAsArray(Stones::find_all_by(["type" => $type]), $key);
    }

    /**
     * Haalt alle stoneIds op van de stenen met dezelfde sizeId
     * @param $sizeId
     * @return []
     */
    public static function getStonesBySize($sizeId) {
      $related_stones = Stones::find_all_by(['sizeId' => $sizeId]);

      return $related_stones;
    }

    /**
     * @param $prices []
     * @param StoneSizes $stone_size
     * @param bool $ceil
     * @return false|float|int
     */
    public function getMeterprice($prices, $stone_size = false, $ceil = false) {
      if ($stone_size === false) {
        $stone_size = StoneSizes::find_by(["sizeId" => $this->sizeId]);
      }
      $dummyquotation = new Quotations();
      $dummyquotation->stoneId = $this->stoneId;
      $dummyquotation->stoneCategoryId = $this->category_id;
      $dummyquotation->endstone = "false";
      $dummyquotation->brandId = $this->brandId;

      $dummyquotation_e = new QuotationsExtra();
      $dummyquotation_e->wall_thickness = 0;

      $element = new OrderElements();
      $element->leftEndstone = 0;
      $element->leftEndstoneGrooves = 0;
      $element->inputLength = 10000;
      $element->calculateValues($dummyquotation, $dummyquotation_e, $stone_size, $this);
      $element->calculatePrice($dummyquotation, $prices, $stone_size, $this);

      if ($ceil) {
        return ceil($element->elementPrice / 10);
      }
      return $element->elementPrice / 10;
    }

    public function getPricefactor() {
      $c = Config::get("PRICE_FACTORS");
      if (isset($c[$this->type])) {
        if ($this->endstone == "true" && isset($c[$this->type]["endstone"])) {
          return $c[$this->type]["endstone"];
        }
        if (isset($c[$this->type]["default"])) {
          return $c[$this->type]["default"];
        }
      }
      return 1.0;
    }

    /**
     * @return string
     */
    public function getType() {
      return $this->type;
    }

    /**
     * @return boolean
     */
    public function isRaamdorpel() {
      return $this->type == Stones::TYPE_RAAMDORPEL;
    }

    /**
     * @return boolean
     */
    public function isMuurafdekker() {
      return $this->type == Stones::TYPE_MUURAFDEKKER;
    }

    /**
     * @return boolean
     */
    public function isSpekband() {
      return $this->type == Stones::TYPE_SPEKBAND;
    }

    /**
     * @return boolean
     */
    public function isVensterbank() {
      return $this->type == Stones::TYPE_VENSTERBANK;
    }

    /**
     * @return boolean
     */
    public function isBalkje() {
      return $this->type == Stones::TYPE_BALKJES;
    }

    /**
     * @return string
     */
    public function getMaterial() {
      return $this->material;
    }

    /**
     * @return boolean
     */
    public function isKeramiek() {
      return $this->material == Stones::MATERIAL_KERAMIEK;
    }

    /**
     * @return boolean
     */
    public function isBeton() {
      return $this->material == Stones::MATERIAL_BETON;
    }

    /**
     * @return boolean
     */
    public function isNatuursteen() {
      return $this->material == Stones::MATERIAL_NATUURSTEEN;
    }

    /**
     * @return boolean
     */
    public function isIsosill() {
      return $this->material == Stones::MATERIAL_ISOSILL;
    }

    /**
     * Extra element spacing in mm
     */
    public function getExtraSpacing() {
      if ($this->isBeton()) {
        return 4;
      }
      return 2;
    }

    public function removeImageFile() {
      if ($this->image != "") {
        if (file_exists(DIR_ROOT_HTTPDOCS . '/images/thresholds/' . $this->image)) {
          unlink(DIR_ROOT_HTTPDOCS . '/images/thresholds/' . $this->image);
        }
        $this->image = null;
      }
    }

    public function removePdfFile() {
      if ($this->pdfLocation != "") {
        if (file_exists(DIR_ROOT_HTTPDOCS . $this->pdfLocation)) {
          unlink(DIR_ROOT_HTTPDOCS . $this->pdfLocation);
        }
        $this->pdfLocation = null;
      }
    }

    public function destroy() {
      $this->removeImageFile();
      $this->removePdfFile();
      parent::destroy();
    }

  }