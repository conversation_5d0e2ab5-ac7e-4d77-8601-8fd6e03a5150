<?php
class BaseVgm extends AppModel
{
  const DB_NAME = 'rde_b1mo';
  const TABLE_NAME = 'vgm';
  const OM_CLASS_NAME = 'Vgm';
  const columns = ['id', 'employee_id', 'customer_user_id', 'customer_name', 'quotation_id', 'partner', 'date', 'employees', 'location', 'job', 'improvement'];
  const field_structure = [
    'id'                          => ['type' => 'mediumint', 'length' => '8', 'null' => false],
    'employee_id'                 => ['type' => 'mediumint', 'length' => '8', 'null' => false],
    'customer_user_id'            => ['type' => 'int', 'length' => '11', 'null' => false],
    'customer_name'               => ['type' => 'varchar', 'length' => '150', 'null' => true],
    'quotation_id'                => ['type' => 'int', 'length' => '11', 'null' => false],
    'partner'                     => ['type' => 'varchar', 'length' => '255', 'null' => true],
    'date'                        => ['type' => 'datetime', 'length' => '', 'null' => false],
    'employees'                   => ['type' => 'text', 'length' => '', 'null' => true],
    'location'                    => ['type' => 'varchar', 'length' => '255', 'null' => true],
    'job'                         => ['type' => 'varchar', 'length' => '255', 'null' => true],
    'improvement'                 => ['type' => 'boolean', 'length' => '1', 'null' => false],
  ];

  protected static $primary_key = ['id'];
  protected $auto_increment = 'id';

  public $id, $employee_id, $customer_user_id, $customer_name, $quotation_id, $partner, $date, $employees, $location, $job, $improvement;

  function __construct($obj_arr = [], &$error_codes = []) {
    parent::__construct();
    $this->setDefaults();
    if (!empty($obj_arr)) {
      $this->from_array($obj_arr);
      $this->valid_required_fields($error_codes);
    }
  }

  public function valid(&$error_codes = []) {
    $errors = [];
    foreach(self::columns as $column) {
      if ($column != $this->auto_increment)
      {
        call_user_func_array([$this, 'v_' . $column], [&$errors]);
      }
    }
    if(empty($errors)) {
      return true;
    }
    $error_codes = array_merge($error_codes, $errors);
    return false;
  }

  public function valid_required_fields(&$error_codes = []) {
    $errors = [];
    foreach (self::columns as $column) {
      $function_name = "v_$column";
      if ($column!=$this->auto_increment) {
        call_user_func_array([$this, $function_name], [&$errors]);
      }
    }
    if (empty($errors)) {
      return true;
    }
    $error_codes = array_merge($error_codes, $errors);
    return false;
  }

  public function setDefaults() {
    $this->improvement = 1;
  }



  public function v_id(&$error_codes = []) {
    if (!is_null($this->id) && strlen($this->id) > 0 && self::valid_mediumint($this->id, '8')) {
      return true;
    }
    $error_codes[] = 'id';
    return false;
  }

  public function v_employee_id(&$error_codes = []) {
    if (!is_null($this->employee_id) && strlen($this->employee_id) > 0 && self::valid_mediumint($this->employee_id, '8')) {
      return true;
    }
    $error_codes[] = 'employee_id';
    return false;
  }

  public function v_customer_user_id(&$error_codes = []) {
    if (!is_null($this->customer_user_id) && strlen($this->customer_user_id) > 0 && self::valid_int($this->customer_user_id, '11')) {
      return true;
    }
    $error_codes[] = 'customer_user_id';
    return false;
  }

  public function v_customer_name(&$error_codes = []) {
    if (is_null($this->customer_name) || strlen($this->customer_name) == 0 || self::valid_varchar($this->customer_name, '150')) {
      return true;
    }
    $error_codes[] = 'customer_name';
    return false;
  }

  public function v_quotation_id(&$error_codes = []) {
    if (!is_null($this->quotation_id) && strlen($this->quotation_id) > 0 && self::valid_int($this->quotation_id, '11')) {
      return true;
    }
    $error_codes[] = 'quotation_id';
    return false;
  }

  public function v_partner(&$error_codes = []) {
    if (is_null($this->partner) || strlen($this->partner) == 0 || self::valid_varchar($this->partner, '255')) {
      return true;
    }
    $error_codes[] = 'partner';
    return false;
  }

  public function v_date(&$error_codes = []) {
    if (!is_null($this->date) && strlen($this->date) > 0 && self::valid_datetime($this->date)) {
      return true;
    }
    $error_codes[] = 'date';
    return false;
  }

  public function v_employees(&$error_codes = []) {
    if (is_null($this->employees) || strlen($this->employees) == 0 || self::valid_text($this->employees)) {
      return true;
    }
    $error_codes[] = 'employees';
    return false;
  }

  public function v_location(&$error_codes = []) {
    if (is_null($this->location) || strlen($this->location) == 0 || self::valid_varchar($this->location, '255')) {
      return true;
    }
    $error_codes[] = 'location';
    return false;
  }

  public function v_job(&$error_codes = []) {
    if (is_null($this->job) || strlen($this->job) == 0 || self::valid_varchar($this->job, '255')) {
      return true;
    }
    $error_codes[] = 'job';
    return false;
  }

  public function v_improvement(&$error_codes = []) {
    if (!is_null($this->improvement) && strlen($this->improvement) > 0 && self::valid_tinyint($this->improvement, '1')) {
      return true;
    }
    $error_codes[] = 'improvement';
    return false;
  }

  /**
  *@param array $conditions (required)
  *@param String $raw_sql (optional)
  *@return Vgm[]
  **/
  public static function find_all_like($conditions, $raw_sql = '') { return parent::generic_find_all_like($conditions, static::OM_CLASS_NAME, $raw_sql); }
  /**
  *@param array $conditions (required)
  *@param String $raw_sql (optional)
  *@return Vgm[]
  **/
  public static function find_all_by($conditions, $raw_sql = '') { return parent::generic_find_all_by($conditions, static::OM_CLASS_NAME, $raw_sql); }
  /**
  *@param String $raw_sql (optional)
  *@return Vgm[]
  **/
  public static function find_all($raw_sql = '') { return parent::generic_find_all(static::OM_CLASS_NAME, $raw_sql); }
  /**
  *@param array $conditions (required)
  *@param String $raw_sql (optional)
  *@return Vgm
  **/
  public static function find_by($conditions, $raw_sql = '') { return parent::generic_find_by($conditions, static::OM_CLASS_NAME, $raw_sql); }
  /**
  *@param String $id (required)
  *@param String $raw_sql (optional)
  *@return Vgm
  **/
  public static function find_by_id($id, $raw_sql = '') { return parent::generic_find_by_id($id, static::OM_CLASS_NAME, $raw_sql); }
  /**
  *@param array $conditions (required)
  *@param String $raw_sql (optional)
  *@return int
  **/
  public static function count_all_by($conditions, $raw_sql = '') { return parent::generic_count_all_by($conditions, static::OM_CLASS_NAME, $raw_sql); }
  /**
  *@param array $conditions (required)
  *@param String $raw_sql (optional)
  *@return bool
  **/
  public static function delete_by($conditions, $raw_sql = '') { return parent::generic_delete_by($conditions, static::OM_CLASS_NAME, $raw_sql); }
}