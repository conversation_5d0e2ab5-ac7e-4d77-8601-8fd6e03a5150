import { createApp, h } from 'vue';

import * as Sentry from "@sentry/vue";

import VueSweetalert2 from 'vue-sweetalert2';
import 'sweetalert2/dist/sweetalert2.min.css';

import ModuleNotFound from './components/ModuleNotFound.vue';

import SelectStone from './components/SelectStone.vue';
import Material from './components/Material.vue';
import Elements from './components/Elements.vue';
import ElementsWindowsill from './components/ElementsWindowsill.vue';
import ElementsBeam from './components/ElementsBeam.vue';
import Mitres from './components/Mitres.vue';
import DeliveryAddress from "./components/DeliveryAddress";
import DevelopmentHelper from "./helpers/DevelopmentHelper";

const steps = {
  'step0': Material,
  'step1': SelectStone,
  'step2': Elements,
  'step2Windowsill': ElementsWindowsill,
  'step2Beam': ElementsBeam,
  'step3': Mitres,
  'step5': DeliveryAddress,
}

const currentStep = document.getElementById('app').dataset.step;

const StepRouter = {
  data: () => ({
    step: currentStep,
  }),
  computed: {
    CurrentComponent() {
      if(DevelopmentHelper.isDevelopment()) console.log(steps[this.step].name);
      return steps[this.step] || ModuleNotFound
    }
  },
  render() {
    return h(this.CurrentComponent)
  }
}


const app = createApp(StepRouter);
app.use(VueSweetalert2);

Sentry.init({
  app,
  dsn: "https://<EMAIL>/4508438545498192",
  enabled: process.env.NODE_ENV === "production",
  integrations: [
  ],
});

app.mount('#app');

