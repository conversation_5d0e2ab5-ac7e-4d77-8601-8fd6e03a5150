<?php
class BaseWindowsill extends AppModel
{
  const DB_NAME = 'rde_b1mo';
  const TABLE_NAME = 'windowsill';
  const OM_CLASS_NAME = 'Windowsill';
  const columns = ['id', 'name', 'online', 'x1', 'x2', 'x3', 'x4', 'x5', 'x6', 'imagefilename', 'imagesmallfilename', 'mitre_factor', 'sort'];
  const field_structure = [
    'id'                          => ['type' => 'mediumint', 'length' => '8', 'null' => false],
    'name'                        => ['type' => 'varchar', 'length' => '255', 'null' => false],
    'online'                      => ['type' => 'boolean', 'length' => '1', 'null' => false],
    'x1'                          => ['type' => 'varchar', 'length' => '100', 'null' => true],
    'x2'                          => ['type' => 'varchar', 'length' => '100', 'null' => true],
    'x3'                          => ['type' => 'varchar', 'length' => '100', 'null' => true],
    'x4'                          => ['type' => 'varchar', 'length' => '100', 'null' => true],
    'x5'                          => ['type' => 'varchar', 'length' => '100', 'null' => true],
    'x6'                          => ['type' => 'varchar', 'length' => '100', 'null' => true],
    'imagefilename'               => ['type' => 'varchar', 'length' => '255', 'null' => true],
    'imagesmallfilename'          => ['type' => 'varchar', 'length' => '255', 'null' => true],
    'mitre_factor'                => ['type' => 'decimal', 'length' => '3,1', 'null' => false],
    'sort'                        => ['type' => 'smallint', 'length' => '6', 'null' => false],
  ];

  protected static $primary_key = ['id'];
  protected $auto_increment = 'id';

  public $id, $name, $online, $x1, $x2, $x3, $x4, $x5, $x6, $imagefilename, $imagesmallfilename, $mitre_factor, $sort;

  function __construct($obj_arr = [], &$error_codes = []) {
    parent::__construct();
    $this->setDefaults();
    if (!empty($obj_arr)) {
      $this->from_array($obj_arr);
      $this->valid_required_fields($error_codes);
    }
  }

  /**
  * Set default object values
  **/
  public function setDefaults() {
    $this->online = 1;
    $this->mitre_factor = 0.0;
    $this->sort = 0;
  }

  /**
   * @param array $conditions (required)
   * @param string $raw_sql (optional)
   * @return Windowsill[]
   **/
  public static function find_all_like($conditions, $raw_sql = '') {
    return parent::generic_find_all_like($conditions, static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param array $conditions (required)
   * @param string $raw_sql (optional)
   * @return Windowsill[]
   **/
  public static function find_all_by($conditions, $raw_sql = '') {
    return parent::generic_find_all_by($conditions, static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param string $raw_sql (optional)
   * @return Windowsill[]
   **/
  public static function find_all($raw_sql = '') {
    return parent::generic_find_all(static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param array $conditions (required)
   * @param string $raw_sql (optional)
   * @return Windowsill
   **/
  public static function find_by($conditions, $raw_sql = '') {
    return parent::generic_find_by($conditions, static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param string $id (required)
   * @param string $raw_sql (optional)
   * @return Windowsill
   **/
  public static function find_by_id($id, $raw_sql = '') {
    return parent::generic_find_by_id($id, static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param array $conditions (required)
   * @param string $raw_sql (optional)
   * @return int
   **/
  public static function count_all_by($conditions, $raw_sql = '') {
    return parent::generic_count_all_by($conditions, static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param array $conditions (required)
   * @param string $raw_sql (optional)
   * @return bool
   **/
  public static function delete_by($conditions, $raw_sql = '') {
    return parent::generic_delete_by($conditions, static::OM_CLASS_NAME, $raw_sql);
  }

}