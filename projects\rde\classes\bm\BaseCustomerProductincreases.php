<?php
class BaseCustomerProductincreases extends AppModel
{
  const DB_NAME = 'rde_b1mo';
  const TABLE_NAME = 'customer_productincreases';
  const OM_CLASS_NAME = 'CustomerProductincreases';
  const columns = ['id', 'groupId', 'stoneIncrease', 'glueIncrease', 'joint', 'coldGlaze', 'spacers', 'glazeside', 'stoneIncreaseGroupA', 'stoneIncreaseGroupB', 'stoneIncreaseGroupC', 'stoneIncreaseGroupD', 'naturalstoneIncrease', 'naturalstoneChinaIncrease', 'concreteIncrease', 'isosillIncrease', 'validFrom', 'validTo'];
  const field_structure = [
    'id'                          => ['type' => 'int', 'length' => '4', 'null' => false],
    'groupId'                     => ['type' => 'tinyint', 'length' => '2', 'null' => false],
    'stoneIncrease'               => ['type' => 'decimal', 'length' => '5,2', 'null' => false],
    'glueIncrease'                => ['type' => 'decimal', 'length' => '5,2', 'null' => false],
    'joint'                       => ['type' => 'decimal', 'length' => '5,2', 'null' => false],
    'coldGlaze'                   => ['type' => 'decimal', 'length' => '5,2', 'null' => false],
    'spacers'                     => ['type' => 'decimal', 'length' => '5,2', 'null' => false],
    'glazeside'                   => ['type' => 'decimal', 'length' => '5,2', 'null' => false],
    'stoneIncreaseGroupA'         => ['type' => 'decimal', 'length' => '5,2', 'null' => false],
    'stoneIncreaseGroupB'         => ['type' => 'decimal', 'length' => '5,2', 'null' => false],
    'stoneIncreaseGroupC'         => ['type' => 'decimal', 'length' => '5,2', 'null' => false],
    'stoneIncreaseGroupD'         => ['type' => 'decimal', 'length' => '5,2', 'null' => false],
    'naturalstoneIncrease'        => ['type' => 'decimal', 'length' => '5,2', 'null' => false],
    'naturalstoneChinaIncrease'   => ['type' => 'decimal', 'length' => '5,2', 'null' => false],
    'concreteIncrease'            => ['type' => 'decimal', 'length' => '5,2', 'null' => false],
    'isosillIncrease'             => ['type' => 'decimal', 'length' => '5,2', 'null' => false],
    'validFrom'                   => ['type' => 'date', 'length' => '', 'null' => false],
    'validTo'                     => ['type' => 'date', 'length' => '', 'null' => false],
  ];

  protected static $primary_key = ['id'];
  protected $auto_increment = 'id';

  public $id, $groupId, $stoneIncrease, $glueIncrease, $joint, $coldGlaze, $spacers, $glazeside, $stoneIncreaseGroupA, $stoneIncreaseGroupB, $stoneIncreaseGroupC, $stoneIncreaseGroupD, $naturalstoneIncrease, $naturalstoneChinaIncrease, $concreteIncrease, $isosillIncrease, $validFrom, $validTo;

  function __construct($obj_arr = [], &$error_codes = []) {
    parent::__construct();
    $this->setDefaults();
    if (!empty($obj_arr)) {
      $this->from_array($obj_arr);
      $this->valid_required_fields($error_codes);
    }
  }

  /**
  * Set default object values
  **/
  public function setDefaults() {
    $this->glazeside = 0.00;
    $this->naturalstoneIncrease = 0.00;
    $this->naturalstoneChinaIncrease = 0.00;
    $this->concreteIncrease = 0.00;
    $this->isosillIncrease = 0.00;
    $this->validTo = '9999-12-31';
  }

  /**
   * @param array $conditions (required)
   * @param string $raw_sql (optional)
   * @return CustomerProductincreases[]
   **/
  public static function find_all_like($conditions, $raw_sql = '') {
    return parent::generic_find_all_like($conditions, static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param array $conditions (required)
   * @param string $raw_sql (optional)
   * @return CustomerProductincreases[]
   **/
  public static function find_all_by($conditions, $raw_sql = '') {
    return parent::generic_find_all_by($conditions, static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param string $raw_sql (optional)
   * @return CustomerProductincreases[]
   **/
  public static function find_all($raw_sql = '') {
    return parent::generic_find_all(static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param array $conditions (required)
   * @param string $raw_sql (optional)
   * @return CustomerProductincreases
   **/
  public static function find_by($conditions, $raw_sql = '') {
    return parent::generic_find_by($conditions, static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param string $id (required)
   * @param string $raw_sql (optional)
   * @return CustomerProductincreases
   **/
  public static function find_by_id($id, $raw_sql = '') {
    return parent::generic_find_by_id($id, static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param array $conditions (required)
   * @param string $raw_sql (optional)
   * @return int
   **/
  public static function count_all_by($conditions, $raw_sql = '') {
    return parent::generic_count_all_by($conditions, static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param array $conditions (required)
   * @param string $raw_sql (optional)
   * @return bool
   **/
  public static function delete_by($conditions, $raw_sql = '') {
    return parent::generic_delete_by($conditions, static::OM_CLASS_NAME, $raw_sql);
  }

}