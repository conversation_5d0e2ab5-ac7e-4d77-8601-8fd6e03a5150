<?php TemplateHelper::includePartial('_wizardheader.php', 'quotation', ['step' =>$step, 'quotation' =>$quotation, 'hasVerstek' =>$hasVerstek]); ?>

  <div id="app" data-step="<?php echo $stepname ?>"></div>

<?php if(ENVIRONMENT=="LOCAL"): ?>
  <link href="https://localhost:8081/wizard.css" rel="stylesheet">
  <script type="text/javascript" src="https://localhost:8081/wizard.js"></script>
<!--  --><?php //echo TemplateHelper::includeStylesheet(URL_PROJECT_FOLDER.'resources/vueapps/quotation/dist/wizard'); ?>
<!--  --><?php //echo TemplateHelper::includeJavascript(URL_PROJECT_FOLDER.'resources/vueapps/quotation/dist/wizard'); ?>
<?php else: ?>
  <?php echo TemplateHelper::includeStylesheet($site->getTemplateUrl().'dist-spa/wizard.min'); ?>
  <?php echo TemplateHelper::includeJavascript($site->getTemplateUrl().'dist-spa/wizard.min'); ?>
<?php endif;

