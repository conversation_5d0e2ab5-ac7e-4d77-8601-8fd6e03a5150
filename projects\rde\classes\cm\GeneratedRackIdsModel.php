<?php

  AppModel::loadBaseClass('BaseGeneratedRackIds');

  class GeneratedRackIdsModel extends BaseGeneratedRackIds {

    public static function getNewRackId() {
      $query = "SELECT MAX(rackId) as max_rack_id FROM " . GeneratedRackIds::getTablename() . " ";
      $result = DBConn::db_link()->query($query);
      if ($row = $result->fetch_assoc()) {
        return $row['max_rack_id'] += 1;
      }
      return 0;
    }

  }