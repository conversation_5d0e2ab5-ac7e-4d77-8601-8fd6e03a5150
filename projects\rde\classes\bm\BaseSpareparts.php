<?php
class BaseSpareparts extends AppModel
{
  const DB_NAME = 'rde_b1mo';
  const TABLE_NAME = 'spareparts';
  const OM_CLASS_NAME = 'Spareparts';
  const columns = ['partId', 'global', 'description', 'visible'];
  const field_structure = [
    'partId'                      => ['type' => 'int', 'length' => '2', 'null' => false],
    'global'                      => ['type' => 'varchar', 'length' => '20', 'null' => false],
    'description'                 => ['type' => 'varchar', 'length' => '50', 'null' => false],
    'visible'                     => ['type' => 'boolean', 'length' => '1', 'null' => false],
  ];

  protected static $primary_key = ['partId'];
  protected $auto_increment = 'partId';

  public $partId, $global, $description, $visible;

  function __construct($obj_arr = [], &$error_codes = []) {
    parent::__construct();
    $this->setDefaults();
    if (!empty($obj_arr)) {
      $this->from_array($obj_arr);
      $this->valid_required_fields($error_codes);
    }
  }

  public function valid(&$error_codes = []) {
    $errors = [];
    foreach(self::columns as $column) {
      if ($column != $this->auto_increment)
      {
        call_user_func_array([$this, 'v_' . $column], [&$errors]);
      }
    }
    if(empty($errors)) {
      return true;
    }
    $error_codes = array_merge($error_codes, $errors);
    return false;
  }

  public function valid_required_fields(&$error_codes = []) {
    $errors = [];
    foreach (self::columns as $column) {
      $function_name = "v_$column";
      if ($column!=$this->auto_increment) {
        call_user_func_array([$this, $function_name], [&$errors]);
      }
    }
    if (empty($errors)) {
      return true;
    }
    $error_codes = array_merge($error_codes, $errors);
    return false;
  }

  public function setDefaults() {
    $this->visible = 0;
  }



  public function v_partId(&$error_codes = []) {
    if (!is_null($this->partId) && strlen($this->partId) > 0 && self::valid_int($this->partId, '2')) {
      return true;
    }
    $error_codes[] = 'partId';
    return false;
  }

  public function v_global(&$error_codes = []) {
    if (!is_null($this->global) && strlen($this->global) > 0 && self::valid_varchar($this->global, '20')) {
      return true;
    }
    $error_codes[] = 'global';
    return false;
  }

  public function v_description(&$error_codes = []) {
    if (!is_null($this->description) && strlen($this->description) > 0 && self::valid_varchar($this->description, '50')) {
      return true;
    }
    $error_codes[] = 'description';
    return false;
  }

  public function v_visible(&$error_codes = []) {
    if (!is_null($this->visible) && strlen($this->visible) > 0 && self::valid_tinyint($this->visible, '1')) {
      return true;
    }
    $error_codes[] = 'visible';
    return false;
  }

  /**
  *@param array $conditions (required)
  *@param String $raw_sql (optional)
  *@return Spareparts[]
  **/
  public static function find_all_like($conditions, $raw_sql = '') { return parent::generic_find_all_like($conditions, static::OM_CLASS_NAME, $raw_sql); }
  /**
  *@param array $conditions (required)
  *@param String $raw_sql (optional)
  *@return Spareparts[]
  **/
  public static function find_all_by($conditions, $raw_sql = '') { return parent::generic_find_all_by($conditions, static::OM_CLASS_NAME, $raw_sql); }
  /**
  *@param String $raw_sql (optional)
  *@return Spareparts[]
  **/
  public static function find_all($raw_sql = '') { return parent::generic_find_all(static::OM_CLASS_NAME, $raw_sql); }
  /**
  *@param array $conditions (required)
  *@param String $raw_sql (optional)
  *@return Spareparts
  **/
  public static function find_by($conditions, $raw_sql = '') { return parent::generic_find_by($conditions, static::OM_CLASS_NAME, $raw_sql); }
  /**
  *@param String $id (required)
  *@param String $raw_sql (optional)
  *@return Spareparts
  **/
  public static function find_by_id($id, $raw_sql = '') { return parent::generic_find_by_id($id, static::OM_CLASS_NAME, $raw_sql); }
  /**
  *@param array $conditions (required)
  *@param String $raw_sql (optional)
  *@return int
  **/
  public static function count_all_by($conditions, $raw_sql = '') { return parent::generic_count_all_by($conditions, static::OM_CLASS_NAME, $raw_sql); }
  /**
  *@param array $conditions (required)
  *@param String $raw_sql (optional)
  *@return bool
  **/
  public static function delete_by($conditions, $raw_sql = '') { return parent::generic_delete_by($conditions, static::OM_CLASS_NAME, $raw_sql); }
}