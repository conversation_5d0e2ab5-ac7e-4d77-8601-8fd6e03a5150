<?php

  use domain\packs\service\BookShipment;
  use gsdfw\plugins\packs\domain\exception\PacksException;
  use gsdfw\plugins\packs\domain\service\PacksApi;

  /**
   * Trait apiShippingActions
   * Verzenden bestellingen
   */
  trait apiShippingActions {

    /**
     * Verwerkt bestelling
     * Vraag gegevens op via packs api
     */
    public function executeShipping() {

      $request_vars = $this->data->getData();
      logToFile('getzebrazpl', print_r($request_vars, true));

      //Pakket=P / Brievenbus=GB
      $labels_in = RestUtils::getPostvaluesByKey("json", $request_vars);

      if (false) { //test
        $lin = new stdClass();
        $lin->quotationIds = ['113275'];
        $lin->type = "Pakket";
//        $lin->type = "Brievenbus";
        $lin->weight = "20";
        $labels_in = [];
        $labels_in[] = $lin;
//        $labels_in[] = $lin;
      }

      $labels_out = []; //alleen voor packs levering
      if (!$labels_in || count($labels_in) == 0) {
        RestUtils::sendResponseError("Geen labels gekozen.");
      }

      //dit label gaat altijd naar 1 adres, ook al zitten er meer quotations aan gekoppeld.
      $quotations = Quotations::find_all_by(["quotationId" => $labels_in[0]->quotationIds]);
      if (count($quotations) == 0) {
        RestUtils::sendResponseError("Bestellingen niet gevonden.");
      }
      $quotation = $quotations[0];
      if ($quotation->country != "NL") {
        RestUtils::sendResponseError("Opvragen label buiten Nederland is niet mogelijk.");
      }

      $has_brievenbus = false;
      $has_other = false;
      foreach ($labels_in as $tel => $label_in) {
        if ($label_in->type == "Brievenbus") {
          $has_brievenbus = true;
        }
        else {
          $has_other = true;
        }
      }

      if ($has_brievenbus && $has_other) {
        RestUtils::sendResponseError("Het is niet mogelijk om pakket en brievenbuspost te combineren.");
      }

      if ($has_brievenbus) {
        //alleen brievenbus, geen packs labels
        $zpl_template = file_get_contents(DIR_PROJECT_FOLDER . 'templates/backend2/files/zebra_brievenbus_lbl.txt');

        $company = CrmCompanies::find_by(["companyId" => $quotation->companyId]);
        $sandbox_user = SandboxUsers::find_by(["userId" => $quotation->userId]);
        $crm_person = CrmPersons::find_by(["personId" => $sandbox_user->personId, "flagForDeletion" => 0]);

        $send_companyname = '';
        if ($company) {
          $send_companyname = $company->name;
        }
        $send_contactname = '';
        if ($crm_person) {
          $send_contactname = "T.a.v. " . $crm_person->getNaam();
        }
        $send_address = trim($quotation->street);
        $send_number = trim($quotation->nr . " " . $quotation->ext);
        $send_zip = $quotation->zipcode;
        $send_city = $quotation->domestic;
        $send_country = "Nederland";

        foreach ($labels_in as $tel => $label_in) {

          $zpl = $zpl_template;
          $zpl = str_replace("[*Sender1*]", "Afz. Raamdorpel.nl", $zpl);
          $zpl = str_replace("[*Sender2*]", "Raambrug 9, 5531 AG Bladel", $zpl);
          $zpl = str_replace("[*DeliveryName*]", $send_companyname, $zpl);
          $zpl = str_replace("[*DeliveryAttention*]", $send_contactname, $zpl);
          $zpl = str_replace("[*DeliveryStreetNumber*]", $send_address . ' ' . $send_number, $zpl);
          $zpl = str_replace("[*DeliveryZipPlace*]", $send_zip . ' ' . $send_city, $zpl);
          $zpl = str_replace("[*DeliveryCountry*]", $send_country, $zpl);

          $label_out = new stdClass();
          $label_out->zpl = $zpl;
          $labels_out[] = $label_out;
        }

      }
      else {

        //packs labels
        $allWeightsEqual = true;
        $prevWeight = false;
        foreach ($labels_in as $label_in) {
          if ($prevWeight === false) {
            $prevWeight = $label_in->weight;
          }
          elseif ($label_in->weight != $prevWeight) {
            //meerdere vrachten
            $allWeightsEqual = false;
          }
        }

        $packs_result = [];

        if (false) {
          //use this to download a registered packs again
          $packs_api = new PacksApi();
          $packs_result[] = $packs_api->downloadlabel("6021DA", "21085894");
          logToFile('getzebrazpl', print_r($packs_result, true));
        }
        else {
          try {
            if ($allWeightsEqual) {
              //alle gewichten hetzelfde, dus meerdere labels met collo
              $packs_api = new PacksApi();
              $book_shipment = new BookShipment($packs_api);
              $pres = $book_shipment->bookshipment($quotation, $labels_in[0]->type == "Brievenbus" ? "GB" : "P", $labels_in[0]->weight, count($labels_in));
              if ($pres === false) {
                RestUtils::sendResponseError("Packs zegel aanmaken mislukt. Onbekende oorzaak.");
              }
              $packs_result[] = $pres;
            }
            else {
              //verschilllende gewichten, dus losse verzendlabels.
              foreach ($labels_in as $tel => $label_in) {
                $packs_api = new PacksApi();
                $book_shipment = new BookShipment($packs_api);
                $pres = $book_shipment->bookshipment($quotation, $label_in->type == "Brievenbus" ? "GB" : "P", $label_in->weight);

                if ($pres === false) {
                  RestUtils::sendResponseError("Packs API error: onbekende oorzaak");
                }
                $packs_result[] = $pres;
              }
            }
          }
          catch (PacksException $e) {
            RestUtils::sendResponseError($e->getMessage());
          }
        }

        $zpl_template = file_get_contents(DIR_PROJECT_FOLDER . 'templates/backend2/files/zebra_packs_lbl.txt');

        foreach ($packs_result as $xmlstring) {
          $xml = simplexml_load_string($xmlstring);
          foreach ($xml as $packs_label) {
            //pd($packs_label);

            $zpl = $zpl_template;
            $zpl = str_replace("[*LoadName*]", "RAAMDORPEL.NL", $zpl);
            $zpl = str_replace("[*LoadAddress*]", "5531PP BLADEL", $zpl);
            $zpl = str_replace("[*LoadDepotCode*]", $packs_label->LoadDepotCode, $zpl);
            $zpl = str_replace("[*Collo*]", $packs_label->Multicolli, $zpl);
            $deliverydate = strftimesafe("%A %d %B %Y", strtotime($packs_label->DeliveryDate));
            $zpl = str_replace("[*DeliveryDate*]", $deliverydate, $zpl);
            $zpl = str_replace("[*DeliveryName*]", $packs_label->DeliveryName, $zpl);
            $zpl = str_replace("[*DeliveryAttention*]", $packs_label->DeliveryAttention, $zpl);
            $zpl = str_replace("[*DeliveryStreetNumber*]", $packs_label->DeliveryStreetNumber, $zpl);
            $zpl = str_replace("[*DeliveryZipPlace*]", $packs_label->DeliveryZip . ' ' . $packs_label->DeliveryPlace, $zpl);
            $zpl = str_replace("[*DeliveryCountry*]", $packs_label->DeliveryCountry, $zpl);
            $zpl = str_replace("[*Reference*]", $packs_label->Reference, $zpl);
            $zpl = str_replace("[*PackageFullName*]", $packs_label->PackageFullName, $zpl);
            $zpl = str_replace("[*Depot*]", $packs_label->Depot, $zpl);
            $zpl = str_replace("[*Rayon*]", $packs_label->Rayon, $zpl);
            $zpl = str_replace("123456789", $packs_label->Barcode, $zpl);
            $zpl = str_replace("[*BarcodeText*]", $packs_label->BarcodeText, $zpl);

            $memo_regels = ["", "", "", "", ""];
            if (isset($packs_label->Remarks)) {
              $vals = wordwrap($packs_label->Remarks, 60, "<>");
              $vals = explode("<>", $vals);
              foreach ($vals as $t => $val) {
                $memo_regels[$t] = $val;
                if ($t >= 4)
                  break;
              }
            }
            foreach ($memo_regels as $mt => $mr) {
              $zpl = str_replace("[*Memo" . ($mt + 1) . "*]", $mr, $zpl);
            }


            $label_out = new stdClass();
            $label_out->zpl = $zpl;
            $labels_out[] = $label_out;

          }

        }
      }

      RestUtils::sendResponseOK("Label retrieved", $labels_out);
    }

  }