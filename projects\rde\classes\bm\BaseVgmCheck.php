<?php
class BaseVgmCheck extends AppModel
{
  const DB_NAME = 'rde_b1mo';
  const TABLE_NAME = 'vgm_check';
  const OM_CLASS_NAME = 'VgmCheck';
  const columns = ['id', 'vgm_id', 'code', 'check', 'remark'];
  const field_structure = [
    'id'                          => ['type' => 'mediumint', 'length' => '8', 'null' => false],
    'vgm_id'                      => ['type' => 'mediumint', 'length' => '8', 'null' => false],
    'code'                        => ['type' => 'tinyint', 'length' => '2', 'null' => true],
    'check'                       => ['type' => 'boolean', 'length' => '1', 'null' => false],
    'remark'                      => ['type' => 'text', 'length' => '', 'null' => true],
  ];

  protected static $primary_key = ['id'];
  protected $auto_increment = 'id';

  public $id, $vgm_id, $code, $check, $remark;

  function __construct($obj_arr = [], &$error_codes = []) {
    parent::__construct();
    $this->setDefaults();
    if (!empty($obj_arr)) {
      $this->from_array($obj_arr);
      $this->valid_required_fields($error_codes);
    }
  }

  public function valid(&$error_codes = []) {
    $errors = [];
    foreach(self::columns as $column) {
      if ($column != $this->auto_increment)
      {
        call_user_func_array([$this, 'v_' . $column], [&$errors]);
      }
    }
    if(empty($errors)) {
      return true;
    }
    $error_codes = array_merge($error_codes, $errors);
    return false;
  }

  public function valid_required_fields(&$error_codes = []) {
    $errors = [];
    foreach (self::columns as $column) {
      $function_name = "v_$column";
      if ($column!=$this->auto_increment) {
        call_user_func_array([$this, $function_name], [&$errors]);
      }
    }
    if (empty($errors)) {
      return true;
    }
    $error_codes = array_merge($error_codes, $errors);
    return false;
  }

  public function setDefaults() {
    $this->check = 0;
  }



  public function v_id(&$error_codes = []) {
    if (!is_null($this->id) && strlen($this->id) > 0 && self::valid_mediumint($this->id, '8')) {
      return true;
    }
    $error_codes[] = 'id';
    return false;
  }

  public function v_vgm_id(&$error_codes = []) {
    if (!is_null($this->vgm_id) && strlen($this->vgm_id) > 0 && self::valid_mediumint($this->vgm_id, '8')) {
      return true;
    }
    $error_codes[] = 'vgm_id';
    return false;
  }

  public function v_code(&$error_codes = []) {
    if (is_null($this->code) || strlen($this->code) == 0 || self::valid_tinyint($this->code, '2')) {
      return true;
    }
    $error_codes[] = 'code';
    return false;
  }

  public function v_check(&$error_codes = []) {
    if (!is_null($this->check) && strlen($this->check) > 0 && self::valid_tinyint($this->check, '1')) {
      return true;
    }
    $error_codes[] = 'check';
    return false;
  }

  public function v_remark(&$error_codes = []) {
    if (is_null($this->remark) || strlen($this->remark) == 0 || self::valid_text($this->remark)) {
      return true;
    }
    $error_codes[] = 'remark';
    return false;
  }

  /**
  *@param array $conditions (required)
  *@param String $raw_sql (optional)
  *@return VgmCheck[]
  **/
  public static function find_all_like($conditions, $raw_sql = '') { return parent::generic_find_all_like($conditions, static::OM_CLASS_NAME, $raw_sql); }
  /**
  *@param array $conditions (required)
  *@param String $raw_sql (optional)
  *@return VgmCheck[]
  **/
  public static function find_all_by($conditions, $raw_sql = '') { return parent::generic_find_all_by($conditions, static::OM_CLASS_NAME, $raw_sql); }
  /**
  *@param String $raw_sql (optional)
  *@return VgmCheck[]
  **/
  public static function find_all($raw_sql = '') { return parent::generic_find_all(static::OM_CLASS_NAME, $raw_sql); }
  /**
  *@param array $conditions (required)
  *@param String $raw_sql (optional)
  *@return VgmCheck
  **/
  public static function find_by($conditions, $raw_sql = '') { return parent::generic_find_by($conditions, static::OM_CLASS_NAME, $raw_sql); }
  /**
  *@param String $id (required)
  *@param String $raw_sql (optional)
  *@return VgmCheck
  **/
  public static function find_by_id($id, $raw_sql = '') { return parent::generic_find_by_id($id, static::OM_CLASS_NAME, $raw_sql); }
  /**
  *@param array $conditions (required)
  *@param String $raw_sql (optional)
  *@return int
  **/
  public static function count_all_by($conditions, $raw_sql = '') { return parent::generic_count_all_by($conditions, static::OM_CLASS_NAME, $raw_sql); }
  /**
  *@param array $conditions (required)
  *@param String $raw_sql (optional)
  *@return bool
  **/
  public static function delete_by($conditions, $raw_sql = '') { return parent::generic_delete_by($conditions, static::OM_CLASS_NAME, $raw_sql); }
}