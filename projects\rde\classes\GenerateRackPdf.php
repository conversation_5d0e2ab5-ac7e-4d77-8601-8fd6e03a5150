<?php

class GenerateRackPdf extends GSDPDF {

  public function __construct(int $amount) {
    parent::__construct();

    $this->AddFont('Nunito', '', 'Nunito-Regular.ttf', true);
    $this->AddFont('Nunito', 'B', 'Nunito-Bold.ttf', true);
//    $this->SetAutoPageBreak(true, 30);

    $this->amount = $amount;
  }

  public function Header() {
    //$this->Image('C:\Work\www\HTTPDOCS\html\rde\filesystem\raamdorpel\clients\quotation\elementparts\105883\159915.png',null,null,0,0,'png');
    $this->Image(DIR_PROJECT_FOLDER . "templates/backend2/images/logopdf.png", null, 10.5, 90);
    $this->SetFont('Nunito', '', 10);
    $this->Cell(25);
    $this->Cell(0, 5, '', 0, 1, 'L', false);

    $this->SetLeftMargin(104);
    $this->SetY(10.5);
    $this->SetFont('', 'B');
    $this->Cell(91, 4, "Raamdorpelelementen BV", 0, 1, "R");
    $this->SetFont('', '');
    $this->Cell(91, 4, "Raambrug 9", 0, 1, "R");
    $this->Cell(91, 4, "5531 AG Bladel", 0, 1, "R");
    $this->Cell(91, 4, "0497 36 07 91", 0, 1, "R");
    $this->Cell(91, 4, "<EMAIL>", 0, 1, "R");
    $this->Cell(91, 4, "www.raamdorpel.nl", 0, 1, "R");
    $this->Ln();
    $this->Ln(3.5);

    $this->SetX(10);
    $this->SetLeftMargin(10);
  }

  // Footer
  // - Public
  // Adds HeBlad Raamdorpelelementen page footer
  public function Footer() {

    $this->setY(-25);
    $this->SetFont('Nunito', 'B', 10);
    $this->Cell(0, 7, 'Raamdorpel.nl - Raamdorpelelementen BV', 'T', 1, 'L', false);

    $this->SetFont('Nunito', '', 10);
    $this->SetTextColor(128, 128, 128);
    $this->Cell(15, 5, 'Adres:');
    $this->SetTextColor(0, 0, 0);
    $this->Cell(50, 5, 'Raambrug 9', 0, 0, 'L', false);
    $this->SetTextColor(128, 128, 128);
    $this->Cell(25, 5, 'Telefoon:');
    $this->SetTextColor(0, 0, 0);
    $this->Cell(35, 5, '0497 - 36.07.91', 0, 0, 'L', false);
    $this->SetTextColor(128, 128, 128);
    $this->Cell(25, 5, 'E-mail:');
    $this->SetTextColor(0, 0, 0);
    $this->Cell(35, 5, '<EMAIL>', 0, 1, 'L', false);

    $this->Cell(15);
    $this->Cell(50, 5, '5531 AG Bladel', 0, 0, 'L', false);
    $this->SetTextColor(128, 128, 128);
    $this->Cell(25, 5, 'Fax:');
    $this->SetTextColor(0, 0, 0);
    $this->Cell(35, 5, '0497 - 38.09.71', 0, 0, 'L', false);
    $this->SetTextColor(128, 128, 128);
    $this->Cell(25, 5, 'Website:');
    $this->SetTextColor(0, 0, 0);
    $this->Cell(35, 5, 'www.raamdorpel.nl', 0, 1, 'L', false);

  }

  public function NewPage($new_rack_id) {

    $this->addPage();

    $this->SetFont('Arial', '', 8);
    $this->SetDrawColor(50, 50, 50);
    $cellHeight = 5;
    $this->SetTextColor(0, 0, 0);
    $this->SetLeftMargin(10);
    $this->SetTopMargin(0);

    $cellHeightTopRight = 10;
    $xyCoordinatesStart = 5;
    $xyCoordinatesFromLeft = 5;

    $this->setXY($xyCoordinatesFromLeft, $xyCoordinatesStart);

    $this->SetLineWidth(0.2);
    $this->SetDrawColor(255, 0, 0);
    $this->SetFillColor(0, 255, 0);

    //- left, top
    $this->setXY(0, 0);

    $this->Image('https://www.raamdorpel.nl/3rdparty/barcodegen/datamatrix.php?text=rack' . $new_rack_id, 85, 40, 43, 43, 'PNG');
    // $this->Image($dataMatrixLink, 85, 17, 43, 43, 'PNG');

    $this->SetFont('Arial', 'B', 16);
    $this->setXY(5, 5);
    $this->Cell(50, 9, 'Rek nummer', 0, 1);
    $this->SetDrawColor(255, 0, 0);

    $this->setXY(10, 140);
    $this->SetFont('Arial', 'B', 190);
    $this->Cell(50, 10, '0' . $new_rack_id, 0, 1);

    $new_rack_id += 1;
  }

  public function generatePdf() {
    $new_pdf_id = GeneratedRackIdPdfs::getNewPdfId();

    for ($a = 0; $this->amount > $a; $a++) {
      $new_rack_id = GeneratedRackIds::GetNewRackId();
      $new_rack = new GeneratedRackIds();
      $new_rack->rackCode     = 0 . $new_rack_id;
      $new_rack->rackScanCode = 'RACK' . $new_rack->rackCode;
      $new_rack->datetime     = date("Y-m-d H:i:s");
      $new_rack->pdfId        = $new_pdf_id;
      $new_rack->save();

      $this->NewPage($new_rack_id);
      $amount = $a;
    }

    $new_rack_pdf = new GeneratedRackIdPdfs();
    $new_rack_pdf->rackPdfId = $new_pdf_id;
    $new_rack_pdf->datetime  = date("Y-m-d H:i:s");
    $new_rack_pdf->amount    = $amount + 1;
    $new_rack_pdf->save();

    $this->generate();
  }

  private function generate() {
    $this->SetDisplayMode('real');

    $filename = 'nieuwe_reknummers.pdf';

    $this->Output("I", $filename);

    return $filename;
  }
}
