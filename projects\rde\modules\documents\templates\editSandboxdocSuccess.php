<?php
  TemplateHelper::includePartial('_tabs.php', 'documents');
?>
<?php /** @var Files $file */ ?>

<?php writeErrors($errors); ?>

<form action='<?php echo reconstructQuery() ?>' method='post' name='project' enctype="multipart/form-data" class="edit-form">
  <table class="default_table">
    <tr class="dataTableHeadingRow">
      <td colspan="2">Documenten</td>
    </tr>
    <tr class="dataTableRow trhover">
      <td class="head">
        <?php echo __('Omschrijving') ?>
      </td>
      <td>
        <input type="text" name="title" placeholder="Omschrijving..." value="<?php echo $file->title ?>">
      </td>
    </tr>

    <tr class="dataTableRow trhover">
      <td class="head">
        <?php echo __('Selecteer bedrijf') ?>
      </td>
      <td>
        <a href="#" data-url="?action=companysearch" data-title="Selecteer machine" class="gsd-btn gsd-btn-secondary" id="selectCompany" style="margin: 0 15px 0 0;">Selecteer bedrijf</a>
      </td>
    </tr>

    <tr class="dataTableRow trhover">
      <td class="head">
        <?php echo __('Bedrijfsnaam') ?>
      </td>
      <td>
        <input type="text" name="company_name" id="company_name" placeholder="Bedrijfsnaam..." value="<?php if (!empty($file->companyId) && $file->companyId !== 0) echo $company->name ?>" disabled>
      </td>
    </tr>

        <input type="hidden" name="company_id" id="company_id" value="">

    <tr class="dataTableRow trhover">
      <td class="head">
        <?php echo __('Datum') ?>
      </td>
      <td>
        <input type="date" name="date" value=<?php echo $file->uploadDate ?>>
      </td>
    </tr>

    <tr class="dataTableRow trhover">
      <td class="head">
        <?php echo __('Delen met accountant') ?>
      </td>
      <td>
        <input type="checkbox" name="upload_alert" <?php if ($file->uploadAlert == 1) echo 'checked';?>>
        Deze uitvinken als notitie gelezen is.
      </td>
    </tr>

    <tr class="dataTableRow trhover">
      <td class="head">
        <?php echo __('Notities') ?>
      </td>
      <td>
        <textarea name="notes" rows="5"><?php echo $file->notes ?></textarea>
      </td>
    </tr>

  </table>

  <input type="submit" value="Opslaan" name="save" class="gsd-btn gsd-btn-primary"/>
  <input type="submit" value="Opslaan en naar lijst" name="savelist" class="gsd-btn gsd-btn-secondary"/>
  <a href="<?php echo reconstructQueryAdd(['pageId']) ?>" class="gsd-btn gsd-btn-link">
    <?php echo __('Annuleren') ?>
  </a>

</form>

<script type="text/javascript">

  let gsdModal = new GsdModal();
  gsdModal.init();

  $(document).ready(function() {


    $("#selectCompany").on("click", function (e) {
      e.preventDefault();
      gsdModal.open($(this).attr("data-url"), $(this).attr("data-title"));
    });


    $(document).on("gsdModalSelect", function (e, msg) {

      gsdModal.hide();

      let company = JSON.parse(msg);
      console.log(company)
      $("#company_id").val(company.companyId);
      $("#company_name").val(company.name);
    });
  });
</script>
