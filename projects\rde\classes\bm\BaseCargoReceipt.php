<?php
class BaseCargoReceipt extends AppModel
{
  const DB_NAME = 'rde_b1mo';
  const TABLE_NAME = 'cargo_receipt';
  const OM_CLASS_NAME = 'CargoReceipt';
  const columns = ['cargoReceiptId', 'companyId', 'cargoReceiptType', 'shippingAgentName', 'shippingAgentStreet', 'shippingAgentNumber', 'shippingAgentExt', 'shippingAgentZip', 'shippingAgentCity', 'shippingAgentPhone', 'destinationName', 'destinationStreet', 'destinationNumber', 'destinationExt', 'destinationZip', 'destinationCity', 'city3', 'country3', 'city4', 'country4', 'dateOfToday', 'documentsAttached', 'marksAndNums', 'numberOfPackages', 'methodOfPacking', 'natureOfTheGoods', 'staticNumberNear', 'staticNumberFar', 'staticNumberRetour', 'gross', 'volume', 'destinationContact', 'destinationPhone', 'destinationMobile', 'consigneeExecutor', 'consigneeExecutorMobile', 'consigneeExecutorMail', 'senderInstructions', 'carriagePaid', 'carriageForward', 'cashOnDelivery', 'carrierId', 'carrierName', 'carrierStreet', 'carrierStreetNumber', 'carrierExt', 'carrierZip', 'carrierCity', 'carrierNumberplate', 'successiveCarriers', 'carrierDispatchAppointment', 'specialAgreements', 'specialAgreementsRead', 'toBePaidBy', 'establishedCity', 'establishedDate', 'signatureSenderName', 'signatureSenderFilename', 'signatureSenderPlace', 'signatureSenderDate', 'signatureCarrierName', 'signatureCarrierFilename', 'signatureCarrierPlace', 'signatureCarrierDate', 'signatureCarrierMail', 'signatureConsigneeName', 'signatureConsigneeFilename', 'signatureConsigneePlace', 'signatureConsigneeDateTime', 'signatureConsigneeMail', 'cmr', 'avc', 'carrierCode'];
  const field_structure = [
    'cargoReceiptId'              => ['type' => 'int', 'length' => '11', 'null' => false],
    'companyId'                   => ['type' => 'int', 'length' => '7', 'null' => true],
    'cargoReceiptType'            => ['type' => 'enum', 'length' => '3', 'null' => false, 'enums' => ['standard','pickup','factorypickup']],
    'shippingAgentName'           => ['type' => 'varchar', 'length' => '50', 'null' => false],
    'shippingAgentStreet'         => ['type' => 'varchar', 'length' => '50', 'null' => false],
    'shippingAgentNumber'         => ['type' => 'int', 'length' => '5', 'null' => false],
    'shippingAgentExt'            => ['type' => 'varchar', 'length' => '10', 'null' => true],
    'shippingAgentZip'            => ['type' => 'varchar', 'length' => '6', 'null' => false],
    'shippingAgentCity'           => ['type' => 'varchar', 'length' => '50', 'null' => false],
    'shippingAgentPhone'          => ['type' => 'varchar', 'length' => '20', 'null' => false],
    'destinationName'             => ['type' => 'varchar', 'length' => '200', 'null' => true],
    'destinationStreet'           => ['type' => 'varchar', 'length' => '50', 'null' => true],
    'destinationNumber'           => ['type' => 'int', 'length' => '5', 'null' => true],
    'destinationExt'              => ['type' => 'varchar', 'length' => '10', 'null' => true],
    'destinationZip'              => ['type' => 'varchar', 'length' => '6', 'null' => true],
    'destinationCity'             => ['type' => 'varchar', 'length' => '50', 'null' => true],
    'city3'                       => ['type' => 'varchar', 'length' => '50', 'null' => true],
    'country3'                    => ['type' => 'varchar', 'length' => '50', 'null' => true],
    'city4'                       => ['type' => 'varchar', 'length' => '50', 'null' => true],
    'country4'                    => ['type' => 'varchar', 'length' => '50', 'null' => true],
    'dateOfToday'                 => ['type' => 'datetime', 'length' => '', 'null' => true],
    'documentsAttached'           => ['type' => 'varchar', 'length' => '255', 'null' => true],
    'marksAndNums'                => ['type' => 'text', 'length' => '', 'null' => true],
    'numberOfPackages'            => ['type' => 'int', 'length' => '10', 'null' => false],
    'methodOfPacking'             => ['type' => 'enum', 'length' => '4', 'null' => true, 'enums' => ['rek','bak','pal','opr']],
    'natureOfTheGoods'            => ['type' => 'varchar', 'length' => '100', 'null' => true],
    'staticNumberNear'            => ['type' => 'varchar', 'length' => '255', 'null' => true],
    'staticNumberFar'             => ['type' => 'varchar', 'length' => '255', 'null' => true],
    'staticNumberRetour'          => ['type' => 'text', 'length' => '', 'null' => true],
    'gross'                       => ['type' => 'varchar', 'length' => '10', 'null' => true],
    'volume'                      => ['type' => 'varchar', 'length' => '10', 'null' => true],
    'destinationContact'          => ['type' => 'varchar', 'length' => '200', 'null' => true],
    'destinationPhone'            => ['type' => 'varchar', 'length' => '15', 'null' => true],
    'destinationMobile'           => ['type' => 'varchar', 'length' => '15', 'null' => true],
    'consigneeExecutor'           => ['type' => 'varchar', 'length' => '200', 'null' => true],
    'consigneeExecutorMobile'     => ['type' => 'varchar', 'length' => '15', 'null' => true],
    'consigneeExecutorMail'       => ['type' => 'varchar', 'length' => '255', 'null' => true],
    'senderInstructions'          => ['type' => 'varchar', 'length' => '255', 'null' => true],
    'carriagePaid'                => ['type' => 'boolean', 'length' => '1', 'null' => false],
    'carriageForward'             => ['type' => 'boolean', 'length' => '1', 'null' => false],
    'cashOnDelivery'              => ['type' => 'boolean', 'length' => '1', 'null' => false],
    'carrierId'                   => ['type' => 'int', 'length' => '11', 'null' => false],
    'carrierName'                 => ['type' => 'varchar', 'length' => '200', 'null' => true],
    'carrierStreet'               => ['type' => 'varchar', 'length' => '50', 'null' => true],
    'carrierStreetNumber'         => ['type' => 'int', 'length' => '5', 'null' => true],
    'carrierExt'                  => ['type' => 'varchar', 'length' => '10', 'null' => true],
    'carrierZip'                  => ['type' => 'varchar', 'length' => '6', 'null' => true],
    'carrierCity'                 => ['type' => 'varchar', 'length' => '70', 'null' => true],
    'carrierNumberplate'          => ['type' => 'varchar', 'length' => '10', 'null' => true],
    'successiveCarriers'          => ['type' => 'varchar', 'length' => '255', 'null' => true],
    'carrierDispatchAppointment'  => ['type' => 'text', 'length' => '', 'null' => true],
    'specialAgreements'           => ['type' => 'varchar', 'length' => '255', 'null' => true],
    'specialAgreementsRead'       => ['type' => 'boolean', 'length' => '1', 'null' => false],
    'toBePaidBy'                  => ['type' => 'varchar', 'length' => '255', 'null' => true],
    'establishedCity'             => ['type' => 'varchar', 'length' => '70', 'null' => true],
    'establishedDate'             => ['type' => 'datetime', 'length' => '', 'null' => true],
    'signatureSenderName'         => ['type' => 'varchar', 'length' => '200', 'null' => true],
    'signatureSenderFilename'     => ['type' => 'varchar', 'length' => '255', 'null' => true],
    'signatureSenderPlace'        => ['type' => 'varchar', 'length' => '70', 'null' => true],
    'signatureSenderDate'         => ['type' => 'datetime', 'length' => '', 'null' => true],
    'signatureCarrierName'        => ['type' => 'varchar', 'length' => '200', 'null' => true],
    'signatureCarrierFilename'    => ['type' => 'varchar', 'length' => '255', 'null' => true],
    'signatureCarrierPlace'       => ['type' => 'varchar', 'length' => '70', 'null' => true],
    'signatureCarrierDate'        => ['type' => 'datetime', 'length' => '', 'null' => true],
    'signatureCarrierMail'        => ['type' => 'varchar', 'length' => '255', 'null' => true],
    'signatureConsigneeName'      => ['type' => 'varchar', 'length' => '200', 'null' => true],
    'signatureConsigneeFilename'  => ['type' => 'varchar', 'length' => '255', 'null' => true],
    'signatureConsigneePlace'     => ['type' => 'varchar', 'length' => '70', 'null' => true],
    'signatureConsigneeDateTime'  => ['type' => 'datetime', 'length' => '', 'null' => true],
    'signatureConsigneeMail'      => ['type' => 'varchar', 'length' => '255', 'null' => true],
    'cmr'                         => ['type' => 'boolean', 'length' => '1', 'null' => false],
    'avc'                         => ['type' => 'boolean', 'length' => '1', 'null' => false],
    'carrierCode'                 => ['type' => 'varchar', 'length' => '20', 'null' => true],
  ];

  protected static $primary_key = ['cargoReceiptId'];
  protected $auto_increment = 'cargoReceiptId';

  public $cargoReceiptId, $companyId, $cargoReceiptType, $shippingAgentName, $shippingAgentStreet, $shippingAgentNumber, $shippingAgentExt, $shippingAgentZip, $shippingAgentCity, $shippingAgentPhone, $destinationName, $destinationStreet, $destinationNumber, $destinationExt, $destinationZip, $destinationCity, $city3, $country3, $city4, $country4, $dateOfToday, $documentsAttached, $marksAndNums, $numberOfPackages, $methodOfPacking, $natureOfTheGoods, $staticNumberNear, $staticNumberFar, $staticNumberRetour, $gross, $volume, $destinationContact, $destinationPhone, $destinationMobile, $consigneeExecutor, $consigneeExecutorMobile, $consigneeExecutorMail, $senderInstructions, $carriagePaid, $carriageForward, $cashOnDelivery, $carrierId, $carrierName, $carrierStreet, $carrierStreetNumber, $carrierExt, $carrierZip, $carrierCity, $carrierNumberplate, $successiveCarriers, $carrierDispatchAppointment, $specialAgreements, $specialAgreementsRead, $toBePaidBy, $establishedCity, $establishedDate, $signatureSenderName, $signatureSenderFilename, $signatureSenderPlace, $signatureSenderDate, $signatureCarrierName, $signatureCarrierFilename, $signatureCarrierPlace, $signatureCarrierDate, $signatureCarrierMail, $signatureConsigneeName, $signatureConsigneeFilename, $signatureConsigneePlace, $signatureConsigneeDateTime, $signatureConsigneeMail, $cmr, $avc, $carrierCode;

  function __construct($obj_arr = [], &$error_codes = []) {
    parent::__construct();
    $this->setDefaults();
    if (!empty($obj_arr)) {
      $this->from_array($obj_arr);
      $this->valid_required_fields($error_codes);
    }
  }

  /**
  * Set default object values
  **/
  public function setDefaults() {
    $this->cargoReceiptType = 'standard';
    $this->numberOfPackages = 0;
    $this->carriagePaid = 0;
    $this->carriageForward = 0;
    $this->cashOnDelivery = 0;
    $this->carrierId = 0;
    $this->specialAgreementsRead = 0;
    $this->cmr = 0;
    $this->avc = 0;
  }

  /**
   * @param array $conditions (required)
   * @param string $raw_sql (optional)
   * @return CargoReceipt[]
   **/
  public static function find_all_like($conditions, $raw_sql = '') {
    return parent::generic_find_all_like($conditions, static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param array $conditions (required)
   * @param string $raw_sql (optional)
   * @return CargoReceipt[]
   **/
  public static function find_all_by($conditions, $raw_sql = '') {
    return parent::generic_find_all_by($conditions, static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param string $raw_sql (optional)
   * @return CargoReceipt[]
   **/
  public static function find_all($raw_sql = '') {
    return parent::generic_find_all(static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param array $conditions (required)
   * @param string $raw_sql (optional)
   * @return CargoReceipt
   **/
  public static function find_by($conditions, $raw_sql = '') {
    return parent::generic_find_by($conditions, static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param string $id (required)
   * @param string $raw_sql (optional)
   * @return CargoReceipt
   **/
  public static function find_by_id($id, $raw_sql = '') {
    return parent::generic_find_by_id($id, static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param array $conditions (required)
   * @param string $raw_sql (optional)
   * @return int
   **/
  public static function count_all_by($conditions, $raw_sql = '') {
    return parent::generic_count_all_by($conditions, static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param array $conditions (required)
   * @param string $raw_sql (optional)
   * @return bool
   **/
  public static function delete_by($conditions, $raw_sql = '') {
    return parent::generic_delete_by($conditions, static::OM_CLASS_NAME, $raw_sql);
  }

}