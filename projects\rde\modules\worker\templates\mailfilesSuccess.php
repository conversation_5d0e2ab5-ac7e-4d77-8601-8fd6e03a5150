<?php require_once("_tabs.php") ?>

<div>
  <p>Personeels-email: <a href="mailto:<EMAIL>"><EMAIL></a></p>
</div>

<form name="workerlist" action="<?php echo reconstructQueryAdd(['pageId']) ?>" method="post">
  <div class="list-filter-form">
    <input type="text" name="m_search" value="<?php echo $_SESSION['m_search'] ?>" placeholder="Zoeken..."/>
    <input type="submit" name="go" value="Zoeken"/>
    <input type="submit" name="fetchnow" value="E-mails ophalen" class="gsd-btn gsd-btn-primary"/>
    <?php echo showHelpButton("De emails worden elke 5 minuten automatisch opgehaald.") ?>
  </div>

  <?php if (count($files) == 0): ?>
    <section class="empty-list-state">
      <p>Er zijn geen bestanden gevonden.</p>
    </section>
  <?php else: ?>
    <table class="default_table" id="sortable">
      <tr class="dataTableHeadingRow">
        <td>Datum</td>
        <td>Onderwerp</td>
        <td style="width: 30px;">Type</td>
        <td>Bestandsnaam</td>
        <td>Medewerker</td>
        <td>Verplaats</td>
        <td style="width: 50px">Verwijder</td>
      </tr>
      <?php foreach ($files as $item): ?>
        <tr class="dataTableRow trhover" id="<?php echo $item->id ?>">
          <td><?php echo $item->getInsertTS() ?></td>
          <td><?php echo $item->mail_subject ?></td>
          <td><img src="/gsdfw/images/<?php echo $item->getExtImg(); ?>" alt="<?php echo $item->ext_type; ?>"/></td>
          <td>
            <a href="<?php echo reconstructQuery() ?>fid=<?php echo $item->id ?>"><?php echo $item->originalfilename ?></a>
          </td>
          <td>
            <select id="worker_id" name="workerids[<?php echo $item->id ?>]">
              <option value="">Selecteer medewerker...</option>
              <?php foreach ($workers as $worker): ?>
                <option value="<?php echo $worker->id ?>"><?php echo $worker->getNaam() ?></option>
              <?php endforeach ?>
            </select>
          </td>
          <td>
            <input type="submit" name="koppelen" value="Koppelen" class="gsd-btn gsd-btn-secondary"/>
          </td>
          <td>
            <?php echo BtnHelper::getRemove(reconstructQuery(['action', 'id']) . 'action=mailfiledelete&mfid=' . $item->id) ?>
          </td>
        </tr>
      <?php endforeach; ?>
    </table>
  <?php endif; ?>
</form>
