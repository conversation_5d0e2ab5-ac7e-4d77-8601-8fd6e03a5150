<?php
class BaseFiles extends AppModel
{
  const DB_NAME = 'rde_b1mo';
  const TABLE_NAME = 'files';
  const OM_CLASS_NAME = 'Files';
  const columns = ['fileId', 'quotationId', 'companyId', 'title', 'folder', 'filename', 'uploadDate', 'documentDate', 'categoryId', 'typeId', 'uploadAlert', 'notesAlert', 'showInDocuments', 'notes'];
  const field_structure = [
    'fileId'                      => ['type' => 'int', 'length' => '11', 'null' => false],
    'quotationId'                 => ['type' => 'int', 'length' => '11', 'null' => true],
    'companyId'                   => ['type' => 'int', 'length' => '11', 'null' => true],
    'title'                       => ['type' => 'varchar', 'length' => '255', 'null' => false],
    'folder'                      => ['type' => 'varchar', 'length' => '255', 'null' => false],
    'filename'                    => ['type' => 'varchar', 'length' => '255', 'null' => false],
    'uploadDate'                  => ['type' => 'datetime', 'length' => '', 'null' => false],
    'documentDate'                => ['type' => 'datetime', 'length' => '', 'null' => true],
    'categoryId'                  => ['type' => 'int', 'length' => '11', 'null' => false],
    'typeId'                      => ['type' => 'int', 'length' => '11', 'null' => false],
    'uploadAlert'                 => ['type' => 'boolean', 'length' => '1', 'null' => false],
    'notesAlert'                  => ['type' => 'boolean', 'length' => '1', 'null' => false],
    'showInDocuments'             => ['type' => 'boolean', 'length' => '1', 'null' => false],
    'notes'                       => ['type' => 'text', 'length' => '', 'null' => true],
  ];

  protected static $primary_key = ['fileId'];
  protected $auto_increment = 'fileId';

  public $fileId, $quotationId, $companyId, $title, $folder, $filename, $uploadDate, $documentDate, $categoryId, $typeId, $uploadAlert, $notesAlert, $showInDocuments, $notes;

  function __construct($obj_arr = [], &$error_codes = []) {
    parent::__construct();
    $this->setDefaults();
    if (!empty($obj_arr)) {
      $this->from_array($obj_arr);
      $this->valid_required_fields($error_codes);
    }
  }

  /**
  * Set default object values
  **/
  public function setDefaults() {
    $this->uploadAlert = 0;
    $this->notesAlert = 1;
    $this->showInDocuments = 0;
  }

  /**
   * @param array $conditions (required)
   * @param string $raw_sql (optional)
   * @return Files[]
   **/
  public static function find_all_like($conditions, $raw_sql = '') {
    return parent::generic_find_all_like($conditions, static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param array $conditions (required)
   * @param string $raw_sql (optional)
   * @return Files[]
   **/
  public static function find_all_by($conditions, $raw_sql = '') {
    return parent::generic_find_all_by($conditions, static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param string $raw_sql (optional)
   * @return Files[]
   **/
  public static function find_all($raw_sql = '') {
    return parent::generic_find_all(static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param array $conditions (required)
   * @param string $raw_sql (optional)
   * @return Files
   **/
  public static function find_by($conditions, $raw_sql = '') {
    return parent::generic_find_by($conditions, static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param string $id (required)
   * @param string $raw_sql (optional)
   * @return Files
   **/
  public static function find_by_id($id, $raw_sql = '') {
    return parent::generic_find_by_id($id, static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param array $conditions (required)
   * @param string $raw_sql (optional)
   * @return int
   **/
  public static function count_all_by($conditions, $raw_sql = '') {
    return parent::generic_count_all_by($conditions, static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param array $conditions (required)
   * @param string $raw_sql (optional)
   * @return bool
   **/
  public static function delete_by($conditions, $raw_sql = '') {
    return parent::generic_delete_by($conditions, static::OM_CLASS_NAME, $raw_sql);
  }

}