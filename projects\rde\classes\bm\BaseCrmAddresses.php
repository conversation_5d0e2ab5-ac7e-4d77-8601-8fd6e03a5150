<?php
class BaseCrmAddresses extends AppModel {

  const DB_NAME = 'rde_cms';
  const TABLE_NAME = 'crm_addresses';
  const OM_CLASS_NAME = 'CrmAddresses';
  const columns = ['addressId', 'companyId', 'type', 'title', 'street', 'nr', 'extension', 'zipcode', 'domestic', 'country', 'longitude', 'latitude', 'extraInfo', 'date', 'mapExtra'];
  const field_structure = [
    'addressId'                   => ['type' => 'int', 'length' => '10', 'null' => false],
    'companyId'                   => ['type' => 'int', 'length' => '10', 'null' => true],
    'type'                        => ['type' => 'enum', 'length' => '5', 'null' => false, 'enums' => ['visit','post','invoice','delivery','map']],
    'title'                       => ['type' => 'varchar', 'length' => '255', 'null' => true],
    'street'                      => ['type' => 'varchar', 'length' => '150', 'null' => false],
    'nr'                          => ['type' => 'int', 'length' => '5', 'null' => true],
    'extension'                   => ['type' => 'varchar', 'length' => '10', 'null' => true],
    'zipcode'                     => ['type' => 'varchar', 'length' => '6', 'null' => false],
    'domestic'                    => ['type' => 'varchar', 'length' => '150', 'null' => false],
    'country'                     => ['type' => 'varchar', 'length' => '2', 'null' => false],
    'longitude'                   => ['type' => 'float', 'length' => '17,15', 'null' => true],
    'latitude'                    => ['type' => 'float', 'length' => '17,15', 'null' => true],
    'extraInfo'                   => ['type' => 'text', 'length' => '', 'null' => true],
    'date'                        => ['type' => 'timestamp', 'length' => '', 'null' => false],
    'mapExtra'                    => ['type' => 'boolean', 'length' => '1', 'null' => true],
  ];

  protected static $primary_key = ['addressId'];
  protected $auto_increment = 'addressId';

  public $addressId, $companyId, $type, $title, $street, $nr, $extension, $zipcode, $domestic, $country, $longitude, $latitude, $extraInfo, $date, $mapExtra;

  function __construct($obj_arr = [], &$error_codes = []) {
    parent::__construct();
    $this->setDefaults();
    if (!empty($obj_arr)) {
      $this->from_array($obj_arr);
      $this->valid_required_fields($error_codes);
    }
  }

  /**
  * Set default object values
  **/
  public function setDefaults() {
    $this->country = 'NL';
    $this->date = 'current_timestamp()';
  }

  /**
   * @param array $conditions (required)
   * @param string $raw_sql (optional)
   * @return CrmAddresses[]
   **/
  public static function find_all_like($conditions, $raw_sql = '') {
    return parent::generic_find_all_like($conditions, static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param array $conditions (required)
   * @param string $raw_sql (optional)
   * @return CrmAddresses[]
   **/
  public static function find_all_by($conditions, $raw_sql = '') {
    return parent::generic_find_all_by($conditions, static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param string $raw_sql (optional)
   * @return CrmAddresses[]
   **/
  public static function find_all($raw_sql = '') {
    return parent::generic_find_all(static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param array $conditions (required)
   * @param string $raw_sql (optional)
   * @return CrmAddresses
   **/
  public static function find_by($conditions, $raw_sql = '') {
    return parent::generic_find_by($conditions, static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param string $id (required)
   * @param string $raw_sql (optional)
   * @return CrmAddresses
   **/
  public static function find_by_id($id, $raw_sql = '') {
    return parent::generic_find_by_id($id, static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param array $conditions (required)
   * @param string $raw_sql (optional)
   * @return int
   **/
  public static function count_all_by($conditions, $raw_sql = '') {
    return parent::generic_count_all_by($conditions, static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param array $conditions (required)
   * @param string $raw_sql (optional)
   * @return bool
   **/
  public static function delete_by($conditions, $raw_sql = '') {
    return parent::generic_delete_by($conditions, static::OM_CLASS_NAME, $raw_sql);
  }

}