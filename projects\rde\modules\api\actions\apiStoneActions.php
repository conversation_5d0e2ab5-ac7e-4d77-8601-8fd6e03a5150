<?php


  /**
   * Trait apiStoneActions
   * Used for calls from other rde cms
   */
  trait apiStoneActions {

    /**
     * Get stones
     */
    public function executeGetStones() {
      $request_vars = $this->data->getData();
      $filter = [];
      if (isset($request_vars['colorId'])) {
        $filter["colorId"] = $request_vars['colorId'];
      }
      elseif (isset($request_vars['truckfillers'])) {
        $filter["stoneId"] = [238, 292, 38, 13];
      }
      if (isset($request_vars['brandId'])) {
        $filter["brandId"] = $request_vars['brandId'];
      }
      //rightg genegeerd, word niet gebruikt bij voorraad
      if (count($filter) > 0) {
        $stones = Stones::find_all_by($filter, "AND stones.endstone!='rightg' ORDER BY name");
      }
      else {
        $stones = Stones::find_all("WHERE stones.endstone!='rightg' ORDER BY name");
      }

      $alreadyOrdered = StoneOrderItem::getOrderedNotDelivered();

      foreach ($stones as $k => $stone) {
        if (isset($alreadyOrdered[$stone->stoneId])) {
          $stones[$k]->alreadyOrdered = $alreadyOrdered[$stone->stoneId];
        }
        else {
          $stones[$k]->alreadyOrdered = 0;
        }
      }

      RestUtils::sendResponseOK("STONE LIST SUCCESFULLY RETRIEVED", AppModel::plainObjects($stones, ["alreadyOrdered"]));

    }

    /**
     * Get stones to order
     */
    public function executeGetStonesToOrder() {
      $request_vars = $this->data->getData();
      $brandId = '';
      $rdata = $this->data->getData();
      if (isset($rdata['brandId'])) {
        $brandId = $rdata['brandId'];
      }
      $onlyshortage = true;
      $hasorders = false;
      if (isset($rdata['needed'])) {
        $onlyshortage = false;
        $hasorders = true;
      }
      $result = Stones::getStonesToOrder(false, '', $brandId, $onlyshortage, $hasorders);
      $stones = $result["stones"];
      $order_item_quotations = $result["order_item_quotations"];

      $stones_plain = AppModel::plainObjects($stones, ["totals", "orders", "sibelings", "onOrderlist", "alreadyOrdered", "toOrder"]);
      foreach ($stones_plain as $stone) {
        $stone->sibelings = array_values($stone->sibelings);
      }

      $stones_undefined = [];
      foreach (Quotations::find_all_by(["statusId" => [20, 21, 30, 31, 35]]) as $q) {
        $qa = QuotationsExtra::find_by(["quotationId" => $q->quotationId, "totalMiddlesStones" => 0]);
        if ($qa) {
          $stones_undefined[$q->quotationId] = AppModel::plainObject($q);
        }

      }

      $return = [
        "stones"                => array_values($stones_plain),
        "order_item_quotations" => array_values($order_item_quotations),
        "stones_undefined"      => array_values($stones_undefined),
      ];

      RestUtils::sendResponseOK("STONE LIST SUCCESFULLY RETRIEVED", $return);

    }


    public function executeGetStoneOrderItems() {

      $rdata = $this->data->getData();

      $sois = [];
      $stoneorderitems = StoneOrderItem::getStonesToRecieve($rdata["brandId"]);
      foreach ($stoneorderitems as $k => $si) {
        $si->size -= $si->receivedsize;
        $si->receivedsize = 0;
      }

      usort($stoneorderitems, function ($a, $b) {
        $recievea = time();
        if ($a->getSupplierreadydate("U") != "") {
          $recievea = intval($a->getSupplierreadydate("U"));
        }
        $recieveb = time();
        if ($b->getSupplierreadydate("U") != "") {
          $recieveb = intval($b->getSupplierreadydate("U"));
        }
        if ($recievea < $recieveb) return -1;
        if ($recievea > $recieveb) return 1;
        return strcmp($a->stone->name, $b->stone->name);
      });

      foreach ($stoneorderitems as $soi) {
        $si = AppModel::plainObject($soi);
        $si->stone = AppModel::plainObject($soi->stone);
        $sois[] = $si;
      }

      RestUtils::sendResponseOK("STONE LIST SUCCESFULLY RETRIEVED", $sois);
    }


    /**
     * Get possible stone colors
     */
    public function executeGetColors() {
      $request_vars = $this->data->getData();
      $getstones = false;

      if (isset($request_vars["stones"])) {
        $getstones = true;
      }

      $filt = "";
      if (isset($request_vars["brandId"]) && $request_vars["brandId"] != 0) {
        $filt .= "WHERE brandId=" . $request_vars["brandId"];
      }
      $filt .= " ORDER BY brandId, name";

      $colors = AppModel::mapObjectIds(StoneColors::find_all($filt), 'colorId');
      if ($getstones) {
        foreach (Stones::find_all() as $stone) {
          if (!isset($colors[$stone->colorId]))
            continue; //gekoppeld aan kleur die niet bestaat....
          if (!isset($colors[$stone->colorId]->stones)) {
            $colors[$stone->colorId]->stones = [];
          }
          $colors[$stone->colorId]->stones[] = $stone;
        }
      }

      RestUtils::sendResponseOK("STONE LIST SUCCESFULLY RETRIEVED", AppModel::plainObjects(array_values($colors), ["stones"]));

    }

    /**
     * Stenen bestelling
     */
    public function executePutStoneorder() {

      $request_vars = $this->data->getData();
      $updateStock = isset($_GET['updatestock']);

      $json = RestUtils::getPostvaluesByKey("json", $request_vars);

      logToFile('stoneorder', print_r($json, true));

      foreach ($json as $stone_in) {
        $brandId = $stone_in->brandId;

        $toOrders = [];
        $toOrders[$stone_in->stoneId] = $stone_in->toOrder;
        if (isset($stone_in->sibelings)) {
          foreach ($stone_in->sibelings as $stone_in_sib) {
            $toOrders[$stone_in_sib->stoneId] = $stone_in_sib->toOrder;
          }
        }

        if (count($toOrders) > 0) {

          $stoneorder = StoneOrder::find_by(["brand_id" => $brandId, "status" => "new"]);
          if (!$stoneorder) {
            $stoneorder = new StoneOrder();
            $stoneorder->brand_id = $brandId;
            $stoneorder->save();
          }
          foreach ($toOrders as $stoneId => $toOrder) {
            $stone = Stones::find_by(["stoneId" => $stoneId]);

            $start = $toOrder > 0;
            if (!$start && isset($stone_in->orders) && $stone->endstone == 'false') {
              foreach ($stone_in->orders as $or) {
                if ($or->checked == 1) {
                  $start = true;
                  break;
                }
              }
            }

            if ($start) {
              $soi = StoneOrderItem::find_by(["stone_id" => $stoneId], " AND senddate IS NULL ");
              if (!$soi) {
                $soi = new StoneOrderItem();
                $soi->stone_order_id = $stoneorder->id;
                $soi->stone_id = $stoneId;
                $soi->size = 0;
              }
              $soi->size += $toOrder;
              $soi->save();

              logToFile('stoneorder', "To order: " . $soi->id);

              if (isset($stone_in->orders)) {
                foreach ($stone_in->orders as $or) {
                  if ($or->checked == 1) {
                    $soiq = StoneOrderItemQuotation::find_by(["stone_order_item_id" => $soi->id, "quotation_id" => $or->quotationId]);
                    if (!$soiq) { //nog niet gekoppeld, aanmaken maar
                      $soiq = new StoneOrderItemQuotation();
                      $soiq->stone_order_item_id = $soi->id;
                      $soiq->quotation_id = $or->quotationId;
                      $soiq->save();
                    }
                  }
                }
              }

            }
          }
        }

        //update stock?
        if ($updateStock) {
          $stone = Stones::find_by(["stoneId" => $stone_in->stoneId]);
          if ($stone && $stone_in->stock != $stone->stock && $stone_in->stockChanged == 1) {
            $stone->stock = $stone_in->stock;
            $stone->save();
            logToFile('stoneorder', "Saved: " . $stone->stoneId);
          }
        }
      }

      RestUtils::sendResponseOK("Stenen bestelling geplaatst.");

    }


    /**
     * Stenen ingevoerd op voorraad updaten app
     */
    public function executeUpdatestonestock() {

      $request_vars = $this->data->getData();

      $json = RestUtils::getPostvaluesByKey("json", $request_vars);

      //      logToFile('stoneupdate', print_r($json, true));

      foreach ($json as $stone_in) {
        $stone = Stones::find_by(["stoneId" => $stone_in->stoneId]);
        if ($stone && $stone_in->stock != $stone->stock && $stone_in->stockChanged == 1) {
          $stone->stock = $stone_in->stock;
          $stone->save();
          //logToFile('stoneupdate', "save: ".$stone->stoneId." ".$stone->stock);
        }
      }

      RestUtils::sendResponseOK("Stenen voorraad geupdate.");

    }

    /**
     * Stenen ingevoerd op voorraad updaten app
     */
    public function executeUpdatestonestockloss() {

      $request_vars = $this->data->getData();

      $json = RestUtils::getPostvaluesByKey("json", $request_vars);

      //      logToFile('stoneloss', print_r($json, true));

      foreach ($json as $stone_in) {
        if ($stone_in->stock != "" && $stone_in->stock != 0) {
          $stone = Stones::find_by(["stoneId" => $stone_in->stoneId]);
          $stone->stock -= $stone_in->stock;
          if ($stone->stock < 0) $stone->stock = 0; //negatief, dan gewoon op 0 zetten.
          $stone->save();

          $sl = new StoneLoss();
          $sl->stockloss = $stone_in->stock;
          $sl->stone_id = $stone->stoneId;
          $sl->save();

        }
      }

      RestUtils::sendResponseOK("Stenen verlies verwerkt.");

    }


    /**
     * Extra stenen om de vrachtwagen af te vullen.
     */
    public function executeStonepickupextra() {

      $request_vars = $this->data->getData();

      $json = RestUtils::getPostvaluesByKey("json", $request_vars);

      //      logToFile('stonepickup', print_r($json, true));

      $stones = [];
      foreach ($json as $stone_in) {
        if ($stone_in->stock != "" && $stone_in->stock != 0) {
          $stone = Stones::find_by(["stoneId" => $stone_in->stoneId]);
          $stone->stock += $stone_in->stock; //voorraad ophogen met geladen aantal
          $stone->stockadded = $stone_in->stock;
          $stone->save();

          //direct ook in verwerkt lijst bijzetten.
          $stoneorder = StoneOrder::find_by(["brand_id" => $stone->brandId, "status" => ["ordered", "delivered"]], " AND DATE(receiveddate)='" . date("Y-m-d") . "'");
          $soi = false;
          if (!$stoneorder) { //geen stoneorder, aanmaken
            $stoneorder = new StoneOrder();
            $stoneorder->brand_id = $stone->brandId;
            $stoneorder->senddate = date("Y-m-d H:i:s");
            $stoneorder->receiveddate = date("Y-m-d H:i:s");
            $stoneorder->status = "delivered";
            $stoneorder->save();
          }
          else { //wel een stone order, ook een stoneorderitem?
            $soi = StoneOrderItem::find_by(["stone_id" => $stone->id, "stone_order_id" => $stoneorder->id]);
          }

          if (!$soi) {
            $soi = new StoneOrderItem();
            $soi->stone_order_id = $stoneorder->id;
            $soi->stone_id = $stone->stoneId;
            $soi->size = $stone_in->stock;
          }
          else {
            $soi->size += $stone_in->stock;
          }
          if ($soi->senddate == "") $soi->senddate = $stoneorder->senddate;
          if ($soi->receiveddate == "") $soi->receiveddate = $stoneorder->receiveddate;
          $soi->receivedsize = $soi->size;
          $soi->save();

          $stones[] = $stone;
        }
      }

      if (count($stones) > 0) {
        MailsFactory::sendStonesDeliveredExtra($stones);
      }


      RestUtils::sendResponseOK("Stenen verlies verwerkt.");

    }


    /**
     * Stenen opgehaald door vrachtwagen
     */
    public function executeSendstones() {

      $request_vars = $this->data->getData();

      $json = RestUtils::getPostvaluesByKey("json", $request_vars);

      //logToFile('montage',print_r($request_vars, true));
      logToFile('stones', print_r($json, true));

      $remark = "";
      $quotationsReceived = [];
      $stoneorders = [];
      $stoneorderitems_delivered = [];
      foreach ($json as $stoneorder_in) {
        $stoneorder = null;
        if (isset($stoneorders[$stoneorder_in->id])) {
          $stoneorder = $stoneorders[$stoneorder_in->id];
        }
        else {
          $stoneorder = StoneOrderItem::find_by_id($stoneorder_in->id);
          if (!$stoneorder) {
            logToFile('stones', "StoneOrderItem niet gevonden!? " . print_r($stoneorder_in, true));
            continue;
          }
          $stoneorder->quotations = StoneOrderItemQuotation::getQuotationsByItem($stoneorder->id);
          $stoneorders[$stoneorder->id] = $stoneorder;
        }

        if ($stoneorder_in->remark != "") {
          $remark .= $stoneorder_in->remark . "\n";
        }

        $stone = Stones::find_by(["stoneId" => $stoneorder_in->stone->stoneId]);
        if ($stoneorder_in->receivedsize != "" && $stoneorder_in->receivedsize > 0) {
          $stoneorder->receivedsize_now = 0;
          if ($stoneorder->receiveddate == "") {
            if ($stoneorder->receivedsize == "") {
              $stoneorder->receivedsize = 0;
            }
            $stoneorder->receivedsize += $stoneorder_in->receivedsize;
            $stoneorder->receivedsize_now = $stoneorder_in->receivedsize;

            //alles opgehaald, zet receiveddate
            if ($stoneorder->receivedsize >= $stoneorder->size) {
              $stoneorder->receiveddate = date("Y-m-d H:i:s");
            }

            //voorraad ophogen
            $stone->addStock($stoneorder_in->receivedsize);

            $stoneorder->save();

            //de quotations bij deze bestellingen krijgen ook deze waarde
            foreach ($stoneorder->quotations as $quot) {
              $quotationsReceived[$quot->quotationId] = $quot->quotationId;
            }

            $stoneorderitems_delivered[$stoneorder->id] = $stoneorder;

          }
        }
      }

      StoneOrderItem::checkReceivedQuotations($quotationsReceived);

      StoneOrder::checkStatus();

      MailsFactory::sendStonesDelivered($stoneorderitems_delivered, $remark);

      RestUtils::sendResponseOK("Montage verzonden.");

    }


  }