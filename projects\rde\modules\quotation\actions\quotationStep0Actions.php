<?php

  use domain\quotations\service\GetStoneCategoryInfo;

  trait quotationStep0Actions {


    public function executeWizardstep0() {
      $quotation = $this->initWizard();

      $this->step = 0;
      $this->quotation = $quotation;

      if (isset($_GET["json"])) {
        $data = file_get_contents('php://input');
        if ($post = StringHelper::getJson($data)) {
          $this->step0Post($post);
        }
        else {
          $this->step0Get();
        }
      }

    }

    public function step0Get() {
      $quotation = $this->initWizard();

      $data = new stdClass();
      $data->step = 0;
      $data->quotation = $quotation;
      $data->categories = StoneCategory::getTree(true);
      $data->isAdmin = SandboxUsers::isAdmin();
      ResponseHelper::responseSuccess($data);
    }

    public function step0Post($post) {
      $response = [];
      $response["errors"] = [];

      $quotation = $this->initWizard();

      $prevStoneCategoryId = $quotation->stoneCategoryId;
      $quotation->stoneCategoryId = trim($post->stoneCategoryId);

      if ($quotation->stoneCategoryId == "") {
        $response["errors"][] = "Selecteer uw product.";
      }

      if (count($response["errors"]) > 0) {
        ResponseHelper::responseSuccess($response);
      }

      $quotationInfo = new GetStoneCategoryInfo($quotation->stoneCategoryId);

      if (($quotationInfo->isNatuursteen() && ($quotationInfo->isBalkje() || $quotationInfo->isMuurafdekker())) && !SandboxUsers::isAdmin()) {
        //deze kun nog niet online
        ResponseHelper::responseSuccess($response, "?action=notavailable&stoneCategoryId=" . $quotation->stoneCategoryId);
      }

      if ($quotation->stoneCategoryId != $prevStoneCategoryId) {
        //stoneCategoryId aangepast.
        unset($_SESSION["wizard"]);
        $quotation = $this->initWizard();
        $quotation->stoneCategoryId = trim($post->stoneCategoryId);
      }

      $_SESSION["wizard"]['quotation'] = $quotation;
      ResponseHelper::responseSuccess($response, reconstructQuery(["step", "json"]) . "step=1");


    }

  }