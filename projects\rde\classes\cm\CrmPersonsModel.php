<?php

  AppModel::loadBaseClass('BaseCrmPersons');

  class CrmPersonsModel extends BaseCrmPersons {

    public function getNaam() {
      return $this->firstName . ' ' . $this->lastName;
    }

    public function save(&$errors = []) {
      if ($this->from_db == false) {
        $this->dateCreated = date('Y-m-d');
      }
      if ($this->dateCreated == "0000-00-00") {
        $this->dateCreated = date('Y-m-d', strtotime($this->lastUpdated));
      }
      $this->lastUpdated = date('Y-m-d H:i:s');
      if ($this->gender == "") $this->gender = null;
      return parent::save($errors);
    }

    public function getLoginLink() {
      $userhash = urlencode(EncryptionHelper::encrypt("RDEUSER#824yy" . date("Ydm")));
      $domain = SiteHost::getPrimary(1)->getDomainSmart(true);
      return $domain . "/offerte?email=" . $this->email . "&userhash=" . $userhash;
    }

  }