<?php

  AppModel::loadBaseClass('BaseGpsbuddyRde');

  class GpsbuddyRdeModel extends BaseGpsbuddyRde {

    /**
     * @param $quotationId
     * @return false|GpsbuddyRoutes
     */
    public static function getDeliverRoute($quotationId) {
      $grde = GpsbuddyRde::find_by(["quotationId" => $quotationId, "bakId" => 0]);
      if (!$grde) {
        return false;
      }
      return GpsbuddyRoutes::find_by(["routeId" => $grde->routeId]);
    }

  }