<?php

  AppModel::loadBaseClass('BaseGluePrices');

  class GluePricesModel extends BaseGluePrices {

    public static function getLatestPrices() {
      return AppModel::mapObjectIds(GluePrices::find_all("WHERE validFrom<NOW() AND validTo>NOW()"));
    }

    public function getValidFrom($format = 'd-m-Y') {
      if ($this->validFrom == "0000-00-00" || $this->validFrom == "") {
        return "";
      }
      return date($format, strtotime($this->validFrom));
    }

    public function getValidTo($format = 'd-m-Y') {
      if ($this->validTo == "0000-00-00" || $this->validTo == "") {
        return "";
      }
      return date($format, strtotime($this->validTo));
    }

  }