<?php

  AppModel::loadBaseClass('BaseQuotationsExtra');

  class QuotationsExtraModel extends BaseQuotationsExtra {

    /**
     * Handmatig, anders hele object nalopen...
     * @param $quotationId
     */
    public static function manualOrdereddate($quotationId) {
      $query = "UPDATE " . QuotationsExtra::getTablename() . " SET stoneOrdered=1, stoneOrderedDate='" . date("Y-m-d") . "' WHERE quotationId=" . $quotationId;
      DBConn::db_link()->query($query);
    }

    public static function manualStoneDeliverydate($quotationId) {
      $query = "UPDATE " . QuotationsExtra::getTablename() . " SET stoneDeliveryDate='" . date("Y-m-d") . "' WHERE quotationId=" . $quotationId;
      DBConn::db_link()->query($query);
    }

    public function isPickup() {
      return $this->addressDeliveryId == 20357 ? true : false;
    }

    public function getQuotationAltPriceYear($format = 'Y') {
      if ($this->quotationAltPriceYear == "") {
        return date($format);
      }
      return date($format, strtotime($this->quotationAltPriceYear));
    }

    /**
     * Zet de maxmale element lengte
     * @param Quotations $quotation
     * @param false|CrmCompanies $company
     * @param Stones $stone
     * @return void
     */
    public function setMaxMeasureElement($quotation, $company, $stone) {
      if ($stone->isBeton()) {
        $this->maxMeasureElement = 2200;
      }
      elseif ($stone->isNatuursteen()) {
        if ($stone->brandId == 4) {
          $this->maxMeasureElement = 2200;
        }
        else {
          $this->maxMeasureElement = 2400;
        }
      }
      elseif ($company && $company->noRack == 1) {
        $this->maxMeasureElement = 2400;
      }
      elseif (($company && $company->noContainer == 1) || $quotation->meters > 80) {
        $this->maxMeasureElement = 3000;
      }
      else {
        $this->maxMeasureElement = 2400;
      }
    }


    public function save(&$errors = []) {
      if ($this->stoneOrderedDate == "0000-00-00") {
        $this->stoneOrderedDate = null;
      }
      if ($this->quotationAltPriceYear == "0000-00-00") {
        $this->quotationAltPriceYear = null;
      }
      if ($this->stoneDeliveryDate == "0000-00-00") {
        $this->stoneDeliveryDate = null;
      }

      return parent::save($errors);
    }


  }