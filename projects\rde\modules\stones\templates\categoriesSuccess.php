<?php TemplateHelper::includePartial('_tabs.php', 'stones'); ?>

<form method="post">
    <div class="box">
      <a href="<?php echo reconstructQueryAdd(array('pageId')).'action=categoryedit&parent_id=' . $catid ?>" class="gsd-btn gsd-btn-primary">Nieuwe categorie toevoegen</a>

      <input type="button" id="sortablecontrol" name="" value="Sorteren aanzetten"  class="gsd-btn gsd-btn-secondary"/> <?php echo showHelpButton("Door op 'sorteren aanzetten' te klikken wordt het mogelijk om de rijen van de tabel te verslepen, en zo de volgorde van de vensterbanken aan te passen.",'Sorteren'); ?>
      <br/><br/>

    </div>
  </form>

  <div id="message"></div>

  <?php if(count($categories)==0): ?>
    <Br/>
    Geen subcategorieën gevonden.
  <?php else: ?>
    <table id="sortable" class="default_table">

      <tr class="dataTableHeadingRow nodrop nodrag">
        <td class="sort-td"></td>
        <td><?php echo __('Naam');?></td>
        <td style="text-align: center;width: 60px;" class="qtipa" title="<?php echo __('Zet categorie online/offline');?>"><?php echo __('Online');?></td>
        <td style="text-align: center; width: 60px;"><?php echo __('Bewerk') ?></td>
        <td style="text-align: center; width: 60px;"><?php echo __('Verwijder') ?></td>
      </tr>

      <?php foreach ($categories as $category): ?>
        <tr class="dataTableRow trhover" id="<?php echo $category->id ?>">
          <td class="sort-td"><span class="fa fa-bars"></span></td>
          <td><a href="<?php echo reconstructQueryAdd(array('pageId'))?>catid=<?php echo $category->id ?>"><?php echo $category->name ?></a></td>
          <td style="text-align: center;">
            <?php if($category->online==1): ?>
              <?php echo IconHelper::getCheckboxGreen() ?>
            <?php endif; ?>
          </td>
          <td style="text-align: center;">
            <?php echo BtnHelper::getEdit(reconstructQueryAdd(['pageId']) . "action=categoryedit&id=" . $category->id) ?>
          </td>
          <td style="text-align: center;">
            <?php echo BtnHelper::getRemove(reconstructQueryAdd(['pageId']) . "action=categorydelete&delid=" . $category->id, __('Let op: U verwijdert ook eventuele subcategorieën.\nWeet u zeker dat u deze categorie wilt verwijderen?')."\n".$category->name) ?>
          </td>
        </tr>
      <?php endforeach; ?>
  </table>
  <br/>
<?php endif; ?>
<?php if(isset($stones) && count($stones)>0):  ?>
  <h3>Producten in categorie <?php if($category) echo $category->name ?></h3>
  <table id="prod" class="default_table">
    <tr class="dataTableHeadingRow">
      <td><?php echo __('Merk');?></td>
      <td><?php echo __('Naam');?></td>
      <td><?php echo __('Type');?></td>
      <td><?php echo __('Acties')?></td>
    </tr>
    <?php
      /** @var Stones $stone */
      foreach ($stones as $stone): ?>
      <tr class="dataTableRow trhover">
        <td><?php echo $brands[$stone->brandId]->name ?></td>
        <td><?php echo $stone->name ?></td>
        <td><?php echo Stones::TYPES[$stone->type] ?></td>
        <td>
          <?php echo BtnHelper::getEdit(PageMap::getUrl("M_STONES_STONES").'?action=stoneedit&id='.$stone->stoneId) ?>
        </td>
      </tr>
    <?php endforeach; ?>
  </table>
<?php endif; ?>

<script type="text/javascript">
  $(document).ready(function() {

    gsdRowSorter("<?php echo reconstructQuery() ?>action=categorymove&");

  });
</script>