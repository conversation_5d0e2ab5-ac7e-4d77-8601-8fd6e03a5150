<?php

  namespace domain\elements\service\helpers;

  class CheckMitreElementParts extends SplitElementParts
  {
    public function checkMitreElementParts($aElements, $aMitre, $voeg, $mitreLengthSizeLeft, $mitreLengthSizeRight, $shorter): array {
      $amount = $aMitre['amount'] ?? 0;

      return match ($amount) {
        2 => $this->handleTwoMitres($aElements, $voeg, $mitreLengthSizeLeft, $mitreLengthSizeRight),
        1 => $this->handleOneMitre($aElements, $aMitre['side'] ?? '', $voeg, $mitreLengthSizeLeft, $mitreLengthSizeRight, $shorter),
        default => $this->handleNoMitres($aElements),
      };
    }

    private function handleTwoMitres(array $aElements, $voeg, $mitreLengthSizeLeft, $mitreLengthSizeRight): array
    {
      $parts = $this->assignCommonParts($aElements, true);
      $first = reset($aElements);
      $last = end($aElements);

      $parts['firstLength2'] = $this->elementCheckMitreTwo($first, $mitreLengthSizeLeft, $voeg);
      $parts['firstLengthCheck'] = $parts['firstLength2'];
      $parts['lastLength2'] = $this->elementCheckMitreTwo($last, $mitreLengthSizeRight, $voeg);
      $parts['lastLengthCheck'] = $parts['lastLength2'];

      return $parts;
    }

    private function handleOneMitre(array $aElements, string $side, $voeg, $mitreLengthSizeLeft, $mitreLengthSizeRight, $shorter): array
    {
      $parts = $this->assignCommonParts($aElements, false);
      $last = end($aElements);
      $length = $side === 'left' ? $mitreLengthSizeLeft : $mitreLengthSizeRight;

      $parts['lastLength2'] = $this->elementCheckMitreOne($last, $length, $voeg, $shorter);
      $parts['lastLengthCheck'] = $parts['lastLength2'];

      return $parts;
    }

    private function handleNoMitres(array $aElements): array
    {
      $parts = $this->assignCommonParts($aElements, false);
      $parts['firstLength2'] = '';
      $parts['firstLengthCheck'] = $parts['firstLength1'];
      $parts['lastLength2'] = '';
      $parts['lastLengthCheck'] = $parts['lastLength1'];

      return $parts;
    }

    /**
     * Deelt gedeelde onderdelen toe voor 0, 1 en 2 mitres
     */
    private function assignCommonParts(array $aElements, bool $assignFirstLength2 = false): array
    {
      $parts = [];
      $lastIndex = count($aElements) - 1;

      foreach ($aElements as $i => $element) {
        if ($i === 0) {
          $parts['firstLength1'] = $element;
          if (!$assignFirstLength2) {
            $parts['firstLength2'] = '';
            $parts['firstLengthCheck'] = $element;
          }
        } elseif ($i === 1) {
          $parts['bLength1'] = $element;
          $parts['bLength1Check'] = $element;
        } elseif ($i === 2 && $i !== $lastIndex) {
          $parts['cLength1'] = $element;
          $parts['cLength1Check'] = $element;
        }

        if ($i === $lastIndex) {
          $parts['lastLength1'] = $element;
          if (!$assignFirstLength2) {
            $parts['lastLength2'] = '';
            $parts['lastLengthCheck'] = $element;
          }
        }
      }

      return $parts;
    }
  }