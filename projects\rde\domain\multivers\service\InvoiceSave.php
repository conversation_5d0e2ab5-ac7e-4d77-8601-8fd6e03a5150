<?php

  namespace domain\multivers\service;

  use gsdfw\domain\multivers\exception\MultiversException;
  use gsdfw\domain\multivers\model\CustomerInvoiceLinesModel;
  use gsdfw\domain\multivers\model\CustomerInvoiceModel;
  use gsdfw\domain\multivers\service\CustomerInvoice;
  use gsdfw\domain\multivers\service\MultiversApi;
  use Invoices;
  use SandboxUsers;

  /**
   * Class InvoiceSave
   * Save invoice and customer to mulitver
   *
   * @package domain\multivers\service
   */
  class InvoiceSave {

    private $multivers_api;

    public function __construct(MultiversApi $multivers_api) {
      $this->multivers_api = $multivers_api;
    }

    /**
     * Send invoice to multivers
     * @param Invoices $invoice
     * @return bool|CustomerInvoiceModel
     * @throws MultiversException
     */
    public function add(Invoices $invoice) {

      $user = SandboxUsers::getUserAndCompany($invoice->userId);
      if (!$user) {
        throw new MultiversException("SandboxUser niet gevonden. Userid: " . $invoice->userId);
      }

      $customer_invoice = new CustomerInvoice($this->multivers_api);

      //controleer eerst of factuur al niet in multivers staat.
      if (DEVELOPMENT) {
        $invoice->invoiceNumber = 40000;
      }

//      $customer = new Customer($this->multivers_api);
//      $cm = $customer->exists(115035); //bestaat klant?
//      pd($cm);ResponseHelper::exit();

//      pd($customer_invoice->retrieveById("30343"));

      if ($customer_invoice->exists($invoice->invoiceNumber)) {
        if ($invoice->send_multivers != 1) {
          $invoice->send_multivers = 1;
          $invoice->save();
        }
        return false;
      }

      //company data naar multivers (nieuwe aanmaken of updaten klantgegevens)
      $customer_save = new CustomerSave($this->multivers_api, $user);
      $cm = $customer_save->save();
      if (!$cm) return false;

      $country_group = 'nl';
      if ($user->country != "NL") {
        $country_group = 'eu';
      }
      $multivers_ledger_codes = $this->multivers_api->getLedgerCodes($country_group);
      $multivers_btw_codes = $this->multivers_api->getVatCodes();
      $invoice_vat = $invoice->getVat();
      if (!isset($multivers_ledger_codes[$invoice_vat])) {
        throw new MultiversException("Grootboeknummer niet beschikbaar voor dit land en btw. Land: " . $user->country . ", Btw: " . $invoice_vat);
      }

      $total_excl = round($invoice->totalProjectValue / (1 + ($invoice_vat / 100)), 2);
      $vat = $invoice->totalProjectValue - $total_excl;

      $cim = new CustomerInvoiceModel();
      $cim->setNew(true);
      $cim->set("invoiceId", $invoice->invoiceNumber);
      $cim->set("amountTotal", $invoice->totalProjectValue);
      $cim->set("amountTotalCur", $invoice->totalProjectValue);
      $cim->set("totalAmountVatExcl", $total_excl);
      $cim->set("totalAmountVatExclCur", $total_excl);
      $cim->set("canChange", 1);
      $cim->set("customerId", $user->companyId);
      $cim->set("fiscalYear", $invoice->getDateInvoice("Y"));
      $cim->set("periodNumber", intval($invoice->getDateInvoice("m")));
      $cim->set("invoiceDate", $invoice->getDateInvoice("d-m-Y"));
      $cim->set("vatOnInvoice", 1);
      $cim->set("journalId", 80);

      $cim->set("paymentConditionId", $cm->get("paymentConditionId"));

      //        $cim->set("journalSection",1);
      //        $cim->set("vatScenarioId",6);

      //voor elke invoice regel een CustomerInvoiceLinesModel en vatTransactionLine
      $customerInvoiceLinesModels = [];
      $vatTransactionLines = [];

      //voor elke invoice regel een invoiceline
      $cilm = new CustomerInvoiceLinesModel();
      $cilm->setNew(true);
      $cilm->set("accountId", $multivers_ledger_codes[$invoice_vat]);
      $cilm->set("canChange", 1);
      $cilm->set("creditAmount", $total_excl);
      $cilm->set("creditAmountCur", $total_excl);
      $cilm->set("vatAmount", $vat);
      $cilm->set("vatAmountCur", $vat);
      $cilm->set("transactionDate", $invoice->getDateInvoice("d-m-Y"));
      $cilm->set("vatCodeId", $multivers_btw_codes[$invoice_vat]);
      $customerInvoiceLinesModels[] = $cilm;
      //per invoice regel ook een vat regel
      $vatTransactionLine = [
        'amountTurnoverCur' => $total_excl,
        'canChange'         => 1,
        'vatAmountCur'      => $vat,
        'vatCodeId'         => $multivers_btw_codes[$invoice_vat],
      ];
      $vatTransactionLines[] = $vatTransactionLine;

      $cim->set("customerInvoiceLines", $customerInvoiceLinesModels);
      $cim->set("vatTransactionLines", $vatTransactionLines);

      $cim = $cim->isNew() ? $customer_invoice->add($cim) : $customer_invoice->put($cim);
      if ($cim) { //succes
        $invoice->send_multivers = 1;
        $invoice->save();
      }
      return $cim;
    }


  }