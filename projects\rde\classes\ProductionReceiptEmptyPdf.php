<?php
  /**
   * Class for generating an empty PDF when no products are found
   */
  class ProductionReceiptEmptyPdf extends GSDPDF {
    private $quotationId;

    public function __construct($quotationId) {
      parent::__construct();
      $this->quotationId = $quotationId;
    }

    public function generate() {
      $this->AddPage();
      $this->SetFont('Arial', 'B', 16);
      $this->Cell(0, 10, 'Geen producten gevonden voor deze offerte', 0, 1, 'C');
      $this->SetFont('Arial', '', 12);
      $this->Cell(0, 10, 'Offerte ID ' . $this->quotationId, 0, 1, 'C');

      $filename = 'empty_production_receipt_' . $this->quotationId . '.pdf';
      $this->Output("I", $filename);

      return $filename;
    }
  }