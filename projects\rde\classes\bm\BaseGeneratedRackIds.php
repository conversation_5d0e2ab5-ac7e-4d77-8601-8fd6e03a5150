<?php
class BaseGeneratedRackIds extends AppModel
{
  const DB_NAME = 'rde_b1mo';
  const TABLE_NAME = 'generated_rack_ids';
  const OM_CLASS_NAME = 'GeneratedRackIds';
  const columns = ['rackId', 'rackCode', 'rackScanCode', 'datetime', 'pdfId'];
  const field_structure = [
    'rackId'                      => ['type' => 'int', 'length' => '11', 'null' => false],
    'rackCode'                    => ['type' => 'varchar', 'length' => '255', 'null' => false],
    'rackScanCode'                => ['type' => 'varchar', 'length' => '255', 'null' => false],
    'datetime'                    => ['type' => 'datetime', 'length' => '', 'null' => false],
    'pdfId'                       => ['type' => 'int', 'length' => '11', 'null' => false],
  ];

  protected static $primary_key = ['rackId'];
  protected $auto_increment = 'rackId';

  public $rackId, $rackCode, $rackScanCode, $datetime, $pdfId;

  function __construct($obj_arr = [], &$error_codes = []) {
    parent::__construct();
    $this->setDefaults();
    if (!empty($obj_arr)) {
      $this->from_array($obj_arr);
      $this->valid_required_fields($error_codes);
    }
  }

  /**
  * Set default object values
  **/
  public function setDefaults() {
  }

  /**
   * @param array $conditions (required)
   * @param string $raw_sql (optional)
   * @return GeneratedRackIds[]
   **/
  public static function find_all_like($conditions, $raw_sql = '') {
    return parent::generic_find_all_like($conditions, static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param array $conditions (required)
   * @param string $raw_sql (optional)
   * @return GeneratedRackIds[]
   **/
  public static function find_all_by($conditions, $raw_sql = '') {
    return parent::generic_find_all_by($conditions, static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param string $raw_sql (optional)
   * @return GeneratedRackIds[]
   **/
  public static function find_all($raw_sql = '') {
    return parent::generic_find_all(static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param array $conditions (required)
   * @param string $raw_sql (optional)
   * @return GeneratedRackIds
   **/
  public static function find_by($conditions, $raw_sql = '') {
    return parent::generic_find_by($conditions, static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param string $id (required)
   * @param string $raw_sql (optional)
   * @return GeneratedRackIds
   **/
  public static function find_by_id($id, $raw_sql = '') {
    return parent::generic_find_by_id($id, static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param array $conditions (required)
   * @param string $raw_sql (optional)
   * @return int
   **/
  public static function count_all_by($conditions, $raw_sql = '') {
    return parent::generic_count_all_by($conditions, static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param array $conditions (required)
   * @param string $raw_sql (optional)
   * @return bool
   **/
  public static function delete_by($conditions, $raw_sql = '') {
    return parent::generic_delete_by($conditions, static::OM_CLASS_NAME, $raw_sql);
  }

}