<?php

  use Gsd\DataTable\DataTable;
  use Gsd\Form\Elements\Select;
  use Gsd\Form\ModelForm;

  class documentsRdeActions extends gsdActions {

    //Sandboxdocs
    public function executeSandboxdocslist() {
      $this->createSandboxdocsListFilters();
    }

    public function createSandboxdocsListFilters() {

      $dataTable = new DataTable('sandboxdocs');
      $dataTable->setRequestUrl(reconstructQueryAdd(['pageId']) . "action=sandboxdocslistajax");
      $dataTable->addColumnHelper("title", "Omschrijving");
      $dataTable->addColumnHelper("uploadDate", "Datum");
      $dataTable->addColumnHelper("actions", "Acties");

      $dataTable->getColumn("actions")->setSortable(false);
      $dataTable->getColumn("actions")->setClass("docs_actions");
      $dataTable->addSearchInput();
      $dataTable->addSearchReset();

      $dataTable->handleRequest($_POST);

      $this->dataTable = $dataTable;
    }

    public function executeSandboxdocsListajax() {

      $this->createSandboxdocsListFilters();

      /** FILTER QUERY */
      $filter_query = 'WHERE (companyId = 0 OR companyId IS NULL) ';

      if ($this->dataTable->hasFormElementValue("search")) {
        $searchstr = escapeForDB($this->dataTable->getFormElementValue("search"));
        $filter_query .= "AND ( ";
        $filter_query .= "files.title LIKE '%" . $searchstr . "%' ";
        $filter_query .= ") ";
      }

      $filter_query .= 'AND categoryId = 4 ';

      /** TOTALS */
      $total_count = Files::count_all_by([]);
      $total_count_filtered = Files::count_all_by([], $filter_query);

      /** GET DATA */
      $query = "SELECT * FROM " . Files::getTablename() . " ";
      $query .= $filter_query;
      $query .= $this->dataTable->getSortQuery();
      $query .= $this->dataTable->getPager()->getLimitQuery();

      $result = DBConn::db_link()->query($query);

      $table_data = [];
      while ($row = $result->fetch_array()) {
        $column_count = 0;

        $file = new Files();
        $file->hydrate($row, $column_count);

        $actions_html = "";
        $actions_html .= " " . BtnHelper::getEdit('?action=editSandboxdoc&id=' . $file->fileId);
        $actions_html .= " " . BtnHelper::getRemove('?action=deleteSandboxdoc&id=' . $file->fileId);
        $actions_html .= " " . BtnHelper::getPrintPDF('?action=viewpdf&is_factuur=0&id=' . $file->fileId, __('Bekijk pdf'), '_blank');


        $table_data[] = [
          'DT_RowId'   => $file->fileId,
          'title'      => $file->title,
          'uploadDate' => $file->uploadDate,
          'actions'    => $actions_html,
        ];
      }

      /** RETURN DATA AS JSON */
      ResponseHelper::exitAsJson([
        'data'            => $table_data,
        'recordsTotal'    => $total_count,
        'recordsFiltered' => $total_count_filtered,
        'draw'            => (int)$_POST['draw'],
      ]);
    }

    public function executeEditSandboxdoc() {
      $errors = [];

      if (empty($_GET['id'])) {
        ResponseHelper::redirectError();
      }
      $file = Files::find_by(['fileId' => $_GET['id']]);

      if (isset($_POST['save']) || isset($_POST['savelist'])) {
        if (empty($_POST['date'])) {
          $errors[] = 'Er is geen geldige datum opgegeven';
        }
        if (!empty($_POST['company_id'])) {
          $file->companyId = $_POST['company_id'];
        }
        $file->title = $_POST['title'];
        $file->uploadDate = $_POST['date'];
        $file->notes = $_POST['notes'];
        $file->uploadAlert = isset($_POST['upload_alert']) ? 1 : 0;

        if (count($errors) == 0) {
          $file->save();
          if (isset($_POST['savelist'])) {
            ResponseHelper::redirectMessage("Bestand opgeslagen", reconstructQueryAdd());
          }
          ResponseHelper::redirectMessage("Bestand opgeslagen", reconstructQuery());
        }
      }

      $this->file = $file;
      $this->company = CrmCompanies::find_by(['companyId' => $file->companyId]);
      $this->errors = $errors;

    }

    public function executeDeleteSandboxdoc() {
      if (empty($_GET['id'])) {
        ResponseHelper::redirectAlertMessage("Project niet gevonden", reconstructQueryAdd());
      }

      $file = Files::find_by(['fileId' => $_GET['id']]);
      if (!$file) {
        ResponseHelper::redirectAlertMessage("Project niet gevonden", reconstructQueryAdd());
      }

      if ($file) {
        $file->destroy();
      }

      ResponseHelper::redirectMessage("Project succesvol verwijderd", reconstructQueryAdd());
    }

    public function executeCompanysearch() {
      $this->template_wrapper_clear = true;
    }

    public function executeCompanysearchgo() {
      $searchval = DbHelper::escape(trim($_GET['val']));
      $filtquery = "WHERE 1";
      if ($searchval != "") {
        $filtquery .= " AND (";
        $filtquery .= " companyId LIKE '%" . $searchval . "%'";
        $filtquery .= " OR name LIKE '%" . $searchval . "%'";
        $filtquery .= " )";
      }
      $filtquery .= " LIMIT 75";

      $this->companies = CrmCompanies::find_all($filtquery);

      $this->template_wrapper_clear = true;
      $this->template = "_companysearch.php";

    }

    public function executeViewpdf() {

      $full_path = substr(DIR_ROOT_HTTPDOCS, 0, -1);
      if ($_GET['is_factuur'] == 0) {
        $pdf = Files::find_by(['fileId' => $_GET['id']]);
        $full_path .= $pdf->folder . $pdf->filename;
      }
      if ($_GET['is_factuur'] == 1) {
        $pdf = InvoicesOld::find_by(['oldInvoiceId' => $_GET['id']]);
        $full_path .= '/' . $pdf->folder . '/' . $pdf->filename;
      }

      if (!$pdf) {
        ResponseHelper::redirectAlertMessage("Deze PDF is niet beschikbaar.", PageMap::getUrl("M_DOCUMENTS"));
      }

      if (!file_exists($full_path)) {
        ResponseHelper::redirectAlertMessage("De PDF is niet gevonden op de server??", PageMap::getUrl("M_DOCUMENTS"));
      }

      FileHelper::getFileAndOuput($full_path);
      ResponseHelper::exit();
    }

    //Sandboxdocs 2
    public function executeSandboxdocs2list() {
      $this->createSandboxdocs2ListFilters();
    }

    public function createSandboxdocs2ListFilters() {
      Context::addStylesheet(URL_INCLUDES . "vendor/ivaynberg/select2/dist/css/select2.min.css");
      Context::addJavascript(URL_INCLUDES . "vendor/ivaynberg/select2/dist/js/select2.min.js");
      Context::addJavascript(URL_INCLUDES . "vendor/ivaynberg/select2/dist/js/i18n/nl.js");

      $dataTable = new DataTable('sandboxdocs2');
      $dataTable->setRequestUrl(reconstructQueryAdd() . "action=sandboxdocs2listajax");
      $dataTable->addColumnHelper("uploadDate", "Datum");
      $dataTable->addColumnHelper("name", "Bedrijfsnaam");
      $dataTable->addColumnHelper("title", "Omschrijving");
      $dataTable->addColumnHelper("notes", "Opmerkingen");
      $dataTable->addColumnHelper("uploadAlert", "Delen met accountant");
      $dataTable->addColumnHelper("actions", "Acties");

      $dataTable->getColumn("actions")->setSortable(false);
      $dataTable->getColumn("actions")->setClass("docs_actions");
      $dataTable->setDefaultSort("uploadDate", "Desc");

      $dataTable->addSearchInput("Bedrijfsnaam");

      $input_year = new Select("Jaar", "year");
      $input_year->addOptionHelper('', __("Jaar..."));
      for ($tel = date("Y"); $tel > 2011; $tel--) {
        $input_year->addOptionHelper($tel, $tel);
      }
      $dataTable->getForm()->addElement($input_year);

      $input_month = new Select("Maand", "month");
      $input_month->addOptionHelper('', __("Maand..."));
      for ($m = 1; $m <= 12; $m++) {
        $month = date('F', mktime(0, 0, 0, $m, 1, date('Y')));
        $input_month->addOptionHelper($m, $month);
      }
      $dataTable->getForm()->addElement($input_month);

      $dataTable->addSearchReset();

      $dataTable->handleRequest($_POST);

      $this->dataTable = $dataTable;
    }

    public function executeSandboxdocs2Listajax() {

      $this->createSandboxdocs2ListFilters();

      $filter_query = "";
      $filter_query .= "JOIN " . CrmCompanies::getTablename() . " ON crm_companies.companyId = files.companyId ";
      $filter_query .= "WHERE files.categoryId = 4 ";

      if ($this->dataTable->hasFormElementValue("search")) {
        $searchstr = DbHelper::escape($this->dataTable->getFormElementValue("search"));
        $filter_query .= "AND ( ";
        $filter_query .= "crm_companies.name LIKE '%" . $searchstr . "%' ";
        $filter_query .= ") ";
      }

      if ($this->dataTable->hasFormElementValue("year")) {
        $year = DbHelper::escape($this->dataTable->getFormElementValue("year"));
        $filter_query .= "AND YEAR(files.uploadDate) = " . $year . " ";
      }
      if ($this->dataTable->hasFormElementValue("month")) {
        $month = DbHelper::escape($this->dataTable->getFormElementValue("month"));
        $filter_query .= "AND MONTH(files.uploadDate) = " . $month . " ";
      }

      /** TOTALS */
      $total_count = Files::count_all_by([]);
      $total_count_filtered = Files::count_all_by([], $filter_query);

      /** GET DATA */
      $query = "SELECT * FROM " . Files::getTablename() . " ";
      $query .= $filter_query;
      $query .= $this->dataTable->getSortQuery();
      $query .= $this->dataTable->getPager()->getLimitQuery();

      $result = DBConn::db_link()->query($query);

      $table_data = [];
      while ($row = $result->fetch_array()) {
        $column_count = 0;

        $file = (new Files())->hydrateNext($row, $column_count);
        $company = (new CrmCompanies())->hydrateNext($row, $column_count);

        //delen met accountant
        if ($file->uploadAlert == 1) {
          $upload_alert = IconHelper::getAlert();
        }
        else {
          $upload_alert = '';
        }

        $actions_html = "";
        $actions_html .= BtnHelper::getEdit('?action=editSandboxdoc&id=' . $file->fileId) . " ";
        $actions_html .= " " . BtnHelper::getRemove('?action=deleteSandboxdoc&id=' . $file->fileId);
        $actions_html .= " " . BtnHelper::getPrintPDF('?action=viewpdf&is_factuur=0&id=' . $file->fileId, __('Bekijk pdf'), '_blank');

        $table_data[] = [
          'DT_RowId'    => $file->fileId,
          'uploadDate'  => $file->uploadDate,
          'name'        => $company->name,
          'title'       => $file->title,
          'notes'       => $file->notes,
          'uploadAlert' => $upload_alert,
          'actions'     => $actions_html,
        ];
      }

      /** RETURN DATA AS JSON */
      ResponseHelper::exitAsJson([
        'data'            => $table_data,
        'recordsTotal'    => $total_count,
        'recordsFiltered' => $total_count_filtered,
        'draw'            => (int)$_POST['draw'],
      ]);
    }

    //oude offertes
    public function executeOldquotationslist() {
      $this->createOldquotationsListFilters();
    }

    public function createOldquotationsListFilters() {
      $dataTable = new DataTable('old_quotations');
      $dataTable->setRequestUrl(reconstructQueryAdd() . "action=oldquotationslistajax");
      $dataTable->addColumnHelper("name", "Bedrijfsnaam");
      $dataTable->addColumnHelper("email", "Email");
      $dataTable->addColumnHelper("offerteNummer", "Offerte Nr.");
      $dataTable->addColumnHelper("factuurId", "Factuur id");
      $dataTable->addColumnHelper("actions", "Acties");

      $dataTable->getColumn("actions")->setSortable(false);
      $dataTable->addSearchInput();
      $dataTable->addSearchReset();

      $dataTable->handleRequest($_POST);

      $this->dataTable = $dataTable;
    }

    public function executeOldquotationsListajax() {
      $this->createOldquotationsListFilters();

      $filter_query = "LEFT JOIN " . CrmCompanies::getTablename() . " ON crm_companies.companyId = quotations_old.companyId ";

      if ($this->dataTable->hasFormElementValue("search")) {
        $searchstr = escapeForDB($this->dataTable->getFormElementValue("search"));
        $filter_query .= "WHERE crm_companies.companyId IS NOT NULL ";
        $filter_query .= "AND ( ";
        $filter_query .= "crm_companies.name LIKE '%" . $searchstr . "%' OR offerteNummer LIKE '%" . $searchstr . "%' ";
        $filter_query .= ") ";
      }

      /** TOTALS */
      $total_count = QuotationsOld::count_all_by([]);
      $total_count_filtered = QuotationsOld::count_all_by([], $filter_query);

      /** GET DATA */
      $query = "SELECT * FROM " . QuotationsOld::getTablename() . " ";
      $query .= $filter_query;
      $query .= $this->dataTable->getSortQuery();
      $query .= $this->dataTable->getPager()->getLimitQuery();

      $result = DBConn::db_link()->query($query);

      $table_data = [];
      while ($row = $result->fetch_array()) {
        $column_count = 0;

        $old_quotation = (new QuotationsOld())->hydrateNext($row, $column_count);
        $company = (new CrmCompanies())->hydrateNext($row, $column_count);

        $actions_html = "";
        $actions_html .= " " . BtnHelper::getPrintPDF('?action=viewhtml&id=' . $old_quotation->oldQuotationId, __('Bekijk HTML'), '_blank');

        $table_data[] = [
          'DT_RowId'      => $old_quotation->quotationsOldId,
          'name'          => $company->name,
          'email'         => $company->email,
          'offerteNummer' => $old_quotation->offerteNummer,
          'factuurId'     => $old_quotation->factuurId,
          'actions'       => $actions_html,
        ];
      }

      /** RETURN DATA AS JSON */
      ResponseHelper::exitAsJson([
        'data'            => $table_data,
        'recordsTotal'    => $total_count,
        'recordsFiltered' => $total_count_filtered,
        'draw'            => (int)$_POST['draw'],
      ]);
    }

    public function executeViewhtml() {
      $quotation_old = QuotationsOld::find_by(['oldQuotationId' => $_GET['id']]);
      $full_path = '/var/www/html/rde/' . $quotation_old->folder . '/' . $quotation_old->filename;

      if (!$quotation_old) {
        $_SESSION["flash_message_red"] = "Deze factuur is niet beschikbaar.";
        ResponseHelper::redirect(PageMap::getUrl("M_DOCUMENTS_OLD_QUOTATIONS"));
      }

      if (!file_exists($full_path)) {
        ResponseHelper::redirectAlertMessage("De offerte is niet gevonden op de server.", PageMap::getUrl("M_DOCUMENTS"));
      }

      FileHelper::getFileAndOuput($full_path);
      ResponseHelper::exit();
    }

    //oude facturen
    public function executeOldinvoiceslist() {
      $this->createOldinvoicesListFilters();
    }

    public function createOldinvoicesListFilters() {
      $dataTable = new DataTable('old_invoices');
      $dataTable->setRequestUrl(reconstructQueryAdd() . "action=oldinvoiceslistajax");
      $dataTable->addColumnHelper("name", "Bedrijfsnaam");
      $dataTable->addColumnHelper("email", "Email");
      $dataTable->addColumnHelper("factuurNummer", "Factuur Nr.");
      $dataTable->addColumnHelper("actions", "Acties");

      $dataTable->getColumn("actions")->setSortable(false);
      $dataTable->addSearchInput();
      $dataTable->addSearchReset();

      $dataTable->handleRequest($_POST);

      $this->dataTable = $dataTable;
    }

    public function executeOldinvoicesListajax() {
      $this->createOldinvoicesListFilters();

      $filter_query = "LEFT JOIN " . CrmCompanies::getTablename() . " ON crm_companies.companyId = invoices_old.companyId ";

      if ($this->dataTable->hasFormElementValue("search")) {
        $searchstr = escapeForDB($this->dataTable->getFormElementValue("search"));
        $filter_query .= "WHERE crm_companies.companyId IS NOT NULL ";
        $filter_query .= "AND ( ";
        $filter_query .= "crm_companies.name LIKE '%" . $searchstr . "%' OR factuurNummer LIKE '%" . $searchstr . "%' ";
        $filter_query .= ") ";
      }

      /** TOTALS */
      $total_count = InvoicesOld::count_all_by([]);
      $total_count_filtered = InvoicesOld::count_all_by([], $filter_query);

      /** GET DATA */
      $query = "SELECT * FROM " . InvoicesOld::getTablename() . " ";
      $query .= $filter_query;
      $query .= $this->dataTable->getSortQuery();
      $query .= $this->dataTable->getPager()->getLimitQuery();

      $result = DBConn::db_link()->query($query);

      $table_data = [];
      while ($row = $result->fetch_array()) {
        $column_count = 0;

        $old_invoice = (new InvoicesOld())->hydrateNext($row, $column_count);
        $company = (new CrmCompanies())->hydrateNext($row, $column_count);

        $actions_html = "";
        $actions_html .= " " . BtnHelper::getPrintPDF('?action=viewpdf&is_factuur=1&id=' . $old_invoice->oldInvoiceId, __('Bekijk PDF'), '_blank');

        $table_data[] = [
          'DT_RowId'      => $old_invoice->invoiceOldId,
          'name'          => $company->name,
          'email'         => $company->email,
          'factuurNummer' => $old_invoice->factuurNummer,
          'actions'       => $actions_html,
        ];
      }

      /** RETURN DATA AS JSON */
      ResponseHelper::exitAsJson([
        'data'            => $table_data,
        'recordsTotal'    => $total_count,
        'recordsFiltered' => $total_count_filtered,
        'draw'            => (int)$_POST['draw'],
      ]);
    }

    public function executeDocs() {
      $this->docs = Files::getFinanceFiles();
    }

    public function executeDocopen() {
      $file = Files::find_by(["fileId" => $_GET["id"]]);
      if (!$file) {
        MessageFlashCoordinator::addMessageAlert("Bestand niet gevonden, of u heeft geen rechten.");
        ResponseHelper::redirect(reconstructQueryAdd());
      }
      $filepath = DIR_ROOT_HTTPDOCS . $file->folder . '/' . $file->filename;
      if (!file_exists($filepath)) {
        MessageFlashCoordinator::addMessageAlert("Bestand niet gevonden.");
        ResponseHelper::redirect(reconstructQueryAdd());
      }
      FileHelper::getFileAndOuput($filepath);
      ResponseHelper::exit();
    }


    public function executeDocedit() {

      $doc = Files::find_by(["fileId" => $_GET["id"]]);
      $company = CrmCompanies::find_by(["companyId" => $doc->companyId]);

      $form = new ModelForm();
      $form->addClass("edit-form");
      $form->buildElementsFromModel($doc);
      $form->setElementsLabel([
        "uploadAlert" => "Alert",
        "notes"       => "Opmerking",
        "notesAlert"  => "Opmerking alert",
      ], true)
        ->getElement("notes")->addAtribute("rows", "8");
      $form->getElement("notesAlert")->setLabel("Opmerking alert - zet dit vinkje uit als u het bericht hebt gelezen.");

      if (isset($_POST['go']) || isset($_POST['go_list'])) {

        $form->setElementsAndObjectValue($_POST);
        if ($form->isValid()) {
          $form->getModelobject()->save();
          $_SESSION['flash_message'] = "Gegevens opgeslagen";
          if (isset($_POST['go_list'])) {
            ResponseHelper::redirect(reconstructQueryAdd());
          }
          ResponseHelper::redirect(reconstructQuery());
        }

      }

      $this->form = $form;
      $this->doc = $doc;
      $this->company = $company;
    }

  }