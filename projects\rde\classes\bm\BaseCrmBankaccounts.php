<?php
class BaseCrmBankaccounts extends AppModel
{
  const DB_NAME = 'rde_cms';
  const TABLE_NAME = 'crm_bankaccounts';
  const OM_CLASS_NAME = 'CrmBankaccounts';
  const columns = ['accountId', 'companyId', 'accountnumber', 'holder', 'IBAN', 'collection', 'country', 'debit'];
  const field_structure = [
    'accountId'                   => ['type' => 'int', 'length' => '7', 'null' => false],
    'companyId'                   => ['type' => 'int', 'length' => '7', 'null' => false],
    'accountnumber'               => ['type' => 'varchar', 'length' => '12', 'null' => true],
    'holder'                      => ['type' => 'varchar', 'length' => '150', 'null' => true],
    'IBAN'                        => ['type' => 'varchar', 'length' => '34', 'null' => true],
    'collection'                  => ['type' => 'boolean', 'length' => '1', 'null' => false],
    'country'                     => ['type' => 'varchar', 'length' => '2', 'null' => false],
    'debit'                       => ['type' => 'boolean', 'length' => '1', 'null' => false],
  ];

  protected static $primary_key = ['accountId'];
  protected $auto_increment = 'accountId';

  public $accountId, $companyId, $accountnumber, $holder, $IBAN, $collection, $country, $debit;

  function __construct($obj_arr = [], &$error_codes = []) {
    parent::__construct();
    $this->setDefaults();
    if (!empty($obj_arr)) {
      $this->from_array($obj_arr);
      $this->valid_required_fields($error_codes);
    }
  }

  /**
  * Set default object values
  **/
  public function setDefaults() {
    $this->collection = 0;
    $this->country = 'NL';
    $this->debit = 0;
  }

  /**
   * @param array $conditions (required)
   * @param string $raw_sql (optional)
   * @return CrmBankaccounts[]
   **/
  public static function find_all_like($conditions, $raw_sql = '') {
    return parent::generic_find_all_like($conditions, static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param array $conditions (required)
   * @param string $raw_sql (optional)
   * @return CrmBankaccounts[]
   **/
  public static function find_all_by($conditions, $raw_sql = '') {
    return parent::generic_find_all_by($conditions, static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param string $raw_sql (optional)
   * @return CrmBankaccounts[]
   **/
  public static function find_all($raw_sql = '') {
    return parent::generic_find_all(static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param array $conditions (required)
   * @param string $raw_sql (optional)
   * @return CrmBankaccounts
   **/
  public static function find_by($conditions, $raw_sql = '') {
    return parent::generic_find_by($conditions, static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param string $id (required)
   * @param string $raw_sql (optional)
   * @return CrmBankaccounts
   **/
  public static function find_by_id($id, $raw_sql = '') {
    return parent::generic_find_by_id($id, static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param array $conditions (required)
   * @param string $raw_sql (optional)
   * @return int
   **/
  public static function count_all_by($conditions, $raw_sql = '') {
    return parent::generic_count_all_by($conditions, static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param array $conditions (required)
   * @param string $raw_sql (optional)
   * @return bool
   **/
  public static function delete_by($conditions, $raw_sql = '') {
    return parent::generic_delete_by($conditions, static::OM_CLASS_NAME, $raw_sql);
  }

}