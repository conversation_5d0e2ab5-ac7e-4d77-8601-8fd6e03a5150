<?php TemplateHelper::includePartial('_tabs.php', 'prices'); ?>

<form method="post" action="<?php echo reconstructQueryAdd() ?>">
  <div class="box">
    <input type="text" name="bsp_search" value="<?php echo $_SESSION['bsp_search'] ?>" placeholder="Zoeken..."/>
    <select name="bsp_brand" id="bsp_brand">
      <option value="">Selecteer merk...</option>
      <?php foreach($brands as $brand): ?>
        <option value="<?php echo $brand->brandId ?>" <?php if($_SESSION['bsp_brand']==$brand->brandId) echo 'selected'; ?>><?php echo $brand->name ?></option>
      <?php endforeach; ?>
    </select>
    <select name="bsp_type" id="bsp_type">
      <option value="">Selecteer type...</option>
      <?php foreach(Stones::TYPES as $type=>$typename):
        if($type==Stones::TYPE_VENSTERBANK) continue;
        ?>
        <option value="<?php echo $type ?>" <?php if($_SESSION['bsp_type']==$type) echo 'selected'; ?>><?php echo $typename ?></option>
      <?php endforeach; ?>
    </select>
    <select name="bsp_endstone"  id="bsp_endstone">
      <option value="">Selecteer eindsteen...</option>
      <option value="true" <?php if($_SESSION['bsp_endstone']=="true") echo 'selected'; ?>>Ja</option>
      <option value="false" <?php if($_SESSION['bsp_endstone']=="false") echo 'selected'; ?>>Nee</option>
    </select>
    <select name="bsp_display"  id="bsp_display">
      <option value="">Selecteer zichtbaar...</option>
      <option value="true" <?php if($_SESSION['bsp_display']=="true") echo 'selected'; ?>>Ja</option>
      <option value="false" <?php if($_SESSION['bsp_display']=="false") echo 'selected'; ?>>Nee</option>
    </select>
    <select name="bsp_year" id="bsp_year">
      <option value="now">Huidige prijslijst</option>
      <?php for($year=date("Y")+1;$year>=2011;$year--): ?>
        <option value="<?php echo $year ?>" <?php writeIfSelectedVal($_SESSION['bsp_year'], $year) ?>><?php echo $year ?></option>
      <?php endfor ?>
    </select>
    <input type="submit" name="go" id="go" value="Zoeken" />
    <input type="submit" name="reset" id="reset" value="Reset filter" />
  </div>
<!--  <div class="box">-->
<!--    --><?php //if(count($colors)==0): ?>
<!--      <b>Selecteer merk om verder te gaan.</b>-->
<!--    --><?php //else: ?>
<!--      <div id="colors_wrapper">-->
<!--        --><?php //foreach($colors as $color): ?>
<!--          <label><input type="checkbox" class="colors" name="bsp_colors[--><?php //echo $color->colorId ?><!--]" value="--><?php //echo $color->colorId ?><!--" --><?php //if(isset($_SESSION["bsp_colors"][$color->colorId])) echo 'checked'; ?><!--/> --><?php //echo $color->name ?><!--</label>-->
<!--        --><?php //endforeach; ?>
<!--      </div>-->
<!--    --><?php //endif; ?>
<!--  </div>-->
</form>

<div class="box">

  <?php if(count($items)!=0): ?>
    <form method="get">
      <input type="hidden" name="action" value="bretistonepriceeditnew"/>
      Prijzen aanpassen welke ingaan vanaf 1 januari <select name="yearfrom" id="yearfrom">
        <?php echo getOptionVal(date("Y"), date("Y")+1, date("Y")+1) ?>
      </select>
      <input type="submit" name="edit" id="edit" value="Bewerk prijslijst" class="gsd-btn gsd-btn-primary"/>
      <?php echo showHelpButton("Met deze knop kun je de steen prijs per stuk aanpassen van de prijzen in deze lijst.","Bewerk prijslijst") ?>

      <?php if(isset($_SESSION["bsp_colors"]) && count($_SESSION["bsp_colors"])>=1): ?>
        <input type="submit" name="editbatch" id="editbatch" value="Bewerk prijslijst gegroepeerd" />
        <?php echo showHelpButton("Met deze knop zal het bewerk scherm de prijzen slim groeperen, waardoor je batch gewijs prijzen kunt aanpassen.","Bewerk prijslijst gegroepeerd") ?>
      <?php endif; ?>
    </form>
  <?php endif; ?>

  <?php if($_SESSION['bsp_year'] == "now" && $_SESSION['bsp_brand'] != ""): ?>
    <form method="post">
      Toevoegen nieuwe steenprijs
      <?php if(count($missing_sizes)==0): ?>
        <i>Geen onbrekende steenprijzen gevonden voor dit merk</i>
      <?php else: ?>
        <select name="missingsize" id="missingsize">
          <?php foreach($missing_sizes as $ms): ?>
            <option value="<?php echo $ms->stoneId ?>"><?php echo $ms->name ?></option>
          <?php endforeach; ?>
        </select>
        <input type="submit" name="add" id="add" value="Toevoegen steenprijs" />
        <?php echo showHelpButton("U kunt hier een steenprijs toevoegen van een nieuwe steen. Deze word direct toegevoegd met prijs 999.99 euro met startdatum 1 januari ".date("Y").", en kun je vervolgens bewerken.","Bewerk factorlijst") ?>
      <?php endif; ?>
    </form>
  <?php endif; ?>

</div>


  <?php $pager->writePreviousNext(); ?>

  <?php if(count($items)==0): ?>
    <br/>
    <?php if($_SESSION["bsp_brand"]!=""): ?>
      <section class="empty-list-state">
        <p><?php echo __('Er zijn geen items gevonden.') ?></p>
      </section>
    <?php endif; ?>
  <?php else: ?>
    <table class="default_table" style="width: auto;">
      <tr class="dataTableHeadingRow">
        <td>Naam</td>
        <td>Type</td>
        <td style="text-align: left">Kleur</td>
        <td style="text-align: center">Eindsteen</td>
        <td style="text-align: center">Online</td>
        <td style="text-align: right">Prijs</td>
        <td style="text-align: center">Geldig van</td>
        <td style="text-align: center">Geldig tot</td>
      </tr>
      <?php
        /** @var Stones $item */
        foreach($items as $item): ?>
        <tr class="dataTableRow trhover">
          <td><?php echo $item->name ?></td>
          <td><?php echo Stones::TYPES[$item->type] ?></td>
          <td><?php if(isset($colors[$item->colorId])) echo $colors[$item->colorId]->name ?></td>
          <td style="text-align: center"><?php echo $item->endstone=="true"?"Ja":"Nee" ?></td>
          <td style="text-align: center"><?php echo $item->display=="true"?"Ja":"Nee" ?></td>
          <td style="text-align: right"><?php echo StringHelper::asMoney($item->price->price) ?></td>
          <td style="text-align: center"><?php echo $item->price->getValidFrom() ?></td>
          <td style="text-align: center"><?php echo $item->price->getValidTo()=="31-12-9999"?"-":$item->price->getValidTo() ?></td>
        </tr>
      <?php endforeach; ?>
    </table>
  <?php endif; ?>

<script>
  $(document).ready(function() {
    $("#bsp_brand,#bsp_display,#bsp_type,#bsp_endstone,#bsp_year,.colors").change(function() {
      $("#go").click();
    })
  });
</script>
<style>
  #colors_wrapper {
  }
  #colors_wrapper label {
    padding: 5px 5px 0 0;
    display: inline-block;
  }

</style>