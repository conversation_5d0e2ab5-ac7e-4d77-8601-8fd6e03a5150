<?php

  use Gsd\DataTable\DataTable;
  use Gsd\Form\Elements\Select;

  class dashboardRdeActions extends gsdActions {

    public function executeOverview() {
      $this->createOverviewFilters();
    }

    private function createOverviewFilters() {

      $dataTable = new DataTable("large-work-datatable");
      $dataTable->setRequestUrl(reconstructQueryAdd() . "action=ajax");

      $dataTable->addColumnHelper("quotationDate", "Datum");
      $dataTable->addColumnHelper("name", "Bedrijfnaam");
      $dataTable->addColumnHelper("meters", "Meters")->addClass("dt-body-right");
      $dataTable->addColumnHelper("domestic", "Plaats");
      $dataTable->addColumnHelper("quotationNumber", "Offertenummer");
      $dataTable->addColumnHelper("projectName", "Werk");
      $dataTable->addColumn<PERSON>elper("projectReference", "Kenmerk");
      $dataTable->addColumnHelper("largeworklistinfo", "Extra info");

      $variants = [
        "overzicht",
        "opdracht",
        "gekregen",
        "onderaannemer",
        "via handel",
        "niet gekregen",
        "concurent",
        "handel",
        "te duur",
        "ander product",
        "te duur",
        "geannuleerd",
        "anders",
      ];
      $select = new Select("Variant", 'variant');
      foreach ($variants as $variant) {
        $select->addOptionHelper($variant, ucfirst($variant) . ' scherm');
      }
      $dataTable->getForm()->addElement($select);
      $dataTable->addSearchInput();


      $dataTable->addSearchReset();

      $dataTable->handleRequest($_POST);

      $this->dataTable = $dataTable;

    }


    public function executeAjax() {

      $this->createOverviewFilters();

      $filter = "LEFT JOIN " . CrmCompanies::getTablename() . " ON quotations.companyId = crm_companies.companyId ";
      $filter .= "WHERE metersMuch = 'true' ";

      $variant = $this->dataTable->getFormElementValue("variant");
      if ($variant == "opdracht") {
        $filter .= "AND statusId > 11 ";
        $filter .= "AND NOT statusId IN (70, 80) ";
      }
      elseif ($variant == "overzicht") {
        $filter .= "AND statusId IN (10, 80) ";
        $filter .= "AND largeWorkList = 'overzicht' ";
      }
      else {
        $filter .= ' AND largeWorkList = "' . $variant . '" ';
      }


      if ($this->dataTable->hasFormElementValue("search")) {
        $searchstr = escapeForDB($this->dataTable->getFormElementValue("search"));
        $filter .= "AND ( ";
        $filter .= "name LIKE '%" . $searchstr . "%' OR ";
        $filter .= "domestic LIKE '%" . $searchstr . "%' OR ";
        $filter .= "quotationNumber LIKE '%" . $searchstr . "%' OR ";
        $filter .= "projectName LIKE '%" . $searchstr . "%' ";
        $filter .= ") ";
      }

      $total_count = Quotations::count_all_by([], $filter);
      $total_count_filtered = Quotations::count_all_by([], $filter);

      $query = "SELECT * FROM " . Quotations::getTablename() . " ";
      $query .= $filter;
      if (!empty($this->dataTable->getSortColumn())) {
        $query .= $this->dataTable->getSortQuery();
      }
      else {
        $query .= " ORDER BY quotationId ASC";
      }
      $query .= $this->dataTable->getPager()->getLimitQuery();
      $result = DBConn::db_link()->query($query);

      /** @var Quotations[] $quotations */
      $quotations = [];
      while ($row = $result->fetch_row()) {
        $column_counter = 0;
        $quotation = (new Quotations())->hydrateNext($row, $column_counter);
        $quotation->company = (new CrmCompanies())->hydrateNext($row, $column_counter);
        $quotations[] = $quotation;
      }

      $table_data = [];
      foreach ($quotations as $quotation) {

        $table_data[] = [
          'DT_RowId'          => $quotation->quotationId,
          'quotationDate'     => $quotation->getQuotationDate(),
          'name'              => $quotation->company->name,
          'meters'            => $quotation->meters,
          'domestic'          => $quotation->domestic,
          //'quotationNumber'       => '<a href="' .  . '">' . $quotation->getQuotationNumberFull() . '</a>',
          'quotationNumber'   => $quotation->getQuotationNumberFull(),
          'projectName'       => $quotation->projectName,
          'projectReference'  => $quotation->projectReference,
          'largeworklistinfo' => $quotation->largeworklistinfo,
        ];
      }

      /** RETURN DATA AS JSON */
      ResponseHelper::exitAsJson([
        'data'            => $table_data,
        'recordsTotal'    => $total_count,
        'recordsFiltered' => $total_count_filtered,
        'draw'            => (int)$_POST['draw'],
      ]);
    }

  }

