<div class="box">
  <form method="post" action="<?php echo reconstructQueryAdd() ?>">
    <input type="text" name="size_search" value="<?php echo $_SESSION['size_search'] ?>" placeholder="Zoeken..."/>
    <select name="size_brand" id="size_brand">
      <option value="">Selecteer merk...</option>
      <?php foreach($brands as $brand): ?>
        <option value="<?php echo $brand->brandId ?>" <?php if($_SESSION['size_brand']==$brand->brandId) echo 'selected'; ?>><?php echo $brand->name ?></option>
      <?php endforeach; ?>
    </select>
    <input type="submit" name="go" id="go" value="Zoeken" />

  </form>
</div>

<?php if(count($quotations)==0): ?>
  <section class="empty-list-state">
    <p><?php echo __('Er zijn geen items gevonden.') ?></p>
  </section>
<?php else: ?>
  <table class="default_table" style="width: auto;">
    <tr class="dataTableHeadingRow">
      <td>Leveringsnummer
        <?php echo showHelpButton("Het leveringsnummer komt overeen met het unieke id van het afleveradres. Immers producten met hetzelfde afleveradres mogen in dezelfde bak.") ?>
      </td>
      <td>Leverweek klant</td>
      <td>Opdrachtdatum</td>
      <td>Offertenummer</td>
      <td>Merk/Materiaal</td>
      <td>Model</td>
      <td>M</td>
      <td>M2</td>
      <td>PDF</td>
      <td>Geleverd op</td>
    </tr>
    <?php
      /** @var Quotations $quotation */
      foreach($quotations as $quotation):
        $supplier_time = strtotime("-1 WEEKS",intval($quotation->getDueDate("U")));
        $stone = Stones::find_by(["stoneId" => $quotation->stoneId]);
        ?>
        <tr class="dataTableRow trhover">
          <td><?php echo $quotation->quotation_extra->addressDeliveryId ?></td>
          <td><?php echo date("Y-W",$supplier_time) ?></td>
          <td><?php echo $quotation->getProductionDate() ?></td>
          <td><?php echo $quotation->getQuotationNumberFull() ?></td>
          <td><?php echo $brands[$quotation->brandId]->name ?></td>
          <td><?php
              if($stone):
                $size = StoneSizes::find_by(["sizeId" => $stone->sizeId]);
                if($size):
                  echo $size->name;
                endif;
              endif;
            ?></td>
          <td style="text-align: right;"><?php echo $quotation->meters ?></td>
          <td style="text-align: right;"><?php $surf = $quotation->getSurface($stone); echo $surf!=0?$surf:''; ?></td>
          <td>
            <?php echo BtnHelper::getPrintPDF("?action=downloadpdf&id=".$quotation->quotationId) ?>
          </td>
          <td><?php echo $quotation->getProduceDate() ?></td>
        </tr>
      <?php endforeach; ?>
  </table>
<?php endif; ?>

<script>
  $(document).ready(function() {
    $("#size_brand,#size_display").change(function() {
      $("#go").click();
    })
  });
</script>