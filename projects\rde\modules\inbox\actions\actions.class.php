<?php

  class inboxRdeActions extends gsdActions {

    public function preExecute() {
      $this->site = $_SESSION['site'];
      if (is_numeric($this->pageId)) {
        $page = Page::getPageAndContent($this->pageId, $_SESSION['lang']);
        $this->seo_title = $page->content->getSeoTitle();
        $this->seo_description = $page->content->getSeoDescription();
        $this->page = $page;
      }
      $this->cats = Category::getShopCats();
    }

    public function executeList() {
      if (!isset($_SESSION["userObject"]) || !isset($_SESSION["userObject"]->usergroup)) {
        ResponseHelper::redirect("/");
      }

      $imessages = Imessage::find_all("ORDER BY date DESC");
      $this->imessages = $imessages;
    }

    public function executeEdit() {
      if (!isset($_SESSION["userObject"]) || !isset($_SESSION["userObject"]->usergroup)) {
        ResponseHelper::redirect("/");
      }

      $errors = [];

      $imessage = new Imessage();
      if (isset($_GET['id'])) {
        $imessage = Imessage::find_by_id($_GET['id']);
      }

      if (isset($_POST['verzend']) || isset($_POST['verzend_list'])) {

        $online_old = $imessage->online;

        $imessage->subject = trim($_POST["subject"]);
        $imessage->message = trim($_POST["message"]);
        $imessage->date = getTSFromStr($_POST["date"]);
        $imessage->construction = isset($_POST["construction"]) ? 1 : 0;
        $imessage->constructiontraders = isset($_POST["constructiontraders"]) ? 1 : 0;
        $imessage->private = isset($_POST["private"]) ? 1 : 0;
        $imessage->online = isset($_POST["online"]) ? 1 : 0;
        $imessage->homepage = isset($_POST["homepage"]) ? 1 : 0;

        if ($imessage->subject == "") {
          $errors['subject'] = "Onderwerp is verplicht";
        }
        if ($imessage->getDate() == "") {
          $errors['date'] = "Datum is verplicht";
        }
        if ($imessage->message == "") {
          $errors['message'] = "Bericht is verplicht";
        }
        if ($imessage->construction == 0 && $imessage->constructiontraders == 0 && $imessage->private == 0) {
          $errors['message'] = "Selecteer minstens 1 groep";
        }

        if (count($errors) == 0) {

          $imessage->possible_readers = $imessage->getPossibleReaders();
          $imessage->save();

          if ($imessage->online != $online_old) {
            //online is veranderd, dan verwijderen lezers
            ImessageRead::delete_by(["imessage_id" => $imessage->id]);
          }

          $_SESSION['flash_message'] = "Bericht is opgeslagen";
          if (isset($_POST['verzend_list'])) {
            ResponseHelper::redirect(PageMap::getUrl('M_INBOX'));
          }
          else {
            ResponseHelper::redirect(PageMap::getUrl('M_INBOX', ['action' => 'edit', 'id' => $imessage->id]));
          }
        }

      }
      elseif ($imessage->id == null) {
        $imessage->message = '<h1>Titel</h1><p>Inhoud</p>';
      }
      $this->errors = $errors;
      $this->imessage = $imessage;
    }

    public function executeDelete() {
      if (!isset($_SESSION["userObject"]) || !isset($_SESSION["userObject"]->usergroup)) {
        ResponseHelper::redirect("/");
      }
      if (isset($_GET['id'])) {
        $spr = Imessage::find_by_id($_GET['id']);
        if ($spr) {
          $spr->destroy();
          $_SESSION['flash_message'] = "Bericht is verwijderd.";
          ResponseHelper::redirect(reconstructQueryAdd());
        }
      }

      $_SESSION['flash_message_red'] = "Bericht verwijderd.";
      ResponseHelper::redirect(reconstructQueryAdd());
    }

    public function executeOverview() {
      if (!isset($_SESSION["userObject"])) { //uitgelogd
        ResponseHelper::redirect(PageMap::getUrl(208)); //terug naar root offerte systeem
      }
      $imessages = Imessage::getMyMessages($_SESSION['userObject']);
      $imessages_read = ImessageRead::getReadMessages($_SESSION['userObject']->userId);

      $this->imessages = $imessages;
      $this->imessages_read = $imessages_read;
    }

    public function executeShow() {
      if (!isset($_SESSION["userObject"])) { //uitgelogd
        ResponseHelper::redirect(PageMap::getUrl(208)); //terug naar root offerte systeem
      }
      $imessage = Imessage::find_by_id($_GET["id"]);
      $imessage->setRead($_SESSION['userObject']->userId);

      $this->imessage = $imessage;
    }

  }

