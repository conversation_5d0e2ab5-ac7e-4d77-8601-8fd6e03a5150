<section>

  <div class="contenttxt">
    <h1><PERSON><PERSON> g<PERSON></h1>
    Op deze pagina kun u uw gegevens aanpassen.
    <br/><br/>
  </div>

  <form method="post" id="basketform">

    <?php writeErrors($errors, true) ?>

    <div class="form-row">
      <label for="private" class="col-4 col-form-label"><?php echo __("Bestellen als") ?> <span class="form-arterisk">*</span> </label>
      <div class="col-4-3 ">
        <label style="float: left;">
          <input type="radio" class="form-radio form-check-inline organ_type" name="private" value="false" required <?php writeIfCheckedVal($user->private,'false') ?>/>
          <?php echo __("Bedrijf") ?>
        </label>
        <label style="float: left;padding-left: 15px;">
          <input type="radio" class="form-radio form-check-inline organ_type" name="private" value="true" required <?php writeIfCheckedVal($user->private,'true') ?>/>
          <?php echo __("Particulier") ?>
        </label>
      </div>
    </div>

    <div class="form-row <?php if($user->isPrivate()): ?> hidden <?php endif; ?>" id="companyName">
      <label for="organ_name" class="col-4 col-form-label"><?php echo __("Bedrijfsnaam") ?>
        <?php echo showHelpButton("Neem contact op als u de bedrijfsnaam wilt aanpassen.","Bedrijfsnaam") ?>
      </label>
      <div class="col-4-3">
        <input type="text" class="form-input" name="companyName" id="companyName" value="<?php echo displayAsHtml($user->companyName) ?>" size="30" maxlength="50" required placeholder="<?php echo __("Bedrijfsnaam") ?>" readonly/>
      </div>
    </div>

    <?php if(isset($user->company)): ?>
      <div class="form-row  <?php if($user->isPrivate()): ?> hidden <?php endif; ?>" id="telGeneral">
        <label for="company_phone" class="col-4 col-form-label"><?php echo __("Telefoon algemeen") ?></label>
        <div class="col-4-3">
          <input type="text" class="form-input" name="company_phone" id="company_phone" value="<?php echo displayAsHtml($user->company->phone) ?>" size="25" maxlength="20" placeholder="<?php echo __("Telefoonnummer") ?>"/>
        </div>
      </div>
    <?php endif; ?>

    <div class="form-row">
      <label for="gender" class="col-4 col-form-label"><?php echo __("Aanhef") ?> <span class="form-arterisk">*</span> </label>
      <div class="col-4-3">
        <label style="float:left;">
          <input type="radio" class="form-radio form-check-inline" name="gender" value="male" required <?php writeIfCheckedVal($user->gender, 'male') ?>/>
          <?php echo __("Dhr.") ?>
        </label>
        <label style="float: left;padding-left: 15px;">
          <input type="radio" class="form-radio form-check-inline" name="gender" value="female" required <?php writeIfCheckedVal($user->gender, 'female') ?>/>
          <?php echo __("Mevr.") ?>
        </label>
      </div>
    </div>

    <div class="form-row">
      <label for="firstName" class="col-4 col-form-label"><?php echo __("Voornaam") ?> <span class="form-arterisk">*</span> </label>

      <div class="col-4-3">
        <input type="text" class="form-input" name="firstName" placeholder="<?php echo __("Voornaam") ?>" value="<?php echo displayAsHtml($user->firstName) ?>" required/>
      </div>
    </div>

    <div class="form-row">
      <label for="lastName" class="col-4 col-form-label"><?php echo __("Achternaam") ?> <span class="form-arterisk">*</span> </label>

      <div class="col-4-3">
        <input type="text" class="form-input" name="lastName" placeholder="<?php echo __("Achternaam") ?>" value="<?php echo displayAsHtml($user->lastName )?>" required/>
      </div>
    </div>


    <div class="form-row">
      <label for="email" class="col-4 col-form-label">
        <?php echo __("Uw e-mailadres") ?>
        <span class="form-arterisk">*</span>
      </label>
      <div class="col-4-3">
        <input type="email" class="form-input" name="email" value="<?php echo escapeForInput($user->email) ?>" size="40" maxlength="150" placeholder="<?php echo __("E-mail") ?>" required/>
      </div>
    </div>

    <div class="form-row">
      <label for="password_change" class="col-4 col-form-label"><?php echo __("Wachtwoord aanpassen") ?></label>
      <div class="col-4-3">
        <label>
        <input type="checkbox" class="form-checkbox" name="password_change" id="password_change" value="1" <?php writeIfChecked("password_change",1) ?>/>
        aanpassen wachtwoord
        </label>
      </div>
    </div>

    <div class="form-row passwordrow">
      <label for="password1" class="col-4 col-form-label"><?php echo __("Wachtwoord") ?> <span class="form-arterisk">*</span></label>
      <div class="col-4-3">
        <input type="password" class="form-input" name="password1" id="password1" value="<?php writeIfSet("password1") ?>" size="30" maxlength="50" placeholder="<?php echo __("Wachtwoord") ?>" required/>
      </div>
    </div>

    <div class="form-row passwordrow">
      <label for="password2" class="col-4 col-form-label"><?php echo __("Wachtwoord bevestig") ?> <span class="form-arterisk">*</span></label>
      <div class="col-4-3">
        <input type="password" class="form-input" name="password2" id="password2" value="<?php writeIfSet("password2") ?>" size="30" maxlength="50" placeholder="<?php echo __("Wachtwoord bevestig") ?>" required/>
      </div>
    </div>


    <div class="form-row">
      <label for="phone" class="col-4 col-form-label"><?php echo __("Telefoon direct") ?> <span class="form-arterisk">*</span> </label>
      <div class="col-4-3">
        <input type="text" class="form-input" name="phone" value="<?php echo displayAsHtml($user->phone) ?>" size="25" maxlength="15" placeholder="<?php echo __("Telefoonnummer") ?>" required/>
      </div>
    </div>

    <div class="form-row">
      <label for="mobile" class="col-4 col-form-label"><?php echo __("Mobiel nummer") ?></label>
      <div class="col-4-3">
        <input type="text" class="form-input" name="mobile" value="<?php echo displayAsHtml($user->mobile) ?>" size="25" maxlength="15" placeholder="<?php echo __("Mobiel nummer") ?>"/>
      </div>
    </div>

    <div class="form-row">
      <label class="col-4 col-form-label mobileshow"><b><?php echo __("Factuur  gegevens") ?></b></label>
      <div class="col-4-3">
      </div>
    </div>

    <div class="form-row">
      <label class="col-4 col-form-label"><?php echo __("Postcode + nummer") ?> <span class="form-arterisk">*</span></label>
      <div class="col-4"  style="margin-right: 15px;">
        <input type="text" class="form-input" name="zipcode" id="zipcode" value="<?php echo displayAsHtml($user->zipcode) ?>" placeholder="<?php echo __("Postcode") ?>" required/>
      </div>
      <div class="col-4" style="margin-right: 15px;">
        <input type="number" class="form-input" name="nr" id="nr" value="<?php echo displayAsHtml($user->nr) ?>" placeholder="<?php echo __("Huisnummer") ?>" required maxlength="5"/>
      </div>
      <div class="col-4" style="width: calc(25% - 30px);">
        <input type="text" class="form-input" name="extension" value="<?php echo displayAsHtml($user->extension) ?>" placeholder="<?php echo __("Toevoeging") ?>" maxlength="10" />
      </div>
    </div>

    <div class="form-row">
      <label class="col-4 col-form-label"><?php echo __("Plaats + straatnaam") ?> <span class="form-arterisk">*</span></label>
      <div class="col-4" style="margin-right: 15px;">
        <input type="text" class="form-input" name="domestic" id="domestic" value="<?php echo displayAsHtml($user->domestic) ?>" placeholder="<?php echo __("Plaats") ?>" readonly/>
      </div>
      <div class="col-4" style="width: calc(50% - 15px);">
        <input type="text" class="form-input" name="street" id="street" value="<?php echo displayAsHtml($user->street) ?>" placeholder="<?php echo __("Straatnaam") ?>" readonly/>
      </div>
    </div>



    <div class="form-row">
      <label class="col-4 col-form-label"><?php echo __("Land") ?> <span class="form-arterisk">*</span></label>
      <div class="col-4-3">
        <select name="country" id="country" required>
          <option value="BE" <?php writeIfSelectedVal($user->country,'BE') ?>>België</option>
          <option value="DE" <?php writeIfSelectedVal($user->country,'DE') ?>>Duitsland</option>
          <option value="NL" <?php writeIfSelectedVal($user->country,'NL') ?>>Nederland</option>
        </select>
      </div>
    </div>

    <?php if(!$user->hasCompany()): ?>
      <div class="form-row">
        <label for="invoiceemail" class="col-4 col-form-label">
          <?php echo __("Factuur e-mailadres") ?>
        </label>
        <div class="col-4-3">
          <input type="email" class="form-input" name="invoice_email" value="<?php echo escapeForInput($user->invoice_email) ?>" size="40" maxlength="255" placeholder="<?php echo __("Factuur e-mailadres") ?>"/>
        </div>
        <?php if($user->invoice_email!="" && $user->invoice_email_confirmed==0): ?>
          <label class="col-4 col-form-label">&nbsp;</label>
          <div class="col-4-3" style="color: red;line-height: 1.5;">
            Let op: dit e-mailadres is nog niet bevestigd. U heeft een email ontvangen waarmee u het e-mailadres kunt bevestigen.<br/>
            <input type="submit" value="Verstuur bevestigings email nogmaals" name="go_send" class="form-input" style="width: auto;margin-top: 5px;"/>
          </div>
        <?php endif; ?>
      </div>
    <?php else: ?>
      <div class="form-row">
        <label for="invoiceemail" class="col-4 col-form-label">
          <?php echo __("Factuur e-mailadres") ?>
        </label>
        <div class="col-4-3">
          <input type="email" class="form-input" name="invoiceemail" value="<?php echo escapeForInput($invoiceparty->email) ?>" size="40" maxlength="255" placeholder="<?php echo __("Factuur e-mailadres") ?>"/>
        </div>
        <?php if($invoiceparty->email!="" && $invoiceparty->emailConfirmed==0): ?>
          <label class="col-4 col-form-label">&nbsp;</label>
          <div class="col-4-3" style="color: red;line-height: 1.5;">
            Let op: dit e-mailadres is nog niet bevestigd. U heeft een email ontvangen waarmee u het e-mailadres kunt bevestigen.<br/>
            <input type="submit" value="Verstuur bevestigings email nogmaals" name="go_send" class="form-input" style="width: auto;margin-top: 5px;"/>
          </div>
        <?php endif; ?>
      </div>

      <?php foreach($banksaccounts as $tel=>$banksaccount): ?>
        <div class="form-row">
          <label class="col-4 col-form-label">
            <?php echo __("IBAN rekeningnummer")." ".($tel+1) ?>
            <?php if($banksaccount->debit==1): ?>
              <?php echo showAlertButton("Dit rekeningnummer word voor incasso gebruikt.") ?>
            <?php endif; ?>
          </label>
          <div class="col-4-3">
            <input type="text" class="form-input" name="iban[]" value="<?php echo \StringHelper::ibanFormat($banksaccount->IBAN) ?>" placeholder="<?php echo __("IBAN rekeningnummer") ?>..."/>
          </div>
        </div>
      <?php endforeach; ?>
    <?php endif; ?>

    <div class="form-row">
      <div class="col-4 responsive-hide">&nbsp;</div>
      <div class="col-4-3">
        <input type="submit" value="<?php echo __("Opslaan") ?>" class="btn btn-primary" name="go" id="registeren"/>
      </div>
    </div>

  </form>

</section>

<script type="text/javascript">
  $(document).ready(function () {

    $("#password_change").change(function() {
      if($(this).prop("checked")) {
        $(".passwordrow").show()
      }
      else {
        $(".passwordrow").hide()
      }
    });
    $("#password_change").change();


    $('.organ_type').on("click", function () {
      if ($(this).val() == 'false') {
        $('#companyName,#telGeneral').removeClass('hidden');
      }
      else {
        $('#companyName,#telGeneral').addClass('hidden');
      }
    });
    $('#afleveradres').on("click", function () {
      if ($(this).prop("checked")) {
        $('.afleveradres').removeClass("hidden");
      }
      else {
        $('.afleveradres').addClass("hidden");
      }
    });


    jQuery.extend(jQuery.validator.messages, {
      required: "<?php echo __("Dit veld is verplicht") ?>",
      email: "<?php echo __("Voer een geldig emailadres in.") ?>",
      equalTo: "<?php echo __("Voer 2 keer dezelfde waarde in.") ?>",
      minlength: "<?php echo __("Voer minimaal {0} karakters in." ) ?>"
    });
    // //deel formulieren valideren
    // jQuery.validator.prototype.subset = function (container) {
    //   var ok = true;
    //   var self = this;
    //   $(container).find(':input').each(function () {
    //     if (!self.element($(this))) ok = false;
    //   });
    //   return ok;
    // }

    $('#basketform').validate( {
      rules: {
        password1: {
          minlength: 8
        },
        password2: {
          equalTo: "#password1"
        }
      }
    });

    //----------- postcode api javascript - start --------------

    $('#country').on("change", function () {
      //only use postcode api in netherlands
      if($("#country").val()=="NL") {
        $('#street,#domestic').prop("readonly",true);
        $("#zipcode").change();
      }
      else {
        $('#street,#domestic').prop("readonly",false);
      }
    });

    $('#zipcode,#nr').on("change", function () {
      if($("#country").val()!="NL") { //only use postcode api in netherlands
        return;
      }
      $.getJSON("<?php echo reconstructQuery() ?>&action=postcodeapi&zipcode=" + $("#zipcode").val()+"&nr=" + $("#nr").val(),
        function (data) {
          if (data.success) {
            if (data.success=="open") {
              //no credits at postcode api, customer can enter the info himself
              $('#street,#domestic').prop("readonly",false);
            }
            else {
              $("#street").val(data.data.street);
              $("#domestic").val(data.data.city);
            }
          }
          else {
            $("#street").val("");
            $("#domestic").val("");
          }
        });
    });

    <?php if(isset($errors["street"]) || isset($errors["domestic"])): //on error customer may enter street and city ?>
    $('#street,#domestic').prop("readonly",false);
    <?php endif; ?>

    //----------- postcode api javascript - end --------------

  });
</script>