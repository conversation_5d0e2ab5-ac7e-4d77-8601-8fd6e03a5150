const path = require("path");

const devPort = 8081;
// const PROJECT = 'rde';
const TEMPLATE = 'frontend';
// const DIR_GSDFW = '../../gsdfw/';
const OUTPUTDIR = process.env.NODE_ENV === "production"?"../../../templates/"+TEMPLATE+"/dist-spa/":"dist/";

module.exports = {
  devServer: {
    hot: true,
    writeToDisk: true,
    liveReload: false,
    disableHostCheck: true,
    sockPort: devPort,
    port: devPort,
    https: true,
    headers: {"Access-Control-Allow-Origin": "*"}
  },
  publicPath:
    process.env.NODE_ENV === "production"
      ? OUTPUTDIR
      : `https://localhost:${devPort}/`,
  configureWebpack: {
    output: {
      filename: process.env.NODE_ENV === "production"?"wizard.min.js":"wizard.js",
      hotUpdateChunkFilename: "hot/hot-update.js",
      hotUpdateMainFilename: "hot/hot-update.json"
    },
    optimization: {
      splitChunks: false
    }
  },
  filenameHashing: true,
  css: {
    extract: {
      filename: process.env.NODE_ENV === "production"?"wizard.min.css":"wizard.css",
    }
  },
  outputDir: path.resolve(__dirname, OUTPUTDIR),
  // assetsDir: "../../static/SPA"

};