<?php

  AppModel::loadBaseClass('BaseProductionOrder');

  class ProductionOrderModel extends BaseProductionOrder {

    /**
     * Create order if none existing
     * @param Quotations $quotation
     * @return bool|ProductionOrder
     */
    public static function create($quotation) {
      $po = ProductionOrder::find_by(["quotationId" => $quotation->quotationId]);
      if ($po) {
        return $po;
      }
      $stone = Stones::find_by(["stoneId" => $quotation->stoneId]);
      if (!$stone || $stone->colorId == "" || $stone->sizeId == "") {
        return false;
      }
      $productionorder = new ProductionOrder();
      $productionorder->quotationId = $quotation->quotationId;
      $productionorder->colorId = $stone->colorId;
      $productionorder->sizeId = $stone->sizeId;
      $productionorder->productionDate = $quotation->dueDate;
      $productionorder->save();
      return $productionorder;
    }


  }