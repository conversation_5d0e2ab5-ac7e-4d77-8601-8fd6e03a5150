<h3>Bestelde meters per jaar</h3>
Hieronder vind u een overzicht van het aantal meters besteld per jaar.<Br/>
<div class="filter">
  Filter:
  <form>
    <select name="type" id="chartyearlyorder_type">
      <option value="">Selecteer type...</option>
      <?php foreach(Stones::TYPES as $mk=>$mname): ?>
        <option value="<?php echo $mk ?>" <?php writeIfSelectedVal($type,$mk); ?>><?php echo $mname ?></option>
      <?php endforeach; ?>
    </select>
    <select name="material" id="chartyearlyorder_material">
      <option  value="">Selecteer materiaal...</option>
      <?php foreach(Stones::MATERIALS as $mk=>$mname): ?>
        <option value="<?php echo $mk ?>" <?php writeIfSelectedVal($material,$mk); ?>><?php echo $mname ?></option>
      <?php endforeach; ?>
    </select>
  </form>
</div>
<div class="rdechart" id="chartyearlyorder_chart"></div>

<script>
  $(document).ready(function() {
    createBarchart("chartyearlyorder_chart", <?php echo json_encode($chartdata) ?>);

    $("#chartyearlyorder_material,#chartyearlyorder_year,#chartyearlyorder_type").change(function() {
      getChart("chartyearlyorder");
    })

  });
</script>