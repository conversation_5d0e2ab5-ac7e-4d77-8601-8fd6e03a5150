<?php

  AppModel::loadBaseClass('BaseDamageQuotation');

  class DamageQuotationModel extends BaseDamageQuotation {

    /**
     * Get insertTS
     * @param string $format
     * @return string
     */
    public function getInsertTS(string $format = 'd-m-Y H:i:s'): string {
      return DateTimeHelper::formatDbDate($this->insertTS, $format);
    }

    public function save(&$errors = []) {
      if ($this->from_db == false || $this->insertTS == "0000-00-00 00:00:00") {
        $this->insertTS = date('Y-m-d H:i:s');
      }
      return parent::save($errors);
    }


  }