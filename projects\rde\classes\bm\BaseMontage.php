<?php
class BaseMontage extends AppModel
{
  const DB_NAME = 'rde_b1mo';
  const TABLE_NAME = 'montage';
  const OM_CLASS_NAME = 'Montage';
  const columns = ['id', 'employee_id', 'company_id', 'company_address_id', 'type', 'name', 'plandate', 'remark', 'executor_montage', 'executor_montage_mobile', 'done', 'slopen', 'verstekken', 'clips', 'specie', 'water', 'uitkrabben', 'voegen', 'pointeren', 'steigers', 'reistijd'];
  const field_structure = [
    'id'                          => ['type' => 'mediumint', 'length' => '8', 'null' => false],
    'employee_id'                 => ['type' => 'mediumint', 'length' => '8', 'null' => false],
    'company_id'                  => ['type' => 'int', 'length' => '11', 'null' => false],
    'company_address_id'          => ['type' => 'int', 'length' => '11', 'null' => true],
    'type'                        => ['type' => 'varchar', 'length' => '30', 'null' => true],
    'name'                        => ['type' => 'varchar', 'length' => '255', 'null' => false],
    'plandate'                    => ['type' => 'date', 'length' => '', 'null' => true],
    'remark'                      => ['type' => 'text', 'length' => '', 'null' => true],
    'executor_montage'            => ['type' => 'varchar', 'length' => '150', 'null' => true],
    'executor_montage_mobile'     => ['type' => 'varchar', 'length' => '20', 'null' => true],
    'done'                        => ['type' => 'boolean', 'length' => '1', 'null' => false],
    'slopen'                      => ['type' => 'boolean', 'length' => '1', 'null' => false],
    'verstekken'                  => ['type' => 'boolean', 'length' => '1', 'null' => false],
    'clips'                       => ['type' => 'boolean', 'length' => '1', 'null' => false],
    'specie'                      => ['type' => 'boolean', 'length' => '1', 'null' => false],
    'water'                       => ['type' => 'boolean', 'length' => '1', 'null' => false],
    'uitkrabben'                  => ['type' => 'boolean', 'length' => '1', 'null' => false],
    'voegen'                      => ['type' => 'boolean', 'length' => '1', 'null' => false],
    'pointeren'                   => ['type' => 'boolean', 'length' => '1', 'null' => false],
    'steigers'                    => ['type' => 'boolean', 'length' => '1', 'null' => false],
    'reistijd'                    => ['type' => 'float unsigned', 'length' => '', 'null' => false],
  ];

  protected static $primary_key = ['id'];
  protected $auto_increment = 'id';

  public $id, $employee_id, $company_id, $company_address_id, $type, $name, $plandate, $remark, $executor_montage, $executor_montage_mobile, $done, $slopen, $verstekken, $clips, $specie, $water, $uitkrabben, $voegen, $pointeren, $steigers, $reistijd;

  function __construct($obj_arr = [], &$error_codes = []) {
    parent::__construct();
    $this->setDefaults();
    if (!empty($obj_arr)) {
      $this->from_array($obj_arr);
      $this->valid_required_fields($error_codes);
    }
  }

  public function valid(&$error_codes = []) {
    $errors = [];
    foreach(self::columns as $column) {
      if ($column != $this->auto_increment)
      {
        call_user_func_array([$this, 'v_' . $column], [&$errors]);
      }
    }
    if(empty($errors)) {
      return true;
    }
    $error_codes = array_merge($error_codes, $errors);
    return false;
  }

  public function valid_required_fields(&$error_codes = []) {
    $errors = [];
    foreach (self::columns as $column) {
      $function_name = "v_$column";
      if ($column!=$this->auto_increment) {
        call_user_func_array([$this, $function_name], [&$errors]);
      }
    }
    if (empty($errors)) {
      return true;
    }
    $error_codes = array_merge($error_codes, $errors);
    return false;
  }

  public function setDefaults() {
    $this->done = 0;
    $this->slopen = 0;
    $this->verstekken = 0;
    $this->clips = 0;
    $this->specie = 0;
    $this->water = 0;
    $this->uitkrabben = 0;
    $this->voegen = 0;
    $this->pointeren = 0;
    $this->steigers = 0;
    $this->reistijd = 0;
  }



  public function v_id(&$error_codes = []) {
    if (!is_null($this->id) && strlen($this->id) > 0 && self::valid_mediumint($this->id, '8')) {
      return true;
    }
    $error_codes[] = 'id';
    return false;
  }

  public function v_employee_id(&$error_codes = []) {
    if (!is_null($this->employee_id) && strlen($this->employee_id) > 0 && self::valid_mediumint($this->employee_id, '8')) {
      return true;
    }
    $error_codes[] = 'employee_id';
    return false;
  }

  public function v_company_id(&$error_codes = []) {
    if (!is_null($this->company_id) && strlen($this->company_id) > 0 && self::valid_int($this->company_id, '11')) {
      return true;
    }
    $error_codes[] = 'company_id';
    return false;
  }

  public function v_company_address_id(&$error_codes = []) {
    if (is_null($this->company_address_id) || strlen($this->company_address_id) == 0 || self::valid_int($this->company_address_id, '11')) {
      return true;
    }
    $error_codes[] = 'company_address_id';
    return false;
  }

  public function v_type(&$error_codes = []) {
    if (is_null($this->type) || strlen($this->type) == 0 || self::valid_varchar($this->type, '30')) {
      return true;
    }
    $error_codes[] = 'type';
    return false;
  }

  public function v_name(&$error_codes = []) {
    if (!is_null($this->name) && strlen($this->name) > 0 && self::valid_varchar($this->name, '255')) {
      return true;
    }
    $error_codes[] = 'name';
    return false;
  }

  public function v_plandate(&$error_codes = []) {
    if (is_null($this->plandate) || strlen($this->plandate) == 0 || self::valid_date($this->plandate)) {
      return true;
    }
    $error_codes[] = 'plandate';
    return false;
  }

  public function v_remark(&$error_codes = []) {
    if (is_null($this->remark) || strlen($this->remark) == 0 || self::valid_text($this->remark)) {
      return true;
    }
    $error_codes[] = 'remark';
    return false;
  }

  public function v_executor_montage(&$error_codes = []) {
    if (is_null($this->executor_montage) || strlen($this->executor_montage) == 0 || self::valid_varchar($this->executor_montage, '150')) {
      return true;
    }
    $error_codes[] = 'executor_montage';
    return false;
  }

  public function v_executor_montage_mobile(&$error_codes = []) {
    if (is_null($this->executor_montage_mobile) || strlen($this->executor_montage_mobile) == 0 || self::valid_varchar($this->executor_montage_mobile, '20')) {
      return true;
    }
    $error_codes[] = 'executor_montage_mobile';
    return false;
  }

  public function v_done(&$error_codes = []) {
    if (!is_null($this->done) && strlen($this->done) > 0 && self::valid_tinyint($this->done, '1')) {
      return true;
    }
    $error_codes[] = 'done';
    return false;
  }

  public function v_slopen(&$error_codes = []) {
    if (!is_null($this->slopen) && strlen($this->slopen) > 0 && self::valid_tinyint($this->slopen, '1')) {
      return true;
    }
    $error_codes[] = 'slopen';
    return false;
  }

  public function v_verstekken(&$error_codes = []) {
    if (!is_null($this->verstekken) && strlen($this->verstekken) > 0 && self::valid_tinyint($this->verstekken, '1')) {
      return true;
    }
    $error_codes[] = 'verstekken';
    return false;
  }

  public function v_clips(&$error_codes = []) {
    if (!is_null($this->clips) && strlen($this->clips) > 0 && self::valid_tinyint($this->clips, '1')) {
      return true;
    }
    $error_codes[] = 'clips';
    return false;
  }

  public function v_specie(&$error_codes = []) {
    if (!is_null($this->specie) && strlen($this->specie) > 0 && self::valid_tinyint($this->specie, '1')) {
      return true;
    }
    $error_codes[] = 'specie';
    return false;
  }

  public function v_water(&$error_codes = []) {
    if (!is_null($this->water) && strlen($this->water) > 0 && self::valid_tinyint($this->water, '1')) {
      return true;
    }
    $error_codes[] = 'water';
    return false;
  }

  public function v_uitkrabben(&$error_codes = []) {
    if (!is_null($this->uitkrabben) && strlen($this->uitkrabben) > 0 && self::valid_tinyint($this->uitkrabben, '1')) {
      return true;
    }
    $error_codes[] = 'uitkrabben';
    return false;
  }

  public function v_voegen(&$error_codes = []) {
    if (!is_null($this->voegen) && strlen($this->voegen) > 0 && self::valid_tinyint($this->voegen, '1')) {
      return true;
    }
    $error_codes[] = 'voegen';
    return false;
  }

  public function v_pointeren(&$error_codes = []) {
    if (!is_null($this->pointeren) && strlen($this->pointeren) > 0 && self::valid_tinyint($this->pointeren, '1')) {
      return true;
    }
    $error_codes[] = 'pointeren';
    return false;
  }

  public function v_steigers(&$error_codes = []) {
    if (!is_null($this->steigers) && strlen($this->steigers) > 0 && self::valid_tinyint($this->steigers, '1')) {
      return true;
    }
    $error_codes[] = 'steigers';
    return false;
  }

  public function v_reistijd(&$error_codes = []) {
    return true;
  }

  /**
  *@param array $conditions (required)
  *@param String $raw_sql (optional)
  *@return Montage[]
  **/
  public static function find_all_like($conditions, $raw_sql = '') { return parent::generic_find_all_like($conditions, static::OM_CLASS_NAME, $raw_sql); }
  /**
  *@param array $conditions (required)
  *@param String $raw_sql (optional)
  *@return Montage[]
  **/
  public static function find_all_by($conditions, $raw_sql = '') { return parent::generic_find_all_by($conditions, static::OM_CLASS_NAME, $raw_sql); }
  /**
  *@param String $raw_sql (optional)
  *@return Montage[]
  **/
  public static function find_all($raw_sql = '') { return parent::generic_find_all(static::OM_CLASS_NAME, $raw_sql); }
  /**
  *@param array $conditions (required)
  *@param String $raw_sql (optional)
  *@return Montage
  **/
  public static function find_by($conditions, $raw_sql = '') { return parent::generic_find_by($conditions, static::OM_CLASS_NAME, $raw_sql); }
  /**
  *@param String $id (required)
  *@param String $raw_sql (optional)
  *@return Montage
  **/
  public static function find_by_id($id, $raw_sql = '') { return parent::generic_find_by_id($id, static::OM_CLASS_NAME, $raw_sql); }
  /**
  *@param array $conditions (required)
  *@param String $raw_sql (optional)
  *@return int
  **/
  public static function count_all_by($conditions, $raw_sql = '') { return parent::generic_count_all_by($conditions, static::OM_CLASS_NAME, $raw_sql); }
  /**
  *@param array $conditions (required)
  *@param String $raw_sql (optional)
  *@return bool
  **/
  public static function delete_by($conditions, $raw_sql = '') { return parent::generic_delete_by($conditions, static::OM_CLASS_NAME, $raw_sql); }
}