<div class="row">
  <div class="col6 col12-xs">
    <div class="contenttxt">
      <?php echo process_text($page->content->content1); ?>
    </div>
  </div>
  <div class="col6 col12-xs contenttxt">
    <h2></h2>
    <br/>
    <p><PERSON><PERSON> met onderstaand formulier de gewenste detailtekening.</p>

    <form id="negemaat" method="get" action="#negenmaat">
      <p>
        <b>Kozijn</b><br/>
        <label><input type="radio" name="kozijn" class="kozijn" value="hout" <?php if(isset($_GET['kozijn']) && $_GET['kozijn'] != 'kozijn') echo "checked"; ?>/> Hout</label>
        <br/>
        <label><input type="radio" name="kozijn" class="kozijn" value="kunststof" <?php if(isset($_GET['kozijn']) && $_GET['kozijn'] == 'kunststof') echo "checked"; ?>/> Kunststof</label>
      </p>

      <p>
        <b>Negmaat</b><br />
        <div class="row">
          <div class="col8">
            <div class="select">
              <select name="neg" id="neg">
                <?php for($iNeg =0; $iNeg <= 195; $iNeg += 5): ?>
                  <option value="<?php echo $iNeg ?>" <?php if(isset($_GET['neg']) && intval($_GET['neg']) == $iNeg) echo "selected"; ?>><?php echo $iNeg ?></option>
                <?php endfor; ?>
              </select></div>
          </div>
          <div class="col4 l-h-xl">
            mm
          </div>
        </div>
      </p>
    </form>

    <?php if(count($aFiles) > 0): ?>
      <table class="result download">
        <tbody><tr>
          <th></th>
          <th>Kozijn</th>
          <th>Neg</th>
          <th>Maat</th>
          <th>Klik</th>
          <th>Bestand</th>
        </tr>
        <?php for($iFile = 0; $iFile < count($aFiles); $iFile++): ?>
          <tr>
            <td><?php echo ($iFile+1); ?></td>
            <td><?php echo ucfirst($sMaterial) ?></td>
            <td><?php echo intval($_GET['neg']) ?> mm</td>
            <td><?php echo $aFiles[$iFile]['length'] ?> x <?php echo $aFiles[$iFile]['width'] ?> x <?php echo $aFiles[$iFile]['depth'] ?> mm</td>
            <td>
              <?php if($aFiles[$iFile]['click'] != 'n.v.t.'): ?>
                <?php echo $aFiles[$iFile]['click'] ?> cm
              <?php else: ?>
                Color-Line
              <?php endif; ?>
            </td>
            <td><a target="_blank" title="Download detailtekening als PDF" href="<?php echo URL_UPLOADS ?>neggemaattabel/<?php echo $sMaterial ?>/<?php echo $aFiles[$iFile]['filename'] ?>"><i class="fa fa-file-pdf-o"></i></a></td>
          </tr>
        <?php endfor; ?>
        </tbody>
      </table>
    <?php else: ?>
      <p>Geen detailtekening gevonden</p>
    <?php endif; ?>
  </div>
</div>

<script type="text/javascript">
  $(document).ready(function () {

      $(".kozijn,#neg").change(function() {
        $("#negemaat").submit();
      });

  });
</script>