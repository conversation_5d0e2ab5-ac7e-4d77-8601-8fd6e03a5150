.product-col {
  padding: 15px 15px 50px;
  margin-bottom: 20px;
  background: #fff;
  border: 1px solid #e8e8e8;
  position: relative;
  min-height: 285px;
}

.product-col-a {
  text-align: center;
  display: block;
  height: 152px;
}

.col6 .product-col-a {
  height: 200px;
}

.discountflag {
  padding: 5px 10px;
  font-weight: bold;
  position: absolute;
  top: 20px;
  left: 40px;
  border-radius: 5px;
}

.price-dot-circle {
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  border-radius: 38px;
  height: 75px;
  width: 75px;
  position: absolute;
  top: -12px;
  right: -12px;
  color: white;
}

.price-on-request {
  font-size: 15px;
}

.product-col .caption h4.ft-green {
  height: 60px;
}

.product-col .caption .description {
  height: 46px;
}

.price-2-digits-ft-size {
  font-size: 24px;
}

.price-3-digits-ft-size {
  font-size: 24px;
}

.price-4-digits-ft-size {
  font-size: 20px;
}

.price-5-digits-ft-size {
  font-size: 20px;
}

.price-6-digits-ft-size {
  font-size: 18px;
}

.bg-green {
  background-color: #67993a;
}

.bg-blue {
  background-color: #00467f;
}

.bg-yellow {
  background-color: #f6c500;
}

.bg-grey {
  background-color: #524d49 !important;
}

.bg-red {
  background-color: #CE000C;
}

.btn-cart.bg-moreinfo {
  background-color: #ebebeb;
  color: black;
  position: absolute;
  bottom: 15px;
  width: calc(100% - 30px);
}

.btn-cart.bg-moreinfo:hover .fa.bg-grey {
  background-color: #CE000C !important;
  transition: all 0.3s ease;
}

.bg-white {
  background-color: white;
  color: black;
  box-shadow: 0 1px 3px 0 rgba(189, 189, 189, 1)
}

.cart-button {
  text-align: left;
}

.btn-cart {
  padding: 0 0 0 15px;
  font-size: 18px;
  color: #fff;
  text-transform: uppercase;
  border: none;
  height: auto;
  line-height: initial;
  font-weight: 500;
  display: flex;
  width: 100%;
  align-items: center;
  justify-content: space-between;
}

.btn i.fa.fa-chevron-right {
  margin-left: 10px;
  margin-right: 0;
}

.btn-cart:hover {
  background-color: #f7f7f7;
  color: #CE000C;
}

.btn-cart:hover .fa {
}

.btn-cart .fa {
  margin: 0;
  margin-left: 10px;
  padding: 10px;
  color: white;
  border: 1px solid transparent
}

a.product-img-a {
  display: block;
}

.product-col .prcaption h4 {
  padding: 15px 5px;
  height: 90px;
}

.product-col .prcaption .description {
  height: 50px;
  padding: 15px;
}

.manufacturer {
  list-style: none;
}

.hide {
  display: none;
}

.product-addtocart-div {
  display: flex;
  align-items: center;
  background-color: #EBEBEB;
  padding: 15px;
}

#productpage .productimage {
  border: 1px solid #EBEBEB;
  display: inline-block;
  margin-bottom: 15px;
}

#productpage img {
}

#product_orderform {
  background-color: #EBEBEB;
  padding: 10px;
}

.product-stone-div {
  display: flex;
  align-items: center;
  padding: 5px;
  justify-content: space-between;
}

.call-to-action {
  width: 100%;
  text-align: center;
  margin-bottom: 15px;
  height: auto;
}

#usp {
  font-size: 18px;
}

#usp h4 {
  font-weight: bold;
  padding-bottom: 15px;
}

#usp ul {
  margin: 0;
  padding: 0;
  list-style: none;
}

#usp li {
  padding: 8px 20px 8px 0;
}

#usp li i {
  font-weight: bold;
  margin-right: 10px;
}

#productprops td {
  padding: 5px;
}

#productprops td:first-child {
  font-weight: bolder;
}

#basket .alert.alert-danger {
  margin-top: 10px;
}

#basket_row_header {
  padding-top: 10px;
  padding-bottom: 10px;
  background-color: #EAEBEC;
  margin-top: 15px !important;
  margin-bottom: 15px !important;
  border-radius: 5px;
  font-weight: bold;
}

.basket_pieceprice, .basket_price, .basket_subtotal {
  text-align: right;
}

.basket-form .fa {
  padding: 8px 5px 13px 5px;
}

#shippingbar {
  background-color: #EAEBEC;
  padding: 15px;
  font-weight: bold;
}

.shop_top_message {
  background-color: #ebebeb;
  color: #CE000C;
  font-weight: 500;
  text-align: center;
  font-size: 17px;
  padding-top: 10px;
  padding-bottom: 10px;
  margin-bottom: 15px;
  border-radius: 3px;
}

.payment_logo {
  padding: 5px 5px;
  background: white;
  text-align: center;
  display: block;
}

.payment_logo img {
  max-height: 50px;
}

.basket-step-2 .form-row {
  margin: 0 0 0.4em 0;
  display: flex;
  align-items: center;
}

.price-on-request {
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
}