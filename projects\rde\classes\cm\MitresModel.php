<?php

  AppModel::loadBaseClass('BaseMitres');

  class MitresModel extends BaseMitres {

    /**
     * @param $stoneId
     * @param bool|int $angle
     * @return Mitres[]
     */
    public static function getMitresByStoneId($stoneId = false, $angle = false) {

      $sQuery1 = "SELECT mitres.* FROM " . Stones::getTablename() . " ";
      $sQuery1 .= "JOIN " . StoneSizes::getTablename() . " ON stones.sizeId = stone_sizes.sizeId ";
      $sQuery1 .= "JOIN " . Mitres::getTablename() . " ON stone_sizes.length = mitres.stoneLength ";
      $sQuery1 .= "WHERE mitres.display='true' ";
      if ($stoneId !== false) {
        $sQuery1 .= "AND stones.stoneId=" . $stoneId . " ";
      }
      if ($angle !== false) {
        $sQuery1 .= "AND mitres.angle=" . $angle . " ";
      }
      $sQuery1 .= "ORDER BY mitres.angle ";

      $result = DBConn::db_link()->query($sQuery1);

      $mitres = [];
      while ($row = $result->fetch_row()) {
        $mitre = new Mitres();
        $mitre->hydrate($row);
        $mitre->from_db = true;
        $mitres[] = $mitre;
      }
      return $mitres;

    }

    /**
     * Get mitre price for certain stone
     * @param Stones $stone
     * @param StoneSizes $stone_size
     * @param StonePrices $stoneprice
     * @return mixed
     */
    public function getMitrePrice($stone, $stone_size, $stoneprice) {
      $dummyquotation = new Quotations();
      $dummyquotation->stoneId = $stone->stoneId;
      $dummyquotation->stoneCategoryId = $stone->category_id;
      $dummyquotation->endstone = "false";

      $dummyquotation_e = new QuotationsExtra();
      $dummyquotation_e->wall_thickness = 0;

      $element = new OrderElements();
      $element->leftEndstone = 0;
      $element->leftEndstoneGrooves = 0;
      $element->inputLength = $this->longLength > $this->shortLength ? $this->longLength : $this->shortLength;
      $element->leftMitre = $this;
      $element->calculateValues($dummyquotation, $dummyquotation_e, $stone_size, $stone);
      $element->calculatePrice($dummyquotation, $stoneprice, $stone_size, $stone);

      return $element->elementPrice;
    }

  }