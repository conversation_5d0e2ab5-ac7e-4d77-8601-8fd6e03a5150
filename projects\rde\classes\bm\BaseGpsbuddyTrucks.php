<?php
class BaseGpsbuddyTrucks extends AppModel
{
  const DB_NAME = 'rde_route';
  const TABLE_NAME = 'gpsbuddy_trucks';
  const OM_CLASS_NAME = 'GpsbuddyTrucks';
  const columns = ['truckId', 'gpsbuddyId', 'name', 'licence', 'orderId', 'active'];
  const field_structure = [
    'truckId'                     => ['type' => 'tinyint', 'length' => '2', 'null' => false],
    'gpsbuddyId'                  => ['type' => 'int', 'length' => '5', 'null' => false],
    'name'                        => ['type' => 'varchar', 'length' => '120', 'null' => false],
    'licence'                     => ['type' => 'varchar', 'length' => '8', 'null' => false],
    'orderId'                     => ['type' => 'int', 'length' => '5', 'null' => false],
    'active'                      => ['type' => 'boolean', 'length' => '1', 'null' => false],
  ];

  protected static $primary_key = ['truckId'];
  protected $auto_increment = 'truckId';

  public $truckId, $gpsbuddyId, $name, $licence, $orderId, $active;

  function __construct($obj_arr = [], &$error_codes = []) {
    parent::__construct();
    $this->setDefaults();
    if (!empty($obj_arr)) {
      $this->from_array($obj_arr);
      $this->valid_required_fields($error_codes);
    }
  }

  /**
  * Set default object values
  **/
  public function setDefaults() {
    $this->active = 1;
  }

  /**
   * @param array $conditions (required)
   * @param string $raw_sql (optional)
   * @return GpsbuddyTrucks[]
   **/
  public static function find_all_like($conditions, $raw_sql = '') {
    return parent::generic_find_all_like($conditions, static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param array $conditions (required)
   * @param string $raw_sql (optional)
   * @return GpsbuddyTrucks[]
   **/
  public static function find_all_by($conditions, $raw_sql = '') {
    return parent::generic_find_all_by($conditions, static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param string $raw_sql (optional)
   * @return GpsbuddyTrucks[]
   **/
  public static function find_all($raw_sql = '') {
    return parent::generic_find_all(static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param array $conditions (required)
   * @param string $raw_sql (optional)
   * @return GpsbuddyTrucks
   **/
  public static function find_by($conditions, $raw_sql = '') {
    return parent::generic_find_by($conditions, static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param string $id (required)
   * @param string $raw_sql (optional)
   * @return GpsbuddyTrucks
   **/
  public static function find_by_id($id, $raw_sql = '') {
    return parent::generic_find_by_id($id, static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param array $conditions (required)
   * @param string $raw_sql (optional)
   * @return int
   **/
  public static function count_all_by($conditions, $raw_sql = '') {
    return parent::generic_count_all_by($conditions, static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param array $conditions (required)
   * @param string $raw_sql (optional)
   * @return bool
   **/
  public static function delete_by($conditions, $raw_sql = '') {
    return parent::generic_delete_by($conditions, static::OM_CLASS_NAME, $raw_sql);
  }

}