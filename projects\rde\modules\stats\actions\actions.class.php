<?php

  use domain\stones\service\ProductionSpeed;
  use domain\stones\service\StoneSpeed;
  use Gsd\DataTable\DataTable;
  use Gsd\Form\Elements\Number;
  use Gsd\Form\Elements\Select;
  use Gsd\Form\ModelForm;

  class statsRdeActions extends statsActions {

    public function executeOverview() {
    }

    public function executeChartyearlyorder() {

      $material = $_GET["material"] ?? '';
      $type = $_GET["type"] ?? '';
      $year = $_GET["year"] ?? date("Y");

      $query = "SELECT sum(meters) as total_meters, YEAR(productionDate) as orderyear ";
      $query .= "FROM " . Quotations::getTablename() . " ";
      $query .= "WHERE statusId!=10 AND statusId!=70 AND statusId!=80 ";
      $query .= "AND productionDate!='' ";
      if ($material != "") {
        $query .= "AND stoneId IN ('" . implode("','", Stones::getStoneIdsByMaterial($material)) . "') ";
      }
      if ($type != "") {
        $query .= "AND stoneId IN ('" . implode("','", Stones::getStoneIdsByType($type)) . "') ";
      }
      $query .= "GROUP BY YEAR(productionDate)";
      $query .= "ORDER BY productionDate DESC ";

      $result = DBConn::db_link()->query($query);
      $chartdata = [];
      while ($row = $result->fetch_assoc()) {
        $vals = [];
        $vals["category"] = $row["orderyear"];
        $vals["value"] = round($row["total_meters"]);
        $chartdata[$row["orderyear"]] = $vals;
      }

      if ($material === 'keramiek' || $material === '') {
        $query = "SELECT YEAR(opdracht_datum) as year, SUM(meters) as meters FROM " . OldOffertes::getTablename() . " WHERE status='done' AND opdracht_datum!='' GROUP BY status, YEAR(opdracht_datum)";
        $result = DBConn::db_link()->query($query);
        while ($row = $result->fetch_assoc()) {
          if (!isset($chartdata[$row["year"]]["value"])) {
            $chartdata[$row["year"]]["category"] = $row["year"];
            $chartdata[$row["year"]]["value"] = 0;
          }
          $chartdata[$row["year"]]["value"] += round($row["meters"]);
        }
      }

      krsort($chartdata);

      $name = "Bestelde meters";
      $this->material = $material;
      $this->type = $type;
      $this->year = $year;
      $this->chartdata = [
        "name"   => $name,
        "data"   => array_values($chartdata),
        "series" => [
          ["name" => $name, "valuecolumn" => "value"],
        ],
      ];
      $this->template_wrapper_clear = true;
    }


    public function executeChartstatus() {

      $material = $_GET["material"] ?? '';
      $type = $_GET["type"] ?? '';
      $year = $_GET["year"] ?? date("Y");

      $query = "SELECT meters, statusId, quotationNumber, quotationVersion ";
      $query .= "FROM " . Quotations::getTablename() . " ";
      $query .= "WHERE statusId!=70 AND statusId!=80 ";
      $query .= "AND YEAR(quotationDate)=" . $year . " ";
      if ($material != "") {
        $query .= "AND stoneId IN ('" . implode("','", Stones::getStoneIdsByMaterial($material)) . "') ";
      }
      if ($type != "") {
        $query .= "AND stoneId IN ('" . implode("','", Stones::getStoneIdsByType($type)) . "') ";
      }
      $query .= "ORDER BY quotationId ASC ";

      $stati = AppModel::mapObjectIds(Status::find_all("ORDER BY orderid"), "statusId");

      $result = DBConn::db_link()->query($query);
      $chartdata = [];
      $quotationNumbersDone = [];
      while ($row = $result->fetch_assoc()) {
        if (!isset($chartdata[$row["statusId"]])) {
          $vals = [];
          $vals["statusId"] = $row["statusId"];
          $vals["category"] = $stati[$row["statusId"]]->bedrijvengidsName;
          $vals["value"] = 0;
          $chartdata[$row["statusId"]] = $vals;
        }
        $chartdata[$row["statusId"]]["value"] += $row["meters"];

        if (isset($quotationNumbersDone[$row["quotationNumber"]])) {
          //hey...dit offertenummer bestaat al. voorgaande lengte verwijderen.
          $chartdata[$row["statusId"]]["value"] -= $quotationNumbersDone[$row["quotationNumber"]];
        }
        $quotationNumbersDone[$row["quotationNumber"]] = $row["meters"];
      }

      usort($chartdata, function ($a, $b) {
        return $a["statusId"] - $b["statusId"];
      });

      foreach ($chartdata as &$val) {
        $val["value"] = round($val["value"]);
      }

      $name = "Meters per status";
      $this->material = $material;
      $this->type = $type;
      $this->year = $year;
      $this->chartdata = [
        "name"   => $name,
        "data"   => $chartdata,
        "series" => [
          ["name" => $name, "valuecolumn" => "value"],
        ],
      ];
      $this->template_wrapper_clear = true;
    }


    public function executeCharttender() {

      $material = $_GET["material"] ?? '';
      $type = $_GET["type"] ?? '';
      $year = $_GET["year"] ?? date("Y");

      $query = "SELECT meters, quotationDate, YEAR(quotationDate) as year, MONTH(quotationDate) as month, quotationNumber ";
      $query .= "FROM " . Quotations::getTablename() . " ";
      $query .= "WHERE statusId!=70 AND statusId!=80 ";  //niet gesplitst of verwijderd
      $query .= "AND (YEAR(quotationDate)=" . $year . " OR YEAR(quotationDate)=" . ($year - 1) . ") ";
      if ($material != "") {
        $query .= "AND stoneId IN ('" . implode("','", Stones::getStoneIdsByMaterial($material)) . "') ";
      }
      if ($type != "") {
        $query .= "AND stoneId IN ('" . implode("','", Stones::getStoneIdsByType($type)) . "') ";
      }
      $query .= "ORDER BY quotationDate DESC ";

      $result = DBConn::db_link()->query($query);
      $chartdata = [];

      for ($month = 1; $month <= 12; $month++) {
        $vals["month"] = $month;
        $vals["category"] = strftimesafe("%b", strtotime("2020-" . $month . "-01-01"));
        $vals["value"] = 0;
        $vals["value2"] = 0;
        $chartdata[$month] = $vals;
      }
      $totals = [
        $year     => 0,
        "compare" => 0,
        $year - 1 => 0,
      ];
      $quotationNumbersDone = [];
      while ($row = $result->fetch_assoc()) {
        if ($row["year"] == $year) {
          $chartdata[$row["month"]]["value"] += $row["meters"];
          $totals[$year] += $row["meters"];
          if (isset($quotationNumbersDone[$row["quotationNumber"]])) {
            //hey...dit offertenummer bestaat al. voorgaande lengte verwijderen.
            $chartdata[$row["month"]]["value"] -= $quotationNumbersDone[$row["quotationNumber"]];
            $totals[$year] -= $quotationNumbersDone[$row["quotationNumber"]];
          }
        }
        else {

          $compare_last_year = $year - 1 == (date("Y") - 1) && strtotime($row["quotationDate"]) < strtotime("-1 YEAR");
          if ($compare_last_year) {
            //waarde van vorige jaar berekenen tot nu
            $totals["compare"] += $row["meters"];
          }


          $chartdata[$row["month"]]["value2"] += $row["meters"];
          $totals[$year - 1] += $row["meters"];
          if (isset($quotationNumbersDone[$row["quotationNumber"]])) {
            //hey...dit offertenummer bestaat al. voorgaande lengte verwijderen.
            $chartdata[$row["month"]]["value2"] -= $quotationNumbersDone[$row["quotationNumber"]];
            $totals[$year - 1] -= $quotationNumbersDone[$row["quotationNumber"]];
            if ($compare_last_year) {
              //waarde van vorige jaar berekenen tot nu
              $totals["compare"] -= $quotationNumbersDone[$row["quotationNumber"]];
            }
          }
        }
        $quotationNumbersDone[$row["quotationNumber"]] = $row["meters"];
      }
      usort($chartdata, function ($a, $b) {
        return $a["month"] - $b["month"];
      });

      foreach ($chartdata as &$val) {
        $val["value"] = round($val["value"]);
        $val["value2"] = round($val["value2"]);
      }

      //pd($chartdata);

      $name = "Offerte meters";
      $this->chartid = "charttender";
      $this->material = $material;
      $this->type = $type;
      $this->year = $year;
      $this->totals = $totals;
      $this->chartdata = [
        "name"   => $name,
        "data"   => $chartdata,
        "series" => [
          ["name" => (string)($year - 1), "valuecolumn" => "value2"],
          ["name" => $year, "valuecolumn" => "value"],
        ],
      ];
      $this->template_wrapper_clear = true;
    }

    public function executeChartorder() {

      $material = $_GET["material"] ?? '';
      $type = $_GET["type"] ?? '';
      $year = $_GET["year"] ?? date("Y");

      $query = "SELECT meters as value, productionDate, YEAR(productionDate) as year, MONTH(productionDate) as month ";
      $query .= "FROM " . Quotations::getTablename() . " ";
      $query .= "WHERE statusId!=10 AND statusId!=70 AND statusId!=80 "; //niet gesplitst of verwijderd
      $query .= "AND (YEAR(productionDate)=" . $year . " OR YEAR(productionDate)=" . ($year - 1) . ") ";
      if ($material != "") {
        $query .= "AND stoneId IN ('" . implode("','", Stones::getStoneIdsByMaterial($material)) . "') ";
      }
      if ($type != "") {
        $query .= "AND stoneId IN ('" . implode("','", Stones::getStoneIdsByType($type)) . "') ";
      }
//      $query .= "GROUP BY YEAR(productionDate), MONTH(productionDate)";
      $query .= "ORDER BY productionDate DESC ";

      $result = DBConn::db_link()->query($query);
      $chartdata = [];

      for ($month = 1; $month <= 12; $month++) {
        $vals["month"] = $month;
        $vals["category"] = strftimesafe("%b", strtotime("2020-" . $month . "-01-01"));
        $vals["value"] = 0;
        $vals["value2"] = 0;
        $chartdata[$month] = $vals;
      }
      $totals = [
        $year     => 0,
        "compare" => 0,
        $year - 1 => 0,
      ];
      while ($row = $result->fetch_assoc()) {
        $vals = $chartdata[$row["month"]];
        if ($row["year"] == $year) {
          $vals["value"] += round($row["value"]);
          $totals[$year] += $row["value"];
        }
        else {
          $vals["value2"] += round($row["value"]);
          $totals[$year - 1] += $row["value"];

          $compare_last_year = $year - 1 == (date("Y") - 1) && strtotime($row["productionDate"]) < strtotime("-1 YEAR");
          if ($compare_last_year) {
            //waarde van vorige jaar berekenen tot nu
            $totals["compare"] += $row["value"];
          }

        }

        $chartdata[$row["month"]] = $vals;
      }
      usort($chartdata, function ($a, $b) {
        return $a["month"] - $b["month"];
      });

      $name = "Bestelling meters";
      $this->chartid = "chartorder";
      $this->material = $material;
      $this->type = $type;
      $this->year = $year;
      $this->totals = $totals;
      $this->chartdata = [
        "name"   => $name,
        "data"   => $chartdata,
        "series" => [
          ["name" => (string)($year - 1), "valuecolumn" => "value2"],
          ["name" => $year, "valuecolumn" => "value"],
        ],
      ];
      $this->template_wrapper_clear = true;
    }

    public function executeCharttendercount() {

      $material = $_GET["material"] ?? '';
      $type = $_GET["type"] ?? '';
      $year = $_GET["year"] ?? date("Y");

      $query = "SELECT quotationDate, YEAR(quotationDate) as year, MONTH(quotationDate) as month, quotationNumber ";
      $query .= "FROM " . Quotations::getTablename() . " ";
      $query .= "WHERE statusId!=70 AND statusId!=80 ";  //niet gesplitst of verwijderd
      $query .= "AND (YEAR(quotationDate)=" . $year . " OR YEAR(quotationDate)=" . ($year - 1) . ") ";
      if ($material != "") {
        $query .= "AND stoneId IN ('" . implode("','", Stones::getStoneIdsByMaterial($material)) . "') ";
      }
      if ($type != "") {
        $query .= "AND stoneId IN ('" . implode("','", Stones::getStoneIdsByType($type)) . "') ";
      }
      $query .= "ORDER BY quotationDate DESC ";

      $result = DBConn::db_link()->query($query);
      $chartdata = [];

      for ($month = 1; $month <= 12; $month++) {
        $vals["month"] = $month;
        $vals["category"] = strftimesafe("%b", strtotime("2020-" . $month . "-01-01"));
        $vals["value"] = 0;
        $vals["value2"] = 0;
        $chartdata[$month] = $vals;
      }
      $totals = [
        $year     => 0,
        "compare" => 0,
        $year - 1 => 0,
      ];
      $quotationNumbersDone = [];
      while ($row = $result->fetch_assoc()) {
        if ($row["year"] == $year) {
          $chartdata[$row["month"]]["value"]++;
          $totals[$year]++;
          if (isset($quotationNumbersDone[$row["quotationNumber"]])) {
            //hey...dit offertenummer bestaat al. voorgaande lengte verwijderen.
            $chartdata[$row["month"]]["value"]--;
            $totals[$year] -= $quotationNumbersDone[$row["quotationNumber"]];
          }
        }
        else {
          $chartdata[$row["month"]]["value2"]++;
          $totals[$year - 1]++;
          $compare_last_year = $year - 1 == (date("Y") - 1) && strtotime($row["quotationDate"]) < strtotime("-1 YEAR");
          if ($compare_last_year) {
            //waarde van vorige jaar berekenen tot nu
            $totals["compare"]++;
          }
          if (isset($quotationNumbersDone[$row["quotationNumber"]])) {
            //hey...dit offertenummer bestaat al. voorgaande lengte verwijderen.
            $chartdata[$row["month"]]["value2"]--;
            $totals[$year - 1] -= $quotationNumbersDone[$row["quotationNumber"]];
            if ($compare_last_year) {
              //waarde van vorige jaar berekenen tot nu
              $totals["compare"] -= $quotationNumbersDone[$row["quotationNumber"]];
            }
          }
        }
        $quotationNumbersDone[$row["quotationNumber"]] = true;
      }
      usort($chartdata, function ($a, $b) {
        return $a["month"] - $b["month"];
      });

      foreach ($chartdata as &$val) {
        $val["value"] = round($val["value"]);
        $val["value2"] = round($val["value2"]);
      }

      //pd($chartdata);

      $name = "Offerte aantal";
      $this->chartid = "charttendercount";
      $this->material = $material;
      $this->type = $type;
      $this->year = $year;
      $this->totals = $totals;
      $this->chartdata = [
        "name"   => $name,
        "data"   => $chartdata,
        "series" => [
          ["name" => (string)($year - 1), "valuecolumn" => "value2"],
          ["name" => $year, "valuecolumn" => "value"],
        ],
      ];
      $this->template_wrapper_clear = true;
    }

    public function executeChartordercount() {

      $material = $_GET["material"] ?? '';
      $type = $_GET["type"] ?? '';
      $year = $_GET["year"] ?? date("Y");

      $query = "SELECT count(quotationId) as value, productionDate, YEAR(productionDate) as year, MONTH(productionDate) as month ";
      $query .= "FROM " . Quotations::getTablename() . " ";
      $query .= "WHERE statusId!=10 AND statusId!=70 AND statusId!=80 "; //niet gesplitst of verwijderd
      $query .= "AND (YEAR(productionDate)=" . $year . " OR YEAR(productionDate)=" . ($year - 1) . ") ";
      if ($material != "") {
        $query .= "AND stoneId IN ('" . implode("','", Stones::getStoneIdsByMaterial($material)) . "') ";
      }
      if ($type != "") {
        $query .= "AND stoneId IN ('" . implode("','", Stones::getStoneIdsByType($type)) . "') ";
      }
      $query .= "GROUP BY YEAR(productionDate), MONTH(productionDate)";
      $query .= "ORDER BY productionDate DESC ";

      $result = DBConn::db_link()->query($query);
      $chartdata = [];

      for ($month = 1; $month <= 12; $month++) {
        $vals["month"] = $month;
        $vals["category"] = strftimesafe("%b", strtotime("2020-" . $month . "-01-01"));
        $vals["value"] = 0;
        $chartdata[$month] = $vals;
      }
      $totals = [
        $year     => 0,
        "compare" => 0,
        $year - 1 => 0,
      ];
      while ($row = $result->fetch_assoc()) {
        $vals = [];
        if (isset($chartdata[$row["month"]])) {
          $vals = $chartdata[$row["month"]];
        }
        if ($row["year"] == $year) {
          $vals["value"] = round($row["value"]);
          $totals[$year] += $row["value"];
        }
        else {
          $vals["value2"] = round($row["value"]);
          $totals[$year - 1] += $row["value"];
          $compare_last_year = $year - 1 == (date("Y") - 1) && strtotime($row["productionDate"]) < strtotime("-1 YEAR");
          if ($compare_last_year) {
            //waarde van vorige jaar berekenen tot nu
            $totals["compare"] += $row["value"];
          }
        }
        $chartdata[$row["month"]] = $vals;
      }
      usort($chartdata, function ($a, $b) {
        return $a["month"] - $b["month"];
      });

      $name = "Bestelling aantal";
      $this->chartid = "chartordercount";
      $this->material = $material;
      $this->type = $type;
      $this->year = $year;
      $this->totals = $totals;
      $this->chartdata = [
        "name"   => $name,
        "data"   => $chartdata,
        "series" => [
          ["name" => (string)($year - 1), "valuecolumn" => "value2"],
          ["name" => $year, "valuecolumn" => "value"],
        ],
      ];
      $this->template_wrapper_clear = true;
    }

    public function executeStonesspeed() {

      $type = $_GET["type"] ?? '';

      if (!isset($_SESSION["stat_search"])) $_SESSION["stat_search"] = '';
      if (!isset($_SESSION["stat_from"])) $_SESSION["stat_from"] = '';
      if (!isset($_SESSION["stat_to"])) $_SESSION["stat_to"] = '';
      if (!isset($_SESSION["stat_employee"])) $_SESSION["stat_employee"] = '';
      if (!isset($_SESSION["stat_endstones"])) $_SESSION["stat_endstones"] = '0';

      if (isset($_POST["stat_search"])) {
        $_SESSION["stat_search"] = $_POST["stat_search"];
        $_SESSION["stat_from"] = $_POST["stat_from"];
        $_SESSION["stat_to"] = $_POST["stat_to"];
        $_SESSION["stat_employee"] = $_POST["stat_employee"];
        $_SESSION["stat_endstones"] = isset($_POST["stat_endstones"]) ? 1 : 0;
      }

      $query = "SELECT stoneId, sum(meters) as total_meters, sum(prod_cm_per_hour) as sum_cm_per_hour, count(quotations.quotationId) as total_orders  FROM " . Quotations::getTablename() . " ";
      $query .= "JOIN " . QuotationsExtra::getTablename() . " ON quotations_extra.quotationId=quotations.quotationId ";

      $query .= "WHERE stoneId!=0 AND NOT prod_cm_per_hour IS NULL AND prod_cm_per_hour!=0 ";

      //geen test personen
      $query .= "AND " . DbHelper::getSqlIn('prod_employee_id', ProductionEmployees::TESTERS, true, true);

      if ($_SESSION["stat_from"] != "" && $_SESSION["stat_to"] != "") {
        $query .= "AND productionDate>='" . getTSFromStr($_SESSION["stat_from"]) . "' AND productionDate<='" . getTSFromStr($_SESSION["stat_to"]) . "' ";
      }
      if ($_SESSION["stat_employee"] != "") {
        $query .= "AND prod_employee_id=" . $_SESSION["stat_employee"] . " ";
      }
      if ($type != "") {
        $query .= "AND stoneId IN ('" . implode("','", Stones::getStoneIdsByType($type)) . "') ";
      }
      if ($_SESSION["stat_endstones"] == "0") {
        $query .= "AND (endstone='false' OR endstone='') ";
      }
      else {
        $query .= "AND NOT (endstone='false' OR endstone='') ";
      }
      $query .= "GROUP BY stoneId";

      $stones = AppModel::mapObjectIds(Stones::find_all(), "stoneId");

      $stats = [];
      $result = DBConn::db_link()->query($query);
      while ($row = $result->fetch_assoc()) {
        $vals = [];
        $vals["total_meters"] = $row["total_meters"];
        $vals["total_orders"] = $row["total_orders"];
        $vals["cm_per_hour"] = floor($row["sum_cm_per_hour"] / $vals["total_orders"]);
        $vals["stone"] = $stones[$row["stoneId"]] ?? null;

        if ($_SESSION["stat_search"] != "") {
          if (empty($vals["stone"]->name) || stripos($vals["stone"]->name, $_SESSION["stat_search"]) === false) {
            continue;
          }
        }


        $stats[] = $vals;
      }

      if (isset($_GET["sort"])) {
        if ($_GET["sort"] == "total_meters") {
          usort($stats, function ($a, $b) {
            return $b["total_meters"] - $a["total_meters"];
          });
        }
        elseif ($_GET["sort"] == "cm_per_hour") {
          usort($stats, function ($a, $b) {
            return $b["cm_per_hour"] - $a["cm_per_hour"];
          });
        }
        elseif ($_GET["sort"] == "total_orders") {
          usort($stats, function ($a, $b) {
            return $b["total_orders"] - $a["total_orders"];
          });
        }
        elseif ($_GET["sort"] == "stone") {
          usort($stats, function ($a, $b) {
            if (empty($a["stone"])) return 1;
            if (empty($b["stone"])) return -1;
            return strcmp($a["stone"]->name, $b["stone"]->name);
          });
        }
        if ($_GET["dir"] == "up") {
          $stats = array_reverse($stats);
        }
      }
      else {
        usort($stats, function ($a, $b) {
          return $b["cm_per_hour"] - $a["cm_per_hour"];
        });
      }


      $this->stats = $stats;
    }


    public function executeStonesspeeddetail() {
      $stone = Stones::find_by(["stoneId" => $_GET["stoneid"]]);
      $employees = AppModel::mapObjectIds(ProductionEmployees::find_all_by(["working" => 1], "ORDER BY name"), "employeeId");
      if (isset($_POST["stat_from"])) {
        $_SESSION["stat_from"] = $_POST["stat_from"];
        $_SESSION["stat_to"] = $_POST["stat_to"];
        $_SESSION["stat_employee"] = $_POST["stat_employee"];
        $_SESSION["stat_endstones"] = isset($_POST["stat_endstones"]) ? 1 : 0;
      }
      $query = "SELECT * FROM " . Quotations::getTablename() . " ";
      $query .= "JOIN " . QuotationsExtra::getTablename() . " ON quotations_extra.quotationId=quotations.quotationId ";

      $query .= "WHERE stoneId=" . $stone->stoneId . " AND NOT prod_cm_per_hour IS NULL AND prod_cm_per_hour!=0 ";

      //geen test personen
      $query .= "AND " . DbHelper::getSqlIn('prod_employee_id', ProductionEmployees::TESTERS, true, true);

      if ($_SESSION["stat_from"] != "" && $_SESSION["stat_to"] != "") {
        $query .= "AND productionDate>='" . getTSFromStr($_SESSION["stat_from"]) . "' AND productionDate<='" . getTSFromStr($_SESSION["stat_to"]) . "' ";
      }
      if ($_SESSION["stat_employee"] != "") {
        $query .= "AND prod_employee_id=" . $_SESSION["stat_employee"] . " ";
      }
      if ($_SESSION["stat_endstones"] == "0") {
        $query .= "AND (endstone='false' OR endstone='') ";
      }
      else {
        $query .= "AND NOT (endstone='false' OR endstone='') ";
      }
      $query .= "ORDER BY productionDate";

      $stats = [];
      $result = DBConn::db_link()->query($query);
      while ($row = $result->fetch_row()) {
        $quot = new Quotations();
        $quot->hydrate($row);
        $quotextra = new QuotationsExtra();
        $quotextra->hydrate($row, count(Quotations::columns));
        $quot->quotations_extra = $quotextra;

        if (!isset($stats[$quotextra->prod_employee_id])) {
          $stats[$quotextra->prod_employee_id]["quotes"] = [];
          $stats[$quotextra->prod_employee_id]["total_meters"] = 0;
          $stats[$quotextra->prod_employee_id]["total_orders"] = 0;
          $stats[$quotextra->prod_employee_id]["total_cm_per_hour"] = 0;
          $stats[$quotextra->prod_employee_id]["cm_per_hour"] = 0;
          $stats[$quotextra->prod_employee_id]["employeeId"] = $quotextra->prod_employee_id;
          $stats[$quotextra->prod_employee_id]["employeeName"] = isset($employees[$quotextra->prod_employee_id]) ? $employees[$quotextra->prod_employee_id]->name : '';
        }

//        $stats[$quotextra->prod_employee_id]["quotes"][] = $quot;
        $stats[$quotextra->prod_employee_id]["total_orders"]++;
        $stats[$quotextra->prod_employee_id]["total_meters"] += $quot->meters;
        $stats[$quotextra->prod_employee_id]["total_cm_per_hour"] += $quotextra->prod_cm_per_hour;
        $stats[$quotextra->prod_employee_id]["cm_per_hour"] = $stats[$quotextra->prod_employee_id]["total_cm_per_hour"] / $stats[$quotextra->prod_employee_id]["total_orders"];
      }


      if (isset($_GET["sort"])) {
        if ($_GET["sort"] == "total_meters") {
          usort($stats, function ($a, $b) {
            return $a["total_meters"] <=> $b["total_meters"];
          });
        }
        elseif ($_GET["sort"] == "cm_per_hour") {
          usort($stats, function ($a, $b) {
            return $b["cm_per_hour"] <=> $a["cm_per_hour"];
          });
        }
        elseif ($_GET["sort"] == "total_orders") {
          usort($stats, function ($a, $b) {
            return $b["total_orders"] <=> $a["total_orders"];
          });
        }
        elseif ($_GET["sort"] == "employee") {
          usort($stats, function ($a, $b) {
            return strcmp($a["employeeName"], $b["employeeName"]);
          });
        }
        if ($_GET["dir"] == "up") {
          $stats = array_reverse($stats);
        }
      }
      else {
        usort($stats, function ($a, $b) {
          return $b["cm_per_hour"] <=> $a["cm_per_hour"];
        });
      }


      $this->stats = $stats;
      $this->stone = $stone;
      $this->employees = $employees;
    }


    public function executeEmployeesspeed() {
      $this->employees = AppModel::mapObjectIds(ProductionEmployees::find_all_by(["working" => 1], "ORDER BY name"), "employeeId");
    }

    public function executeEmployeesspeeddetail() {


      if (!isset($_SESSION["employee_glue_from"])) $_SESSION["employee_glue_from"] = "01-01-" . (date("Y") - 1);
      if (!isset($_SESSION["employee_glue_to"])) $_SESSION["employee_glue_to"] = "31-12-" . date("Y");

      if (isset($_POST["search"])) {
        $_SESSION["employee_glue_from"] = $_POST["employee_glue_from"];
        $_SESSION["employee_glue_to"] = $_POST["employee_glue_to"];
        ResponseHelper::redirect(reconstructQuery());
      }

      $employee = ProductionEmployees::find_by(['employeeId' => $_GET["employeeid"]]);
      $stone_speed = (new StoneSpeed());
      if (!empty($_SESSION["employee_glue_from"])) {
        $stone_speed->setFromDate(getTSFromStr($_SESSION["employee_glue_from"]));
      }
      if (!empty($_SESSION["employee_glue_to"])) {
        $stone_speed->setToDate(getTSFromStr($_SESSION["employee_glue_to"]));
      }
      $stones = $stone_speed->getEmployeeStoneSpeed($employee);
      $stats = $stone_speed->getStats();

      $speed = '';
      $this->total_meters = $this->total_orders = 0;

      if (isset($stats[$employee->employeeId])) {
        $this->total_meters = $stats[$employee->employeeId]["total_meters"];
        $this->total_orders = $stats[$employee->employeeId]["total_orders"];
        $speed = 'Bij ' . $stone_speed->getFaster() . ' van de ' . count($stones) . " steensoorten (" . floor(($stone_speed->getFaster() / count($stones) * 100)) . "%), is deze medewerker minimaal 10% sneller als zijn collega's.<Br/>";
        $speed .= 'Bij ' . $stone_speed->getSlower() . ' van de ' . count($stones) . " steensoorten (" . floor(($stone_speed->getSlower() / count($stones) * 100)) . "%), is deze medewerker minimaal 10% langzamer als zijn collega's.";
      }
      $this->speed = $speed;
      $this->employee = $employee;
      $this->stones = $stones;
    }

    public function executeEmployeesspeeddetailchart() {

      if (!isset($_SESSION["employee_glue_from"])) $_SESSION["employee_glue_from"] = "01-01-" . date("Y");
      if (!isset($_SESSION["employee_glue_to"])) $_SESSION["employee_glue_to"] = "31-12-" . date("Y");

      if (isset($_POST["search"])) {
        $_SESSION["employee_glue_from"] = $_POST["employee_glue_from"];
        $_SESSION["employee_glue_to"] = $_POST["employee_glue_to"];
        ResponseHelper::redirect(reconstructQuery());
      }

      $employee = ProductionEmployees::find_by(['employeeId' => $_GET["employeeid"]]);
      $productionSpeed = (new ProductionSpeed($employee));
      if (!empty($_SESSION["employee_glue_from"])) {
        $productionSpeed->setFromDate(getTSFromStr($_SESSION["employee_glue_from"]));
      }
      if (!empty($_SESSION["employee_glue_to"])) {
        $productionSpeed->setToDate(getTSFromStr($_SESSION["employee_glue_to"]));
      }
      $datas = $productionSpeed->execute();

      $chartdata = [];
      $chartdata = [
        "name"   => "Cm per uur",
        "data"   => [],
        "series" => [
          ["name" => "Cm per uur", "valuecolumn" => "value"],
        ],
      ];

      foreach ($datas as $hour => $cmPerHour) {
        $chartdata["data"][$hour]["category"] = $hour;
        $chartdata["data"][$hour]["value"] = $cmPerHour;
      }

//      dumpe($chartdata);
      $chartdata["data"] = array_values($chartdata["data"]);
      $this->chartdata = $chartdata;
      $this->employee = $employee;

    }


    public function executeSuscpiciousquotations() {

      $forms = [];
      foreach (Setting::getAllByType("suspicious") as $setting) {
        $form = new ModelForm("setting" . $setting->id);
        $form->buildElementsFromModel($setting);
        $number = new Number("Meter", "value", $setting->value);
        $number->setLabel($setting->code);
        $form->addElement($number);
        $form->setElementsRequired(["value"]);
        $forms[] = $form;
      }


      if (isset($_POST['go'])) {

        $allvalid = true;
        foreach ($forms as $form) {
          $form->setElementsAndObjectValue($_POST);
          if (!$form->isValid()) {
            $allvalid = false;
          }
        }

        if ($allvalid) {
          foreach ($forms as $form) {
            $form->getModelobject()->save();
          }
          $_SESSION['flash_message'] = "Gegevens opgeslagen";
          ResponseHelper::redirect(reconstructQuery());
        }

      }

      $this->forms = $forms;
    }

    public function executeChartordertodeliver() {

      $material = $_GET["material"] ?? '';
      $type = $_GET["type"] ?? '';
      $year = $_GET["year"] ?? date("Y");

      $query = "SELECT quotationId, deliverDate ";
      $query .= "FROM " . ContainersQuotations::getTablename() . " ";
      $query .= "WHERE YEAR(deliverDate)>=2013 ";
      $result = DBConn::db_link()->query($query);
      $deliver_dates = [];
      while ($row = $result->fetch_assoc()) {
        $deliver_dates[$row["quotationId"]] = $row["deliverDate"];
      }


      $chartdata = [];

      for ($month = 1; $month <= 12; $month++) {
        $vals["month"] = $month;
        $vals["category"] = strftimesafe("%b", strtotime("2020-" . $month . "-01-01"));
        $vals["value"] = 0;
        $vals["value2"] = 0;
        $chartdata[$month] = $vals;
      }

      $query = "SELECT quotations.quotationId, productionDate, meters  ";
      $query .= "FROM " . Quotations::getTablename() . " ";
      $query .= "WHERE statusId!=10 AND statusId!=70 AND statusId!=80 "; //niet gesplitst of verwijderd
      //$query .= "AND productionDate<'".date("Y-m-d",strtotime("+1 MONTH",$time))."' ";
      $query .= "AND YEAR(productionDate)>=2013 ";
      if ($material != "") {
        $query .= "AND stoneId IN ('" . implode("','", Stones::getStoneIdsByMaterial($material)) . "') ";
      }
      if ($type != "") {
        $query .= "AND stoneId IN ('" . implode("','", Stones::getStoneIdsByType($type)) . "') ";
      }
      //echo $query;
      $result = DBConn::db_link()->query($query);
      $values = [];
      while ($row = $result->fetch_assoc()) {

        $order_time = strtotime(date("Y-m-01", strtotime($row["productionDate"])));

        if (!isset($deliver_dates[$row["quotationId"]]) && $order_time < strtotime("2019-01-01")) {
          //besteld voor 2019 en geen leverdatum...niet meetellen.
          continue;
        }
        $key = date("Y-m-d", $order_time);
        if (!isset($values[$key])) {
          $values[$key] = [
            "ordered"   => 0,
            "delivered" => 0,
          ];
        }

        if (isset($deliver_dates[$row["quotationId"]])) {
          $deliver_time = strtotime(date("Y-m-01", strtotime($deliver_dates[$row["quotationId"]])));
          $deliver_key = date("Y-m-d", $deliver_time);
          if (!isset($values[$deliver_key])) {
            $values[$deliver_key] = [
              "ordered"   => 0,
              "delivered" => 0,
            ];
          }
          $values[$deliver_key]["delivered"] += $row["meters"];
        }

        $values[$key]["ordered"] += $row["meters"];
        //$values[$key]["quotationIds"][$row["quotationId"]] = $row["quotationId"];

      }

      //pd($values);

      $totals = [
        $year     => 0,
        $year - 1 => 0,
      ];

      $total_ordered = 0;
      $total_delivered = 0;
      foreach ($values as $date => $value) {

        $order_time = strtotime($date);
        $total_ordered += $value["ordered"];
        $total_delivered += $value["delivered"];

        if ($order_time < strtotime(($year - 1) . "-01-01")) {
          //oudere data, niet opnemen
          continue;
        }

        $order_month = date("n", $order_time);
        $order_year = date("Y", $order_time);
        if ($order_year == $year) {
          $chartdata[$order_month]["value"] = round($total_ordered - $total_delivered);
        }
        elseif ($order_year == $year - 1) {
          $chartdata[$order_month]["value2"] = round($total_ordered - $total_delivered);
        }
        if (isset($totals[$order_year])) $totals[$order_year] += $total_ordered - $total_delivered;
      }

      usort($chartdata, function ($a, $b) {
        return $a["month"] - $b["month"];
      });

      $name = "Bestelling meters";
      $this->chartid = "chartordertodeliver";
      $this->material = $material;
      $this->type = $type;
      $this->year = $year;
      $this->totals = $totals;
      $this->chartdata = [
        "name"   => $name,
        "data"   => $chartdata,
        "series" => [
          ["name" => (string)($year - 1), "valuecolumn" => "value2"],
          ["name" => $year, "valuecolumn" => "value"],
        ],
      ];
      $this->template_wrapper_clear = true;
    }

    public function executeLargeworklist() {
      $this->createOverviewFilters();
    }

    private function createOverviewFilters() {

      $dataTable = new DataTable("large-work-datatable");
      $dataTable->setRequestUrl(reconstructQueryAdd() . "action=ajax");

      $dataTable->addColumnHelper("quotationDate", "Datum");
      $dataTable->addColumnHelper("name", "Bedrijfnaam");
      $dataTable->addColumnHelper("meters", "Meters")->addClass("dt-body-right");
      $dataTable->addColumnHelper("domestic", "Plaats");
      $dataTable->addColumnHelper("quotationNumber", "Offertenummer");
      $dataTable->addColumnHelper("projectName", "Werk");
      $dataTable->addColumnHelper("projectReference", "Kenmerk");
      $dataTable->addColumnHelper("largeworklistinfo", "Extra info");

      $variants = [
        "overzicht",
        "opdracht",
        "gekregen",
        "onderaannemer",
        "via handel",
        "niet gekregen",
        "concurent",
        "handel",
        "te duur",
        "ander product",
        "te duur",
        "geannuleerd",
        "anders",
      ];
      $select = new Select("Variant", 'variant');
      foreach ($variants as $variant) {
        $select->addOptionHelper($variant, ucfirst($variant) . ' scherm');
      }
      $dataTable->getForm()->addElement($select);
      $dataTable->addSearchInput();


      $dataTable->addSearchReset();

      $dataTable->handleRequest($_POST);

      $this->dataTable = $dataTable;

    }


    public function executeAjax() {

      $this->createOverviewFilters();

      $filter = "LEFT JOIN " . CrmCompanies::getTablename() . " ON quotations.companyId = crm_companies.companyId ";
      $filter .= "WHERE metersMuch = 'true' ";

      $variant = $this->dataTable->getFormElementValue("variant");
      if ($variant == "opdracht") {
        $filter .= "AND statusId > 11 ";
        $filter .= "AND NOT statusId IN (70, 80) ";
      }
      elseif ($variant == "overzicht") {
        $filter .= "AND statusId IN (10, 80) ";
        $filter .= "AND largeWorkList = 'overzicht' ";
      }
      else {
        $filter .= ' AND largeWorkList = "' . $variant . '" ';
      }


      if ($this->dataTable->hasFormElementValue("search")) {
        $searchstr = escapeForDB($this->dataTable->getFormElementValue("search"));
        $filter .= "AND ( ";
        $filter .= "name LIKE '%" . $searchstr . "%' OR ";
        $filter .= "domestic LIKE '%" . $searchstr . "%' OR ";
        $filter .= "quotationNumber LIKE '%" . $searchstr . "%' OR ";
        $filter .= "projectName LIKE '%" . $searchstr . "%' ";
        $filter .= ") ";
      }

      $total_count = Quotations::count_all_by([], $filter);
      $total_count_filtered = Quotations::count_all_by([], $filter);

      $query = "SELECT * FROM " . Quotations::getTablename() . " ";
      $query .= $filter;
      if (!empty($this->dataTable->getSortColumn())) {
        $query .= $this->dataTable->getSortQuery();
      }
      else {
        $query .= " ORDER BY quotationId ASC";
      }
      $query .= $this->dataTable->getPager()->getLimitQuery();
      $result = DBConn::db_link()->query($query);

      /** @var Quotations[] $quotations */
      $quotations = [];
      while ($row = $result->fetch_row()) {
        $column_counter = 0;
        $quotation = (new Quotations())->hydrateNext($row, $column_counter);
        $quotation->company = (new CrmCompanies())->hydrateNext($row, $column_counter);
        $quotations[] = $quotation;
      }

      $table_data = [];
      foreach ($quotations as $quotation) {

        $table_data[] = [
          'DT_RowId'          => $quotation->quotationId,
          'quotationDate'     => $quotation->getQuotationDate(),
          'name'              => $quotation->company->name,
          'meters'            => $quotation->meters,
          'domestic'          => $quotation->domestic,
          //'quotationNumber'       => '<a href="' .  . '">' . $quotation->getQuotationNumberFull() . '</a>',
          'quotationNumber'   => $quotation->getQuotationNumberFull(),
          'projectName'       => $quotation->projectName,
          'projectReference'  => $quotation->projectReference,
          'largeworklistinfo' => $quotation->largeworklistinfo,
        ];
      }

      /** RETURN DATA AS JSON */
      ResponseHelper::exitAsJson([
        'data'            => $table_data,
        'recordsTotal'    => $total_count,
        'recordsFiltered' => $total_count_filtered,
        'draw'            => (int)$_POST['draw'],
      ]);
    }

  }