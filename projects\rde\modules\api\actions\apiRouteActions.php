<?php

  use gsdfw\domain\messagebird\helpers\CleanPhonenumber;
  use gsdfw\domain\messagebird\service\SmsApi;
  use gsdfw\domain\messagebird\service\WhatsappApi;


  /**
   * Trait apiCmsActions
   * Used route actions
   */
  trait apiRouteActions {

    /**
     * Get routeitems for vrachtwagen app
     * @param bool $timestamp
     * @param bool $truckid
     * @param bool $return
     * @return array|void
     */
    public function executeGetRouteItems($timestamp = false, $truckid = false, $return = false) {
      $request_vars = $this->data->getData();
      if ($timestamp === false) {
        $timestamp = $request_vars['date'];
        $truckid = $request_vars['truck_id'];
      }
      $extra_data = true;
      if (!$return) {
        $extra_data = isset($request_vars['company_data']) ? true : false;
      }

      $date = date('Y-m-d', intval($timestamp / 1000)); //convert to php timestamp
//            $date = '2022-05-11';
      //      echo $date;

      $query = "SELECT R.date as deliverDate, R.routeId, R.company, R.truckId, R.date, R.rank, R.subject, R.latitude, R.longitude, R.cargoReceiptId, R.mapExtraPoi, R.city as zipcode, R.domestic ";
      $query .= "FROM " . GpsbuddyRoutes::getTablename() . " R ";
      $query .= "WHERE R.date = '" . $date . "' ";
      if ($truckid !== false) {
        $query .= "AND R.truckId = " . escapeForDB($truckid) . " ";
      }
      $query .= "ORDER BY R.truckId ASC, R.rank ASC, R.routeId ASC";

      $result = DBConn::db_link()->query($query);

      $routes = [];
      $routeids = [];
      while ($oData = $result->fetch_object()) {

        foreach (get_object_vars($oData) as $key => $val) {
          if ($oData->{$key} === null) {
            $oData->{$key} = "";
          }
          elseif ($key == "quotationPart") {
            $oData->{$key} = RdeHelper::asciiIntToAlpha($val);
          }
        }
        $routeids[] = $oData->routeId;
        $routes[] = $oData;

      }

      if (count($routeids) > 0) {

        //ophalen stati
        $query0 = "SELECT statusId, bedrijvengidsName FROM " . Status::getTablename() . " ";
        $oResult0 = DBConn::db_link()->query($query0);
        $quotationStati = [];
        while ($oStati = $oResult0->fetch_object()) {
          $quotationStati[$oStati->statusId] = $oStati->bedrijvengidsName;
        }

        $allContainers = [];

        foreach ($routes as $route) {

          $route->meters = 0;
          $route->sendSMS = false;

          //ophalen quotations per route
          $query1 = "SELECT gpsbuddy_rde.quotationId,gpsbuddy_rde.bakId, quotations.statusId,quotations.quotationNumber,quotations.quotationPart,quotations.quotationVersion,quotations.projectName,quotations.projectReference,quotations.meters,quotations.dispatchAppointment, street, nr, ext, zipcode, domestic, country, userId, companyId, executor, executorMobile, executorMail, executorPersonId ";
          $query1 .= "FROM " . GpsbuddyRde::getTablename() . " ";
          $query1 .= "JOIN " . Quotations::getTablename() . " ON quotations.quotationId=gpsbuddy_rde.quotationId ";
          $query1 .= "WHERE routeId=" . $route->routeId . " ";
          $query1 .= "ORDER BY quotations.quotationNumber ";
          $oResult1 = DBConn::db_link()->query($query1);
          $quotations = [];
          $containers = [];
          $quotationIds = [];
          $notes = [];
          while ($l_quotation = $oResult1->fetch_object()) {

            $container_quotations = ContainersQuotations::find_all_by(["quotationId" => $l_quotation->quotationId], "GROUP BY containerId");
            foreach ($container_quotations as $cont_quot) {

              if (!isset($containers[$cont_quot->containerId])) {
                $container = Containers::find_by(['containerId' => $cont_quot->containerId]);
                $container->returnDate = $cont_quot->returnDate;
                $container->deliverDate = $cont_quot->deliverDate;

                //ophalen tagid van container
                $container->tagIds = [];
                foreach (ContainerTag::find_all_by(["containerId" => $container->containerId]) as $to) {
                  $container->tagIds[] = $to->tagId;
                }

                $container->quotations = [];

                $containers[$container->containerId] = $container;
                $allContainers[$container->containerId] = $container;
                //@todo: hier verder voor performance!
              }

              $containers[$cont_quot->containerId]->quotations[] = $l_quotation->quotationId;

            }


            $route->street = $l_quotation->street;
            $route->nr = $l_quotation->nr;
            $route->ext = $l_quotation->ext;
            $route->zipcode = $l_quotation->zipcode;
            $route->domestic = $l_quotation->domestic;
            $route->country = $l_quotation->country;
            $route->companyId = 0;
            if ($l_quotation->companyId != "") {
              $route->companyId = $l_quotation->companyId;
            }
            if (!empty($l_quotation->dispatchAppointment) && trim($l_quotation->dispatchAppointment) != "" && !in_array(trim($l_quotation->dispatchAppointment), $notes)) {
              $notes[] = trim($l_quotation->dispatchAppointment);
            }
            $route->userId = $l_quotation->userId;
            if (!isset($route->executorPersonId)) {
              $route->executorPersonId = "";
            }
            if ($l_quotation->executorPersonId != "") {
              $route->executorPersonId = $l_quotation->executorPersonId;
              $route->executor = "";
              $route->executorMobile = "";
              $route->executorMail = "";
            }
            else {
              $route->executor = $l_quotation->executor;
              $route->executorMobile = $l_quotation->executorMobile;
              $route->executorMail = $l_quotation->executorMail;
            }

            if (!isset($route->extraInfo)) {
              $route->extraInfo = '';
              $sql = "SELECT c.extraInfo FROM " . QuotationsExtra::getTablename() . " qe ";
              $sql .= "LEFT JOIN " . CrmAddresses::getTablename() . " c ON c.addressId = qe.addressDeliveryId ";
              $sql .= "WHERE qe.quotationId = '" . $l_quotation->quotationId . "' LIMIT 1 ";
              $oResult4 = DBConn::db_link()->query($sql);
              if ($obj = $oResult4->fetch_object()) {
                $route->extraInfo = $obj->extraInfo;
              }
            }

            $l_quotation_short = new stdClass();
            $l_quotation_short->quotationId = $l_quotation->quotationId;
            $l_quotation_short->statusId = $l_quotation->statusId;
            $l_quotation_short->userId = $l_quotation->userId;
            $l_quotation_short->quotationNumber = $l_quotation->quotationNumber;
            $l_quotation_short->quotationPart = $l_quotation->quotationPart == "" ? "" : RdeHelper::asciiIntToAlpha($l_quotation->quotationPart);
            $l_quotation_short->quotationVersion = $l_quotation->quotationVersion;
            $l_quotation_short->projectName = $l_quotation->projectName;
            $l_quotation_short->projectReference = $l_quotation->projectReference;
            $l_quotation_short->meters = (float)$l_quotation->meters;
//            $l_quotation_short->sms = $l_quotation->sms;

            $quotations[] = $l_quotation_short;
            $quotationIds[] = $l_quotation_short->quotationId;

            $route->meters += $l_quotation->meters;

          }
          $route->notes = "";
          if (count($notes) > 0) {
            $route->notes = implode(", ", $notes);
          }
          $route->quotations = $quotations;
          $route->containers = array_values($containers);

          //files
          if (count($quotationIds) > 0) {
            $query5 = "SELECT files.fileId, title as name FROM " . Files::getTablename() . " ";
            $query5 .= "WHERE files.quotationId IN(" . implode(",", $quotationIds) . ") AND categoryId=2 "; //alleen delivery
            $oResult5 = DBConn::db_link()->query($query5);
            while ($row_5 = $oResult5->fetch_object()) {
              $route->files[] = $row_5;
            }
          }

          if ($extra_data) {

            //ook overige containers zetten
            $all_container_nrs = [];
            foreach ($route->containers as $c) {
              $all_container_nrs[] = $c->containerNumber;
            }

            $sendSMSUsersToTheck = [];
            $sendSMSUsersToTheck[$route->userId] = $route->userId;

            $users = [];
            foreach ($route->quotations as $l_q) {
              $users[$l_q->userId] = $l_q->userId;
              $sendSMSUsersToTheck[$l_q->userId] = $l_q->userId;
            }

            $route->sendSMS = QuotationsExtra::count_all_by(["quotationId" => $quotationIds, "sms" => 1], "AND NOT smsnumber IS NULL") > 0;
            if (!$route->sendSMS) {
              foreach (SandboxUsers::find_all_by(["userId" => $sendSMSUsersToTheck, "sms" => 1]) as $sUser) {
                if (!ValidationHelper::isMobile($sUser->mobile)) continue;
                $route->sendSMS = true;
                break;
              }
            }


            $route->containers_other = apiRdeActions::getContainersNearFar($route->companyId, $users, $route->zipcode, $date, $all_container_nrs);

            //tagids toevoegen
            foreach ($route->containers_other as $ca) {
              if (isset($containers[$ca->containerId])) {
                $ca->tagIds = $containers[$ca->containerId]->tagIds;
              }
              else {
                $ca->tagIds = [];
                foreach (ContainerTag::find_all_by(["containerId" => $ca->containerId]) as $to) {
                  $ca->tagIds[] = $to->tagId;
                }
              }
            }

            $route->companyName = "";
            $route->persons = [];

            if ($route->companyId != 0) {

              $company = CrmCompanies::find_by(["companyId" => $route->companyId]);
              $route->companyName = ($company->name == null ? "" : $company->name);
              $route->companyEmail = (ValidationHelper::isEmail($company->email) ? $company->email : "");
              $route->companyPhone = $company->phone;

              //alle personen gekoppeld bij dit bedrijf
              $persons = CrmPersons::find_all_by(["companyId" => $route->companyId, "flagForDeletion" => 0]);
              foreach ($persons as $person_o) {
                $person = [];
                $person['id'] = ($person_o->personId + 1000000);
                $person['personid'] = $person_o->personId;
                $person['userid'] = 0;
                $person['name'] = $person_o->firstName . ' ' . $person_o->lastName;
                $person['phone'] = $person_o->phone;
                $person['mobile'] = $person_o->mobile;
                $person['email'] = $person_o->email;
                if ($person_o->flagForExecutor == 1) {
                  $person['type'] = 'executor';

                  //dit is een executor. Ophalen extra informatie
                  $queryE = "SELECT quotations.quotationId, quotations.quotationNumber,quotations.quotationPart,quotations.quotationVersion,quotations.projectName,quotations.projectReference,quotations.domestic FROM " . Quotations::getTablename() . " ";
                  $queryE .= "WHERE executorPersonId=" . $person_o->personId . " ";
                  $queryE .= "AND statusId>=20 ";
                  $queryE .= "AND quotationDate>='" . date("Y-m-d", strtotime("-1 YEAR")) . "' ";
                  $queryE .= "GROUP BY quotationId ";
                  $queryE .= "ORDER BY quotationId DESC ";

                  $oResultE = DBConn::db_link()->query($queryE);
                  $extrainfo = [];
                  while ($ei = $oResultE->fetch_object()) {
                    $info = $ei->quotationNumber . '-' . $ei->quotationVersion;
                    if ($ei->quotationPart != "") {
                      $info .= '-' . $ei->quotationPart; //is als letter zichtbaar
                    }
                    if ($ei->projectName != "") {
                      $info .= ' - ' . $ei->projectName;
                    }
                    if ($ei->domestic != "") {
                      $info .= ' - ' . $ei->domestic;
                    }

                    $extrainfo[substr($ei->quotationNumber, 2)] = $info;
                  }
                  $person['extrainfo'] = implode("\n", $extrainfo);

                }
                else {
                  $person['type'] = 'other';
                }

                if ($person_o->email == "") { //is er een sandbox user met email? Gebruik deze dan na @
                  $person_s = SandboxUsers::find_by(["personId" => $person_o->personId]);
                  if ($person_s && ValidationHelper::isEmail($person_s->email)) {
                    $person['email'] = substr($person_s->email, strpos($person_s->email, '@'));
                  }
                  elseif (ValidationHelper::isEmail($company->email)) {
                    $person['email'] = substr($company->email, strpos($company->email, '@'));
                  }
                }

                $route->persons[] = $person;
              }

              //sandboxuser gekoppeld aan bestelling
              $person_o = SandboxUsers::find_by(["userId" => $route->userId]);
              if ($person_o) {
                $found_main_user = false;
                foreach ($route->persons as $k => $p) {
                  if ($p['personid'] != "" && $p['personid'] == $person_o->personId) {
                    $route->persons[$k]['type'] = 'contact';
                    $found_main_user = true;
                    break;
                  }
                }
                if (!$found_main_user) {
                  $person = [];
                  $person['id'] = $person_o->userId;
                  $person['personid'] = ($person_o->personId == "" ? 0 : $person_o->personId);
                  $person['userid'] = $person_o->userId;
                  $person['name'] = $person_o->firstName . ' ' . $person_o->lastName;
                  $person['phone'] = $person_o->phone;
                  $person['mobile'] = $person_o->mobile;
                  $person['email'] = $person_o->email;
                  $person['type'] = 'contact';
                  $route->persons[] = $person;
                }
              }

              if ($route->executorPersonId != "") {
                $person_o = CrmPersons::find_by(["personId" => $route->executorPersonId]);

                if ($person_o) {
                  $found_executor = false;
                  foreach ($route->persons as $k => $p) {
                    if ($p['personid'] == $person_o->personId) {
                      $route->persons[$k]['type'] = 'executor';
                      $route->persons[$k]['defaultexecutor'] = true;
                      $found_executor = true;
                      break;
                    }
                  }
                  if (!$found_executor) {
                    $person = [];
                    $person['id'] = ($person_o->personId + 1000000);
                    $person['personid'] = $person_o->personId;
                    $person['userid'] = 0;
                    $person['name'] = $person_o->firstName . ' ' . $person_o->lastName;
                    $person['phone'] = $person_o->phone;
                    $person['mobile'] = $person_o->mobile;
                    $person['email'] = $person_o->email;
                    $person['type'] = 'executor';
                    $person['defaultexecutor'] = true;
                    $route->persons[] = $person;
                  }
                }


              }

            }
            elseif ($route->mapExtraPoi != 1) { //particulier

              $person_o = SandboxUsers::find_by(["userId" => $route->userId]);

              $person = [];
              $person['id'] = $person_o->userId;
              $person['personid'] = ($person_o->personId == "" ? 0 : $person_o->personId);
              $person['userid'] = $person_o->userId;
              $person['name'] = $person_o->firstName . ' ' . $person_o->lastName;
              $person['phone'] = $person_o->phone;
              $person['mobile'] = $person_o->mobile;
              $person['email'] = $person_o->email;
              $person['type'] = 'contact';
              $route->persons[] = $person;
            }

            usort($route->persons, "apiRdeActions::sortPersons");

          }

        }

        foreach ($routes as $route) {
          $route->lowestQuotationStatusId = null;
          $route->lowestQuotationStatusName = null;
          foreach ($route->quotations as $quotation) {
            if ($route->lowestQuotationStatusId == null || $quotation->statusId < $route->lowestQuotationStatusId) {
              $route->lowestQuotationStatusId = $quotation->statusId;
              $route->lowestQuotationStatusName = $quotationStati[$quotation->statusId];
            }
          }

        }

      }

      if ($return) {
        return $routes;
      }
      RestUtils::sendResponseOK("ROUTE LIST SUCCESFULLY RETRIEVED", $routes);

    }

    /**
     * For GoogleMaps Udo
     */
    public function executeGetRouteItemsSoon() {

      $routes = [];
      $trucks = GpsbuddyTrucks::getActiveTrucks();
      $starttime = strtotime("-1 DAY");
      $endtime = strtotime("+2 DAYS");
      if (date('w', $endtime) == 4 || date('w', $endtime) == 5 || date('w', $endtime) == 6 || date('w', $endtime) == 0) { //zonddag=0, weekend niet meetellen
        $endtime = strtotime("+4 DAYS");
      }
      else {
        $endtime = strtotime("+2 DAYS");
      }
//      $starttime = strtotime("-5 DAY");
//      $endtime = strtotime("+8 DAYS");

      for ($time = $starttime; $time <= $endtime; $time = strtotime("+1 DAYS", $time)) {
        foreach ($trucks as $truck) {
          $val = [];
          $val["truckid"] = $truck->truckId;
          $val["date"] = date("Y-m-d", $time);
          $val["route"] = $this->executeGetRouteItems($time * 1000, $truck->truckId, true);
          if (count($val["route"]) > 0) {
            $routes[] = $val;
          }
        }
        //        break;
      }

      RestUtils::sendResponseOK("ROUTE LIST SUCCESFULLY RETRIEVED", $routes);

    }


    /**
     * Save routes
     * Status naar 53 -> 55
     * contrainers ophalen: deliverDate zetten + naar W->N
     * containers ophalen: returnDate zetten + naar N->Y
     * Handtekening + afbeeldingen uploaden
     * Mailtje naar emailadres signee + cc bart + naar quotation user_id (C personen, ontdubbelen)
     *   vrachtbon 1 pdf
     *   productiestaat 1 pdf
     *
     * @param stdClass[] $request_vars
     */
    public function executeSendRoutes() {
      $request_vars = $this->data->getData();

      $go = true;

      logToFile("sendroutes", print_r($_FILES, true));
      //      logToFile("sendroutes", print_r($request_vars, true));
      $json = json_decode($request_vars["json"]);
      $json = $json[0];
      logToFile("sendroutes", print_r($json, true));

      $base_path = DIR_ROOT_HTTPDOCS . "filesystem/raamdorpel/clients/signatures/";
      $success = true;

      $container_quotations = [];  //alle containers met hun quotations.
      foreach ($json as $transportticket) {

        $route = GpsbuddyRoutes::find_by(["routeId"=>$transportticket->routeId]);
        if(!$route) {
          logToFile("sendroutes", "ROUTE NIET GEVONDEN: " . $transportticket->routeId);
          continue;
        }

        $route->companyId = 0;

        $query1 = "SELECT gpsbuddy_rde.quotationId, quotations.statusId, street, nr, ext, zipcode, domestic, country, userId, companyId, executor, executorMobile, executorMail FROM " . GpsbuddyRde::getTablename() . " ";
        $query1 .= "JOIN " . Quotations::getTablename() . " ON quotations.quotationId=gpsbuddy_rde.quotationId ";
        $query1 .= "WHERE routeId=" . $route->routeId . " ";
        $query1 .= "ORDER BY quotations.quotationNumber ";
        $oResult1 = DBConn::db_link()->query($query1);

        $quotations = [];
        $quotationIds_53 = [];
        while ($l_quotation = $oResult1->fetch_object()) {
          $query2 = "SELECT * FROM " . ContainersQuotations::getTablename() . " WHERE quotationId = '" . $l_quotation->quotationId . "' GROUP BY containerId";
          $oResult2 = DBConn::db_link()->query($query2);
          if ($l_quotation->statusId == Status::STATUS_LOADED) {
            $quotationIds_53[$l_quotation->quotationId] = $l_quotation->quotationId;
          }
          while ($container_quotation = $oResult2->fetch_object()) {

            $query3 = "SELECT containers.* FROM " . Containers::getTablename() . " ";
            $query3 .= "WHERE containers.containerId = '" . $container_quotation->containerId . "' ";
            $oResult3 = DBConn::db_link()->query($query3);
            $container = $oResult3->fetch_object();
            $container_quotation->container = $container;

            $quotations[$l_quotation->quotationId][] = $container_quotation;

            $container_quotations[$container->containerId][$l_quotation->quotationId] = $l_quotation->quotationId;
          }

          $route->companyId = $l_quotation->companyId;
          $route->userId = $l_quotation->userId;
        }


        $containers_retour = [];
        $hasDelivered = false;
        $hasPickedup = false;
        foreach ($transportticket->containers_scanned as $scanned) {
          $container_found = false;
          foreach ($quotations as $quotation) {
            foreach ($quotation as $container_quotation) {
              if ($scanned->containerId == $container_quotation->containerId) {
                $container_found = true;
                if ($scanned->action == 'deliver') { //bezorg actie. Altijd deliverdate zetten. Soms is deze al gezet bij containerRetour
                  if ($go) {
                    $cq = ContainersQuotations::find_by(["containerQuotationId" => $container_quotation->containerQuotationId]);
                    $cq->deliverDate = date("Y-m-d", strtotime($transportticket->deliverdate));
                    $cq->deliverUserId = gsdApiSession::getUserId();
                    $cq->save();
                  }

                  //W naar N
                  if ($go) {
                    $c = Containers::find_by(['containerId' => $container_quotation->containerId]);
                    if ($c->inStock == 'W') {
                      $c->inStock = 'N';
                      $c->save();
                    }
                  }
                  $hasDelivered = true;
                }
                elseif ($scanned->action == 'pickup') { //pickup actie
                  if ($container_quotation->returnDate == "") {

                    if ($go) {
                      $cq = ContainersQuotations::find_by(["containerQuotationId" => $container_quotation->containerQuotationId]);
                      $cq->returnDate = date("Y-m-d", strtotime($transportticket->deliverdate));
                      $cq->returnUserId = gsdApiSession::getUserId();
                      $cq->save();
                    }

                    //N naar Y
                    if ($go) {
                      $c = Containers::find_by(['containerId' => $container_quotation->containerId]);
                      if ($c->inStock == 'N') {
                        $c->inStock = 'Y';
                        $c->save();
                      }
                    }
                  }
                  $containers_retour[$container_quotation->containerId] = $container_quotation->container->containerNumber;
                  $hasPickedup = true;
                }
                break;
              }
            }
          }

          if (!$container_found && $scanned->action == 'pickup') { //handmatige toegevoegde container opslaan
            //handmatige toevoegde heeft een dummy $scanned->containerId. Deze eerst ophalen.

            $container_manual = Containers::find_by(['containerNumber' => $scanned->containerNumber]);

            if ($container_manual) {
              //container status updaten
              if ($go) {
                $container_manual->inStock = 'Y';
                $container_manual->save();
              }

              //deze container returnDate zetten
              if ($go) {
                foreach (ContainersQuotations::find_all_by(["containerId" => $container_manual->containerId], "AND returnDate IS NULL AND NOT deliverDate IS NULL AND deliverDate<='" . date("Y-m-d", strtotime($transportticket->deliverdate)) . "' ") as $cq) {
                  $cq->returnUserId = gsdApiSession::getUserId();
                  $cq->returnDate = date("Y-m-d", strtotime($transportticket->deliverdate));
                  $cq->save();
                }
              }

              $containers_retour[$container_manual->containerId] = $scanned->containerNumber;

              $hasPickedup = true;
              logToFile("sendroutes", "manual " . $scanned->containerNumber);

            }
            else {
              logToFile("sendroutes", "Onbekende container nummer, overgeslagen. containerNumber=" . $scanned->containerNumber);
            }
          }
        }

        //update stati 53 naar 55
        if (count($quotationIds_53) > 0) {
          $query = "UPDATE " . Quotations::getTablename() . " ";
          $query .= "SET statusId=" . Status::STATUS_DELIVERED . " ";
          $query .= "WHERE statusId=" . Status::STATUS_LOADED . " AND quotationId IN (" . implode(",", $quotationIds_53) . ") ";
          //logToFile("mysql", $query);

          if ($go) {
            DBConn::db_link()->query($query);
            QuotationStatus::registerBatch($quotationIds_53, Status::STATUS_DELIVERED, gsdApiSession::getUserId());

            //send sms to users
            $this->sendSMSByQuotationIds($quotationIds_53);

          }

          if ($transportticket->driverRemarks != "null" && trim($transportticket->driverRemarks) != "") {
            //opslaan opmerkingen chauffeur
            $query = "UPDATE " . QuotationsExtra::getTablename() . " QE ";
            $query .= "SET QE.quoteInvoiceAlertFlag=1, QE.quoteInvoiceAlertInfo='" . escapeForDB($transportticket->driverRemarks) . "' ";
            $query .= "WHERE quotationId IN (" . implode(",", $quotationIds_53) . ") ";
            //logToFile("mysql", $query);
            if ($go)
              DBConn::db_link()->query($query);
          }
          if ($transportticket->custRemarks != "null" && trim($transportticket->custRemarks) != "") {
            //opslaan opmerkingen klant bij alle facturen met tekst in dispatchAppointment
            $query = "UPDATE " . Quotations::getTablename() . " Q ";
            $query .= "SET Q.dispatchAppointment='" . escapeForDB($transportticket->custRemarks) . "' ";
            $query .= "WHERE Q.quotationId IN (" . implode(",", $quotationIds_53) . ") AND Q.dispatchAppointment!='' ";
            //logToFile("mysql", $query);
            if ($go)
              DBConn::db_link()->query($query);
          }

        }

        //er is geen cargo receipt. aanmaken.
        if (empty($route->cargoReceiptId)) {
          $this->executeSetPacked(strtotime($route->date) * 1000, $route->truckId, $transportticket->routeId . '_', true);
          logToFile("sendroutes", "Vrachtbon aangemaakt: " . $route->routeId);
          $route_o = GpsbuddyRoutes::find_by(["routeId" => $route->routeId]);
          $route->cargoReceiptId = $route_o->cargoReceiptId;
        }

        //bakken retour op vrachtbon zetten
        $cargo_receipt = CargoReceipt::find_by(["cargoReceiptId" => $route->cargoReceiptId]);
        //schijnbaar is er altijd een cargoreceipt..anders zou hij crashen

        $containers_retour_nrs = [];
        $containers_near_nrs = [];
        if ($cargo_receipt->staticNumberRetour != "") {
          $bakken = explode("\n", $cargo_receipt->staticNumberRetour);
          if (count($bakken) > 0) {
            $containers_retour_nrs = $bakken;
          }
        }
        if ($cargo_receipt->staticNumberNear != "") {
          $bakken = explode("\n", $cargo_receipt->staticNumberNear);
          if (count($bakken) > 0) {
            $containers_near_nrs = $bakken;
          }
        }

        foreach ($containers_retour as $crnr) {
          if (!in_array($crnr, $containers_retour_nrs)) {
            $containers_retour_nrs[] = $crnr;
          }
        }

        $containers_retour_nrs = array_unique($containers_retour_nrs);

        if (count($containers_retour_nrs) > 0) {
          sort($containers_retour_nrs);
          $containers_retour_str = implode("\n", $containers_retour_nrs);

          $added_near = false;
          foreach ($containers_retour_nrs as $retour_nr) {
            if (!in_array($retour_nr, $containers_near_nrs)) {
              $containers_near_nrs[] = $retour_nr;
              $added_near = true;
            }
          }

          //zet deze container op de cargo_receipt erbij
          $cargo_receipt->staticNumberRetour = $containers_retour_str;
          if ($added_near) {
            $containers_near_nrs = array_unique($containers_near_nrs);
            sort($containers_near_nrs);
            $containers_near_str = implode("\n", $containers_near_nrs);
            $cargo_receipt->staticNumberNear = $containers_near_str;
          }
          if ($go)
            $cargo_receipt->save();

        }


        if ($transportticket->driverSignee != 1) { //er is getekend

          if (isset($_FILES['signatures'])) {
            $filename = $transportticket->routeId . ".png";
            move_uploaded_file($_FILES['signatures']['tmp_name'][$transportticket->routeId], $base_path . $filename);
          }

          $query = "UPDATE " . CargoReceipt::getTablename() . " ";
          $query .= "SET ";
          $query .= "signatureConsigneeName='" . escapeForDB($transportticket->receiverName) . "', ";
          $query .= "signatureConsigneeFilename='" . $filename . "', ";
          $query .= "signatureConsigneePlace='" . escapeForDB($route->domestic) . "', ";
          $query .= "signatureConsigneeDateTime='" . date('Y-m-d H:i:s', strtotime($transportticket->signedDate)) . "', ";
          $query .= "signatureConsigneeMail='" . escapeForDB($transportticket->receiverEmail) . "', ";
          $query .= "specialAgreements='" . escapeForDB($transportticket->driverRemarks) . "' ";
          $query .= "WHERE cargoReceiptId=" . $route->cargoReceiptId;
          //logToFile("mysql", $query);
          if ($go)
            DBConn::db_link()->query($query);
        }
        else { //vrachtwagenchauffeur tekent
          $query = "UPDATE " . CargoReceipt::getTablename() . " ";
          $query .= "SET ";
          $query .= "signatureConsigneePlace='" . escapeForDB($route->domestic) . "', ";
          $query .= "signatureConsigneeDateTime='" . date('Y-m-d H:i:s', strtotime($transportticket->signedDate)) . "', ";
          $query .= "specialAgreements='" . escapeForDB($transportticket->driverRemarks) . "', ";
          if ($transportticket->noSignee == 1) { //er was niemand aanwezig
            $query .= "signatureConsigneeFilename='niemandaanwezig.png', ";
          }
          $query .= "signatureSenderFilename='ludo.png' ";
          $query .= "WHERE cargoReceiptId=" . $route->cargoReceiptId;
          //logToFile("mysql", $query);
          if ($go)
            DBConn::db_link()->query($query);

        }

        $route->persons = [];

        if ($route->companyId != 0) {

          //alle personen gekoppeld bij dit bedrijf
          $query3 = "SELECT * FROM " . CrmPersons::getTablename() . " WHERE flagForDeletion=0 AND companyId = " . $route->companyId;
          $oResult3 = DBConn::db_link()->query($query3);
          while ($person_o = $oResult3->fetch_object()) {
            $person = [];
            $person['id'] = ($person_o->personId + 1000000);
            $person['personid'] = $person_o->personId;
            $person['userid'] = 0;
            $person['name'] = $person_o->firstName . ' ' . $person_o->lastName;
            $person['phone'] = $person_o->phone;
            $person['mobile'] = $person_o->mobile;
            $person['email'] = $person_o->email;
            $person['type'] = 'other';
            $route->persons[] = $person;
          }

          //sandbox user gekoppeld aan deze bestelling
          $person_o = SandboxUsers::find_by(["userId" => $route->userId]);
          if ($person_o) {
            $found_main_user = false;
            foreach ($route->persons as $k => $p) {
              if ($p['personid'] != "" && $p['personid'] == $person_o->personId) {
                $route->persons[$k]['type'] = 'contact';
                $found_main_user = true;
                break;
              }
            }
            if (!$found_main_user) {
              $person = [];
              $person['id'] = $person_o->userId;
              $person['personid'] = ($person_o->personId == "" ? 0 : $person_o->personId);
              $person['userid'] = $person_o->userId;
              $person['name'] = $person_o->firstName . ' ' . $person_o->lastName;
              $person['phone'] = $person_o->phone;
              $person['mobile'] = $person_o->mobile;
              $person['email'] = $person_o->email;
              $person['type'] = 'contact';
              $route->persons[] = $person;
            }
          }

          if (isset($route->executorPersonId) && $route->executorPersonId != "") {
            $person_o = CrmPersons::find_by(["personId" => $route->executorPersonId]);

            if ($person_o) {
              $found_executor = false;
              foreach ($route->persons as $k => $p) {
                if ($p) {
                  $route->persons[$k]['type'] = 'executor';
                  $found_executor = true;
                  break;
                }
              }
              if (!$found_executor) {
                $person = [];
                $person['id'] = ($person_o->personId + 1000000);
                $person['personid'] = $person_o->personId;
                $person['userid'] = 0;
                $person['name'] = $person_o->firstName . ' ' . $person_o->lastName;
                $person['phone'] = $person_o->phone;
                $person['mobile'] = $person_o->mobile;
                $person['email'] = $person_o->email;
                $person['type'] = 'executor';
                $route->persons[] = $person;
              }
            }


          }

        }
        else { //nog geen bedrijf gekoppeld
          $person_o = SandboxUsers::find_by(["userId" => $route->userId]);

          $person = [];
          $person['id'] = $person_o->userId;
          $person['personid'] = ($person_o->personId == "" ? 0 : $person_o->personId);
          $person['userid'] = $person_o->userId;
          $person['name'] = $person_o->firstName . ' ' . $person_o->lastName;
          $person['phone'] = $person_o->phone;
          $person['mobile'] = $person_o->mobile;
          $person['email'] = $person_o->email;
          $person['type'] = 'contact';
          $route->persons[] = $person;
        }

        $tos = [];
        if (isset($transportticket->receiverEmail) && ValidationHelper::isEmail($transportticket->receiverEmail)) {
          $tos[$transportticket->receiverEmail] = $transportticket->receiverEmail;
        }

        //        logToFile("sendroutes", print_r($route,true));

        //mail gaat naar contactpersonen en persoon welke heeft getekend
        $signeefound = false;
        foreach ($route->persons as $person) {
          if ($person["type"] == "contact" && ValidationHelper::isEmail($person["email"])) {
            $tos[$person["email"]] = $person["email"];
          }
          if ($transportticket->driverSignee != 1) { //klant heeft getekend
            if ($transportticket->receiverPersonId == $person["personid"] && $person["personid"] != 0) { //crm_person heeft getekend

              $crm_person = CrmPersons::find_by(["personId" => $person["personid"]]);
              if ($crm_person) {
                $crm_person->email = $transportticket->receiverEmail;
                $crm_person->mobile = $transportticket->receiverPhone;
                if ($go) $crm_person->save();
              }

              $signeefound = true;
            }
            if ($transportticket->receiverUserId == $person["userid"] && $person["userid"] != 0) { //sandboxuser heeft getekend

              $sbu = SandboxUsers::find_by(["userId" => $person["userid"]]);
              if ($sbu) {
                $sbu->email = $transportticket->receiverEmail;
                $sbu->mobile = $transportticket->receiverPhone;
                if ($go) $sbu->save();
              }
              $signeefound = true;
            }
          }
        }


        if (!$signeefound && $transportticket->driverSignee != 1 && $route->companyId != 0 && $transportticket->receiverPersonId == -2) {
          //Nieuwe uitvoerder ingevoerd. Voeg deze toe aan crm_persons met flagForExecutor=1
          //Een nieuwe contactpersoon word alleen in de cargoreceipt opgeslagen.
          $names = explode(" ", $transportticket->receiverName);
          $firstName = trim($names[0]);
          unset($names[0]);
          $lastName = '';
          if (count($names) > 0) {
            $lastName = implode(" ", $names);
          }

          $crm_person = new CrmPersons();
          $crm_person->companyId = $route->companyId;
          $crm_person->firstName = $firstName;
          $crm_person->lastName = $lastName;
          $crm_person->mobile = $transportticket->receiverPhone;
          $crm_person->email = $transportticket->receiverEmail;
          $crm_person->flagForExecutor = 1;
          if ($go) $crm_person->save();

        }


        MailsFactory::sendContainerretourEmail($containers_retour);

        if ($hasDelivered) { //er zijn producten bezorgd.
          MailsFactory::sendDeliveredEmail($tos, $route->cargoReceiptId, $quotationIds_53);
        }
        if (!$hasDelivered && $hasPickedup) { //er zijn containers opgehaald. Alleen verzenden als er niks is bezorgd.
          MailsFactory::sendPickupcontainersEmail($tos, $route->cargoReceiptId, $quotationIds_53);
        }

      }

      //alle verzonden afbeeldingen opslaan
      $base_path_cont = DIR_ROOT_HTTPDOCS . "filesystem/raamdorpel/clients/container/";

      if (isset($_FILES['containerImage'])) {
        foreach ($_FILES['containerImage']['name'] as $containerId => $items) {
          foreach ($items as $nr => $filename) {

            $tmp_filename = $_FILES['containerImage']['tmp_name'][$containerId][$nr];

            $cont_check = Containers::find_by(['containerId' => $containerId]);
            if (!$cont_check) {//als deze container niet gevonden word dan overslaan!
              logToFile("sendroutes", "Onbekende container, overgeslagen. containerid=" . $containerId);
              continue;
            }

            if (!file_exists($base_path_cont . $containerId . '/')) { //map aanmaken
              mkdir($base_path_cont . $containerId . '/');
            }

            $containerFilePath = $base_path_cont . $containerId . '/' . $filename;

            if (!file_exists($containerFilePath)) {
              move_uploaded_file($tmp_filename, $containerFilePath);

              ImageHelper::compress($containerFilePath); //verklein afbeelding

              $remark = '';
              if (isset($request_vars["containerImageRemark"][$containerId][$nr])) {
                $remark = $request_vars["containerImageRemark"][$containerId][$nr];
              }

              if (isset($container_quotations[$containerId])) {
                foreach ($container_quotations[$containerId] as $quotationId) {

                  $cim = new ContainerImg();
                  $cim->containerId = $containerId;
                  $cim->quotationId = $quotationId;
                  $cim->filename = $filename;
                  $cim->statusPhotoId = 2;
                  $cim->remark = $remark;
                  if ($go) $cim->save();

                }
              }
              else { //geen quotation, dan maar zonder....

                $cim = new ContainerImg();
                $cim->containerId = $containerId;
                $cim->filename = $filename;
                $cim->statusPhotoId = 2;
                $cim->remark = $remark;
                if ($go) $cim->save();
              }

              logToFile("sendroutes", "Image succesvol gekoppeld");
            }
          }
        }
      }

      if ($success) {
        RestUtils::sendResponseOK("Routes succesvol verzonden");
      }
      else {
        RestUtils::sendResponseError("Er is een fout opgetreden tijdens het verzenden van de routes");
      }
    }


    /**
     * For GoogleMaps Udo
     */
    public function executeGetTrucks() {

      $trucks = GpsbuddyTrucks::getActiveTrucks();

      RestUtils::sendResponseOK("TRUCKS RETRIEVED", AppModel::plainObjects($trucks));

    }

    public function executeGetcontainerspickup() {
      $addresses = $this->getContainerspickup();
      RestUtils::sendResponseOK("TRUCKS RETRIEVED", AppModel::plainObjects(array_values($addresses), ["companyname", "containers", "nextroute"]));
    }

    /**
     * @param $crmaddress_id : only show of 1 address
     * @return array
     */
    private function getContainerspickup($crmaddress_id = false) {

      $query = "SELECT crm_addresses.*,containers.*,containers_quotations.nextroute FROM " . ContainersQuotations::getTablename() . " ";
      $query .= "JOIN " . Containers::getTablename() . " ON containers_quotations.containerId=containers.containerId ";
      $query .= "JOIN " . QuotationsExtra::getTablename() . " ON quotations_extra.quotationId=containers_quotations.quotationId ";
      $query .= "JOIN " . CrmAddresses::getTablename() . " ON crm_addresses.addressId=quotations_extra.addressDeliveryId ";
      $query .= "WHERE returnDate IS NULL AND NOT deliverDate IS NULL ";
//      $query .= "AND nextroute='true' ";
      if ($crmaddress_id !== false) {
        $query .= "AND crm_addresses.addressId=" . $crmaddress_id . " ";
      }
      $query .= "GROUP BY containers.containerId";

      $result = DBConn::db_link()->query($query);

      $addresses = [];
      $companyIds = [];
      while ($row = $result->fetch_row()) {
        $address = new CrmAddresses();
        $address->hydrate($row);
        $address->containers = [];
        if (isset($addresses[$address->addressId])) { //lat/lng vergelijken
          $address = $addresses[$address->addressId];
        }

        $container = new Containers();
        $container->hydrate($row, count(CrmAddresses::columns));

        $container->nextroute = $row[count(CrmAddresses::columns) + count(Containers::columns)];

        $address->containers[] = $container;
        $addresses[$address->addressId] = $address;

        $companyIds[$address->companyId] = $address->companyId;

      }

      $companies = AppModel::mapObjectIds(CrmCompanies::find_all_by(["companyId" => $companyIds]), "companyId");
      foreach ($addresses as $address) {
        if (isset($companies[$address->companyId])) {
          $address->companyname = $companies[$address->companyId]->name;
        }
      }

      return $addresses;

    }

    /**
     * Add crmadress to route, to be picked up
     */
    public function executeAddresstoroute() {
      $request_vars = $this->data->getData();
      $addressId = $request_vars['addressId'];
      $truckId = $request_vars['truckId'];
      $date = $request_vars['date'];

      $address = CrmAddresses::find_by(["addressId" => $addressId]);
      if (!$address) {
        RestUtils::sendResponseError("Adres niet gevonden.");
      }
      if ($address->latitude == "" || $address->latitude == 0) {
        RestUtils::sendResponseError("Latitude en longitude niet gezet. Niet mogelijk om toe te voegen.");
      }
      if ($date == "") {
        RestUtils::sendResponseError("Datum niet gezet.");
      }
      $truck = GpsbuddyTrucks::find_by(["truckId" => $truckId]);
      if (!$truck) {
        RestUtils::sendResponseError("Truck niet gevonden.");
      }
      $company = CrmCompanies::find_by(["companyId" => $address->companyId]);
      if (!$company) {
        RestUtils::sendResponseError("Bedrijf niet gevonden.");
      }
      $addresses = $this->getContainerspickup($address->addressId);
      if (count($addresses) == 0) {
        RestUtils::sendResponseError("Adres niet gevonden (2)");
      }

      $containers = AppModel::mapObjectIds(array_values($addresses)[0]->containers, "containerId");
      $containerIds = [];
      foreach ($containers as $container) {
        $containerIds[] = $container->containerId;
      }
      $container_quotations = ContainersQuotations::find_all_by(["containerId" => $containerIds, "nextroute" => "true"], "AND returnDate IS NULL AND NOT deliverDate IS NULL");

      //zoek naar geleverde quotations, en voeg daarna deze container toe.
      $routes_on_day = GpsbuddyRoutes::find_all_by(["date" => $date, "truckId" => $truckId], "ORDER BY truckId ASC, rank ASC, routeId ASC");
      $rank = 1;
      foreach ($routes_on_day as $rd) {
        $quoationIds = [];
        foreach (GpsbuddyRde::find_all_by(["routeId" => $rd->routeId]) as $rde) {
          $quoationIds[] = $rde->quotationId;
        }
        foreach (Quotations::find_all_by(["quotationId" => $quoationIds]) as $quotation) {
          if ($quotation->statusId != Status::STATUS_DELIVERED) {
            break;
          }
        }
        $rank += 10;
      }


      $route = new GpsbuddyRoutes();
      $route->bid = $company->companyId;
      $route->truckId = $truck->truckId;
      $route->date = $date;
      $route->rank = $rank;
      $route->subject = $company->name;
      $route->city = $address->zipcode;
      $route->latitude = $address->latitude;
      $route->longitude = $address->longitude;
      $route->domestic = $address->domestic;
      //cargo receipt word niet gezet, deze word automatisch aangemaakt bij het verwerken.
      //$route->cargoReceiptId = $cargoreceipt->id;
      $route->save();

      foreach ($container_quotations as $cq) {
        $container = $containers[$cq->containerId];
        $gpsrde = new GpsbuddyRde();
        $gpsrde->bakId = $container->containerNumber;
        $gpsrde->quotationId = $cq->quotationId;
        $gpsrde->routeId = $route->routeId;
        $gpsrde->save();
      }

      RestUtils::sendResponseOK("Addresstoroute success");

    }

    public function executeSendSMS() {
      $request_vars = $this->data->getData();
      $duur = $request_vars["duur"];
      $routeId = $request_vars["routeId"];

      $route = GpsbuddyRoutes::find_by(["routeId" => $routeId]);
      if (!$route) {
        RestUtils::sendResponseError("Route niet gevonden.");
      }

      $sendSMSUsersToTheck = [];

      $query1 = "SELECT gpsbuddy_rde.quotationId, userId ";
      $query1 .= "FROM " . GpsbuddyRde::getTablename() . " ";
      $query1 .= "JOIN " . Quotations::getTablename() . " ON quotations.quotationId=gpsbuddy_rde.quotationId ";
      $query1 .= "WHERE gpsbuddy_rde.routeId=" . $route->routeId . " ";
      $oResult1 = DBConn::db_link()->query($query1);

      $quotationIds = [];
      while ($l_quotation = $oResult1->fetch_object()) {
        $quotationIds[$l_quotation->quotationId] = $l_quotation->quotationId;
        $sendSMSUsersToTheck[$l_quotation->userId] = $l_quotation->userId;
      }

      foreach (QuotationsExtra::find_all_by(["quotationId" => $quotationIds, "sms" => 1], "AND NOT smsnumber IS NULL") as $quotationsExtra) {
        $nr = CleanPhonenumber::clean($quotationsExtra->smsnumber);
        $recievingNrs[$nr] = $nr;
      }
      foreach (SandboxUsers::find_all_by(["userId" => $sendSMSUsersToTheck, "sms" => 1]) as $sandUser) {
        if (!ValidationHelper::isMobile($sandUser->mobile)) continue;
        $nr = CleanPhonenumber::clean($sandUser->mobile);
        $recievingNrs[$nr] = $nr;
      }

      if (count($recievingNrs) == 0) {
        RestUtils::sendResponseOK("SMS niet verzonden. Er zijn geen klanten die een SMS willen ontvangen, of met een geldig mobiel nummer.");
      }

      $message = "We verwachten binnen " . $duur . " uw producten te bezorgen. Mvg Raamdorpelelementen B.V.";


      if (DEVELOPMENT) {
        $recievingNrs = [];
        $recievingNrs[] = "+31654650888";
      }

      if (false) { //SEND VIA WHATSAPP OR SMS

        $succesful = array_flip($recievingNrs);
        foreach ($recievingNrs as $nr) {
          try {
            $whatsappApi = new WhatsappApi();
            $template_params = [
              "NAAM",
              "NR",
              "ORDER",
              "Bladel",
            ];
            $conversation = $whatsappApi->send($nr, "support", $template_params);
            //$message = $whatsappApi->sendMessageInConversation($conversation, "Test vanuit gsdfw 2");
            $succesful[$nr] = true;
          }
          catch (GsdException $e) {
            if (DEVELOPMENT) throw $e;
          }
        }

        foreach ($recievingNrs as $nr) {
          if ($succesful[$nr] !== true) {
            //whatsapp bericht niet gelukt, dan maar een SMS
            $succesful[$nr] = SmsApi::send($nr, $message);
          }
        }

        $countValid = 0;
        foreach ($succesful as $nr => $success) {
          if ($success === true) {
            $countValid++;
          }
        }

        if (count($succesful) == $countValid) {
          RestUtils::sendResponseOK("SMS/Whatsapp verzonden");
        }
        else {
          RestUtils::sendResponseOK($countValid . " van de " . count($succesful) . " SMS/Whatsapp succesvol verzonden");
        }


      }
      else {
        $result = SmsApi::send($recievingNrs, $message);
        if ($result !== true) {
          RestUtils::sendResponseError("SMS niet verzonden - " . $result);
        }
        RestUtils::sendResponseOK("SMS verzonden");
      }


    }


    private function sendSMSByQuotationIds($quotationIds) {

      $quotations = Quotations::find_all_by(["quotationId" => $quotationIds]);

      $telnrs = [];
      foreach ($quotations as $quotation) {
        //alle users emailen met vinkje sms_delivered aan
        $user = SandboxUsers::find_by(["userId" => $quotation->userId, "sms_delivered" => 1], "AND NOT phone IS NULL");
        if ($user && ValidationHelper::isMobile($user->mobile)) {
          $nr = CleanPhonenumber::clean($user->mobile);
          $telnrs[$nr] = $nr;
        }

        $quotationsExtra = QuotationsExtra::find_by(["quotationId" => $quotation->quotationId, "sms_delivered" => 1], "AND NOT smsnumber IS NULL");
        if ($quotationsExtra) {
          $nr = CleanPhonenumber::clean($quotationsExtra->smsnumber);
          $telnrs[$nr] = $nr;
        }

      }


      $message = "Uw bestelling is zojuist geleverd. Mvg Raamdorpelelementen B.V.";


      if (DEVELOPMENT) {
        $telnrs = [];
        $telnrs["+31654650888"] = "+31654650888";
      }

      if (false) { //SEND VIA WHATSAPP OR SMS

        $succesful = array_flip($telnrs);
        foreach ($telnrs as $nr) {
          try {
            $whatsappApi = new WhatsappApi();
            $template_params = [
              "NAAM",
              "NR",
              "ORDER",
              "Bladel",
            ];
            $conversation = $whatsappApi->send($nr, "support", $template_params);
            //$message = $whatsappApi->sendMessageInConversation($conversation, "Test vanuit gsdfw 2");
            $succesful[$nr] = true;
          }
          catch (GsdException $e) {
            if (DEVELOPMENT) throw $e;
          }
        }

        foreach ($telnrs as $nr) {
          if ($succesful[$nr] !== true) {
            //whatsapp bericht niet gelukt, dan maar een SMS
            $succesful[$nr] = SmsApi::send($nr, $message);
          }
        }

        $countValid = 0;
        foreach ($succesful as $nr => $success) {
          if ($success === true) {
            $countValid++;
          }
        }

        if (count($succesful) == $countValid) {
          return true;
        }
        return $countValid . " van de " . count($succesful) . " SMS/Whatsapp succesvol verzonden";
      }


      $result = SmsApi::send($telnrs, $message);
      if ($result !== true) {
        return "SMS niet verzonden - " . $result;
      }
      return true;


    }


  }