<template>
  <div id="map-container">
    <div id="map" class="google-maps" />
  </div>
</template>

<script setup>
import { ref, watch } from 'vue'
import axios from "axios";
import { useGoogleMaps } from "../composables/googleMaps";
import { getStatusColor } from '../helpers/ColorHelper'

const props = defineProps({
  companies: {
    type: Array,
    required: true
  },
  quotationsByComp: {
    type: Object,
    required: true
  },
  statuses: {
    type: Object,
    required: true
  }
})

const emit = defineEmits(['selectedCompanyChange'])

const { map, loader } = useGoogleMaps('map');

const markers = ref([])

const companiesWithAddress = ref([])
const errors = ref([])

// Methods
const getCompanyAddresses = async () => {
  try {
    const { data } = await axios.post('?action=getcompanieswithaddress', {
      companyIds: props.companies
    })
    companiesWithAddress.value = data
    await initRouteMarkers()
  } catch (error) {
    errors.value = [`Server fout opgetreden. Probeer het later nog eens, of neem contact op (${error})`]
  }
}

const initRouteMarkers = async () => {
  if (!companiesWithAddress.value.length) return

  clearMarkers()

  const { InfoWindow } = await loader.value.importLibrary("maps")
  const { AdvancedMarkerElement, PinElement } = await loader.value.importLibrary("marker")
  const { LatLng } = await loader.value.importLibrary("core")

  const infoWindow = new InfoWindow()

  companiesWithAddress.value.forEach((route_delivery) => {
    // Get the first quotation's status for this company to determine color
    const companyQuotations = props.quotationsByComp[route_delivery.companyId] || []
    const firstQuotation = companyQuotations[0]
    const statusId = firstQuotation?.statusId
    const statusColor = getStatusColor(statusId)

    const pin = new PinElement({
      background: statusColor,
    })

    const info_window_text = `
      <div class="marker-window">
        <p>${route_delivery.address.domestic}<br>
        <b>${route_delivery.name}</b><br>
        ${route_delivery.address.street} ${route_delivery.address.nr} ${route_delivery.address.extension}<br>
        ${route_delivery.address.zipcode} ${route_delivery.address.domestic} ${route_delivery.address.country}<br></p>
        <p>Opmerking: ${route_delivery.notes}</p>
        <p><a target="_blank" href="http://www.google.com/maps/place/${route_delivery.address.latitude},${route_delivery.address.longitude}">Bekijk in Maps</a></p>
      </div>
    `

    const marker = new AdvancedMarkerElement({
      position: new LatLng(route_delivery.address.latitude, route_delivery.address.longitude),
      map: map.value,
      title: `${route_delivery.name}`,
      content: pin.element,
    })

    markers.value.push(marker);

    marker.addListener("gmp-click", () => {
      infoWindow.close()
      infoWindow.setContent(info_window_text)
      infoWindow.open(marker.map, marker)
      emit('selectedCompanyChange', route_delivery.companyId)
    })
  })
}

const clearMarkers = () => {
  // Clear existing markers from the map
  markers.value.forEach(marker => {
    marker.map = null;
  });
  markers.value = []; // Clear the array
}

const createMapDirections = async () => {
  if (companiesWithAddress.value.length < 2) return

  const { DirectionsService, DirectionsRenderer, LatLng } = await loader.value.importLibrary("routes")
  const { TravelMode } = await loader.value.importLibrary("maps")

  const directionsService = new DirectionsService()
  const directionsRenderer = new DirectionsRenderer({ suppressMarkers: true })
  directionsRenderer.setMap(map.value)

  const directions_json = {
    origin: new LatLng(
      companiesWithAddress.value[0].address.latitude,
      companiesWithAddress.value[0].address.longitude
    ),
    destination: new LatLng(
      companiesWithAddress.value[companiesWithAddress.value.length - 1].address.latitude,
      companiesWithAddress.value[companiesWithAddress.value.length - 1].address.longitude
    ),
    waypoints: companiesWithAddress.value.slice(1, -1).map(company => ({
      location: new LatLng(company.address.latitude, company.address.longitude)
    })),
    travelMode: TravelMode.DRIVING
  }

  try {
    const result = await directionsService.route(directions_json)
    directionsRenderer.setDirections(result)
  } catch (e) {
    console.error("Could not display directions due to:", e)
  }
}

watch(() => props.companies, async () => {
  if (props.companies.length) {
    await getCompanyAddresses()
  } else {
    clearMarkers()
  }
}, { immediate: true })
</script>

<style>
#map-container {
  height: 100%;
  width: 100%;
}

.google-maps {
  width: 100%;
  height: 100%;
}

.marker-window {
  padding: 8px;
  max-width: 300px;
}
</style>
