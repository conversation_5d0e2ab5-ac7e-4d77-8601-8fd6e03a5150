<?php

  AppModel::loadBaseClass('BaseCrmBankaccounts');

  class CrmBankaccountsModel extends BaseCrmBankaccounts {

    /**
     * @return array: with company id's
     */
    public static function getAllIncassoCompanyIds() {
      $companies = [];
      foreach (self::find_all_by(["debit" => 1]) as $bank) {
        $companies[$bank->companyId] = $bank->companyId;
      }
      return $companies;
    }


    /**
     * @param $companyId
     * @return CrmBankaccounts[]
     */
    public static function getByCompanyId($companyId) {
      return self::find_all_by(["companyId" => $companyId]);
    }

    /**
     * Company has incasso
     * @param $companyId
     * @return bool
     */
    public static function hasIncasso($companyId): bool {
      foreach (self::getByCompanyId($companyId) as $bank) {
        if ($bank->debit == 1) return true;
      }
      return false;
    }


  }