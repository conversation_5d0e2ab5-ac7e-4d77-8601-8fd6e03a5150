<?php

  namespace domain\quotations\service;

  use GsdException;
  use StoneBrands;
  use Stones;

  class GetBrandIdsByStone {

    private string $stone_type;
    private string $stone_material;

    public function __construct(string $stone_type, string $stone_material) {
      $this->stone_type = $stone_type;
      $this->stone_material = $stone_material;
    }


    /**
     * Get brandIds for certain stone
     * @return int[]
     * @throws GsdException
     */
    public function getBrandids(): array {

      if ($this->stone_material == Stones::MATERIAL_KERAMIEK) {
        return [1, 2];
      }
      if ($this->stone_material == Stones::MATERIAL_BETON) {
        return [3];
      }
      if ($this->stone_material == Stones::MATERIAL_NATUURSTEEN && ($this->stone_type == Stones::TYPE_RAAMDORPEL || $this->stone_type == Stones::TYPE_MUURAFDEKKER || $this->stone_type == Stones::TYPE_BALKJES)) {
        return [4, 5, 6, 7, 12, 15];
      }
      if ($this->stone_material == Stones::MATERIAL_NATUURSTEEN && $this->stone_type == Stones::TYPE_VENSTERBANK) {
        return StoneBrands::getBrandidsWithVensterbanken();
      }
      if ($this->stone_material == Stones::MATERIAL_NATUURSTEEN && $this->stone_type == Stones::TYPE_SPEKBAND) {
        return [6, 7];
      }
      if ($this->stone_material == Stones::MATERIAL_ISOSILL) {
        return [14];
      }

      throw new GsdException("Onbekend type/materiaal! (2-" . $this->stone_type . "-" . $this->stone_material . ")");
    }

  }