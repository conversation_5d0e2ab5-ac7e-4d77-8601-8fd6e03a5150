<?php

  AppModel::loadBaseClass('BaseImessageRead');

  class ImessageReadModel extends BaseImessageRead {

    /**
     * Get read messages ids by userid
     * @param $userId
     * @return array
     */
    public static function getReadMessages($userId) {
      $read = [];
      foreach (ImessageRead::find_all_by(['insertUser' => $userId]) as $imr) {
        $read[$imr->imessage_id] = $imr->imessage_id;
      }
      return $read;
    }


    public function save(&$errors = []) {
      if ($this->from_db == false) {
        $this->insertTS = date('Y-m-d H:i:s');
      }
      return parent::save($errors);
    }


  }