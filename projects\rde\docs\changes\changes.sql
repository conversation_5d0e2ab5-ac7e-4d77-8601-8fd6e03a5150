CREATE TABLE rde_b1mo.quotation_img (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `quotationId` int(11) DEFAULT NULL,
  `filename` varchar(255) NOT NULL,
  `remark` text,
  `insertDate` datetime NOT NULL,
  `statusPhotoId` int(11) NOT NULL,
  PRIMARY KEY (id),
  KEY quotationId (quotationId)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


CREATE TABLE rde_b1mo.cargo_receipt_quotation (
  `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT,
  `cargoReceiptId` int(11) NOT NULL,
  `quotationId` int(11) NOT NULL,
  PRIMARY KEY (id),
  KEY cargoReceiptId (cargoReceiptId),
  KEY quotationId (quotationId)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


-- 29-06-2017 - ROBERT - changes registeren
CREATE TABLE rde_b1mo.quotation_status (
  `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT,
  `quotationId` int(11) UNSIGNED NOT NULL,
  `statusId` int(2) UNSIGNED NOT NULL,
  `insertUserId` int(3) UNSIGNED NOT NULL,
  `insertTS` datetime NOT NULL,
  PRIMARY KEY (id),
  KEY quotationId (quotationId),
  KEY statusId (statusId),
  KEY insertUserId (insertUserId),
  KEY insertTS (insertTS)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

ALTER TABLE rde_b1mo.containers_quotations ADD `deliverUserId` INT(3) UNSIGNED NULL DEFAULT NULL AFTER `deliverDate`;
ALTER TABLE rde_b1mo.containers_quotations ADD `returnUserId` INT(3) UNSIGNED NULL DEFAULT NULL AFTER `returnDate`;
ALTER TABLE `production_employees` ADD `app8` TINYINT(1) NOT NULL DEFAULT '0' AFTER `app7`;

ALTER TABLE `containers` CHANGE `containerWeight` `containerWeight` INT(5) NOT NULL DEFAULT '0';
ALTER TABLE `containers` CHANGE `action` `action` TINYINT(1) NOT NULL DEFAULT '0';
ALTER TABLE `containers` CHANGE `actionRemark` `actionRemark` TEXT CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL;
ALTER TABLE `containers` CHANGE `actionRemarkExtra` `actionRemarkExtra` TEXT CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL;


ALTER TABLE `production_employees` ADD `passwordnr` MEDIUMINT NULL AFTER `password`, ADD INDEX (`passwordnr`);

-- 29-06-2017 - ROBERT - vgm

CREATE TABLE rde_b1mo.vgm (
  `id` mediumint(8) UNSIGNED NOT NULL AUTO_INCREMENT,
  `employee_id` mediumint(8) UNSIGNED NOT NULL,
  `customer_user_id` int(11) UNSIGNED NOT NULL,
  `quotation_id` int(11) UNSIGNED NOT NULL,
  `partner` varchar(255) DEFAULT NULL,
  `date` DATETIME NOT NULL,
  `employees` text DEFAULT NULL,
  `location` varchar(255) DEFAULT NULL,
  `job` varchar(255) DEFAULT NULL,
  `improvement` BOOLEAN NOT NULL DEFAULT TRUE,
  PRIMARY KEY (`id`),
  KEY `employee_id` (`employee_id`),
  KEY `customer_user_id` (`customer_user_id`),
  KEY `quotation_id` (`quotation_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE rde_b1mo.vgm_check (
  `id` mediumint(8) UNSIGNED NOT NULL AUTO_INCREMENT,
  `vgm_id` mediumint(8) UNSIGNED NOT NULL,
  `code` tinyint(2),
  `check` tinyint(1) NOT NULL DEFAULT 0,
  `remark` text,
  PRIMARY KEY (`id`),
  KEY `vgm_id` (`vgm_id`),
  CONSTRAINT `fk_vgm_checks` FOREIGN KEY (`vgm_id`) REFERENCES `vgm` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

ALTER TABLE `vgm` ADD `customer_name` VARCHAR(150) NULL AFTER `customer_user_id`;

-- 29-06-2017 - ROBERT - rechten
ALTER TABLE `production_employees` ADD `app9` BOOLEAN NOT NULL DEFAULT FALSE AFTER `app8`;
ALTER TABLE `production_employees` ADD `app10` BOOLEAN NOT NULL DEFAULT FALSE AFTER `app9`;
ALTER TABLE `production_employees` ADD `app11` BOOLEAN NOT NULL DEFAULT FALSE AFTER `app10`;


CREATE TABLE rde_b1mo.montage (
  `id` mediumint(8) UNSIGNED NOT NULL AUTO_INCREMENT,
  `employee_id` mediumint(8) UNSIGNED NOT NULL,
  `company_id` int(11) UNSIGNED NOT NULL,
  `type` varchar(30) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL ,
  `name` varchar(255) NOT NULL,
  `plandate` DATE DEFAULT NULL,
  `remark` text,
  `done` BOOLEAN NOT NULL DEFAULT FALSE,
  `slopen` BOOLEAN NOT NULL DEFAULT FALSE,
  `verstekken` BOOLEAN NOT NULL DEFAULT FALSE,
  `clips` BOOLEAN NOT NULL DEFAULT FALSE,
  `specie` BOOLEAN NOT NULL DEFAULT FALSE,
  `water` BOOLEAN NOT NULL DEFAULT FALSE,
  `uitkrabben` BOOLEAN NOT NULL DEFAULT FALSE,
  `voegen` BOOLEAN NOT NULL DEFAULT FALSE,
  `pointeren` BOOLEAN NOT NULL DEFAULT FALSE,
  `steigers` BOOLEAN NOT NULL DEFAULT FALSE,
  `reistijd` float UNSIGNED NOT NULL DEFAULT 0,
  PRIMARY KEY (`id`),
  KEY `employee_id` (`employee_id`),
  KEY `company_id` (`company_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE rde_b1mo.montage_quotation (
  `id` mediumint(8) UNSIGNED NOT NULL AUTO_INCREMENT,
  `montage_id` mediumint(8) UNSIGNED NOT NULL,
  `quotation_id` int(11) DEFAULT NULL,
  `done` BOOLEAN NOT NULL DEFAULT FALSE,
  `specie` int(6) UNSIGNED NOT NULL DEFAULT 0,
  `specie_dikte` int(6) UNSIGNED NOT NULL DEFAULT 0,
  `meters` int(6) UNSIGNED NOT NULL DEFAULT 0,
  `arbeidsduur` float UNSIGNED NOT NULL DEFAULT 0,
  `bereikbaar_achter` TINYINT UNSIGNED DEFAULT NULL,
  `bereikbaar_voor` TINYINT UNSIGNED DEFAULT NULL,
  `slopen` TINYINT UNSIGNED DEFAULT NULL,
  `voegen` TINYINT UNSIGNED DEFAULT NULL,
  `remark` text,
  PRIMARY KEY (`id`),
  KEY `montage_id` (`montage_id`),
  KEY `quotation_id` (`quotation_id`),
  CONSTRAINT `fk_montage_id` FOREIGN KEY (`montage_id`) REFERENCES `montage` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE rde_b1mo.montage_quotation_empl (
  `id` mediumint(8) UNSIGNED NOT NULL AUTO_INCREMENT,
  `montage_quotation_id` mediumint(8) UNSIGNED NOT NULL,
  `employee_id` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `montage_quotation_id` (`montage_quotation_id`),
  KEY `employee_id` (`employee_id`),
  CONSTRAINT `fk_montage_quotation_id` FOREIGN KEY (`montage_quotation_id`) REFERENCES `montage_quotation` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

ALTER TABLE `montage` ADD `company_address_id` INT NULL AFTER `company_id`;

ALTER TABLE `montage` ADD `executor_montage` VARCHAR(150) NULL AFTER `remark`;
ALTER TABLE `montage` ADD `executor_montage_mobile` VARCHAR(20) NULL AFTER `executor_montage`;

CREATE TABLE rde_api.worker_planhour (
  `id` mediumint(8) UNSIGNED NOT NULL AUTO_INCREMENT,
  `worker_id` mediumint(8) UNSIGNED NOT NULL,
  `date` DATE NOT NULL,
  `hours` mediumint(8) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `worker_id` (`worker_id`),
  KEY `date` (`date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
ALTER TABLE `worker_planhour` ADD FOREIGN KEY (`worker_id`) REFERENCES `worker`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE `worker_planhour` CHANGE `hours` `hours` DECIMAL(8,2) NOT NULL;

-- 14-12-2017 - ROBERT - prikklok app
ALTER TABLE `production_employees` ADD `app12` TINYINT(1) NOT NULL DEFAULT '0' AFTER `app11`;
ALTER TABLE `production_employees` ADD `app13` TINYINT(1) NOT NULL DEFAULT '0' AFTER `app12`;
ALTER TABLE `production_employees` ADD `app14` TINYINT(1) NOT NULL DEFAULT '0' AFTER `app13`;

-- 14-12-2017 - ROBERT - voorraad
CREATE TABLE rde_b1mo.stone_order (
  `id` mediumint(8) UNSIGNED NOT NULL AUTO_INCREMENT,
  `brand_id` TINYINT(2) UNSIGNED NOT NULL,
  `status` varchar(30) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  `senddate` DATETIME NOT NULL,
  `emailtext` TEXT NULL,
  PRIMARY KEY (`id`),
  KEY `brand_id` (`brand_id`),
  KEY `status` (`status`),
  KEY `senddate` (`senddate`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE rde_b1mo.stone_order_item (
  `id` mediumint(8) UNSIGNED NOT NULL AUTO_INCREMENT,
  `stone_order_id` mediumint(8) UNSIGNED NOT NULL,
  `stone_id` int(4) UNSIGNED NOT NULL,
  `size` int(4) NOT NULL,
  `senddate` DATETIME NULL,
  `supplierreadydate` DATETIME NULL,
  `receiveddate` DATETIME NULL,
  PRIMARY KEY (`id`),
  KEY `stone_id` (`stone_id`),
  KEY `stone_order_id` (`stone_order_id`),
  KEY `senddate` (`senddate`),
  KEY `supplierreadydate` (`supplierreadydate`),
  KEY `receiveddate` (`receiveddate`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

ALTER TABLE rde_b1mo.stones ADD `stock` MEDIUMINT NOT NULL DEFAULT '0' AFTER `sizeId`;

ALTER TABLE `stones`
  CHANGE `details` `details` LONGTEXT CHARACTER SET utf8
COLLATE utf8_unicode_ci NULL,
  CHANGE `alert` `alert` TEXT CHARACTER SET utf8
COLLATE utf8_unicode_ci NULL,
  CHANGE `image` `image` VARCHAR(255) CHARACTER SET utf8
COLLATE utf8_unicode_ci NULL;
ALTER TABLE `stones`
  CHANGE `minAmountPallet` `minAmountPallet` INT(11) NULL,
  CHANGE `minAmountStones` `minAmountStones` INT(11) NOT NULL DEFAULT '0',
  CHANGE `amountPerPallet` `amountPerPallet` INT(11) NULL,
  CHANGE `plantext` `plantext` VARCHAR(8) CHARACTER SET utf8
COLLATE utf8_unicode_ci NULL;

-- 04-01-2018 - ROBERT - voorraad

ALTER TABLE `stone_order` CHANGE `senddate` `senddate` DATETIME NULL;
ALTER TABLE `stone_order_item` ADD FOREIGN KEY (`stone_order_id`) REFERENCES `stone_order`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE `stone_order` CHANGE `status` `status` VARCHAR(30) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL DEFAULT 'new';
ALTER TABLE `stone_order_item` ADD `receivedsize` INT(4) NOT NULL DEFAULT '0' AFTER `receiveddate`;

CREATE TABLE rde_b1mo.stone_order_item_quotation (
  `id` mediumint(8) UNSIGNED NOT NULL AUTO_INCREMENT,
  `stone_order_item_id` mediumint(8) UNSIGNED NOT NULL,
  `quoation_id` int(10) UNSIGNED NOT NULL,
  PRIMARY KEY (`id`),
  KEY `stone_order_item_id` (`stone_order_item_id`),
  KEY `quotation_id` (`quoation_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
ALTER TABLE `stone_order_item_quotation` ADD FOREIGN KEY (`stone_order_item_id`) REFERENCES `stone_order_item`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE `stone_brands` ADD `orderemail` VARCHAR(255) NULL AFTER `default`;


ALTER TABLE `stone_order_item` ADD `name` TEXT NULL AFTER `stone_id`;

-- 08-02-2018 - ROBERT - voorraad
ALTER TABLE `stone_order` ADD `receiveddate` DATETIME NULL AFTER `senddate`;

-- 15-02-2018 - ROBERT - voorraad stenen prijs mail
ALTER TABLE `stone_colors` ADD `discount` DECIMAL(6,2) NOT NULL DEFAULT '0' AFTER `common`;
UPDATE `stone_colors` SET discount = 9 WHERE brandId=1;
UPDATE `stone_colors` SET discount = 12.5 WHERE brandId=2;

-- 15-02-2018 - ROBERT - nieuwe apps
ALTER TABLE `production_employees` ADD `app15` BOOLEAN NOT NULL DEFAULT FALSE COMMENT 'Ophalen met vrachtbon' AFTER `app14`;
ALTER TABLE `production_employees` CHANGE `LockApp` `LockApp` TINYINT(1) NOT NULL DEFAULT '0' COMMENT 'Voorraad verlies';

CREATE TABLE IF NOT EXISTS `message` (
  `id` mediumint(8) UNSIGNED NOT NULL AUTO_INCREMENT,
  `type` varchar(25) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
  `user_id` mediumint(8) UNSIGNED NOT NULL,
  `code` varchar(45) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  `object_id` varchar(100) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  `message` text,
  `url` varchar(255) DEFAULT NULL,
  `url_title` varchar(255) DEFAULT NULL,
  `insertTS` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `fk_message_user_id_idx` (`user_id`),
  KEY `i_message_type` (`type`),
  KEY `i_message_code` (`code`),
  KEY `i_message_insertTS` (`insertTS`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

ALTER TABLE `message`
  ADD CONSTRAINT `fk_message_user_id` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- 05-04-2018 - ROBERT - stenen verlies registeren
CREATE TABLE rde_b1mo.stone_loss (
  `id` mediumint(8) UNSIGNED NOT NULL AUTO_INCREMENT,
  `stone_id` int(4) UNSIGNED NOT NULL,
  `stockloss` mediumint(8) UNSIGNED NOT NULL,
  `insertTS` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `stone_id` (`stone_id`),
  KEY `insertTS` (`insertTS`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

ALTER TABLE `sandbox_users` CHANGE `oldUserId` `oldUserId` INT(11) NOT NULL DEFAULT '0';
ALTER TABLE `sandbox_users` CHANGE `name` `name` VARCHAR(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL;
ALTER TABLE `sandbox_users` CHANGE `fax` `fax` VARCHAR(15) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL;
ALTER TABLE `sandbox_users` CHANGE `mobile` `mobile` VARCHAR(15) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL;
ALTER TABLE `sandbox_users` CHANGE `supplier` `supplier` VARCHAR(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL;
UPDATE `sandbox_users` SET companyId = NULL WHERE companyId=0;
UPDATE `sandbox_users` SET personId = NULL WHERE personId=0;


-- 05-04-2018 - ROBERT - offerte
ALTER TABLE `quotations_extra` CHANGE `quotationAltPriceYear` `quotationAltPriceYear` DATE NULL DEFAULT NULL;

ALTER TABLE `order_elements` CHANGE `leftEndstone` `leftEndstone` BOOLEAN NOT NULL DEFAULT FALSE;
ALTER TABLE `order_elements` CHANGE `leftEndstoneGrooves` `leftEndstoneGrooves` BOOLEAN NOT NULL DEFAULT FALSE;
ALTER TABLE `order_elements` CHANGE `rightEndstone` `rightEndstone` BOOLEAN NOT NULL DEFAULT FALSE;
ALTER TABLE `order_elements` CHANGE `rightEndstoneGrooves` `rightEndstoneGrooves` BOOLEAN NOT NULL DEFAULT FALSE;
ALTER TABLE `order_elements` CHANGE `heartClickSize` `heartClickSize` BOOLEAN NOT NULL DEFAULT FALSE;

-- 05-04-2018 - ROBERT - offerte changes
ALTER TABLE `quotations_extra` CHANGE `callToDelivery` `callToDelivery` TINYINT(1) NOT NULL DEFAULT '0';
ALTER TABLE `quotations_extra` CHANGE `executorTicket` `executorTicket` BOOLEAN NOT NULL DEFAULT FALSE;
ALTER TABLE `quotations_extra` CHANGE `rackContainerReturn` `rackContainerReturn` TINYINT(1) NOT NULL DEFAULT '0' COMMENT 'bak-rek retour nemen';
ALTER TABLE `quotations` CHANGE `invoiceNumber` `invoiceNumber` VARCHAR(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT 'factuurNummer';
ALTER TABLE `quotations` CHANGE `invoiceDate` `invoiceDate` DATE NULL DEFAULT NULL COMMENT 'factuurDatum';
UPDATE `quotations` set invoiceDate = NULL WHERE invoiceDate='0000-00-00';
ALTER TABLE `quotations` CHANGE `projectReference` `projectReference` VARCHAR(20) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL;
ALTER TABLE `quotations` CHANGE `largeworklistinfo` `largeworklistinfo` TEXT CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL;
ALTER TABLE `quotations` CHANGE `productionNotes` `productionNotes` TEXT CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL;
ALTER TABLE `quotations` CHANGE `specialFreightCost` `specialFreightCost` TINYINT(1) NOT NULL DEFAULT '0' COMMENT 'Afgesproken vrachtkosten';
ALTER TABLE `quotations` CHANGE `specialFreightCostPrice` `specialFreightCostPrice` DECIMAL(8,2) NOT NULL DEFAULT '0.00' COMMENT 'Afgesproken vrachtkosten';
ALTER TABLE `quotations` CHANGE `mountingFlag` `mountingFlag` TINYINT(1) NOT NULL DEFAULT '0';
ALTER TABLE `quotations` CHANGE `mountingPrice` `mountingPrice` DECIMAL(8,2) NOT NULL DEFAULT '0.00' COMMENT 'Montage Raamdorpelelementen';
ALTER TABLE `quotations` CHANGE `dispatchAppointment` `dispatchAppointment` TEXT CHARACTER SET utf8 COLLATE utf8_general_ci NULL;
ALTER TABLE `quotations` CHANGE `cashPayment` `cashPayment` TINYINT(1) NOT NULL DEFAULT '0';
ALTER TABLE `quotations` CHANGE `cashPaymentPrice` `cashPaymentPrice` DECIMAL(8,2) NOT NULL DEFAULT '0.00';
ALTER TABLE `quotations` CHANGE `toNumberQuotations` `toNumberQuotations` TINYINT(1) NOT NULL DEFAULT '0';
ALTER TABLE `quotations` CHANGE `sendMailToClient` `sendMailToClient` TINYINT(1) NOT NULL DEFAULT '0';
ALTER TABLE `quotations` CHANGE `callOrEmailNotes` `callOrEmailNotes` TEXT CHARACTER SET utf8 COLLATE utf8_general_ci NULL;
ALTER TABLE `quotations` CHANGE `noRackQuotations` `noRackQuotations` TINYINT(1) NOT NULL DEFAULT '0' COMMENT 'in een bak';
ALTER TABLE `quotations` CHANGE `noContainerQuotations` `noContainerQuotations` TINYINT(1) NOT NULL DEFAULT '0' COMMENT 'in een rek';
ALTER TABLE `quotations` CHANGE `noRackNoContainerQuotations` `noRackNoContainerQuotations` TINYINT(1) NOT NULL DEFAULT '0' COMMENT 'afhaal rek';
ALTER TABLE `quotations` CHANGE `palletQuotations` `palletQuotations` TINYINT(1) NOT NULL DEFAULT '0' COMMENT 'op een pallet';
ALTER TABLE `quotations` CHANGE `afhalenQuotations` `afhalenQuotations` TINYINT(1) NOT NULL DEFAULT '0';
ALTER TABLE `quotations` CHANGE `urgencyFlag` `urgencyFlag` TINYINT(1) NOT NULL DEFAULT '0';
ALTER TABLE `quotations` CHANGE `calledFlag` `calledFlag` TINYINT(1) NOT NULL DEFAULT '0';
ALTER TABLE `quotations` CHANGE `executor` `executor` VARCHAR(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT 'uitvoerder aflever_contact';
ALTER TABLE `quotations` CHANGE `executorMobile` `executorMobile` VARCHAR(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT 'uitvoerder mobiel';
ALTER TABLE `quotations` CHANGE `executorMail` `executorMail` VARCHAR(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL;
ALTER TABLE `quotations` CHANGE `customColor` `customColor` VARCHAR(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL;
ALTER TABLE `quotations` CHANGE `customSize` `customSize` VARCHAR(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL;
ALTER TABLE `quotations` CHANGE `mattingOnlyGlueFlag` `mattingOnlyGlueFlag` TINYINT(1) NOT NULL DEFAULT '0';
ALTER TABLE `quotations` CHANGE `mattingRemovalDiscountFlag` `mattingRemovalDiscountFlag` TINYINT(1) NOT NULL DEFAULT '0';
ALTER TABLE `quotations` CHANGE `offerteType` `offerteType` VARCHAR(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL;
ALTER TABLE `quotations` CHANGE `billingFeature` `billingFeature` VARCHAR(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT 'factuurkenmerk';
ALTER TABLE `quotations` CHANGE `separatePackage` `separatePackage` VARCHAR(2) CHARACTER SET utf8 COLLATE utf8_general_ci NULL;
ALTER TABLE `quotations` CHANGE `quotationNumber` `quotationNumber` VARCHAR(9) CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT 'offerteNummer';
ALTER TABLE `quotations` CHANGE `quotationVersion` `quotationVersion` TINYINT(2) UNSIGNED NOT NULL DEFAULT '0' COMMENT 'volgnummer';
ALTER TABLE `quotations` CHANGE `metersMuch` `metersMuch` ENUM('false','true') CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT 'false';
ALTER TABLE `quotations` CHANGE `mattingRemovalDiscount` `mattingRemovalDiscount` VARCHAR(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT 'bouwmatAfhaalKorting';
ALTER TABLE `quotations` CHANGE `mattingOnlyGlue` `mattingOnlyGlue` FLOAT(8,2) NULL DEFAULT '0.00' COMMENT 'bouwmatAlleenLijmen';
ALTER TABLE `quotations_extra` CHANGE `rackContainerMark` `rackContainerMark` VARCHAR(10) CHARACTER SET utf8 COLLATE utf8_general_ci NULL;
ALTER TABLE `quotations_extra` CHANGE `stoneOrdered` `stoneOrdered` TINYINT(1) NOT NULL DEFAULT '0';
ALTER TABLE `quotations_extra` CHANGE `stoneOrderedDate` `stoneOrderedDate` DATE NULL DEFAULT NULL;

UPDATE `quotations_extra` set stoneOrderedDate = NULL WHERE stoneOrderedDate='0000-00-00';
ALTER TABLE `quotations_extra` CHANGE `stoneDeliveryDate` `stoneDeliveryDate` DATE NULL DEFAULT NULL;
UPDATE `quotations_extra` set stoneDeliveryDate = NULL WHERE stoneDeliveryDate='0000-00-00';
ALTER TABLE `quotations_extra` CHANGE `carrierCompany` `carrierCompany` VARCHAR(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL;
ALTER TABLE `quotations_extra` CHANGE `addressDeliveryId` `addressDeliveryId` INT(11) NULL DEFAULT NULL;
ALTER TABLE `quotations_extra` CHANGE `supplier` `supplier` VARCHAR(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL;
ALTER TABLE `quotations_extra` CHANGE `addressSplit` `addressSplit` VARCHAR(10) CHARACTER SET utf8 COLLATE utf8_general_ci NULL;
ALTER TABLE `quotations_extra` CHANGE `addressSplitInfo` `addressSplitInfo` VARCHAR(70) CHARACTER SET utf8 COLLATE utf8_general_ci NULL;
ALTER TABLE `quotations_extra` CHANGE `carrierId` `carrierId` INT(11) NOT NULL DEFAULT '0';
ALTER TABLE `quotations_extra` CHANGE `quoteInvoiceAlertFlag` `quoteInvoiceAlertFlag` TINYINT(1) NOT NULL DEFAULT '0';
ALTER TABLE `quotations_extra` CHANGE `quoteInvoiceAlertInfo` `quoteInvoiceAlertInfo` VARCHAR(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL;

-- ROBERT - offerte systeem

ALTER TABLE `sandbox_users` CHANGE `gender` `gender` ENUM('female','male') CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL;
ALTER TABLE `sandbox_users` CHANGE `created` `created` DATETIME NULL DEFAULT NULL;
UPDATE sandbox_users SET created=NULL WHERE created='0000-00-00 00:00:00';
UPDATE sandbox_users SET lastLogin=NULL WHERE lastLogin='0000-00-00 00:00:00';
UPDATE sandbox_users SET gender=NULL WHERE gender='';
ALTER TABLE `sandbox_users` CHANGE `street` `street` VARCHAR(150) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL;
ALTER TABLE `crm_addresses` CHANGE `extraInfo` `extraInfo` TEXT CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL;
ALTER TABLE `crm_addresses` CHANGE `title` `title` VARCHAR(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL;
ALTER TABLE `crm_addresses` CHANGE `mapExtra` `mapExtra` TINYINT(1) NULL DEFAULT NULL;
ALTER TABLE `crm_addresses` CHANGE `longitude` `longitude` FLOAT(17,15) NULL DEFAULT NULL;
ALTER TABLE `crm_addresses` CHANGE `latitude` `latitude` FLOAT(17,15) NULL DEFAULT NULL;
UPDATE crm_addresses SET longitude=NULL WHERE longitude = 0;
UPDATE crm_addresses SET latitude=NULL WHERE latitude = 0;

ALTER TABLE `production_order`
  CHANGE `custom` `custom` TEXT CHARACTER SET utf8
COLLATE utf8_general_ci NULL,
  CHANGE `customColor` `customColor` VARCHAR(255) CHARACTER SET utf8
COLLATE utf8_general_ci NULL,
  CHANGE `customSize` `customSize` VARCHAR(255) CHARACTER SET utf8
COLLATE utf8_general_ci NULL;

ALTER TABLE `stone_order_item` CHANGE `size` `size` INT(8) NOT NULL;


-- ROBERT - 13-09-2018 - berichten

CREATE TABLE IF NOT EXISTS `imessage` (
  `id` mediumint(8) UNSIGNED NOT NULL AUTO_INCREMENT,
  `user_id` mediumint(8) UNSIGNED DEFAULT NULL,
  `company_id` mediumint(8) UNSIGNED DEFAULT NULL,
  `subject` varchar(255) NOT NULL,
  `message` TEXT NOT NULL,
  `date` DATE DEFAULT NULL,
  `construction` BOOLEAN NOT NULL DEFAULT FALSE,
  `constructiontraders` BOOLEAN NOT NULL DEFAULT FALSE,
  `private` BOOLEAN NOT NULL DEFAULT FALSE,
  `possible_readers` INT(6) DEFAULT 0,
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `company_id` (`company_id`),
  KEY `subject` (`subject`),
  KEY `date` (`date`),
  KEY `construction` (`construction`),
  KEY `constructiontraders` (`contstuctiontraders`),
  KEY `private` (`private`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE IF NOT EXISTS `imessage_read` (
  `id` mediumint(8) UNSIGNED NOT NULL AUTO_INCREMENT,
  `imessage_id` mediumint(8) UNSIGNED NOT NULL,
  `insertUser` mediumint(8) UNSIGNED NOT NULL,
  `insertTS` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `imessage_id` (`imessage_id`),
  KEY `insertUser` (`insertUser`),
  KEY `insertTS` (`insertTS`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
ALTER TABLE `imessage` ADD `online` BOOLEAN NOT NULL DEFAULT TRUE AFTER `possible_readers`;
ALTER TABLE `imessage` ADD INDEX(`online`);
ALTER TABLE `imessage` ADD `homepage` BOOLEAN NOT NULL DEFAULT FALSE AFTER `online`;

ALTER TABLE `order_elementparts` CHANGE `aAmount1` `aAmount1` SMALLINT(4) UNSIGNED NOT NULL DEFAULT '1';
ALTER TABLE `order_elementparts` CHANGE `aLength1` `aLength1` DECIMAL(8,2) NULL DEFAULT NULL;
ALTER TABLE `order_elementparts` CHANGE `aLength2` `aLength2` DECIMAL(8,2) NULL DEFAULT NULL;
ALTER TABLE `order_elementparts` CHANGE `bAmount1` `bAmount1` SMALLINT(4) UNSIGNED NOT NULL DEFAULT '1';
ALTER TABLE `order_elementparts` CHANGE `bLength1` `bLength1` DECIMAL(8,2) NULL DEFAULT NULL;
ALTER TABLE `order_elementparts` CHANGE `bLength2` `bLength2` DECIMAL(8,2) NULL DEFAULT NULL;
ALTER TABLE `order_elementparts` CHANGE `cAmount1` `cAmount1` SMALLINT(4) UNSIGNED NOT NULL DEFAULT '1';
ALTER TABLE `order_elementparts` CHANGE `cLength1` `cLength1` DECIMAL(8,2) NULL DEFAULT NULL;
ALTER TABLE `order_elementparts` CHANGE `cLength2` `cLength2` DECIMAL(8,2) NULL DEFAULT NULL;
ALTER TABLE `order_elementparts` CHANGE `dAmount1` `dAmount1` SMALLINT(4) UNSIGNED NOT NULL DEFAULT '1';
ALTER TABLE `order_elementparts` CHANGE `dLength1` `dLength1` DECIMAL(8,2) NULL DEFAULT NULL;
ALTER TABLE `order_elementparts` CHANGE `dLength2` `dLength2` DECIMAL(8,2) NULL DEFAULT NULL;

UPDATE `order_elementparts` SET aLength1=NULL WHERE aLength1=0;
UPDATE `order_elementparts` SET aLength2=NULL WHERE aLength2=0;
UPDATE `order_elementparts` SET bLength1=NULL WHERE bLength1=0;
UPDATE `order_elementparts` SET bLength2=NULL WHERE bLength2=0;
UPDATE `order_elementparts` SET cLength1=NULL WHERE cLength1=0;
UPDATE `order_elementparts` SET cLength2=NULL WHERE cLength2=0;
UPDATE `order_elementparts` SET dLength1=NULL WHERE dLength1=0;
UPDATE `order_elementparts` SET dLength2=NULL WHERE dLength2=0;

ALTER TABLE `stone_order_item` CHANGE `receivedsize` `receivedsize` INT(8) NOT NULL DEFAULT '0';


ALTER TABLE `projects` ADD `size` MEDIUMINT(8) NOT NULL DEFAULT '0' AFTER `quotationId`;
ALTER TABLE `projects` ADD `product_id` MEDIUMINT(8) UNSIGNED DEFAULT NULL AFTER `size`;
ALTER TABLE `projects` CHANGE `invoiceId` `invoiceId` INT(11) NULL DEFAULT NULL;
UPDATE projects SET invoiceId = NULL WHERE invoiceId=0;
ALTER TABLE `projects` CHANGE `quotationId` `quotationId` INT(11) NULL DEFAULT NULL;
UPDATE projects SET quotationId = NULL WHERE quotationId=0;
ALTER TABLE `projects` ADD INDEX(`invoiceId`);
ALTER TABLE `projects` ADD INDEX(`quotationId`);
ALTER TABLE `projects` ADD INDEX(`orderNr`);
ALTER TABLE `projects` ADD INDEX(`product_id`);

ALTER TABLE `projects` ADD `pieceprice` DECIMAL(8,2) NOT NULL DEFAULT '0' AFTER `size`;
ALTER TABLE `projects` CHANGE `size` `size` MEDIUMINT(8) NOT NULL DEFAULT '1';

-- ROBERT - 06-12-2018 - notificaties
ALTER TABLE `sandbox_users` ADD `noti_delivery_mail` BOOLEAN NOT NULL DEFAULT TRUE AFTER `private`;
ALTER TABLE `sandbox_users` ADD `noti_order_mail` BOOLEAN NOT NULL DEFAULT TRUE AFTER `noti_delivery_mail`;
ALTER TABLE `sandbox_users` ADD `noti_confirm_mail` BOOLEAN NOT NULL DEFAULT TRUE AFTER `noti_order_mail`;

ALTER TABLE `order_elements`
  CHANGE `amount` `amount` SMALLINT(5) UNSIGNED NOT NULL,
  CHANGE `stoneAmount` `stoneAmount` SMALLINT(5) UNSIGNED NOT NULL,
  CHANGE `inputLength` `inputLength` MEDIUMINT(7) UNSIGNED NOT NULL
COMMENT 'kozijn',
  CHANGE `elementLength` `elementLength` MEDIUMINT(7) UNSIGNED NOT NULL;

ALTER TABLE `quotations` CHANGE `zipcode` `zipcode` VARCHAR(10) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'aflever_postcode';

ALTER TABLE `quotations` ADD `paymentMethod` VARCHAR(20) NOT NULL DEFAULT 'overmaken' COMMENT 'overmaken / mollie' AFTER `dispatchAppointment`;
ALTER TABLE `quotations` CHANGE `brandId` `brandId` SMALLINT(2) UNSIGNED  NOT NULL DEFAULT '0';
ALTER TABLE `quotations` CHANGE `stoneId` `stoneId` INT(4) UNSIGNED NOT NULL DEFAULT '0';
ALTER TABLE `quotations` CHANGE `projectType` `projectType` ENUM('newbuilt','renovation') CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'Type werk / renovatie';
ALTER TABLE `quotations` CHANGE `shorter` `shorter` TINYINT(2) UNSIGNED NULL DEFAULT NULL COMMENT 'speling';
ALTER TABLE `quotations` CHANGE `projectName` `projectName` VARCHAR(30) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'project_naam / werk';

ALTER TABLE `quotations_extra` CHANGE `totalLeftEndStones` `totalLeftEndStones` INT(11) NOT NULL DEFAULT '0';
ALTER TABLE `quotations_extra` CHANGE `totalRightEndStones` `totalRightEndStones` INT(11) NOT NULL DEFAULT '0';
ALTER TABLE `quotations_extra` CHANGE `totalLeftEndStonesGrooves` `totalLeftEndStonesGrooves` INT(11) NOT NULL DEFAULT '0';
ALTER TABLE `quotations_extra` CHANGE `totalRightEndStonesGrooves` `totalRightEndStonesGrooves` INT(11) NOT NULL DEFAULT '0';
ALTER TABLE `quotations_extra` CHANGE `totalMiddlesStones` `totalMiddlesStones` INT(11) NOT NULL DEFAULT '0';


-- ROBERT - 14-02-2019 - webshop
ALTER TABLE `quotations` ADD `paymentId` VARCHAR(30) DEFAULT NULL COMMENT 'id of psp' AFTER `paymentMethod`;
ALTER TABLE `projects` ADD `stone_id` mediumint(11) unsigned DEFAULT NULL AFTER productFromWebshop;
ALTER TABLE `projects` ADD `mitre_id` mediumint(11) unsigned DEFAULT NULL AFTER stone_id;
ALTER TABLE `projects` ADD `glaced_left` BOOLEAN NOT NULL DEFAULT FALSE AFTER mitre_id;
ALTER TABLE `projects` ADD `glaced_right` TINYINT(1) NOT NULL DEFAULT FALSE AFTER glaced_left;
ALTER TABLE `customer_productincreases` ADD `glazeside` DECIMAL(5,2) NOT NULL DEFAULT '0.00' AFTER `spacers`;

ALTER TABLE `quotations` CHANGE `zipcodeMap` `zipcodeMap` VARCHAR(10) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL;

-- ROBERT - 07-03-2019 - webshop
ALTER TABLE `projects` ADD `webshopOnly` BOOLEAN NOT NULL DEFAULT FALSE AFTER `productFromWebshop`;
ALTER TABLE `projects` ADD INDEX(`webshopOnly`);
ALTER TABLE `projects` ADD INDEX(`showOnProductionPage`);
ALTER TABLE `projects` ADD INDEX(`productFromWebshop`);

ALTER TABLE `quotations` ADD `createdVia` VARCHAR(20) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL DEFAULT 'other' AFTER `separatePackage`;
UPDATE quotations SET createdVia="wizard" WHERE offerteType IS NULL;
UPDATE quotations SET createdVia="other" WHERE createdVia='';

UPDATE product SET online_uc=1;

UPDATE page_content SET content1=REPLACE(content1, 'https://www.raamdorpel.nl/image/', '/uploads/rde/sites/1/ckfiles/');
UPDATE page_content SET content1=REPLACE(content1, 'https://www.raamdorpel.nl/uploads/images/', '/uploads/rde/sites/1/ckfiles/images/');
UPDATE page_content SET content1=REPLACE(content1, 'https://www.raamdorpel.nl/uploads/documents/', '/uploads/rde/sites/1/ckfiles/documents/');
UPDATE page_content SET content1=REPLACE(content1, 'https://www.raamdorpel.nl/uploads/slides/', '/uploads/rde/sites/1/ckfiles/slides/');

-- ROBERT - 21-03-2019 - opslaggroepen stenen
ALTER TABLE `stones` ADD `stoneIncreaseGroup` CHAR(1) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL AFTER `display`;
ALTER TABLE `customer_productincreases` CHANGE `stoneIncreaseWebshop` `stoneIncreaseGroupA` DECIMAL(5,2) NOT NULL;
ALTER TABLE `customer_productincreases` ADD `stoneIncreaseGroupB` DECIMAL(5,2) NOT NULL AFTER stoneIncreaseGroupA;
ALTER TABLE `customer_productincreases` ADD `stoneIncreaseGroupC` DECIMAL(5,2) NOT NULL AFTER stoneIncreaseGroupB;
ALTER TABLE `customer_productincreases` ADD `stoneIncreaseGroupD` DECIMAL(5,2) NOT NULL AFTER stoneIncreaseGroupC;

UPDATE stones SET `stoneIncreaseGroup` = 'A';
UPDATE customer_productincreases SET `stoneIncreaseGroupB` = stoneIncreaseGroupA;
UPDATE customer_productincreases SET `stoneIncreaseGroupD` = stoneIncreaseGroupA;
UPDATE customer_productincreases SET `stoneIncreaseGroupC` = stoneIncreaseGroupA;

-- ROBERT - 11-04-2019 - vrachtkosten
ALTER TABLE `quotations` ADD `freightCosts` DECIMAL(8,2) NULL DEFAULT NULL AFTER `discountValue`;


-- ROBERT - stats
ALTER TABLE `quotations_extra` ADD `prod_cm_per_hour` MEDIUMINT UNSIGNED NULL DEFAULT NULL AFTER `stoneSizeWidth`;
ALTER TABLE `quotations_extra` ADD `prod_employee_id` INT(11) UNSIGNED NULL AFTER `prod_cm_per_hour`;
ALTER TABLE `quotations_extra` ADD INDEX(`prod_cm_per_hour`);
ALTER TABLE `quotations_extra` ADD INDEX(`prod_employee_id`);


ALTER TABLE `crm_bankaccounts` CHANGE `debit` `debit` BOOLEAN NOT NULL DEFAULT FALSE COMMENT 'incasso';
ALTER TABLE `crm_bankaccounts` CHANGE `holder` `holder` VARCHAR(150) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL;
ALTER TABLE `crm_invoiceparties` CHANGE `emailReminder` `emailReminder` VARCHAR(150) CHARACTER SET utf8 COLLATE utf8_general_ci NULL;
ALTER TABLE `crm_invoiceparties` CHANGE `invoicePartyNotes` `invoicePartyNotes` TEXT CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT 'financieel';
ALTER TABLE `crm_invoiceparties` ADD `emailConfirmed` BOOLEAN NOT NULL DEFAULT FALSE AFTER `email`;
UPDATE crm_invoiceparties SET emailConfirmed=1 WHERE email!='' AND NOT email IS NULL;

ALTER TABLE `quotations` ADD `weight_webshop` MEDIUMINT(6) NOT NULL DEFAULT '0' AFTER `weight`;

ALTER TABLE `cargo_receipt`
    CHANGE `shippingAgentExt` `shippingAgentExt` VARCHAR(10) CHARACTER SET utf8 COLLATE utf8_general_ci NULL,
    CHANGE `destinationExt` `destinationExt` VARCHAR(10) CHARACTER SET utf8 COLLATE utf8_general_ci NULL,
    CHANGE `numberOfPackages` `numberOfPackages` INT(10) NOT NULL DEFAULT '0',
    CHANGE `methodOfPacking` `methodOfPacking` ENUM ('rek','bak','pal','opr') CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT 'bak, rek, pallet, ophaalrek',
    CHANGE `natureOfTheGoods` `natureOfTheGoods` VARCHAR(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL,
    CHANGE `staticNumberNear` `staticNumberNear` VARCHAR(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT 'bak in de buurt',
    CHANGE `staticNumberFar` `staticNumberFar` VARCHAR(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT 'Ergens anders',
    CHANGE `staticNumberRetour` `staticNumberRetour` TEXT CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT 'retour',
    CHANGE `gross` `gross` VARCHAR(10) CHARACTER SET utf8 COLLATE utf8_general_ci NULL,
    CHANGE `volume` `volume` VARCHAR(10) CHARACTER SET utf8 COLLATE utf8_general_ci NULL,
    CHANGE `destinationContact` `destinationContact` VARCHAR(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL,
    CHANGE `destinationPhone` `destinationPhone` VARCHAR(15) CHARACTER SET utf8 COLLATE utf8_general_ci NULL,
    CHANGE `destinationMobile` `destinationMobile` VARCHAR(15) CHARACTER SET utf8 COLLATE utf8_general_ci NULL,
    CHANGE `consigneeExecutor` `consigneeExecutor` VARCHAR(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL,
    CHANGE `consigneeExecutorMobile` `consigneeExecutorMobile` VARCHAR(15) CHARACTER SET utf8 COLLATE utf8_general_ci NULL,
    CHANGE `consigneeExecutorMail` `consigneeExecutorMail` VARCHAR(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL,
    CHANGE `senderInstructions` `senderInstructions` VARCHAR(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL,
    CHANGE `carriagePaid` `carriagePaid` TINYINT(1) NOT NULL DEFAULT '0',
    CHANGE `carriageForward` `carriageForward` TINYINT(1) NOT NULL DEFAULT '0',
    CHANGE `cashOnDelivery` `cashOnDelivery` TINYINT(1) NOT NULL DEFAULT '0',
    CHANGE `carrierId` `carrierId` INT(11) NOT NULL DEFAULT '0',
    CHANGE `carrierName` `carrierName` VARCHAR(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL,
    CHANGE `carrierStreet` `carrierStreet` VARCHAR(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL,
    CHANGE `carrierStreetNumber` `carrierStreetNumber` INT(5) NULL DEFAULT NULL,
    CHANGE `carrierExt` `carrierExt` VARCHAR(10) CHARACTER SET utf8 COLLATE utf8_general_ci NULL,
    CHANGE `carrierZip` `carrierZip` VARCHAR(6) CHARACTER SET utf8 COLLATE utf8_general_ci NULL,
    CHANGE `carrierCity` `carrierCity` VARCHAR(70) CHARACTER SET utf8 COLLATE utf8_general_ci NULL,
    CHANGE `carrierNumberplate` `carrierNumberplate` VARCHAR(10) CHARACTER SET utf8 COLLATE utf8_general_ci NULL,
    CHANGE `successiveCarriers` `successiveCarriers` VARCHAR(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL,
    CHANGE `carrierDispatchAppointment` `carrierDispatchAppointment` VARCHAR(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT 'vakje 18',
    CHANGE `specialAgreements` `specialAgreements` VARCHAR(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT 'vakje 19 - door chauffeur omschrijving',
    CHANGE `specialAgreementsRead` `specialAgreementsRead` TINYINT(1) NOT NULL DEFAULT '0',
    CHANGE `toBePaidBy` `toBePaidBy` VARCHAR(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL,
    CHANGE `establishedCity` `establishedCity` VARCHAR(70) CHARACTER SET utf8 COLLATE utf8_general_ci NULL,
    CHANGE `establishedDate` `establishedDate` DATETIME NULL,
    CHANGE `signatureSenderName` `signatureSenderName` VARCHAR(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL,
    CHANGE `signatureSenderFilename` `signatureSenderFilename` VARCHAR(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL,
    CHANGE `signatureSenderPlace` `signatureSenderPlace` VARCHAR(70) CHARACTER SET utf8 COLLATE utf8_general_ci NULL,
    CHANGE `signatureSenderDate` `signatureSenderDate` DATETIME NULL,
    CHANGE `signatureCarrierName` `signatureCarrierName` VARCHAR(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL,
    CHANGE `signatureCarrierFilename` `signatureCarrierFilename` VARCHAR(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL,
    CHANGE `signatureCarrierPlace` `signatureCarrierPlace` VARCHAR(70) CHARACTER SET utf8 COLLATE utf8_general_ci NULL,
    CHANGE `signatureCarrierDate` `signatureCarrierDate` DATETIME NULL,
    CHANGE `signatureCarrierMail` `signatureCarrierMail` VARCHAR(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL,
    CHANGE `signatureConsigneeName` `signatureConsigneeName` VARCHAR(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL,
    CHANGE `signatureConsigneeFilename` `signatureConsigneeFilename` VARCHAR(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL,
    CHANGE `signatureConsigneePlace` `signatureConsigneePlace` VARCHAR(70) CHARACTER SET utf8 COLLATE utf8_general_ci NULL,
    CHANGE `signatureConsigneeDateTime` `signatureConsigneeDateTime` DATETIME NULL,
    CHANGE `signatureConsigneeMail` `signatureConsigneeMail` VARCHAR(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL,
    CHANGE `cmr` `cmr` TINYINT(1) NOT NULL DEFAULT '0',
    CHANGE `avc` `avc` TINYINT(1) NOT NULL DEFAULT '0',
    CHANGE `carrierCode` `carrierCode` VARCHAR(20) CHARACTER SET utf8 COLLATE utf8_general_ci NULL;

ALTER TABLE `invoices` ADD `paid_with` VARCHAR(20) NOT NULL DEFAULT 'bank' COMMENT 'bank/online' AFTER `vatRegShifted`;
ALTER TABLE `invoices` ADD `paid_with_id` VARCHAR(30) NULL AFTER `paid_with`;

ALTER TABLE `invoices` ADD `send_multivers` BOOLEAN NOT NULL DEFAULT FALSE AFTER `paid_with_id`, ADD INDEX (`send_multivers`);

ALTER TABLE `stone_order_item` ADD `loadreference` VARCHAR(20) NULL AFTER `size`;

INSERT INTO `setting` (`id`, `type`, `code`, `value`) VALUES (NULL, 'suspicious', '5', NULL);
INSERT INTO `setting` (`id`, `type`, `code`, `value`) VALUES (NULL, 'suspicious', '10', NULL);
INSERT INTO `setting` (`id`, `type`, `code`, `value`) VALUES (NULL, 'suspicious', '15', NULL);
INSERT INTO `setting` (`id`, `type`, `code`, `value`) VALUES (NULL, 'suspicious', '20', NULL);
INSERT INTO `setting` (`id`, `type`, `code`, `value`) VALUES (NULL, 'suspicious', '25', NULL);
INSERT INTO `setting` (`id`, `type`, `code`, `value`) VALUES (NULL, 'suspicious', '30', NULL);
INSERT INTO `setting` (`id`, `type`, `code`, `value`) VALUES (NULL, 'suspicious', '35', NULL);
INSERT INTO `setting` (`id`, `type`, `code`, `value`) VALUES (NULL, 'suspicious', '40', NULL);
INSERT INTO `setting` (`id`, `type`, `code`, `value`) VALUES (NULL, 'suspicious', '45', NULL);
INSERT INTO `setting` (`id`, `type`, `code`, `value`) VALUES (NULL, 'suspicious', '50', NULL);


ALTER TABLE `crm_companies`
    CHANGE `stJoris` `stJoris` TINYINT(1) NOT NULL DEFAULT '1',
    CHANGE `terca` `terca`     TINYINT(1) NOT NULL DEFAULT '1';
ALTER TABLE `crm_companies`
    CHANGE `introDiscount` `introDiscount`               TINYINT(1) NOT NULL DEFAULT '0',
    CHANGE `containerDoNotReturn` `containerDoNotReturn` TINYINT(1) NOT NULL DEFAULT '0',
    CHANGE `toNumber` `toNumber`                         TINYINT(1) NOT NULL DEFAULT '0',
    CHANGE `callToDelivery` `callToDelivery`             TINYINT(1) NOT NULL DEFAULT '0',
    CHANGE `sendMailToClient` `sendMailToClient`         TINYINT(1) NOT NULL DEFAULT '0',
    CHANGE `executorTicket` `executorTicket`             TINYINT(1) NOT NULL DEFAULT '0',
    CHANGE `sendByPost` `sendByPost`                     TINYINT(1) NOT NULL DEFAULT '0',
    CHANGE `rackContainerReturn` `rackContainerReturn`   TINYINT(1) NOT NULL DEFAULT '0';
ALTER TABLE `crm_companies` CHANGE `noDelivery` `noDelivery` TINYINT(1) NOT NULL DEFAULT '0';
ALTER TABLE `crm_companies` CHANGE `payInAdvance` `payInAdvance` TINYINT(1) NOT NULL DEFAULT '0';

CREATE TABLE `mollie` (
        `id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT,
        `paymentId` varchar(20) NOT NULL,
        `amount` DECIMAL(8,2) DEFAULT 0,
        PRIMARY KEY (`id`),
        KEY `paymentId` (`paymentId`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8;

ALTER TABLE `invoices` ADD `mollie_id` MEDIUMINT(8) UNSIGNED NULL DEFAULT NULL AFTER `paid_with_id`;
ALTER TABLE `quotations` ADD `mollie_id` MEDIUMINT(8) UNSIGNED NULL DEFAULT NULL AFTER `paymentMethod`;
ALTER TABLE `mollie` ADD `insertTS` DATETIME NULL AFTER `amount`;

ALTER TABLE `invoices` DROP `paid_with_id`;
ALTER TABLE `quotations` DROP `paymentId`;
ALTER TABLE `mollie` ADD INDEX(`insertTS`);
ALTER TABLE `quotations` ADD INDEX(`orderDate`);
ALTER TABLE `invoices` ADD INDEX(`mollie_id`);
ALTER TABLE `quotations` ADD INDEX(`mollie_id`);

CREATE TABLE rde_b1mo.damage (
                                 `id` int(11) NOT NULL AUTO_INCREMENT,
                                 `name_nl` varchar(255) NOT NULL,
                                 `name_pl` varchar(255) NOT NULL,
                                 PRIMARY KEY (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE rde_b1mo.damage_quotation (
                                 `id` int(11) NOT NULL AUTO_INCREMENT,
                                 `quotationId` int(11) NOT NULL,
                                 `employeeId` int(11) NOT NULL,
                                 `rackId` int(11) DEFAULT NULL,
                                 `damageId` mediumint(8) UNSIGNED NOT NULL,
                                 `insertTS` datetime NOT NULL,
                                 PRIMARY KEY (id),
                                 KEY quotationId (quotationId),
                                 KEY employeeId (employeeId),
                                 KEY rackId (rackId),
                                 KEY damageId (damageId),
                                 KEY insertTS (insertTS)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- offerteVariant
ALTER TABLE `quotations` ADD `offerteVariant` VARCHAR(50) NULL DEFAULT NULL AFTER `offerteType`, ADD INDEX (`offerteVariant`);

-- mitre prices
CREATE TABLE  `mitre_price` (
                                `id` int(5) UNSIGNED NOT NULL AUTO_INCREMENT,
                                `price` decimal(6,2) NOT NULL,
                                `validFrom` date NOT NULL,
                                `validTo` date NOT NULL DEFAULT '9999-12-31',
                                PRIMARY KEY (`id`),
                                KEY `validFrom` (`validFrom`),
                                KEY `validTo` (`validTo`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
INSERT INTO `mitre_price` (`id`, `price`, `validFrom`, `validTo`) VALUES (NULL, '5', '2020-01-01', '9999-12-31');

-- correctie raar karakter in short
ALTER TABLE `stone_colors` CHANGE `short` `short` VARCHAR(15) CHARACTER SET utf8 COLLATE utf8_unicode_ci NULL DEFAULT NULL;
UPDATE `stone_colors` SET short=NULL WHERE short='';

UPDATE `quotations` SET offerteVariant='keramische_raamdorpel' WHERE offerteVariant='keramische_raamdorpels';


ALTER TABLE `order_elements` ADD `elementLengthTotal` MEDIUMINT(7) UNSIGNED NOT NULL DEFAULT '0' COMMENT 'Totale lengte van 1 element, inclusief mitres' AFTER `elementLength`;

-- custom size raamdorpel
CREATE TABLE rde_b1mo.quotations_custom_stone (
                                        `id` int(11) NOT NULL AUTO_INCREMENT,
                                        `quotationId` int(11) DEFAULT NULL,
                                        `depth` smallint(4) UNSIGNED NOT NULL,
                                        `thickness` smallint(4) UNSIGNED NOT NULL,
                                        `height` smallint(4) UNSIGNED NOT NULL,
                                        PRIMARY KEY (id),
                                        KEY quotationId (quotationId)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- muurdikte bij keramische muurafdekkers
ALTER TABLE `quotations_extra` ADD `wall_thickness` SMALLINT(3) UNSIGNED NULL DEFAULT NULL COMMENT 'Muurdikte in mm' AFTER `stoneSizeWidth`;
ALTER TABLE `stone_sizes` ADD `wall_min` SMALLINT(3) UNSIGNED NULL DEFAULT NULL COMMENT 'Minimale muurdikte in mm' AFTER `click`;
ALTER TABLE `stone_sizes` ADD `wall_max` SMALLINT(3) UNSIGNED NULL DEFAULT NULL COMMENT 'Maximale muurdikte in mm' AFTER `wall_min`;

-- factuur emailadres 18-06-2020
ALTER TABLE `sandbox_users` ADD `invoice_email` VARCHAR(150) NULL AFTER `email`;
ALTER TABLE `sandbox_users` ADD `invoice_email_confirmed` BOOLEAN NOT NULL DEFAULT FALSE AFTER `invoice_email`;

ALTER TABLE `quotations` ADD `deliveryNotes` TEXT NULL AFTER `productionNotes`;

ALTER TABLE `gpsbuddy_routes` CHANGE `company` `company` ENUM('vdh','rde') CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL DEFAULT 'rde';
ALTER TABLE `gpsbuddy_routes` CHANGE `mapExtraPoi` `mapExtraPoi` INT(1) NULL DEFAULT NULL, CHANGE `poiAddresId` `poiAddresId` INT(11) NULL DEFAULT NULL;
ALTER TABLE `gpsbuddy_routes` CHANGE `cargoReceiptId` `cargoReceiptId` INT(11) NULL DEFAULT NULL COMMENT 'vrachtbonId';


ALTER TABLE `sandbox_users` ADD `noti_produced_mail` BOOLEAN NOT NULL DEFAULT TRUE AFTER `private`;

-- vanaf hier gebruiken we het updater script
-- version 1: 14-01-2021

ALTER TABLE `stone_sizes` DROP `displayOrder`;

-- indexes
ALTER TABLE rde_b1mo.`quotations` ADD INDEX(`quotationPart`);
ALTER TABLE rde_b1mo.`quotations` ADD INDEX(`nextRouteDate`);
ALTER TABLE rde_cms.`auth_pages` ADD INDEX(`slug`);
ALTER TABLE rde_cms.`auth_pages` ADD INDEX(`menuId`);
ALTER TABLE rde_cms.`auth_user_group` ADD INDEX(`userId`);
ALTER TABLE rde_cms.`auth_user_group` ADD INDEX(`groupId`);
ALTER TABLE rde_cms.`auth_group_pages` ADD INDEX(`groupId`);
ALTER TABLE rde_cms.`auth_group_pages` ADD INDEX(`pageId`);
ALTER TABLE rde_cms.`auth_menu_pages` ADD INDEX(`menuId`);
ALTER TABLE rde_cms.`auth_menu_pages` ADD INDEX(`pageId`);
ALTER TABLE rde_b1mo.`status` ADD INDEX(`statusId`);
ALTER TABLE rde_b1mo.`quotations` ADD INDEX(`stoneId`);
ALTER TABLE rde_b1mo.`seam_stone_color_link` ADD INDEX(`seamColorId`);
ALTER TABLE rde_b1mo.`seam_stone_color_link` ADD INDEX(`stoneColorId`);
ALTER TABLE heblad5.`gpsbuddy_rde` ADD INDEX(`bakId`);
ALTER TABLE heblad5.`gpsbuddy_rde` ADD INDEX(`routeId`);
ALTER TABLE heblad5.`gpsbuddy_rde` ADD INDEX(`quotationId`);
ALTER TABLE heblad5.`gpsbuddy_trucks` ADD INDEX(`gpsbuddyId`);
ALTER TABLE heblad5.`gpsbuddy_trucks` ADD INDEX(`orderId`);
ALTER TABLE heblad5.`gpsbuddy_routes` ADD INDEX(`cargoReceiptId`);
ALTER TABLE heblad5.`gpsbuddy_routes` ADD INDEX(`bid`);
ALTER TABLE heblad5.`gpsbuddy_routes` ADD INDEX(`date`);
ALTER TABLE heblad5.`gpsbuddy_routes` ADD INDEX(`rank`);
ALTER TABLE rde_b1mo.`containers_quotations` ADD INDEX(`quotationId`);
ALTER TABLE rde_b1mo.`production_order` ADD INDEX(`sizeId`);
ALTER TABLE rde_b1mo.`production_order` ADD INDEX(`productionDate`);
ALTER TABLE rde_b1mo.`quotations_extra` ADD INDEX(`addressDeliveryId`);
ALTER TABLE rde_b1mo.`quotations` ADD INDEX(`zipcode`);
ALTER TABLE rde_b1mo.`quotations` ADD INDEX(`dueDate`);
ALTER TABLE rde_cms.`crm_addresses` ADD INDEX(`zipcode`);
ALTER TABLE rde_cms.`crm_addresses` ADD INDEX(`zipcode`);
ALTER TABLE rde_b1mo.`email_to_clients` ADD INDEX(`routeId`);
ALTER TABLE rde_b1mo.`quotations` ADD INDEX(`dueDateWeek`);
ALTER TABLE rde_cms.`crm_addresses` ADD INDEX(`longitude`);
ALTER TABLE rde_cms.`crm_addresses` ADD INDEX(`latitude`);

-- +foreign key
DROP TABLE `rde_cms`.`crm_banks`;
ALTER TABLE `crm_bankaccounts` DROP `bankId`;

-- opruimen niet gebruikte tabellen
DROP TABLE `rde_cms`.`crm_holdings`;
DROP TABLE `rde_cms`.`crm_keywords`;
DROP TABLE `rde_cms`.`crm_notifications`;
DROP TABLE `rde_cms`.`crm_tradinghours`;
DROP TABLE `rde_b1mo`.`crm_customercodes`;
DROP TABLE `rde_b1mo`.collections;

DROP TABLE rde_b1mo.containers_rides;
DROP TABLE rde_b1mo.rides_pallets;
DROP TABLE rde_b1mo.rides;
DROP TABLE rde_b1mo.drivers;
DROP TABLE rde_b1mo.file_cat;
DROP TABLE rde_b1mo.file_type;
DROP TABLE rde_b1mo.ip_overide_admin;
DROP TABLE rde_b1mo.labels;
DROP TABLE rde_b1mo.news;
DROP TABLE rde_b1mo.news_cat;
DROP TABLE rde_b1mo.layPrices;
DROP TABLE rde_b1mo.production_overview;
DROP TABLE rde_b1mo.quotation_img;
DROP TABLE temp_phonenumbers;

ALTER TABLE `quotations_extra` ADD `supplier_show` DATETIME NULL DEFAULT NULL  COMMENT 'datum zichtbaar gezet voor leverancier' AFTER `customerSplitRights`, ADD INDEX (`supplier_show`);
ALTER TABLE `stone_brands` DROP `mailPdfSentNaturalStone`;
DROP TABLE `rde_b1mo`.`email_to_clients_pdf_naturalstone`;

-- password reset table
CREATE TABLE rde_b1mo.sandbox_users_pw (
                                           `id` mediumint(11) NOT NULL AUTO_INCREMENT,
                                           `userId` int(8) unsigned DEFAULT NULL,
                                           `hash` varchar(255) NOT NULL,
                                           `validuntilTS` datetime NOT NULL,
                                           PRIMARY KEY (id),
                                           KEY userId (userId)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

ALTER TABLE `sandbox_users_pw` ADD INDEX(`hash`);

ALTER TABLE `sandbox_users` DROP `name`;
ALTER TABLE `sandbox_users` DROP `sendQuote`;
ALTER TABLE `sandbox_users` DROP `accounttype`;

UPDATE `quotations` SET noRackQuotations=0 WHERE `noRackQuotations` = -1;
UPDATE `quotations` SET NoContainerQuotations=0 WHERE `NoContainerQuotations` = -1;
UPDATE `quotations` SET NoRackNoContainerQuotations=0 WHERE `NoRackNoContainerQuotations` = -1;
UPDATE `quotations` SET PalletQuotations=0 WHERE `PalletQuotations` = -1;
UPDATE `quotations` SET ToNumberQuotations=0 WHERE `ToNumberQuotations` = -1;

ALTER TABLE `stones` CHANGE `weight` `weight` DECIMAL(5,3) UNSIGNED NOT NULL DEFAULT '0';
ALTER TABLE `stones` CHANGE `weightm1` `weightm1` DECIMAL(5,3) UNSIGNED NOT NULL DEFAULT '0';


CREATE TABLE rde_b1mo.windowsill
(
    `id`        mediumint(8) unsigned NOT NULL AUTO_INCREMENT,
    `name`      varchar(255)  NOT NULL,
    `x1`      varchar(100)  NULL,
    `x2`      varchar(100)  NULL,
    `x3`      varchar(100)  NULL,
    `x4`      varchar(100)  NULL,
    `x5`      varchar(100)  NULL,
    `x6`      varchar(100)  NULL,
    `imagefilename`      varchar(255) NULL,
    PRIMARY KEY (id)
);

CREATE TABLE rde_b1mo.order_element_windowsill
(
    `id`        mediumint(8)  unsigned NOT NULL AUTO_INCREMENT,
    `element_id` int(12) unsigned DEFAULT NULL,
    `windowsill_id` int(8) unsigned DEFAULT NULL,
    `x1`      smallint(6)  NULL,
    `x2`      smallint(6)  NULL,
    `x3`      smallint(6)  NULL,
    `x4`      smallint(6)  NULL,
    `x5`      smallint(6)  NULL,
    `x6`      smallint(6)  NULL,
    PRIMARY KEY (id),
    KEY element_id (element_id),
    KEY windowsill_id (windowsill_id)
);

ALTER TABLE `windowsill` ADD `online` BOOLEAN NOT NULL DEFAULT TRUE AFTER `name`, ADD INDEX (`online`);
ALTER TABLE `windowsill` ADD `imagesmallfilename` VARCHAR(255) NULL AFTER `imagefilename`;
ALTER TABLE `windowsill` ADD `price_surcharge` DECIMAL(6,2) NOT NULL AFTER `imagesmallfilename`;

ALTER TABLE `gpsbuddy_trucks` ADD `active` BOOLEAN NOT NULL DEFAULT TRUE AFTER `orderId`, ADD INDEX (`active`);
ALTER TABLE `order_element_windowsill` ADD `remark_cust` TEXT NULL AFTER `x6`;

-- vensterbank prijzen
ALTER TABLE `stone_prices` CHANGE `price` `price` DECIMAL(6,2) NULL COMMENT 'prijs per steen / prijs per meter';
ALTER TABLE `stone_prices` ADD `price_m2` DECIMAL(6,2) NULL COMMENT 'prijs per m2' AFTER `price`;
ALTER TABLE `stone_prices` ADD `price_piece` DECIMAL(6,2) NULL COMMENT 'prijs per stuk additioneel' AFTER `price_m2`
ALTER TABLE `windowsill` DROP price_surcharge;

-- opslag per materiaal
ALTER TABLE `customer_productincreases` ADD `naturalstoneIncrease` DECIMAL(5,2) NOT NULL DEFAULT '0' AFTER `stoneIncreaseGroupD`;
ALTER TABLE `customer_productincreases` ADD `concreteIncrease` DECIMAL(5,2) NOT NULL DEFAULT '0' AFTER `naturalstoneIncrease`;
ALTER TABLE `customer_productincreases` ADD `isosillIncrease` DECIMAL(5,2) NOT NULL DEFAULT '0' AFTER `concreteIncrease`;
UPDATE `customer_productincreases` SET naturalstoneIncrease = stoneIncrease,`concreteIncrease` = stoneIncrease,`isosillIncrease` = stoneIncrease;

ALTER TABLE `windowsill` ADD `sort` SMALLINT NOT NULL DEFAULT '0' AFTER `imagesmallfilename`, ADD INDEX (`sort`);

ALTER TABLE `quotations` CHANGE `projectType` `projectType` ENUM('newbuilt','renovation') CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT 'renovation' COMMENT 'Type werk / renovatie';

-- opslag chinees natuursteen 13-01-2022
ALTER TABLE `customer_productincreases` ADD `naturalstoneChinaIncrease` DECIMAL(5,2) NOT NULL DEFAULT '0.0' AFTER `naturalstoneIncrease`;
UPDATE `customer_productincreases` SET naturalstoneChinaIncrease = naturalstoneIncrease;

-- opruimen ongebruikte kolommen 17-03-2022
ALTER TABLE `quotations` DROP `separatePackage`;
ALTER TABLE `quotations` DROP `notBeforeDate`;
ALTER TABLE `quotations` DROP `prepareDate`;
ALTER TABLE `quotations` DROP `discountValue`;
ALTER TABLE `quotations` DROP `stonesFacilitated`;
ALTER TABLE `quotations` DROP `zipcodeZone`;
ALTER TABLE `quotations` DROP `zipcodeMap`;
ALTER TABLE `quotations` DROP `latitude`;
ALTER TABLE `quotations` DROP `longitude`;
ALTER TABLE `quotations` DROP `nextRouteNote`;
ALTER TABLE `quotations` DROP `customColor`;
ALTER TABLE `quotations` DROP `customSize`;
ALTER TABLE `quotations` DROP `billingFeature`;
ALTER TABLE `production_order` DROP `custom`;
ALTER TABLE `production_order` DROP `customColor`;
ALTER TABLE `production_order` DROP `customSize`;
ALTER TABLE `quotations_extra` DROP `carrierCompany`;

-- verwijdern offerteVariant, toevoegen stone categorieen en materiaal

CREATE TABLE rde_b1mo.stone_category
(
    `id`        mediumint(8) unsigned NOT NULL AUTO_INCREMENT,
    `parent_id` mediumint(8) unsigned NULL,
    `online`    BOOLEAN               NOT NULL DEFAULT TRUE,
    `name`      varchar(255)          NOT NULL,
    `sort`      SMALLINT              NOT NULL DEFAULT '0',
    PRIMARY KEY (id),
    INDEX(parent_id),
    INDEX(online),
    INDEX(sort)
);
ALTER TABLE `stone_category` ADD `imagefilename` VARCHAR(255) NULL DEFAULT NULL AFTER `name`;
ALTER TABLE `stone_category` ADD `shortname` VARCHAR(255) NULL DEFAULT NULL AFTER `name`;
UPDATE stone_category SET shortname = name;

ALTER TABLE `quotations` ADD `stoneCategoryId` MEDIUMINT(8) UNSIGNED NULL DEFAULT NULL AFTER `stoneId`;
ALTER TABLE stones ADD `category_id` MEDIUMINT(8) UNSIGNED NULL DEFAULT NULL AFTER `stoneId`;
ALTER TABLE `stones` ADD `material` VARCHAR(40) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL AFTER `type`, ADD INDEX (`material`);
ALTER TABLE `stones` CHANGE `type` `type` VARCHAR(30) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL DEFAULT 'raamdorpel';
UPDATE stones SET type = 'spekband' WHERE type='spekbanden';

-- steen kleuren
ALTER TABLE `stone_colors` ADD `hexcolor` VARCHAR(10) NULL AFTER `discount`;
ALTER TABLE `stone_colors` ADD `filename` VARCHAR(100) NULL AFTER `hexcolor`;

-- zaagopslag
ALTER TABLE `stone_prices` ADD `price_mitre` DECIMAL(6,2) NULL DEFAULT NULL AFTER `price_piece`;
ALTER TABLE `windowsill` ADD `mitre_factor` DECIMAL(3,1) UNSIGNED NOT NULL DEFAULT '0' AFTER `imagesmallfilename`;

-- sms
ALTER TABLE `quotations_extra` ADD `sms` BOOLEAN NOT NULL DEFAULT FALSE AFTER `supplier_show`;
ALTER TABLE `quotations_extra` ADD `smsnumber`  VARCHAR(30)  DEFAULT NULL AFTER `sms`;
ALTER TABLE `sandbox_users` ADD `sms` BOOLEAN NOT NULL DEFAULT FALSE AFTER `supplier`;
ALTER TABLE `quotations_extra` ADD INDEX(`sms`);
ALTER TABLE `quotations_extra` ADD INDEX(`smsnumber`);
ALTER TABLE `sandbox_users` ADD INDEX(`sms`);

-- sms_delivered
ALTER TABLE `quotations_extra` ADD `sms_delivered` BOOLEAN NOT NULL DEFAULT FALSE AFTER `sms`;
ALTER TABLE `sandbox_users` ADD `sms_delivered` BOOLEAN NOT NULL DEFAULT FALSE AFTER `sms`;
ALTER TABLE `quotations_extra` ADD INDEX(`sms_delivered`);
ALTER TABLE `sandbox_users` ADD INDEX(`sms_delivered`);

-- balkjes width / height invoeren
CREATE TABLE rde_b1mo.order_element_size
(
    `id`         mediumint(8) unsigned                           NOT NULL AUTO_INCREMENT,
    `element_id` int(12) unsigned DEFAULT NULL,
    `code`       VARCHAR(30) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
    `value`      smallint(6)                                     NULL,
    PRIMARY KEY (id),
    KEY element_id (element_id)
);

ALTER TABLE `files` CHANGE `quotationId` `quotationId` INT(11) NULL DEFAULT NULL;
ALTER TABLE `files` CHANGE `companyId` `companyId` INT(11) NULL DEFAULT NULL;
ALTER TABLE `files` CHANGE `uploadAlert` `uploadAlert` TINYINT(1) NOT NULL DEFAULT '0';

ALTER TABLE `stones` ADD `color_model_code` INT(10) UNSIGNED NULL DEFAULT NULL AFTER `modelSpecialForProduction`;

UPDATE quotations_old SET folder=REPLACE(folder, '/var/www/vhosts/raamdorpel.nl/httpdocs/', '');
UPDATE invoices_old SET folder=REPLACE(folder, '/var/www/vhosts/raamdorpel.nl/httpdocs/', '');

ALTER TABLE `files` ADD INDEX(`quotationId`);
ALTER TABLE `files` ADD INDEX(`companyId`);
ALTER TABLE `files` ADD INDEX(`categoryId`);
ALTER TABLE `files` ADD INDEX(`typeId`);
ALTER TABLE `files` ADD INDEX(`showInDocuments`);

-- -------------------------- RDE - LIVE -----------------------------------------------

