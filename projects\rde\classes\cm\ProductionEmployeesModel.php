<?php

  AppModel::loadBaseClass('BaseProductionEmployees');

  class ProductionEmployeesModel extends BaseProductionEmployees {

    public const TESTERS = [
      0,
      33,
    ];

    public static function getWorkingNotTesters() {
      return ProductionEmployees::find_all_by(["working" => 1], "AND " . DbHelper::getSqlIn('employeeId', ProductionEmployees::TESTERS, true, true) . " ORDER BY name");
    }

  }