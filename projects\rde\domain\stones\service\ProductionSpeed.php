<?php

  namespace domain\stones\service;

  use AppModel;
  use DBConn;
  use EmployeeQuotation;
  use ProductionEmployees;
  use Quotations;
  use QuotationsExtra;
  use Stones;

  class ProductionSpeed {

    private ProductionEmployees $employee;
    private string $fromDate;
    private string $toDate;

    public function __construct($employee) {
      $this->employee = $employee;
      $this->fromDate = date("Y-01-01");
      $this->toDate = date("Y-12-31");
    }

    public function getFromDate(): string {
      return $this->fromDate;
    }

    public function setFromDate(string $fromDate): void {
      $this->fromDate = $fromDate;
    }

    public function getToDate(): string {
      return $this->toDate;
    }

    public function setToDate(string $toDate): void {
      $this->toDate = $toDate;
    }


    public function execute() {

      $filter = "WHERE startdate>='" . $this->getFromDate() . "' AND enddate<='" . $this->getToDate() . "' ";
      $filter .= "AND employeeId=" . $this->employee->employeeId . " ";
      $filter .= "ORDER BY id DESC ";
//        $filter .= "LIMIT 10000, 100000";
      $equotations = EmployeeQuotation::find_all($filter);

      $hourlyChart = [];
      for ($tel = 6; $tel < 19; $tel++) {
        $hourlyChart[$tel] = [];
      }

      foreach ($equotations as $equotation) {

        $eqs = EmployeeQuotation::find_all_by(["quotationId" => $equotation->quotationId]);

        $quotation_extra = QuotationsExtra::find_by(["quotationId" => $equotation->quotationId]);
        if (!$quotation_extra) {
          continue;
        }
        $quotation = Quotations::find_by(["quotationId" => $equotation->quotationId]);

        if (count($eqs) != 1) {
          continue;
        }
        $eq = $eqs[0];

        if ($eq->getStartdateFormatted("dmY") != $eq->getEnddateFormatted("dmY")) {
          continue;
        }
        elseif (!$quotation) {
          continue;
        }
        elseif ($quotation->meters == 0) {
          continue;
        }

        $minutes = ((int)$eq->getEnddateFormatted("U") - (int)$eq->getStartdateFormatted("U")) / 60;
        $prevTime = intval($eq->getStartdateFormatted("Hi"));
        $curTime = intval($eq->getEnddateFormatted("Hi"));
        if (($prevTime <= 1005 && $curTime >= 1010) || ($prevTime <= 1505 && $curTime >= 1510)) {
          //er zit koffie pauze tussen
          $minutes -= 15;
        }
        elseif ($prevTime <= 1235 && $curTime >= 1255) {
          //er zit lunch pauze tussen
          $minutes -= 30;
        }

        if ($minutes == 0) continue;

        $speed_calc = floor(($quotation->meters / ($minutes / 60)) * 100); // cm per uur
        if ($speed_calc < 200 || $speed_calc > 1700) {
          continue;
        }

        if (!isset($hourlyChart[date("G", $eq->getStartdateFormatted("U"))])) {
          //starttijd buiten normale werktijden. Overslaan.
          continue;
        }


        $startime = $eq->getStartdateFormatted("U");
        for ($cTime = $startime; $cTime < $eq->getEnddateFormatted("U"); $cTime = strtotime("+1 HOURS", $cTime)) {
          $hourlyChart[date("G", $cTime)][$eq->getStartdateFormatted()] = $speed_calc;
        }

//        dump($minutes);
//        dump($quotation->meters);
//        dump($eq->getStartdateFormatted());
//        dump($eq->getEnddateFormatted());

      }

      $calculatedHourslyChart = [];
      foreach ($hourlyChart as $hour => $values) {
        if (count($values) == 0) {
          $calculatedHourslyChart[$hour] = 0;
          continue;
        }
        $sum = 0;
        foreach ($values as $value) {
          $sum += $value;
        }
        $calculatedHourslyChart[$hour] = round($sum / count($values));
      }

//      dump($hourlyChart);
//      dumpe($calculatedHourslyChart);
      return $calculatedHourslyChart;
    }

  }