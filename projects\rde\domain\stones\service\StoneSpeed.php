<?php

  namespace domain\stones\service;

  use AppModel;
  use DBConn;
  use ProductionEmployees;
  use Quotations;
  use QuotationsExtra;
  use Stones;

  class StoneSpeed {

    private array $stats = [];
    private int $faster = 0;
    private int $slower = 0;
    private string $fromDate;
    private string $toDate;

    public function __construct() {
      $this->fromDate = date("Y-01-01");
      $this->toDate = date("Y-12-31");
    }

    public function getStats() {
      return $this->stats;
    }

    public function getFromDate(): string {
      return $this->fromDate;
    }

    public function setFromDate(string $fromDate): void {
      $this->fromDate = $fromDate;
    }

    public function getToDate(): string {
      return $this->toDate;
    }

    public function setToDate(string $toDate): void {
      $this->toDate = $toDate;
    }


    /**
     * @return int
     */
    public function getFaster(): int {
      return $this->faster;
    }

    /**
     * @return int
     */
    public function getSlower(): int {
      return $this->slower;
    }

    /**
     * @param ProductionEmployees $employee
     * @param bool|int $stoneId
     * @return Stones[]
     */
    public function getEmployeeStoneSpeed($employee, $stoneId = false) {
      $this->stats = [];
      $this->slower = 0;
      $this->faster = 0;

      $employees = AppModel::mapObjectIds(ProductionEmployees::find_all_by(["working" => 1], "ORDER BY name"), "employeeId");

      $query = "SELECT * FROM " . Quotations::getTablename() . " ";
      $query .= "JOIN " . QuotationsExtra::getTablename() . " ON quotations_extra.quotationId=quotations.quotationId ";
      $query .= "WHERE NOT prod_cm_per_hour IS NULL AND prod_cm_per_hour!=0 ";
      $query .= "AND productionDate>='" . $this->getFromDate() . "' AND productionDate<='" . $this->getToDate() . "' ";
      if ($stoneId !== false) {
        $query .= "AND stoneId=" . $stoneId . " ";
      }
      $query .= "ORDER BY productionDate";

      $stoneinfo = [];
      $employeeStoneIds = [];
      $result = DBConn::db_link()->query($query);
      while ($row = $result->fetch_row()) {
        $quot = new Quotations();
        $quot->hydrate($row);
        $quotextra = new QuotationsExtra();
        $quotextra->hydrate($row, count(Quotations::columns));
        $quot->quotations_extra = $quotextra;

        if (!isset($this->stats[$quotextra->prod_employee_id])) {
          $this->stats[$quotextra->prod_employee_id]["quotes"] = [];
          $this->stats[$quotextra->prod_employee_id]["total_meters"] = 0;
          $this->stats[$quotextra->prod_employee_id]["total_orders"] = 0;
        }
        if (!isset($this->stats[$quotextra->prod_employee_id][$quot->stoneId])) {
          $this->stats[$quotextra->prod_employee_id][$quot->stoneId]["total_orders"] = 0;
          $this->stats[$quotextra->prod_employee_id][$quot->stoneId]["total_cm_per_hour_end"] = 0;
          $this->stats[$quotextra->prod_employee_id][$quot->stoneId]["total_cm_per_hour_noend"] = 0;
          $this->stats[$quotextra->prod_employee_id][$quot->stoneId]["cm_per_hour_end"] = 0;
          $this->stats[$quotextra->prod_employee_id][$quot->stoneId]["cm_per_hour_noend"] = 0;
          $this->stats[$quotextra->prod_employee_id][$quot->stoneId]["total_meters"] = 0;
        }

        //$this->stats[$quotextra->prod_employee_id]["quotes"][] = $quot;
        $this->stats[$quotextra->prod_employee_id]["total_orders"]++;
        $this->stats[$quotextra->prod_employee_id]["total_meters"] += $quot->meters;
        $this->stats[$quotextra->prod_employee_id][$quot->stoneId]["total_meters"] += $quot->meters;
        $this->stats[$quotextra->prod_employee_id][$quot->stoneId]["total_orders"]++;
        if ($quot->endstone == "false" || $quot->endstone == "") {
          $this->stats[$quotextra->prod_employee_id][$quot->stoneId]["total_cm_per_hour_noend"] += $quotextra->prod_cm_per_hour;
          $this->stats[$quotextra->prod_employee_id][$quot->stoneId]["cm_per_hour_noend"] = $this->stats[$quotextra->prod_employee_id][$quot->stoneId]["total_cm_per_hour_noend"] / $this->stats[$quotextra->prod_employee_id][$quot->stoneId]["total_orders"];
        }
        else {
          $this->stats[$quotextra->prod_employee_id][$quot->stoneId]["total_cm_per_hour_end"] += $quotextra->prod_cm_per_hour;
          $this->stats[$quotextra->prod_employee_id][$quot->stoneId]["cm_per_hour_end"] = $this->stats[$quotextra->prod_employee_id][$quot->stoneId]["total_cm_per_hour_end"] / $this->stats[$quotextra->prod_employee_id][$quot->stoneId]["total_orders"];
        }

        //stones
        if (!isset($stoneinfo[$quot->stoneId])) {
          $stoneinfo[$quot->stoneId]["total_orders_end"] = 0;
          $stoneinfo[$quot->stoneId]["total_orders_noend"] = 0;
          $stoneinfo[$quot->stoneId]["total_meters_end"] = 0;
          $stoneinfo[$quot->stoneId]["total_meters_noend"] = 0;
          $stoneinfo[$quot->stoneId]["total_cm_per_hour_end"] = 0;
          $stoneinfo[$quot->stoneId]["total_cm_per_hour_noend"] = 0;
          $stoneinfo[$quot->stoneId]["stoneId"] = $quot->stoneId;
        }

        if ($quot->endstone == "false" || $quot->endstone == "") {
          $stoneinfo[$quot->stoneId]["total_orders_noend"]++;
          $stoneinfo[$quot->stoneId]["total_meters_noend"] += $quot->meters;
          $stoneinfo[$quot->stoneId]["total_cm_per_hour_noend"] += $quotextra->prod_cm_per_hour;
          $stoneinfo[$quot->stoneId]["cm_per_hour_noend"] = $stoneinfo[$quot->stoneId]["total_cm_per_hour_noend"] / $stoneinfo[$quot->stoneId]["total_orders_noend"];
        }
        else {
          $stoneinfo[$quot->stoneId]["total_orders_end"]++;
          $stoneinfo[$quot->stoneId]["total_meters_end"] += $quot->meters;
          $stoneinfo[$quot->stoneId]["total_cm_per_hour_end"] += $quotextra->prod_cm_per_hour;
          $stoneinfo[$quot->stoneId]["cm_per_hour_end"] = $stoneinfo[$quot->stoneId]["total_cm_per_hour_end"] / $stoneinfo[$quot->stoneId]["total_orders_end"];
        }

        if ($quotextra->prod_employee_id == $employee->employeeId) {
          $employeeStoneIds[$quot->stoneId] = $quot->stoneId;
        }

      }

      $stones = Stones::find_all_by(["stoneId" => $employeeStoneIds]);
      foreach ($stones as $stone) {
        $stone->procent_end = 0;
        $stone->cm_per_hour_end = 0;
        $stone->cm_per_hour_avg_end = 0;
        $stone->procent_noend = 0;
        $stone->cm_per_hour_noend = 0;
        $stone->cm_per_hour_avg_noend = 0;

        $cm_per_hour_end = 0;
        $cm_per_hour_noend = 0;
        $cm_per_hour_count_end = 0;
        $cm_per_hour_count_noend = 0;
        foreach ($employees as $lemployee) {
          if (isset($this->stats[$lemployee->employeeId][$stone->stoneId])) {
            if ($this->stats[$lemployee->employeeId][$stone->stoneId]["cm_per_hour_end"] != 0) {
              $cm_per_hour_end += $this->stats[$lemployee->employeeId][$stone->stoneId]["cm_per_hour_end"];
              $cm_per_hour_count_end++;
            }
            if ($this->stats[$lemployee->employeeId][$stone->stoneId]["cm_per_hour_noend"] != 0) {
              $cm_per_hour_noend += $this->stats[$lemployee->employeeId][$stone->stoneId]["cm_per_hour_noend"];
              $cm_per_hour_count_noend++;
            }
          }
        }
        $stone->total_orders = $this->stats[$employee->employeeId][$stone->stoneId]["total_orders"];
        $stone->total_meters = $this->stats[$employee->employeeId][$stone->stoneId]["total_meters"];

        if ($this->stats[$employee->employeeId][$stone->stoneId]["cm_per_hour_end"] != 0) {
          $stone->cm_per_hour_end = $this->stats[$employee->employeeId][$stone->stoneId]["cm_per_hour_end"];
          $stone->cm_per_hour_avg_end = $cm_per_hour_end / $cm_per_hour_count_end;
          $stone->procent_end = (($stone->cm_per_hour_end / $stone->cm_per_hour_avg_end) - 1) * 100;
        }
        if ($this->stats[$employee->employeeId][$stone->stoneId]["cm_per_hour_noend"] != 0) {
          $stone->cm_per_hour_noend = $this->stats[$employee->employeeId][$stone->stoneId]["cm_per_hour_noend"];
          $stone->cm_per_hour_avg_noend = $cm_per_hour_noend / $cm_per_hour_count_noend;
          $stone->procent_noend = (($stone->cm_per_hour_noend / $stone->cm_per_hour_avg_noend) - 1) * 100;
        }

        if ($stone->procent_end >= 10 || $stone->procent_noend >= 10) {
          $this->faster++;
        }
        if ($stone->procent_end <= -10 || $stone->procent_noend <= -10) {
          $this->slower++;
        }

      }

      usort($stones, function ($a, $b) {
        return strcmp($a->name, $b->name);
      });

      return $stones;

    }

  }