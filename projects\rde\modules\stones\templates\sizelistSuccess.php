<?php TemplateHelper::includePartial('_tabs.php', 'stones'); ?>

<div class="box">
    <form method="post" action="<?php echo reconstructQueryAdd() ?>">
      <input type="text" name="size_search" value="<?php echo $_SESSION['size_search'] ?>" placeholder="Zoeken..."/>
      <select name="size_brand" id="size_brand">
        <option value="">Selecteer merk...</option>
        <?php foreach($brands as $brand): ?>
          <option value="<?php echo $brand->brandId ?>" <?php if($_SESSION['size_brand']==$brand->brandId) echo 'selected'; ?>><?php echo $brand->name ?></option>
        <?php endforeach; ?>
      </select>
      <select name="size_display" id="size_display">
        <option value="">Selecteer zichtbaar...</option>
        <option value="true" <?php if($_SESSION['size_display']=="true") echo 'selected'; ?>>Ja</option>
        <option value="false" <?php if($_SESSION['size_display']=="false") echo 'selected'; ?>>Nee</option>
      </select>
      <input type="submit" name="go" id="go" value="Zoeken" />

      <a href="?action=sizeedit" class="gsd-btn gsd-btn-primary gsd-btn-primary">Toevoegen nieuwe maat</a>
    </form>
  </div>

  <?php $pager->writePreviousNext(); ?>

  <?php if(count($items)==0): ?>
    <br/>
    Er zijn nog geen items gevonden.
  <?php else: ?>
    <table class="default_table" style="width: auto;">
      <tr class="dataTableHeadingRow">
        <td>Merk</td>
        <td>Afmeting</td>
        <td>Lengte</td>
        <td>Breedte</td>
        <td>Hoogte</td>
        <td>Zichtbaar</td>
        <td style="width: 70px;">Actie</td>
      </tr>
      <?php
        /** @var StoneSizes $item */
        foreach($items as $item): ?>
        <tr class="dataTableRow trhover">
          <td><?php echo $brands[$item->brandId]->name ?></td>
          <td><?php echo $item->name ?></td>
          <td><?php echo $item->length ?></td>
          <td><?php echo $item->width ?></td>
          <td><?php echo $item->height ?></td>
          <td><?php echo $item->display=="true"?"Ja":"Nee" ?></td>
          <td>
            <?php echo BtnHelper::getEdit(reconstructQueryAdd(['pageId']).'action=sizeedit&id='.$item->sizeId) ?>
          </td>
        </tr>
      <?php endforeach; ?>
    </table>
  <?php endif; ?>

<script>
  $(document).ready(function() {
    $("#size_brand,#size_display").change(function() {
      $("#go").click();
    })
  });
</script>