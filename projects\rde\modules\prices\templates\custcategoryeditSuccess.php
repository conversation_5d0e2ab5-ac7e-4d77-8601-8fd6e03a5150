<?php TemplateHelper::includePartial('_tabs.php', 'prices'); ?>

<h3>Bewerk klantgroep opslag</h3>

<?php writeErrors($errors, true); ?>

<form method="post">
  <table class="default_table" style="width: auto;">
    <tr class="dataTableHeadingRow">
      <td>Item</td>
      <td>Instelling</td>
    </tr>
    <tr class="dataTableRow trhover">
      <td>G<PERSON><PERSON> vanaf</td>
      <td>
        01-01-<?php echo $_GET["yearfrom"] ?>
      </td>
    </tr>
  </table>
  <br/>

  <table class="default_table" style="width: auto;">
    <tr class="dataTableHeadingRow">
      <td>Naam</td>
      <td>Steen opslag %</td>
      <td>Lijm opslag %</td>
      <td>Voegsel %</td>
      <td>Koud glazuur %</td>
      <td>Afstand houders %</td>
      <td>Glazuur webshop %</td>
      <td>Natuursteen %</td>
      <td>Natuursteen chinees %</td>
      <td>Beton %</td>
      <td>Isosill %</td>
      <td>Steenopslag A %</td>
      <td>Steenopslag B %</td>
      <td>Steenopslag C %</td>
      <td>Steenopslag D %</td>
    </tr>
    <?php
      /** @var StoneSizes $item */
      foreach($forms as $groupId=>$form): ?>
        <tr class="dataTableRow trhover">
          <td><?php echo $groups[$groupId]->name ?></td>
          <td><?php $form->getElement("stoneIncrease")->render() ?></td>
          <td><?php $form->getElement("glueIncrease")->render() ?></td>
          <td><?php $form->getElement("joint")->render() ?></td>
          <td><?php $form->getElement("coldGlaze")->render() ?></td>
          <td><?php $form->getElement("spacers")->render() ?></td>
          <td><?php $form->getElement("glazeside")->render() ?></td>
          <td><?php $form->getElement("naturalstoneIncrease")->render() ?></td>
          <td><?php $form->getElement("naturalstoneChinaIncrease")->render() ?></td>
          <td><?php $form->getElement("concreteIncrease")->render() ?></td>
          <td><?php $form->getElement("isosillIncrease")->render() ?></td>
          <td><?php $form->getElement("stoneIncreaseGroupA")->render() ?></td>
          <td><?php $form->getElement("stoneIncreaseGroupB")->render() ?></td>
          <td><?php $form->getElement("stoneIncreaseGroupC")->render() ?></td>
          <td><?php $form->getElement("stoneIncreaseGroupD")->render() ?></td>
        </tr>
      <?php endforeach; ?>
  </table>

  <br/>
  <input type="submit" name="go" value="Opslaan" />
  <input type="submit" name="go_list" value="Opslaan en naar lijst" />

</form>
<script type="text/javascript">
  $(document).ready(function () {
    $(".price").focus(function() {
      $(this).select();
    });


    $("input[type=number]").change( function() {
      if($(this).val()!="") {
        var val = $(this).val();
        //console.log(val);
        val = decimalNL(val,2);
        $(this).val(val);
      }
    });

  });

</script>
<style>
  input[type=number] {
    width: 90px;
    text-align: right;
  }
</style>