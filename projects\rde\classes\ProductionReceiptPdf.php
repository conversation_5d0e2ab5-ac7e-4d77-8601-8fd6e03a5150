<?php

  class ProductionReceiptPdf extends GSDPDF {

    protected $supplierExternal = false;
    protected $multiple = false;

    public function __construct($quotationId) {

      parent::__construct();

      $this->AddFont('Nunito', '', 'Nunito-Regular.ttf', true);
      $this->AddFont('Nunito', 'B', 'Nunito-Bold.ttf', true);
      $this->SetAutoPageBreak(true, 30);

      $this->quotation = Quotations::find_by(['quotationId' => $quotationId]);
      $this->sandbox_user = SandboxUsers::find_by(['userId' => $this->quotation->userId]);
      $this->quotation_extra = QuotationsExtra::find_by(['quotationId' => $quotationId]);
      $this->seam_color = SeamColor::find_by(['seamColorId' => $this->quotation_extra->seamColorId]);
      $this->driving_routes = DrivingRoutes::find_by([], ' WHERE ' . substr($this->quotation->zipcode, 0, -2) . ' BETWEEN driving_routes.start AND driving_routes.end ');
      $this->stones = Stones::find_by(['stoneId' => $this->quotation->stoneId]);
      $this->stone_sizes = StoneSizes::find_by(['sizeId' => $this->stones->sizeId]);
      $this->stone_brands = StoneBrands::find_by(['brandId' => $this->stones->brandId]);
      $this->stone_color = StoneColors::find_by(['colorId' => $this->stones->colorId]);
      $this->quotation_custom_stone = QuotationsCustomStone::find_by(['quotationId' => $quotationId]);
      $this->order_elements = OrderElements::find_all_by([ 'quotationId' => $quotationId]);
    }

    /**
     * @param bool $supplierExternal
     */
    public function setSupplierExternal(bool $supplierExternal): void {
      $this->supplierExternal = $supplierExternal;
    }

    /**
     * @return bool
     */
    public function isSupplierExternal(): bool {
      return $this->supplierExternal;
    }

    /**
     * @param bool $multiple
     */
    public function setMultiple(bool $multiple): void {
      $this->multiple = $multiple;
    }

    /**
     * @return bool
     */
    public function isMultiple(): bool {
      return $this->multiple;
    }


    public function build() {

      $this->addPage();

      $quotation = $this->quotation;
      $sandbox_user = $this->sandbox_user;
      $quotation_extra = $this->quotation_extra;
      $seam_color = $this->seam_color;
      $driving_routes = $this->driving_routes;
      $stones = $this->stones;
      $stone_size = $this->stone_sizes;
      $stone_brand = $this->stone_brands;
      $stone_color = $this->stone_color;
      $quotation_custom_stone = $this->quotation_custom_stone;
      $order_elements = $this->order_elements;

      $this->SetFont('Arial', '', 8);
      $this->SetDrawColor(50, 50, 50);
      $this->SetTextColor(0, 0, 0);
      $this->SetLeftMargin(10);
      $this->SetTopMargin(0);

      $this->SetAutoPageBreak(true, 2);

      $fontSizeMainData = 10;
      $cellHeightTopRight = 10;
      $xyCoordinatesStart = 5;
      $xyCoordinatesFromLeft = 5;

      $this->setXY($xyCoordinatesFromLeft, $xyCoordinatesStart);

      $this->SetLineWidth(0.2);
      $this->SetDrawColor(255, 0, 0);
      $this->SetFillColor(0, 255, 0);

      $this->SetFont('Arial', 'B', 16);
      $this->setXY(5, 5);
      $this->Cell(50, 10, 'Productiestaat', 0, 1);
      $this->SetDrawColor(255, 0, 0);

      $quotationNumber = $quotation->getQuotationNumberFull();

      $this->setXY(5, 12);
      $this->Cell(50, $cellHeightTopRight, $quotationNumber, 0);

      //- left, top
      $this->setXY(0, 0);

      //-- location, x, y, w, h, tyle, link
      $this->Image('https://www.raamdorpel.nl/3rdparty/barcodegen/datamatrix.php?text=' . $quotationNumber, 50, 7, 13, 13, 'PNG');

      if ($this->isSupplierExternal()) {
        $this->SetFont('Arial', 'B', 16);
        $this->setXY(67, 7);
        $this->Cell(22, 6, (string)$quotation_extra->addressDeliveryId);
        $this->SetFont('Arial', 'B', 16);
      }

      //-- rood text
      $this->SetTextColor(255, 0, 0);

      if ($quotation->mountingPrice > 0) {
        $this->setXY(0, 0);
        $this->setXY(80, 5);
        $this->SetFont('Arial', 'B', 14);
        $this->Cell(22, $cellHeightTopRight, 'montage', 1);
      }

      //-- niet laten zien bij Grijs (standaard)
      //-- deze straks weer aanzetten.
      if ($seam_color->seamColorId != 1) {
        $this->setXY(0, 0);
        $this->setXY(110, 5);
        $this->SetFont('Arial', 'B', 14);
        $this->Multicell(35, 6, 'voegkleur ' . $seam_color->name, 1);
      }

      if ($quotation->toNumberQuotations == '1') {
        $this->setXY(0, 0);
        $this->setXY(153, 5);
        $this->SetFont('Arial', 'B', 14);
        $this->Multicell(28, 6, 'elementen nummeren', 1);
      }

      //-- zwart text
      $this->SetTextColor(0, 0, 0);
      $this->setXY(0, 0);

      //-- code
      $this->setXY(200, 5);

      if (isset($driving_routes->code)) {
        //-- w, h
        $this->Cell(5, 10, $driving_routes->code, 0);
      }
      else {
        //-- w, h
        $this->Cell(5, 10, '', 0);
      }

      $cellHeightMainData = 5;
      $cellWidthMainData = 50;
      $cellWidthMainData1 = 30;
      $cellWidthMainData2 = 70;

      if ($this->isSupplierExternal()) {
        //toevoegen afbeelding aan breti/external pdf

        $this->SetFont('Arial', '', 10);

        if (in_array($stones->brandId, StoneBrands::getBretiBrandIds())) {

          if ($quotation_custom_stone) {
            $A = $quotation_custom_stone->depth;
            $B = $quotation_custom_stone->thickness;
            $C = $quotation_custom_stone->height;
            $D = $quotation_custom_stone->width_click;
            $E = $quotation_custom_stone->height_click;
          }
          else {
            //maak even een dummy aan.
            $A = $stone_size->length * 10;
            $B = $stone_size->height * 10;
            $det_c = explode("/", $stone_size->name);
            $C = trim($det_c[1] ?? '');
            $D = 20;
            $E = 20;
          }

          if ($quotation_custom_stone && ($C == "" || $D == 0 || $E == 0)) {
            $this->setXY(15, 40);
            $this->Cell(10, 5, 'Op maat gemaakt, met bijzondere afmeting.');
            $this->setXY(15, 45);
            $this->Cell(10, 5, 'Maak zelf een schets.');
          }
          elseif ($stones->type == "balkjes") {
            $image_path = DIR_PROJECT_FOLDER . 'templates/frontend/images/balkje.png';
            $this->Image($image_path, 20, 25);

            $this->setXY(49, 65);
            $this->Cell(10, 5, 'breedte'); //A

            $this->setXY(8, 39);
            $this->Cell(10, 5, 'hoogte'); //B

          }
          elseif ($stones->brandId == 5) {
            $image_path = DIR_PROJECT_FOLDER . 'templates/frontend/images/opmaatgemaakt_budget.png';
            $this->Image($image_path, 20, 28);

            $this->setXY(49, 59);
            $this->Cell(10, 5, $A . 'mm'); //A

            $this->setXY(8, 44);
            $this->Cell(10, 5, $B . 'mm'); //B

            $this->setXY(86, 41);
            $this->Cell(10, 5, $C . 'mm'); //C

            $this->setXY(73, 22);
            $this->Cell(10, 5, $D . 'mm'); //D

            $this->setXY(60, 33);
            $this->Cell(10, 5, $E . 'mm'); //E
          }
          elseif ($stones->brandId == 6 || $stones->brandId == 7) {
            $image_path = DIR_PROJECT_FOLDER . 'templates/frontend/images/opmaatgemaakt_default.png';
            $this->Image($image_path, 20, 25);

            $this->setXY(49, 63);
            $this->Cell(10, 5, $A . 'mm'); //A

            $this->setXY(8, 46);
            $this->Cell(10, 5, $B . 'mm'); //B

            $this->setXY(86, 41);
            $this->Cell(10, 5, $C . 'mm'); //C

            $this->setXY(73, 19);
            $this->Cell(10, 5, $D . 'mm'); //D

            $this->setXY(60, 30);
            $this->Cell(10, 5, $E . 'mm'); //E
          }
          elseif ($stones->brandId == 12) {
            $image_path = DIR_PROJECT_FOLDER . 'templates/frontend/images/basic_default.png';
            $this->Image($image_path, 20, 25);

            $this->setXY(49, 48);
            $this->Cell(10, 5, $A . 'mm'); //A

            $this->setXY(8, 30);
            $this->Cell(10, 5, $B . 'mm'); //B
          }

        }

      }
      else {
        // Bedrijfsnaam
        $this->setXY(5, 22);
        $this->SetFont('Arial', 'B', $fontSizeMainData);
        $this->Cell($cellWidthMainData1, $cellHeightMainData, 'Bedrijfsnaam');

        $this->setXY(37, 22);
        $this->SetFont('Arial', '', $fontSizeMainData);
        $this->Cell($cellWidthMainData2, $cellHeightMainData, $sandbox_user->companyName);

        // Contactpersoon
        if ($sandbox_user->personId > 0) {
          $person = CrmPersons::find_by(['personId' => $sandbox_user->personId]);
          $person_first_name = $person->firstName;
          $person_last_name = $person->lastName;
        }
        else {
          $person_first_name = '';
          $person_last_name = '';
        }

        // Contactpersoon:
        $this->setXY(5, 28);
        $this->SetFont('Arial', 'B', $fontSizeMainData);
        $this->Cell($cellWidthMainData1, $cellHeightMainData, 'Contactpersoon', 0);

        $this->setXY(37, 28);
        $this->SetFont('Arial', '', $fontSizeMainData);
        $this->Cell($cellWidthMainData2, $cellHeightMainData, $person_first_name . ' ' . $person_last_name);

        // Adres
        $address = $sandbox_user->street;
        if ($sandbox_user->nr !== '') {
          $address .= ' ' . $sandbox_user->nr;
        }
        if ($sandbox_user->extension !== '') {
          $address .= ' ' . $sandbox_user->extension;
        }
        $this->setXY(5, 34);
        $this->SetFont('Arial', 'B', $fontSizeMainData);
        $this->Cell($cellWidthMainData1, $cellHeightMainData, 'Adres', 0);

        $this->setXY(37, 34);
        $this->SetFont('Arial', '', $fontSizeMainData);
        $this->Cell($cellWidthMainData2, $cellHeightMainData, $address, 0);

        // Postcode
        $this->setXY(5, 40);
        $this->SetFont('Arial', 'B', $fontSizeMainData);
        $this->Cell($cellWidthMainData1, $cellHeightMainData, 'Postcode', 0);

        $this->setXY(37, 40);
        $this->SetFont('Arial', '', $fontSizeMainData);
        $this->Cell($cellWidthMainData2, $cellHeightMainData, $sandbox_user->zipcode, 0);

        // Plaats
        $this->setXY(5, 46);
        $this->SetFont('Arial', 'B', $fontSizeMainData);
        $this->Cell($cellWidthMainData1, $cellHeightMainData, 'Plaats', 0);

        $this->setXY(37, 46);
        $this->SetFont('Arial', '', $fontSizeMainData);
        $this->Cell($cellWidthMainData2, $cellHeightMainData, $sandbox_user->domestic, 0);

        // Telefoonnummer
        $this->setXY(5, 52);
        $this->SetFont('Arial', 'B', $fontSizeMainData);
        $this->Cell($cellWidthMainData1, $cellHeightMainData, 'Telefoonnummer', 0);

        $this->setXY(37, 52);
        $this->SetFont('Arial', '', $fontSizeMainData);
        $this->Cell($cellWidthMainData2, $cellHeightMainData, $sandbox_user->phone, 0);

        // Datum
        $this->setXY(5, 58);
        $this->SetFont('Arial', 'B', $fontSizeMainData);
        $this->Cell($cellWidthMainData1, $cellHeightMainData, 'Datum', 0);

        $this->setXY(37, 58);
        $this->SetFont('Arial', '', $fontSizeMainData);
        $this->Cell($cellWidthMainData2, $cellHeightMainData, DateTimeHelper::convertFormat($quotation->quotationDate, 'Y-m-d', 'd-m-Y'), 0);
      }

      if ($quotation->productionNotes != '') {

        // Opmerkingen
        $this->setXY(5, 72);
        $this->SetFont('Arial', 'B', $fontSizeMainData);
        $this->Cell($cellWidthMainData1, $cellHeightMainData, 'Opmerkingen', 0);

        $this->SetTextColor(255, 0, 0);

        $this->setXY(37, 72);
        $this->SetFont('Arial', '', $fontSizeMainData);
        $this->Multicell(170, $cellHeightMainData, $quotation->productionNotes, 0);

      }

      $this->SetTextColor(0, 0, 0);

      $this->SetLeftMargin(105);
      $this->setXY(105, 22);
      $this->SetFont('Arial', '', $fontSizeMainData);
      $cellWidthMainDataRight = 29;
      $cellHeightMainDataRight = 6;

      // Projectnaam
      $this->SetFont('Arial', 'B');
      $this->Cell($cellWidthMainDataRight, $cellHeightMainDataRight, 'Projectnaam', 0);
      $this->SetFont('Arial', '');
      $this->Cell($cellWidthMainDataRight, $cellHeightMainDataRight, $quotation->projectName, 0);
      $this->Ln();

      // Kenmerk
      $this->SetFont('Arial', 'B');
      $this->Cell($cellWidthMainDataRight, $cellHeightMainDataRight, 'Kenmerk', 0);
      $this->SetFont('Arial', '');
      $this->Cell($cellWidthMainDataRight, $cellHeightMainDataRight, $quotation->projectReference, 0);
      $this->Ln();

      // Merk
//      require_once($_SERVER['DOCUMENT_ROOT'] . '/cms/crm/classes/clsStones.php');
//      $oStones = new clsStones();
//      $oStones->quotationId = $this->quoteId;
//      $stoneDescription = $oStones->getStoneDescription();

      $this->SetFont('Arial', 'B');
      $this->Cell($cellWidthMainDataRight, $cellHeightMainDataRight, 'Merk', 0);
      $this->SetFont('Arial', '');
      $this->Cell($cellWidthMainDataRight, $cellHeightMainDataRight, $stone_brand->name, 0);
      $this->Ln();

      // Kleur
      if ($stone_brand->stoneTypeId == 2 || $stone_brand->stoneTypeId == 3) {
        $color = $stone_color->short . ' ' . $stone_color->name;
      }
      else {
        if ($stone_color->glaced == 'true') {
          $color = $stone_color->short . ' ' . $stone_color->name . ' geglazuurd';
        }
        else {
          $color = $stone_color->short . ' ' . $stone_color->name . ' ongeglazuurd';
        }
      }
      if ($quotation->ralColor) {
        $color = $quotation->ralColor;
      }

      $label = $quotation->ralColor ? 'RAL-kleur' : 'Kleur';
      $this->SetFont('Arial', 'B');
      $this->Cell($cellWidthMainDataRight, $cellHeightMainDataRight, $label, 0);
      $this->SetFont('Arial', '');
      $this->Cell($cellWidthMainDataRight, $cellHeightMainDataRight, trim($color), 0);
      $this->Ln();

      // Model
//      $oStones->sizeId = $stoneDescription->sizeId;
      $model_name = $stone_size->short;
      if ($stones->type == "balkjes") {
        $model_name = "Divers";
      }

      $stone_end = $quotation->getEndstoneName();

      $this->SetFont('Arial', 'B');
      $this->Cell($cellWidthMainDataRight, $cellHeightMainDataRight, 'Model', 0);
      $this->SetFont('Arial', '');
      $this->Cell($cellWidthMainDataRight, $cellHeightMainDataRight, $model_name, 0);
      $this->Ln();

      if (!in_array($stone_brand->stoneTypeId, [2, 3])) {
        // Eindstenen
        $this->SetFont('Arial', 'B');
        $this->Cell($cellWidthMainDataRight, $cellHeightMainDataRight, 'Eindstenen', 0);
        $this->SetFont('Arial', '');
        $this->Cell($cellWidthMainDataRight, $cellHeightMainDataRight, $stone_end, 0);
        $this->Ln();
      }

      // Aantal meter
      $this->SetFont('Arial', 'B');
      $this->Cell($cellWidthMainDataRight, $cellHeightMainDataRight, 'Aantal meter', 0);
      $this->SetFont('Arial', '');
      $this->Cell($cellWidthMainDataRight, $cellHeightMainDataRight, $quotation->meters, 0);
      $this->Ln();

      $total_element_count = count($order_elements);

      if ($quotation->stoneCategoryId == 13) { //natuursteen_vensterbank
        $this->SetFont('Arial', 'B');
        $this->Cell($cellWidthMainDataRight, $cellHeightMainDataRight, 'Oppervlakte m2', 0);
        $this->SetFont('Arial', '');
        $opp = 0;

        for ($i = 0; $i < $total_element_count; $i++) {
          $windowsill = OrderElementWindowsill::find_by(['element_id' => $order_elements[$i]->elementId]);
          $opp += ($windowsill->x1 * $windowsill->x2) / 1000000 * $order_elements[$i]->amount;
        }

        $this->Cell($cellWidthMainDataRight, $cellHeightMainDataRight, (string)round($opp, 2), 0);
        $this->Ln();
      }

      // Aantal delingen:
      //-- always subtract 3 voor de last 3

      if ($order_elements[0]->heartClickSize == 1) {
        $heartClickSizeText = 'tot aan de punt';
      }
      else {
        $heartClickSizeText = 'hart klik gemeten';
      }

      $aCount4 = 0;
      $aForm['elements4'][$aCount4]['label'] = 'type gekozen maat';
      $aForm['elements4'][$aCount4]['span'] = ['id' => 'spanAdmin', 'value' => $heartClickSizeText, 'class' => 'divTextFC'];

      $aCount5 = 0;
      $aantalHoekjes = '';
      for ($i = 0; $i < $total_element_count; $i++) {
        // Totaal aantal meter hoger dan 100 meter?
        // Alle elementen
        if ($quotation->meters >= 100) {
          $element = intval($order_elements[$i]->elementLength);
          $aantal = intval($order_elements[$i]->amount);
          $aantalHoekjes = intval($aantalHoekjes);
          $aantalHoekjes += $aantal * floor((ceil($element / 2800) / 2));
        }
        else {
          $element = intval($order_elements[$i]->elementLength);
          $aantal = intval($order_elements[$i]->amount);
          $aantalHoekjes = intval($aantalHoekjes);
          $aantalHoekjes += $aantal * floor((ceil($element / 2300) / 2));
        }
      }

      // Aantal delingen:
      if (!in_array($stone_brand->stoneTypeId, [2, 3])) {
        $this->SetFont('Arial', 'B');
        $this->Cell($cellWidthMainDataRight, $cellHeightMainDataRight, 'Aantal delingen', 0);
        $this->SetFont('Arial', '');
        $this->Cell($cellWidthMainDataRight, $cellHeightMainDataRight, $aantalHoekjes, 0);
        $this->Ln();
      }

      $this->SetLeftMargin(10);

      if ($quotation->productionNotes !== '') {
        $this->setXY(5, 90);
      }
      else {
        $this->setXY(5, 74);
      }

      // Order items
      // $cellWidthArray = cwa

      $this->SetLineWidth(0.4);
      $this->SetDrawColor(0, 255, 0);
      $this->SetFillColor(0, 0, 0);
      $this->SetTextColor(255, 255, 255);

      $arrayElementList = $this->showElementList($quotation->quotationId);
      $this->SetFont('Arial', '', 10);
      if ($stones->type == Stones::TYPE_BALKJES) {
        $this->writeElementsBalkje($this, $arrayElementList);
        $this->writeImages($this, $arrayElementList);
      }
      elseif ($quotation->stoneCategoryId == 13) { //natuursteen_vensterbank
        $this->vensterbankenTable($this, $arrayElementList);
        $this->includeVensterbankDrawings($this, $quotation);
      }
      elseif ($stone_brand->stoneTypeId == 2 || $stone_brand->stoneTypeId == 3) {
        $this->notKeramicTable($this, $arrayElementList);
        $this->writeImages($this, $arrayElementList);
      }
      else {
        $this->keramicTable($this, $arrayElementList);
        $this->writeImages($this, $arrayElementList);
      }

    }

    public function footer() {

    }

    public function generate() {

      $this->build();

      $filename = 'productiestaat_' . $this->quotation->quotationId .'.pdf';

      if ($this->isMultiple()) {
        $this->Output("F", DIR_TEMP . $filename);

      }
      else {
        $this->Output("I", $filename);
      }

      return $filename;

    }

    /**
     * @param GSDPDF $pdf
     * @param OrderElements[] $elements
     * @return int|mixed
     */
    private function writeElementsBalkje($pdf, $elements) {
      $aWidth = [30, 25, 25, 25, 25, 35];
      $pdf->SetFont('Arial', 'B', 10);
      $pdf->SetDrawColor(255, 255, 255);
      $pdf->Ln();
      $pdf->Cell($aWidth[0], 5, 'Kenmerk', 'B', 0, 'L', true);
      $pdf->Cell($aWidth[1], 5, 'Aantal', 'B', 0, 'L', true);
      $pdf->Cell($aWidth[2], 5, 'Lengte (mm)', 'B', 0, 'R', true);
      $pdf->Cell($aWidth[3], 5, 'Breedte (mm)', 'B', 0, 'R', true);
      $pdf->Cell($aWidth[4], 5, 'Hoogte (mm)', 'B', 0, 'R', true);
      $pdf->Cell($aWidth[5], 5, "Verstek", 'B', 0, 'L', true);
      $pdf->Ln();

      $pdf->SetFont('Arial', '', 10);
      $pdf->SetTextColor(0, 0, 0);

      $element_count = 0;
      foreach ($elements as $element) {

        if (!is_array($element)) continue;

        $order_element_sizes = OrderElementSize::find_all_by(['element_id' => $element['elementId']]);

        $sizes = [];
        foreach ($order_element_sizes as $size) {
          $sizes[$size->code] = $size->value;
        }

        $miteLabel = '';
        if ($element["sMitre"] == "Beide") {
          $miteLabel = 'Beide';
        }
        elseif ($element["sMitre"] == "Links") {
          $miteLabel = 'Links';
        }
        elseif ($element["sMitre"] == "Rechts") {
          $miteLabel = 'Rechts';
        }

        $pdf->Cell($aWidth[0], 5, $element['referenceName'], 0, 0, 'L', false);
        $pdf->Cell($aWidth[1], 5, $element['aantal'], 0, 0, 'L', false);
        $pdf->Cell($aWidth[2], 5, $element['inputLength'], 0, 0, 'R', false);
        $pdf->Cell($aWidth[3], 5, $sizes['width'], 0, 0, 'R', false);
        $pdf->Cell($aWidth[4], 5, $sizes['height'], 0, 0, 'R', false);
        $pdf->Cell($aWidth[5], 5, $miteLabel, 0, 0, 'L', false);
        $pdf->Ln();
        $element_count += $element['aantal'];

      }

      $pdf->SetLineWidth(0.4);
      $pdf->SetDrawColor(208, 208, 208);

      // function Cell($w, $h=0, $txt='', $border=0, $ln=0, $align='', $fill=false, $link='')
      $pdf->Cell($aWidth[0], 5, 'Totaal', 'T', 0);
      $pdf->Cell($aWidth[1], 5, $element_count . '', 'T', 0, 'L', false);
      $pdf->Cell($aWidth[2], 5, '', 'T', 0, 'C', false);
      $pdf->Cell($aWidth[3], 5, '', 'T', 0, 'C', false);
      $pdf->Cell($aWidth[4], 5, '', 'T', 0, 'C', false);
      $pdf->Cell($aWidth[5], 5, '', 'T', 0, 'C', false);

      return $element_count;
    }

    /**
     * @param GSDPDF $pdf
     * @param OrderElements[] $arrayElementList
     * @return void
     */
    private function writeImages($pdf, $arrayElementList) {
//dumpe($arrayElementList);
      // $widthCellImages = 150;
      $widthCellImages = 300;

      $pdf->SetLineWidth(0.3);
      $pdf->SetDrawColor(255, 0, 0);

      //-- black
      $pdf->SetDrawColor(0, 0, 0);

      // Ln zorgt ervoor dat de volgende image op de volgende pagina komt als deze overlapt
      $pdf->Ln();

      //-- een lege cell die iets meer ruimte creert voor de eerste element.
      $pdf->Cell($widthCellImages, 10, '', 0, 0, 'L', false);
      $pdf->Ln();

      /*
       * deze functie kan ik misschien gebruiken in de toekomst als ik moet weten hoeveel plaatjes er zk
       */
      $countImages = 0;
      foreach ($arrayElementList as $key => $value) {
        if (isset($value['elementId'])) {
          $imageLocation = DIR_ROOT_HTTPDOCS . 'filesystem/raamdorpel/clients/quotation/elementparts/' . $this->quotation->quotationId . '/' . $value['elementId'] . '.png';
          if (file_exists($imageLocation)) {
            $countImages++;
          }

        }

      }

      $count = 0;
      foreach ($arrayElementList as $key => $value) {
        $getYPosition = $pdf->getY();
        if ($count == $countImages) {
          if ($getYPosition > 270) {
            //-- add line so we go to next page.
            $pdf->addPage();
            $pdf->setY(30);
            $pdf->SetLeftMargin(10);
            $pdf->SetTopMargin(0);

          }
        }
        else {
          if ($getYPosition > 250) {
            //-- add line so we go to next page.
            $pdf->addPage();
            $pdf->setY(30);
            $pdf->SetLeftMargin(10);
            $pdf->SetTopMargin(0);
          }
          $imageLocation = DIR_ROOT_HTTPDOCS . 'filesystem/raamdorpel/clients/quotation/elementparts/' . $this->quotation->quotationId . '/' . $value['elementId'] . '.png';
          if (file_exists($imageLocation)) {
            $count++;
            $pdf->Image($imageLocation, $pdf->GetX(), $pdf->GetY());
            $pdf->Ln(35);
          }
        }
      }
    }

    private function keramicTable($pdf, $arrayElementList) {
//dumpe($arrayElementList);
      $cwa = 20;
      $aWidth = [50, $cwa, 15, 15, $cwa, $cwa, $cwa, $cwa, $cwa];

      $pdf->Cell($aWidth[0], 4.5, 'Merk', 0, 0, 'L', true);
      $pdf->Cell($aWidth[1], 4.5, 'Aantal', 0, 0, 'L', true);
      $pdf->Cell($aWidth[2], 4.5, 'Verstek', 0, 0, 'L', true);
      $pdf->Cell($aWidth[3], 4.5, 'Vlag', 0, 0, 'L', true);
      $pdf->Cell($aWidth[4], 4.5, 'Element', 0, 0, 'L', true);
      $pdf->Cell($aWidth[5], 4.5, 'Stenen', 0, 0, 'L', true);
      $pdf->Cell($aWidth[6], 4.5, 'Zaagwerk', 0, 0, 'L', true);
      $pdf->Cell($aWidth[7], 4.5, 'Verdeling', 0, 0, 'L', true);
      $pdf->Cell($aWidth[8], 4.5, 'Steentotaal', 0, 1, 'L', true);

      $pdf->setX(5);
      $pdf->SetFont('Arial', '', 10);

      $totaalaantal = 0;
      $totaalsteen = 0;

      $pdf->SetTextColor(0, 0, 0);

      for ($i = 0; $i < (count($arrayElementList)) - 3; $i++) {

        $pdf->Cell($aWidth[0], 5, $arrayElementList[$i]['referenceName'], 0, 0, 'L', false);
        $pdf->Cell($aWidth[1], 5, $arrayElementList[$i]['aantal'] . '', 0, 0, 'L', false);

        if ($arrayElementList[$i]['sMitre'] == 'Geen') {
          $sMitre = '';
        }
        else {
          $sMitre = $arrayElementList[$i]['sMitre'];
        }

        $pdf->Cell($aWidth[2], 4, $sMitre, 0, 0, 'L', false);

        if ($arrayElementList[$i]['sFlagwindow'] == 'Geen') {
          $sFlagwindow = '';
        }
        else {
          $sFlagwindow = $arrayElementList[$i]['sFlagwindow'];
        }

        $pdf->Cell($aWidth[3], 4, $sFlagwindow, 0, 0, 'L', false);
        $pdf->Cell($aWidth[4], 5, $arrayElementList[$i]['element'] . '', 0, 0, 'L', false);
        $pdf->Cell($aWidth[5], 5, $arrayElementList[$i]['stenen'] . '', 0, 0, 'L', false);
        $pdf->Cell($aWidth[6], 5, $arrayElementList[$i]['zaagmaat'] . '', 0, 0, 'L', false);
        $pdf->Cell($aWidth[7], 5, $arrayElementList[$i]['verdeling'] . '', 0, 0, 'L', false);
        $pdf->Cell($aWidth[8], 5, $arrayElementList[$i]['steentotaal'] . '', 0, 1, 'L', false);

        $pdf->setX(5);

        $totaalaantal += $arrayElementList[$i]['aantal'];
        $totaalsteen += $arrayElementList[$i]['steentotaal'];

      }

      $pdf->SetLineWidth(0.4);
      $pdf->SetDrawColor(208, 208, 208);

      // function Cell($w, $h=0, $txt='', $border=0, $ln=0, $align='', $fill=false, $link='')
      $pdf->Cell($aWidth[0], 5, 'Totaal', 'T', 0);
      $pdf->Cell($aWidth[1], 5, $totaalaantal . '', 'T', 0, 'L', false);
      $pdf->Cell($aWidth[2], 5, '', 'T', 0, 'C', false);
      $pdf->Cell($aWidth[3], 5, '', 'T', 0, 'C', false);
      $pdf->Cell($aWidth[4], 5, '', 'T', 0, 'C', false);

      $pdf->Cell($aWidth[5], 5, '', 'T', 0, 'C', false);
      $pdf->Cell($aWidth[6], 5, '', 'T', 0, 'C', false);
      $pdf->Cell($aWidth[7], 5, '', 'T', 0, 'C', false);
      $pdf->Cell($aWidth[8], 5, $totaalsteen . '', 'T', 0, 'L', false);
    }

    private function vensterbankenTable($pdf, $arrayElementList) {

      $aWidth = [50, 20, 30, 30, 60];

      $pdf->Cell($aWidth[0], 4.5, 'Kenmerk', 0, 0, 'L', true);
      $pdf->Cell($aWidth[1], 4.5, 'Aantal', 0, 0, 'L', true);
      $pdf->Cell($aWidth[2], 4.5, 'Lengte (mm)', 0, 0, 'R', true);
      $pdf->Cell($aWidth[3], 4.5, 'Breedte (mm)   ', 0, 0, 'R', true);
      $pdf->Cell($aWidth[4], 4.5, 'Model', 0, 0, 'L', true);
      $pdf->Ln();

      $pdf->setX(5);
      $pdf->SetFont('Arial', '', 10);

      $totaalaantal = 0;
      $totaalsteen = 0;

      $pdf->SetTextColor(0, 0, 0);

      for ($i = 0; $i < (count($arrayElementList) - 3); $i++) {

        $windowsill = OrderElementWindowsill::find_by(['element_id' => $arrayElementList[$i]["elementId"]]);
        $windowsill_template = Windowsill::find_by(['id' => $windowsill->windowsill_id]);

        $pdf->Cell($aWidth[0], 5, strtoupper($arrayElementList[$i]['referenceName']), 0, 0, 'L', false);
        $pdf->Cell($aWidth[1], 5, $arrayElementList[$i]['aantal'] . '', 0, 0, 'L', false);
        $pdf->Cell($aWidth[2], 5, number_format($windowsill->x1, 0, "", ""), 0, 0, 'R', false);
        $pdf->Cell($aWidth[3], 5, number_format($windowsill->x2, 0, "", "") . '   ', 0, 0, 'R', false);
        $pdf->Cell($aWidth[4], 5, $windowsill_template->name, 0, 0, 'L', false);

        $pdf->Ln();

        $pdf->setX(5);

        $totaalaantal += $arrayElementList[$i]['aantal'];
        $totaalsteen += $arrayElementList[$i]['steentotaal'];

      }

      $pdf->SetLineWidth(0.4);
      $pdf->SetDrawColor(208, 208, 208);

      // function Cell($w, $h=0, $txt='', $border=0, $ln=0, $align='', $fill=false, $link='')
      $pdf->Cell($aWidth[0], 5, 'Totaal', 'T', 0);
      $pdf->Cell($aWidth[1], 5, $totaalaantal . '', 'T', 0, 'L', false);
      $pdf->Cell($aWidth[2], 5, '', 'T', 0, 'C', false);
      $pdf->Cell($aWidth[3], 5, '', 'T', 0, 'C', false);
      $pdf->Cell($aWidth[4], 5, '', 'T', 0, 'C', false);
    }

    private function notKeramicTable($pdf, $arrayElementList) {

      $cwa = 20;
      $aWidth = [50, $cwa, 15, 15, $cwa, $cwa, $cwa, $cwa, $cwa];

      $pdf->Cell($aWidth[0], 4.5, 'Merk', 0, 0, 'L', true);
      $pdf->Cell($aWidth[1], 4.5, 'Aantal', 0, 0, 'L', true);
      $pdf->Cell($aWidth[2], 4.5, 'Verstek', 0, 0, 'L', true);
      $pdf->Cell($aWidth[3], 4.5, 'Vlag', 0, 0, 'L', true);
      $pdf->Cell($aWidth[4], 4.5, 'Element', 0, 0, 'L', true);
      $pdf->Cell($aWidth[5], 4.5, '', 0, 0, 'L', true);
      $pdf->Cell($aWidth[6], 4.5, '', 0, 0, 'L', true);
      $pdf->Cell($aWidth[7], 4.5, '', 0, 0, 'L', true);
      $pdf->Cell($aWidth[8], 4.5, '', 0, 1, 'L', true);

      $pdf->setX(5);
      $pdf->SetFont('Arial', '', 10);

      $totaalaantal = 0;
      $totaalsteen = 0;

      $pdf->SetTextColor(0, 0, 0);

      for ($i = 0; $i < (count($arrayElementList) - 3); $i++) {

        $pdf->Cell($aWidth[0], 5, strtoupper($arrayElementList[$i]['referenceName']), 0, 0, 'L', false);
        $pdf->Cell($aWidth[1], 5, $arrayElementList[$i]['aantal'] . '', 0, 0, 'L', false);

        if ($arrayElementList[$i]['sMitre'] == 'Geen') {
          $sMitre = '';
        }
        else {
          $sMitre = $arrayElementList[$i]['sMitre'];
        }

        $pdf->Cell($aWidth[2], 4, $sMitre, 0, 0, 'L', false);

        if ($arrayElementList[$i]['sFlagwindow'] == 'Geen') {
          $sFlagwindow = '';
        }
        else {
          $sFlagwindow = $arrayElementList[$i]['sFlagwindow'];
        }

        $pdf->Cell($aWidth[3], 4, $sFlagwindow, 0, 0, 'L', false);

        $order_element_parts = OrderElementparts::find_by(['elementId' => $arrayElementList[$i]['elementId']]);

        //-- als het keramisch of beton is dan kijken naar de heartClickSize (hartklikmaat)
        if ($arrayElementList[$i]['heartClickSize'] == 0 || $order_element_parts) {
          $pdf->Cell($aWidth[4], 5, $arrayElementList[$i]['dbElementLength'] . '', 0, 0, 'L', false);
        }
        else {
          $pdf->Cell($aWidth[4], 5, $arrayElementList[$i]['element'] . '', 0, 0, 'L', false);
        }

        $pdf->Cell($aWidth[5], 5, '', 0, 0, 'L', false);
        $pdf->Cell($aWidth[6], 5, '', 0, 0, 'L', false);
        $pdf->Cell($aWidth[7], 5, '', 0, 0, 'L', false);
        $pdf->Cell($aWidth[8], 5, '', 0, 1, 'L', false);

        $pdf->setX(5);

        $totaalaantal += $arrayElementList[$i]['aantal'];
        $totaalsteen += $arrayElementList[$i]['steentotaal'];

      }

      $pdf->SetLineWidth(0.4);
      $pdf->SetDrawColor(208, 208, 208);

      // function Cell($w, $h=0, $txt='', $border=0, $ln=0, $align='', $fill=false, $link='')
      $pdf->Cell($aWidth[0], 5, 'Totaal', 'T', 0);
      $pdf->Cell($aWidth[1], 5, $totaalaantal . '', 'T', 0, 'L', false);
      $pdf->Cell($aWidth[2], 5, '', 'T', 0, 'C', false);
      $pdf->Cell($aWidth[3], 5, '', 'T', 0, 'C', false);
      $pdf->Cell($aWidth[4], 5, '', 'T', 0, 'C', false);
      $pdf->Cell($aWidth[5], 5, '', 'T', 0, 'C', false);
      $pdf->Cell($aWidth[6], 5, '', 'T', 0, 'C', false);
      $pdf->Cell($aWidth[7], 5, '', 'T', 0, 'C', false);
      $pdf->Cell($aWidth[8], 5, '', 'T', 0, 'L', false);
    }

    private function includeVensterbankDrawings($pdf, $oQuotationData) {

      $quotationPdf = new QuotationPdf($oQuotationData->quotationId);
      $quotationPdf->setVariant('vensterbanken');
      $filename = $quotationPdf->generatePdf();

      $filepath = DIR_TEMP . $filename;

      global $tplidx;
      $pagecount = $pdf->setSourceFile($filepath);
      for ($tel = 1; $tel <= $pagecount; $tel++) {
        $tplidx = $pdf->importPage($tel);
        $pdf->addPage();
        $pdf->useTemplate($tplidx, 0, 0);
      }

    }


    private function showElementList($quotationId) {
      // Generate element drawings
      $drawElements = new DrawElements();
      $drawElements->setSeperateFiles(true);
      $drawElements->generate($quotationId, false);

      $query = 'SELECT
                            E.`elementId`,
                            E.`referenceName`,
                            E.`amount`,
                            E.`stoneAmount`,
                            E.`inputLength`,
                            E.`totalPrice`,
                            E.`flagWindow`,
                            E.`leftEndstone`,
                            E.`rightEndstone`,
                            E.`leftEndStoneGrooves`,
                            E.`rightEndStoneGrooves`,
                            E.`flagWindowSide`,
                            E.`divisionMeasure`,
                            E.`fitStoneLength`,
                            E.`elementLengthTotal`,
                            E.elementLength AS dbElementLength,
                            E.heartClickSize,
                            L.heartLength AS heartLengthLeft,
                            R.heartLength AS heartLengthRight,
                            IF(E.`leftMitreId` IS NULL, 
                                    0,
                                    E.`leftMitreId`
                            ) AS `leftMitreId`,

                            IF(E.`rightMitreId` IS NULL,
                                    0,
                                    E.`rightMitreId`
                            ) AS `rightMitreId`,

                            CEILING(
                                    IF(E.`leftMitreId` IS NOT NULL,
                                            IF(L.`shortLength` > L.`longLength`,
                                                    L.`shortLength`,
                                                    L.`longLength`
                                            ),
                                            0
                                    ) 
                                    + 
                                    E.`elementLength`
                                    +
                                    IF(E.`rightMitreId` IS NOT NULL,
                                            IF(R.`shortLength` > R.`longLength`,
                                                    R.`shortLength`,
                                                    R.`longLength`
                                            ),
                                            0
                                    ) 							
                            ) AS `elementLength`								
                    FROM
                            ' . OrderElements::getTablename() . ' E
                    LEFT JOIN
                            ' . Mitres::getTablename() . ' L
                    ON
                            E.`leftMitreId` = L.`mitreId`
                    LEFT JOIN
                            ' . Mitres::getTablename() . ' R
                    ON
                            E.`rightMitreId` = R.`mitreId`
                    WHERE
                            E.`quotationId` = ' . $quotationId . '
                    ORDER BY 
                            E.elementId ASC';

      $result = DBConn::db_link()->query($query);

      $totalPrice2 = 0;
      $totaalsteen = 0;
      $aDataElementList = [];
      $counter = 0;
      $totaalaantal = 0;
      $totaalElementLength = 0;

      //-- quotation_extra.stoneSizeWidth ophalen
      //-- soms wijkt deze maat af van de standaard.
      $oQuoteExtra = QuotationsExtra::find_all_by(['quotationId' => $quotationId]);

      if (empty($oQuoteExtra)) {
        echo '<p>Er is geen ' . $quotationId . ' quotationExtra gekoppeld. Dus berekening hieronder klopt niet. 2</p>';
      }

      if (count($oQuoteExtra) > 0) {
        $oQuoteExtra = $oQuoteExtra[0];
        $stoneSizeWidthQuoteExtra = $oQuoteExtra->stoneSizeWidth;
        $maxMeasureElement = $oQuoteExtra->maxMeasureElement;
      }
      else {
        $stoneSizeWidthQuoteExtra = 0;
        $maxMeasureElement = '';
      }

      $stoneSizeWidthQuoteExtraPlus10 = $stoneSizeWidthQuoteExtra * 10;


      while ($row = $result->fetch_array()) {

        $iStones = 0;

        // Count stones for constrution material customers
        $iStones = $iStones + ($row['amount'] * $row['stoneAmount']);

        switch ($row['flagWindow']) {
          case 'false':
            $sFlagwindow = 'Geen';
            break;
          case 'single':
            $sFlagwindow = 'Enkel';
            break;
          case 'double':
            $sFlagwindow = 'Dubbel';
            break;
        }

        //----------------------------------------------------------
        $aantal = $row['amount'];
        $totalPrice = $row['totalPrice'];
        $element = $row['elementLength'];

        if ($row['leftMitreId'] > 0 && $row['rightMitreId'] > 0) {
          $sMitre = 'Beide';
          $element = $element + 7;
        }
        elseif ($row['leftMitreId'] > 0) {
          $sMitre = 'Links';
          $element = floor($element + 3.5);
        }
        elseif ($row['rightMitreId'] > 0) {
          $sMitre = 'Rechts';
          $element = floor($element + 3.5);
        }
        else {
          $sMitre = 'Geen';
        }

        $elementDB = $row['dbElementLength'];
        $kozijn = $row['inputLength'];
        //-----------------------------------------------------------
        $leftEndstone = $row['leftEndstone'];
        $rightEndstone = $row['rightEndstone'];
        $leftEndStoneGrooves = $row['leftEndStoneGrooves'];
        $rightEndStoneGrooves = $row['rightEndStoneGrooves'];
        //-----------------------------------------------------------

        $verdeling = $stoneSizeWidthQuoteExtraPlus10 + 3.5;  // WORDT MOGELIJK AANGEPAST DOOR NIEUWE FORMULE
        $steentotaal = $iStones;
        $iElementVoeg = $elementDB + 3.5;
        $verdelingMin = $stoneSizeWidthQuoteExtraPlus10 + 3;
        $verdelingMax = $stoneSizeWidthQuoteExtraPlus10 + 4.5; // LET OP! Voeg erbij!

        // LET OP! Voeg erbij!
        $iStenenMin = ceil($iElementVoeg / $verdelingMin);
        $iStenenStd = ceil($iElementVoeg / $verdeling);
        $iStenenMax = ceil($iElementVoeg / $verdelingMax);

        if (($iStenenMin - $iStenenMax) >= 1) {
          // Geen zaagwerk, minder stenen, nieuwe verdeling
          $stenen = $iStenenMax;
          $zaagmaat = 0;
          $verdeling = number_format($elementDB / $iStenenMax, 1, '.', '');      // LET OP! Element zonder voeg!
          $steentotaal = $row['amount'] * $iStenenMax;
        }
        else {
          // Zaagwerk aanpassen
          $stenen = $iStenenStd;
          $zaagmaat = $stoneSizeWidthQuoteExtraPlus10 - (((($stenen * $verdeling) - 3.5) - $elementDB) / 2);
          // round to 0 decimals
          $zaagmaat = number_format($zaagmaat, 0, '', '');
        }

        $prijs = number_format($totalPrice, 2, ",", ".");

        $referenceName = $row['referenceName'];
        $heartClickSize = $row['heartClickSize'];
        $heartLengthLeft = $row['heartLengthLeft'];
        $heartLengthRight = $row['heartLengthRight'];

        if ($aantal != 0) {

          $aDataElementList[$counter]['elementLengthTotal'] = $row['elementLengthTotal'];
          $aDataElementList[$counter]['divisionMeasure'] = $row['divisionMeasure'];
          $aDataElementList[$counter]['fitStoneLength'] = $row['fitStoneLength'];
          $aDataElementList[$counter]['stoneSizeWidthQuoteExtraPlus10'] = $stoneSizeWidthQuoteExtraPlus10;
          $aDataElementList[$counter]['maxMeasureElement'] = $maxMeasureElement;
          $aDataElementList[$counter]['inputLength'] = $row['inputLength'];
          $aDataElementList[$counter]['dbElementLength'] = $row['dbElementLength'];

          $aDataElementList[$counter]['elementId'] = $row['elementId'];
          $aDataElementList[$counter]['leftEndstone'] = $leftEndstone;
          $aDataElementList[$counter]['rightEndstone'] = $rightEndstone;
          $aDataElementList[$counter]['leftEndStoneGrooves'] = $leftEndStoneGrooves;
          $aDataElementList[$counter]['rightEndStoneGrooves'] = $rightEndStoneGrooves;
          $aDataElementList[$counter]['referenceName'] = $referenceName;
          $aDataElementList[$counter]['aantal'] = $aantal;
          $aDataElementList[$counter]['kozijn'] = $kozijn;
          $aDataElementList[$counter]['sMitre'] = $sMitre;
          $aDataElementList[$counter]['leftMitreId'] = $row['leftMitreId'];
          $aDataElementList[$counter]['rightMitreId'] = $row['rightMitreId'];
          $aDataElementList[$counter]['sFlagwindow'] = $sFlagwindow;
          $aDataElementList[$counter]['flagWindowSide'] = $row['flagWindowSide'];

          //-- vanaf hier moet ik kijken wat voor brandId dat het heeft.
          if ($this->stone_brands && ($this->stone_brands->stoneTypeId == 2 || $this->stone_brands->stoneTypeId == 3)) {
            $aDataElementList[$counter]['element'] = $row['elementLengthTotal'];
          }
          else {
            $aDataElementList[$counter]['element'] = $element;
          }

          $totaalElementLength += $aDataElementList[$counter]['element'];

          $aDataElementList[$counter]['stenen'] = $stenen;
          $aDataElementList[$counter]['zaagmaat'] = $zaagmaat;
          $aDataElementList[$counter]['steentotaal'] = $steentotaal;
          $aDataElementList[$counter]['prijs'] = $prijs;
          $aDataElementList[$counter]['heartLengthLeft'] = $heartLengthLeft;
          $aDataElementList[$counter]['heartLengthRight'] = $heartLengthRight;
          $aDataElementList[$counter]['heartClickSize'] = $heartClickSize;
          $aDataElementList[$counter]['verdeling'] = $verdeling;

        }

        $totaalaantal += $aantal;
        $totaalsteen += $steentotaal;
        $totalPrice2 += $totalPrice;
        $counter++;

      }

      $aDataElementList['totaalaantal'] = $totaalaantal;
      $aDataElementList['steentotaal'] = $totaalsteen;
      $aDataElementList['totalPrice'] = $totalPrice2;

      return $aDataElementList;
    }

  }