<?php

  AppModel::loadBaseClass('BaseSpareparts');

  class SparepartsModel extends BaseSpareparts {

    public static function getValues($date = false) {
      if ($date === false) {
        $date = date("Y-m-d");
      }
      $query = "SELECT global, value FROM " . Spareparts::getTablename() . " ";
      $query .= "JOIN " . SparepartsPrices::getTablename() . " ON spareparts_prices.partId = spareparts.partId ";
      $query .= "WHERE (validFrom <= '" . $date . "' AND validTo >= '" . $date . "') ";

      $result = DBConn::db_link()->query($query);

      $vals = [];
      while ($row = $result->fetch_assoc()) {
        $vals[$row["global"]] = $row["value"];
      }
      return $vals;
    }

  }