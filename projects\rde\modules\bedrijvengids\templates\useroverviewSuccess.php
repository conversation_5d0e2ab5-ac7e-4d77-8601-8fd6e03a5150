<?php
  TemplateHelper::includePartial('_tabs.php', 'sandbox');
  include("_header.php")
?>

<form>
  <a href="?action=personedit&companyid=<?php echo $company->companyId ?>" class="gsd-btn gsd-btn-primary" style="margin-bottom: 17px">Nieuw persoon toevoegen</a>
</form>


<?php foreach ($personsGrouped as $key=>$personGroup): ?>
  <h2><?php
    if($key=="old"):
      echo "Contactpersonen OUD";
    elseif($key=="executors"):
      echo "Uitvoerders";
    else:
      echo "Contactpersonen";
    endif;
    ?></h2>
  <?php if(empty($personGroup)): ?>
    Geen personen gevonden.
  <?php else: ?>
    <table class="default_table">
      <tr class="dataTableHeadingRow">
        <td>Naam</td>
        <td>Afdeling</td>
        <td>Functie</td>
        <td>E-mail</td>
        <td>Telefoon</td>
        <td>Mobiel</td>
        <td>Acties</td>
      </tr>
      <?php foreach ($personGroup as $person): ?>
        <?php if ($person->flagForDeletion == 1) continue; ?>
        <tr class="dataTableRow trhover">
          <td><?php echo $person->firstName . ' ' . $person->lastName ?></td>
          <td><?php echo $person->department ?></td>
          <td><?php echo $person->jobtitle ?></td>
          <td><a href="mailto:<?php echo $person->email ?>"><?php echo $person->email ?></a></td>
          <td><?php echo BtnHelper::getPhone($person->phone) ?></td>
          <td><?php echo BtnHelper::getPhone($person->mobile) ?></td>
          <td>
            <?php echo BtnHelper::getEdit(reconstructQueryAdd(['pageId']) . 'action=personedit&personid=' . $person->personId, __('Bewerk persoon')) ?>
            <?php echo BtnHelper::getRemove(reconstructQueryAdd(['pageId']) . 'action=persondelete&personid=' . $person->personId, __('Verwijder persoon')) ?>
          </td>
        </tr>
      <?php endforeach; ?>
    </table>
  <?php endif; ?>
<?php endforeach;