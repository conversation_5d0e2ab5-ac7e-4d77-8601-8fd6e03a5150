<template>
  <Popper v-bind="$attrs" hover disableClickAway arrow>
    <slot/>
    <template #content="props">
      <slot name="content" v-bind="props"/>
    </template>
  </Popper>
</template>

<script>
import {defineComponent} from "vue";
import Popper from "vue3-popper";

export default defineComponent({
  name: "GsdPopper",
  components: {
    Popper,
  },

});
</script>

<style>

:root {
  --popper-theme-background-color: white;
  --popper-theme-background-color-hover: white;
  --popper-theme-text-color: black;
  --popper-theme-border-width: 0px;
  --popper-theme-border-style: solid;
  --popper-theme-border-radius: 6px;
  --popper-theme-padding: 32px;
  --popper-theme-box-shadow: 0 6px 30px -6px rgba(0, 0, 0, 0.25);
}

.popper {
  max-width: 400px;
  font-weight: normal;
}


</style>