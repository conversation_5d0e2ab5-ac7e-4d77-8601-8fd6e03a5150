<?php TemplateHelper::includePartial('_tabs.php', 'orders', compact(["quotation", "quotation_extra", "company", "sandboxuser"])); ?>
<form method="post">

  <div style="display: flex; flex-direction: column">
    <br/>
    <label>
      <p>Verstuur aangepaste offerte</p>
      <a href="#" data-url="?action=sendMail&id=<?php echo $quotation->quotationId ?>" data-title="Verstuur offerte" class="gsd-btn gsd-btn-secondary" id="send_new_quotation" style="margin: 0 15px 0 0;">Verstuur aangepaste offerte</a>
    </label>
  </div>

</form>


<script>
  let gsdModal = new GsdModal();
  gsdModal.init();

  $(document).ready(function() {

    $("#send_new_quotation").on("click", function (e) {
      e.preventDefault();
      gsdModal.open($(this).attr("data-url"), $(this).attr("data-title"));
    });

    $(document).on("gsdModalSelect", function (e, msg) {

      gsdModal.hide();

      // let company = JSON.parse(msg);
      // $("#company_id").val(company.companyId);
      // $("#company_name").val(company.name);
    });

    $(document).on("gsdModalClose", function (e, msg) {
      // refresh the page
      location.reload();
    });
  });
</script>

<!--<a id="selectCompany" class="btn btn-primary" style="margin: 0 15px 0 0;" href="#" data-url="?action=contactrobert">-->