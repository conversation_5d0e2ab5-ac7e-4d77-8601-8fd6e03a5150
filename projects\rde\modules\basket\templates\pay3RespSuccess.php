<section id="basket">
  <div>
    <h1><?php echo $wizard_name ?> - verz<PERSON><PERSON>ten</h1>

    <?php include("_wizardheader.php"); ?>

    <?php writeErrors($errors, true); ?>

    Overzicht van uw <?php echo strtolower($wizard_name) ?>.<br/>
    <br/>


    <div id="step3" class="wizard basket-step-3">
      <form method="post">

        <div class="form-row">
          <label class="col3 col-form-label"><?php echo $quotation_extra->isPickup()?__("Ophaaladres"):__("Afleveradres") ?></label>
          <div class="col9 col-form-label">
            <?php if($quotation_extra->isPickup()): ?>
              Afhalen in Bladel
            <?php else: ?>
              <span style="line-height: 23px;display: inline-block;"><?php echo $quotation->getAddressFull() ?></span>
            <?php endif; ?>
          </div>
        </div>

        <div class="form-row">
          <label class="col3 col-form-label">Uw producten</label>
          <div class="col9 col-form-label">
          </div>
          <div style="padding: 15px;">
            <div id="basket_row_header" class="row">
              <div class="col6 col12-xs">Productnaam</div>
              <div class="col2 col12-xs" style="text-align: right">Aantal</div>
              <div class="col2 col12-xs" style="text-align: right">Stukprijs</div>
              <div class="col2 col12-xs" style="text-align: right">Prijs</div>
            </div>
            <?php foreach ($basket['products'] as $tel => $item):
              $product = $item['product'];
              $invoice_product_stone = false;
              if(isset($item["invoice_product_stone"])) {
                $invoice_product_stone = $item["invoice_product_stone"];
              }
              ?>
              <div class="row">
                <div class="col6 col12-xs ">
                  <a href="<?php echo $product->getShopUrl() ?>" id="prodlink_<?php echo $tel ?>"><?php echo $item["name"] ?></a>
                </div>
                <div class="col2 col12-xs " style="text-align: right">
                  <?php echo $item['size'] ?>
                </div>
                <div class="col2 col12-xs basket_pieceprice">
                  <?php echo $product->price_on_request ?
                    'Op aanvraag' :
                    '€' . getLocalePrice($item["pieceprice"])
                  ?>
                </div>
                <div class="col2 col12-xs basket_price">
                  <?php if ($product->price_on_request): ?>
                    Op aanvraag
                  <?php else: ?>
                    <?php echo '€' . (Config::get('PRODUCTS_SHOW_PRICES_INC_VAT', true)
                        ? getLocalePrice($item["totalprice_inc"])
                        : getLocalePrice($item["totalprice"])) ?>
                  <?php endif; ?>
                </div>
              </div>
            <?php endforeach; ?>
            <input type="hidden" value="<?php echo $basket['subtotal'] ?>" id="product_subtotal_excl"/>
            <?php if(SandboxUsers::isAdmin()): ?>
              <div class="row">
                <div class="col3" style="padding-top:8px;">
                  Admin - extra regel
                </div>
                <div class="col6">
                  <input class="form-input" maxlength="20" name="admin_extra_desc" id="admin_extra_desc" placeholder="Omschrijving..." value="<?php if(isset($_SESSION["basket"]['admin_extra_desc'])) echo $_SESSION["basket"]['admin_extra_desc'] ?>" />
                </div>
                <div class="col3">
                  <input class="form-input price" maxlength="10" name="admin_extra_amount" id="admin_extra_amount" placeholder="Bedrag..." value="<?php if(isset($_SESSION["basket"]['admin_extra_amount'])) echo $_SESSION["basket"]['admin_extra_amount'] ?>" style="text-align: right;"/>
                </div>
              </div>
              <div class="row"  style="padding-top:15px;">
                <div class="col3">
                  Admin - verzenden
                </div>
                <div class="col9">
                  <label><input type="checkbox" value="1" name="freeshipping" id="freeshipping" <?php echo isset($_SESSION["basket"]['freeshipping']) && $_SESSION["basket"]['freeshipping']==1?'checked':''; ?>/> gratis verzenden</label>
                </div>
              </div>
            <?php endif; ?>

            <?php if($quotation->verzendkosten>0): ?>
              <div class="row">
                <div class="col12 basket_subtotal" style="padding-top: 15px;">
                  Verzendkosten €
                  <span id="freightCosts" data-val="<?php echo $quotation->verzendkosten ?>"><?php echo getLocalePrice($quotation->verzendkosten); ?></span>
                </div>
              </div>
            <?php endif; ?>

            <?php if (!$hasRequestPrices): ?>
              <div class="row">
                <div class="col12 basket_subtotal" style="padding-top: 15px;">
                  Subtotaal excl. BTW €
                  <span id="subtotal_excl" data-val="<?php echo $basket['subtotal']+$quotation->verzendkosten ?>"><?php echo getLocalePrice($basket['subtotal']+$quotation->verzendkosten); ?></span>
                </div>
              </div>
              <div class="row">
                <div class="col12 basket_subtotal" style="padding-top: 15px;">
                  BTW €
                  <span id="vat" data-val="<?php echo ($basket['subtotal_inc'] - $basket['subtotal'] + (0.21*$quotation->verzendkosten)) ?>"><?php echo getLocalePrice($basket['subtotal_inc'] - $basket['subtotal'] + (0.21*$quotation->verzendkosten)); ?></span>
                </div>
              </div>
              <div class="row">
                <div class="col12 basket_subtotal bold" style="padding-top: 15px;">
                  Totaal €
                  <span id="total" data-val="<?php echo $basket['subtotal_inc'] + (1.21*$quotation->verzendkosten) ?>"><?php echo getLocalePrice($basket['subtotal_inc'] + (1.21*$quotation->verzendkosten)); ?></span>
                </div>
              </div>
            <?php else: ?>
              <div class="row">
                <div class="col12 basket_subtotal bold" style="padding-top: 15px;">
                  Totale prijs: Op aanvraag
                </div>
              </div>
            <?php endif; ?>
          </div>
        </div>
        <?php if(!$tender): ?>
          <div class="form-row">
            <label class="col3 col-form-label">Afrekenen</label>
            <div class="col9 col-form-label">
              <?php echo $paymethods[$quotation->paymentMethod]->getTitle() ?>
            </div>
          </div>
        <?php endif; ?>

        <div class="form-row">
          <label class="col3 col-form-label"><?php echo __("Uw referentie") ?> <span style="color: red;">*</span></label>
          <div class="col9 col12-xs">
            <input class="form-input" maxlength="20" name="projectName" id="projectName" placeholder="<?php echo __("Uw referentie") ?>" value="<?php echo escapeForInput($quotation->projectName) ?>"/>
          </div>
        </div>

        <div class="form-row">
          <label class="col3 col-form-label"><?php echo __("Opmerkingen") ?></label>
          <div class="col9 col12-xs">
            <textarea class="form-input" name="customerNotes" id="customerNotes" placeholder="<?php echo __("Uw opmerkingen of vragen") ?>"><?php echo $quotation->customerNotes ?></textarea>
          </div>
        </div>

        <div class="form-row">
          <label class="col3 col-form-label"><?php echo __("Algemene voorwaarden") ?></label>
          <div class="col9 col12-xs" style="line-height: 2.5">
            <label>
              Door verder te gaan ga je akkoord met onze <a href="https://www.raamdorpel.nl/algemene-voorwaarden" target="_blank">Algemene Voorwaarden</a>
            </label>
          </div>
        </div>

        <br/><br/>
        <button type="submit" name="prev" id="prev" class="btn" style="float: left;" formnovalidate><i class="fa fa-chevron-left"></i> <?php echo __("Vorige stap"); ?></button>

        <?php if($tender): ?>
          <button type="submit" name="next" id="next" class="btn" style="float: right;">
            <?php echo __("Offerte aanvragen"); ?>
            <i class="fa fa-chevron-right"></i>
          </button>
        <?php else: ?>
          <button type="submit" name="next" id="next" class="btn" style="float: right;">
            <?php echo $quotation->paymentMethod==Payment::PAYMENT_MOLLIE? __("Bevestigen en online afrekenen"):__("Bestelling bevestigen"); ?>
            <i class="fa fa-chevron-right"></i>
          </button>
        <?php endif; ?>
        <button type="submit" name="refresh" id="refresh" style="display:none;"></button>

      </form>

    </div>


  </div>
</section>

<script type="text/javascript">

  blockEnterSubmit();

  $(document).ready(function () {

    $("#admin_extra_amount").change(function() {
      $(this).val(decimalNL($(this).val(),2));
      $(this).attr("data-val",Math.round($(this).val(),2));
      recalculate();
    });

    $("#freeshipping").change(function() {
      $("#refresh").click();
    });

    recalculate();

    function recalculate() {
      var vat = parseFloat($("#vat").attr("data-val"));
      if(isNaN(vat)) {
        vat = 0;
      }
      var subtotal = parseFloat($("#product_subtotal_excl").val());
      var extra = parseFloat($("#admin_extra_amount").val());
      var extra_vat = 0;
      if(isNaN(extra)) {
        extra = 0;
      }
      else if(vat!=0) {
        extra_vat = 0.21*extra;
        vat += extra_vat;
      }
      var shipping = parseFloat($("#freightCosts").attr("data-val"));
      if(isNaN(shipping)) {
        shipping = 0;
      }
      var subtotal_excl = subtotal + extra + shipping;
      var total = subtotal_excl + vat;

      $("#subtotal_excl").text(currencyFormat(subtotal + extra));
      $("#vat").text(currencyFormat(vat));
      $("#total").text(currencyFormat(total));



    }



  });

</script>