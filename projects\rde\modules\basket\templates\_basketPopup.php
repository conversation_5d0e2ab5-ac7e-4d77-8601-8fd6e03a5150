<ul class="child hcart">
  <?php if (!isset($_SESSION['basket']['subtotal_inc']) || !isset($_SESSION['basket']['products']) || $_SESSION['basket']['products'] == 0): ?>
    <li>
      Geen producten in winkelmandje.
    </li>
  <?php else: ?>
    <li>
      <table class="table">
        <?php foreach ($_SESSION['basket']['products'] as $basket_product): ?>
          <tr>
            <td class="text-left">
              <a class="hchart_product_link"
                 href="<?php echo "https://" . $_SERVER['HTTP_HOST'] . $basket_product['product']->getShopUrl(); ?>">
                <?php echo $basket_product['name']; ?>
              </a>
            </td>
            <td class="text-right">x<?php echo $basket_product['size']; ?></td>
            <td class="text-right">
              <?php echo $basket_product['product']->price_on_request ? __('Prijs op aanvraag') : '&euro;' . getLocalePrice($basket_product['totalprice']); ?>
            </td>
          </tr>
        <?php endforeach; ?>
      </table>
    </li>
    <li>
      <table class="table table-bordered total">
        <tbody>
        <tr>
          <td class="text-right"><strong>Totaal excl.</strong></td>
          <td class="text-right bold">
            <?php
              if ($hasRequestPrices) echo __('Prijs op aanvraag');

              elseif (isset($_SESSION['basket']['subtotal'])) echo '&euro;' . getLocalePrice($_SESSION['basket']['subtotal']);

              else echo '&euro;0,-';
            ?>
          </td>
        </tr>
        </tbody>
      </table>
      <a href="<?php echo PageMap::getUrl('M_BASKET'); ?>" class="btn btn-primary">
        Wijzig winkelmandje
      </a>
      <a href="<?php echo PageMap::getUrl('M_BASKET'); ?>?action=pay1" class="btn btn-primary">
        Bestellen
      </a>
    </li>
  <?php endif; ?>
</ul>