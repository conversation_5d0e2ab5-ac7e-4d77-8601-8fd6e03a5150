<?php
class BaseOrderElementparts extends AppModel
{
  const DB_NAME = 'rde_b1mo';
  const TABLE_NAME = 'order_elementparts';
  const OM_CLASS_NAME = 'OrderElementparts';
  const columns = ['orderElementPartId', 'elementId', 'quotationId', 'aAmount1', 'aLength1', 'aLength2', 'bAmount1', 'bLength1', 'bLength2', 'cAmount1', 'cLength1', 'cLength2', 'dAmount1', 'dLength1', 'dLength2'];
  const field_structure = [
    'orderElementPartId'          => ['type' => 'int', 'length' => '11', 'null' => false],
    'elementId'                   => ['type' => 'int', 'length' => '11', 'null' => false],
    'quotationId'                 => ['type' => 'int', 'length' => '11', 'null' => false],
    'aAmount1'                    => ['type' => 'smallint', 'length' => '4', 'null' => false],
    'aLength1'                    => ['type' => 'decimal', 'length' => '8,2', 'null' => true],
    'aLength2'                    => ['type' => 'decimal', 'length' => '8,2', 'null' => true],
    'bAmount1'                    => ['type' => 'smallint', 'length' => '4', 'null' => false],
    'bLength1'                    => ['type' => 'decimal', 'length' => '8,2', 'null' => true],
    'bLength2'                    => ['type' => 'decimal', 'length' => '8,2', 'null' => true],
    'cAmount1'                    => ['type' => 'smallint', 'length' => '4', 'null' => false],
    'cLength1'                    => ['type' => 'decimal', 'length' => '8,2', 'null' => true],
    'cLength2'                    => ['type' => 'decimal', 'length' => '8,2', 'null' => true],
    'dAmount1'                    => ['type' => 'smallint', 'length' => '4', 'null' => false],
    'dLength1'                    => ['type' => 'decimal', 'length' => '8,2', 'null' => true],
    'dLength2'                    => ['type' => 'decimal', 'length' => '8,2', 'null' => true],
  ];

  protected static $primary_key = ['orderElementPartId'];
  protected $auto_increment = 'orderElementPartId';

  public $orderElementPartId, $elementId, $quotationId, $aAmount1, $aLength1, $aLength2, $bAmount1, $bLength1, $bLength2, $cAmount1, $cLength1, $cLength2, $dAmount1, $dLength1, $dLength2;

  function __construct($obj_arr = [], &$error_codes = []) {
    parent::__construct();
    $this->setDefaults();
    if (!empty($obj_arr)) {
      $this->from_array($obj_arr);
      $this->valid_required_fields($error_codes);
    }
  }

  public function valid(&$error_codes = []) {
    $errors = [];
    foreach(self::columns as $column) {
      if ($column != $this->auto_increment)
      {
        call_user_func_array([$this, 'v_' . $column], [&$errors]);
      }
    }
    if(empty($errors)) {
      return true;
    }
    $error_codes = array_merge($error_codes, $errors);
    return false;
  }

  public function valid_required_fields(&$error_codes = []) {
    $errors = [];
    foreach (self::columns as $column) {
      $function_name = "v_$column";
      if ($column!=$this->auto_increment) {
        call_user_func_array([$this, $function_name], [&$errors]);
      }
    }
    if (empty($errors)) {
      return true;
    }
    $error_codes = array_merge($error_codes, $errors);
    return false;
  }

  public function setDefaults() {
    $this->aAmount1 = 1;
    $this->bAmount1 = 1;
    $this->cAmount1 = 1;
    $this->dAmount1 = 1;
  }



  public function v_orderElementPartId(&$error_codes = []) {
    if (!is_null($this->orderElementPartId) && strlen($this->orderElementPartId) > 0 && self::valid_int($this->orderElementPartId, '11')) {
      return true;
    }
    $error_codes[] = 'orderElementPartId';
    return false;
  }

  public function v_elementId(&$error_codes = []) {
    if (!is_null($this->elementId) && strlen($this->elementId) > 0 && self::valid_int($this->elementId, '11')) {
      return true;
    }
    $error_codes[] = 'elementId';
    return false;
  }

  public function v_quotationId(&$error_codes = []) {
    if (!is_null($this->quotationId) && strlen($this->quotationId) > 0 && self::valid_int($this->quotationId, '11')) {
      return true;
    }
    $error_codes[] = 'quotationId';
    return false;
  }

  public function v_aAmount1(&$error_codes = []) {
    if (!is_null($this->aAmount1) && strlen($this->aAmount1) > 0 && self::valid_smallint($this->aAmount1, '4')) {
      return true;
    }
    $error_codes[] = 'aAmount1';
    return false;
  }

  public function v_aLength1(&$error_codes = []) {
    if (is_null($this->aLength1) || strlen($this->aLength1) == 0 || self::valid_decimal($this->aLength1, '8,2')) {
      return true;
    }
    $error_codes[] = 'aLength1';
    return false;
  }

  public function v_aLength2(&$error_codes = []) {
    if (is_null($this->aLength2) || strlen($this->aLength2) == 0 || self::valid_decimal($this->aLength2, '8,2')) {
      return true;
    }
    $error_codes[] = 'aLength2';
    return false;
  }

  public function v_bAmount1(&$error_codes = []) {
    if (!is_null($this->bAmount1) && strlen($this->bAmount1) > 0 && self::valid_smallint($this->bAmount1, '4')) {
      return true;
    }
    $error_codes[] = 'bAmount1';
    return false;
  }

  public function v_bLength1(&$error_codes = []) {
    if (is_null($this->bLength1) || strlen($this->bLength1) == 0 || self::valid_decimal($this->bLength1, '8,2')) {
      return true;
    }
    $error_codes[] = 'bLength1';
    return false;
  }

  public function v_bLength2(&$error_codes = []) {
    if (is_null($this->bLength2) || strlen($this->bLength2) == 0 || self::valid_decimal($this->bLength2, '8,2')) {
      return true;
    }
    $error_codes[] = 'bLength2';
    return false;
  }

  public function v_cAmount1(&$error_codes = []) {
    if (!is_null($this->cAmount1) && strlen($this->cAmount1) > 0 && self::valid_smallint($this->cAmount1, '4')) {
      return true;
    }
    $error_codes[] = 'cAmount1';
    return false;
  }

  public function v_cLength1(&$error_codes = []) {
    if (is_null($this->cLength1) || strlen($this->cLength1) == 0 || self::valid_decimal($this->cLength1, '8,2')) {
      return true;
    }
    $error_codes[] = 'cLength1';
    return false;
  }

  public function v_cLength2(&$error_codes = []) {
    if (is_null($this->cLength2) || strlen($this->cLength2) == 0 || self::valid_decimal($this->cLength2, '8,2')) {
      return true;
    }
    $error_codes[] = 'cLength2';
    return false;
  }

  public function v_dAmount1(&$error_codes = []) {
    if (!is_null($this->dAmount1) && strlen($this->dAmount1) > 0 && self::valid_smallint($this->dAmount1, '4')) {
      return true;
    }
    $error_codes[] = 'dAmount1';
    return false;
  }

  public function v_dLength1(&$error_codes = []) {
    if (is_null($this->dLength1) || strlen($this->dLength1) == 0 || self::valid_decimal($this->dLength1, '8,2')) {
      return true;
    }
    $error_codes[] = 'dLength1';
    return false;
  }

  public function v_dLength2(&$error_codes = []) {
    if (is_null($this->dLength2) || strlen($this->dLength2) == 0 || self::valid_decimal($this->dLength2, '8,2')) {
      return true;
    }
    $error_codes[] = 'dLength2';
    return false;
  }

  /**
  *@param array $conditions (required)
  *@param String $raw_sql (optional)
  *@return OrderElementparts[]
  **/
  public static function find_all_like($conditions, $raw_sql = '') { return parent::generic_find_all_like($conditions, static::OM_CLASS_NAME, $raw_sql); }
  /**
  *@param array $conditions (required)
  *@param String $raw_sql (optional)
  *@return OrderElementparts[]
  **/
  public static function find_all_by($conditions, $raw_sql = '') { return parent::generic_find_all_by($conditions, static::OM_CLASS_NAME, $raw_sql); }
  /**
  *@param String $raw_sql (optional)
  *@return OrderElementparts[]
  **/
  public static function find_all($raw_sql = '') { return parent::generic_find_all(static::OM_CLASS_NAME, $raw_sql); }
  /**
  *@param array $conditions (required)
  *@param String $raw_sql (optional)
  *@return OrderElementparts
  **/
  public static function find_by($conditions, $raw_sql = '') { return parent::generic_find_by($conditions, static::OM_CLASS_NAME, $raw_sql); }
  /**
  *@param String $id (required)
  *@param String $raw_sql (optional)
  *@return OrderElementparts
  **/
  public static function find_by_id($id, $raw_sql = '') { return parent::generic_find_by_id($id, static::OM_CLASS_NAME, $raw_sql); }
  /**
  *@param array $conditions (required)
  *@param String $raw_sql (optional)
  *@return int
  **/
  public static function count_all_by($conditions, $raw_sql = '') { return parent::generic_count_all_by($conditions, static::OM_CLASS_NAME, $raw_sql); }
  /**
  *@param array $conditions (required)
  *@param String $raw_sql (optional)
  *@return bool
  **/
  public static function delete_by($conditions, $raw_sql = '') { return parent::generic_delete_by($conditions, static::OM_CLASS_NAME, $raw_sql); }
}