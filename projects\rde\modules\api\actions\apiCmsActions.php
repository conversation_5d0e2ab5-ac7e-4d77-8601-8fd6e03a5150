<?php


  use domain\multivers\service\InvoiceSave;
  use domain\multivers\service\InvoiceSyncStatus;
  use gsdfw\domain\multivers\exception\MultiversException;
  use gsdfw\domain\multivers\service\MultiversApi;

  /**
   * Trait apiCmsActions
   * Used for calls from other rde cms
   */
  trait apiCmsActions {

    /**
     * Synchronise invoice status
     */
    public function executeInvoicestatussync() {

      set_time_limit(600); //10 minuten
      ini_set('memory_limit', '712M');
      ini_set('max_execution_time', 600); // 300 sec = 10 min

      $user = User::getUserWithOrganById(ADMIN_DEFAULT_ID);
      $multivers = Multivers::find_by(["organisation_id" => $user->organisation_id]);
      $multivers_api = new MultiversApi($multivers);

      try {
        $sync_invoice_status = new InvoiceSyncStatus($multivers_api);
        if (!$sync_invoice_status->sync()) {
          RestUtils::sendResponseError("Status synchronisatie mislukt.");
        }
      }
      catch (MultiversException $e) {
        logToFile("multivers_error", 'Status synchronisatie mislukt executeInvoicestatussync: ' . $e->getMessage() . ' ' . $e->getTraceAsString());
        RestUtils::sendResponseError("Status synchronisatie mislukt: ", $e->getMessage());
      }

      $response = [
        "checked"        => $sync_invoice_status->getChecked(),
        "open"           => $sync_invoice_status->getOpen(),
        "paid"           => $sync_invoice_status->getPaid(),
        "paidInvoiceNrs" => $sync_invoice_status->getPaidInvoiceNrs(),
      ];
      RestUtils::sendResponseOK("Factuur status gesynchroniseerd", $response);
    }

    /**
     * Send all not synced invoices
     * @throws Exception
     */
    public function executeInvoicessend() {

      set_time_limit(600); //10 minuten
      ini_set('memory_limit', '512M');
      ini_set('max_execution_time', 600); // 300 sec = 10 min

      $user = User::getUserWithOrganById(ADMIN_DEFAULT_ID);
      $multivers = Multivers::find_by(["organisation_id" => $user->organisation_id]);
      $multivers_api = new MultiversApi($multivers);
      $invoice_service = new InvoiceSave($multivers_api);

      //      $invoices = Invoices::getNotInMultiversInvoices("LIMIT 50");
      $invoices = Invoices::getNotInMultiversInvoices();
      $response = [
        "send"   => [],
        "failed" => [],
      ];
      try {
        foreach ($invoices as $invoice) {
          $result = false;
          if (!DEVELOPMENT) {
            $result = $invoice_service->add($invoice);
          }
          if ($result === false) {
            $response["failed"][] = $invoice->invoiceNumber . ': onbekende fout';
            break;
          }
          else {
            $response["send"][] = $invoice->invoiceNumber;
          }
        }
      }
      catch (MultiversException $e) {
        logToFile("multivers_error", 'Foutmelding executeInvoicessend: ' . $e->getMessage() . ' ' . $e->getTraceAsString());
        RestUtils::sendResponseError("Facturen verzonden", $e->getMessage());
      }
      RestUtils::sendResponseOK("Facturen verzonden", $response);
    }

  }