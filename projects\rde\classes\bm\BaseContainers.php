<?php
class BaseContainers extends AppModel
{
  const DB_NAME = 'rde_b1mo';
  const TABLE_NAME = 'containers';
  const OM_CLASS_NAME = 'Containers';
  const columns = ['containerId', 'containerNumber', 'type', 'inStock', 'containerWeight', 'action', 'actionRemark', 'actionRemarkExtra'];
  const field_structure = [
    'containerId'                 => ['type' => 'int', 'length' => '7', 'null' => false],
    'containerNumber'             => ['type' => 'varchar', 'length' => '8', 'null' => false],
    'type'                        => ['type' => 'enum', 'length' => '4', 'null' => false, 'enums' => ['bak','rek','pal','opr']],
    'inStock'                     => ['type' => 'enum', 'length' => '4', 'null' => false, 'enums' => ['A','Y','N','W']],
    'containerWeight'             => ['type' => 'int', 'length' => '5', 'null' => false],
    'action'                      => ['type' => 'boolean', 'length' => '1', 'null' => false],
    'actionRemark'                => ['type' => 'text', 'length' => '', 'null' => true],
    'actionRemarkExtra'           => ['type' => 'text', 'length' => '', 'null' => true],
  ];

  protected static $primary_key = ['containerId'];
  protected $auto_increment = 'containerId';

  public $containerId, $containerNumber, $type, $inStock, $containerWeight, $action, $actionRemark, $actionRemarkExtra;

  function __construct($obj_arr = [], &$error_codes = []) {
    parent::__construct();
    $this->setDefaults();
    if (!empty($obj_arr)) {
      $this->from_array($obj_arr);
      $this->valid_required_fields($error_codes);
    }
  }

  public function valid(&$error_codes = []) {
    $errors = [];
    foreach(self::columns as $column) {
      if ($column != $this->auto_increment)
      {
        call_user_func_array([$this, 'v_' . $column], [&$errors]);
      }
    }
    if(empty($errors)) {
      return true;
    }
    $error_codes = array_merge($error_codes, $errors);
    return false;
  }

  public function valid_required_fields(&$error_codes = []) {
    $errors = [];
    foreach (self::columns as $column) {
      $function_name = "v_$column";
      if ($column!=$this->auto_increment) {
        call_user_func_array([$this, $function_name], [&$errors]);
      }
    }
    if (empty($errors)) {
      return true;
    }
    $error_codes = array_merge($error_codes, $errors);
    return false;
  }

  public function setDefaults() {
    $this->type = 'bak';
    $this->inStock = 'Y';
    $this->containerWeight = 0;
    $this->action = 0;
  }



  public function v_containerId(&$error_codes = []) {
    if (!is_null($this->containerId) && strlen($this->containerId) > 0 && self::valid_int($this->containerId, '7')) {
      return true;
    }
    $error_codes[] = 'containerId';
    return false;
  }

  public function v_containerNumber(&$error_codes = []) {
    if (!is_null($this->containerNumber) && strlen($this->containerNumber) > 0 && self::valid_varchar($this->containerNumber, '8')) {
      return true;
    }
    $error_codes[] = 'containerNumber';
    return false;
  }

  public function v_type(&$error_codes = []) {
    if ($this->type == 'bak') { return true; }
    if ($this->type == 'rek') { return true; }
    if ($this->type == 'pal') { return true; }
    if ($this->type == 'opr') { return true; }
    $error_codes[] = 'type';
    return false;
  }

  public function v_inStock(&$error_codes = []) {
    if ($this->inStock == 'A') { return true; }
    if ($this->inStock == 'Y') { return true; }
    if ($this->inStock == 'N') { return true; }
    if ($this->inStock == 'W') { return true; }
    $error_codes[] = 'inStock';
    return false;
  }

  public function v_containerWeight(&$error_codes = []) {
    if (!is_null($this->containerWeight) && strlen($this->containerWeight) > 0 && self::valid_int($this->containerWeight, '5')) {
      return true;
    }
    $error_codes[] = 'containerWeight';
    return false;
  }

  public function v_action(&$error_codes = []) {
    if (!is_null($this->action) && strlen($this->action) > 0 && self::valid_tinyint($this->action, '1')) {
      return true;
    }
    $error_codes[] = 'action';
    return false;
  }

  public function v_actionRemark(&$error_codes = []) {
    if (is_null($this->actionRemark) || strlen($this->actionRemark) == 0 || self::valid_text($this->actionRemark)) {
      return true;
    }
    $error_codes[] = 'actionRemark';
    return false;
  }

  public function v_actionRemarkExtra(&$error_codes = []) {
    if (is_null($this->actionRemarkExtra) || strlen($this->actionRemarkExtra) == 0 || self::valid_text($this->actionRemarkExtra)) {
      return true;
    }
    $error_codes[] = 'actionRemarkExtra';
    return false;
  }

  /**
  *@param array $conditions (required)
  *@param String $raw_sql (optional)
  *@return Containers[]
  **/
  public static function find_all_like($conditions, $raw_sql = '') { return parent::generic_find_all_like($conditions, static::OM_CLASS_NAME, $raw_sql); }
  /**
  *@param array $conditions (required)
  *@param String $raw_sql (optional)
  *@return Containers[]
  **/
  public static function find_all_by($conditions, $raw_sql = '') { return parent::generic_find_all_by($conditions, static::OM_CLASS_NAME, $raw_sql); }
  /**
  *@param String $raw_sql (optional)
  *@return Containers[]
  **/
  public static function find_all($raw_sql = '') { return parent::generic_find_all(static::OM_CLASS_NAME, $raw_sql); }
  /**
  *@param array $conditions (required)
  *@param String $raw_sql (optional)
  *@return Containers
  **/
  public static function find_by($conditions, $raw_sql = '') { return parent::generic_find_by($conditions, static::OM_CLASS_NAME, $raw_sql); }
  /**
  *@param String $id (required)
  *@param String $raw_sql (optional)
  *@return Containers
  **/
  public static function find_by_id($id, $raw_sql = '') { return parent::generic_find_by_id($id, static::OM_CLASS_NAME, $raw_sql); }
  /**
  *@param array $conditions (required)
  *@param String $raw_sql (optional)
  *@return int
  **/
  public static function count_all_by($conditions, $raw_sql = '') { return parent::generic_count_all_by($conditions, static::OM_CLASS_NAME, $raw_sql); }
  /**
  *@param array $conditions (required)
  *@param String $raw_sql (optional)
  *@return bool
  **/
  public static function delete_by($conditions, $raw_sql = '') { return parent::generic_delete_by($conditions, static::OM_CLASS_NAME, $raw_sql); }
}