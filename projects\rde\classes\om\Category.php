<?php

  AppModel::loadModelClass('CategoryModel');

  class Category extends CategoryModel {

    /**
     * @return Category[]
     */
    public static function getShopCats() {
      $filt = 'WHERE category.void = 0 ';
      if (isset($_SESSION["userAdmin"])) {
        $filt .= "AND online_admin=1 ";
      }
      else {
        $filt .= "AND online_custshop=1 ";
      }
      $filt .= "ORDER BY category.sort ASC";
      return Category::getCategoryTree("nl", $filt);
    }

  }