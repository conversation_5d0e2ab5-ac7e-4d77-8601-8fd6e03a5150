<?php /** @var array $errors */ ?>

<div>
  <?php if (isset($errors)) writeErrors($errors); ?>

  <form method="post" class="custom_form required-form">
    <div class="form" id="form-1">
      <div class="form-row">
        <label class="col-4" for="name">Naam <span class="form-arterisk">*</span></label>
        <input id="name" class="col-4-3 required form-input" value="<?php writeIfSet('name') ?>" type="text" name="name">
      </div>
      <div class="form-row">
        <label class="col-4" for="email">E-mailadres <span class="form-arterisk">*</span></label>
        <input id="email" class="col-4-3 required form-input" value="<?php writeIfSet('email') ?>" type="email" name="email">
      </div>
      <div class="form-row">
        <label for="answer" class="col-4">Antwoord <span class="form-arterisk">*</span></label>
        <textarea id="answer" class="col-4-3 required form-input" name="answer"><?php writeIfSet('answer') ?></textarea>
      </div>
      <div class="form-row">
        <div class="col-4 responsive-hide">&nbsp;</div>
        <div class="col-4-3 g-recaptcha" data-sitekey="<?php echo Config::get("GOOGLE_RECAPTCHA")["public_key"] ?>" data-callback="onSubmit"></div>
      </div>
      <div class="form-row">
        <div class="col-4 responsive-hide">&nbsp;</div>
        <div class="col-4-3">
          <button class="btn" name="send" id="send" type="submit">Verstuur</button>
        </div>
      </div>
    </div>
  </form>
</div>

<script src='https://www.google.com/recaptcha/api.js' async defer></script>

