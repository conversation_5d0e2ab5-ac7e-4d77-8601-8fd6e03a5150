<?php

  use domain\elements\service\SplitCalculator;
  use domain\production\service\ProductionPdfGenerator;
  use Gsd\DataTable\DataTable;
  use Gsd\Form\Elements\Select;
  use Gsd\Form\ModelForm;
  use \Gsd\Form\Elements\Option;

  class ordersRdeActions extends gsdActions {
    const ORDERS = '0';
    const ORDERS_COMMISION = '1';
    const ORDERS_NATURE_STONE_B = '2';
    const ORDERS_DELIVERY_WEEK = '3';
    const ORDERS_WEBSHOP = '4';
    const ORDERS_PRIVATE = '5';
    const ORDERS_SPECIAL = '6';
    const ORDERS_NO_DELIVERY = '7';
    const ORDERS_CONCRETE = '8';
    const ORDERS_NO_COMPANY = '9';
    const ORDERS_ISOSILL = '10';
    const ORDERS_NATURE_STONE_C = '11';

    public function executelist($page = self::ORDERS): void {
      $this->createListFilters($page);
      $this->template = 'listSuccess.php';
    }

    public function executelistNatureStoneB(): void {
      $this->executelist(self::ORDERS_NATURE_STONE_B);
    }

    public function executelistNatureStoneC(): void {
      $this->executelist(self::ORDERS_NATURE_STONE_C);
    }

    public function executeListCommission(): void {
      $this->executelist(self::ORDERS_COMMISION);
    }

    public function executeListDeliveryWeek(): void {
      $this->executelist(self::ORDERS_DELIVERY_WEEK);
    }

    public function executeListWebshop(): void {
      $this->executelist(self::ORDERS_WEBSHOP);
    }

    public function executeListPrivate(): void {
      $this->executelist(self::ORDERS_PRIVATE);
    }

    public function executeListSpecial(): void {
      $this->executelist(self::ORDERS_SPECIAL);
    }

    public function executeListNoDeliveryAddress(): void {
      $this->executelist(self::ORDERS_NO_DELIVERY);
    }

    public function executeListConcrete(): void {
      $this->executelist(self::ORDERS_CONCRETE);
    }

    public function executeListNoCompany(): void {
      $this->executelist(self::ORDERS_NO_COMPANY);
    }

    public function executeListIsosill(): void {
      $this->executelist(self::ORDERS_ISOSILL);
    }

    public function createListFilters($page): void {
      Context::addStylesheet(URL_INCLUDES . "vendor/ivaynberg/select2/dist/css/select2.min.css");
      Context::addJavascript(URL_INCLUDES . "vendor/ivaynberg/select2/dist/js/select2.min.js");
      Context::addJavascript(URL_INCLUDES . "vendor/ivaynberg/select2/dist/js/i18n/nl.js");

      $dataTable = new DataTable("orders-$page");

      // send the company id to the ajax request to filter if applicable
      $dataTable->setRequestUrl(reconstructQueryAdd() . "action=listajax&page=$page");
      $dataTable->addColumnHelper("statusId", "Status");
      $dataTable->addColumnHelper("quotationNumber", "Offertenummer");
      $dataTable->addColumnHelper("quotationDate", "Offertedatum");
      $dataTable->addColumnHelper("code", "Code");
      $dataTable->addColumnHelper("name", "Bedrijfsnaam");
      $dataTable->addColumnHelper("meters", "Meters");
      $dataTable->addColumnHelper("place", "Plaats");
      $dataTable->addColumnHelper("zipcode", "Postcode");
      $dataTable->addColumnHelper("plannedDate", "Geplanned");
      $dataTable->addColumnHelper("dueDateWeek", "W");
      $dataTable->addColumnHelper("urgencyFlag", "S");
      $dataTable->addColumnHelper("payedFlag", "€");
      $dataTable->addColumnHelper("onHoldFlag", "H");
      $dataTable->addColumnHelper("seamColor", "V");
      $dataTable->addColumnHelper("actions", "Acties");

      $dataTable->setDefaultSort("quotationNumber", "desc");

      if ($page === self::ORDERS_DELIVERY_WEEK) {
        // Delivery week page has its own sorting so we disable the default sorting for this page
        foreach ($dataTable->getColumns() as $column) {
          $column->setSortable(false);
        }
      } else {
        $dataTable->getColumn("name")->setSortable(false);
        $dataTable->getColumn("code")->setSortable(false);
        $dataTable->getColumn("place")->setSortable(false);
        $dataTable->getColumn("plannedDate")->setSortable(false);
        $dataTable->getColumn("zipcode")->setSortable(false);
        $dataTable->getColumn("seamColor")->setSortable(false);
        $dataTable->getColumn("actions")->setSortable(false);
      }


      $dataTable->addSearchInput();

      $select = new Select("Status", 'status_filter');
      foreach (Status::STATI as $code => $name) {
        $select->addOptionHelper($code, $name);
      }
      $select->setMuliple(true);

      // The default selection of the status filter is based on the page
      $valuesToSet = match ($page) {
        self::ORDERS => [Status::STATUS_NEW, Status::STATUS_ORDER, Status::STATUS_CHECKED, Status::STATUS_PREPARED, Status::STATUS_IN_PRODUCTION, Status::STATUS_PRODUCED, Status::STATUS_PACKED, Status::STATUS_LOADED, Status::STATUS_DELIVERED, Status::STATUS_INVOICED, Status::STATUS_PAID, Status::STATUS_SPLIT],
        self::ORDERS_DELIVERY_WEEK => [Status::STATUS_ORDER, Status::STATUS_CHECKED, Status::STATUS_PREPARED, Status::STATUS_IN_PRODUCTION, Status::STATUS_PRODUCED],
        self::ORDERS_SPECIAL => [Status::STATUS_ORDER, Status::STATUS_CHECKED, Status::STATUS_PREPARED, Status::STATUS_IN_PRODUCTION],
        self::ORDERS_COMMISION => [Status::STATUS_ORDER],
        default => [Status::STATUS_ORDER, Status::STATUS_CHECKED, Status::STATUS_PREPARED, Status::STATUS_IN_PRODUCTION, Status::STATUS_PRODUCED, Status::STATUS_PACKED, Status::STATUS_LOADED, Status::STATUS_DELIVERED],
      };

      $select->setValue($valuesToSet);

      $dataTable->getForm()->addElement($select);

      $select = new Select("Jaar", 'year_filter');

      $currentYear = (int)date('Y');
      for ($tel = $currentYear + 1; $tel >= 2010; $tel--) {
        $select->addOptionHelper($tel, $tel);
      }

      $select->setMuliple(true);
      $select->setValue([date('Y'), date('Y') - 1, date('Y') - 2]);
      $dataTable->getForm()->addElement($select);

      $company_select = new Select("Bedrijf", 'companyid');
      $company_select->addOptionHelper('', 'Filter op bedrijf');
      $dataTable->getForm()->addElement($company_select);

      // add the company as option if it exists in the session
      $companyId = $_SESSION[$dataTable->getName()][$dataTable->getForm()->getName()]['companyid'] ?? null;
      if ($companyId) {
        $company = CrmCompanies::find_by(['companyId' => $companyId]);
        if ($company) {
          $company_select->addOptionHelper($companyId, $company->name);
        }
      }

      $dataTable->addSearchReset();

      $dataTable->handleRequest($_POST);

      $this->dataTable = $dataTable;
      $this->page = $page;
      $this->disablePagination = in_array($page, [self::ORDERS, self::ORDERS_COMMISION, self::ORDERS_ISOSILL, self::ORDERS_PRIVATE, self::ORDERS_NATURE_STONE_B, self::ORDERS_NATURE_STONE_C]);
    }

    public function executeListajax(): void {
      $page = $_GET['page'];

      $this->createListFilters($page);

      /** FILTER QUERY */
      $filter_query = $this->buildQuotationListFilterQuery();

      /** FILTER BASED ON CURRENT PAGE */
      $filter_query .= match ($page) {
        self::ORDERS_WEBSHOP => " AND quotations.createdVia LIKE 'webshop' ",
        self::ORDERS_PRIVATE => "AND quotations.codeId IN (31,32,33,34,35,36) ",
        self::ORDERS_SPECIAL => "AND stones.modelSpecialForProduction = 1 ",
        self::ORDERS_NO_DELIVERY => "AND (crm_addresses.longitude IS NULL OR crm_addresses.latitude IS NULL) ",
        self::ORDERS_CONCRETE => "AND stones.brandId = 3 ",
        self::ORDERS_NO_COMPANY => "AND (quotations.companyId IS NULL OR quotations.companyId = 0) ",
        self::ORDERS_ISOSILL => "AND stones.brandId = 14 ",
        // belgium, zimbabwe & windowsill
        self::ORDERS_NATURE_STONE_B => " AND (stone_brands.stoneTypeId = 4 OR stone_brands.brandId IN (5,6,7,8,12,13,15,50)) ",
        // chinese, asian
        self::ORDERS_NATURE_STONE_C => " AND stone_brands.brandId = 4 ",
        default => "",
      };

      if ($this->dataTable->hasFormElementValue("status_filter") && !empty($this->dataTable->getFormElementValue("status_filter")[0])) {
        $filter_query .= " AND " . DbHelper::getSqlIn("statusId", $this->dataTable->getFormElementValue("status_filter"));
      }

      if ($this->dataTable->hasFormElementValue("year_filter") && !empty($this->dataTable->getFormElementValue("year_filter")[0])) {
        $years_to_filter = $this->dataTable->getFormElementValue("year_filter");
        $year_filter_clause = $this->buildYearDateRangeFilter($years_to_filter);
        if (!empty($year_filter_clause)) {
          $filter_query .= " AND " . $year_filter_clause;
        }
      }

      if ($this->dataTable->hasFormElementValue("companyid")) {
        $filter_query .= " AND sandbox_users.companyId = " . $this->dataTable->getFormElementValue("companyid");
      }

      /** TOTALS */
      $total_count = Quotations::count_all_by([]);
      $total_count_filtered = Quotations::count_all_by([], $filter_query);

      /** GET DATA */
      $quotations = $this->getQuotationsFromFilterQuery($filter_query, $page);

      $mainHost = SiteHost::getMainSiteHost(1)->getDomainSmart(true);
      $table_data = [];
      foreach ($quotations as $quot) {
        $showSupplier = empty($quot->quot_extra->supplier_show) ? 'false' : 'true';
        $btn_text = empty($quot->quot_extra->supplier_show) ? 'Toon leverancier' : 'Verberg leverancier';

        $actions_html = " " . BtnHelper::getEdit(PageMap::getUrl("M_RDE_ORDERS_GENERAL") . '?id=' . $quot->quotationId);
        $actions_html .= " " . BtnHelper::getPrintPDF('?action=showpdf&quotation_id=' . $quot->quotationId, __('Bekijk pdf'));
        $actions_html .= " " . BtnHelper::getLogin($this->getLoginLinkForQuotation($quot, $quot->user, $mainHost), __('Wijzig offerte als klant'), '_blank');

        if (($page === self::ORDERS_NATURE_STONE_B) && in_array($quot->brandId, [5, 6, 7, 8, 9, 10, 11]) && in_array($quot->statusId, [20, 30, 35, 40]) && (empty($quot->quot_extra->supplier_show) || $quot->quot_extra->supplier_show == date('Y-m-d H:i:s'))) {
          $actions_html .= ' <a class="gsd-btn gsd-btn-small" href="?action=setshowsupplier&id=' . $quot->quot_extra->id . '&showSupplier=' . $showSupplier . '">' . $btn_text . "</a>";
        }

        $orderLink = '<a href="' . PageMap::getUrl("M_RDE_ORDERS_GENERAL") . '?id=' . $quot->quotationId . '">' . $quot->getQuotationNumberFull() . "</a>";

        $companyDisplay = $quot->user->company ?
          <<<HTML
            <a class="company-filter" style="cursor: pointer;" data-id="{$quot->user->companyId}" data-name="{$quot->user->company->name}">{$quot->user->company->name}</a>
          HTML
          : $quot->user->companyName;

        $urgencyFlag = '';
        if ($quot->urgencyFlag) {
          $urgencyFlag = IconHelper::getAlert();
        }

        $payedFlag = '';
        if ($quot->payedFlag) {
          $payedFlag = IconHelper::getCheckboxGreen("Deze offerte is betaald.");
        }

        $onHoldFlag = '';
        if ($quot->onHoldFlag) {
          $onHoldFlag = IconHelper::getAlert();
        }

        $seam_color = SeamColor::find_by(['seamColorId' => $quot->quot_extra->seamColorId]);

        $table_data[] = [
          'DT_RowId'        => $quot->quotationId,
          'statusId'        => Status::getIconHTML($quot->statusId),
          'name'            => $companyDisplay,
          'quotationDate'   => $quot->getQuotationDate(),
          'code'            => $quot->code,
          'plannedDate'     => $quot->plannedDate ? date('d-m-Y', strtotime($quot->plannedDate)) : '',
          'meters'          => $quot->meters,
          'place'           => $quot->domestic,
          'zipcode'         => $quot->zipcode,
          'quotationNumber' => $orderLink,
          'dueDateWeek'     => $quot->dueDateWeek,
          'urgencyFlag'     => $urgencyFlag,
          'payedFlag'       => $payedFlag,
          'onHoldFlag'      => $onHoldFlag,
          'seamColor'       => $seam_color->code,
          'actions'         => $actions_html,
        ];

      }

      /** RETURN DATA AS JSON */
      ResponseHelper::exitAsJson([
        'data'            => $table_data,
        'recordsTotal'    => $total_count,
        'recordsFiltered' => $total_count_filtered,
        'draw'            => (int)$_POST['draw'],
      ]);
    }

    private function buildYearDateRangeFilter(array $years): string
    {
      if (empty($years)) return '';

      $date_conditions = array_map(function($year) {
        $start_date_obj = new DateTime($year . '-01-01 00:00:00');
        $end_date_obj = (clone $start_date_obj)->modify('+1 year');

        // Using date ranges prevent us from having to use the SQL YEAR(), which is very slow on large datasets.
        return sprintf(
          "(quotationDate >= '%s' AND quotationDate < '%s')",
          $start_date_obj->format('Y-m-d H:i:s'),
          $end_date_obj->format('Y-m-d H:i:s')
        );
      }, $years);

      return "(" . implode(" OR ", $date_conditions) . ")";
    }

    public function executeShowpdf() {
      $pdfGenerator = new ProductionPdfGenerator($_GET['quotation_id']);
      $pdfGenerator->generatePdf();
    }

    private function getQuotationinfo() {
      $quotation = Quotations::find_by(["quotationId" => $_GET["id"]]);
      if (!$quotation) ResponseHelper::redirectNotFound();
      $quotationExtra = QuotationsExtra::find_by(["quotationId" => $_GET["id"]]);
      $company = CrmCompanies::find_by(["companyId" => $quotation->companyId]);
      $invoiceParty = CrmInvoiceparties::find_by(["companyId" => $quotation->companyId]);
      $sandboxuser = SandboxUsers::getById($quotation->userId);
      $mainHost = SiteHost::getMainSiteHost(1)->getDomainSmart(true);

      //button_links
      $userLoginLink = $this->getLoginLinkForQuotation($quotation, $sandboxuser, $mainHost);
      $userLoginLinkExtraProd = "$userLoginLink&extraproducts=1";
      $userLoginLinkOrderConfirm = "$userLoginLink&orderconfirm=1";

      $this->quotation = $quotation;
      $this->quotation_extra = $quotationExtra;
      $this->company = $company;
      $this->invoiceParty = $invoiceParty;
      $this->sandboxuser = $sandboxuser;
      $this->userLoginLink = $userLoginLink;
      $this->userLoginLinkExtraProd = $userLoginLinkExtraProd;
      $this->userLoginLinkOrderConfirm = $userLoginLinkOrderConfirm;
    }

    private function getLoginLinkForQuotation(Quotations $quotation, SandboxUsers $user, string $host): string {
      $userhash = urlencode(EncryptionHelper::encrypt(("RDEUSER#824yy" . date("Ydm"))));
      return "$host/offerte?email=$user->email&userhash=$userhash&offerte=$quotation->quotationId";
    }


    public function executeGeneral() {
      $quotations = Quotations::find_all_by(['statusId' => [Status::STATUS_ORDER, Status::STATUS_CHECKED, Status::STATUS_PREPARED, Status::STATUS_IN_PRODUCTION, Status::STATUS_PRODUCED, Status::STATUS_PACKED, Status::STATUS_LOADED, Status::STATUS_DELIVERED]]);

      $quotationsWithSameZip = [];
      foreach ($quotations as $quotation) {
        $zipcode = substr($quotation->zipcode, 0, -2);

        if (!in_array($zipcode, $quotationsWithSameZip)) {
          $quotationsWithSameZip[$zipcode] = [$quotation];
        }
        else {
          $quotationsWithSameZip[$zipcode][] = $quotation;
        }
      }

//      dumpe($quotationsWithSameZip);

      $this->getQuotationinfo();

      // get order elements from quotationId
      $this->elements = OrderElements::find_all_by(['quotationId' => $this->quotation->quotationId]);
      $this->calculateSawSizeForElements($this->quotation_extra, $this->elements);

      $this->totalAmount = $this->totalLength = $this->totalStones = $this->totalPrice = 0;
      foreach ($this->elements as $element) {
        $element->totalStones = $element->stoneAmount * $element->amount;
        $this->totalAmount  += $element->amount;
        $this->totalLength  += $element->elementLength * $element->amount;
        $this->totalStones  += $element->totalStones;
        $this->totalPrice   += $element->totalPrice;
      }

      $this->projects = Projects::find_all_by(['quotationId' => $this->quotation->quotationId]);

      $customerCode = CustomerCodes::find_by(['codeId' => $this->quotation->codeId]);
      $groupId = $customerCode ? $customerCode->groupId : 0;

      //-- bouwmateriaal handel voor groepen 1, 2, 8, 9, 16, 17, 18, 19, 20, 21
      $this->buildingMaterialTradingFlag = in_array($groupId, [1, 2, 8, 9, 16, 17, 18, 19, 20, 21]);

      $form = new ModelForm("general");
      $form->addClass("edit-form");
      $form->buildElementsFromModel($this->quotation);

      $select = new Select("Status", "statusId", $this->quotation->statusId);
      foreach (Status::find_all("ORDER BY statusId") as $status) {
        $select->addOption(new Option($status->statusId, $status->bedrijvengidsName));
      }
      $form->addElement($select);

      $form->setElementsLabel([
        "statusId"         => "Status",
        "productionDate"   => "Opdracht datum",
        "dueDateWeek"      => "Uiterlijke leveringsweek",
        "dueDate"          => "Uiterlijke leverdatum",
        "urgencyFlag"      => "Spoed",
        'onHoldFlag'       => "On hold",
        "payedFlag"        => "Betaald",
//        "proformaFlag" => "Proforma",
        "calledFlag"       => "Contact opgenomen",
        "sendMailToClient" => "Contact opnemen",
        "callOrEmailNotes" => "Bellen of mailen info",
        "internNotes"      => "Opmerkingen intern",
//        ""       => "Type gekozen maat",
        "projectName"      => "Project naam",
        "projectReference" => "Kenmerk",
        "productionNotes"  => "Opmerkingen productie",
        "customerNotes"    => "Opmerkingen klant",
      ], true);

      $form->getElement('productionDate')->addClass('datepicker_weeks');
      $form->getElement('dueDate')->addClass('datepicker_weeks');

      $form->addClass('form-class');

      if (isset($_POST["approved"])) {
        // check if on hold is set, is so redirect with alert message
        if ($this->quotation->onHoldFlag) {
          ResponseHelper::redirectAlertMessage("Deze bestelling staat op hold, de status mag niet omgezet worden", reconstructQuery());
        }
        $this->quotation->statusId = Status::STATUS_CHECKED;
        $this->quotation->save();
        ResponseHelper::redirectMessage("Bestelling goedgekeurd", PageMap::getUrl('M_RDE_ORDERS_STATUS_COMMISSION'));
      }

      if (isset($_POST["go"]) || isset($_POST["go_list"])) {

        $form->setElementsAndObjectValue($_POST);
        $this->setQuotationAndProjectValuesFromVue();

        if ($form->isValid()) {
          $this->handleReferenceNamesSubmission();
          $this->quotation->save();

          MessageFlashCoordinator::addMessage("Bestelling opgeslagen");
          if (isset($_POST["go_list"])) {
            ResponseHelper::redirect(PageMap::getUrl("M_RDE_ORDERS_LIST"));
          }
          ResponseHelper::redirect(reconstructQuery());

        }
      // only add conditional flash message on GET request
      } else $this->checkForMissingStoneInQuotation();

      $quotation = Quotations::find_by(['quotationId' => $this->quotation->quotationId]);

      $stone = Stones::find_by(['stoneId' => $this->quotation->stoneId]);
      $quotation->brand = StoneBrands::find_by(['brandId' => $this->quotation->brandId]);
      $quotation->stoneColor = $stone ? StoneColors::find_by(['colorId' => $stone->colorId]) : null;
      $quotation->stoneSize = $stone ? StoneSizes::find_by(['sizeId' => $stone->sizeId]) : null;
      $this->setHeartClickSizeText($quotation, $this->elements);

      $this->address = $this->quotation_extra ? CrmAddresses::find_by(['addressId' => $this->quotation_extra->addressDeliveryId]) : null;
      $this->quotation = $quotation;
      $this->form = $form;
      Context::addJavascript("https://maps.googleapis.com/maps/api/js?v=3.exp&libraries=places&key=" . LocationHelper::getGoogleMapsKey());
      Context::addJavascript('/gsdfw/includes/jsscripts/vuejs/vue.3.1.5' . (DEVELOPMENT ? '' : '.min') . '.js');
    }

    private function calculateSawSizeForElements($quotationExtra, $elements): void {
      foreach ($elements as $element) {
        $elementLength = $element->elementLength;
        $stoneWidth = $quotationExtra->stoneSizeWidth ?? 0;
        $stoneWidthPlus10 = $stoneWidth * 10;

        $joint = 3.5;
        $spacing = $stoneWidthPlus10 + $joint;
        $spacingMin = $stoneWidthPlus10 + 3;
        $spacingMax = $stoneWidthPlus10 + 4.5;

        $elementWithJoint = $elementLength + $joint;

        $stonesMin = ceil($elementWithJoint / $spacingMin);
        $stonesStd = ceil($elementWithJoint / $spacing);
        $stonesMax = ceil($elementWithJoint / $spacingMax);

        if (($stonesMin - $stonesMax) >= 1) {
          $stoneCount = $stonesMax;
          $sawSize = 0;
        } else {
          $stoneCount = $stonesStd;
          $sawSize = $stoneWidthPlus10 - (((($stonesStd * $spacing) - $joint) - $elementLength) / 2);
        }

        $element->sawSize = round($sawSize, 2);
        $element->stoneCount = $stoneCount;
      }
    }

    private function setHeartClickSizeText($quotation, $elements): void {
      $quotation->heartClickSizeText = count($elements) && reset($elements)->heartClickSize === '1'
        ? 'tot aan de punt'
        : 'hart klik gemeten';
    }

    private function handleReferenceNamesSubmission(): void {
      if (!isset($_POST['referenceName']) || !is_array($_POST['referenceName'])) return;

      $referenceNames = $_POST['referenceName'];
      $submittedNames = [];
      $updates = [];

      foreach ($referenceNames as $elementId => $newReferenceName) {
        $elementId = (int)$elementId;
        $newReferenceName = trim($newReferenceName);
        $lowerName = strtolower($newReferenceName);

        if (empty($newReferenceName)) {
          ResponseHelper::redirectAlertMessage("Referentienaam mag niet leeg zijn.", reconstructQuery());
        }
        if (preg_match('/^\d/', $newReferenceName)) {
          ResponseHelper::redirectAlertMessage("Referentienaam mag niet beginnen met een nummer: '$newReferenceName'", reconstructQuery());
        }
        if (in_array($lowerName, $submittedNames, true)) {
          ResponseHelper::redirectAlertMessage("Referentienaam moet uniek zijn: '$newReferenceName' komt meerdere keren voor.", reconstructQuery());
        }

        $submittedNames[] = $lowerName;

        // Mark element for update
        $updates[] = [
          'elementId' => $elementId,
          'referenceName' => $newReferenceName,
        ];
      }

      // All validations passed, now update in database
      foreach ($updates as $update) {
        if ($element = OrderElements::find_by(['elementId' => $update['elementId']])) {
          $element->referenceName = $update['referenceName'];
          $element->save();
        }
      }
    }

    // Handle dynamic values that where set inside Vue.js app
    private function setQuotationAndProjectValuesFromVue(): void {

      // submitted projects
      $projectsToSubmit = $_POST['projects'] ?? [];
      foreach ($projectsToSubmit as $projectToSubmit) {
        $projectToSubmit['showOnProductionPage'] = ($projectToSubmit['showOnProductionPage'] ?? false) ? 1 : 0;
        // values based on quotation
        $projectToSubmit['productFromWebshop'] = $this->quotation->createdVia === 'webshop' ? 1 : 0;
        $projectToSubmit['quotationId'] = $this->quotation->quotationId;

        // calculate totalprice (pieceprice * size)
        $projectToSubmit['euro'] = $projectToSubmit['pieceprice'] * $projectToSubmit['size'];

        // submitted projects not in database should be added, otherwise edited
        $project = Projects::find_by(['projectId' => $projectToSubmit['projectId']]) ?: new Projects();

        // set all values from Vue.js app to project and save to database
        foreach ($projectToSubmit as $key => $value) {
          $project->$key = $value ?? null;
        }
        if ($project->valid()) $project->save();
      }

      $this->quotation->specialFreightCostPrice = $_POST['specialFreightCostPrice'];
      $this->quotation->specialFreightCost = isset($_POST['specialFreightCost']) ? 1 : 0;
      $this->quotation->mattingOnlyGlueFlag = isset($_POST['mattingOnlyGlueFlag']) ? 1 : 0;
      $this->quotation->mattingRemovalDiscount = isset($_POST['mattingRemovalDiscountFlag']) ? 1 : 0;
      $this->quotation->mountingFlag = isset($_POST['mountingFlag']) ? 1 : 0;
      $this->quotation->cashPayment = isset($_POST['cashPayment']) ? 1 : 0;
    }

    public function executeContact() {
      $this->getQuotationinfo();

      $this->sand_person = false;
      $this->comp = false;
      $this->comp_address = false;

      if (!empty($this->sandboxuser->personId)) {
        $this->sand_person = CrmPersons::find_by(['personId' => $this->sandboxuser->personId]);
      }
      if ($this->company) {
        $this->comp = $this->company;
        $this->comp_address = CrmAddresses::find_by(['addressId' => $this->company->visitAddressId]);
      }

      $this->quot = $this->quotation;
      $this->sand_user = $this->sandboxuser;
    }

    public function executeProduction() {
      $this->getQuotationinfo();

      $form = new ModelForm("quotation");
      $form->addClass("edit-form");
      $form->buildElementsFromModel($this->quotation);
      $form->setElementsLabel([
        "noRackQuotations"            => "Klant wil orders enkel in bak ontvangen",
        "noContainerQuotations"       => "Klant wil orders enkel in rek ontvangen",
        "noRackNoContainerQuotations" => "Klant wil orders niet in bak of rek ontvangen",
        "palletQuotations"            => "Klant wil orders op een pallet",
        "toNumberQuotations"          => "Elementen nummeren",
        "endstone"                    => "Eindstenen",
        "mountingPrice"               => "Montage prijs",
      ], true);

      $endstoneSelector = new Select("Eindstenen", "endstone", $this->quotation->endstone);
      $endstoneSelector->addOptionHelper("false", "Nee");
      $endstoneSelector->addOptionHelper("true", "Ja");
      $endstoneSelector->addOptionHelper("true_grooves", "Ja, met groeven");
      $form->addElement($endstoneSelector);

      $formQE = new ModelForm("quotation_extra");
      $formQE->addClass("edit-form");
      $qe = $this->quotation_extra;
      if (!$this->quotation_extra) {
        $qe = new QuotationsExtra();
        $qe->quotationId = $this->quotation->quotationId;
      }
      $formQE->buildElementsFromModel($qe);
      $formQE->setElementsLabel([
        "rackContainerMark"         => "Bak/rek kenmerk",
        "stoneSizeWidth"            => "Steen maat breedte (mm)",
        "maxMeasureElement"         => "Max. lengte element (mm)",
        "seamColorId"               => "Voegkleur",
        "customerSplitRights"       => "Klant split rechten",
        "stoneOrdered"              => "Stenen besteld",
        "stoneOrderedDate"          => "Stenen besteld datum",
        "stoneDeliveryDate"         => "Leverdatum",
        "totalMiddlesStones"        => "Stenen totaal",
        "totalLeftEndStones"        => "Eindstenen links",
        "totalRightEndStones"       => "Eindstenen rechts",
        "totalLeftEndStonesGrooves" => "Eindstenen links gr.",

      ], true);

      $formQE->getElement('stoneOrderedDate')->addClass('datepicker_weeks');
      $formQE->getElement('stoneDeliveryDate')->addClass('datepicker_weeks');

      $select = new Select("Voegkleur", "seamColorId", $this->quotation_extra->seamColorId);
      foreach (SeamColor::find_all("ORDER BY seamColorId") as $color) {
        $select->addOption(new \Gsd\Form\Elements\Option($color->seamColorId, $color->name));
      }
      $formQE->addElement($select);

      $visit_address = false;
      if ($this->company) {
        $visit_address = CrmAddresses::find_by(['addressId' => $this->company->visitAddressId]);
        $formAddress = new ModelForm("visit_address");
        $formAddress->addClass("edit-form");
        if ($visit_address) {
          $formAddress->buildElementsFromModel($visit_address);
        }
        $formAddress->setElementsLabel([
          "extension" => "Adres extensie",
        ], true);
      }

      $stone = Stones::find_by(['stoneId' => $this->quotation->stoneId]);
      $brand = false;
      if (!empty($stone->brandId)) {
        $brand = StoneBrands::find_by(['brandId' => $stone->brandId]);
      }
      $color = false;
      if (!empty($stone->colorId)) {
        $color = StoneColors::find_by(['colorId' => $stone->colorId]);
      }
      $stone_size = false;
      if (!empty($stone->sizeId)) {
        $stone_size = StoneSizes::find_by(['sizeId' => $stone->sizeId]);
      }

      if (isset($_POST["go"]) || isset($_POST["go_list"])) {
        $maxMeasureElementHasChanged = $this->quotation_extra->maxMeasureElement != $_POST['quotation_extra']['maxMeasureElement'] ?? null;

        $form->setElementsAndObjectValue($_POST);
        $formQE->setElementsAndObjectValue($_POST);

        if (isset($formAddress) && $formAddress->modelobject != null) {
          $formAddress->setElementsAndObjectValue($_POST);
        }

        if ($form->isValid() && $formQE->isValid()) {
          $this->quotation->save();
          $this->quotation_extra->save();

          if ($maxMeasureElementHasChanged) {
            // this operation is expensive, so only recalculate if the maxMeasureElement has changed
            (new SplitCalculator($this->quotation, $this->quotation_extra))->run();
          }
          if ($visit_address) {
            $visit_address->save();
          }

          MessageFlashCoordinator::addMessage("Bestelling opgeslagen");
          if (isset($_POST["go_list"])) {
            ResponseHelper::redirect(PageMap::getUrl("M_RDE_ORDERS_LIST"));
          }
          ResponseHelper::redirect(reconstructQuery());

        }

      } else $this->checkForMissingStoneInQuotation();

      $this->errors = array_merge($form->getErrors(), $formQE->getErrors());
      $this->form = $form;
      $this->formQE = $formQE;
      if ($visit_address) {
        $this->formAddress = $formAddress;
      }
      $this->stone_brand = '?';
      if ($brand) {
        $this->stone_brand = $brand->name;
      }
      $this->stone_color = '?';
      if ($color) {
        $this->stone_color = $color->name;
      }
      $this->stone_size = '?';
      if ($stone_size) {
        $this->stone_size = $stone_size->getSizeString();
      }
      $this->shortLabels = [
        'noRackQuotations' => 'Bak',
        'noContainerQuotations' => 'Rek',
        'noRackNoContainerQuotations' => 'Afhaal rek',
        'palletQuotations' => 'Pallet - offerte'
      ];
    }

    public function executeSizes() {
      $this->getQuotationinfo();
      $stone = Stones::find_by(['stoneId' => $this->quotation->stoneId]);

      //get elements from quotationId
      $elements = OrderElements::find_all_by(['quotationId' => $this->quotation->quotationId]);
      $elementArr = [];
      foreach ($elements as $element) {
        $elementPart = OrderElementparts::find_all_by(['elementId' => $element->elementId]);
        $element->elementPart = $elementPart;

        $element->paintleft = $element->leftEndstone == 0 && $element->leftMitreId == 0 && $element->leftEndstoneGrooves == 0;
        $element->paintright = $element->rightEndstone == 0 && $element->rightMitreId == 0 && $element->rightEndstoneGrooves == 0;

        $leftMitre = Mitres::find_by(['mitreId' => $element->leftMitreId]);
        $element->leftMitre = $leftMitre;
        $rightMitre = Mitres::find_by(['mitreId' => $element->rightMitreId]);
        $element->rightMitre = $rightMitre;

        $elementArr[] = $element;
      }

      if (isset($_POST['go'])) {
        foreach ($elements as $element) {
          $id = $element->elementId;

          // left radio button
          $radioLeft = $_POST['radioLeft_' . $id] ?? null;
          switch ($radioLeft) {
            case 'leftEndstone':
              $element->leftEndstone = 1;
              $element->leftEndstoneGrooves = 0;
              break;
            case 'leftEndstoneGrooves':
              $element->leftEndstone = 0;
              $element->leftEndstoneGrooves = 1;
              break;
            case 'paintLeft':
              $element->leftEndstone = 0;
              $element->leftEndstoneGrooves = 0;
              break;
          }
          // right radio button
          $radioRight = $_POST['radioRight_' . $id] ?? null;
          switch ($radioRight) {
            case 'rightEndstone':
              $element->rightEndstone = 1;
              $element->rightEndstoneGrooves = 0;
              break;
            case 'rightEndstoneGrooves':
              $element->rightEndstone = 0;
              $element->rightEndstoneGrooves = 1;
              break;
            case 'paintRight':
              $element->rightEndstone = 0;
              $element->rightEndstoneGrooves = 0;
              break;
          }

          $flagLeft = isset($_POST['flagWindowLeft_' . $element->elementId]);
          $flagRight = isset($_POST['flagWindowRight_' . $element->elementId]);
          if ($flagLeft && $flagRight) {
            $element->flagWindowSide = 'both';
            $element->flagWindow = 'double';
          } elseif ($flagLeft || $flagRight) {
            $element->flagWindowSide = $flagLeft ? 'left' : 'right';
            $element->flagWindow = 'single';
          } else {
            $element->flagWindowSide = 'none';
            $element->flagWindow = 'false';
          }
          if (isset($_POST['elementLength_' . $element->elementId])) {
            $element->elementLength = $_POST['elementLength_' . $element->elementId];
          }
          $element->save();
        }
        (new SplitCalculator($this->quotation, $this->quotation_extra))->run();

        MessageFlashCoordinator::addMessage("Maten zijn opgeslagen");
        ResponseHelper::redirect(reconstructQuery());
      } else $this->checkForMissingStoneInQuotation();

      $this->elements = $elementArr;
      $this->stone = $stone;
    }

    public function executeDelivery() {
      $this->getQuotationinfo();

      $this->deliveryAddress = CrmAddresses::find_by(['addressId' => $this->quotation_extra->addressDeliveryId]);
      $this->pickupAddress = CrmAddresses::find_by(['addressId' => 20357]);
      $this->visitAddress = CrmAddresses::find_by(['addressId' => $this->company?->visitAddressId]);
      $this->mapAdress = CrmAddresses::find_by(['addressId' => $this->quotation_extra->addressDeliveryId, 'type' => CrmAddresses::TYPE_MAP]);

      // all delivery addresses for the company
      $this->deliveryAddresses = CrmAddresses::find_all_by(['companyId' => $this->company?->companyId, 'type' => CrmAddresses::TYPE_DELIVERY]);

      $deliveryId = $this->deliveryAddress?->addressId ?? null;
      $pickupId = $this->pickupAddress?->addressId ?? null;
      $mapAddressId = $this->mapAdress?->addressId ?? null;

      // based on quot extra, determine which address to select
      $this->selectedAddress = match ($this->quotation_extra->addressDeliveryId) {
        $this->visitAddress->addressId => $this->visitAddress,
        $pickupId => $this->pickupAddress,
        $deliveryId => $this->deliveryAddress,
        $mapAddressId => $this->mapAdress,
        default => null,
      };

      $executors = CrmPersons::find_all_by(['companyId' => $this->quotation->companyId, 'flagForExecutor' => 1]);

      $executor_arr = [];
      foreach ($executors as $key => $executor) {
        $arr = [
          'name'   => $executor->firstName . ' ' . $executor->lastName,
          'mobile' => $executor->mobile,
          'email'  => $executor->email,
        ];

        $executor_arr[$executor->personId] = $arr;
      }

      $personsUrl = PageMap::getUrl("M_BEDRIJVENGIDS_PERSONS");
      $this->addExecutorLink = "$personsUrl?action=personedit&companyid={$this->company->companyId}&executor=1";

      $relativeDir = "/filesystem/raamdorpel/clients/quotation/delivery/{$this->quotation->quotationId}/";
      $uploadDir = DIR_ROOT_HTTPDOCS . $relativeDir;

      $uploader = new Uploader('appendix', reconstructQueryAdd(['pageId']), $uploadDir);
      $uploader->setAllowed([
        'application/pdf' => 'pdf',
      ]);

      // get appendix file for quotation
      $this->files = Files::find_all_by(['quotationId' => $this->quotation->quotationId, 'categoryId' => Files::CATEGORY_DELIVERY]);

      if (isset($_POST['save_address'])) {
        $this->handleAddressSelection();
      }

      if (isset($_POST['save_executor'])) {
        $this->handleFileUpload($uploader, $uploadDir, $relativeDir, Files::CATEGORY_DELIVERY);

        $this->quotation->executor = trim($_POST['name']);
        $this->quotation->executorMobile = trim($_POST['tel']);
        $this->quotation->executorMail = trim($_POST['email']);
        $this->quotation->executorPersonId = trim($_POST['personId']);
        if (!empty($_POST['deliveryNotes'])) {
          $this->quotation->deliveryNotes = $_POST['deliveryNotes'];
        }

        $this->quotation->save();
        $this->quotation_extra->save();

        MessageFlashCoordinator::addMessage("Uitvoerder succesvol opgeslagen");
        ResponseHelper::redirect(reconstructQuery());
      }

      $this->executors = $executors;
      $this->executor_arr = $executor_arr;
      $this->uploader = $uploader;

      Context::addJavascript("https://maps.googleapis.com/maps/api/js?v=3.exp&libraries=places&key=" . LocationHelper::getGoogleMapsKey());
    }


    private function handleAddressSelection(): void {
      $selected = $_POST['address_select'] ?? null;

      switch ($selected) {
        case 'visit':
          $this->quotation_extra->addressDeliveryId = $this->company->visitAddressId;
          break;

        case 'pickup':
          $this->quotation_extra->addressDeliveryId = $this->pickupAddress->addressId;
          break;

        case 'custom':
          $this->handleCustomMapAddress();
          break;

        default:
          if (str_starts_with($selected ?? '', 'delivery_')) {
            $this->quotation_extra->addressDeliveryId = (int) str_replace('delivery_', '', $selected);
          }
          break;
      }

      // Save quotation extra and handle dispatch appointment
      $this->quotation_extra->save();

      if ($dispatchAppointment = $_POST['dispatchAppointment'] ?? null) {
        $this->quotation->dispatchAppointment = $dispatchAppointment;
        $this->quotation->save();
      }

      MessageFlashCoordinator::addMessage("Adresgegevens succesvol opgeslagen");
      ResponseHelper::redirect(reconstructQuery());
    }

    private function handleCustomMapAddress(): void
    {
      // Validate required fields
      $requiredFields = ['street' => 'Straat', 'number' => 'Nummer', 'zip' => 'Postcode', 'city' => 'Stad'];
      $missingFields = array_filter($requiredFields, fn($field) => empty(trim($_POST[$field] ?? '')), ARRAY_FILTER_USE_KEY);

      if ($missingFields) {
        MessageFlashCoordinator::addMessageAlert("Vul alle verplichte velden in: " . implode(', ', $missingFields));
        ResponseHelper::redirect(reconstructQuery());
        return;
      }

      // Prepare address data
      $addressData = [
        'street' => trim($_POST['street']),
        'nr' => trim($_POST['number']),
        'extension' => trim($_POST['extension'] ?? ''),
        'zipcode' => trim($_POST['zip']),
        'domestic' => trim($_POST['city']),
        'country' => trim($_POST['country'] ?? ''),
        'latitude' => trim($_POST['lat'] ?? ''),
        'longitude' => trim($_POST['lng'] ?? '')
      ];

      // Try to update existing map address first
      $currentAddress = CrmAddresses::find_by(['addressId' => $this->quotation_extra->addressDeliveryId, 'type' => CrmAddresses::TYPE_MAP]);
      if ($currentAddress) {
        foreach ($addressData as $field => $value) {
          $currentAddress->$field = $value;
        }
        $currentAddress->save();
      } else {
        // Check for existing identical address
        $existingAddress = CrmAddresses::find_by([
          'street' => $addressData['street'],
          'nr' => $addressData['nr'],
          'zipcode' => $addressData['zipcode'],
          'companyId' => $this->company->companyId,
          'type' => CrmAddresses::TYPE_MAP
        ]);

        if ($existingAddress) {
          $this->quotation_extra->addressDeliveryId = $existingAddress->addressId;
        } else {
          // Create new address
          $newAddress = new CrmAddresses();
          $newAddress->companyId = $this->company->companyId;
          $newAddress->type = CrmAddresses::TYPE_MAP;
          foreach ($addressData as $field => $value) {
            $newAddress->$field = $value;
          }
          $newAddress->save();
          $this->quotation_extra->addressDeliveryId = $newAddress->addressId;
        }
      }
    }

    private function handleFileUpload($uploader, $uploadDir, $relativeDir, $fileCategory): void {
      // if path to file doesn't exist, create it
      if (!file_exists($uploadDir)) mkdir($uploadDir, 0777, true);

      // upload file to directory
      $fileWasUploaded = $uploader->parseUpload();
      if (!$fileWasUploaded) return;

      $file = new Files();

      $file->quotationId = $this->quotation->quotationId;
      $file->categoryId = $fileCategory;
      $file->companyId = $this->quotation->companyId;
      $file->uploadDate = date('Y-m-d H:i:s');
      $file->typeId = 1;
      $file->title = $uploader->getOriginalfilename();
      $file->filename = basename($uploader->getFilelocation());
      $file->folder = $relativeDir;

      // save file to db
      $file->save();
    }

    public function executeOpenAppendix() {
      $file = Files::find_by(['fileId' => $_GET['file_id']]);
      // zoiets zou het moeten zijn?
      $path = DIR_ROOT_HTTPDOCS . "$file->folder/$file->filename";

      FileHelper::getFileAndOuput($path);
      ResponseHelper::exit();
    }

    public function executeDeleteAppendix() {
      try {
        // check if file exists in db
        $file = Files::find_by(['fileId' => $_GET['file_id']]);
        if (!$file) throw new Exception("Bestand niet gevonden");

        // check if file exists in filesystem
        $path = DIR_ROOT_HTTPDOCS . "$file->folder/$file->filename";
        if (!file_exists($path)) throw new Exception("Bestand niet gevonden");

        // remove file from filesystem and db
        if (unlink($path)) $file->destroy();
        ResponseHelper::redirectMessage("Bestand verwijderd", reconstructQueryAdd(['id' => $_GET['id']]));
      } catch (Exception) {
        ResponseHelper::redirectMessage("Bestand kon niet worden verwijderd", reconstructQueryAdd(['id' => $_GET['id']]));
      }
    }

    public function executeDeleteProject () {
      $projectId = DbHelper::escape($_GET['projectId']);
      $project = Projects::find_by(['projectId' => $projectId]);
      if ($project) {
        $project->destroy();
        MessageFlashCoordinator::addMessage("Project succesvol verwijderd");
      } else {
        MessageFlashCoordinator::addMessageAlert("Project niet gevonden");
      }
      ResponseHelper::redirect(reconstructQueryAdd(['id' => $_GET['quotationId']]));
    }

    public function executeFindlatlng() {
      $countries = Organisation::getCountries();

      $zipcode = '';
      $street = '';
      $nr = '';
      $city = '';
      $countrysub = '';

      if (isset($_GET['street']))
        $street = $_GET['street'];
      if (isset($_GET['nr']))
        $nr = $_GET['nr'];
      if (isset($_GET['zipcode']))
        $zipcode = $_GET['zipcode'];
      if (isset($_GET['city']))
        $city = $_GET['city'];
      if (isset($_GET['country']) && $_GET['country'] != '')
        $countrysub = $_GET['country'];

      $country = "Nederland";
      if ($countrysub != "" && isset($countries[$countrysub])) {
        $country = $countries[$countrysub];
      }
      if ($country == 'Engeland') { //@todo: kan dit niet weg? is niet hetzelfde
        $country = 'Verenigd Koninkrijk';
      }

      $latlng = LocationHelper::retrieveLatLngGoogle($zipcode, $street . ' ' . $nr, $city, $country);
      ResponseHelper::exitAsJson($latlng);

    }

//    public function executeInvoice() {
//      $this->getQuotationinfo();
//
//      $quotations = Quotations::find_all_by(['companyId' => $this->quotation->companyId], "");
//
//      function cb($a, $b) {
//        return strtotime($a->quotationDate) - strtotime($b->quotationDate);
//      }
//      usort($quotations, 'cb');
//      dumpe(array_reverse($quotations));
//    }

    public function executeInvoice() {
      $this->getQuotationinfo();
      $this->createInvoiceListFilters($this->quotation->userId);
      $this->quotation->averagePaymentTerm = $this->getAveragePaymentTerm($this->quotation->userId);

      $relativeDir = "/filesystem/raamdorpel/clients/quotation/invoice/{$this->quotation->quotationId}/";
      $uploadDir = DIR_ROOT_HTTPDOCS . $relativeDir;
      $uploader = new Uploader('appendix', reconstructQueryAdd(['pageId']), $uploadDir);
      $uploader->setAllowed([
        'application/pdf' => 'pdf',
      ]);

      // get appendix file for quotation
      $this->files = Files::find_all_by(['quotationId' => $this->quotation->quotationId, 'categoryId' => Files::CATEGORY_INVOICE]);
      $this->uploader = $uploader;

      // get cargo receipt for quotation
      $this->cargoReceipt = $this->getCargoReceipt($this->quotation->quotationId);

      if (isset($_POST['go']) || isset($_POST['go_list'])) {
        $this->handleFileUpload($uploader, $uploadDir, $relativeDir, Files::CATEGORY_INVOICE);

        if ($this->cargoReceipt) {
          $this->cargoReceipt->specialAgreementsRead = isset($_POST['specialAgreementsRead']) ? 1 : 0;
          $this->cargoReceipt->save();
        }

        $this->quotation_extra->executorTicket = isset($_POST['executorTicket']) ? 1 : 0;
        $this->quotation_extra->quoteInvoiceAlertFlag = isset($_POST['quoteInvoiceAlertFlag']) ? 1 : 0;
        if (isset($_POST['quoteInvoiceAlertInfo'])) {
          $this->quotation_extra->quoteInvoiceAlertInfo = $_POST['quoteInvoiceAlertInfo'];
        }

        $this->quotation_extra->save();
        ResponseHelper::redirectMessage("Gegevens opgeslagen", reconstructQuery());
      } else $this->checkForMissingStoneInQuotation();
    }

    private function getCargoReceipt($quotId): ?CargoReceipt {
      $gpsLink = GpsbuddyRde::find_by(['quotationId' => $quotId]);
      if (!$gpsLink) return null;

      $route = GpsbuddyRoutes::find_by(['routeId' => $gpsLink->routeId]);
      if (!$route) return null;

      $cargoReceipt = CargoReceipt::find_by(['cargoReceiptId' => $route->cargoReceiptId]);
      if (!$cargoReceipt) return null;

      return $cargoReceipt;
    }

    private function getAveragePaymentTerm($userId): string {
      $invoices = Invoices::find_all_by(['userId' => $userId], 'AND paid IS NOT NULL AND dateInvoice IS NOT NULL');

      $totalDays = 0;
      $invoiceCount = 0;

      foreach ($invoices as $invoice) {
        try {
          $dateInvoice = new DateTime($invoice->dateInvoice);
          $datePaid = new DateTime($invoice->paid);
          $interval = $dateInvoice->diff($datePaid);
          $totalDays += (int) $interval->format('%a');
          $invoiceCount++;
        } catch (Exception) {
          continue;
        }
      }

      if ($invoiceCount === 0) return 'Er zijn nog geen facturen aanwezig.';

      $averageDays = round($totalDays / $invoiceCount, 2);
      return $averageDays . ' dagen';
    }

    public function createInvoiceListFilters($user_id) {

      $dataTable = new DataTable('invoices');
      $dataTable->setRequestUrl(reconstructQueryAdd() . "action=invoicelistajax&userId=" . $user_id);
      $dataTable->addColumnHelper("invoiceNumber", "Factuurnummer");
      $dataTable->addColumnHelper("dateInvoice", "Datum");
      $dataTable->addColumnHelper("totalProjectValue", "Bedrag");
      $dataTable->addColumnHelper("paid", "Betaal datum");
      $dataTable->addColumnHelper("reminder1", "Aanmaning 1");
      $dataTable->addColumnHelper("reminder2", "Aanmaning 2");
      $dataTable->addColumnHelper("reminder3", "Aanmaning 3");
      $dataTable->addColumnHelper("onHoldFlag", "Aantal dagen");
      $dataTable->addColumnHelper("invoiceNotes", "Opmerking");

      $dataTable->setDefaultSort("dateInvoice", "desc");

      //$dataTable->getColumn("actions")->setClass("docs_actions");

      $dataTable->addSearchInput();
      $dataTable->addSearchReset();

      //$sql .= " ORDER BY orderByNum DESC, q.quotationPart DESC, q.nextRouteDate DESC ";


      $dataTable->handleRequest($_POST);

      $this->dataTable2 = $dataTable;
    }

    public function executeInvoiceListajax() {
      $this->createInvoiceListFilters($_GET['userId']);

      /** FILTER QUERY */

      $filter_query = 'WHERE invoices.userId =' . $_GET['userId'] . ' ';

      if ($this->dataTable2->hasFormElementValue("search")) {
        $searchstr = escapeForDB($this->dataTable2->getFormElementValue("search"));
        $filter_query .= "AND ( ";
        $filter_query .= "OR invoices.invoiceId LIKE '%" . $searchstr . "%' ";
        $filter_query .= "OR invoices.invoiceNumber LIKE '%" . $searchstr . "%' ";
        $filter_query .= ") ";
      }

      /** TOTALS */
      $total_count = Invoices::count_all_by([]);
      $total_count_filtered = Invoices::count_all_by([], $filter_query);

      /** GET DATA */
      $query = "SELECT * FROM " . Invoices::getTablename() . " ";
      $query .= $filter_query;
      $query .= $this->dataTable2->getSortQuery();
      $query .= $this->dataTable2->getPager()->getLimitQuery();

      $result = DBConn::db_link()->query($query);

      $invoices = [];
      while ($row = $result->fetch_array()) {
        $column_count = 0;

        $invoice = (new Invoices())->hydrateNext($row, $column_count);

        $invoices[] = $invoice;
      }

//      $companies = AppModel::mapObjectIds(CrmCompanies::find_all_by(["companyId"=>$companyIds]), "companyId");

      $table_data = [];
      foreach ($invoices as $invoice) {

        $table_data[] = [
          'DT_RowId'          => $invoice->invoiceId,
          'invoiceNumber'     => $invoice->invoiceNumber,
          'dateInvoice'       => $invoice->getDateInvoice(),
          'totalProjectValue' => StringHelper::asMoney($invoice->totalProjectValue),
          'paid'              => $invoice->getPaid(),
          'reminder1'         => $invoice->getReminder1(),
          'reminder2'         => $invoice->getReminder2(),
          'reminder3'         => $invoice->getReminder3(),
          'onHoldFlag'        => $onHoldFlag,
          'invoiceNotes'      => $invoice->invoiceNotes,
        ];

      }

      /** RETURN DATA AS JSON */
      ResponseHelper::exitAsJson([
        'data'            => $table_data,
        'recordsTotal'    => $total_count,
        'recordsFiltered' => $total_count_filtered,
        'draw'            => (int)$_POST['draw'],
      ]);
    }

    public function executeOther() {
      $this->getQuotationinfo();

      $formOther = new ModelForm("quotation_extra");
      $formOther->addClass("edit-form");
      $formOther->buildElementsFromModel($this->quotation);
      $formOther->setElementsLabel([
        "metersMuch"    => "Grote werkenlijst",
        "largeWorkList" => "Grote werkenlijst tabblad",
        "internNotes"   => "Opmerkingen RDE",
        "companyId"     => "Company Id",
      ], true);

      $select = new Select("GroteWerkenLijst", "largeWorkList", $this->quotation->largeWorkList);
      $select->addOption(new \Gsd\Form\Elements\Option('overzicht', 'Overzicht'));
      $select->addOption(new \Gsd\Form\Elements\Option('gekregen', 'Gekregen'));
      $select->addOption(new \Gsd\Form\Elements\Option('onderaannemer', 'Onderaannemer'));
      $select->addOption(new \Gsd\Form\Elements\Option('via handel', 'Via handel'));
      $select->addOption(new \Gsd\Form\Elements\Option('niet gekregen', 'Niet gekregen'));
      $select->addOption(new \Gsd\Form\Elements\Option('concurrent', 'Concurrent'));
      $select->addOption(new \Gsd\Form\Elements\Option('te duur', 'Te duur'));
      $select->addOption(new \Gsd\Form\Elements\Option('handel', 'Handel'));
      $select->addOption(new \Gsd\Form\Elements\Option('ander product', 'Ander product'));
      $select->addOption(new \Gsd\Form\Elements\Option('geannuleerd', 'Geannuleerd'));
      $select->addOption(new \Gsd\Form\Elements\Option('anders', 'Anders'));

      $formOther->addElement($select);

      $employees = $this->getEmployeesFromQuotation($this->quotation);

      if (isset($_POST['change_quotation'])) {
        ResponseHelper::redirect(reconstructQuery());
      }

      if (isset($_POST['extra_products'])) {
        ResponseHelper::redirect(reconstructQuery());
      }

      if (isset($_POST['order_page'])) {
        ResponseHelper::redirect(reconstructQuery());
      }

      if (isset($_POST["go"]) || isset($_POST["go_list"])) {

        $formOther->setElementsAndObjectValue($_POST);

        if ($formOther->isValid()) {
          $this->quotation->save();

          MessageFlashCoordinator::addMessage("Bestelling opgeslagen");
          if (isset($_POST["go_list"])) {
            ResponseHelper::redirect(PageMap::getUrl("M_RDE_ORDERS_LIST"));
          }
          ResponseHelper::redirect(reconstructQuery());

        }
      } else $this->checkForMissingStoneInQuotation();

      $this->formOther = $formOther;
      $this->employees = $employees;
    }

    private function getEmployeesFromQuotation($quotation): array {
      $quotationId = DbHelper::escape($quotation->quotationId);
      $employeeQuotation = EmployeeQuotation::getTablename();
      $productionEmployee = ProductionEmployees::getTablename();

      $query = <<<SQL
        SELECT *
        FROM $employeeQuotation AS eq
        LEFT JOIN $productionEmployee AS pe ON eq.employeeId = pe.employeeId
        WHERE eq.quotationId = $quotationId
       SQL;

      $result = DBConn::db_link()->query($query);
      $employees = [];
      while ($row = $result->fetch_array()) {
        $column_count = 0;
        $employee = (new EmployeeQuotation())->hydrateNext($row, $column_count);
        $employee->productionEmployee = (new ProductionEmployees())->hydrateNext($row, $column_count);
        $employee->date = DateTime::createFromFormat('Y-m-d H:i:s', $employee->enddate);
        $employees[] = $employee;
      }
      return $employees;
    }

    public function executelistContainers() {
      if (isset($_GET['companyid'])) {
        // Remove the query param and store it in the session
        $_SESSION['containers']['form-containers']['companyid'] = $_GET['companyid'];
        ResponseHelper::redirect(reconstructQueryAdd());
      }

      $this->createListContainersFilters();
    }

    public function createListContainersFilters() {

      $dataTable2 = new DataTable('containers');

      $companyId = $_SESSION['containers']['form-containers']['companyid'] ?? '';

      // send the company id to the ajax request to filter if applicable
      $dataTable2->setRequestUrl(reconstructQueryAdd() . "action=listcontainersajax&companyid=$companyId");

      // Make only direct columns of containers table sortable
      $dataTable2->addColumnHelper("inStock", "Aanwezig");
      $dataTable2->addColumnHelper("nextroute", "Ophalen")->setSortable(false);
      $dataTable2->addColumnHelper("containerNumber", "Bak nummer");
      $dataTable2->addColumnHelper("container_tag", "Bak NFC")->setSortable(false);
      $dataTable2->addColumnHelper("type", "Type");
      $dataTable2->addColumnHelper("containerId", "Bak ID")->setSortable(false);
      $dataTable2->addColumnHelper("quotationNr", "Offerte nummer")->setSortable(false);
      $dataTable2->addColumnHelper("quotationId", "Offerte ID")->setSortable(false);
      $dataTable2->addColumnHelper("containerQuotationId", "Bak-offerte ID")->setSortable(false);
      $dataTable2->addColumnHelper("companyName", "Bedrijfsnaam")->setSortable(false);
      $dataTable2->addColumnHelper("domestic", "Plaats")->setSortable(false);
      $dataTable2->addColumnHelper("actions", "Acties")->setSortable(false);

      $dataTable2->setDefaultSort("containerNumber", "desc");

//      $dataTable2->getColumn("name")->setSortable(false);
//      $dataTable2->getColumn("seamColor")->setSortable(false);
//      $dataTable2->getColumn("actions")->setSortable(false);
      //$dataTable2->getColumn("actions")->setClass("docs_actions");

      $dataTable2->addSearchInput();
      $dataTable2->addSearchReset();

      //$sql .= " ORDER BY orderByNum DESC, q.quotationPart DESC, q.nextRouteDate DESC ";


      $dataTable2->handleRequest($_POST);

      $this->dataTable2 = $dataTable2;
    }

    public function executeListContainersajax() {
      $this->createListContainersFilters();

      $c = Containers::getTablename();
      $cq = ContainersQuotations::getTablename();
      $q = Quotations::getTablename();
      $ct = ContainerTag::getTablename();
      $s = SandboxUsers::getTablename();

      $genericSearch = $this->dataTable2->hasFormElementValue("search") ?
        escapeForDB($this->dataTable2->getFormElementValue("search")) : '';

      // ignore the version and part of the quotation number when searching
      $quotationNumberSearch = explode('-', $genericSearch)[0];

      $companyId = isset($_GET['companyid']) ? escapeForDB($_GET['companyid']) : '';
      $companySearch = $_GET['companyid'] ?
        " AND $s.companyId = $companyId " : '';


      /** FILTER QUERY */
      $filterQuery = <<<SQL
        LEFT JOIN $cq ON $c.containerId = $cq.containerId AND $cq.returnDate IS NULL
        LEFT JOIN $q ON $cq.quotationId = $q.quotationId
        LEFT JOIN $ct ON $c.containerId = $ct.containerId
        LEFT JOIN $s ON $s.userId = $q.userId
        WHERE ($c.containerNumber LIKE '%$genericSearch%'
            OR $q.quotationNumber LIKE '%$quotationNumberSearch%'
            OR $s.companyName LIKE '%$genericSearch%')
            $companySearch
        GROUP BY $c.containerId
      SQL;

      /** TOTALS */
      $total_count = Containers::count_all_by([]);
      $total_count_filtered = Containers::count_all_by([], $filterQuery);

      /** GET DATA */
      $query = "SELECT * FROM " . Containers::getTablename() . " ";
      $query .= $filterQuery;
      $query .= $this->dataTable2->getSortQuery();
      $query .= $this->dataTable2->getPager()->getLimitQuery();

      $result = DBConn::db_link()->query($query);

      $containers = [];
      while ($row = $result->fetch_array()) {
        $column_count = 0;

        $container = (new Containers())->hydrateNext($row, $column_count);
        $container_quotation = (new ContainersQuotations())->hydrateNext($row, $column_count);
        $quotation = (new Quotations())->hydrateNext($row, $column_count);
        $container_tag = (new ContainerTag())->hydrateNext($row, $column_count);
        $sandbox_user = (new SandboxUsers())->hydrateNext($row, $column_count);

        $container_tag_check = 'Nee';
        if (!empty($container_tag->tagId)) {
          $container_tag_check = 'Ja';
        }

        $container->container_tag = $container_tag_check;
        $container->pickup = $container_quotation->nextroute === 'true';
        $container->quotationNr = $quotation->getQuotationNumberFull();
        $container->companyId = $sandbox_user->companyId;
        $container->companyName = $sandbox_user->companyName;
        $container->containerQuotationId = $container_quotation->containerQuotationId;
        $container->quotationId = $quotation->quotationId;
        $container->domestic = $quotation->domestic;

        $containers[] = $container;

      }

      $table_data = [];
      foreach ($containers as $cont) {

        $actions_html = ' ' . BtnHelper::getEdit(reconstructQueryAdd(['action' => 'containerOverview', 'containerid' => $cont->containerId]));

        //container in stock?
        if ($cont->inStock == 'Y') {
          $cont_in_stock = '<span title="Aanwezig" class="gsd-svg-icon gsd-check"><svg style="color: limegreen;" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"><path d="M9.89557 13.4982L7.79487 11.2651C7.26967 10.7068 6.38251 10.7068 5.85731 11.2651C5.37559 11.7772 5.37559 12.5757 5.85731 13.0878L9.74989 17.2257C10.1448 17.6455 10.8118 17.6455 11.2066 17.2257L18.1427 9.85252C18.6244 9.34044 18.6244 8.54191 18.1427 8.02984C17.6175 7.47154 16.7303 7.47154 16.2051 8.02984L11.061 13.4982C10.7451 13.834 10.2115 13.834 9.89557 13.4982Z" fill="currentColor"/></svg></span>';
        }
        elseif ($cont->inStock == 'W') {
          $cont_in_stock = '<span title="Wachtend" class="gsd-svg-icon gsd-check"><svg style="color: darkorange;" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"><path d="M9.89557 13.4982L7.79487 11.2651C7.26967 10.7068 6.38251 10.7068 5.85731 11.2651C5.37559 11.7772 5.37559 12.5757 5.85731 13.0878L9.74989 17.2257C10.1448 17.6455 10.8118 17.6455 11.2066 17.2257L18.1427 9.85252C18.6244 9.34044 18.6244 8.54191 18.1427 8.02984C17.6175 7.47154 16.7303 7.47154 16.2051 8.02984L11.061 13.4982C10.7451 13.834 10.2115 13.834 9.89557 13.4982Z" fill="currentColor"/></svg></span>';
        }
        else {
          $cont_in_stock = '<span title="Niet aanwezig" class="gsd-svg-icon gsd-check"><svg style="color: red;" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"><path d="M9.89557 13.4982L7.79487 11.2651C7.26967 10.7068 6.38251 10.7068 5.85731 11.2651C5.37559 11.7772 5.37559 12.5757 5.85731 13.0878L9.74989 17.2257C10.1448 17.6455 10.8118 17.6455 11.2066 17.2257L18.1427 9.85252C18.6244 9.34044 18.6244 8.54191 18.1427 8.02984C17.6175 7.47154 16.7303 7.47154 16.2051 8.02984L11.061 13.4982C10.7451 13.834 10.2115 13.834 9.89557 13.4982Z" fill="currentColor"/></svg></span>';
        }

        $pickupDisplay = '<input type="checkbox" name="pickup" value="1" ' . ($cont->pickup ? 'checked' : '') . ' disabled>';
        $tagDisplay = $cont->container_tag ?
          '<span title="Ja" class="gsd-svg-icon gsd-check"><svg style="color: limegreen;" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"><path d="M9.89557 13.4982L7.79487 11.2651C7.26967 10.7068 6.38251 10.7068 5.85731 11.2651C5.37559 11.7772 5.37559 12.5757 5.85731 13.0878L9.74989 17.2257C10.1448 17.6455 10.8118 17.6455 11.2066 17.2257L18.1427 9.85252C18.6244 9.34044 18.6244 8.54191 18.1427 8.02984C17.6175 7.47154 16.7303 7.47154 16.2051 8.02984L11.061 13.4982C10.7451 13.834 10.2115 13.834 9.89557 13.4982Z" fill="currentColor"/></svg></span>' :
          '<span title="Nee" class="gsd-svg-icon gsd-check"><svg style="color: red;" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"><path d="M9.89557 13.4982L7.79487 11.2651C7.26967 10.7068 6.38251 10.7068 5.85731 11.2651C5.37559 11.7772 5.37559 12.5757 5.85731 13.0878L9.74989 17.2257C10.1448 17.6455 10.8118 17.6455 11.2066 17.2257L18.1427 9.85252C18.6244 9.34044 18.6244 8.54191 18.1427 8.02984C17.6175 7.47154 16.7303 7.47154 16.2051 8.02984L11.061 13.4982C10.7451 13.834 10.2115 13.834 9.89557 13.4982Z" fill="currentColor"/></svg></span>';

        $orderLink = $cont->quotationId ? '<a href="' . PageMap::getUrl("M_RDE_ORDERS_GENERAL") . '?id=' . $cont->quotationId . '">' . $cont->quotationNr . "</a>" : '';

        $table_data[] = [
          'DT_RowId'        => $cont->containerId,
          'inStock'         => $cont_in_stock,
          'nextroute'       => $pickupDisplay,
          'containerNumber' => $cont->containerNumber,
          'containerId'     => $cont->containerId,
          'container_tag'   => $tagDisplay,
          'type'            => $cont->type,
          'quotationNr'     => $orderLink,
          'quotationId'     => $cont->quotationId,
          'containerQuotationId' => $cont->containerQuotationId ?? 'Deze container is niet gekoppeld',
          'companyName'     => $cont->companyName ?? '',
          'domestic'        => $cont->domestic,
          'actions'         => $actions_html,
        ];
      }

      /** RETURN DATA AS JSON */
      ResponseHelper::exitAsJson([
        'data'            => $table_data,
        'recordsTotal'    => $total_count,
        'recordsFiltered' => $total_count_filtered,
        'draw'            => (int)$_POST['draw'],
      ]);
    }

    public function executeContainerOverview() {
      $container = Containers::find_by(['containerId' => $_GET['containerid']]);
      $containerQuotations = ContainersQuotations::find_all_by(['containerId' => $container->containerId], "ORDER BY deliverDate DESC, returnDate DESC");

      foreach ($containerQuotations as $containerQuotation) {
        $containerQuotation->quotation = Quotations::find_by(['quotationId' => $containerQuotation->quotationId]);

        if ($containerQuotation->quotation->companyId) {
          $containerQuotation->company = CrmCompanies::find_by(['companyId' => $containerQuotation->quotation->companyId]);
        }

        if ($container->inStock == 'N' && isset($containerQuotation->returnDate)) {
          $cont_in_stock = '<span title="Niet aanwezig" class="gsd-svg-icon gsd-check"><svg style="color: red;" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"><path d="M9.89557 13.4982L7.79487 11.2651C7.26967 10.7068 6.38251 10.7068 5.85731 11.2651C5.37559 11.7772 5.37559 12.5757 5.85731 13.0878L9.74989 17.2257C10.1448 17.6455 10.8118 17.6455 11.2066 17.2257L18.1427 9.85252C18.6244 9.34044 18.6244 8.54191 18.1427 8.02984C17.6175 7.47154 16.7303 7.47154 16.2051 8.02984L11.061 13.4982C10.7451 13.834 10.2115 13.834 9.89557 13.4982Z" fill="currentColor"/></svg></span>';
        }
        elseif ($container->inStock == 'W' && isset($containerQuotation->returnDate)) {
          $cont_in_stock = '<span title="Wachtend" class="gsd-svg-icon gsd-check"><svg style="color: darkorange;" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"><path d="M9.89557 13.4982L7.79487 11.2651C7.26967 10.7068 6.38251 10.7068 5.85731 11.2651C5.37559 11.7772 5.37559 12.5757 5.85731 13.0878L9.74989 17.2257C10.1448 17.6455 10.8118 17.6455 11.2066 17.2257L18.1427 9.85252C18.6244 9.34044 18.6244 8.54191 18.1427 8.02984C17.6175 7.47154 16.7303 7.47154 16.2051 8.02984L11.061 13.4982C10.7451 13.834 10.2115 13.834 9.89557 13.4982Z" fill="currentColor"/></svg></span>';
        }
        else {
          $cont_in_stock = '<span title="Aanwezig" class="gsd-svg-icon gsd-check"><svg style="color: limegreen;" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"><path d="M9.89557 13.4982L7.79487 11.2651C7.26967 10.7068 6.38251 10.7068 5.85731 11.2651C5.37559 11.7772 5.37559 12.5757 5.85731 13.0878L9.74989 17.2257C10.1448 17.6455 10.8118 17.6455 11.2066 17.2257L18.1427 9.85252C18.6244 9.34044 18.6244 8.54191 18.1427 8.02984C17.6175 7.47154 16.7303 7.47154 16.2051 8.02984L11.061 13.4982C10.7451 13.834 10.2115 13.834 9.89557 13.4982Z" fill="currentColor"/></svg></span>';
        }

        $containerQuotation->cont_in_stock = $cont_in_stock;
      }

      $form = new ModelForm("container_form");
      $form->addClass("edit-form");
      $form->buildElementsFromModel($container);

      $form->setElementsLabel([
        "action"            => "Actie",
        "actionRemark"      => "Actie opmerking",
        "actionRemarkExtra" => "Notities",
      ], true);
//      $form->setElementsReadonly([
//        "quotationDate",
//        "shorter"
//      ]);


      if (isset($_POST["go"]) || isset($_POST["go_list"])) {

        $form->setElementsAndObjectValue($_POST);

        if ($form->isValid()) {
          $container->save();

          MessageFlashCoordinator::addMessage("Container opgeslagen");
          if (isset($_POST["go_list"])) {
            ResponseHelper::redirect(PageMap::getUrl("M_RDE_ORDERS_CONTAINERS_OVERVIEW"));
          }
          ResponseHelper::redirect(reconstructQuery());

        }

      }

      $this->form = $form;
      $this->container = $container;
      $this->containerQuotations = $containerQuotations;
    }

    function executeSetshowsupplier() {
      $quotation_extra = QuotationsExtra::find_by(['id' => $_GET['id']]);
      if (!$quotation_extra) {
        ResponseHelper::redirectAlertMessage('Geen quotation_extra gevonden', reconstructQueryAdd());
      }

      if ($_GET['showSupplier'] == 'true') {
        $quotation_extra->supplier_show = null;
      }
      else {
        $quotation_extra->supplier_show = date('Y-m-d H:i:s');
      }

      $quotation_extra->save();

      ResponseHelper::redirectMessage('Toon leverancier is succesvol gewijzigd', reconstructQueryAdd());
    }

    function executePlanroute() {
      $this->getQuotationinfo();
      $errors = [];

      if (isset($_POST['save_route']) || isset($_POST['save_to_list'])) {
        // ids to remove
        $rdeIds = explode(',', $_POST['rdeIds']);

        // ids to add a route to
        $quotationIds = explode(',', $_POST['quotationIds']);

        // handle route removal (we don't need to check for errors here)
        if (!empty($rdeIds) && reset($rdeIds)) $this->handleDeleteRoutes($rdeIds);

        if (!empty($quotationIds) && reset($quotationIds)) {
          if (empty($_POST['truck'])) $errors[] = "Selecteer een truck";

          $deliveryAddress = CrmAddresses::find_by(['addressId' => $this->quotation_extra->addressDeliveryId]);
          if (!$deliveryAddress || empty($deliveryAddress->latitude)) $errors[] = "Afleveradres heeft geen geldig latitude/longitude. Niet mogelijk om in te plannen.";

          $date = DateTime::createFromFormat('d-m-Y', $_POST['selectDate']);
          if (!$date || $date->format('d-m-Y') !== $_POST['selectDate']) $errors[] = "Selecteer een geldige datum";

          if (empty($errors)) $this->handleAddRoutes($quotationIds, $date, $deliveryAddress);
        }

        if (isset($_POST['save_to_list'])) {
          ResponseHelper::redirectMessage('Wijzigingen succesvol doorgevoerd', reconstructQueryAdd());
        }
      } else $this->checkForMissingStoneInQuotation();

      $quotations = $this->getQuotationsFromCompany($this->quotation->companyId);

      // make quotation groups based on the delivery address (we can't plan a route for multiple addresses)
      $groupedQuotations = [];
      foreach ($quotations as $q) {
        $groupedQuotations[$q->quotation_extra->addressDeliveryId][] = $q;
      }

      $this->groupedQuotations = $groupedQuotations;
      $this->trucks = GpsbuddyTrucks::find_all_by(['active' => 1]);
      $this->errors = $errors;
    }

    private function handleDeleteRoutes($rdeIds): void {
      foreach ($rdeIds as $rdeId) {
        $gpsbuddyRde = GpsbuddyRde::find_by(['logId' => $rdeId]);
        if (!$gpsbuddyRde) continue;

        $routeId = $gpsbuddyRde->routeId;
        $gpsbuddyRde->destroy();

        // check if the route has any linked rdes left, if not delete the route
        $gpsbuddyRoute = GpsbuddyRoutes::find_by(['routeId' => $routeId]);
        if ($gpsbuddyRoute) {
          $gpsbuddyRdeCount = GpsbuddyRde::count_all_by(['routeId' => $gpsbuddyRoute->routeId]);
          if ($gpsbuddyRdeCount == 0) $gpsbuddyRoute->destroy();
        }
      }
    }

    private function handleAddRoutes($quotationIds, $date, $deliveryAddress): void {
      //make a new route with the quotation info
      $gpsbuddyRoute = new GpsbuddyRoutes();
      $gpsbuddyRoute->bid = 0;
      $gpsbuddyRoute->truckId = GpsbuddyTrucks::find_by(['truckId' => $_POST['truck']])->truckId;

      $gpsbuddyRoute->date = $date->format('Y-m-d');
      $gpsbuddyRoute->called = $this->quotation_extra->callToDelivery ? 'N' : 'Y';
      $gpsbuddyRoute->company = 'rde';
      $gpsbuddyRoute->rank = 0;
      $gpsbuddyRoute->city = $deliveryAddress->zipcode;
      $gpsbuddyRoute->latitude = $deliveryAddress->latitude;
      $gpsbuddyRoute->longitude = $deliveryAddress->longitude;
      $gpsbuddyRoute->domestic = $deliveryAddress->domestic;

      if (!empty($this->company->companyId)) {
        $gpsbuddyRoute->bid = $this->company->companyId;
        $gpsbuddyRoute->subject = $this->company->name;
      }

      $gpsbuddyRoute->save();

      //loop through selected quotations
      foreach ($quotationIds as $quotationId) {
        $containerQuotation = ContainersQuotations::find_by(['quotationId' => $quotationId]);
        //add new gpsbuddy_rde from quotation
        $gpsbuddyRde = new GpsbuddyRde();
        $gpsbuddyRde->bakId = 0;
        if ($containerQuotation) {
          $container = Containers::find_by(['containerId' => $containerQuotation->containerId]);
          if ($container) {
            $gpsbuddyRde->bakId = $container->containerNumber;
          }
        }
        $gpsbuddyRde->routeId = $gpsbuddyRoute->routeId;
        $gpsbuddyRde->quotationId = $quotationId;

        $gpsbuddyRde->save();
      }
    }

    private function getQuotationsFromCompany($companyId): array {
      $quotTable = Quotations::getTablename();
      $quotExtraTable = QuotationsExtra::getTablename();
      $gpsbuddyRdeTable = GpsbuddyRde::getTablename();
      $gpsbuddyRoutesTable = GpsbuddyRoutes::getTablename();
      $gpsbuddyTrucksTable = GpsbuddyTrucks::getTablename();
      $routeTitleTable = RouteTitle::getTablename();

      $query = <<<SQL
        SELECT * FROM $quotTable
        LEFT JOIN $quotExtraTable ON $quotExtraTable.quotationId = $quotTable.quotationId
        LEFT JOIN $gpsbuddyRdeTable ON $gpsbuddyRdeTable.quotationId = $quotTable.quotationId
        LEFT JOIN $gpsbuddyRoutesTable ON $gpsbuddyRoutesTable.routeId = $gpsbuddyRdeTable.routeId
        LEFT JOIN $gpsbuddyTrucksTable ON $gpsbuddyTrucksTable.truckId = $gpsbuddyRoutesTable.truckId
        LEFT JOIN $routeTitleTable ON $routeTitleTable.transportId = $gpsbuddyTrucksTable.truckId AND $routeTitleTable.deliveryDate = $gpsbuddyRoutesTable.date
        WHERE $quotTable.companyId = $companyId AND $quotTable.statusId IN ('20','30','35','38','40','50')
        ORDER BY $quotTable.dueDateWeek
       SQL;


      $result = DBConn::db_link()->query($query);
      $quotations = [];
      while ($row = $result->fetch_array()) {
        $column_count = 0;

        $quotation = (new Quotations())->hydrateNext($row, $column_count);
        $quotation_extra = (new QuotationsExtra())->hydrateNext($row, $column_count);
        $gpsbuddy_rde = (new GpsbuddyRde())->hydrateNext($row, $column_count);
        $gpsbuddy_route = (new GpsbuddyRoutes())->hydrateNext($row, $column_count);
        $gpsbuddy_truck = (new GpsbuddyTrucks())->hydrateNext($row, $column_count);
        $route_title = (new RouteTitle())->hydrateNext($row, $column_count);

        $quotation->quotation_extra = $quotation_extra;
        $quotation->gpsbuddy_rde = $gpsbuddy_rde;
        $quotation->gpsbuddy_route = $gpsbuddy_route;
        $quotation->gpsbuddy_truck = $gpsbuddy_truck;
        $quotation->route_title = $route_title;

        $quotations[] = $quotation;
      }
      return $quotations;
    }

    public function executeRacks() {

    }

    public function executeMail() {
      $this->getQuotationinfo();
      $elements = OrderElements::find_all_by(['quotationId' => $this->quotation->quotationId]);

      if (isset($_POST['go'])) {
        // get the content from the ckeditor
        $editorContent = $_POST['content'] ?? false;
        MailsFactory::sendTenderEmail($this->quotation, $elements, $editorContent);
        ResponseHelper::redirectMessage('Mail is succesvol verzonden', reconstructQueryAdd(['id' => $this->quotation->quotationId]));
      }
    }

    public function executeSendMail() {
      $this->template_wrapper_clear = true;
      $this->getQuotationinfo();

      $mailTemplates = CommunicationTemplate::find_all();

      $mailTemplatesAndContent = [];
      foreach ($mailTemplates as $template) {
        $template->contentString = CommunicationTemplateContent::find_by(['communication_template_id' => $template->id]);

        $mailTemplatesAndContent[] = $template;
      }

      $this->mailTemplates = $mailTemplatesAndContent;
      $this->preferedMailTemplate = CommunicationTemplate::find_by(['code' => 'MAIL_CUSTOM_QUOTATION']);
    }

    public function executeContainers(): void {
      $this->getQuotationinfo();
      $this->createContainerListFilters();

      if (isset($_POST['containerQuotationId'])) {
        // handle post request
        $redirectUrl = reconstructQueryAdd(['id' => $_GET['id']]);
        try {
          $containerQuotation = ContainersQuotations::find_by(['containerQuotationId' => $_POST['containerQuotationId']]);
          if (!$containerQuotation) throw new Exception('ContainerQuotation not found');

          // If the containerId is -1, we want to remove the container from the quotation
          if ($_POST['containerId'] == '-1') {
            $containerQuotation->destroy();
          } else {
            $containerQuotation->containerId = $_POST['containerId'];
            $containerQuotation->save();
          }

          ResponseHelper::redirectMessage('Succesvol opgeslagen', $redirectUrl);
        } catch (Exception) {
          ResponseHelper::redirectAlertMessage('Er is iets fout gegaan', $redirectUrl);
        }
      }
    }

    public function createContainerListFilters(): void {
      $dataTable = new DataTable('orders');
      $dataTable->setRequestUrl(reconstructQueryAdd([
        'action' => 'containerlistajax',
        'quotationId'     => $_GET['id'],
      ]));
      $dataTable->addColumnHelper("quotationNumber", "Offerte Nummer");
      $dataTable->addColumnHelper("containerNumber", "Container Nummer");
      $dataTable->addColumnHelper("actions", "Acties");

      $dataTable->handleRequest($_POST);
      $this->dataTable = $dataTable;
    }

    public function executeContainerlistajax() {

      $this->createContainerListFilters();
      $tableData = [];

      $containerQuotations = ContainersQuotations::find_all_by(['quotationId' => $_GET['quotationId']]);
      $availableContainers = Containers::find_all('order by containerNumber');

      foreach ($containerQuotations as $containerQuotation) {
        $container = Containers::find_by(['containerId' => $containerQuotation->containerId]);
        $quotation = Quotations::find_by(['quotationId' => $containerQuotation->quotationId]);
        if (!$container || !$quotation) continue;

        $postAction = reconstructQueryAdd(['id' => $containerQuotation->quotationId]);

        // Build the options for the select dropdown
        $options = "<option value='-1'>Niet ingepakt</option>";
        foreach ($availableContainers as $availableContainer) {
          $selected = $availableContainer->containerId == $container->containerId ? 'selected' : '';
          $options .= "<option value='{$availableContainer->containerId}' $selected>{$availableContainer->containerNumber}</option>";
        }

        $historyButton = BtnHelper::getOpenPage(
          PageMap::getUrl('M_RDE_ORDERS_CONTAINERS_LIST', ['action' => 'containerOverview', 'containerid' => $container->containerId]),
          'Bekijk container geschiedenis'
        );

        $tableData[] = [
          'DT_RowId'        => $containerQuotation->containerQuotationId,
          'quotationNumber' => $quotation->getQuotationNumberFull(),
          'containerNumber' => "$container->containerNumber $historyButton",
          // A form to optionally change the container
          'actions'         => <<<HTML
            <form method="post" action="$postAction">
              <input type="hidden" name="containerQuotationId" value="$containerQuotation->containerQuotationId">
              <select name="containerId">
                $options
              </select>
            <button class="gsd-btn gsd-btn-secondary gsd-btn-small" type="submit">Opslaan</button>  
          HTML
  ,
        ];
      }

      ResponseHelper::exitAsJson([
        'data'            => $tableData,
        'recordsTotal'    => count($containerQuotations),
        'recordsFiltered' => count($containerQuotations),
      ]);
    }

    public function executeImages(): void {
      $this->getQuotationinfo();

      $quotationId = DbHelper::escape($this->quotation->quotationId);

      $imageTable = ContainerImg::getTablename();
      $containerTable = Containers::getTablename();
      $statusPhotoTable = 'rde_b1mo.status_photo'; // no model for this table

      $query = <<<SQL
        SELECT $imageTable.id, $imageTable.insertDate, $imageTable.remark, $imageTable.statusPhotoId, $containerTable.containerNumber, $statusPhotoTable.description
        FROM $imageTable
        JOIN $containerTable ON $containerTable.containerId = $imageTable.containerId
        JOIN $statusPhotoTable ON $statusPhotoTable.id = $imageTable.statusPhotoId
        WHERE quotationId = $quotationId
      SQL;

      $result = DBConn::db_link()->query($query);
      $images = [];
      while ($object = $result->fetch_object()) {
        $images[] = $object;
      }
      $this->images = $images;

      // status 1
      $this->images1 = array_filter($images, fn ($image) => $image->statusPhotoId == 1);

      // status 2
      $this->images2 = array_filter($images, fn ($image) => $image->statusPhotoId == 2);

      // status 3
      $this->images3 = array_filter($images, fn ($image) => $image->statusPhotoId == 3);
    }

    public function executeShowImage(): void {
      $imageId = DbHelper::escape($_GET['id']);
      $image = ContainerImg::find_by(['id' => $imageId]);
      $path = DIR_ROOT_HTTPDOCS . "/filesystem/raamdorpel/clients/container/$image->containerId/$image->filename";
      header('Content-Type: image/jpeg');
      echo file_get_contents($path);
    }

    private function checkForMissingStoneInQuotation() {
      if (!$this->quotation->stoneId) {
        MessageFlashCoordinator::addMessageAlert("Geen steen gevonden bij deze offerte, dit kan het geval zijn bij een webshop only product. Mogelijk ontbreken sommige waarden");
      }
    }

    private function buildQuotationListFilterQuery() {
      $filterQuery = 'JOIN ' . SandboxUsers::getTablename() . ' ON sandbox_users.userId = quotations.userId ';
      $filterQuery .= 'LEFT JOIN ' . Stones::getTablename() . ' ON stones.stoneId = quotations.stoneId ';
      $filterQuery .= 'LEFT JOIN ' . QuotationsExtra::getTablename() . ' ON quotations_extra.quotationId = quotations.quotationId ';
      $filterQuery .= 'LEFT JOIN ' . CrmAddresses::getTablename() . ' ON quotations_extra.addressDeliveryId = crm_addresses.addressId ';
      $filterQuery .= 'LEFT JOIN ' . StoneBrands::getTablename() . ' ON stones.brandId = stone_brands.brandId ';
      $filterQuery .= 'LEFT JOIN ' . CustomerCodes::getTablename() . ' ON customer_codes.codeId = quotations.codeId ';
      $filterQuery .= 'WHERE 1 ';

      if ($this->dataTable->hasFormElementValue("search")) {
        $searchstr = escapeForDB($this->dataTable->getFormElementValue("search"));

        // ignore the version and part of the quotation number when searching
        $quotationNumberSearch = explode('-', $searchstr)[0];

        $filterQuery .= "AND ( ";
        $filterQuery .= "sandbox_users.companyName LIKE '%" . $searchstr . "%' ";
        $filterQuery .= "OR sandbox_users.firstName LIKE '%" . $searchstr . "%' ";
        $filterQuery .= "OR sandbox_users.lastName LIKE '%" . $searchstr . "%' ";
        $filterQuery .= "OR sandbox_users.zipcode LIKE '%" . $searchstr . "%' ";
        $filterQuery .= "OR sandbox_users.domestic LIKE '%" . $searchstr . "%' ";
        $filterQuery .= "OR sandbox_users.street LIKE '%" . $searchstr . "%' ";
        $filterQuery .= "OR (CONCAT(SUBSTR(quotations.zipcode, 1, 4), ' ', SUBSTR(quotations.zipcode, 5, 2))   LIKE '%" . $searchstr . "%') ";
        $filterQuery .= "OR quotations.quotationId LIKE '%" . $searchstr . "%' ";
        $filterQuery .= "OR quotations.quotationNumber LIKE '%" . $quotationNumberSearch . "%' ";
        $filterQuery .= ") ";
      }
      return $filterQuery;
    }

    private function getLimitPreset($page): ?int {
      return match ($page) {
        self::ORDERS => 100,
        self::ORDERS_COMMISION, self::ORDERS_ISOSILL, self::ORDERS_PRIVATE, self::ORDERS_NATURE_STONE_B, self::ORDERS_NATURE_STONE_C => 99999,
        default => null,
      };
    }

    private function getQuotationsFromFilterQuery(string $filterQuery, $page): array {
      $q = Quotations::getTablename();
      $query = "SELECT * FROM $q $filterQuery";

      if (in_array($page, [self::ORDERS_DELIVERY_WEEK, self::ORDERS_NATURE_STONE_B, self::ORDERS_NATURE_STONE_C, self::ORDERS_ISOSILL])) {
        $query .= " ORDER BY YEAR($q.dueDate) ASC, dueDateWeek ASC, quotationNumber DESC, $q.quotationPart DESC, $q.nextRouteDate DESC";
      } else {
        // by default let datatable handle the sorting
        $query .= $this->dataTable->getSortQuery();
      }

      $limitPreset = $this->getLimitPreset($page);
      if (!empty($limitPreset)) {
        $this->dataTable->getPager()->setRowsPerPage($limitPreset);
      }

      $query .= $this->dataTable->getPager()->getLimitQuery();
      $result = DBConn::db_link()->query($query);

      $quotations = [];
      while ($row = $result->fetch_array()) {
        $column_count = 0;

        $quot = (new Quotations())->hydrateNext($row, $column_count);
        $su = (new SandboxUsers())->hydrateNext($row, $column_count);
        $stones = (new Stones())->hydrateNext($row, $column_count);
        $quot_extra = (new QuotationsExtra())->hydrateNext($row, $column_count);


        $quot->user = $su;
        $quot->stones = $stones;
        $quot->quot_extra = $quot_extra;

        if (!empty($su->companyId)) {
          $quot->user->company = CrmCompanies::find_by(['companyId' => $su->companyId]) ?? null;
        }

        // Name of the customer code
        $quot->code = $row['name'];

        // Get the planned date (could be null)
        $gps = GpsbuddyRde::find_by(['quotationId' => $quot->quotationId]);
        $route = $gps ? GpsbuddyRoutes::find_by(['routeId' => $gps->routeId]) : null;
        $quot->plannedDate = $route?->date;

        $quotations[] = $quot;
      }
      return $quotations;
    }

    /**
     * Handles an AJAX request for searching companies.
     *
     * This method fetches up to 10 companies from the database that match the given search term.
     * The results are formatted for use with Select2.
     */
    public function executeCompanySearchAjax(): void {
      $search = DbHelper::escape($_GET['search'] ?? '');
      $id = $_GET['id'] ?? null;

      if ($id) {
        // If ID is provided, fetch specific company
        $company = CrmCompanies::find_by(['companyId' => $id]);
        if ($company) {
          ResponseHelper::exitAsJson([[
            'id' => $company->companyId,
            'text' => $company->name
          ]]);
        }
        ResponseHelper::exitAsJson([]);
      }

      // Otherwise use existing search logic
      $companies = CrmCompanies::find_all("WHERE name <> ''  AND name LIKE '%$search%' ORDER BY name LIMIT 10");
      $items = array_map(function ($company) {
        return [
          'id'   => $company->companyId,
          'text' => $company->name
        ];
      }, $companies);
      ResponseHelper::exitAsJson($items);
    }
  }

