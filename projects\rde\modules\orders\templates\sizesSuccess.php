<?php TemplateHelper::includePartial('_tabs.php', 'orders', compact(["quotation", "quotation_extra", "company", "sandboxuser"])); ?>

<h2>
  <?php echo $stone ? ucfirst($stone->material) : 'Keramiek' ?>
  / Raamdorpel
</h2>

<?php if (empty($elements)): ?>
  Geen elementen
<?php else: ?>
  <form method="post" class="edit-form">
    <table class="default_table">
      <tr class="dataTableHeadingRow" style="font-weight: bold">
        <td>Merk</td>
        <td>Aantal</td>
        <td>Element</td>
        <td>Eind</td>
        <td>Groef</td>
        <td>Vlag</td>
        <td>Verf</td>
        <td>Verstek</td>
        <td>Graden</td>
        <td>Lengte</td>
        <td>Graden</td>
        <td>Verstek</td>
        <td>Verf</td>
        <td>Vlag</td>
        <td>Groef</td>
        <td>Eind</td>
        <td>HK</td>
        <td>TP</td>
        <td>Aantal</td>
        <td>Lengte A</td>
        <td>Aantal</td>
        <td>Lengte B</td>
        <td>Aantal</td>
        <td>Lengte C</td>
        <td>Aantal</td>
        <td>Lengte D</td>
      </tr>
      <?php foreach ($elements as $element): ?>
        <tr class="dataTableRow">
          <td><?php echo $element->referenceName ?></td>
          <td><?php echo $element->amount ?></td>
          <td><?php echo $element->inputLength ?></td>
          <td><input type="radio" value="leftEndstone" name="radioLeft_<?php echo $element->elementId ?>" <?php if (!empty($element->leftEndstone)) echo 'checked' ?>></td>
          <td><input type="radio" value="leftEndstoneGrooves" name="radioLeft_<?php echo $element->elementId ?>" <?php if (!empty($element->leftEndstoneGrooves)) echo 'checked' ?>>
          </td>
          <td><input type="checkbox" name="flagWindowLeft_<?php echo $element->elementId ?>" <?php if (in_array($element->flagWindowSide, ['left', 'both'])) echo 'checked' ?>></td>
          <td><input type="radio" value="paintLeft" name="radioLeft_<?php echo $element->elementId ?>" <?php if($element->paintleft) echo 'checked' ?>></td>
          <td><?php echo empty($element->leftMitre) ? 'Nee' : 'Ja' ?></td>
          <td><?php echo empty($element->leftMitre) ? '' : $element->leftMitre->angle ?></td>
          <td><input style="width: 7rem" type="text" name="elementLength_<?php echo $element->elementId ?>" value="<?php echo $element->elementLength ?>"></td>
          <td><?php echo empty($element->rightMitre) ? '' : $element->rightMitre->angle ?></td>
          <td><?php echo empty($element->rightMitre) ? 'Nee' : 'Ja' ?></td>
          <td><input type="radio" value="paintRight" name="radioRight_<?php echo $element->elementId ?>" <?php if($element->paintright) echo 'checked' ?>></td>
          <td><input type="checkbox" name="flagWindowRight_<?php echo $element->elementId ?>" <?php if (in_array($element->flagWindowSide, ['right', 'both'])) echo 'checked' ?>></td>
          <td><input type="radio" value="rightEndstoneGrooves"
                     name="radioRight_<?php echo $element->elementId ?>" <?php if (!empty($element->rightEndstoneGrooves)) echo 'checked' ?>></td>
          <td><input type="radio" value="rightEndstone" name="radioRight_<?php echo $element->elementId ?>" <?php if (!empty($element->rightEndstone)) echo 'checked' ?>></td>
          <td><input type="radio" value="heartClickSize" name="radioRightClick_<?php echo $element->elementId ?>" <?php if (!$element->heartClickSize) echo 'checked' ?>></td>
          <td><input type="radio" value="untilPoint" name="radioRightClick_<?php echo $element->elementId ?>" <?php if ($element->heartClickSize) echo 'checked' ?>></td>
          <?php if ($element->elementPart): ?>
            <?php foreach ($element->elementPart as $part): ?>
              <td><?php echo $part->aAmount1 ?></td>
              <td><?php echo $part->aLength1 ?></td>
              <td><?php echo $part->bAmount1 ?></td>
              <td><?php echo $part->bLength1 ?></td>
              <td><?php echo $part->cAmount1 ?></td>
              <td><?php echo $part->cLength1 ?></td>
              <td><?php echo $part->dAmount1 ?></td>
              <td><?php echo $part->dLength1 ?></td>
            <?php endforeach; ?>
          <?php else: ?>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
          <?php endif; ?>
        </tr>
      <?php endforeach; ?>
    </table>
    <input type="submit" name="go" value="Opslaan" class="gsd-btn gsd-btn-primary"/>
  </form>
<?php endif; ?>

<style>
  .edit-form {
    width: min-content;
  }
</style>