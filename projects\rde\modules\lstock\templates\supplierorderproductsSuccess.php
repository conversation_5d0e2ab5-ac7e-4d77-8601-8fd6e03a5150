<?php TemplateHelper::includePartial('_tabs.php','lstock'); ?>
<script type="text/javascript">
  $(document).ready(function() {
    $(".size").change(function (event) {
      var val = 0;
      if($(this).val()!="") {
        var val = parseInt($(this).val());
        if(isNaN(val)) {
          val = 0;
        }
      }
      $(this).val(val);

      // if(val<parseInt($(this).attr("data-size"))) {
      //   $(this).parent().parent().find(".nodelivery").prop("disabled", false);
      // }
      // else {
      //   $(this).parent().parent().find(".nodelivery").prop("disabled", true);
      // }

    });
    $(".size").keyup(function() {
      $(this).parent().parent().find(".delivered").prop("checked", true);
    });

  });
</script>
<?php writeErrors($errors, true); ?>

<form method="post">

  <div class="box">

    <select name="st_brand">
      <option value="">Selecteer leverancier...</option>
      <?php foreach($stonebrands as $sb): ?>
        <option value="<?php echo $sb->brandId ?>" <?php if($_SESSION['st_brand']==$sb->brandId) echo 'selected'; ?>><?php echo $sb->name ?></option>
      <?php endforeach; ?>
    </select>
    <input type="submit" name="go" value="Zoeken" />

  </div>
  <br/>
  Producten in bestelling, maar nog niet geleverd.<br/><br/>
  <?php	if(count($stoneorderitems)==0): ?>
    <br/>Er zijn geen te ontvangen stenen/bestellingen gevonden
  <?php else: ?>
    <table class="default_table" style="width: auto;">
      <tr class="dataTableHeadingRow">
        <td>Product</td>
        <td style="width: 180px;">Naam</td>
        <td>Bestellingen</td>
        <td>Besteld op</td>
        <td>Verwacht op</td>
        <td>Laadreferentie</td>
        <td>Pallets</td>
        <td>Besteld</td>
        <td style="text-align: right">Reeds Geleverd</td>
        <td style="text-align: right; min-width: 150px;">
          Aantal ontvangen
          <?php echo showInfoButton("Als het aantal kleiner is dan het aantal besteld, dan zal het product niet van de lijst verdwijnen.  Wilt u dit toch, zet dan het vinkje word niet meer geleverd aan.","Word niet meer geleverd") ?>
        </td>
      </tr>
      <?php
        $stoneprev = false;
        foreach($stoneorderitems as $item):
        $stone = $item->stone;
        ?>
        <tr class="dataTableRow trhover  <?php if($stoneprev && !Stones::areSiblings($stone, $stoneprev)): ?>topborder<?php endif; ?>">
          <td><?php echo $stone->name ?></td>
          <td><?php echo $item->name ?></td>
          <td>
            <?php if($item->quotationNames!=""): ?>
              <a href="#" class="qtipa" title="<?php echo str_replace(", ","<br/>",$item->quotationNames) ?>"><Bestellingen></Bestellingen></a>
            <?php endif; ?>
          </td>
          <td><?php echo $item->getSenddate() ?></td>
          <td><?php echo getDateSelector('supplierreadydate['.$item->id.']', $item->getSupplierreadydate(), false, "datepicker_weeks","Verwacht...") ?></td>
          <td><input type="text" name="loadreference[<?php echo $item->id ?>]" value="<?php echo $item->loadreference ?>" style="width: 120px;" maxlength="12" placeholder="Laadreferentie..."/> </td>
          <td style="text-align: right;">
            <?php if($stone->amountPerPallet>0): ?>
              <?php if(fmod($item->size/$stone->amountPerPallet,1)==0): ?>
                <?php echo $item->size/$stone->amountPerPallet ?> x <?php echo $stone->amountPerPallet ?> stenen
              <?php endif; ?>
            <?php endif; ?>
          </td>
          <td style="text-align: right;">
            <?php echo $item->size ?>
          </td>
          <td style="text-align: right;">
            <?php echo $item->receivedsize ?>
          </td>
          <td style="text-align: right;">
            <input type="checkbox" name="checked[<?php echo $item->id ?>]" class="delivered" title="Is geleverd"/>
            <input style="width: 75px; text-align:right;" type="text" id="hours" name="received[<?php echo $item->id ?>]" data-size="<?php echo $item->size ?>"  class="size" value="<?php echo $item->size-$item->receivedsize ?>" <?php if($item->receiveddate!="") echo "readonly" ?> title="<?php if($item->receivedsize!="0") echo "Reeds ".$item->receivedsize." geleverd"; ?>"/>
            <input type="checkbox" name="nodelivery[<?php echo $item->id ?>]" class="nodelivery"  title="Word niet meer geleverd"/>
          </td>
        </tr>
      <?php
          $stoneprev = $stone;
        endforeach;
      ?>
    </table>
    <br/>
    <input type="submit" name="go_updatestock" value="Opslaan"/>
    <input type="submit" name="cancel" value="Annuleren" class="gsd-btn gsd-btn-link"/>
    <?php echo showInfoButton("Wanneer van een bestelling de benodigde elementen zijn geselecteerd ontvangen, zal de leverdatum op de bestelling ook worden gezet.") ?>
  <?php endif; ?>

</form>
