<?php

  trait quotationStep5Actions {


    public function executeWizardstep5() {

      if (!isset($_SESSION["wizard"]['quotation']) || !isset($_SESSION["wizard"]['elements'])) {
        ResponseHelper::redirect($this->wizardurl);
      }

      if (!isset($_SESSION['userObject'])) {
        $this->executeWizardLogin();
        $this->template = "wizardstep5LoginSuccess.php";
      }
      else {
        $this->executeWizardAddress();
        $this->stepname = "step5";
        $this->template = "vuespaSuccess.php";
      }
    }

    public function executeWizardLogin() {
      $this->step = 5;
      $this->quotation = $_SESSION["wizard"]['quotation'];
    }

    /**
     * Afleveradres
     */
    public function executeWizardAddress() {
      $this->step = 5;
      $this->quotation = $_SESSION["wizard"]['quotation'];

      if (isset($_GET["json"])) {
        if (isset($_POST["next"]) || isset($_POST["prev"])) {
          $this->step5Post();
        }
        else {
          $this->step5Get();
        }
      }

    }

    public function step5Get(bool $ajax = true) {

      if (!isset($_SESSION["wizard"]['quotation'])) {
        ResponseHelper::responseError("Geen offerte. U word doorgestuurd naar stap 1.", $this->wizardurl);
      }

      /** @var Quotations $quotation */
      $quotation = $_SESSION["wizard"]['quotation'];
      $stone = Stones::find_by(["stoneId" => $quotation->stoneId]);
      $stone_size = StoneSizes::find_by(["sizeId" => $stone->sizeId]);
      $userId = $_SESSION['userObject']->userId;

      if (isset($_SESSION["wizard"]['quotation_extra'])) {
        $quotation_extra = $_SESSION["wizard"]['quotation_extra'];
      }
      else {
        $quotation_extra = new QuotationsExtra();
        $quotation_extra->quotationAltPriceYear = date("Y") . '-01-01';
      }
      if ($quotation_extra->quotationAltPriceYear == "" || $quotation_extra->quotationAltPriceYear == "0000-00-00") {
        $quotation_extra->quotationAltPriceYear = date("Y") . '-01-01';
      }

      if ($quotation_extra->addressDeliveryId == null && $quotation->street != "") { //geen id, en staat ingevuld, dan nieuw selecteren.
        $quotation_extra->addressDeliveryId = "NEW";
      }
      elseif ($quotation_extra->addressDeliveryId == null) {
        $quotation_extra->addressDeliveryId = $_SESSION['userObject']->lastDeliveryAddressId;
        if ($quotation_extra->addressDeliveryId == null || $quotation_extra->addressDeliveryId == 0) {
          $quotation_extra->addressDeliveryId = "";
        }
        $quotation->country = "NL";
      }

      $addresses = [];
      $newAdres = new CrmAddresses();
      $newAdres->addressId = "NEW";
      $addresses[] = $newAdres; //nieuwe afleveradres
      if ($quotation->companyId != "" && $quotation->companyId != "0") {
        $types = ["delivery"];
        if ($_SESSION['userObject']->company->noDelivery != 1) {
          $types[] = "visit";
        }
        $addresses = array_merge($addresses, CrmAddresses::find_all_by(["companyId" => $quotation->companyId, 'type' => $types], "ORDER BY domestic, street"));
      }
      else { //particulier, dan account adres standaard invullen.
        if ($quotation->street == "") { //alleen vullen als straat leeg is.
          $quotation->street = $_SESSION['userObject']->street;
          $quotation->nr = $_SESSION['userObject']->nr;
          $quotation->ext = $_SESSION['userObject']->extension;
          $quotation->zipcode = $_SESSION['userObject']->zipcode;
          $quotation->domestic = $_SESSION['userObject']->domestic;
        }
        if ($quotation_extra->addressDeliveryId == null) { //particulier pak standaard de nieuwe
          $quotation_extra->addressDeliveryId = "NEW";
        }
      }
      if ($quotation_extra->addressDeliveryId != "" && $quotation_extra->addressDeliveryId != 0) {
        $isfound = false;
        foreach ($addresses as $addr) {
          if ($addr->addressId == $quotation_extra->addressDeliveryId) {
            $isfound = true;
            break;
          }
        }
        if ($isfound != true) {
          //gekoppelde addres niet gevonden in array, kijk of je hem kunt vinden en voeg hem dan toe
          $addr_search = CrmAddresses::find_by(["addressId" => $quotation_extra->addressDeliveryId]);
          if ($addr_search) {
            $addresses[] = $addr_search;
          }
          else { //?? address id niet gevonden....leeg maken.
            $quotation_extra->addressDeliveryId = null;
          }
        }
      }
      if ($quotation_extra->addressDeliveryId != 20357) $addresses[] = CrmAddresses::find_by(["addressId" => 20357]); //rde pickup address

      $priceyears = [];
      if ($stone_size->isOpmaatgemaakt()) {
        $priceyears[] = [
          "start" => date("Y-01-01"),
          "year"  => date("Y"),
        ];
      }
      else {

        for ($tel = 0; $tel < 4; $tel++) {
          $time = strtotime("+" . ($tel - 2) . " YEAR");
          $date = date("Y-m-d", $time);

          $prices = Quotations::getPrices($date, $userId, $stone);
          if ($prices) { //er zijn prijzen voor deze steen
            $priceyears[] = [
              "start" => date("Y", $time) . '-01-01',
              "year"  => date("Y", $time),
            ];
          }
        }
      }

      $data = new stdClass();
      $data->step = 5;
      $data->quotation = $quotation;
      $data->quotation_extra = $quotation_extra;
      $data->addresses = $addresses;
      $data->priceyears = $priceyears;
      $data->isAdmin = SandboxUsers::isAdmin();
      $data->delivery_reach = isset($_POST["delivery_reach"]) || (!isset($_POST["delivery_reach"]) && !isset($_POST["addressDeliveryId"]) && $quotation->deliveryNotes == "");
      $data->showSms = !isset($_SESSION['userObject']) || $_SESSION['userObject']->sms == 0;

      if ($ajax) {
        ResponseHelper::responseSuccess($data);
      }

      return $data;

    }

    public function step5Post() {

      if (!isset($_SESSION["wizard"]['quotation'])) {
        ResponseHelper::responseError("Geen offerte. U word doorgestuurd naar stap 1.", $this->wizardurl);
      }

      $response = [];
      $response["errors"] = [];

      $data = $this->step5Get(false);

      $data->quotation_extra->addressDeliveryId = $_POST['addressDeliveryId'];
      $data->quotation->NoEmail = isset($_POST["NoEmail"]) ? 1 : 0;
      if (isset($_POST["deliveryNotes"])) $data->quotation->deliveryNotes = trim($_POST["deliveryNotes"]);

      if ($data->quotation_extra->addressDeliveryId == "NEW") { //nieuwe adres
        $data->quotation->street = trim($_POST["street"]);
        $data->quotation->nr = trim($_POST["nr"]);
        $data->quotation->ext = substr(trim($_POST["ext"]),0,10);
        $data->quotation->zipcode = \StringHelper::cleanZip($_POST["zipcode"]);
        $data->quotation->domestic = trim($_POST["domestic"]);
        $data->quotation->country = trim($_POST["country"]);
        $data->quotation->street_city_manual = $_POST["street_city_manual"];
      }

      if (isset($_POST["quotationAltPriceYear"])) {
        $data->quotation_extra->quotationAltPriceYear = $_POST["quotationAltPriceYear"];
      }
      else { //als de post niet is gezet, dan altijd prijzen van nu gebruiken)
        $data->quotation_extra->quotationAltPriceYear = date("Y") . '-01-01';
      }

      //bij een volgende post, zonder validatie naar vorige stap, maar wel elementen opslaan.
      if (isset($_POST["prev"])) {

        $_SESSION["wizard"]['quotation'] = $data->quotation;
        $_SESSION["wizard"]['quotation_extra'] = $data->quotation_extra;

        ResponseHelper::responseSuccess($response, reconstructQuery(["step", "json"]) . "step=4");

      }

      if (!$data->quotation_extra->isPickup()) {

        if ($data->quotation_extra->addressDeliveryId != "NEW" && $data->quotation_extra->addressDeliveryId != "" && $data->quotation_extra->addressDeliveryId != 0) {
          $rde_address = CrmAddresses::find_by(["addressId" => $data->quotation_extra->addressDeliveryId]);
          if ($rde_address) {
            $data->quotation->street = $rde_address->street;
            $data->quotation->nr = $rde_address->nr;
            $data->quotation->ext = substr($rde_address->extension,0,10);
            $data->quotation->zipcode = $rde_address->zipcode;
            $data->quotation->domestic = $rde_address->domestic;
            $data->quotation->country = $rde_address->country;
          }
        }

        if ($data->quotation->nr != "" && !is_numeric($data->quotation->nr) && !str_starts_with($data->quotation->nr, ".")) {
          $response["errors"][] = "Huisnummer moet een getal zijn.";
        }
        if ($data->quotation->nr != "" && strlen($data->quotation->nr) > 5) {
          $response["errors"][] = "Huisnummer mag maximaal 5 characters bevatten.";
        }
        if (!ValidationHelper::isZip($data->quotation->zipcode, strtolower($data->quotation->country))) {
          $response["errors"][] = "Postcode is leeg of ongeldig.";
        }
        if ($data->quotation->street == "") {
          $response["errors"][] = "Straat mag niet leeg zijn.";
        }
        if ($data->quotation->domestic == "") {
          $response["errors"][] = "Plaats mag niet leeg zijn.";
        }

      }

      if (!isset($_POST["delivery_reach"])) {
        if ($data->quotation->deliveryNotes == "") {
          $response["errors"][] = 'U heeft aangeven dat er bijzonderheden zijn bij de leverlocatie. U bent verplicht een toelichting in te vullen.';
        }
      }
      else {
        $data->quotation->deliveryNotes = "";
      }

      $data->quotation_extra->sms = isset($_POST["sms"]) ? 1 : 0;
      $data->quotation_extra->sms_delivered = isset($_POST["sms_delivered"]) ? 1 : 0;

      if ($data->quotation_extra->sms == 1 || $data->quotation_extra->sms_delivered == 1) {
        $data->quotation_extra->smsnumber = trim($_POST["smsnumber"]);
        if (!ValidationHelper::isMobile($data->quotation_extra->smsnumber)) {
          $response["errors"][] = 'SMS number ontbreekt of is ongeldig. Uw mobielenummer is verplicht als u een SMS/Whatsapp wilt ontvangen.';
        }
      }
      else {
        $data->quotation_extra->smsnumber = null;
      }

      if (count($response["errors"]) > 0) {
        ResponseHelper::responseSuccess($response);
      }

      if ($data->quotation_extra->isPickup()) { //ophalen. Vul raamdorpel address in.
        $rde_address = CrmAddresses::find_by(["addressId" => 20357]);
        $data->quotation->street = $rde_address->street;
        $data->quotation->nr = $rde_address->nr;
        $data->quotation->ext = substr($rde_address->extension,0,10);
        $data->quotation->zipcode = $rde_address->zipcode;
        $data->quotation->domestic = $rde_address->domestic;
        $data->quotation_extra->sms = 0;
        $data->quotation_extra->sms_delivered = 0;
        $data->quotation_extra->smsnumber = null;
      }

      $_SESSION["wizard"]['quotation'] = $data->quotation;
      $_SESSION["wizard"]['quotation_extra'] = $data->quotation_extra;
      ResponseHelper::responseSuccess($response, reconstructQuery(["step", "json"]) . "step=6");


    }


  }