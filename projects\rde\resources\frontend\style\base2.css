/* FORM COLUMNS */
.form-row {
  margin: 0 0 0.2em 0;
  display: inline-block;
  width: 100%;
}

.form-row .col-1,
.form-row .col-2,
.form-row .col-3,
.form-row .col-4,
.form-row .col-4-3 {
  box-sizing: border-box;
  float: left;
  line-height: 3.0em;
}

.form-row .col-1 {
  width: 100%;
}

.form-row .col-2 {
  width: 50%;
}

.form-row .col-3 {
  width: 33.33%;
}

.form-row .col-4 {
  width: 25%;
}

.form-row .col-4-3 {
  width: 75%;
}

/* FORM FIELDS */
.form-input {
  width: 100%;
  box-sizing: border-box;
  border: 1px solid #ccc;
  min-height: 3.0em;
  resize: vertical;
  padding: 0 4%;
  border-radius: 2px;
  -moz-border-radius: 2px;
  -webkit-border-radius: 2px;
  -webkit-appearance: none;
}

.form-input, textarea.form-input {
  color: #666;
  font-family: Arial, Helvetica, sans-serif;
}

textarea.form-input {
  min-height: 100px;
  padding: 5px 4%;
}

select {
  box-sizing: border-box;
  border: 1px solid #ccc;
  min-height: 3.0em;
  resize: vertical;
  padding: 0 4%;
  border-radius: 1px;
  -moz-border-radius: 1px;
  -webkit-border-radius: 1px;
  width: 100%;
}

select.hour,
select.minute {
  width: 49%;
  margin-right: 1%;
}

select.minute {
  margin-right: 0;
  margin-left: 1%;
}

/* OTHER */
.form-arterisk {
  color: red;
  font-size: 18px;
  line-height: 12px
}

/* THANKYOU */
.thanks {
  border: 3px solid #9aca74;
  -webkit-border-radius: 3px;
  -moz-border-radius: 3px;
  border-radius: 3px;
  display: inline-block;
  width: 100%;
  margin: 1.0em 0 1.0em 0;
  box-sizing: border-box;
  padding: 2.0em 2% 2.0em 2%;

}

.thanks_l {
  float: left;
  width: 20%;
}

.thanks_r {
  float: left;
  width: 75%;
  padding-top: 2%;
}

.thanks h2 {
  font-size: 1.5em;
  margin-bottom: 0.2em;
}

.thanks p {
  font-size: 16px;
  margin-bottom: 0.5em;
}

.thanks .fa {
  color: #9aca74;
  font-size: 6em;
}


/** SLIDER **/
.slider,
#slider_container {
  position: relative;
}

.slider .slide {
  position: absolute;
  z-index: 50;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  background-size: cover;
  background-position: center
}

.slider img {
  position: absolute;
  z-index: 55;
  top: 0;
  left: 0;
}

.slider .slide .wrap {
  position: relative;
}

.slider .slide .slide-content {
  position: absolute;
  z-index: 80;
  padding: 5% 20% 0 0;
}

.home .slider .slide .slide-title {
  width: 60%
}

.landing .slider .slide .slide-content {
  padding: 25px 25px 0 0;
}

.slider .slide .slide-title {
  color: #fff;
  font-weight: 900;
  font-size: 48px;
  line-height: 60px;
  position: relative;
  padding-bottom: 30px;
}

.slider .slide .slide-description {
  color: #242729;
  font-weight: 900;
  font-size: 210%;
  line-height: 100%;
}


/*navigation*/
.slider-navigation {
  position: absolute;
  bottom: 20px;
  height: 11px;
  z-index: 70;
  list-style: none;
  margin: 0;
  padding: 0;
  left: 0;
  right: 0;
  text-align: center;
}


.slider-navigation li {
  cursor: pointer;
  display: inline-block;
  margin: 0 5px;
  width: 11px;
  height: 11px;
  border-radius: 100%;
  background: #fff
}

.slider-navigation li.current {
  background: #242729;
}

/* SLIDER THUMBS */
.slider-thumbnails {
  list-style: none;
  margin: 0;
  padding: 30px 0 0 0;
  height: 80px;
}

.slider-thumbnails li {
  float: left;
  margin: 0 15px 0 0
}

.slider-thumbnails li img {
  cursor: pointer;
  border: 2px solid #ccc
}

.slider-thumbnails li.current img {
  border-color: #087da9
}
