<?php TemplateHelper::includePartial('_tabs.php', 'other'); ?>
<?php TemplateHelper::includePartial('_tabs.php','planning'); ?>
<?php writeErrors($errors, true); ?>
<h3>Planuren aanpassen batch gewijs</h3>
De complete periode word aangepast aan de genoemde uren en werknemer. In het planoverzicht word ook rekening gehouden met verlof.<br/>
<br/>
<form method="post">
  <table class="default_table" style="width: auto;">
    <tr class="dataTableHeadingRow">
      <td>Eigenschap</td>
      <td>Waarde</td>
    </tr>
    <tr class="dataTableRow trhover">
      <td class="head">Werknemers</td>
      <td>
        <?php foreach($workers as $worker): ?>
          <label style="display: inline-block"><input type="checkbox" name="workers[]" value="<?php echo $worker->id ?>" <?php if(isset($_POST["workers"]) && in_array($worker->id, $_POST["workers"])) echo 'checked'; ?> style="vertical-align: sub"/><?php echo $worker->getNaam() ?></label>
        <?php endforeach; ?>
      </td>
    </tr>
    <tr class="dataTableRow trhover">
      <td class="head">Uren</td>
      <td>
        <table class="default_table" >
          <tr class="dataTableHeadingRow">
            <td><label for="mon">Dag</label></td>
            <td class="workerdays"><label for="mon">ma</label></td>
            <td class="workerdays"><label for="tue">di</label></td>
            <td class="workerdays"><label for="wed">woe</label></td>
            <td class="workerdays"><label for="thu">do</label></td>
            <td class="workerdays"><label for="fri">vr</label></td>
            <td class="workerdays"><label for="sat">za</label></td>
            <td class="workerdays"><label for="sun">zo</label></td>
          </tr>
          <tr class="dataTableRow trhover">
            <td><label for="mon">Uren</label></td>
            <?php foreach(DateTimeHelper::getDaysOfWeek() as $nr=>$name): ?>
            <td style="width: 30px;"><input style="width: 40px;" type="text"name="days[<?php echo $nr+1 ?>]" class="hours" value="<?php echo isset($_POST["days"][$nr+1])?$_POST["days"][$nr+1]:''?>" /></td>
            <?php endforeach; ?>
          </tr>
        </table>
      </td>
    </tr>
    <tr class="dataTableRow trhover">
      <td class="head">Aanmaken voor periode</td>
      <td>
        Van <input type="text" class="datepicker_week" name="from" id="from" value="<?php echo isset($_POST["from"])?$_POST["from"]:'' ?>" autocomplete="off"/>
        tot en met
        <input type="text" class="datepicker_week" name="to" id="to" value="<?php echo isset($_POST["to"])?$_POST["to"]:'' ?>" autocomplete="off"/>
      </td>
    </tr>
  </table>
  <br/><br/>
  <input type="submit" name="verzend" value="Opslaan"/>
  <input type="submit" name="verzend_list" value="Opslaan en naar lijst"/>
</form>


<script type="text/javascript">
  $(document).ready(function() {

    $(".datepicker_week").datepicker({ showWeek: true });
  });
</script>
