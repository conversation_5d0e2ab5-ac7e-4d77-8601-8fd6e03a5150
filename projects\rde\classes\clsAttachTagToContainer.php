<?php

  /**
   * Class clsAttachTagToContainer
   */
  class clsAttachTagToContainer {

    /**
     * @var object $request_vars
     */
    protected $request_vars;

    function __construct($request_vars) {
      $this->request_vars = $request_vars;
    }


    /**
     * @return object
     */
    public function getRequestVars() {
      return $this->request_vars;
    }

    /**
     * @param object $request_vars
     */
    public function setRequestVars($request_vars) {
      $this->request_vars = $request_vars;
    }


    /**
     * @return bool attachTagToContainer
     */
    public function attachTagToContainer() {

      $tagIds = self::getTags($this->getRequestVars());
      $containerNumber = self::getContainerNumber($this->getRequestVars());
      $oClsContainerTag = new clsContainerTag();
      $oClsContainerTag->setTagIds($tagIds);
      $oClsContainerTag->setContainerNumber($containerNumber);
      $oClsContainerTag->addTags();
      return true;

    }

    /**
     * @param $request_vars
     * @return array|bool
     */
    public static function getTags($request_vars) {

      $tagIds = [];
      foreach ($request_vars as $key => $value) {
        if (isset($value->tags)) {
          foreach ($value->tags as $tagKey => $tagValue) {
            $tagIds[] = $tagValue;
          }
          break;
        }
      }
      if (count($tagIds) > 0) {
        return $tagIds;
      }
      else {
        return false;
      }

    }


    /**
     * @param $request_vars
     * @return bool|string
     */
    public static function getContainerNumber($request_vars) {
      $containerNumber = false;
      foreach ($request_vars as $key => $value) {
        if (isset($value->containerNumber)) {
          $containerNumber = $value->containerNumber;
          break;
        }
      }

      return $containerNumber;
    }


  }