<?php
  TemplateHelper::includePartial("_tabs.php", "dashboard");
?>
<br>

<?php $dataTable->render(); ?>

<script type="text/javascript">

  $(document).ready(function () {

    var datatable_config = get_default_datatable_config();
    datatable_config.ajax.url = "<?php echo $dataTable->getRequestUrl()  ?>";
    datatable_config.columns = <?php echo $dataTable->getColumnsJson()  ?>;
    var datatable = $('#<?php echo $dataTable->getName()  ?>').DataTable(datatable_config);
    refresh_datatable_on_filter_change(datatable);

    //change selects to select2
    //$("#company").select2();

  });
</script>
