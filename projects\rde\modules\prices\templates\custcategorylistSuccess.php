<?php TemplateHelper::includePartial('_tabs.php', 'prices'); ?>

<div class="box">
      <form method="post" action="<?php echo reconstructQueryAdd() ?>">
        <?php
          foreach($filter_form->getElements() as $element):
            echo $element->render(false). ' ';
          endforeach;
        ?>
        <input type="submit" name="go" id="go" value="Zoeken" />
        <input type="submit" name="reset" id="reset" value="Reset filter" />
      </form>
    </div>
  <?php if(count($items)!=0): ?>
    <div class="box">
      <form method="get">
        <input type="hidden" name="action" value="custcategoryedit"/>
        Prijzen aanpassen welke ingaan vanaf 1 januari <select name="yearfrom" id="yearfrom">
          <?php echo getOptionVal(date("Y"), date("Y")+1, date("Y")+1) ?>
        </select>
        <input type="submit" name="edit" id="edit" value="Bewerk klantgroep opslag" class="gsd-btn gsd-btn-primary" />
        <?php echo showHelpButton("Met deze knop kun je de opslag aanpassen startent op een bepaald jaar.","Bewerk opslag") ?>
      </form>
    </div>
  <?php endif; ?>
  <br/>
  <?php if(count($items)==0): ?>
    <section class="empty-list-state">
      <p><?php echo __('Er zijn geen items gevonden.') ?></p>
    </section>
  <?php else: ?>
    <table class="default_table" style="width: auto;">
      <tr class="dataTableHeadingRow">
        <td style="width: 190px;">Naam</td>
        <td style="text-align: right">Steen opslag %</td>
        <td style="text-align: right">Lijm opslag %</td>
        <td style="text-align: right">Voegsel %</td>
        <td style="text-align: right">Koud glazuur %</td>
        <td style="text-align: right">Afstand houders %</td>
        <td style="text-align: right">Glazuur webshop %</td>
        <td style="text-align: right">Natuursteen %</td>
        <td style="text-align: right">Natuursteen chinees %</td>
        <td style="text-align: right">Beton %</td>
        <td style="text-align: right">Isosill %</td>
        <td>Steenopslag Webshop ABCD %</td>
        <td style="text-align: center;width: 80px;">Geldig van</td>
        <td style="text-align: center;width: 80px;">Geldig tot</td>
      </tr>
      <?php
        /** @var CustomerGroups $item */
        foreach($items as $item):
          /** @var CustomerProductincreases $surcharge */
          $surcharge = $item->surcharge;
          ?>
        <tr class="dataTableRow trhover">
          <td><?php echo $item->name ?></td>
          <td style="text-align: right"><?php echo $surcharge->stoneIncrease!=0?$surcharge->stoneIncrease:'' ?></td>
          <td style="text-align: right"><?php echo $surcharge->glueIncrease!=0?$surcharge->glueIncrease:'' ?></td>
          <td style="text-align: right"><?php echo $surcharge->joint!=0?$surcharge->joint:'' ?></td>
          <td style="text-align: right"><?php echo $surcharge->coldGlaze!=0?$surcharge->coldGlaze:'' ?></td>
          <td style="text-align: right"><?php echo $surcharge->spacers!=0?$surcharge->spacers:'' ?></td>
          <td style="text-align: right"><?php echo $surcharge->glazeside!=0?$surcharge->glazeside:'' ?></td>
          <td style="text-align: right"><?php echo $surcharge->naturalstoneIncrease!=0?$surcharge->naturalstoneIncrease:'' ?></td>
          <td style="text-align: right"><?php echo $surcharge->naturalstoneChinaIncrease!=0?$surcharge->naturalstoneChinaIncrease:'' ?></td>
          <td style="text-align: right"><?php echo $surcharge->concreteIncrease!=0?$surcharge->concreteIncrease:'' ?></td>
          <td style="text-align: right"><?php echo $surcharge->isosillIncrease!=0?$surcharge->isosillIncrease:'' ?></td>
          <td>
            <?php echo $surcharge->stoneIncreaseGroupA!=0?$surcharge->stoneIncreaseGroupA:'' ?> |
            <?php echo $surcharge->stoneIncreaseGroupB!=0?$surcharge->stoneIncreaseGroupB:'' ?> |
            <?php echo $surcharge->stoneIncreaseGroupC!=0?$surcharge->stoneIncreaseGroupC:'' ?> |
            <?php echo $surcharge->stoneIncreaseGroupD!=0?$surcharge->stoneIncreaseGroupD:'' ?>
          </td>
          <td style="text-align: center"><?php echo $surcharge->getValidFrom() ?></td>
          <td style="text-align: center"><?php echo $surcharge->getValidTo()=="31-12-9999"?"-":$surcharge->getValidTo() ?></td>
        </tr>
      <?php endforeach; ?>
    </table>
  <?php endif; ?>

<script>
  $(document).ready(function() {
    $("#year").change(function() {
      $("#go").click();
    })
  });
</script>
<style>
  #colors_wrapper {
  }
  #colors_wrapper label {
    padding: 5px 5px 0 0;
    display: inline-block;
  }

</style>