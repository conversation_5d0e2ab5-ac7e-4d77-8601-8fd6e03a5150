<?php
class BaseStones extends AppModel {

  const DB_NAME = 'rde_b1mo';
  const TABLE_NAME = 'stones';
  const OM_CLASS_NAME = 'Stones';
  const columns = ['stoneId', 'category_id', 'short', 'name', 'type', 'material', 'endstone', 'brandId', 'colorId', 'sizeId', 'stock', 'minAmountPallet', 'minAmountStones', 'amountPerPallet', 'standardOrder', 'plantext', 'weight', 'weightm1', 'details', 'alert', 'image', 'pdfLocation', 'display', 'stoneIncreaseGroup', 'modelSpecialForProduction', 'color_model_code', 'may_order', 'may_not_order_message', 'endstones_required'];
  const field_structure = [
    'stoneId'                     => ['type' => 'int', 'length' => '4', 'null' => false],
    'category_id'                 => ['type' => 'mediumint', 'length' => '8', 'null' => true],
    'short'                       => ['type' => 'varchar', 'length' => '35', 'null' => false],
    'name'                        => ['type' => 'varchar', 'length' => '200', 'null' => false],
    'type'                        => ['type' => 'varchar', 'length' => '30', 'null' => false],
    'material'                    => ['type' => 'varchar', 'length' => '40', 'null' => false],
    'endstone'                    => ['type' => 'enum', 'length' => '8', 'null' => false, 'enums' => ['false','left','right','leftg','rightg','flat','standingside','stuc']],
    'brandId'                     => ['type' => 'tinyint', 'length' => '2', 'null' => false],
    'colorId'                     => ['type' => 'int', 'length' => '3', 'null' => false],
    'sizeId'                      => ['type' => 'int', 'length' => '3', 'null' => false],
    'stock'                       => ['type' => 'mediumint', 'length' => '9', 'null' => false],
    'minAmountPallet'             => ['type' => 'int', 'length' => '11', 'null' => true],
    'minAmountStones'             => ['type' => 'int', 'length' => '11', 'null' => false],
    'amountPerPallet'             => ['type' => 'int', 'length' => '11', 'null' => true],
    'standardOrder'               => ['type' => 'enum', 'length' => '2', 'null' => false, 'enums' => ['yes','no']],
    'plantext'                    => ['type' => 'varchar', 'length' => '8', 'null' => true],
    'weight'                      => ['type' => 'decimal', 'length' => '5,3', 'null' => false],
    'weightm1'                    => ['type' => 'decimal', 'length' => '5,3', 'null' => false],
    'details'                     => ['type' => 'longtext', 'length' => '', 'null' => true],
    'alert'                       => ['type' => 'text', 'length' => '', 'null' => true],
    'image'                       => ['type' => 'varchar', 'length' => '255', 'null' => true],
    'pdfLocation'                 => ['type' => 'varchar', 'length' => '255', 'null' => true],
    'display'                     => ['type' => 'enum', 'length' => '2', 'null' => false, 'enums' => ['true','false']],
    'stoneIncreaseGroup'          => ['type' => 'char', 'length' => '1', 'null' => true],
    'modelSpecialForProduction'   => ['type' => 'boolean', 'length' => '1', 'null' => false],
    'color_model_code'            => ['type' => 'int', 'length' => '10', 'null' => true],
    'may_order'                   => ['type' => 'boolean', 'length' => '1', 'null' => false],
    'may_not_order_message'       => ['type' => 'text', 'length' => '', 'null' => true],
    'endstones_required'          => ['type' => 'boolean', 'length' => '1', 'null' => false],
  ];

  protected static array $primary_key = ['stoneId'];
  protected string $auto_increment = 'stoneId';

  public $stoneId, $category_id, $short, $name, $type, $material, $endstone, $brandId, $colorId, $sizeId, $stock, $minAmountPallet, $minAmountStones, $amountPerPallet, $standardOrder, $plantext, $weight, $weightm1, $details, $alert, $image, $pdfLocation, $display, $stoneIncreaseGroup, $modelSpecialForProduction, $color_model_code, $may_order, $may_not_order_message, $endstones_required;

  function __construct($obj_arr = [], &$error_codes = []) {
    parent::__construct();
    $this->setDefaults();
    if (!empty($obj_arr)) {
      $this->from_array($obj_arr);
      $this->valid_required_fields($error_codes);
    }
  }

  /**
  * Set default object values
  */
  public function setDefaults() {
    $this->type = 'raamdorpel';
    $this->brandId = 1;
    $this->stock = 0;
    $this->minAmountStones = 0;
    $this->standardOrder = 'no';
    $this->weight = 0.000;
    $this->weightm1 = 0.000;
    $this->display = 'false';
    $this->modelSpecialForProduction = 0;
    $this->may_order = 1;
    $this->endstones_required = 0;
  }

  /**
   * @param array $conditions (required)
   * @param string $raw_sql (optional)
   * @return Stones[]
   */
  public static function find_all_like(?array $conditions, string $raw_sql = ''): array {
    return parent::generic_find_all_like($conditions, static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param array|null $conditions (required)
   * @param string $raw_sql (optional)
   * @return Stones[]
   */
  public static function find_all_by(?array $conditions, string $raw_sql = ''): array {
    return parent::generic_find_all_by($conditions, static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param string $raw_sql (optional)
   * @return Stones[]
   */
  public static function find_all(string $raw_sql = ''): array {
    return parent::generic_find_all(static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param array|null $conditions (required)
   * @param string $raw_sql (optional)
   * @return Stones|false
   */
  public static function find_by(?array $conditions, string $raw_sql = ''): Stones|false {
    return parent::generic_find_by($conditions, static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param int|string|null $id (required)
   * @param string $raw_sql (optional)
   * @return Stones|false
   */
  public static function find_by_id($id, string $raw_sql = ''): Stones|false {
    return parent::generic_find_by_id($id, static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param array|null $conditions (required)
   * @param string $raw_sql (optional)
   * @return int
   */
  public static function count_all_by(?array $conditions, string $raw_sql = '') {
    return parent::generic_count_all_by($conditions, static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param array|null $conditions (required)
   * @param string $raw_sql (optional)
   * @return bool
   */
  public static function delete_by(?array $conditions, string $raw_sql = ''): bool {
    return parent::generic_delete_by($conditions, static::OM_CLASS_NAME, $raw_sql);
  }

}