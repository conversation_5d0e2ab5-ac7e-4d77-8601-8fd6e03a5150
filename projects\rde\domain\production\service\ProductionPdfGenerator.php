<?php

  namespace domain\production\service;

  use DbHelper;
  use OrderElements;
  use ProductionReceiptCombinedPdf;
  use ProductionReceiptEmptyPdf;
  use ProductionReceiptPdf;
  use ProductionReceiptWebshopPdf;
  use Projects;

  class ProductionPdfGenerator {
    private string $quotationId;
    public function __construct(string $quotationId) {
      $this->quotationId = DbHelper::escape($quotationId);
    }
    public function generatePdf(): void {
      $hasWebshopProducts = !!Projects::find_by(['quotationId' => $this->quotationId, 'showOnProductionPage' => '1']);
      $hasStoneElementProducts = !!OrderElements::find_by(['quotationId' => $this->quotationId]);

      // Determine which PDFs to show based on product types
      if ($hasWebshopProducts && $hasStoneElementProducts) {
        // Combine both PDFs if both product types exist
        $combinedPdf = new ProductionReceiptCombinedPdf($this->quotationId);
        $combinedPdf->generate();
      }
      elseif ($hasStoneElementProducts) {
        // Show only the stone element products PDF
        $pdf = new ProductionReceiptPdf($this->quotationId);
        $pdf->generate();
      }
      elseif ($hasWebshopProducts) {
        // Show only the webshop products PDF
        $webshopPdf = new ProductionReceiptWebshopPdf($this->quotationId);
        $webshopPdf->generate();
      }
      else {
        // No products to show, display a blank/empty PDF
        $emptyPdf = new ProductionReceiptEmptyPdf($this->quotationId);
        $emptyPdf->generate();
      }
    }
  }