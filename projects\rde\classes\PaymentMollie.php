<?php

  class PaymentMollie extends Payment {

    public function __construct() {
      $this->key = Payment::PAYMENT_MOLLIE;
      $this->enable_discount = false;

      $mollie_config = Config::get("PAYMENT_MOLLIE");
      $this->title = isset($mollie_config['title']) ? $mollie_config['title'] : 'Mollie: iDeal e.a. ';
      $this->short_title = isset($mollie_config['short_title']) ? $mollie_config['short_title'] : 'Mollie';
      $this->setLogoUrl("/gsdfw/images/paymentlogos/ideal.png");
    }

    /**
     * Check if payment method is available in current config
     *
     * @return bool
     */
    public static function isAvailable() {
      if (Config::isdefined('PAYMENT_MOLLIE')) {
        $mollie = Config::get('PAYMENT_MOLLIE');
        if ($mollie['enabled'] && $mollie['api-key'] != "") {
          return true;
        }
      }
      return false;
    }

    public static function getMollie() {
      $mollieconfig = Config::get('PAYMENT_MOLLIE');
      $mollie = new Mollie\Api\MollieApiClient();
      $mollie->setApiKey($mollieconfig['api-key']);
      return $mollie;
    }

    /**
     * @param Quotations $quotation
     * @param $total
     */
    public function handlePayment($quotation, $total) {

      // Gebruik Ngrok zodat mollie een localhost url kan aanspreken voor het testen, url voorbeeld: http://c17d8580.ngrok.io/payment_postback_mollie.php
      // gebruik dit commando om op te starten en door te verwijzen: "ngrok http -host-header=www.gsd.nl.gsd.localhost 80"
      // bekijk vervolgens via: http://127.0.0.1:4040/inspect/http

      try {
        $mollie = PaymentMollie::getMollie();
        $url = $_SESSION['site']->site_host->getDomain(true);
        if (DEVELOPMENT) {
          $url = "http://581a14136ea1.ngrok.io/";
        }
        $values = [
          "amount"      => [
            "currency" => "EUR",
            "value"    => number_format($total, 2, '.', ''),
          ],
          "description" => "Raamdorpel webshop " . $quotation->getQuotationNumberFull(),
          "redirectUrl" => $url . PageMap::getUrl('M_BASKET') . "?action=payfinished&quotationId=" . $quotation->quotationId,
          "webhookUrl"  => $url . PageMap::getUrl('M_EXTERNAL') . "?action=paymentpostback&payvariant=mollie",
          "locale"      => strtolower($_SESSION['lang']) . '_' . strtoupper($_SESSION['lang']),
          "metadata"    => [
            "quotationId" => $quotation->quotationId,
          ],
        ];

        $payment = $mollie->payments->create($values);

        ResponseHelper::redirect($payment->getCheckoutUrl());

      }
      catch (Exception $e) {
        logToFile('mollie_exception', 'handlepayment: quotationId=' . $quotation->quotationId . ' Error: ' . $e->getMessage());
      }

    }

    /**
     * Handles postback from paymentserver
     */
    public static function paymentpostback() {

      if ($_SERVER['REQUEST_METHOD'] == 'POST') {
        logToFile("mollie", "start Mollie postback " . print_r($_POST, true));

        $mollie = PaymentMollie::getMollie();
        $payment = $mollie->payments->get($_POST["id"]);
        if (isset($payment->metadata->quotationId)) {
          $quotationId = $payment->metadata->quotationId;
          if ($quotationId != "") {
            $quotation = Quotations::getById($quotationId);

            logToFile("mollie", 'payment_postback mollie: quotationId=' . $quotationId);
            if (!$quotation) {
              logToFile("mollie", 'payment_postback mollie: QUOTATION NIET GEVONDEN');
            }
            elseif ($quotation->paymentMethod == Payment::PAYMENT_MOLLIE) {

              if ($quotation->payedFlag == 1) {
                //al betaald. overslaan. waarschijnlijk is dit een verlate annulering / verlopen
                logToFile("mollie", 'payment_postback mollie: bestelling is al betaald. Overgeslagen. PaymentId=' . $_POST["id"] . ', status = ' . $payment->status);
                return;
              }

              $m = Mollie::add($_POST["id"], $payment->amount->value);
              $quotation->paymentMethod = "mollie";
              $quotation->mollie_id = $m->id;
              $quotation->save();

              if ($payment->isPaid()) { //succesvolle betaling!
                $quotation->paid_online();
                logToFile("mollie", 'payment_postback mollie: bestelling betaald');
              }
              elseif ($payment->isCanceled() || $payment->isExpired()) {
                //order has not been paid, cleanup if needed
                if ($quotation->mollie_id != "") {
                  $m = Mollie::find_by_id($quotation->mollie_id);
                  if ($m) {
                    $m->destroy();
                  }
                  $quotation->mollie_id = null;
                  $quotation->paymentMethod = "overmaken";
                  $quotation->save();
                }
                logToFile("mollie", 'payment_postback mollie: status ' . ($payment->isCanceled() ? 'cancelled' : ($payment->isCanceled() ? 'verlopen' : '')));
              }
              else {
                logToFile("mollie", 'payment_postback mollie afgebroken: status = ' . $payment->status);
              }
            }
            logToFile("mollie", 'payment_postback mollie: done');
          }
          else {
            logToFile("mollie", 'payment_postback mollie: quotationId leeg');
          }
        }
        elseif (isset($payment->metadata->linkInvoiceId)) {
          //bestelling betaald vanuit paylink
          $invoiceId = $payment->metadata->linkInvoiceId;

          if ($invoiceId != "") {
            $invoice = Invoices::find_by(["invoiceId" => $invoiceId]);

            logToFile("mollie", 'payment_postback mollie paylink: invoiceId=' . $invoiceId);
            if (!$invoice) {
              logToFile("mollie", 'payment_postback mollie: INVOICE NIET GEVONDEN');
            }
            else {

              if ($invoice->paid != "") {
                //al betaald. overslaan. waarschijnlijk is dit een verlate annulering / verlopen
                logToFile("mollie", 'payment_postback mollie: factuur is al betaald. Overgeslagen. PaymentId=' . $_POST["id"] . ', status = ' . $payment->status);
                return;
              }

              $m = Mollie::add($_POST["id"], $invoice->totalProjectValue);
              $invoice->mollie_id = $m->id;
              $invoice->save();

              if ($payment->isPaid()) { //succesvolle betaling!
                $invoice->paid_online_link();
                logToFile("mollie", 'payment_postback mollie: invoice betaald');
              }
              elseif ($payment->isCanceled() || $payment->isExpired()) {
                //order has not been paid, cleanup if needed
                if ($invoice->mollie_id != "") {
                  $m = Mollie::find_by_id($invoice->mollie_id);
                  if ($m) {
                    $m->destroy();
                  }
                  $invoice->mollie_id = null;
                  $invoice->save();
                }
                logToFile("mollie", 'payment_postback mollie: status ' . ($payment->isCanceled() ? 'cancelled' : ($payment->isCanceled() ? 'verlopen' : '')));
              }
              else {
                logToFile("mollie", 'payment_postback mollie afgebroken: status = ' . $payment->status);
              }
            }
            logToFile("mollie", 'payment_postback mollie: done');
          }
          else {
            logToFile("mollie", 'payment_postback mollie: invoiceId leeg');
          }
        }
        elseif (isset($payment->metadata->quotationIds)) {
          //bestellingen betaald
          $quotationIds = $payment->metadata->quotationIds;

          if ($quotationIds != "") {
            logToFile("mollie", 'payment_postback mollie prepaid: quotationIds=' . $quotationIds);

            $quotationIds = explode(",", $quotationIds);
            $quotations = Quotations::find_all_by(["quotationId" => $quotationIds]);

            if (count($quotations) == 0) {
              logToFile("mollie", 'payment_postback mollie prepaid: QUOTATIONS NIET GEVONDEN');
            }
            else {
              $isPayed = false;
              foreach ($quotations as $quotation) {
                if ($quotation->payedFlag == 1) {
                  $isPayed = true;
                  break;
                }
              }
              if ($isPayed) {
                //al betaald. overslaan. waarschijnlijk is dit een verlate annulering / verlopen
                logToFile("mollie", 'payment_postback prepaid: bestellingen zijn al betaald. Overgeslagen. PaymentId=' . $_POST["id"] . ', status = ' . $payment->status);
                return;
              }

              $m = Mollie::add($_POST["id"], $payment->amount->value);
              foreach ($quotations as $quotation) {
                $quotation->mollie_id = $m->id;
                $quotation->paymentMethod = "mollie";
                $quotation->save();
              }

              if ($payment->isPaid()) { //succesvolle betaling!
                $sendMail = false;
                foreach ($quotations as $quotation) {
                  if ($quotation->payedFlag == 0) {
                    $sendMail = true; //prevent double mail
                  }
                  $quotation->setPaid();
                }
                if ($sendMail) {
                  MailsFactory::sendQuotationsPayedMail($quotations);
                }

                logToFile("mollie", 'payment_postback prepaid: bestellingen betaald');
              }
              elseif ($payment->isCanceled() || $payment->isExpired()) {
                //order has not been paid, cleanup if needed
                if ($quotation->mollie_id != "") {
                  foreach ($quotations as $quotation) {
                    $m = Mollie::find_by_id($quotation->mollie_id);
                    if ($m) {
                      $m->destroy();
                    }
                    $quotation->mollie_id = null;
                    $quotation->paymentMethod = "overmaken";
                    $quotation->save();
                  }
                }
                logToFile("mollie", 'payment_postback prepaid: status ' . ($payment->isCanceled() ? 'cancelled' : ($payment->isCanceled() ? 'verlopen' : '')));
              }
              else {
                logToFile("mollie", 'payment_postback prepaid: afgebroken: status = ' . $payment->status);
              }
            }
            logToFile("mollie", 'payment_postback prepaid: done');
          }
          else {
            logToFile("mollie", 'payment_postback mollie: quotationIds leeg');
          }
        }
        else {
          logToFile("mollie", 'payment_postback mollie: ONBEKENDE VARIANT??');
        }
      }
      else {
        logToFile("mollie", 'payment_postback mollie: NO POST');
      }

    }

    /**
     * @param Invoices $invoice
     * @param string $description
     * @param bool string $method: voorselectie betaalmethode
     */
    public function handlePaymentInvoiceLink($invoice, $description, $method = false) {

      // Gebruik Ngrok zodat mollie een localhost url kan aanspreken voor het testen, url voorbeeld: http://c17d8580.ngrok.io/payment_postback_mollie.php
      // gebruik dit commando om op te starten en door te verwijzen: "ngrok http -host-header=www.gsd.nl.gsd.localhost 80"
      // bekijk vervolgens via: http://127.0.0.1:4040/inspect/http

      try {
        $mollie = PaymentMollie::getMollie();
        $url = $_SESSION['site']->site_host->getDomain(true);
        if (DEVELOPMENT) {
          $url = "http://0be743c5.ngrok.io";
        }

        $values = [
          "amount"      => [
            "currency" => "EUR",
            "value"    => number_format($invoice->totalProjectValue, 2, '.', ''),
          ],
          "description" => $description,
          "redirectUrl" => $url . "/nl/winkelmandje?action=paylinkinvoicedone&id=" . $invoice->invoiceId,
          "webhookUrl"  => $url . PageMap::getUrl('M_EXTERNAL') . "?action=paymentpostback&payvariant=mollie",
          "locale"      => strtolower($_SESSION['lang']) . '_' . strtoupper($_SESSION['lang']),
          "metadata"    => [
            "linkInvoiceId" => $invoice->invoiceId,
          ],
        ];

        if ($method && $method != "") { //sla betaalmethode scherm over, en ga direct de betrefende betaling
          $values["method"] = $method;
        }

        $payment = $mollie->payments->create($values);

        ResponseHelper::redirect($payment->getCheckoutUrl());

      }
      catch (Exception $e) {
        logToFile('mollie_exception', 'handlePaymentInvoiceLink: invoiceId=' . $invoice->invoiceId . ' Error: ' . $e->getMessage());
      }

    }

    /**
     * @param Quotations[] $quotations
     * @param      $description
     * @param bool $method
     */
    public function handlePaymentQuotations($quotations, $amount, $method = false) {

      // Gebruik Ngrok zodat mollie een localhost url kan aanspreken voor het testen, url voorbeeld: http://c17d8580.ngrok.io/payment_postback_mollie.php
      // gebruik dit commando om op te starten en door te verwijzen: "ngrok http -host-header=www.gsd.nl.gsd.localhost 80"
      // bekijk vervolgens via: http://127.0.0.1:4040/inspect/http

      try {
        $mollie = PaymentMollie::getMollie();
        $url = $_SESSION['site']->site_host->getDomain(true);
        if (DEVELOPMENT) {
          $url = "http://00eebc26.ngrok.io";
        }

        $quotationsNumbers = [];
        $quotationsIds = [];
        foreach ($quotations as $quotation) {
          $quotationsNumbers[] = $quotation->quotationNumber;
          $quotationsIds[] = $quotation->quotationId;
        }
        $quotationsIdsStr = implode(",", $quotationsIds);

        $values = [
          "amount"      => [
            "currency" => "EUR",
            "value"    => number_format($amount, 2, '.', ''),
          ],
          "description" => "Rde: " . implode(", ", $quotationsNumbers),
          "redirectUrl" => $url . "/offerte?action=prepaiddone&ids=" . $quotationsIdsStr,
          "webhookUrl"  => $url . PageMap::getUrl('M_EXTERNAL') . "?action=paymentpostback&payvariant=mollie",
          "locale"      => strtolower($_SESSION['lang']) . '_' . strtoupper($_SESSION['lang']),
          "metadata"    => [
            "quotationIds" => $quotationsIdsStr,
          ],
        ];

        if ($method && $method != "") { //sla betaalmethode scherm over, en ga direct de betrefende betaling
          $values["method"] = $method;
        }

        $payment = $mollie->payments->create($values);

        ResponseHelper::redirect($payment->getCheckoutUrl());

      }
      catch (Exception $e) {
        logToFile('mollie_exception', 'handlePaymentQuotations: quotationsIds=' . $quotationsIds . ' Error: ' . $e->getMessage());
      }

    }

    /**
     * @return array
     */
    public static function getActivePaymentMethods(): array {
      $mollie_config = Config::get("PAYMENT_MOLLIE");
      return $mollie_config["paymentmethods"];
    }

  }
