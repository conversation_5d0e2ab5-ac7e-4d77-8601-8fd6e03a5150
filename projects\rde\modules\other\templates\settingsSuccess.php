<section class="title-bar">
  <h1>
    <?php echo Navigation::getItem(Navigation::getActivePageId())->getName() ?>
  </h1>
  <?php TemplateHelper::includePartial("_tabs.php","other") ?>
</section>


<script type="text/javascript">
	$(document).ready(function() {
		$("#btn_logoff").on("click", function() {
			$("#logoff").val(1);
			$("#go").trigger("click");
		});
	});
</script>

<?php writeErrors($errors) ?>

<form method="post" class="edit-form" >
	<table class="default_table">
		<tr class="dataTableHeadingRow">
			<td>Item</td>
			<td>Instelling</td>
		</tr>
		<?php if($_SESSION['userObject']->usergroup == User::USERGROUP_ADMIN): ?>
      <input type="hidden" value="1" id="admingo" name="admingo" />
      <tr class="dataTableRow hover">
        <td class="head">Lock applicatie</td>
        <td>
          <label><input type="checkbox" value="1" name="locked" <?php writeIfCheckedVal($locked->value, 1)?>/>Alleen de administrator kan nog inloggen. Alle gebruikers worden bij locken applicatie direct uitgelogd.</label>
          <?php if($locked->value==1): ?><div style="color: red;font-weight: bold;border: 1px solid red;display: inline-block;padding: 5px;">COMPLETE SYSTEEM IS NU GELOCKED!</div><?php endif; ?>
        </td>
      </tr>
      <tr class="dataTableRow hover">
        <td class="head">Alle gebruikers uitloggen</td>
        <td>
          <input type="button" value="Alle gebruikers nu uitloggen" name="btn_logoff" id="btn_logoff"/>
          <input type="hidden" value="" id="logoff" name="logoff" />
        </td>
      </tr>
		<?php endif; ?>
    <?php if(isset($settings)): ?>
      <?php foreach($settings as $setting): ?>
        <tr class="dataTableRow hover">
          <td class="head"><?php echo $descriptions[$setting->code] ?></td>
          <td>
            <input type="text" value="<?php echo escapeForInput($setting->value) ?>" name="values[<?php echo $setting->code  ?>]" id="<?php echo $setting->code  ?>"/>
          </td>
        </tr>
      <?php endforeach; ?>
    <?php endif; ?>
    <?php if (isset($settings_b1mo)): ?>
      <?php foreach ($settings_b1mo as $setting_b1mo): ?>
        <tr class="dataTableRow hover">
          <td class="head"><?php echo $setting_b1mo->description ?></td>
          <td>
            <input type="text" value="<?php echo escapeForInput($setting_b1mo->setting) ?>" name="<?php echo $setting_b1mo->name ?>" id="<?php echo $setting_b1mo->name  ?>"/>
          </td>
        </tr>
      <?php endforeach; ?>
    <?php endif; ?>
  </table>

	<input type="submit" name="go" id="go" value="Opslaan" class="gsd-btn gsd-btn-primary"/>
</form>


