<?php

  AppModel::loadBaseClass('BaseWindowsill');

  class WindowsillModel extends BaseWindowsill {

    public function removeImageFile() {
      if ($this->imagefilename != "") {
        if (file_exists(DIR_ROOT_HTTPDOCS . '/images/thresholds/vensterbanken/' . $this->imagefilename)) {
          unlink(DIR_ROOT_HTTPDOCS . '/images/thresholds/vensterbanken/' . $this->imagefilename);
        }
        $this->imagefilename = null;
      }
    }


    public function removeImageSmallFile() {
      if ($this->imagesmallfilename != "") {
        if (file_exists(DIR_ROOT_HTTPDOCS . '/images/thresholds/vensterbanken/' . $this->imagesmallfilename)) {
          unlink(DIR_ROOT_HTTPDOCS . '/images/thresholds/vensterbanken/' . $this->imagesmallfilename);
        }
        $this->imagesmallfilename = null;
      }
    }

    public function getMetric($xnumber) {
      if ($this->{"x" . $xnumber} == "Hoek") {
        return '°';
      }
      return 'mm';
    }

    public function destroy() {
      $this->removeImageFile();
      $this->removeImageSmallFile();
      parent::destroy();
    }

  }