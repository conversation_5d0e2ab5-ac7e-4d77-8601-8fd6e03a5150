<?php

  /**
   * Api Session Object
   *
   * @package    GSDframework
   * <AUTHOR>
   * @copyright  2006-2017
   * @link       https://www.gsd.nl
   */
  class gsdApiSession {

    private static $instance = null;
    protected $user = null;

    public static function getInstance() {
      if (is_null(self::$instance)) {
        self::$instance = new gsdApiSession();
      }

      return self::$instance;
    }

    /**
     * Set user on action class
     * @param $userId
     */
    public static function setUser($userId) {
      $pe = ProductionEmployees::find_by(["employeeId" => $userId]);
      //ophalen worker id van employee voor koppeling met urenregistratie.
      if ($pe) {
        $worker = Worker::find_by(["external_id" => $pe->employeeId]);
        if ($worker) {
          $pe->worker_id = $worker->id;
        }
        gsdApiSession::getInstance()->user = $pe;
      }
    }

    /**
     * Get user from action class
     * @return Object
     */
    public static function getUser() {
      return gsdApiSession::getInstance()->user;
    }

    /**
     * Get user id from action class
     * @return Object
     */
    public static function getUserId() {
      if (gsdApiSession::getUser() != null) {
        return gsdApiSession::getUser()->employeeId;
      }
    }

  }