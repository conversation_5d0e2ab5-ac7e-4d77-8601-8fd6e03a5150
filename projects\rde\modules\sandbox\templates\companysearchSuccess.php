<?php
  TemplateHelper::includePartial('_tabs.php', 'sandbox');
  include("_header.php")
?>

<h3>1. Bedrijf aanmaken of vergelijken</h3>

  <div class="list-filter-form">
    <a href="?action=companycreate&id=<?php echo $sandboxuser->userId ?>" class="gsd-btn gsd-btn-primary" title="Aanmaken nieuw bedrijf">Aanmaken nieuw bedrijf</a>
  </div>

  <?php if(count($companies)==0): ?>
    <br/><br/>
    Er zijn geen bedrijven gevonden welke overeenkomsten vertonen met het te koppelen bedrijf.
  <?php else: ?>
    <table class="default_table">
      <tr class="dataTableHeadingRow">
        <td>Bedrijfsnaam</td>
        <td>Adres</td>
        <td>Postcode</td>
        <td>Plaats</td>
        <td>Koppelen</td>
      </tr>
      <?php
        /** @var CrmCompanies $company */
        foreach($companies as $company): ?>
        <tr class="dataTableRow trhover">
          <td><?php echo $company->name ?></td>
          <td><?php if($company->visit) echo $company->visit->getAddress() ?></td>
          <td><?php if($company->visit) echo $company->visit->zipcode ?></td>
          <td><?php if($company->visit) echo $company->visit->domestic ?></td>
          <td>
            <?php if($company->visit):  ?>
              <a href="<?php echo reconstructQueryAdd(['pageId'])?>action=companycompare&id=<?php echo $sandboxuser->userId ?>&relatedid=<?php echo $company->companyId ?>" class="gsd-btn gsd-btn-secondary">
                Vergelijken
              </a>
            <?php else: ?>
              <?php echo showHelpButton("Dit bedrijf heeft geen bezoekadres, dus kan niet gebruikt worden.") ?>
            <?php endif; ?>
          </td>
        </tr>
      <?php endforeach; ?>
    </table>
  <?php endif; ?>

