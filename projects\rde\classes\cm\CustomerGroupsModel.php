<?php

  AppModel::loadBaseClass('BaseCustomerGroups');

  class CustomerGroupsModel extends BaseCustomerGroups {

    /**
     * @param int $typeid : 1 = bouwbedrijf/aannemer, 2 = bouwmetshandelaar/bouwmaterialen
     * @return array
     */
    public static function getIdsByTypeid($typeid) {
      $query = "SELECT groupId FROM " . CustomerGroups::getTablename() . " ";
      $query .= "WHERE typeId=" . $typeid;
      $result = DBConn::db_link()->query($query);
      $ids = [];
      while ($row = $result->fetch_assoc()) {
        $ids[] = $row["groupId"];
      }
      return $ids;
    }

  }