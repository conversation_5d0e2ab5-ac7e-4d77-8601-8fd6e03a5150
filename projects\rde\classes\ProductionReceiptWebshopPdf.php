<?php

  class ProductionReceiptWebshopPdf extends GSDPDF {

    protected $supplierExternal = false;
    protected $multiple = false;

    public function __construct($quotation_id) {

      parent::__construct();

      $this->AddFont('Nunito', '', 'Nunito-Regular.ttf', true);
      $this->AddFont('Nunito', 'B', 'Nunito-Bold.ttf', true);
      $this->SetAutoPageBreak(true, 30);

      $this->quotation = Quotations::find_by(['quotationId' => $quotation_id]);
      $this->sandbox_user = SandboxUsers::find_by(['userId' => $this->quotation->userId]);
      $this->quotation_extra = QuotationsExtra::find_by(['quotationId' => $quotation_id]);
      $this->seam_color = SeamColor::find_by(['seamColorId' => $this->quotation_extra->seamColorId]);
      $this->driving_routes = DrivingRoutes::find_by([], ' WHERE ' . substr($this->quotation->zipcode, 0, -2) . ' BETWEEN driving_routes.start AND driving_routes.end ');
      $this->stones = Stones::find_by(['stoneId' => $this->quotation->stoneId]) ?: null;
      $this->stone_sizes = StoneSizes::find_by(['sizeId' => $this->stones?->sizeId]) ?: null;
      $this->stone_brands = StoneBrands::find_by(['brandId' => $this->stones?->brandId]) ?: null;
      $this->stone_color = StoneColors::find_by(['colorId' => $this->stones?->colorId]) ?: null;
      $this->quotation_custom_stone = QuotationsCustomStone::find_by(['quotationId' => $quotation_id]);
      $this->order_elements = OrderElements::find_all_by([ 'quotationId' => $quotation_id]);
    }

    /**
     * @param bool $supplierExternal
     */
    public function setSupplierExternal(bool $supplierExternal): void {
      $this->supplierExternal = $supplierExternal;
    }

    /**
     * @return bool
     */
    public function isSupplierExternal(): bool {
      return $this->supplierExternal;
    }

    /**
     * @param bool $multiple
     */
    public function setMultiple(bool $multiple): void {
      $this->multiple = $multiple;
    }

    /**
     * @return bool
     */
    public function isMultiple(): bool {
      return $this->multiple;
    }

    public function build() {

      $this->addPage();

      $quotation = $this->quotation;
      $sandbox_user = $this->sandbox_user;
      $quotation_extra = $this->quotation_extra;
      $seam_color = $this->seam_color;
      $driving_routes = $this->driving_routes;
      $stones = $this->stones;
      $stone_size = $this->stone_sizes;
      $stone_brand = $this->stone_brands;
      $stone_color = $this->stone_color;
      $quotation_custom_stone = $this->quotation_custom_stone;
      $order_elements = $this->order_elements;

      $this->SetFont('Arial', '', 8);
      $this->SetDrawColor(50, 50, 50);
      $this->SetTextColor(0, 0, 0);
      $this->SetLeftMargin(10);
      $this->SetTopMargin(0);

      $this->SetAutoPageBreak(true, 2);

      $fontSizeMainData = 10;
      $cellHeightTopRight = 10;
      $xyCoordinatesStart = 5;
      $xyCoordinatesFromLeft = 5;

      $this->setXY($xyCoordinatesFromLeft, $xyCoordinatesStart);

      $this->SetLineWidth(0.2);
      $this->SetDrawColor(255, 0, 0);
      $this->SetFillColor(0, 255, 0);

      $this->SetFont('Arial', 'B', 16);
      $this->setXY(5, 5);
      $this->Cell(50, 10, 'Productiestaat', 0, 1);
      $this->SetDrawColor(255, 0, 0);

      $quotationNumber = $quotation->getQuotationNumberFull();
      $quotationDisplay = "$quotationNumber::Webshop";

      $this->setXY(5, 12);
      $this->Cell(50, $cellHeightTopRight, $quotationDisplay, 0);

      //- left, top
      $this->setXY(0, 0);

      //-- location, x, y, w, h, tyle, link
      $this->Image('https://www.raamdorpel.nl/3rdparty/barcodegen/datamatrix.php?text=' . $quotationDisplay, 75, 7, 13, 13, 'PNG');

      //-- rood text
      $this->SetTextColor(255, 0, 0);

      if ($quotation->mountingPrice > 0) {
        $this->setXY(0, 0);
        $this->setXY(80, 5);
        $this->SetFont('Arial', 'B', 14);
        $this->Cell(22, $cellHeightTopRight, 'montage', 1);
      }

      //-- niet laten zien bij Grijs (standaard)
      //-- deze straks weer aanzetten.
      if ($seam_color->seamColorId != 1) {
        $this->setXY(0, 0);
        $this->setXY(110, 5);
        $this->SetFont('Arial', 'B', 14);
        $this->Multicell(35, 6, 'voegkleur ' . $seam_color->name, 1);
      }

      if ($quotation->toNumberQuotations == '1') {
        $this->setXY(0, 0);
        $this->setXY(153, 5);
        $this->SetFont('Arial', 'B', 14);
        $this->Multicell(28, 6, 'elementen nummeren', 1);
      }

      //-- zwart text
      $this->SetTextColor(0, 0, 0);
      $this->setXY(0, 0);

      //-- code
      $this->setXY(200, 5);

      if (isset($driving_routes->code)) {
        //-- w, h
        $this->Cell(5, 10, $driving_routes->code, 0);
      }
      else {
        //-- w, h
        $this->Cell(5, 10, '', 0);
      }

      $cellHeightMainData = 5;
      $cellWidthMainData = 50;
      $cellWidthMainData1 = 30;
      $cellWidthMainData2 = 70;


        // Bedrijfsnaam
        $this->setXY(5, 22);
        $this->SetFont('Arial', 'B', $fontSizeMainData);
        $this->Cell($cellWidthMainData1, $cellHeightMainData, 'Bedrijfsnaam');

        $this->setXY(37, 22);
        $this->SetFont('Arial', '', $fontSizeMainData);
        $this->Cell($cellWidthMainData2, $cellHeightMainData, $sandbox_user->companyName);

        // Contactpersoon
        if ($sandbox_user->personId > 0) {
          $person = CrmPersons::find_by(['personId' => $sandbox_user->personId]);
          $person_first_name = $person->firstName;
          $person_last_name = $person->lastName;
        }
        else {
          $person_first_name = '';
          $person_last_name = '';
        }

        // Contactpersoon:
        $this->setXY(5, 28);
        $this->SetFont('Arial', 'B', $fontSizeMainData);
        $this->Cell($cellWidthMainData1, $cellHeightMainData, 'Contactpersoon', 0);

        $this->setXY(37, 28);
        $this->SetFont('Arial', '', $fontSizeMainData);
        $this->Cell($cellWidthMainData2, $cellHeightMainData, $person_first_name . ' ' . $person_last_name);

        // Adres
        $address = $sandbox_user->street;
        if ($sandbox_user->nr !== '') {
          $address .= ' ' . $sandbox_user->nr;
        }
        if ($sandbox_user->extension !== '') {
          $address .= ' ' . $sandbox_user->extension;
        }
        $this->setXY(5, 34);
        $this->SetFont('Arial', 'B', $fontSizeMainData);
        $this->Cell($cellWidthMainData1, $cellHeightMainData, 'Adres', 0);

        $this->setXY(37, 34);
        $this->SetFont('Arial', '', $fontSizeMainData);
        $this->Cell($cellWidthMainData2, $cellHeightMainData, $address, 0);

        // Postcode
        $this->setXY(5, 40);
        $this->SetFont('Arial', 'B', $fontSizeMainData);
        $this->Cell($cellWidthMainData1, $cellHeightMainData, 'Postcode', 0);

        $this->setXY(37, 40);
        $this->SetFont('Arial', '', $fontSizeMainData);
        $this->Cell($cellWidthMainData2, $cellHeightMainData, $sandbox_user->zipcode, 0);

        // Plaats
        $this->setXY(5, 46);
        $this->SetFont('Arial', 'B', $fontSizeMainData);
        $this->Cell($cellWidthMainData1, $cellHeightMainData, 'Plaats', 0);

        $this->setXY(37, 46);
        $this->SetFont('Arial', '', $fontSizeMainData);
        $this->Cell($cellWidthMainData2, $cellHeightMainData, $sandbox_user->domestic, 0);

        // Telefoonnummer
        $this->setXY(5, 52);
        $this->SetFont('Arial', 'B', $fontSizeMainData);
        $this->Cell($cellWidthMainData1, $cellHeightMainData, 'Telefoonnummer', 0);

        $this->setXY(37, 52);
        $this->SetFont('Arial', '', $fontSizeMainData);
        $this->Cell($cellWidthMainData2, $cellHeightMainData, $sandbox_user->phone, 0);

        // Datum
        $this->setXY(5, 58);
        $this->SetFont('Arial', 'B', $fontSizeMainData);
        $this->Cell($cellWidthMainData1, $cellHeightMainData, 'Datum', 0);

        $this->setXY(37, 58);
        $this->SetFont('Arial', '', $fontSizeMainData);
        $this->Cell($cellWidthMainData2, $cellHeightMainData, DateTimeHelper::convertFormat($quotation->quotationDate, 'Y-m-d', 'd-m-Y'), 0);

      if ($quotation->productionNotes != '') {

        // Opmerkingen
        $this->setXY(5, 72);
        $this->SetFont('Arial', 'B', $fontSizeMainData);
        $this->Cell($cellWidthMainData1, $cellHeightMainData, 'Opmerkingen', 0);

        $this->SetTextColor(255, 0, 0);

        $this->setXY(37, 72);
        $this->SetFont('Arial', '', $fontSizeMainData);
        $this->Multicell(170, $cellHeightMainData, $quotation->productionNotes, 0);

      }

      $this->SetTextColor(0, 0, 0);

      $this->SetLeftMargin(105);
      $this->setXY(105, 22);
      $this->SetFont('Arial', '', $fontSizeMainData);
      $cellWidthMainDataRight = 29;
      $cellHeightMainDataRight = 6;

      // Projectnaam
      $this->SetFont('Arial', 'B');
      $this->Cell($cellWidthMainDataRight, $cellHeightMainDataRight, 'Projectnaam', 0);
      $this->SetFont('Arial', '');
      $this->Cell($cellWidthMainDataRight, $cellHeightMainDataRight, $quotation->projectName, 0);
      $this->Ln();

      // Kenmerk
      $this->SetFont('Arial', 'B');
      $this->Cell($cellWidthMainDataRight, $cellHeightMainDataRight, 'Kenmerk', 0);
      $this->SetFont('Arial', '');
      $this->Cell($cellWidthMainDataRight, $cellHeightMainDataRight, $quotation->projectReference, 0);
      $this->Ln();

      // Merk
//      require_once($_SERVER['DOCUMENT_ROOT'] . '/cms/crm/classes/clsStones.php');
//      $oStones = new clsStones();
//      $oStones->quotationId = $this->quoteId;
//      $stoneDescription = $oStones->getStoneDescription();

      $this->SetFont('Arial', 'B');
      $this->Cell($cellWidthMainDataRight, $cellHeightMainDataRight, 'Merk', 0);
      $this->SetFont('Arial', '');
      $this->Cell($cellWidthMainDataRight, $cellHeightMainDataRight, $stone_brand?->name, 0);
      $this->Ln();

      // Kleur
      $color = '';
      if ($stone_color && ($stone_brand?->stoneTypeId == 2 || $stone_brand?->stoneTypeId == 3)) {
        $color = $stone_color->short . ' ' . $stone_color->name;
      }
      else if ($stone_color) {
        if ($stone_color->glaced == 'true') {
          $color = $stone_color->short . ' ' . $stone_color->name . ' geglazuurd';
        }
        else {
          $color = $stone_color->short . ' ' . $stone_color->name . ' ongeglazuurd';
        }
      }
      if ($quotation->ralColor) {
        $color = $quotation->ralColor;
      }

      $label = $quotation->ralColor ? 'RAL-kleur' : 'Kleur';
      $this->SetFont('Arial', 'B');
      $this->Cell($cellWidthMainDataRight, $cellHeightMainDataRight, $label, 0);
      $this->SetFont('Arial', '');
      $this->Cell($cellWidthMainDataRight, $cellHeightMainDataRight, trim($color), 0);
      $this->Ln();

      // Model
//      $oStones->sizeId = $stoneDescription->sizeId;
      $model_name = $stone_size?->short;
      if ($stones?->type == "balkjes") {
        $model_name = "Divers";
      }

      if ($quotation->endstone == 'true_grooves') {
        $stone_end = 'Ja, groeven';
      }
      elseif ($quotation->endstone == 'true') {
        $stone_end = 'Ja, opstaande zijkanten';
      }
      elseif ($quotation->endstone == 'false') {
        $stone_end = 'Nee';
      }

      $this->SetFont('Arial', 'B');
      $this->Cell($cellWidthMainDataRight, $cellHeightMainDataRight, 'Model', 0);
      $this->SetFont('Arial', '');
      $this->Cell($cellWidthMainDataRight, $cellHeightMainDataRight, $model_name, 0);
      $this->Ln();

      if (!in_array($stone_brand?->stoneTypeId, [2, 3])) {
        // Eindstenen
        $this->SetFont('Arial', 'B');
        $this->Cell($cellWidthMainDataRight, $cellHeightMainDataRight, 'Eindstenen', 0);
        $this->SetFont('Arial', '');
        $this->Cell($cellWidthMainDataRight, $cellHeightMainDataRight, $stone_end, 0);
        $this->Ln();
      }

      // Aantal meter
      $this->SetFont('Arial', 'B');
      $this->Cell($cellWidthMainDataRight, $cellHeightMainDataRight, 'Aantal meter', 0);
      $this->SetFont('Arial', '');
      $this->Cell($cellWidthMainDataRight, $cellHeightMainDataRight, $quotation->meters, 0);
      $this->Ln();

      $total_element_count = 0;
      if ($order_elements) {
        $total_element_count = count($order_elements);
      }

      if ($quotation->stoneCategoryId == 13) { //natuursteen_vensterbank
        $this->SetFont('Arial', 'B');
        $this->Cell($cellWidthMainDataRight, $cellHeightMainDataRight, 'Oppervlakte m2', 0);
        $this->SetFont('Arial', '');
        $opp = 0;

        for ($i = 0; $i < $total_element_count; $i++) {
          $windowsill = OrderElementWindowsill::find_by(['element_id' => $order_elements[$i]->elementId]);
          $opp += ($windowsill->x1 * $windowsill->x2) / 1000000 * $order_elements[$i]->amount;
        }

        $this->Cell($cellWidthMainDataRight, $cellHeightMainDataRight, (string)round($opp, 2), 0);
        $this->Ln();
      }

      // Aantal delingen:
      //-- always subtract 3 voor de last 3

      $heartClickSizeText = 'hart klik gemeten';
      if (empty($order_elements)) {
        $heartClickSizeText = '';
      } else if ($order_elements[0]->heartClickSize == 1) {
        $heartClickSizeText = 'tot aan de punt';
      }

      $aCount4 = 0;
      $aForm['elements4'][$aCount4]['label'] = 'type gekozen maat';
      $aForm['elements4'][$aCount4]['span'] = ['id' => 'spanAdmin', 'value' => $heartClickSizeText, 'class' => 'divTextFC'];

      $aCount5 = 0;
      $aantalHoekjes = '';
      for ($i = 0; $i < $total_element_count; $i++) {
        // Totaal aantal meter hoger dan 100 meter?
        // Alle elementen
        if ($quotation->meters >= 100) {
          $element = intval($order_elements[$i]->elementLength);
          $aantal = intval($order_elements[$i]->amount);
          $aantalHoekjes = intval($aantalHoekjes);
          $aantalHoekjes += $aantal * floor((ceil($element / 2800) / 2));
        }
        else {
          $element = intval($order_elements[$i]->elementLength);
          $aantal = intval($order_elements[$i]->amount);
          $aantalHoekjes = intval($aantalHoekjes);
          $aantalHoekjes += $aantal * floor((ceil($element / 2300) / 2));
        }
      }

      // Aantal delingen:
      if (!in_array($stone_brand?->stoneTypeId, [2, 3])) {
        $this->SetFont('Arial', 'B');
        $this->Cell($cellWidthMainDataRight, $cellHeightMainDataRight, 'Aantal delingen', 0);
        $this->SetFont('Arial', '');
        $this->Cell($cellWidthMainDataRight, $cellHeightMainDataRight, $aantalHoekjes, 0);
        $this->Ln();
      }

      $this->SetLeftMargin(10);

      if ($quotation->productionNotes !== '') {
        $this->setXY(5, 90);
      }
      else {
        $this->setXY(5, 74);
      }

      // Order items
      // $cellWidthArray = cwa
      $cwa = 20;
      $aWidth = [20, 14, 50, 15, $cwa, $cwa, $cwa, $cwa, $cwa];

      $this->SetLineWidth(0.4);
      $this->SetDrawColor(0, 255, 0);
      $this->SetFillColor(0, 0, 0);
      $this->SetTextColor(255, 255, 255);

      $this->SetFont('Arial', '', 10);
      // function Cell($w, $h=0, $txt='', $border=0, $ln=0, $align='', $fill=false, $link='')
      $this->Cell($aWidth[0], 4.5, 'ProductId', 0, 0, 'L', true);
      $this->Cell($aWidth[1], 4.5, 'Aantal', 0, 0, 'L', true);
      $this->Cell($aWidth[2], 4.5, 'Product Naam', 0, 0, 'L', true);
      $this->Cell($aWidth[3], 4.5, '', 0, 0, 'L', true);
      $this->Cell($aWidth[4], 4.5, '', 0, 0, 'L', true);
      $this->Cell($aWidth[5], 4.5, '', 0, 0, 'L', true);
      $this->Cell($aWidth[6], 4.5, '', 0, 0, 'L', true);
      $this->Cell($aWidth[7], 4.5, '', 0, 0, 'L', true);
      $this->Cell($aWidth[8], 4.5, '', 0, 1, 'L', true);

      $this->setX(5);
      $this->SetFont('Arial', '', 10);

      $this->SetTextColor(0, 0, 0);

      //alle projecten van de quotation ophalen
      $projects = Projects::find_all_by(['quotationId' => $quotation->quotationId]);

      // function Cell($w, $h=0, $txt='', $border=0, $ln=0, $align='', $fill=false, $link='')

      $borderOnCell = 0;
      $heightCell = 6;

      foreach ($projects as $key => $value) {

        if ($value->showOnProductionPage === '1') {
          $this->Cell($aWidth[0], $heightCell, $value->product_id, $borderOnCell, 0, 'L', false);
          $this->Cell($aWidth[1], $heightCell, $value->size, $borderOnCell, 0, 'L', false);
          $this->Cell($aWidth[2], $heightCell, $value->name, $borderOnCell, 0, 'L', false);
          $this->Cell($aWidth[3], $heightCell, '', $borderOnCell, 0, 'L', false);
          $this->Cell($aWidth[4], $heightCell, '', $borderOnCell, 0, 'L', false);
          $this->Cell($aWidth[5], $heightCell, '', $borderOnCell, 0, 'L', false);
          $this->Cell($aWidth[6], $heightCell, '', $borderOnCell, 0, 'L', false);
          $this->Cell($aWidth[7], $heightCell, '', $borderOnCell, 0, 'L', false);
          $this->Cell($aWidth[8], $heightCell, '', $borderOnCell, 0, 'L', false);
          $this->Ln();
          $this->setX(5);
        }
      }
    }

    public function footer() {

    }

    public function generate() {

      $this->build();

      $filename = 'productiestaat_webshop.pdf';

      if ($this->isMultiple()) {
        $this->Output("F", DIR_TEMP . $filename);

      }
      else {
        $this->Output("I", $filename);
      }

      return $filename;

    }

  }