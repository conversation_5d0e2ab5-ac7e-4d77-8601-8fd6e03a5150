<?php


  /**
   * Trait apiDamageActions
   */
  trait apiDamageActions {

    /**
     * Damages
     */
    public function executeGetRacks() {
      $vals = [];

      $request = $this->data->getData();
      $quotation = Quotations::find_by(["quotationId" => $request["quotationId"]]);
      if (!$quotation) {
        RestUtils::sendResponseError("Bestelling niet gevonden");
      }

      $racks = GeneratedRackidQuotationids::find_all_by(["quotationId" => $quotation->quotationId], "ORDER BY rackId");
      $prevEmployeeId = false;
      $multipleUsers = false;
      foreach ($racks as $rack) {
        $rack->employee = GeneratedRackIdsEmployees::find_by(["rackId" => $rack->rackId]);
        $rack->rackCode = $rack->employee->rackCode;
        if ($prevEmployeeId != false && $prevEmployeeId != $rack->employee->employeeId) {
          $multipleUsers = true;
        }
        $prevEmployeeId = $rack->employee->employeeId;
      }

      $vals["racks"] = [];
      if ($multipleUsers) {
        //meerdere employees, racks verzenden.
        foreach ($racks as $t => $rack) {
          $val = new stdClass();
          $val->rackId = $rack->rackId;
          $val->rackCode = $rack->employee->rackCode;
//          if($t==0) {
//            $val->rackScanCode = "RACK02155";
//          }
//          else {
          $val->rackScanCode = $rack->employee->rackScanCode;
//          }
          $val->employeeId = $rack->employee->employeeId;
          $vals["racks"][] = $val;
        }
      }

      $vals["damages"] = AppModel::plainObjects(Damage::find_all("ORDER BY name_pl"));

      RestUtils::sendResponseOK("DAMAGES RETRIEVED", $vals);
    }


    /**
     * Set a damagequotation
     */
    public function executeSetDamage() {

      $request = $this->data->getData();

      logToFile('damage', print_r($request, true));

      $quotation = Quotations::find_by(["quotationId" => $request["quotationId"]]);
      if (!$quotation) {
        RestUtils::sendResponseError("Bestelling niet gevonden");
      }
      $rackId = $request["rackId"];
      $damageId = $request["damageId"];


      if ($rackId == 1) {
        //alle rekken. Een rek hoort altijd bij 1 employee
        $racks = GeneratedRackidQuotationids::find_all_by(["quotationId" => $quotation->quotationId], "ORDER BY rackId");
      }
      else {
        //1 rack
        $racks = GeneratedRackidQuotationids::find_all_by(["quotationId" => $quotation->quotationId, "rackId" => $rackId], "ORDER BY rackId");
      }

      foreach ($racks as $rack) {
        $employee = GeneratedRackIdsEmployees::find_by(["rackId" => $rack->rackId]);

        $damage = new DamageQuotation();
        $damage->quotationId = $quotation->quotationId;
        $damage->rackId = $rack->rackId;
        $damage->damageId = $damageId;
        $damage->employeeId = $employee->employeeId;
        $damage->save();
      }


      RestUtils::sendResponseOK("message: success");

    }

  }