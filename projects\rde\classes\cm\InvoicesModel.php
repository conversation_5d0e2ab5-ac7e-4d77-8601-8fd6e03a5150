<?php

  use gsdfw\domain\mail\service\WrapperService;

  AppModel::loadBaseClass('BaseInvoices');

  class InvoicesModel extends BaseInvoices {

    /**
     * Get invoiced date
     * @param string $format
     * @return false|string
     */
    public function getDateInvoice($format = 'd-m-Y') {
      if ($this->dateInvoice == "0000-00-00" || $this->dateInvoice == "") {
        return "";
      }
      return date($format, strtotime($this->dateInvoice));
    }

    /**
     * Get paid date
     * @param string $format
     * @return false|string
     */
    public function getPaid($format = 'd-m-Y') {
      if ($this->paid == "0000-00-00" || $this->paid == "") {
        return "";
      }
      return date($format, strtotime($this->paid));
    }

    public function getReminder1($format = 'd-m-Y') {
      if ($this->reminder1 == "0000-00-00" || $this->reminder1 == "") {
        return "";
      }
      return date($format, strtotime($this->reminder1));
    }

    public function getReminder2($format = 'd-m-Y') {
      if ($this->reminder2 == "0000-00-00" || $this->reminder2 == "") {
        return "";
      }
      return date($format, strtotime($this->reminder2));
    }

    public function getReminder3($format = 'd-m-Y') {
      if ($this->reminder3 == "0000-00-00" || $this->reminder3 == "") {
        return "";
      }
      return date($format, strtotime($this->reminder3));
    }

//    public function getISent($format = 'd-m-Y') {
//      if ($this->iSent == "0000-00-00" || $this->iSent == "") {
//        return "";
//      }
//      return date($format, strtotime($this->iSent));
//    }

    public function getInvoicedateDays() {
      $tijd = time();
      if ($this->getPaid() != "") {
        $tijd = (int)$this->getPaid("U");
      }
      return intval(($tijd - (int)$this->getDateInvoice("U")) / 86400);
    }

    public function getPaybefore($paymentTerm) {
      return date('d-m-Y', strtotime($this->dateInvoice . " +" . $paymentTerm . " DAYS"));
    }

    public function getExpirationdate($paymentTerm, $format = 'd-m-Y') {
      if ($this->dateInvoice == "0000-00-00" || $this->dateInvoice == "") {
        return "";
      }
      return date($format, strtotime($this->dateInvoice . " +" . $paymentTerm . " DAYS"));
    }

    public function getExpirationdays($paymentTerm) {
      return floor((time() - (int)$this->getExpirationdate($paymentTerm, 'U')) / 86400);
    }

    /**
     * Get all unpaid invoices count
     * @return int
     */
    public static function getOpeninvoiceCount() {
      $userIds = [];
      $userIds[$_SESSION["userObject"]->userId] = $_SESSION["userObject"]->userId;
      if ($_SESSION["userObject"]->companyId != '') {
        $companyUsers = SandboxUsers::find_all_by(["companyId" => $_SESSION["userObject"]->companyId]);
        foreach ($companyUsers as $u) {
          $userIds[$u->userId] = $u->userId;
        }
      }
      return Invoices::count_all_by(["userId" => $userIds], " AND paid IS NULL ORDER BY dateInvoice DESC");
    }

    /**
     * Get all open rde invoices, maximum 1 year old
     * @param bool $in_multivers : filter of deze al in multivers staat
     * @return Invoices[]
     */
    public static function getOpenInvoices($in_multivers = true) {
      $filt = ["paid" => null];
      if ($in_multivers) {
        $filt["send_multivers"] = 1;
      }
      $datefrom = date("Y-m-d", strtotime("-12 MONTHS")); //sync 1 year back
      return Invoices::find_all_by($filt, "AND NOT dateInvoice IS NULL AND DATE(dateInvoice)>='" . $datefrom . "' ORDER BY dateInvoice DESC");
    }

    /**
     * Get all open not in multivers invoices
     * @param string $limit bijv "LIMIT 10"
     * @return Invoices[]
     */
    public static function getNotInMultiversInvoices($limit = '') {
      return Invoices::find_all_by(["send_multivers" => 0], "AND NOT dateInvoice IS NULL AND YEAR(dateInvoice)>=2019 ORDER BY dateInvoice ASC " . $limit);
    }

    /**
     * Get all online paid invoices
     * @param string $filter
     * @return Invoices[]
     */
    public static function getOnlinepaidInvoices($filter = '') {
      $filter = ' AND NOT paid IS NULL ' . $filter;
      return Invoices::find_all_by(["paid_with" => "online"], $filter);
    }

    public function mayView($userIds = false) {
      return SandboxUsers::isAdmin() || $this->userId == $_SESSION["userObject"]->userId || ($userIds !== false && in_array($this->userId, $userIds));
    }

    /**
     * Hashcode for paying invoice online (for instance via mollie)
     * @return string
     */
    public function getHashCode() {
      $encrypt_method = 'bf-ecb';
      $string = rtrim(openssl_encrypt($this->invoiceNumber, $encrypt_method, HASH_STRING), '=');
      return str_replace(['+', '/'], ['-', '_'], $string);
    }

    public function getPayUrl() {
      return "  /nl/winkelmandje?action=paylinkinvoice&id=" . $this->invoiceId . "&key=" . urlencode($this->getHashCode());
    }

    /**
     * Online afgerekend
     */
    public function paid_online_link() {
      if ($this->paid == "") { //voorkom dubbel mail posts
        $this->paid_with = 'online';
        $this->setPaid(date('Y-m-d'));

        $llang = 'nl';

        $tos = [];
        $user = SandboxUsers::getUserAndCompany($this->userId);
        if ($user) {
          if ($user->email != "") {
            $tos[$user->email] = $user->email;
          }
          $ip = CrmInvoiceparties::find_by(["invoicePartyId" => $user->company->invoicePartyId]);
          if ($ip && $ip->email != "") {
            $tos[$ip->email] = $ip->email;
          }
        }

        if (count($tos) > 0) {

          //verzend email met bedankt voor uw betaling.
          $html = getLanguageFile($llang, 'mail_invoice_paid.html');
          $html = str_replace("[*AANHEF*]", __("Geachte heer/mevrouw"), $html);
          $html = str_replace("[*INVOICENUMBER*]", $this->invoiceNumber, $html);

          $bericht = (new WrapperService($html))->wrap();

          $subject = __("Betaal bevestiging factuur " . $this->invoiceNumber);
          $gsdmailer = GsdMailer::build($tos, $subject, $bericht);
          $gsdmailer->send();

          $gsdmailer = GsdMailer::build(MAIL_FROM, $subject, $bericht);
          $gsdmailer->send();
        }


      }
    }

    public function getVat() {
      $vats = AppModel::mapObjectIds(VatRates::find_all());
      return $vats[$this->vatRegShifted]->vatRate;
    }

    /**
     * Set invoice paid
     * @param $paymentdate
     */
    public function setPaid($paymentdate): void {

      $this->paid = $paymentdate;
      $this->save();

      //direct controleren of de quotations van deze factuur op betaald staan (statusId 62)
      $quotations = Quotations::find_all_by(["invoiceId" => $this->invoiceId], "AND statusId!=62");
      foreach ($quotations as $quotation) {
        $quotation->statusId = 62;
        $quotation->save();
      }
    }

    public function averageDaysOpenOfCompany($companyId) {
      $userIds = [];
      foreach (SandboxUsers::getUsersOfCompany($companyId) as $sbuser) {
        $userIds[] = $sbuser->userId;
      }
      $vals = [
        "avg"   => 0,
        "days"  => 0,
        "count" => 0,
      ];
      foreach (Invoices::find_all_by(["userId" => $userIds], "AND NOT dateInvoice IS NULL") as $invoice) {
        $paidTime = $invoice->getPaid("U");
        if (empty($paidTime)) {
          $paidTime = time();
        }
        $vals["days"] += ($paidTime - $invoice->getDateInvoice("U")) / 86400;
        $vals["count"]++;
      }
      if ($vals["count"] != 0) {
        $vals["avg"] = floor($vals["days"] / $vals["count"]);
        $vals["days"] = round($vals["days"]);
      }
      return $vals;
    }

    public static function getLastInvoiceNumber() {
      $query = "SELECT MAX(invoiceNumber) as max_invoiceNumber FROM " . Invoices::getTablename() . " ";
      //echo $query;
      $result = DBConn::db_link()->query($query);
      if ($row = $result->fetch_assoc()) {
        return $row['max_invoiceNumber'];
      }
      return 0;
    }


  }