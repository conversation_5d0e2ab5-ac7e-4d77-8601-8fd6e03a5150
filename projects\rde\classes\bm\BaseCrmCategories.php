<?php
class BaseCrmCategories extends AppModel
{
  const DB_NAME = 'rde_cms';
  const TABLE_NAME = 'crm_categories';
  const OM_CLASS_NAME = 'CrmCategories';
  const columns = ['categoryId', 'name', 'oldCategoryId', 'introDiscount', 'groupId'];
  const field_structure = [
    'categoryId'                  => ['type' => 'int', 'length' => '3', 'null' => false],
    'name'                        => ['type' => 'varchar', 'length' => '50', 'null' => false],
    'oldCategoryId'               => ['type' => 'tinyint', 'length' => '3', 'null' => false],
    'introDiscount'               => ['type' => 'boolean', 'length' => '1', 'null' => false],
    'groupId'                     => ['type' => 'int', 'length' => '11', 'null' => false],
  ];

  protected static $primary_key = ['categoryId'];
  protected $auto_increment = 'categoryId';

  public $categoryId, $name, $oldCategoryId, $introDiscount, $groupId;

  function __construct($obj_arr = [], &$error_codes = []) {
    parent::__construct();
    $this->setDefaults();
    if (!empty($obj_arr)) {
      $this->from_array($obj_arr);
      $this->valid_required_fields($error_codes);
    }
  }

  public function valid(&$error_codes = []) {
    $errors = [];
    foreach(self::columns as $column) {
      if ($column != $this->auto_increment)
      {
        call_user_func_array([$this, 'v_' . $column], [&$errors]);
      }
    }
    if(empty($errors)) {
      return true;
    }
    $error_codes = array_merge($error_codes, $errors);
    return false;
  }

  public function valid_required_fields(&$error_codes = []) {
    $errors = [];
    foreach (self::columns as $column) {
      $function_name = "v_$column";
      if ($column!=$this->auto_increment) {
        call_user_func_array([$this, $function_name], [&$errors]);
      }
    }
    if (empty($errors)) {
      return true;
    }
    $error_codes = array_merge($error_codes, $errors);
    return false;
  }

  public function setDefaults() {
  }



  public function v_categoryId(&$error_codes = []) {
    if (!is_null($this->categoryId) && strlen($this->categoryId) > 0 && self::valid_int($this->categoryId, '3')) {
      return true;
    }
    $error_codes[] = 'categoryId';
    return false;
  }

  public function v_name(&$error_codes = []) {
    if (!is_null($this->name) && strlen($this->name) > 0 && self::valid_varchar($this->name, '50')) {
      return true;
    }
    $error_codes[] = 'name';
    return false;
  }

  public function v_oldCategoryId(&$error_codes = []) {
    if (!is_null($this->oldCategoryId) && strlen($this->oldCategoryId) > 0 && self::valid_tinyint($this->oldCategoryId, '3')) {
      return true;
    }
    $error_codes[] = 'oldCategoryId';
    return false;
  }

  public function v_introDiscount(&$error_codes = []) {
    if (!is_null($this->introDiscount) && strlen($this->introDiscount) > 0 && self::valid_tinyint($this->introDiscount, '1')) {
      return true;
    }
    $error_codes[] = 'introDiscount';
    return false;
  }

  public function v_groupId(&$error_codes = []) {
    if (!is_null($this->groupId) && strlen($this->groupId) > 0 && self::valid_int($this->groupId, '11')) {
      return true;
    }
    $error_codes[] = 'groupId';
    return false;
  }

  /**
  *@param array $conditions (required)
  *@param String $raw_sql (optional)
  *@return CrmCategories[]
  **/
  public static function find_all_like($conditions, $raw_sql = '') { return parent::generic_find_all_like($conditions, static::OM_CLASS_NAME, $raw_sql); }
  /**
  *@param array $conditions (required)
  *@param String $raw_sql (optional)
  *@return CrmCategories[]
  **/
  public static function find_all_by($conditions, $raw_sql = '') { return parent::generic_find_all_by($conditions, static::OM_CLASS_NAME, $raw_sql); }
  /**
  *@param String $raw_sql (optional)
  *@return CrmCategories[]
  **/
  public static function find_all($raw_sql = '') { return parent::generic_find_all(static::OM_CLASS_NAME, $raw_sql); }
  /**
  *@param array $conditions (required)
  *@param String $raw_sql (optional)
  *@return CrmCategories
  **/
  public static function find_by($conditions, $raw_sql = '') { return parent::generic_find_by($conditions, static::OM_CLASS_NAME, $raw_sql); }
  /**
  *@param String $id (required)
  *@param String $raw_sql (optional)
  *@return CrmCategories
  **/
  public static function find_by_id($id, $raw_sql = '') { return parent::generic_find_by_id($id, static::OM_CLASS_NAME, $raw_sql); }
  /**
  *@param array $conditions (required)
  *@param String $raw_sql (optional)
  *@return int
  **/
  public static function count_all_by($conditions, $raw_sql = '') { return parent::generic_count_all_by($conditions, static::OM_CLASS_NAME, $raw_sql); }
  /**
  *@param array $conditions (required)
  *@param String $raw_sql (optional)
  *@return bool
  **/
  public static function delete_by($conditions, $raw_sql = '') { return parent::generic_delete_by($conditions, static::OM_CLASS_NAME, $raw_sql); }
}