<?php

  AppModel::loadBaseClass('BaseBretiInvoice');

  class BretiInvoiceModel extends BaseBretiInvoice {

    /**
     * Get last breti invoice number
     * @return int
     * @throws GsdException
     */
    public static function getLastInvoiceNumber(): int {
      $query = "SELECT MAX(breti_invoice_number) as max_invoiceNumber FROM " . BretiInvoice::getTablename() . " ";
      $result = DBConn::db_link()->query($query);
      if ($row = $result->fetch_assoc()) {
        return $row['max_invoiceNumber'];
      }
      return 0;
    }

    /**
     * Get last breti invoice id
     * @return int
     * @throws GsdException
     */
    public static function getLastInvoiceId(): int {
      $query = "SELECT MAX(id) as max_invoiceId FROM " . BretiInvoice::getTablename() . " ";
      $result = DBConn::db_link()->query($query);
      if ($row = $result->fetch_assoc()) {
        return $row['max_invoiceId'] += 1;
      }
      return 0;
    }

  }