<?php
class BaseCustomerCodes extends AppModel
{
  const DB_NAME = 'rde_b1mo';
  const TABLE_NAME = 'customer_codes';
  const OM_CLASS_NAME = 'CustomerCodes';
  const columns = ['codeId', 'groupId', 'payterm', 'short', 'name'];
  const field_structure = [
    'codeId'                      => ['type' => 'int', 'length' => '3', 'null' => false],
    'groupId'                     => ['type' => 'tinyint', 'length' => '2', 'null' => false],
    'payterm'                     => ['type' => 'int', 'length' => '2', 'null' => false],
    'short'                       => ['type' => 'varchar', 'length' => '5', 'null' => false],
    'name'                        => ['type' => 'varchar', 'length' => '10', 'null' => false],
  ];

  protected static $primary_key = ['codeId'];
  protected $auto_increment = 'codeId';

  public $codeId, $groupId, $payterm, $short, $name;

  function __construct($obj_arr = [], &$error_codes = []) {
    parent::__construct();
    $this->setDefaults();
    if (!empty($obj_arr)) {
      $this->from_array($obj_arr);
      $this->valid_required_fields($error_codes);
    }
  }

  public function valid(&$error_codes = []) {
    $errors = [];
    foreach(self::columns as $column) {
      if ($column != $this->auto_increment)
      {
        call_user_func_array([$this, 'v_' . $column], [&$errors]);
      }
    }
    if(empty($errors)) {
      return true;
    }
    $error_codes = array_merge($error_codes, $errors);
    return false;
  }

  public function valid_required_fields(&$error_codes = []) {
    $errors = [];
    foreach (self::columns as $column) {
      $function_name = "v_$column";
      if ($column!=$this->auto_increment) {
        call_user_func_array([$this, $function_name], [&$errors]);
      }
    }
    if (empty($errors)) {
      return true;
    }
    $error_codes = array_merge($error_codes, $errors);
    return false;
  }

  public function setDefaults() {
  }



  public function v_codeId(&$error_codes = []) {
    if (!is_null($this->codeId) && strlen($this->codeId) > 0 && self::valid_int($this->codeId, '3')) {
      return true;
    }
    $error_codes[] = 'codeId';
    return false;
  }

  public function v_groupId(&$error_codes = []) {
    if (!is_null($this->groupId) && strlen($this->groupId) > 0 && self::valid_tinyint($this->groupId, '2')) {
      return true;
    }
    $error_codes[] = 'groupId';
    return false;
  }

  public function v_payterm(&$error_codes = []) {
    if (!is_null($this->payterm) && strlen($this->payterm) > 0 && self::valid_int($this->payterm, '2')) {
      return true;
    }
    $error_codes[] = 'payterm';
    return false;
  }

  public function v_short(&$error_codes = []) {
    if (!is_null($this->short) && strlen($this->short) > 0 && self::valid_varchar($this->short, '5')) {
      return true;
    }
    $error_codes[] = 'short';
    return false;
  }

  public function v_name(&$error_codes = []) {
    if (!is_null($this->name) && strlen($this->name) > 0 && self::valid_varchar($this->name, '10')) {
      return true;
    }
    $error_codes[] = 'name';
    return false;
  }

  /**
  *@param array $conditions (required)
  *@param String $raw_sql (optional)
  *@return CustomerCodes[]
  **/
  public static function find_all_like($conditions, $raw_sql = '') { return parent::generic_find_all_like($conditions, static::OM_CLASS_NAME, $raw_sql); }
  /**
  *@param array $conditions (required)
  *@param String $raw_sql (optional)
  *@return CustomerCodes[]
  **/
  public static function find_all_by($conditions, $raw_sql = '') { return parent::generic_find_all_by($conditions, static::OM_CLASS_NAME, $raw_sql); }
  /**
  *@param String $raw_sql (optional)
  *@return CustomerCodes[]
  **/
  public static function find_all($raw_sql = '') { return parent::generic_find_all(static::OM_CLASS_NAME, $raw_sql); }
  /**
  *@param array $conditions (required)
  *@param String $raw_sql (optional)
  *@return CustomerCodes
  **/
  public static function find_by($conditions, $raw_sql = '') { return parent::generic_find_by($conditions, static::OM_CLASS_NAME, $raw_sql); }
  /**
  *@param String $id (required)
  *@param String $raw_sql (optional)
  *@return CustomerCodes
  **/
  public static function find_by_id($id, $raw_sql = '') { return parent::generic_find_by_id($id, static::OM_CLASS_NAME, $raw_sql); }
  /**
  *@param array $conditions (required)
  *@param String $raw_sql (optional)
  *@return int
  **/
  public static function count_all_by($conditions, $raw_sql = '') { return parent::generic_count_all_by($conditions, static::OM_CLASS_NAME, $raw_sql); }
  /**
  *@param array $conditions (required)
  *@param String $raw_sql (optional)
  *@return bool
  **/
  public static function delete_by($conditions, $raw_sql = '') { return parent::generic_delete_by($conditions, static::OM_CLASS_NAME, $raw_sql); }
}