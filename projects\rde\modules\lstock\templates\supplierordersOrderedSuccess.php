<?php TemplateHelper::includePartial('_tabs.php','lstock'); ?>
<script type="text/javascript">
$(document).ready(function() {

  $(".size").change(function (event) {
    var val = 0;
    if($(this).val()!="") {
      var val = parseInt($(this).val());
      if(isNaN(val)) {
        val = 0;
      }
    }
    $(this).val(val);

    if($(this).attr("data-palletsize")) {
      var palletizer = $(this).parent().parent().find(".palletizer");
      if(isInt(val/$(this).attr("data-palletsize"))) {
        palletizer.val(val/$(this).attr("data-palletsize"));
      }
      else {
        palletizer.val("");
      }
    }

  });

  $(".palletizer").change(function() {
    if($(this).val()==""){
      $(this).parent().parent().find(".size").val("");
    }
    else {
      $(this).parent().parent().find(".size").val(parseInt($(this).val()) * parseInt($(this).attr("data-size")));
    }
  });

});


</script>	

<form method="post">
  <br/>
  Hieronder vind u een overzicht van bestelde bestellijsten welke nog niet in zijn geheel zijn geleverd.<br/><br/>
	<?php	if(count($stoneorders)==0): ?>
		<br/>Er zijn geen bestellingen gevonden
  <?php else: ?>
    <table class="default_table">

    <?php foreach($stoneorders as $brandId=>$brandorders): ?>

      <?php foreach($brandorders as $stoneorder): ?>
        <tr class="dataTableHeadingRow topborder">
          <td colspan="7">
            <?php if($stoneorder->senddate!=""): ?>
              <?php echo $stoneorder->getSenddate("d-m-Y H:i") ?>
            <?php endif; ?>
            - <?php echo $stonebrands[$brandId]->name ?>
            <?php echo BtnHelper::getRemove('?action=supplierorderdelete&delid='.$stoneorder->id, $stoneorder->getSenddate()==""?"nieuw":"van ".$stoneorder->getSenddate()) ?>
          </td>
          <tr class="dataTableHeadingRow">
            <td style="width: 320px;">Product</td>
            <td>
              Naam <?php echo showInfoButton("Als naam leeg is worden de bestelnummers als referentie gebruikt.") ?>
            </td>
            <td>Bestellingen</td>
            <td style="width: 150px;">Per pallet</td>
            <td>Aantal</td>
            <td style="width: 50px;">Geleverd</td>
            <td>Verwachte leverdatum</td>
          </tr>
          <?php
            $stoneprev = false;
            foreach($stoneorder->items as $item):
              $stone = $item->stone;
              ?>
              <tr class="dataTableRow trhover <?php if($stoneprev && !Stones::areSiblings($stone, $stoneprev)): ?>topborder<?php endif; ?>">
                <td><?php echo $stone->name;?></td>
                <td><input type="text" name="name[<?php echo $item->id ?>]" value="<?php echo $item->name ?>" <?php if($stoneorder->status!='new'): ?>readonly<?php endif; ?>/></td>
                <td><?php echo $item->quotationNames ?></td>
                <td>
                  <?php if($stone->amountPerPallet>0): ?>
                    <select class="palletizer" data-size="<?php echo $stone->amountPerPallet ?>">
                      <option value="">#pallets</option>
                      <?php for($tel=1;$tel<100;$tel++): ?>
                        <option value="<?php echo $tel ?>" <?php if($item->size/$stone->amountPerPallet==$tel) echo 'selected'; ?>><?php echo $tel ?></option>
                      <?php endfor; ?>
                    </select>
                    x <?php echo $stone->amountPerPallet ?>
                  <?php endif; ?>
                </td>
                <td><input style="width: 90px;text-align:right;" type="text" name="size[<?php echo $item->id ?>]" class="size" value="<?php echo $item->size ?>" data-palletsize="<?php echo $stone->amountPerPallet ?>" <?php if($stoneorder->status!='new'): ?>readonly<?php endif; ?>/></td>
                <td style="text-align: right;<?php if($item->receivedsize<$item->size) echo 'color: red;font-weight: bold;'; ?>"><?php echo $item->receivedsize ?></td>
                <td><?php echo $item->getSupplierreadydate() ?></td>
              </tr>
              <?php
              $stoneprev = $stone;
            endforeach;
          ?>

      <?php endforeach; ?>


    <?php endforeach; ?>

    </table>

	<?php endif; ?>

</form>