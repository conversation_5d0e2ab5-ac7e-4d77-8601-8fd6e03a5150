<?php TemplateHelper::includePartial('_tabs.php', 'prices'); ?>

<h3>Bewerk steen prijzen - batch</h3>

Op dit scherm kunnen je prijzen gegroepeerd aanpassen.
De steen prijzen zijn gegroepeerd als ze hetzelfde formaat hebben.
Ook behandel ik linker en rechter eindsteen als dezelfde.
<br/><br/>

<?php writeErrors($form->getErrors(), true); ?>

<form method="post">
  <table class="default_table" style="width: auto;">
    <tr class="dataTableHeadingRow">
      <td>Item</td>
      <td>Instelling</td>
    </tr>
    <tr class="dataTableRow trhover">
      <td>Merk</td>
      <td><?php echo $brands[$_SESSION['sp_brand']]->name ?></td>
    </tr>
    <?php if(count($colors)>0): ?>
    <tr class="dataTableRow trhover">
      <td><PERSON><PERSON>uren</td>
      <td>
        <?php
          $colornames = [];
          foreach($colors as $color):
            $colornames[] = $color->name;
          endforeach;
          echo implode(", ", $colornames);
        ?>
      </td>
    </tr>
    <?php endif; ?>
    <tr class="dataTableRow trhover">
      <td>Geldig vanaf</td>
      <td>
        01-01-<?php echo $_GET["yearfrom"] ?>
      </td>
    </tr>
  </table>
  <br/>

  <table class="default_table" style="width: auto;">
    <tr class="dataTableHeadingRow">
      <td>Naam</td>
      <td>Huidige prijs <?php echo showHelpButton("Dit is prijs welk op dit moment gebruikt word voor nieuwe offertes.","Huidige prijs") ?></td>
      <td>Type</td>
      <td style="text-align: center">Eindsteen</td>
      <td style="text-align: center">Online</td>
      <td>Inkooprijs</td>
      <td>Factor</td>
      <td>Nieuwe prijs <?php echo showHelpButton("Als dit veld een waarde bevat, betekent dit dat er al een prijs bekend is welke geldig is vanaf deze datum. Je kunt deze prijs hier wijzigen.","Nieuwe prijs") ?></td>
    </tr>
    <?php
      /** @var Stones $item */
      foreach($stones_grouped as $key=>$group):
        $firststone = $group[0];
        $endstones = [];
        ?>
        <tr class="dataTableRow trhover">
          <td>
            <?php foreach($group as $stone):
              $endstones[$stone->endstone] = Stones::ENDSTONES[$stone->endstone];
              ?>
              <?php echo $stone->name ?><br/>
            <?php endforeach; ?>
          </td>
          <td>
            <?php foreach($group as $stone): ?>
              <?php echo StringHelper::asMoney($stone->price->price) ?><br/>
            <?php endforeach; ?>
          </td>
          <td><?php echo Stones::TYPES[$firststone->type] ?></td>
          <td style="text-align: center"><?php echo implode(", ", $endstones) ?></td>
          <td style="text-align: center"><?php echo $firststone->display=="true"?"Ja":"Nee" ?></td>
          <td>€ <?php $form->getElement("buyprice_".$key)->render() ?></td>
          <td> x <?php $form->getElement("factor_".$key)->render() ?> =</td>
          <td>€ <?php $form->getElement("price_".$key)->render() ?></td>
        </tr>
      <?php endforeach; ?>
  </table>

  <br/>
  <input type="submit" name="go" value="Opslaan" />
  <input type="submit" name="go_list" value="Opslaan en naar lijst" />

</form>
<script type="text/javascript">
  $(document).ready(function () {
    $(".buyprice,.factor,.price").focus(function() {
      $(this).select();
    });

    $(".buyprice,.factor").change( function() {
      if($(this).val()!="") {
        var val = $(this).val();
        //console.log(val);
        val = decimalNL(val,3);
        $(this).val(val);
      }

      var row = $(this).parent().parent();
      var inkoop = parseFloat(row.find(".buyprice").val());
      var factor = parseFloat(row.find(".factor").val());
      var price = row.find(".price");
      if(!isNaN(inkoop) && !isNaN(factor)) {
        price.val(decimalNL(inkoop*factor,3))
      }
    });

    $(".price").change( function() {
      if($(this).val()!="") {
        var val = $(this).val();
        //console.log(val);
        val = decimalNL(val,2);
        $(this).val(val);
      }
    });

  });

</script>
<style>
  input.price,input.buyprice, input.factor, input.currentprice {
    width: 80px;
    text-align: right;
  }
</style>