<?php

  AppModel::loadBaseClass('BaseQuotations');

  class QuotationsModel extends BaseQuotations {

    const PAYMETHODE_ONACCOUNT = 'on_account';
    const PAYMETHODE_ONLINE = 'online';

    const CREATED_VIA_WEBSHOP = 'webshop';
    const CREATED_VIA_WIZARD = 'wizard';
    const CREATED_VIA_OTHER = 'other';

    /**
     * @param int $quotationId
     * @return Quotations
     */
    public static function getById($quotationId) {
      $vals = ["quotationId" => $quotationId, "flaggedForDeletion" => 0];
      return Quotations::find_by($vals);
    }

    public function getQuotationNumberFull() {
      $name = $this->quotationNumber . '-' . $this->quotationVersion;
      if ($this->quotationPart != "") {
        $name .= '-' . $this->getQuotationPartAlpha();
      }
      return $name;
    }

    public function getQuotationPartAlpha() {
      if ($this->quotationPart == "") return "";
      return RdeHelper::asciiIntToAlpha($this->quotationPart);
    }

    public function getName() {
      $name = $this->projectName;
      if ($this->projectName != "" && $this->projectReference != "") {
        $name .= ' - ';
      }
      if ($this->projectReference != "") {
        $name .= $this->projectReference;
      }
      return trim($name);
    }

    public static function getNames($quotations, $returnAr = false) {
      $names = [];
      foreach ($quotations as $q) {
        $name = substr($q->quotationNumber, 3);
        $names[$name] = $name;
      }
      sort($names);
      if ($returnAr) return $names;
      return implode(", ", $names);
    }

    /**
     * @param $date : prijs datum
     * @param $userId : sandbox id
     * @param Stones $stone : steen object
     * @param bool $webshop : opslag losse stenen bij webshop
     * @return array|bool: array met prijzen
     */
    public static function getPrices($date, $userId, $stone, $webshop = false) {
      $increasePrices = SandboxUsers::getPriceIncreases($userId, $date);

      $increaseValue = $increasePrices["stoneIncrease"];

      if ($stone->getMaterial() == Stones::MATERIAL_NATUURSTEEN) {
        $brand = StoneBrands::find_by(["brandId" => $stone->brandId]);
        if ($brand && $brand->isChineesNatuursteen()) {
          //speciale opslag op dit merk
          $increaseValue = $increasePrices["naturalstoneChinaIncrease"];
        }
        else {
          $increaseValue = $increasePrices["naturalstoneIncrease"];
        }
      }
      elseif ($stone->getMaterial() == Stones::MATERIAL_BETON) {
        $increaseValue = $increasePrices["concreteIncrease"];
      }
      elseif ($stone->getMaterial() == Stones::MATERIAL_ISOSILL) {
        $increaseValue = $increasePrices["isosillIncrease"];
      }

      if ($webshop) { //aangeroepen van webshop, pak webshop opslag
        if ($stone->stoneIncreaseGroup != "") {
          $increaseValue = $increasePrices["stoneIncreaseGroup" . $stone->stoneIncreaseGroup];
        }
        else { //groep is niet gezet, pak maar D
          $increaseValue = $increasePrices["stoneIncreaseGroupD"];
        }
      }

      if (!($increasePrices && $increaseValue > 0)) return false;

      $sQuery1 = "SELECT  S.endstone";
      $sQuery1 .= ", SP.price AS stonePrice ";
      $sQuery1 .= ", SP.price_m2 AS stonePriceM2 ";
      $sQuery1 .= ", SP.price_piece AS stonePricePiece ";
      $sQuery1 .= ", SP.price_mitre AS stonePriceMitre ";
      $sQuery1 .= ", S.weightm1 AS stoneWeight ";
      if ($stone->isKeramiek()) {
        $sQuery1 .= ", GP.price AS gluePrice ";
        $sQuery1 .= ", MP.factor AS mitreFactor  ";
      }
      $sQuery1 .= "FROM " . Stones::getTablename() . " S ";
      $sQuery1 .= "JOIN " . StonePrices::getTablename() . " SP ON S.stoneId = SP.stoneId ";
      $sQuery1 .= "JOIN " . StoneSizes::getTablename() . " SS ON S.sizeId = SS.sizeId ";
      if ($stone->isKeramiek()) {
        $sQuery1 .= "JOIN " . GluePrices::getTablename() . " GP ON S.sizeId = GP.sizeId ";
        $sQuery1 .= "JOIN " . MitrePrices::getTablename() . " MP ON SS.length = MP.stoneLength ";
      }
      $sQuery1 .= "WHERE (SP.validFrom <= '" . $date . "' AND SP.validTo >= '" . $date . "') ";
      if ($stone->isKeramiek()) {
        $sQuery1 .= "AND (GP.validFrom <= '" . $date . "' AND GP.validTo >= '" . $date . "') ";
        $sQuery1 .= "AND (MP.validFrom <= '" . $date . "' AND MP.validTo >= '" . $date . "') ";
      }

      $sQuery1 .= "AND (S.type = 'raamdorpel' OR S.type = 'spekband' OR S.type = 'muurafdekker' OR S.type = 'vensterbank' OR S.type = 'balkjes') ";
      $sQuery1 .= "AND S.brandId = '" . $stone->brandId . "' ";
      $sQuery1 .= "AND S.colorId = '" . $stone->colorId . "' ";
      $sQuery1 .= "AND S.sizeId = '" . $stone->sizeId . "' ";

      //$sQuery1 .= "AND S.display = 'true' "; //vanuit het cms?
      $oResult = DBConn::db_link()->query($sQuery1);
      $values = [];
      if ($oResult->num_rows == 0) {
        return false;
      }
      while ($row = $oResult->fetch_assoc()) {
        $values['codeId'] = $increasePrices['codeId'];
        if ($row['endstone'] == 'false') {

          $values['stonePrice'] = round($row['stonePrice'] * $increaseValue, 2);
          $values['stonePriceM2'] = round($row['stonePriceM2'] * $increaseValue, 2);
          $values['stonePricePiece'] = round($row['stonePricePiece'] * $increaseValue, 2);
          $values['stonePriceMitre'] = round($row['stonePriceMitre'] * $increaseValue, 2);

          if (isset($row['gluePrice'])) {
            $values['gluePrice'] = round($row['gluePrice'] * $increasePrices["glueIncrease"], 2);
          }
          if (isset($row['mitreFactor'])) {
            $values['mitreFactor'] = round($row['mitreFactor'], 3);
          }
          $values['stoneWeight'] = $row['stoneWeight'];

        }
        elseif ($row['endstone'] == Stones::ENDSTONE_LEFT) {
          $values['stonePriceEnd'] = round($row['stonePrice'], 2);
          $values['stoneWeightEnd'] = $row['stoneWeight'];
        }
        elseif ($row['endstone'] == Stones::ENDSTONE_RIGHT && !isset($values['stonePriceEnd'])) {
          //alleen zetten als nog niet beschikbaar, normaal staat dit in de linkse
          $values['stonePriceEnd'] = round($row['stonePrice'], 2);
          $values['stoneWeightEnd'] = $row['stoneWeight'];
        }
        elseif ($row['endstone'] == Stones::ENDSTONE_LEFTG) {
          $values['stonePriceGroove'] = round($row['stonePrice'], 2);
          $values['stoneWeightGroove'] = $row['stoneWeight'];
        }
        elseif ($row['endstone'] == Stones::ENDSTONE_RIGHTG && !isset($values['stonePriceGroove'])) {
          //alleen zetten als nog niet beschikbaar, normaal staat dit in de linkse
          $values['stonePriceGroove'] = round($row['stonePrice'], 2);
          $values['stoneWeightGroove'] = $row['stoneWeight'];
        }
      }

      if ($increasePrices["glazeside"] > 0) {
        $values["glazesidePrice"] = round($values['stonePrice'] + ($values['stonePrice'] * $increasePrices["glazeside"] / 100), 2);
      }
      else {
        $values["glazesidePrice"] = round($values['stonePrice'], 2);
      }

      return $values;

    }

    public function getAddress() {
      $string = "";
      if ($this->street . $this->nr) $string .= trim($this->street . " " . $this->nr);
      if ($this->ext != "") $string .= " " . $this->ext;
      return trim($string);
    }

    public function getAddressFull($delimiter = '<br/>') {
      $string = "";
      if ($this->street . $this->nr != "") $string .= trim($this->street . " " . $this->nr);
      if ($this->ext != "") $string .= " " . $this->ext;
      if ($string != "") $string .= $delimiter;
      if ($this->zipcode . $this->domestic != "") $string .= trim(\StringHelper::zipFormat($this->zipcode) . " " . $this->domestic);
      return displayAsHtml(trim($string));
    }

    /**
     * @param Quotations $quot
     * @return int
     */
    public static function getNewVersion($quot) {
      if ($quot->quotationNumber == "") return 0;

      //is er dit jaar een offerte gemaakt?
      $vals = explode(".", $quot->quotationNumber);
      $otheryear = date('y') . '.' . $vals[1];
      $query = "SELECT max(quotationVersion) as maxQuoteVersion FROM " . Quotations::getTablename() . " WHERE quotationNumber = '" . $otheryear . "' ";
      $result = DBConn::db_link()->query($query);
      $row = $result->fetch_assoc();
      if ($row && $row['maxQuoteVersion'] != "") {
        return $row['maxQuoteVersion'] + 1;
      }

      //check dan de huidige
      $query = "SELECT max(quotationVersion) as maxQuoteVersion FROM " . Quotations::getTablename() . " WHERE quotationNumber = '" . $quot->quotationNumber . "' ";
      $result = DBConn::db_link()->query($query);
      $row = $result->fetch_assoc();
      if ($row && $row['maxQuoteVersion'] != "") {
        return $row['maxQuoteVersion'] + 1;
      }

      return 0;

    }

    /**
     * @param QuotationsExtra $quotations_extra
     * @param $prices
     */
    public function setMatting($quotations_extra, $prices) {
      $mattingOnlyGlue = 0;
      $mattingRemovalDiscount = 0;

      if ($this->offerteType == "" || $this->offerteType == 'RDE') {

        $spareparts = Spareparts::getValues();
        $cc = CustomerCodes::find_by(["codeId" => $this->codeId]);

        $groupids_bmh = CustomerGroups::getIdsByTypeid(2); //bouwmateriaal handel

//      $cc->groupId = 1;
        if (in_array($cc->groupId, $groupids_bmh)) {
          if ($this->endstone == 'true_grooves') {
            $mattingOnlyGlue = $this->projectValue - ($this->stoneAmount * $prices['stonePrice']) - (($quotations_extra->totalLeftEndStonesGrooves + $quotations_extra->totalRightEndStonesGrooves) * ($prices['stonePriceGroove'] - $prices['stonePrice'])) - ($this->meters * $spareparts['CMGLUEDISCOUNT']);
          }
          else { //geen groeven, dus standaard prijs pakket. Zijn er geen endstones dan heeft dat geen invloed.
            $mattingOnlyGlue = $this->projectValue - ($this->stoneAmount * $prices['stonePrice']) - (($quotations_extra->totalLeftEndStones + $quotations_extra->totalRightEndStones) * ($prices['stonePriceEnd'] - $prices['stonePrice'])) - ($this->meters * $spareparts['CMGLUEDISCOUNT']);
          }
          $mattingRemovalDiscount = -1 * $this->meters * $spareparts['CMFREIGHTDISCOUNT'];
        }
      }

      $this->mattingOnlyGlue = round($mattingOnlyGlue, 2);
      $this->mattingRemovalDiscount = round($mattingRemovalDiscount, 2);

    }

    public function getIconClass() {
      return "quotation_icon_" . $this->statusId;
    }

    public function getIconHTML() {
      $extraTitle = "";
      if ($this->statusId == 20 && $this->getProductionDate() != ''):
        $extraTitle .= " op " . $this->getProductionDate();
      endif;
      return Status::getIconHTML($this->statusId, $extraTitle);
    }

    public static function getIconHTMLByStatusId($statusId) {
      $q = new Quotations();
      $q->statusId = $statusId;
      return $q->getIconHTML();
    }

    public function getQuotationDate($format = 'd-m-Y') {
      if ($this->quotationDate == "0000-00-00" || $this->quotationDate == "") {
        return "";
      }
      return date($format, strtotime($this->quotationDate));
    }

    /**
     * Let op: dit is de productiedatum
     * @param string $format
     * @return false|string
     */
    public function getOrderDate($format = 'd-m-Y') {
      if ($this->orderDate == "0000-00-00" || $this->orderDate == "") {
        return "";
      }
      return date($format, strtotime($this->orderDate));
    }

    /**
     * OPDRACHT datum
     * @param string $format
     * @return false|string
     */
    public function getProductionDate($format = 'd-m-Y') {
      if ($this->productionDate == "0000-00-00" || $this->productionDate == "") {
        return "";
      }
      return date($format, strtotime($this->productionDate));
    }

    /**
     * Productiedatum
     * @param string $format
     * @return false|string
     */
    public function getProduceDate($format = 'd-m-Y') {
      if ($this->produceDate == "0000-00-00" || $this->produceDate == "") {
        return "";
      }
      return date($format, strtotime($this->produceDate));
    }

    /**
     * Get Uiterlijke Leverdatum
     * @param string $format
     * @return false|string
     */
    public function getDueDate($format = 'd-m-Y') {
      if ($this->dueDate == "0000-00-00" || $this->dueDate == "") {
        return "";
      }
      return date($format, strtotime($this->dueDate));
    }

    public function mayEdit() {
      return $this->createdVia == Quotations::CREATED_VIA_WIZARD && (($this->statusId == 10 && ($this->userId == $_SESSION["userObject"]->userId || ($this->companyId != '' && $this->companyId == $_SESSION["userObject"]->companyId))) || SandboxUsers::isAdmin());
    }

    public function mayDelete() {
      return $this->statusId == 10 && (SandboxUsers::isAdmin() || $this->userId == $_SESSION["userObject"]->userId);
    }

    public function mayView() {
      return SandboxUsers::isAdmin() || $this->userId == $_SESSION["userObject"]->userId || ($this->companyId != '' && $this->companyId == $_SESSION["userObject"]->companyId);
    }

    public function mayConfirm() {
      return $this->statusId == 10 && (SandboxUsers::isAdmin() || $this->userId == $_SESSION["userObject"]->userId || ($this->companyId != '' && $this->companyId == $_SESSION["userObject"]->companyId));
    }

    public function maySplit() {
      if ($this->createdVia == Quotations::CREATED_VIA_WIZARD
        && $this->statusId == 20
        && ((($this->userId == $_SESSION["userObject"]->userId || ($this->companyId != '' && $this->companyId == $_SESSION["userObject"]->companyId))) || SandboxUsers::isAdmin())) {

        $qe = QuotationsExtra::find_by(["quotationId" => $this->quotationId]);
        if (!SandboxUsers::isAdmin() && $qe->customerSplitRights == 0) {
          return false;
        }

        //controleer ook of dit de laatste deellevering is.
        $maySplit = false;
        if ($this->quotationPart == "") {
          $maySplit = true;
        }
        $found = Quotations::find_by(["quotationNumber" => $this->quotationNumber, "quotationVersion" => $this->quotationVersion, "quotationPart" => $this->quotationPart + 1]);
        if (!$found) {
          $maySplit = true;
        }
        if ($maySplit) {
          //controlleer of er wel meer dan 1 element in de bestelling zit.
          $elements = OrderElements::find_all_by(["quotationId" => $this->quotationId]);
          $product_count = 0;
          foreach ($elements as $element) {
            $product_count += $element->amount;
          }
          if ($product_count > 1) {
            return true;
          }
        }
      }
      return false;
    }


    /**
     * Encrypts for direct open
     * @param string $string plaintext password
     * @return string encrypted password
     */
    public static function encrypt($string) {
      $encrypt_method = 'bf-ecb';
      $string = rtrim(openssl_encrypt($string, $encrypt_method, HASH_STRING), '=');
      return str_replace(['+', '/'], ['-', '_'], $string);
    }

    /**
     * Decrypts for direct open
     * @param string $string
     * @return string decrypted password
     */
    public static function decrypt($string) {
      $encrypt_method = 'bf-ecb';
      $string = str_replace(['-', '_'], ['+', '/'], $string);
      $decrypted = openssl_decrypt($string, $encrypt_method, HASH_STRING);
      return $decrypted;
    }

    /**
     * Bepaal de benodigde dagen voor productie, rekening houdende met vakantie.
     * @return float|int
     */
    public static function determineNeededProductiondays() {
      $neededdays = 20; // 4 weken = 20 werkdagen

      $setting = Setting::getByCode("deliveryweeks");
      if ($setting && is_numeric($setting->value)) {
        //gedefineerd in database
        $neededdays = $setting->value * 5;
      }

      //zijn er verlofdagen in deze periode, dan extra tijd toevoegen
      $leaves = AppModel::mapObjectIds(WorkerBaseleave::getOffDaysPeriod(date("Y-m-d"), date("Y-m-d", strtotime("+ 3 MONTHS"))), "date");
      $daysbooked = $neededdays;
      $daytime = time();
      while (true) {
        $daytime = strtotime("+1 DAY", $daytime);
        if (date("N", $daytime) == 6 || date("N", $daytime) == 7 || isset($leaves[date("Y-m-d", $daytime)])) {
          //dit is een verlof dag of za of zo, dus extra dag tellen
          $neededdays++;
        }
        else {
          //er is geen verlof deze dag, dus tellen als werk dag.
          $daysbooked -= 1;
        }

        if ($daysbooked <= 0) break;
      }
      return $neededdays;
    }

    public function getPaymentObject() {
      $classname = 'Payment' . ucfirst($this->paymentMethod);
      return new $classname;
    }

    public function paid_online() {

      if ($this->statusId < 20) {
        $this->statusId = 20; //besteld
        if ($this->orderDate == "") $this->orderDate = date('Y-m-d');
        if ($this->productionDate == "") $this->productionDate = date("Y-m-d"); //besteldatum
        $this->payedFlag = 1;
        $this->save();

        ProductionOrder::create($this);
        MailsFactory::sendOrderMail($this);
        //MailsFactory::sendMissingDataEmail($this);
      }

    }

    public static function getQuotationsPrepaid($user) {
      $filt = [];
      if ($user->companyId != "" && $user->companyId != 0) {
        $filt["companyId"] = $user->companyId;
      }
      else {
        $filt["userId"] = $user->userId;
      }
      $filt["statusId"] = [20, 30];
      $filt["flaggedForDeletion"] = 0;
      return Quotations::find_all_by($filt, "ORDER BY quotationDate DESC, quotationNumber DESC, quotationVersion DESC ");
    }


    public function save(&$errors = []) {
      if ($this->dueDate == "0000-00-00") {
        $this->dueDate = null;
      }
      if ($this->productionDate == "0000-00-00") {
        $this->productionDate = null;
      }
      if ($this->orderDate == "0000-00-00") {
        $this->orderDate = null;
      }
      if ($this->endstone == "") { //correctie vreemde setting
        $this->endstone = "false";
      }
      return parent::save($errors);
    }

    /**
     * Online afgerekend
     */
    public function setPaid() {
      $save = false;
      if ($this->payedFlag == 0) { //voorkom dubbel mail posts
        $this->payedFlag = 1;
        $save = true;
      }
      if ($this->statusId == 20) { //voorkom dubbel mail posts
        $this->statusId = 30;
        $save = true;
      }
      $this->save();

    }

    /**
     * Determine total quotation price
     * @param bool $inclvat
     * @return float
     */
    public function getTotalPrice($inclvat = true) {
      $total = $this->projectValue;
      $projects = Projects::find_all_by(["quotationId" => $this->quotationId]);
      foreach ($projects as $project) {
        $total += $project->euro;
      }
      $total += $this->freightCosts;
      if ($inclvat) {
        $total *= 1.21;
      }
      return $total;
    }

    /**
     * Get voegdikte
     * @return float
     */
    public function getVoegdikte() {
      return 3.5;
    }

    /**
     * Heeft deze quotation mogelijk verstek
     * @param OrderElements[] $elements
     * @param Stones $stone
     * @return bool
     */
    public function hasVerstek($elements, $stone) {
      if (!$elements || count($elements) == 0) return false;
      return OrderElements::hasVerstek($elements) && !($stone->isSpekband() && $this->brandId == 1);
    }

    /**
     * @param Stones $stone
     * @return bool
     */
    public function maySelectEndstone($stone) {
      return ($stone->isSpekband() && $this->brandId == StoneBrands::BRAND_ID_STJORIS) || ($stone->isKeramiek() && $stone->isMuurafdekker()) || ($stone->isNatuursteen() && $stone->isMuurafdekker());
    }

    /**
     * Get surace in m2 of quotation
     * @param Stones $stone
     * @return float|int
     */
    public function getSurface($stone) {
      if (!$stone->isNatuursteen()) return 0.00;

      if ($stone->isVensterbank()) {
        $opp = 0.00;
        $elements = AppModel::mapObjectIds(OrderElements::find_all_by(["quotationId" => $this->quotationId]), "elementId");
        $elementIds = [];
        foreach ($elements as $element) {
          $elementIds[] = $element->elementId;
        }
        $order_element_windowsills = AppModel::mapObjectIds(OrderElementWindowsill::find_all_by(["element_id" => $elementIds]), "element_id");
        foreach ($order_element_windowsills as $oew) {
          $opp += ($oew->x1 * $oew->x2) / 1000000 * $elements[$oew->element_id]->amount;
        }
        return round($opp, 2);
      }

      $opp = 0.00;
      $stone = Stones::find_by(["stoneId" => $this->stoneId]);
      $stoneSize = StoneSizes::find_by(["sizeId" => $stone->sizeId]);
      $elements = OrderElements::find_all_by(["quotationId" => $this->quotationId]);
      foreach ($elements as $element) {
        $opp += ($stoneSize->width * $element->elementLength) / 1000000 * $element->amount;
      }
      return round($opp, 2);

    }


    public function destroy() {
      $qe = QuotationsExtra::find_by(["quotationId" => $this->quotationId]);
      if ($qe) {
        $qe->destroy();
      }

      foreach (OrderElements::find_all_by(["quotationId" => $this->quotationId]) as $el) {
        $oep = OrderElementparts::find_by(["elementId" => $el->elementId]);
        if ($oep) {
          $oep->destroy();
        }
        $el->destroy();
      }

      $production_order = ProductionOrder::find_by(["quotationId" => $this->quotationId]);
      if ($production_order) {
        $production_order->destroy();
      }

      $custom_stone = QuotationsCustomStone::find_by(["quotationId" => $this->quotationId]);
      if ($custom_stone) {
        $custom_stone->destroy();
      }

      foreach (ContainersQuotations::find_all_by(["quotationId" => $this->quotationId]) as $containerQuotation) {
        $containerQuotation->destroy();
      }

      foreach (QuotationStatus::find_all_by(["quotationId" => $this->quotationId]) as $quotationStatus) {
        $quotationStatus->destroy();
      }

      foreach (QuoteStatus35::find_all_by(["quotationId" => $this->quotationId]) as $quoteStatus35) {
        $quoteStatus35->destroy();
      }

      parent::destroy();
    }

    public function getEndstoneName() {
      return match ($this->endstone) {
        "false" => "Nee",
        "left" => "Links",
        "right" => "Rechts",
        "leftg" => "Links met groeven",
        "rightg" => "Rechts met groeven",
        "true" => "Ja",
        "true_grooves" => "Ja, met groeven",
        "flat" => "Vlak",
        "standingside" => "Openstaande zijkant",
        "stuc" => "Stucprofiel",

        default => "Onbekend",
      };
    }
  }