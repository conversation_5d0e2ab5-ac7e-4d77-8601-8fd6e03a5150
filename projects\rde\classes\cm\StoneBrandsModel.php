<?php

  AppModel::loadBaseClass('BaseStoneBrands');

  class StoneBrandsModel extends BaseStoneBrands {

    const BRAND_ID_STJORIS = 1;
    const BRAND_ID_WIENERBERGER = 2;

    public static function getBrands(): array {
      return self::find_all("ORDER BY displayOrder");
    }

    public static function getBrandidsWithVensterbanken(): array {
      return [8, 9, 10, 11];
    }

    public function isChineesNatuursteen(): bool {
      return $this->brandId == 4;
    }

    public static function getBretiBrandIds(): array {
      return [5, 6, 7, 8, 9, 10, 11, 12, 15];
    }

  }