<section>

  <?php if(count($products) > 0): ?>
    <div class="row">
      <?php TemplateHelper::includePartial("_topmessage.php","siteshop") ?>
    </div>
  <?php endif; ?>

  <div>
    <h1><?php echo escapeSafe($category->getName($_SESSION['lang'])) ?></h1>
    <br/>
    <?php if($category->getDescription($_SESSION['lang'])!=""): ?>
      <span class="contenttxt">
        <?php echo $category->getDescription($_SESSION['lang']); ?>
        <br/>
        <br/>
      </span>
    <?php endif; ?>

    <?php if(false && $category->getPhotoUrlOrig(true)) : ?>
      <img class="img-responsive" src="<?php echo $category->getPhotoUrlOrig(true) ?>" alt="<?php echo escapeForInput($category->getName($_SESSION['lang'])) ?>" style="margin-bottom: 10px;"/>
    <?php endif; ?>

  </div>

<!--  --><?php //if($category->id==1): ?>
<!--    --><?php //include("_keramicStoneFilter.php") ?>
<!--  --><?php //elseif($category->id==19): ?>
<!--    --><?php //include("_keramicStandardlengthsFilter.php") ?>
<!--  --><?php //elseif($category->id==20): ?>
<!--    --><?php //include("_naturalStandardlengthsFilter.php") ?>
<!--  --><?php //endif; ?>

  <div style="padding-top: 15px;">

    <?php if(count($products) > 0): ?>
      <?php if(isset($pager)): echo $pager->writePreviousNextResp(); endif ?>
      <?php $tel = 0; ?>
      <div class="row multi-columns-row">
        <?php foreach ($products as $product): ?>
          <?php TemplateHelper::includePartial('_productitem.php', 'siteshop', [
            'product' => $product,
            'tel'     => $tel,
            'cells'   => 2,
          ]) ?>
          <?php
          $tel++;
        endforeach; ?>
      </div>
      <?php if(isset($pager)): echo $pager->writePreviousNextResp(); endif ?>
    <?php endif; ?>

    <?php if(count($subcategories) > 0): ?>
      <div class="panel-heading">
        <h3 class="panel-title">
          Subcategorieën
        </h3>
        <br/>
      </div>
      <div class="row">
        <?php foreach ($subcategories as $subcat): ?>
          <?php TemplateHelper::includePartial("_categoryitem.php", 'siteshop', ['categoryitem' => $subcat]); ?>
        <?php endforeach; ?>
      </div>
    <?php endif; ?>

<!--    --><?php //if(count($products) == 0 && count($subcategories)==0): ?>
<!--      Er zijn geen producten of categorieën gevonden binnen deze categorie.-->
<!--    --><?php //endif; ?>

  </div>

  <?php if ($category->getDescription2($_SESSION['lang']) != ""): ?>
    <div class="contenttxt" style="padding-top: 15px;">
      <?php echo $category->getDescription2($_SESSION['lang']); ?>
      <br/>
      <br/>
    </div>
  <?php endif; ?>

</section>
