<?php TemplateHelper::includePartial('_tabs.php', 'stones'); ?>

<div class="box">
    <form method="post" action="<?php echo reconstructQueryAdd() ?>">
      <input type="text" name="mitre_search" value="<?php echo $_SESSION['mitre_search'] ?>" placeholder="Zoeken..."/>
      <select name="mitre_stonelength" id="mitre_stonelength">
        <option value="">Selecteer steenlengte...</option>
        <?php foreach($stonelengths as $stonelength): ?>
          <option value="<?php echo $stonelength ?>" <?php if($_SESSION['mitre_stonelength']==$stonelength) echo 'selected'; ?>><?php echo $stonelength ?> mm</option>
        <?php endforeach; ?>
      </select>
      <select name="mitre_display"  id="mitre_display">
        <option value="">Selecteer zichtbaar...</option>
        <option value="true" <?php if($_SESSION['mitre_display']=="true") echo 'selected'; ?>>Ja</option>
        <option value="false" <?php if($_SESSION['mitre_display']=="false") echo 'selected'; ?>>Nee</option>
      </select>
      <input type="submit" name="go" id="go" value="Zoeken" />

      <a href="?action=mitreedit" class="gsd-btn gsd-btn-primary">Toevoegen nieuw verstek</a>
    </form>
  </div>

  <?php $pager->writePreviousNext(); ?>

  <?php if(count($items)==0): ?>
    <br/>
    Er zijn nog geen items gevonden.
  <?php else: ?>
    <table class="default_table" style="width: auto;">
      <tr class="dataTableHeadingRow">
        <td>Steenlengte mm</td>
        <td>Hoek º</td>
        <td>Aantal stenen</td>
        <td>Online</td>
        <td style="width: 70px;">Actie</td>
      </tr>
      <?php
        /** @var Mitres $item */
        foreach($items as $item): ?>
        <tr class="dataTableRow trhover">
          <td><?php echo $item->stoneLength ?></td>
          <td><?php echo $item->angle ?></td>
          <td><?php echo $item->stoneCount ?></td>
          <td><?php echo $item->display=="true"?"Ja":"Nee" ?></td>
          <td>
            <?php echo BtnHelper::getEdit(reconstructQueryAdd(['pageId']).'action=mitreedit&id='.$item->mitreId) ?>
          </td>
        </tr>
      <?php endforeach; ?>
    </table>
  <?php endif; ?>

<script>
  $(document).ready(function() {
    $("#mitre_stonelength,#mitre_display").change(function() {
      $("#go").click();
    })
  });
</script>