<?php
  TemplateHelper::includePartial('_tabs.php', 'sandbox');
  include("_header.php")
?>

<h3>2. Aanmaken nieuwe bedrijf</h3>

  <?php writeErrors($form_company->getErrors(), true); ?>

  <form method="post" name="form" id="form" class="edit-form">

    <table class="default_table">
      <tr class="dataTableHeadingRow">
        <td colspan="2">Bedrijfgegevens</td>
      </tr>
      <?php foreach ($form_company->getElements() as $element): ?>
        <?php $element->renderRow() ?>
      <?php endforeach; ?>
      <tr class="dataTableHeadingRow">
        <td colspan="2">Adresgegevens</td>
      </tr>
      <?php foreach ($form_visit->getElements() as $element): ?>
        <?php $element->renderRow() ?>
      <?php endforeach; ?>
      <tr class="dataTableHeadingRow">
        <td colspan="2">Persoonsgegevens</td>
      </tr>
      <?php foreach ($form_person->getElements() as $element): ?>
        <?php $element->renderRow() ?>
      <?php endforeach; ?>
      <tr class="dataTableHeadingRow">
        <td colspan="2">Facturatie</td>
      </tr>
      <?php foreach ($form_invoiceparty->getElements() as $element): ?>
        <?php $element->renderRow() ?>
      <?php endforeach; ?>
      <tr class="dataTableHeadingRow">
        <td colspan="2">Sandbox user</td>
      </tr>
      <?php foreach ($form_sandboxuser->getElements() as $element): ?>
        <?php $element->renderRow() ?>
      <?php endforeach; ?>
    </table>

    <input type="submit" name="go" value="Opslaan" class="gsd-btn gsd-btn-primary"/>

  </form>
