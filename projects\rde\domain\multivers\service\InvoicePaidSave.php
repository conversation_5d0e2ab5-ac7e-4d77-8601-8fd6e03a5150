<?php

  namespace domain\multivers\service;

  use gsdfw\domain\multivers\exception\MultiversException;
  use gsdfw\domain\multivers\model\CustomerEntryPaymentModel;
  use gsdfw\domain\multivers\model\FinTransEntryModel;
  use gsdfw\domain\multivers\model\FinTransModel;
  use gsdfw\domain\multivers\service\CustomerInvoice;
  use gsdfw\domain\multivers\service\CustomerInvoiceInfo;
  use gsdfw\domain\multivers\service\FinTrans;
  use gsdfw\domain\multivers\service\MultiversApi;
  use Invoices;
  use SandboxUsers;
  use Setting;

  /**
   * Class InvoicePaidSave
   * Set invoices paid in multivers
   * @package gsdfw\domain\multivers\service
   */
  class InvoicePaidSave {

    private $multivers_api;
    private $changedInvoices = [];

    public function __construct(MultiversApi $multivers_api) {
      $this->multivers_api = $multivers_api;
    }

    public function getChangedInvoices() {
      return $this->changedInvoices;
    }

    /**
     * Set invoices paid in multivers
     * @param Invoices[] $invoices
     * @return bool
     * @throws MultiversException
     */
    public function setpaid($invoices) {

      $customer_invoice = new CustomerInvoice($this->multivers_api);
      $invoice_service = new InvoiceSave($this->multivers_api);
      $customer_invoice_info = new CustomerInvoiceInfo($this->multivers_api);
      $fin_trans_service = new FinTrans($this->multivers_api);

      //ophalen laatste code
      $lastjournal = Setting::getValueByCode("MULTIVERS_LASTJOURNAL");
      $exists = true;
      $tellimit = 0;
      while ($exists) {
        $lastjournal++;
        $exists = $fin_trans_service->exists($lastjournal);
        $tellimit++;
        if ($tellimit >= 30) break;
      }

      if ($tellimit == 30) {
        throw new MultiversException("Kan geen unieke journal vinden (30 keer geprobeerd). In multivers kun je de laatste journals vinden onder Financieel > Bank. Laatste poging: " . $lastjournal);
      }


      foreach ($invoices as $k => $invoice) {
        if (!$customer_invoice->exists($invoice->invoiceNumber)) {
          //factuur bestaat niet. Toevoegen dan
          $result = $invoice_service->add($invoice);
          if ($result === false) {
            return false;
          }
        }
        $ciim = $customer_invoice_info->retrieveById($invoice->invoiceNumber);
        if ($ciim && $ciim->isPaid()) {
          //factuur staat al op betaald in multivers
          unset($invoices[$k]);
        }
      }

      if (count($invoices) == 0) {
        return true;
      }

      $obj = new FinTransModel();
      $obj->setNew(true);
      $obj->set("description", "Online " . date("Y-m-d H:i"));
//      $obj->set("description","TEST ".date("Y-m-d H:i"));
      $obj->set("fiscalYear", date("Y"));
      $obj->set("journalId", $this->multivers_api->getJournalId());
      $obj->set("journalTransaction", $lastjournal);
      $obj->set("periodNumber", date("n  "));
      $obj->set("transactionDate", date("d-m-Y"));
      $entries = [];
      foreach ($invoices as $invoice) {

        $user = SandboxUsers::getUserAndCompany($invoice->userId);
        if (!$user) {
          throw new MultiversException("SandboxUser niet gevonden. Userid: " . $invoice->userId);
        }

        $entry = new FinTransEntryModel();
        $entry->setNew(true);
        $entry->set("creditAmount", $invoice->totalProjectValue);
        $entry->set("creditAmountCur", $invoice->totalProjectValue);
        $entry->set("creditAmountCur", $invoice->totalProjectValue);
        $entry->set("customerId", $user->companyId);
        $entry->set("customerName", substr($user->company->name . (DEVELOPMENT ? ' TEST' : ''), 0, 40));
        $entry->set("description", substr($user->company->name . (DEVELOPMENT ? ' TEST' : ''), 0, 40));
        $entry->set("transactionDate", $invoice->getPaid("d-m-Y"));

        $cepm = new CustomerEntryPaymentModel();
        $cepm->setNew(true);
        $cepm->set("amountPaid", $invoice->totalProjectValue);
        $cepm->set("amountPaidCur", $invoice->totalProjectValue);
        $cepm->set("invoiceAmountCur", $invoice->totalProjectValue);
//        $cepm->set("invoiceDate",$invoice->getDateInvoice("d-m-Y"));
        $cepm->set("invoiceId", $invoice->invoiceNumber);
        $cepm->set("transactionDate", $invoice->getPaid("d-m-Y"));

        $entry->set("customerEntryPayments", [$cepm]);

        $this->changedInvoices[] = $invoice->invoiceNumber;

        $entries[] = $entry;

      }
      $obj->set("finTransEntries", $entries);
//      pd($obj->getPostProperties());
      $result = $this->multivers_api->post("FinTrans", $obj->getPostProperties());
//      pd($result);
//      if($this->multivers_api->hasError()) {
//        pd($this->multivers_api->getError()->getMessage());
//      }
      if ($result !== false) {
        Setting::setValueByCode("MULTIVERS_LASTJOURNAL", $lastjournal);
        $resobj = new FinTransModel();
        $resobj->map($result);
        logToFile("multivers_fintrans", print_r($resobj, true));
        return true;
      }
      return false;
    }

  }