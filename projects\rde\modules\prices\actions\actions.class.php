<?php

  use Gsd\Form\Elements\Number;
  use Gsd\Form\Elements\Select;
  use Gsd\Form\Elements\Text;
  use Gsd\Form\Form;
  use Gsd\Form\ModelForm;

  class pricesRdeActions extends gsdActions {

    public function preExecute() {
      parent::preExecute();
      $this->showleftmenu = true;
    }

    public function executeStonepricelist() {

      if (isset($_POST['reset'])) {
        unset($_SESSION['sp_search'], $_SESSION['sp_brand'], $_SESSION['sp_type'], $_SESSION['sp_endstone'], $_SESSION['sp_display'], $_SESSION['sp_colors'], $_SESSION['sp_years']);
        ResponseHelper::redirect(reconstructQuery());
      }

      if (isset($_POST["add"])) {
        $add = StonePrices::find_by(["stoneId" => $_POST["missingsize"], "validFrom" => date("Y-01-01")]);
        if (!$add) {
          $add = new StonePrices();
          $add->stoneId = $_POST["missingsize"];
          $add->validFrom = date("Y-01-01");
          $add->validTo = "9999-12-31";
          $add->price = 999.99;
          $add->save();
        }
        MessageFlashCoordinator::addMessage("Steen prijs toegevoegd.");
        //redirect(reconstructQueryAdd()."action=mitrefactoredit&yearfrom=".date("Y")."&edit=Bewerk+factorlijst");
        ResponseHelper::redirect(reconstructQuery());
      }

      if (!isset($_SESSION['sp_search'])) $_SESSION['sp_search'] = '';
      if (!isset($_SESSION['sp_brand'])) $_SESSION['sp_brand'] = '';
      if (!isset($_SESSION['sp_type'])) $_SESSION['sp_type'] = '';
      if (!isset($_SESSION['sp_endstone'])) $_SESSION['sp_endstone'] = '';
      if (!isset($_SESSION['sp_display'])) $_SESSION['sp_display'] = 'true';
      if (!isset($_SESSION['sp_colors'])) $_SESSION['sp_colors'] = [];
      if (!isset($_SESSION['sp_year'])) $_SESSION['sp_year'] = "now";

      if (isset($_POST['go'])) {
        if ($_SESSION['sp_brand'] != $_POST['sp_brand']) {
          //brand is reset, reset colors
          $_POST['sp_colors'] = [];
        }
        $_SESSION['sp_search'] = trim($_POST['sp_search']);
        $_SESSION['sp_brand'] = trim($_POST['sp_brand']);
        $_SESSION['sp_type'] = trim($_POST['sp_type']);
        $_SESSION['sp_endstone'] = trim($_POST['sp_endstone']);
        $_SESSION['sp_display'] = trim($_POST['sp_display']);
        $_SESSION['sp_year'] = trim($_POST['sp_year']);
        if (isset($_POST['sp_colors'])) {
          $_SESSION['sp_colors'] = $_POST['sp_colors'];
        }
        else {
          $_SESSION['sp_colors'] = [];
        }
        ResponseHelper::redirect(reconstructQuery());
      }

      $stones = $this->getStones(isset($_POST['edit']) || isset($_POST['editbatch']));

      if ($_SESSION['sp_year'] == "now" && $_SESSION['sp_brand'] != "") {

        $query = "SELECT * FROM " . Stones::getTablename() . " ";
        $query .= "LEFT JOIN " . StonePrices::getTablename() . " ON stone_prices.stoneId=stones.stoneId ";
        $query .= " WHERE stone_prices.id IS NULL ";
        $query .= " AND brandId='" . $_SESSION['sp_brand'] . "' ";
        $query .= " GROUP BY stones.stoneId ";
        $query .= " ORDER BY name ";
        $sizes_now = [];
        $result = DBConn::db_link()->query($query);
        while ($row = $result->fetch_row()) {
          $size = new Stones();
          $size->hydrate($row);
          $size->from_db = true;
          $sizes_now[] = $size;
        }
        $this->missing_sizes = $sizes_now;
      }

      $colors = [];
      if ($_SESSION['sp_brand'] != "") {
        $colors = StoneColors::getColors($_SESSION['sp_brand']);
      }
      $this->colors = $colors;
      $this->brands = AppModel::mapObjectIds(StoneBrands::find_all("WHERE NOT brandId IN (" . implode(",", StoneBrands::getBrandidsWithVensterbanken()) . ") ORDER BY displayOrder"), "brandId");
      if ($_SESSION['sp_brand'] != "" && !isset($this->brands[$_SESSION['sp_brand']])) {
        unset($_SESSION['sp_brand']);
        ResponseHelper::redirect(reconstructQueryAdd());
      }
      $this->items = $stones;

    }

    private function getStones($edit = false) {

      $this->pager = new Pager();
      $this->pager->setWriteCount(true);
      $this->pager->handle();

      if ($_SESSION['sp_brand'] != "") {
        $filt = " WHERE 1 ";
        if ($_SESSION['sp_brand'] != "") {
          $filt .= " AND brandId='" . $_SESSION['sp_brand'] . "' ";
        }
        $filt .= " AND type!='" . Stones::TYPE_VENSTERBANK . "' ";
        if ($_SESSION['sp_type'] != "") {
          $filt .= " AND type='" . $_SESSION['sp_type'] . "' ";
        }
        if ($_SESSION['sp_endstone'] != "") {
          $filt .= " AND endstone='" . $_SESSION['sp_endstone'] . "' ";
        }
        if ($_SESSION['sp_display'] != "") {
          $filt .= " AND display='" . $_SESSION['sp_display'] . "' ";
        }
        if ($_SESSION['sp_year'] == "now") {
          $filt .= " AND validFrom<='" . date("Y-m-d") . "' AND validTo>'" . date("Y-m-d") . "' ";
        }
        elseif ($_SESSION['sp_year'] != "") {
          $filt .= " AND YEAR(validFrom)='" . $_SESSION['sp_year'] . "' ";
        }
        if (count($_SESSION['sp_colors']) > 0) {
          $filt .= " AND colorId IN (" . implode(",", $_SESSION['sp_colors']) . ") ";
        }
        if ($_SESSION['sp_search'] != "") {
          $searchval = escapeForDB($_SESSION['sp_search']);
          $filt .= " AND (";
          $filt .= " name LIKE '%" . $searchval . "%' ";
          $filt .= ")";
        }

      }
      else {
        $filt = "WHERE 0 ";
      }

      $query = "SELECT * FROM " . Stones::getTablename() . " ";
      $query .= "JOIN " . StonePrices::getTablename() . " ON stone_prices.stoneId=stones.stoneId ";
      $query .= $filt;

      if (!$edit) {
        $result = DBConn::db_link()->query($query);
        $this->pager->count = $result->num_rows;
        if (!$this->pager->count) $this->pager->count = 0;
      }
      $query .= "ORDER BY name";
      if (!$edit) {
        $query .= $this->pager->getLimitQuery();
      }

      $result = DBConn::db_link()->query($query);
      $stones = [];
      while ($row = $result->fetch_row()) {
        $stone = new Stones();
        $stone->hydrate($row);
        $stone->from_db = true;
        $stone->price = new StonePrices();
        $stone->price->hydrate($row, count(Stones::columns));
        $stone->price->from_db = true;
        $stones[] = $stone;
      }
      return $stones;
    }

    public function executeStonepriceedit() {
      if (isset($_GET["editbatch"])) {
        $this->stonepriceeditbatch();
      }
      else {
        $this->stonepriceedit();
      }
    }

    private function stonepriceedit() {
      $stones = $this->getStones(true);

      $validFrom = $_GET["yearfrom"] . "-01-01";
      $colors = [];
      if (isset($_SESSION['sp_colors']) && count($_SESSION['sp_colors']) >= 2) {
        $colors = StoneColors::find_all_by(["colorId" => $_SESSION['sp_colors']]);
      }

      $form = new Form();
      $tel = 2;
      foreach ($stones as $stone) {
        $input = new Text("", "buyprice_" . $stone->stoneId);
        $input->addClass("buyprice");
        $input->addAtribute("tabindex", $tel);
        $form->addElement($input);

        $factor = new Text("Factor", "factor_" . $stone->stoneId, $stone->getPricefactor());
        $factor->addClass("factor");
        $form->addElement($factor);

        $input = new Text("", "price_" . $stone->stoneId);
        $input->addClass("price");
        $price_in_this_year = StonePrices::find_by(["stoneId" => $stone->stoneId, "validFrom" => $validFrom]);
        if ($price_in_this_year) {
          $stone->updateexisting = $price_in_this_year;
          $input->setValue($price_in_this_year->price);
        }
        $form->addElement($input);

        $tel++;
      }

      if (isset($_POST["go"]) || isset($_POST["go_list"])) {

        $form->setElementsValue($_POST);

        if ($form->isValid()) { //normally like this

          foreach ($stones as $stone) {
            $value = $form->getElement("price_" . $stone->stoneId)->getValue();
            if (!empty($value) && is_numeric($value)) {

              if (isset($stone->updateexisting)) {
                //dit is een bestaande prijs welke word aangepast.
                $stone->updateexisting->price = $value;
                $stone->updateexisting->save();
                continue;
              }

              //huidige prijs validTo zetten
              $price_current = $stone->price;
              if ($price_current) {
                $price_current->validTo = date("Y-m-d", strtotime($_GET["yearfrom"] . "-12-31 -1 YEAR"));
                $price_current->save();
              }

              //nieuwe prijs aanpassen/aanmaken
              $price_new = StonePrices::find_by(["stoneId" => $stone->stoneId, "validFrom" => $validFrom, "validTo" => "9999-12-31"]);
              if (!$price_new) {
                $price_new = new StonePrices();
                $price_new->stoneId = $stone->stoneId;
                $price_new->validFrom = $validFrom;
                $price_new->validTo = "9999-12-31";
              }
              $price_new->price = $value;
              $price_new->save();
            }
            elseif (empty($value) && isset($stone->updateexisting)) {
              //er is een prijs in dit jaar, maar deze is leeg gemaakt. Verwijder database prijs.
              $stone->updateexisting->destroy();

              //laatste prijs einddatum tot oneindig zetten
              $price_last = StonePrices::find_by(["stoneId" => $stone->stoneId, "validTo" => date("Y-m-d", strtotime($_GET["yearfrom"] . "-12-31 -1 YEAR"))]);
              if ($price_last) {
                $price_last->validTo = "9999-12-31";
                $price_last->save();
              }
            }
          }

          MessageFlashCoordinator::addMessage("Prijzen opgeslagen");
          if (isset($_POST["go_list"])) {
            ResponseHelper::redirect(PageMap::getUrl('M_STONEPRICES'));
          }
          else {
            ResponseHelper::redirect(reconstructQuery());
          }
        }

      }

      $this->stones = $stones;
      $this->form = $form;
      $this->brands = AppModel::mapObjectIds(StoneBrands::getBrands(), "brandId");
      $this->colors = $colors;
      $this->template = "stonepriceeditSuccess.php";

    }


    private function stonepriceeditbatch() {
      $stones = $this->getStones(true);

      //deze stenen gaan we groeperen op size/endstone
      $stones_grouped = [];
      foreach ($stones as $stone) {
        $endstone = $stone->endstone;
        if ($endstone == Stones::ENDSTONE_RIGHT) {
          $endstone = Stones::ENDSTONE_LEFT;
        }
        if ($endstone == Stones::ENDSTONE_RIGHTG) {
          $endstone = Stones::ENDSTONE_LEFTG;
        }
        $key = $stone->sizeId . "|" . $endstone;
        if (!isset($stones_grouped[$key])) {
          $stones_grouped[$key] = [];
        }
        $stones_grouped[$key][] = $stone;
      }

      $validFrom = $_GET["yearfrom"] . "-01-01";
      $colors = [];
      if (isset($_SESSION['sp_colors']) && count($_SESSION['sp_colors']) >= 2) {
        $colors = StoneColors::find_all_by(["colorId" => $_SESSION['sp_colors']]);
      }

      $form = new Form();
      $tel = 2;
      foreach ($stones_grouped as $key => $stones) {
        $stone = $stones[0];
        $input = new Text("", "buyprice_" . $key);
        $input->addClass("buyprice");
        $input->addAtribute("tabindex", $tel);
        $form->addElement($input);

        $factor = new Text("Factor", "factor_" . $key, $stone->getPricefactor());
        $factor->addClass("factor");
        $form->addElement($factor);

        $input = new Text("", "price_" . $key);
        $input->addClass("price");
        $price_in_this_year = StonePrices::find_by(["stoneId" => $stone->stoneId, "validFrom" => $validFrom]);
        if ($price_in_this_year) {
          $stone->updateexisting = $price_in_this_year;
          $input->setValue($price_in_this_year->price);
        }
        $form->addElement($input);

        $tel++;
      }

      if (isset($_POST["go"]) || isset($_POST["go_list"])) {

        $form->setElementsValue($_POST);

        if ($form->isValid()) { //normally like this

          foreach ($stones_grouped as $key => $stones) {
            $value = $form->getElement("price_" . $key)->getValue();
            foreach ($stones as $stone) {
              if (!empty($value) && is_numeric($value)) {

                if (isset($stone->updateexisting)) {
                  //dit is een bestaande prijs welke word aangepast.
                  $stone->updateexisting->price = $value;
                  $stone->updateexisting->save();
                  continue;
                }

                //huidige prijs validTo zetten
                $price_current = $stone->price;
                if ($price_current) {
                  $price_current->validTo = date("Y-m-d", strtotime($_GET["yearfrom"] . "-12-31 -1 YEAR"));
                  $price_current->save();
                }

                //nieuwe prijs aanpassen/aanmaken
                $price_new = StonePrices::find_by(["stoneId" => $stone->stoneId, "validFrom" => $validFrom, "validTo" => "9999-12-31"]);
                if (!$price_new) {
                  $price_new = new StonePrices();
                  $price_new->stoneId = $stone->stoneId;
                  $price_new->validFrom = $validFrom;
                  $price_new->validTo = "9999-12-31";
                }
                $price_new->price = $value;
                $price_new->save();
              }
              elseif (empty($value) && isset($stone->updateexisting)) {
                //er is een prijs in dit jaar, maar deze is leeg gemaakt. Verwijder database prijs.
                $stone->updateexisting->destroy();

                //laatste prijs einddatum tot oneindig zetten
                $price_last = StonePrices::find_by(["stoneId" => $stone->stoneId, "validTo" => date("Y-m-d", strtotime($_GET["yearfrom"] . "-12-31 -1 YEAR"))]);
                if ($price_last) {
                  $price_last->validTo = "9999-12-31";
                  $price_last->save();
                }
              }
            }
          }

          MessageFlashCoordinator::addMessage("Prijzen opgeslagen");
          if (isset($_POST["go_list"])) {
            ResponseHelper::redirect(PageMap::getUrl('M_STONEPRICES'));
          }
          else {
            ResponseHelper::redirect(reconstructQuery());
          }
        }

      }

      $this->stones_grouped = $stones_grouped;
      $this->form = $form;
      $this->brands = AppModel::mapObjectIds(StoneBrands::getBrands(), "brandId");
      $this->colors = $colors;
      $this->template = "stonepriceeditbatchSuccess.php";

    }

    public function executeGluepricelist() {
      if (isset($_POST['reset'])) {
        unset($_SESSION['gp_search'], $_SESSION['gp_brand'], $_SESSION['gp_type'], $_SESSION['gp_display'], $_SESSION['gp_years']);
        ResponseHelper::redirect(reconstructQuery());
      }

      if (isset($_POST["add"])) {
        $add = GluePrices::find_by(["sizeId" => $_POST["missingsize"], "validFrom" => date("Y-01-01")]);
        if (!$add) {
          $add = new GluePrices();
          $add->sizeId = $_POST["missingsize"];
          $add->validFrom = date("Y-01-01");
          $add->validTo = "9999-12-31";
          $add->price = 99.99;
          $add->save();
        }
        MessageFlashCoordinator::addMessage("Lijm prijs toegevoegd.");
        //redirect(reconstructQueryAdd()."action=mitrefactoredit&yearfrom=".date("Y")."&edit=Bewerk+factorlijst");
        ResponseHelper::redirect(reconstructQuery());
      }


      if (!isset($_SESSION['gp_search'])) $_SESSION['gp_search'] = '';
      if (!isset($_SESSION['gp_brand'])) $_SESSION['gp_brand'] = '';
      if (!isset($_SESSION['gp_display'])) $_SESSION['gp_display'] = 'true';
      if (!isset($_SESSION['gp_year'])) $_SESSION['gp_year'] = "now";

      if (isset($_POST['go'])) {
        $_SESSION['gp_search'] = trim($_POST['gp_search']);
        $_SESSION['gp_brand'] = trim($_POST['gp_brand']);
        $_SESSION['gp_display'] = trim($_POST['gp_display']);
        $_SESSION['gp_year'] = trim($_POST['gp_year']);
        ResponseHelper::redirect(reconstructQuery());
      }

      $sizes = $this->getGlueprice(isset($_POST['edit']));

      if ($_SESSION['gp_year'] == "now" && $_SESSION['gp_brand'] != "") {

        $query = "SELECT * FROM " . StoneSizes::getTablename() . " ";
        $query .= "LEFT JOIN " . GluePrices::getTablename() . " ON glue_prices.sizeId=stone_sizes.sizeId ";
        $query .= " WHERE glue_prices.id IS NULL ";
        $query .= " AND brandId='" . $_SESSION['gp_brand'] . "' ";
        $query .= " GROUP BY stone_sizes.sizeId ";
        $query .= " ORDER BY name ";
        $sizes_now = [];
        $result = DBConn::db_link()->query($query);
        while ($row = $result->fetch_row()) {
          $size = new StoneSizes();
          $size->hydrate($row);
          $size->from_db = true;
          $sizes_now[] = $size;
        }
        $this->missing_sizes = $sizes_now;
      }


      $this->brands = AppModel::mapObjectIds(StoneBrands::getBrands(), "brandId");
      $this->items = $sizes;

    }

    /**
     * @param false $edit
     * @return StoneSizes[]
     */
    private function getGlueprice($edit = false) {

      $this->pager = new Pager();
      $this->pager->setWriteCount(true);
      $this->pager->handle();

      $filt = " WHERE 1 ";
      if ($_SESSION['gp_brand'] != "") {
        $filt .= " AND brandId='" . $_SESSION['gp_brand'] . "' ";
      }
      if ($_SESSION['gp_display'] != "") {
        $filt .= " AND display='" . $_SESSION['gp_display'] . "' ";
      }
      if ($_SESSION['gp_year'] == "now") {
        $filt .= " AND validFrom<='" . date("Y-m-d") . "' AND validTo>'" . date("Y-m-d") . "' ";
      }
      else {
        $filt .= " AND YEAR(validFrom)='" . $_SESSION['gp_year'] . "' ";
      }
      if ($_SESSION['gp_search'] != "") {
        $searchval = escapeForDB($_SESSION['gp_search']);
        $filt .= " AND (";
        $filt .= " name LIKE '%" . $searchval . "%' ";
        $filt .= ")";
      }


      $query = "SELECT * FROM " . StoneSizes::getTablename() . " ";
      $query .= "JOIN " . GluePrices::getTablename() . " ON glue_prices.sizeId=stone_sizes.sizeId ";
      $query .= $filt;

      if (!$edit) {
        $result = DBConn::db_link()->query($query);
        $this->pager->count = $result->num_rows;
        if (!$this->pager->count) $this->pager->count = 0;
      }
      $query .= "ORDER BY name";
      if (!$edit) {
        $query .= $this->pager->getLimitQuery();
      }

      $result = DBConn::db_link()->query($query);
      $sizes = [];
      while ($row = $result->fetch_row()) {
        $size = new StoneSizes();
        $size->hydrate($row);
        $size->from_db = true;
        $size->price = new GluePrices();
        $size->price->hydrate($row, count(StoneSizes::columns));
        $size->price->from_db = true;
        $sizes[] = $size;
      }
      return $sizes;
    }

    public function executeGluepriceedit() {
      $sizes = $this->getGlueprice(true);

      $validFrom = $_GET["yearfrom"] . "-01-01";

      $form = new Form();
      $tel = 2;
      foreach ($sizes as $size) {

        $input = new Text("", "price_" . $size->sizeId);
        $input->addClass("price");
        $price_in_this_year = GluePrices::find_by(["sizeId" => $size->sizeId, "validFrom" => $validFrom]);
        if ($price_in_this_year) {
          $size->updateexisting = $price_in_this_year;
          $input->setValue($price_in_this_year->price);
        }
        $form->addElement($input);

        $tel++;
      }

      if (isset($_POST["go"]) || isset($_POST["go_list"])) {

        $form->setElementsValue($_POST);

        if ($form->isValid()) { //normally like this

          foreach ($sizes as $size) {
            $value = $form->getElement("price_" . $size->sizeId)->getValue();
            if (!empty($value) && is_numeric($value)) {

              if (isset($size->updateexisting)) {
                //dit is een bestaande prijs welke word aangepast.
                $size->updateexisting->price = $value;
                $size->updateexisting->save();
                continue;
              }

              //huidige prijs validTo zetten
              $price_current = $size->price;
              if ($price_current) {
                $price_current->validTo = date("Y-m-d", strtotime($_GET["yearfrom"] . "-12-31 -1 YEAR"));
                $price_current->save();
              }

              //nieuwe prijs aanpassen/aanmaken
              $price_new = GluePrices::find_by(["sizeId" => $size->sizeId, "validFrom" => $validFrom, "validTo" => "9999-12-31"]);
              if (!$price_new) {
                $price_new = new GluePrices();
                $price_new->sizeId = $size->sizeId;
                $price_new->validFrom = $validFrom;
                $price_new->validTo = "9999-12-31";
              }
              $price_new->price = $value;
              $price_new->save();
            }
            elseif (empty($value) && isset($size->updateexisting)) {
              //er is een prijs in dit jaar, maar deze is leeg gemaakt. Verwijder database prijs.
              $size->updateexisting->destroy();

              //laatste prijs einddatum tot oneindig zetten
              $price_last = GluePrices::find_by(["sizeId" => $size->sizeId, "validTo" => date("Y-m-d", strtotime($_GET["yearfrom"] . "-12-31 -1 YEAR"))]);
              if ($price_last) {
                $price_last->validTo = "9999-12-31";
                $price_last->save();
              }
            }
          }

          MessageFlashCoordinator::addMessage("Lijmprijzen opgeslagen");
          if (isset($_POST["go_list"])) {
            ResponseHelper::redirect(PageMap::getUrl('M_GLUEPRICES'));
          }
          else {
            ResponseHelper::redirect(reconstructQuery());
          }
        }

      }

      $this->sizes = $sizes;
      $this->form = $form;
      $this->brands = AppModel::mapObjectIds(StoneBrands::getBrands(), "brandId");
    }

    public function executeMitrefactorlist() {

      if (isset($_POST['reset'])) {
        unset($_SESSION['mp_search'], $_SESSION['mp_display'], $_SESSION['mp_years']);
        ResponseHelper::redirect(reconstructQuery());
      }

      if (isset($_POST["add"])) {
        $add = MitrePrices::find_by(["stoneLength" => $_POST["missingsize"], "validFrom" => date("Y-01-01")]);
        if (!$add) {
          $add = new MitrePrices();
          $add->stoneLength = $_POST["missingsize"];
          $add->validFrom = date("Y-01-01");
          $add->validTo = "9999-12-31";
          $add->factor = 1;
          $add->save();
        }
        MessageFlashCoordinator::addMessage("Factor " . $add->stoneLength . " toegevoegd.");
        //redirect(reconstructQueryAdd()."action=mitrefactoredit&yearfrom=".date("Y")."&edit=Bewerk+factorlijst");
        ResponseHelper::redirect(reconstructQuery());
      }

      if (!isset($_SESSION['mp_search'])) $_SESSION['mp_search'] = '';
      if (!isset($_SESSION['mp_year'])) $_SESSION['mp_year'] = "now";

      if (isset($_POST['go'])) {
        $_SESSION['mp_search'] = trim($_POST['mp_search']);
        $_SESSION['mp_year'] = trim($_POST['mp_year']);
        ResponseHelper::redirect(reconstructQuery());
      }

      $sizes = $this->getMitrefactors(isset($_POST['edit']));

      if ($_SESSION['mp_year'] == "now") {
        //huidige prijslijst word getoond. Zoek niet gedefineerde factors
        $missing_sizes = [];
        $stone_sizes = AppModel::mapObjectIds(StoneSizes::find_all("GROUP BY length ORDER BY length"), "length");
        foreach ($stone_sizes as $sz) {
          $found = false;
          foreach ($sizes as $size) {
            if ($size->stoneLength == $sz->length) {
              $found = true;
              break;
            }
          }
          if (!$found) {
            $missing_sizes[] = $sz->length;
          }
        }
        $this->missing_sizes = $missing_sizes;
      }

      $this->brands = AppModel::mapObjectIds(StoneBrands::getBrands(), "brandId");
      $this->items = $sizes;

    }

    private function getMitrefactors($edit = false) {

      $this->pager = new Pager();
      $this->pager->setWriteCount(true);
      $this->pager->handle();

      $filt = " WHERE 1 ";
      if ($_SESSION['mp_year'] != "now") {
        $filt .= " AND YEAR(validFrom)='" . $_SESSION['mp_year'] . "' ";
      }
      elseif ($_SESSION['mp_year'] != "") {
        $filt .= " AND validFrom<='" . date("Y-m-d") . "' AND validTo>'" . date("Y-m-d") . "' ";
      }
      if ($_SESSION['mp_search'] != "") {
        $searchval = escapeForDB($_SESSION['mp_search']);
        $filt .= " AND (";
        $filt .= " stoneLength LIKE '%" . $searchval . "%' ";
        $filt .= ")";
      }


      $query = $filt;

      if (!$edit) {
        $this->pager->count = MitrePrices::count_all_by([], $filt);
        if (!$this->pager->count) $this->pager->count = 0;
      }
      $query .= "ORDER BY stoneLength";
      if (!$edit) {
        $query .= $this->pager->getLimitQuery();
      }

      $mitres = MitrePrices::find_all($query);

      return $mitres;
    }

    public function executeMitrefactoredit() {
      $mitres = $this->getMitrefactors(true);

      $validFrom = $_GET["yearfrom"] . "-01-01";

      $form = new Form();
      $tel = 2;
      foreach ($mitres as $mitre) {

        $input = new Text("", "factor_" . $mitre->stoneLength);
        $input->addClass("price");
        $price_in_this_year = MitrePrices::find_by(["stoneLength" => $mitre->stoneLength, "validFrom" => $validFrom]);
        if ($price_in_this_year) {
          $mitre->updateexisting = $price_in_this_year;
          $input->setValue($price_in_this_year->factor);
        }
        $form->addElement($input);

        $tel++;
      }

      if (isset($_POST["go"]) || isset($_POST["go_list"])) {

        $form->setElementsValue($_POST);

        if ($form->isValid()) { //normally like this

          foreach ($mitres as $mitre) {
            $value = $form->getElement("factor_" . $mitre->stoneLength)->getValue();
            if (!empty($value) && is_numeric($value)) {

              if (isset($mitre->updateexisting)) {
                //dit is een bestaande prijs welke word aangepast.
                $mitre->updateexisting->factor = $value;
                $mitre->updateexisting->save();
                continue;
              }

              //huidige prijs validTo zetten
              if ($mitre) {
                $mitre->validTo = date("Y-m-d", strtotime($_GET["yearfrom"] . "-12-31 -1 YEAR"));
                $mitre->save();
              }

              //nieuwe prijs aanpassen/aanmaken
              $factor_new = MitrePrices::find_by(["id" => $mitre->id, "validFrom" => $validFrom, "validTo" => "9999-12-31"]);
              if (!$factor_new) {
                $factor_new = new MitrePrices();
                $factor_new->stoneLength = $mitre->stoneLength;
                $factor_new->validFrom = $validFrom;
                $factor_new->validTo = "9999-12-31";
              }
              $factor_new->factor = $value;
              $factor_new->save();
            }
            elseif (empty($value) && isset($mitre->updateexisting)) {
              //er is een prijs in dit jaar, maar deze is leeg gemaakt. Verwijder database prijs.
              $mitre->updateexisting->destroy();

              //laatste prijs einddatum tot oneindig zetten
              $factor_last = MitrePrices::find_by(["stoneLength" => $mitre->stoneLength, "validTo" => date("Y-m-d", strtotime($_GET["yearfrom"] . "-12-31 -1 YEAR"))]);
              if ($factor_last) {
                $factor_last->validTo = "9999-12-31";
                $factor_last->save();
              }
            }
          }

          MessageFlashCoordinator::addMessage("Verstekfactors opgeslagen");
          if (isset($_POST["go_list"])) {
            ResponseHelper::redirect(PageMap::getUrl('M_MITREFACTORS'));
          }
          else {
            ResponseHelper::redirect(reconstructQuery());
          }
        }

      }

      $this->mitres = $mitres;
      $this->form = $form;

    }

    public function executeCustcategorylist() {

      //voorbeeld filterform met GSD form builder

      if (isset($_POST['reset'])) {
        unset($_SESSION['cc_filter']);
        ResponseHelper::redirect(reconstructQuery());
      }
      if (!isset($_SESSION['cc_filter'])) {
        $_SESSION['cc_filter']['search'] = '';
        $_SESSION['cc_filter']['year'] = "now";
      }
      if (isset($_POST['go'])) {
        $_SESSION['cc_filter'] = $_POST['cc_filter'];
        ResponseHelper::redirect(reconstructQuery());
      }

      $filter_form = new Form("cc_filter");

      $input_search = new Text("Zoeken", "search");
      $filter_form->addElement($input_search);

      $input_year = new Select("Jaar", "year");
      $input_year->addOptionHelper("now", "Huidige opslag");
      for ($tel = date("Y") + 1; $tel > 2011; $tel--) {
        $input_year->addOptionHelper($tel, $tel);
      }
      $filter_form->addElement($input_year);

      $filter_form->setElementsValue($_SESSION); //zet de waarden op het form
      $filter_form->trimElementValues();
      $this->filter_form = $filter_form;

      $filt = " WHERE 1 ";
      $showdate = $input_year->getValue() . "-01-01";
      if ($input_year->getValue() == "now") {
        $showdate = date("Y-m-d");
      }
      $filt .= " AND validFrom<='" . $showdate . "' AND validTo>'" . $showdate . "' ";
      if ($input_search->getValue() != "") {
        $searchval = escapeForDB($input_search->getValue());
        $filt .= " AND (";
        $filt .= " name LIKE '%" . $searchval . "%' ";
        $filt .= ")";
      }

      $query = "SELECT * FROM " . CustomerGroups::getTablename() . " ";
      $query .= "JOIN " . CustomerProductincreases::getTablename() . " ON customer_productincreases.groupId=customer_groups.groupId ";
      $query .= $filt;
      $query .= "ORDER BY orderId";

      $result = DBConn::db_link()->query($query);
      $groups = [];
      while ($row = $result->fetch_row()) {
        $group = new CustomerGroups();
        $group->hydrate($row);
        $group->from_db = true;
        $group->surcharge = new CustomerProductincreases();
        $group->surcharge->hydrate($row, count(CustomerGroups::columns));
        $group->surcharge->from_db = true;
        $groups[] = $group;
      }

      $this->brands = AppModel::mapObjectIds(StoneBrands::getBrands(), "brandId");
      $this->items = $groups;

    }

    public function executeCustcategoryedit() {

      $validFrom = $_GET["yearfrom"] . "-01-01";

      $filt = " WHERE 1 ";
      $filt .= " AND validFrom<='" . $validFrom . "' AND validTo>'" . $validFrom . "' ";
      if ($_SESSION["cc_filter"]['search'] != "") {
        $searchval = escapeForDB($_SESSION["cc_filter"]['search']);
        $filt .= " AND (";
        $filt .= " name LIKE '%" . $searchval . "%' ";
        $filt .= ")";
      }

      $query = "SELECT * FROM " . CustomerGroups::getTablename() . " ";
      $query .= "JOIN " . CustomerProductincreases::getTablename() . " ON customer_productincreases.groupId=customer_groups.groupId ";
      $query .= $filt;
      $query .= "ORDER BY orderId";

      $result = DBConn::db_link()->query($query);
      $groups = [];
      while ($row = $result->fetch_row()) {
        $group = new CustomerGroups();
        $group->hydrate($row);
        $group->from_db = true;
        $group->surcharge = new CustomerProductincreases();
        $group->surcharge->hydrate($row, count(CustomerGroups::columns));
        $group->surcharge->from_db = true;

        $groups[$group->groupId] = $group;
      }


      $forms = [];
      foreach ($groups as $group) {
        $form = new ModelForm("group_" . $group->groupId);
        $form->addClass("edit-form");
        $form->buildElementsFromModel($group->surcharge);

        $forms[$group->groupId] = $form;
      }

      if (isset($_POST["go"]) || isset($_POST["go_list"])) {

        $isValid = true;
        foreach ($forms as $form) {
          $form->setElementsAndObjectValue($_POST);
          if (!$form->isValid()) {
            $isValid = false;
          }
        }

        if ($isValid) { //normally like this

          foreach ($forms as $form) {
            /** @var CustomerProductincreases $cpi */
            $cpi = $form->getModelobject();

            $increase_in_this_year = CustomerProductincreases::find_by(["groupId" => $cpi->groupId, "validFrom" => $validFrom]);
            if ($increase_in_this_year) {
              //dit is een bestaande prijs welke word aangepast.
              $cpi->save();
              continue;
            }

            //voorgaande jaar afsluiten, zonder aanpassing
            $cpi_prev = CustomerProductincreases::find_by_id($cpi->id);
            $cpi_prev->validTo = date("Y-m-d", strtotime($_GET["yearfrom"] . "-12-31 -1 YEAR"));
            $cpi_prev->save();

            //nieuwe prijs aanpassen/aanmaken
            $cpi_new = CustomerProductincreases::find_by(["groupId" => $cpi->groupId, "validFrom" => $validFrom, "validTo" => "9999-12-31"]);
            if (!$cpi_new) {
              //kopier data in nieuwe
              $cpi_new = new CustomerProductincreases($cpi->asArray());
              $cpi_new->from_db = false;
              $cpi_new->id = false;
              $cpi_new->groupId = $cpi->groupId;
              $cpi_new->validFrom = $validFrom;
              $cpi_new->validTo = "9999-12-31";
            }
            else {
              //kopier data in bestaande
              $cpi_new = new CustomerProductincreases($cpi->asArray());
              $cpi_new->validFrom = $validFrom;
              $cpi_new->validTo = "9999-12-31";
            }
            //controleer of er al een item is volgend jaar
            $cpi_future = CustomerProductincreases::find_by(["groupId" => $cpi->groupId, "validFrom" => date("Y-m-d", strtotime($validFrom . " +1 YEAR")), "validTo" => "9999-12-31"]);
            if ($cpi_future) {
              //er is er 1, starten vanaf 1 januari volgend jaar
              $cpi_future->validFrom = date("Y-m-d", strtotime($validFrom . " +1 YEAR"));
              $cpi_future->save();
              //deze stoppen op 31 december dit jaar
              $cpi_new->validTo = $_GET["yearfrom"] . "-12-31";
            }

            $cpi_new->save();

          }

          MessageFlashCoordinator::addMessage("Klantgroep opslag opgeslagen");
          if (isset($_POST["go_list"])) {
            ResponseHelper::redirect(PageMap::getUrl('M_CUSTOMERCATS'));
          }
          else {
            ResponseHelper::redirect(reconstructQuery());
          }
        }
      }

      $errors = [];
      foreach ($forms as $form) {
        $errors = array_merge($errors, $form->getErrors());
      }
      $this->groups = $groups;
      $this->forms = $forms;
      $this->errors = $errors;
    }


    public function executePaymenttermlist() {

      //voorbeeld filterform met GSD form builder

      if (isset($_POST['reset'])) {
        unset($_SESSION['pt_filter']);
        ResponseHelper::redirect(reconstructQuery());
      }
      if (!isset($_SESSION['pt_filter'])) {
        $_SESSION['pt_filter']['search'] = '';
        $_SESSION['pt_filter']['year'] = "now";
      }
      if (isset($_POST['go'])) {
        $_SESSION['pt_filter'] = $_POST['pt_filter'];
        ResponseHelper::redirect(reconstructQuery());
      }

      $filter_form = new Form("pt_filter");

      $input_search = new Text("Zoeken", "search");
      $filter_form->addElement($input_search);

      $input_year = new Select("Jaar", "year");
      $input_year->addOptionHelper("now", "Huidige termijn");
      for ($tel = date("Y") + 1; $tel > 2011; $tel--) {
        $input_year->addOptionHelper($tel, $tel);
      }
      $filter_form->addElement($input_year);

      $filter_form->setElementsValue($_SESSION); //zet de waarden op het form
      $filter_form->trimElementValues();
      $this->filter_form = $filter_form;

      $group_filt = "";
      if ($input_search->getValue() != "") {
        $searchval = escapeForDB($input_search->getValue());
        $group_filt .= " WHERE (";
        $group_filt .= " name LIKE '%" . $searchval . "%' ";
        $group_filt .= ")";
      }
      $groups = AppModel::mapObjectIds(CustomerGroups::find_all($group_filt), "groupId");

      foreach (CustomerCodes::find_all() as $cc) {
        if (isset($groups[$cc->groupId])) {
          $groups[$cc->groupId]->paymentterms[$cc->payterm] = $cc;
        }
      }

      $showdate = $input_year->getValue() . "-01-01";
      if ($input_year->getValue() == "now") {
        $showdate = date("Y-m-d");
      }
      $filt = " WHERE validFrom<='" . $showdate . "' AND validTo>'" . $showdate . "' ";

      $cpts = AppModel::mapObjectIds(CustomerPaytermincreases::find_all($filt), "codeId");
      foreach ($groups as $group) {
        foreach ($group->paymentterms as $cc) {
          if (isset($cpts[$cc->codeId])) {
            $cc->current = $cpts[$cc->codeId];
          }
        }
      }

      $this->items = $groups;

    }


    public function executePaymenttermedit() {

      $validFrom = $_GET["yearfrom"] . "-01-01";

      $group_filt = "";
      if ($_SESSION["pt_filter"]['search'] != "") {
        $searchval = escapeForDB($_SESSION["pt_filter"]['search']);
        $group_filt .= " WHERE (";
        $group_filt .= " name LIKE '%" . $searchval . "%' ";
        $group_filt .= ")";
      }
      $groups = AppModel::mapObjectIds(CustomerGroups::find_all($group_filt), "groupId");

      foreach (CustomerCodes::find_all() as $cc) {
        if (isset($groups[$cc->groupId])) {
          $groups[$cc->groupId]->paymentterms[$cc->payterm] = $cc;
        }
      }

      $filt = " WHERE validFrom<='" . $validFrom . "' AND validTo>'" . $validFrom . "' ";
      $cpts = AppModel::mapObjectIds(CustomerPaytermincreases::find_all($filt), "codeId");
      foreach ($groups as $group) {
        foreach ($group->paymentterms as $cc) {
          if (isset($cpts[$cc->codeId])) {
            $cc->current = $cpts[$cc->codeId];
          }
        }
      }

      $form = new ModelForm();
      $form->addClass("edit-form");
      foreach ($groups as $group) {
        foreach (Config::get("RDE_PAYMENTTERMS") as $term) {

          if (!isset($group->paymentterms[$term])) continue;

          $value = "";
          if (isset($group->paymentterms[$term]->current)) {
            $value = $group->paymentterms[$term]->current->increase;
          }
          $input = new Number("", "increase_" . $group->groupId . "_" . $term, $value);
          $input->addAtribute("step", "0.01");
          $input->addClass("increase");
          $form->addElement($input);
        }
      }

      if (isset($_POST["go"]) || isset($_POST["go_list"])) {

        $form->setElementsValue($_POST);

        foreach ($groups as $group) {

          foreach (Config::get("RDE_PAYMENTTERMS") as $term) {

            if (!isset($group->paymentterms[$term])) continue; //customer_code bestaat niet
            $customer_code = $group->paymentterms[$term];

            $value = $form->getElement("increase_" . $group->groupId . "_" . $term)->getValue();
            if ($value == "") {
              $value = 0;
            }

            /** @var CustomerPaytermincreases $current */
            $current = false;
            if (isset($group->paymentterms[$term]) && isset($group->paymentterms[$term]->current)) {
              $current = $group->paymentterms[$term]->current;
            }


            if ($current) {
              $increase_in_this_year = CustomerPaytermincreases::find_by(["codeId" => $current->codeId, "validFrom" => $validFrom]);
              if ($increase_in_this_year) {
                //dit is een bestaande prijs welke word aangepast.
                $increase_in_this_year->increase = $value;
                $increase_in_this_year->save();
                continue;
              }


              //voorgaande jaar afsluiten, zonder aanpassing
              $current->validTo = date("Y-m-d", strtotime($_GET["yearfrom"] . "-12-31 -1 YEAR"));
              $current->save();

            }

            //nieuwe prijs aanpassen/aanmaken
            $cpi_new = CustomerPaytermincreases::find_by(["codeId" => $customer_code->codeId, "validFrom" => $validFrom, "validTo" => "9999-12-31"]);
            if (!$cpi_new) {
              //kopier data in nieuwe
              $cpi_new = new CustomerPaytermincreases();
              $cpi_new->codeId = $customer_code->codeId;
            }
            $cpi_new->increase = $value;
            $cpi_new->validFrom = $validFrom;
            $cpi_new->validTo = "9999-12-31";

            //controleer of er al een item is volgend jaar
            $cpi_future = CustomerPaytermincreases::find_by(["codeId" => $customer_code->codeId, "validFrom" => date("Y-m-d", strtotime($validFrom . " +1 YEAR")), "validTo" => "9999-12-31"]);
            if ($cpi_future) {
              //er is er 1, starten vanaf 1 januari volgend jaar
              $cpi_future->validFrom = date("Y-m-d", strtotime($validFrom . " +1 YEAR"));
              $cpi_future->save();
              //deze stoppen op 31 december dit jaar
              $cpi_new->validTo = $_GET["yearfrom"] . "-12-31";
            }

            $cpi_new->save();

          }
        }

        MessageFlashCoordinator::addMessage("Betalingstermijn opslag opgeslagen");
        if (isset($_POST["go_list"])) {
          ResponseHelper::redirect(PageMap::getUrl('M_PAYMENTTERMS'));
        }
        else {
          ResponseHelper::redirect(reconstructQuery());
        }

      }

      $this->groups = $groups;
      $this->form = $form;
    }


    public function executeWindowsillpricelist() {

      if (isset($_POST['reset'])) {
        unset($_SESSION['sp_search'], $_SESSION['sp_brand'], $_SESSION['sp_type'], $_SESSION['sp_endstone'], $_SESSION['sp_display'], $_SESSION['sp_colors'], $_SESSION['sp_years']);
        ResponseHelper::redirect(reconstructQuery());
      }

      if (isset($_POST["add"])) {
        $add = StonePrices::find_by(["stoneId" => $_POST["missingsize"], "validFrom" => date("Y-01-01")]);
        if (!$add) {
          $add = new StonePrices();
          $add->stoneId = $_POST["missingsize"];
          $add->validFrom = date("Y-01-01");
          $add->validTo = "9999-12-31";
          $add->price = 999.99;
          $add->save();
        }
        MessageFlashCoordinator::addMessage("Steen prijs toegevoegd.");
        //redirect(reconstructQueryAdd()."action=mitrefactoredit&yearfrom=".date("Y")."&edit=Bewerk+factorlijst");
        ResponseHelper::redirect(reconstructQuery());
      }

      $_SESSION['sp_type'] = Stones::TYPE_VENSTERBANK;
      $_SESSION['sp_endstone'] = '';
      $_SESSION['sp_colors'] = [];

      if (!isset($_SESSION['sp_search'])) $_SESSION['sp_search'] = '';
      if (!isset($_SESSION['sp_brand'])) $_SESSION['sp_brand'] = '';
      if (!isset($_SESSION['sp_display'])) $_SESSION['sp_display'] = 'true';
      if (!isset($_SESSION['sp_year'])) $_SESSION['sp_year'] = "now";

      if (isset($_POST['go'])) {
        $_SESSION['sp_search'] = trim($_POST['sp_search']);
        $_SESSION['sp_brand'] = trim($_POST['sp_brand']);
        $_SESSION['sp_display'] = trim($_POST['sp_display']);
        $_SESSION['sp_year'] = trim($_POST['sp_year']);
        ResponseHelper::redirect(reconstructQuery());
      }

      $stones = $this->getStonesVensterbanken(isset($_POST['edit']) || isset($_POST['editbatch']));

      if ($_SESSION['sp_year'] == "now" && $_SESSION['sp_brand'] != "") {

        $query = "SELECT * FROM " . Stones::getTablename() . " ";
        $query .= "LEFT JOIN " . StonePrices::getTablename() . " ON stone_prices.stoneId=stones.stoneId ";
        $query .= " WHERE stone_prices.id IS NULL ";
        $query .= " AND brandId='" . $_SESSION['sp_brand'] . "' ";
        $query .= " GROUP BY stones.stoneId ";
        $query .= " ORDER BY name ";
        $sizes_now = [];
        $result = DBConn::db_link()->query($query);
        while ($row = $result->fetch_row()) {
          $size = new Stones();
          $size->hydrate($row);
          $size->from_db = true;
          $sizes_now[] = $size;
        }
        $this->missing_sizes = $sizes_now;
      }

      $colors = [];
      if ($_SESSION['sp_brand'] != "") {
        $colors = StoneColors::getColors($_SESSION['sp_brand']);
      }
      $this->colors = $colors;
      $this->brands = AppModel::mapObjectIds(StoneBrands::find_all_by(["brandId" => StoneBrands::getBrandidsWithVensterbanken()], "ORDER BY displayOrder"), "brandId");
      if ($_SESSION['sp_brand'] != "" && !isset($this->brands[$_SESSION['sp_brand']])) {
        unset($_SESSION['sp_brand']);
        ResponseHelper::redirect(reconstructQueryAdd());
      }
      $this->items = $stones;

    }

    public function executeWindowsillpriceedit() {
      $stones = $this->getStonesVensterbanken(true);

      $validFrom = $_GET["yearfrom"] . "-01-01";
      $colors = [];
      if (isset($_SESSION['sp_colors']) && count($_SESSION['sp_colors']) >= 2) {
        $colors = StoneColors::find_all_by(["colorId" => $_SESSION['sp_colors']]);
      }

      $form = new Form();
      $tel = 2;
      foreach ($stones as $stone) {
        $input = new Text("", "buyprice_" . $stone->stoneId);
        $input->addClass("buyprice");
        $input->addAtribute("tabindex", $tel);
        $form->addElement($input);

        $factor = new Text("Factor", "factor_" . $stone->stoneId, $stone->getPricefactor());
        $factor->addClass("factor");
        $form->addElement($factor);

        $input_price = new Text("", "price_" . $stone->stoneId);
        $input_price->addClass("price");
        $form->addElement($input_price);

        $input_price_m2 = new Text("", "price_m2_" . $stone->stoneId);
        $input_price_m2->addClass("price");
        $form->addElement($input_price_m2);

        $input_price_piece = new Text("", "price_piece_" . $stone->stoneId);
        $input_price_piece->addClass("price");
        $form->addElement($input_price_piece);

        $input_price_mitre = new Text("", "price_mitre_" . $stone->stoneId);
        $input_price_mitre->addClass("price");
        $form->addElement($input_price_mitre);

        $price_in_this_year = StonePrices::find_by(["stoneId" => $stone->stoneId, "validFrom" => $validFrom]);
        if ($price_in_this_year) {
          $stone->updateexisting = $price_in_this_year;
          $input_price->setValue(number_format($price_in_this_year->price, 2));
          $input_price_m2->setValue(number_format($price_in_this_year->price_m2, 2));
          $input_price_piece->setValue(number_format($price_in_this_year->price_piece, 2));
          $input_price_mitre->setValue(number_format($price_in_this_year->price_mitre, 2));
        }

        $tel++;
      }

      if (isset($_POST["go"]) || isset($_POST["go_list"])) {

        $form->setElementsValue($_POST);

        foreach ($stones as $stone) {
          $price = $form->getElement("price_" . $stone->stoneId)->getValue();
          $price_m2 = $form->getElement("price_m2_" . $stone->stoneId)->getValue();
          $price_piece = $form->getElement("price_piece_" . $stone->stoneId)->getValue();
          $price_mitre = $form->getElement("price_mitre_" . $stone->stoneId)->getValue();
          if (!empty($price) && is_numeric($price) && ($price_m2 == "" || $price_piece == "" || $price_mitre == "")) {
            $form->addError("Als je een prijs van een product aanpast dien je alle prijzen in te vullen. Of gebruik even de kopieer knop om de huidige prijs in te vullen.", $form->getElement("price_" . $stone->stoneId));
          }
        }

        if ($form->isValid()) { //normally like this

          foreach ($stones as $stone) {
            $price = $form->getElement("price_" . $stone->stoneId)->getValue();
            $price_m2 = $form->getElement("price_m2_" . $stone->stoneId)->getValue();
            $price_piece = $form->getElement("price_piece_" . $stone->stoneId)->getValue();
            $price_mitre = $form->getElement("price_mitre_" . $stone->stoneId)->getValue();

            if (!empty($price) && (is_numeric($price) || !empty($price_m2) || !empty($price_piece) || !empty($price_mitre))) {
              if (isset($stone->updateexisting)) {
                //dit is een bestaande prijs welke word aangepast.
                $stone->updateexisting->price = $price;
                $stone->updateexisting->price_m2 = $price_m2;
                $stone->updateexisting->price_piece = $price_piece;
                $stone->updateexisting->price_mitre = $price_mitre;
                $stone->updateexisting->save();
                continue;
              }

              //huidige prijs validTo zetten
              $price_current = $stone->price;
              if ($price_current) {
                $price_current->validTo = date("Y-m-d", strtotime($_GET["yearfrom"] . "-12-31 -1 YEAR"));
                $price_current->save();
              }

              //nieuwe prijs aanpassen/aanmaken
              $price_new = StonePrices::find_by(["stoneId" => $stone->stoneId, "validFrom" => $validFrom, "validTo" => "9999-12-31"]);
              if (!$price_new) {
                $price_new = new StonePrices();
                $price_new->stoneId = $stone->stoneId;
                $price_new->validFrom = $validFrom;
                $price_new->validTo = "9999-12-31";
              }
              $price_new->price = $price;
              $price_new->price_m2 = $price_m2;
              $price_new->price_piece = $price_piece;
              $price_new->price_mitre = $price_mitre;
              $price_new->save();
            }
            elseif (empty($price) && isset($stone->updateexisting)) {

              //er is een prijs in dit jaar, maar deze is leeg gemaakt. Verwijder database prijs.
              $stone->updateexisting->destroy();

              //laatste prijs einddatum tot oneindig zetten
              $price_last = StonePrices::find_by(["stoneId" => $stone->stoneId, "validTo" => date("Y-m-d", strtotime($_GET["yearfrom"] . "-12-31 -1 YEAR"))]);
              if ($price_last) {
                $price_last->validTo = "9999-12-31";
                $price_last->save();
              }
            }
          }

          MessageFlashCoordinator::addMessage("Prijzen opgeslagen");
          if (isset($_POST["go_list"])) {
            ResponseHelper::redirect(PageMap::getUrl('M_WINDOWSILLPRICES'));
          }
          else {
            ResponseHelper::redirect(reconstructQuery());
          }
        }

      }

      $this->stones = $stones;
      $this->form = $form;
      $this->brands = AppModel::mapObjectIds(StoneBrands::getBrands(), "brandId");
      $this->colors = $colors;

    }


    private function getStonesVensterbanken($edit = false) {

      $this->pager = new Pager();
      $this->pager->setWriteCount(true);
      $this->pager->handle();

      if ($_SESSION['sp_brand'] != "") {
        $filt = " WHERE 1 ";
        if ($_SESSION['sp_brand'] != "") {
          $filt .= " AND brandId='" . $_SESSION['sp_brand'] . "' ";
        }
        $filt .= " AND type='" . Stones::TYPE_VENSTERBANK . "' ";
        if ($_SESSION['sp_display'] != "") {
          $filt .= " AND display='" . $_SESSION['sp_display'] . "' ";
        }
        if ($_SESSION['sp_year'] == "now") {
          $filt .= " AND validFrom<='" . date("Y-m-d") . "' AND validTo>'" . date("Y-m-d") . "' ";
        }
        elseif ($_SESSION['sp_year'] != "") {
          $filt .= " AND YEAR(validFrom)='" . $_SESSION['sp_year'] . "' ";
        }
        if (count($_SESSION['sp_colors']) > 0) {
          $filt .= " AND colorId IN (" . implode(",", $_SESSION['sp_colors']) . ") ";
        }
        if ($_SESSION['sp_search'] != "") {
          $searchval = escapeForDB($_SESSION['sp_search']);
          $filt .= " AND (";
          $filt .= " name LIKE '%" . $searchval . "%' ";
          $filt .= ")";
        }

      }
      else {
        $filt = "WHERE 0 ";
      }

      $query = "SELECT * FROM " . Stones::getTablename() . " ";
      $query .= "JOIN " . StonePrices::getTablename() . " ON stone_prices.stoneId=stones.stoneId ";
      $query .= $filt;

      if (!$edit) {
        $result = DBConn::db_link()->query($query);
        $this->pager->count = $result->num_rows;
        if (!$this->pager->count) $this->pager->count = 0;
      }
      $query .= "ORDER BY name";
      if (!$edit) {
        $query .= $this->pager->getLimitQuery();
      }

      $result = DBConn::db_link()->query($query);
      $stones = [];
      while ($row = $result->fetch_row()) {
        $stone = new Stones();
        $stone->hydrate($row);
        $stone->from_db = true;
        $stone->price = new StonePrices();
        $stone->price->hydrate($row, count(Stones::columns));
        $stone->price->from_db = true;
        $stones[] = $stone;
      }
      return $stones;
    }


//    public function executeBretistonepricelist() {
//
//      if (isset($_POST['reset'])) {
//        unset($_SESSION['bsp_search'], $_SESSION['bsp_display'], $_SESSION['bsp_years']);
//        ResponseHelper::redirect(reconstructQuery());
//      }
//
//      if (!isset($_SESSION['bsp_search'])) $_SESSION['bsp_search'] = '';
//      if (!isset($_SESSION['bsp_display'])) $_SESSION['bsp_display'] = 'true';
//      if (!isset($_SESSION['bsp_year'])) $_SESSION['bsp_year'] = "now";
//
//      if (isset($_POST['go'])) {
//        $_SESSION['sp_search'] = trim($_POST['sp_search']);
//        $_SESSION['sp_display'] = trim($_POST['sp_display']);
//        $_SESSION['sp_year'] = trim($_POST['sp_year']);
//        ResponseHelper::redirect(reconstructQuery());
//      }
//
//      $stones = $this->getBretiStones(isset($_POST['edit']) || isset($_POST['editbatch']));
//
//      if ($_SESSION['bsp_year'] == "now") {
//
//        $query = "SELECT * FROM " . Stones::getTablename() . " ";
//        $query .= "LEFT JOIN " . StonePrices::getTablename() . " ON stone_prices.stoneId=stones.stoneId ";
//        $query .= " WHERE stone_prices.id IS NULL ";
//        $query .= " AND brandId='" . $_SESSION['sp_brand'] . "' ";
//        $query .= " GROUP BY stones.stoneId ";
//        $query .= " ORDER BY name ";
//        $sizes_now = [];
//        $result = DBConn::db_link()->query($query);
//        while ($row = $result->fetch_row()) {
//          $size = new Stones();
//          $size->hydrate($row);
//          $size->from_db = true;
//          $sizes_now[] = $size;
//        }
//        $this->missing_sizes = $sizes_now;
//      }
//
//      $colors = [];
//      if ($_SESSION['sp_brand'] != "") {
//        $colors = StoneColors::getColors($_SESSION['sp_brand']);
//      }
//      $this->colors = $colors;
//      $this->brands = AppModel::mapObjectIds(StoneBrands::find_all("WHERE NOT brandId IN (" . implode(",", StoneBrands::getBrandidsWithVensterbanken()) . ") ORDER BY displayOrder"), "brandId");
//      if ($_SESSION['sp_brand'] != "" && !isset($this->brands[$_SESSION['sp_brand']])) {
//        unset($_SESSION['sp_brand']);
//        ResponseHelper::redirect(reconstructQueryAdd());
//      }
//      $this->items = $stones;
//
//    }


    private function getBretiStones($edit = false) {

      $this->pager = new Pager();
      $this->pager->setWriteCount(true);
      $this->pager->handle();

      if ($_SESSION['sp_brand'] != "") {
        $filt = " WHERE brandId=6 "; //belgisch arduin
        $filt .= " AND colorId=40 "; //licht gezoekt - geschuurd
        if ($_SESSION['bsp_display'] != "") {
          $filt .= " AND display='" . $_SESSION['bsp_display'] . "' ";
        }
        if ($_SESSION['bsp_year'] == "now") {
          $filt .= " AND validFrom<='" . date("Y-m-d") . "' AND validTo>'" . date("Y-m-d") . "' ";
        }
        elseif ($_SESSION['bsp_year'] != "") {
          $filt .= " AND YEAR(validFrom)='" . $_SESSION['sp_year'] . "' ";
        }
        if ($_SESSION['bsp_search'] != "") {
          $searchval = escapeForDB($_SESSION['sp_search']);
          $filt .= " AND (";
          $filt .= " name LIKE '%" . $searchval . "%' ";
          $filt .= ")";
        }

      }
      else {
        $filt = "WHERE 0 ";
      }

      $query = "SELECT * FROM " . Stones::getTablename() . " ";
      $query .= "JOIN " . StonePrices::getTablename() . " ON stone_prices.stoneId=stones.stoneId ";
      $query .= $filt;

      if (!$edit) {
        $result = DBConn::db_link()->query($query);
        $this->pager->count = $result->num_rows;
        if (!$this->pager->count) $this->pager->count = 0;
      }
      $query .= "ORDER BY name";
      if (!$edit) {
        $query .= $this->pager->getLimitQuery();
      }

      $result = DBConn::db_link()->query($query);
      $stones = [];
      while ($row = $result->fetch_row()) {
        $stone = new Stones();
        $stone->hydrate($row);
        $stone->from_db = true;
        $stone->price = new StonePrices();
        $stone->price->hydrate($row, count(Stones::columns));
        $stone->price->from_db = true;
        $stones[] = $stone;
      }
      return $stones;
    }

    public function executeBretistonepriceedit() {
//      dumpe($_SESSION);
//      die("Afblijven met je nieuwsgierige vingertjes bart! Is nog niet af.");
      $stones = $this->getStonesBreti(true);

      $validFrom = $_GET["yearfrom"] . "-01-01";
      $colors = [];
      if (isset($_SESSION['sp_colors']) && count($_SESSION['sp_colors']) >= 2) {
        $colors = StoneColors::find_all_by(["colorId" => $_SESSION['sp_colors']]);
      }

      $form = new Form();
      $tel = 2;
      foreach ($stones as $stone) {
        $input = new Text("", "buyprice_" . $stone->stoneId);
        $input->addClass("buyprice");
        $input->addAtribute("tabindex", $tel);
        $form->addElement($input);

        $factor = new Text("Factor", "factor_" . $stone->stoneId, $stone->getPricefactor());
        $factor->addClass("factor");
        $form->addElement($factor);

        $input = new Text("", "price_" . $stone->stoneId);
        $input->addClass("price");
        $price_in_this_year = StonePrices::find_by(["stoneId" => $stone->stoneId, "validFrom" => $validFrom]);
        if ($price_in_this_year) {
          $stone->updateexisting = $price_in_this_year;
          $input->setValue($price_in_this_year->price);
        }
        $form->addElement($input);

        $tel++;
      }

      if (isset($_POST["go"]) || isset($_POST["go_list"])) {

        $form->setElementsValue($_POST);

        if ($form->isValid()) { //normally like this

          foreach ($stones as $stone) {
            $value = $form->getElement("price_" . $stone->stoneId)->getValue();

            if (!empty($value) && is_numeric($value)) {

              if (isset($stone->updateexisting)) {
                //dit is een bestaande prijs welke word aangepast.
                $stone->updateexisting->price = $value;
                $stone->updateexisting->save();
                continue;
              }

              //huidige prijs validTo zetten
              $price_current = $stone->price;
              if ($price_current) {
                $price_current->validTo = date("Y-m-d", strtotime($_GET["yearfrom"] . "-12-31 -1 YEAR"));
                $price_current->save();
              }

              //nieuwe prijs aanpassen/aanmaken
              $price_new = StonePrices::find_by(["stoneId" => $stone->stoneId, "validFrom" => $validFrom, "validTo" => "9999-12-31"]);
              if (!$price_new) {
                $price_new = new StonePrices();
                $price_new->stoneId = $stone->stoneId;
                $price_new->validFrom = $validFrom;
                $price_new->validTo = "9999-12-31";
              }
              $price_new->price = $value;
              $price_new->save();
            }
            elseif (empty($value) && isset($stone->updateexisting)) {
              //er is een prijs in dit jaar, maar deze is leeg gemaakt. Verwijder database prijs.
              $stone->updateexisting->destroy();

              //laatste prijs einddatum tot oneindig zetten
              $price_last = StonePrices::find_by(["stoneId" => $stone->stoneId, "validTo" => date("Y-m-d", strtotime($_GET["yearfrom"] . "-12-31 -1 YEAR"))]);
              if ($price_last) {
                $price_last->validTo = "9999-12-31";
                $price_last->save();
              }
            }

            if ($stone->colorId != 43 && $stone->colorId != 72 && $stone->colorId != 73 && !empty($value)) {

              $stones_related = Stones::find_all_by(['sizeId' => $stone->sizeId]);


              foreach ($stones_related as $stone_related) {

                if ($stone_related->colorId == 39 || $stone_related->colorId == 40) continue;
//                if (empty($value)) continue;
                $price_new = StonePrices::find_by(["stoneId" => $stone_related->stoneId, "validFrom" => $validFrom, "validTo" => "9999-12-31"]);

                //geborsteld en betonlook
                if ($stone_related->colorId == 44 || $stone_related->colorId == 46 || $stone_related->colorId == 45 || $stone_related->colorId == 47) {
                  $val = $value;
                  if (!$price_new) {
                    $price_new = new StonePrices();
                    $price_new->stoneId = $stone_related->stoneId;
                    $price_new->validFrom = $validFrom;
                    $price_new->validTo = "9999-12-31";
                  }
                  $price_new->price = (float)$val += 5;
                  $price_new->save();
                }
                //donker gezoet
                if ($stone_related->colorId == 41 || $stone_related->colorId == 48) {
                  $val = $value;
                  if (!$price_new) {
                    $price_new = new StonePrices();
                    $price_new->stoneId = $stone_related->stoneId;
                    $price_new->validFrom = $validFrom;
                    $price_new->validTo = "9999-12-31";
                  }
                  $price_new->price = (float)$val += 18.5;
                  $price_new->save();
                }
              }
            }
          }


          MessageFlashCoordinator::addMessage("Prijzen opgeslagen");
          if (isset($_POST["go_list"])) {
            ResponseHelper::redirect(PageMap::getUrl('M_STONEPRICES'));
          }
          else {
            ResponseHelper::redirect(reconstructQuery());
          }
        }

      }

      $this->stones = $stones;
      $this->form = $form;
      $this->brands = AppModel::mapObjectIds(StoneBrands::find_all("WHERE brandId IN (" . implode(",", StoneBrands::getBretiBrandIds()) . ") AND NOT brandId IN (" . implode(",", StoneBrands::getBrandidsWithVensterbanken()) . ") ORDER BY displayOrder"), "brandId");
      $this->colors = $colors;
      $this->template = "stonepriceeditSuccess.php";

    }

    public function executeBretistonepricelistnew() {

      if (isset($_POST['reset'])) {
        unset($_SESSION['bsp_search'], $_SESSION['bsp_brand'], $_SESSION['bsp_type'], $_SESSION['bsp_endstone'], $_SESSION['bsp_display'], $_SESSION['bsp_colors'], $_SESSION['bsp_years']);
        ResponseHelper::redirect(reconstructQuery());
      }

      if (isset($_POST["add"])) {
        $add = StonePrices::find_by(["stoneId" => $_POST["missingsize"], "validFrom" => date("Y-01-01")]);
        if (!$add) {
          $add = new StonePrices();
          $add->stoneId = $_POST["missingsize"];
          $add->validFrom = date("Y-01-01");
          $add->validTo = "9999-12-31";
          $add->price = 999.99;
          $add->save();
        }
        MessageFlashCoordinator::addMessage("Steen prijs toegevoegd.");
        //redirect(reconstructQueryAdd()."action=mitrefactoredit&yearfrom=".date("Y")."&edit=Bewerk+factorlijst");
        ResponseHelper::redirect(reconstructQuery());
      }

      if (!isset($_SESSION['bsp_search'])) $_SESSION['bsp_search'] = '';
      if (!isset($_SESSION['bsp_brand'])) $_SESSION['bsp_brand'] = '';
      if (!isset($_SESSION['bsp_type'])) $_SESSION['bsp_type'] = '';
      if (!isset($_SESSION['bsp_endstone'])) $_SESSION['bsp_endstone'] = '';
      if (!isset($_SESSION['bsp_display'])) $_SESSION['bsp_display'] = 'true';
      if (!isset($_SESSION['bsp_colors'])) $_SESSION['bsp_colors'] = [];
      if (!isset($_SESSION['bsp_year'])) $_SESSION['bsp_year'] = "now";

      if (isset($_POST['go'])) {
        if ($_SESSION['bsp_brand'] != $_POST['bsp_brand']) {
          //brand is reset, reset colors
          $_POST['bsp_colors'] = [];
        }
        $_SESSION['bsp_search'] = trim($_POST['bsp_search']);
        $_SESSION['bsp_brand'] = trim($_POST['bsp_brand']);
        $_SESSION['bsp_type'] = trim($_POST['bsp_type']);
        $_SESSION['bsp_endstone'] = trim($_POST['bsp_endstone']);
        $_SESSION['bsp_display'] = trim($_POST['bsp_display']);
        $_SESSION['bsp_year'] = trim($_POST['bsp_year']);
        if (isset($_POST['bsp_colors'])) {
          $_SESSION['bsp_colors'] = $_POST['bsp_colors'];
        }
        else {
          $_SESSION['bsp_colors'] = [];
        }
        ResponseHelper::redirect(reconstructQuery());
      }

      $stones = $this->getStonesBreti(isset($_POST['edit']) || isset($_POST['editbatch']));

      if ($_SESSION['bsp_year'] == "now" && $_SESSION['bsp_brand'] != "") {

        $query = "SELECT * FROM " . Stones::getTablename() . " ";
        $query .= "LEFT JOIN " . StonePrices::getTablename() . " ON stone_prices.stoneId=stones.stoneId ";
        $query .= " WHERE stone_prices.id IS NULL ";
        $query .= " AND brandId='" . $_SESSION['bsp_brand'] . "' ";
        $query .= " GROUP BY stones.stoneId ";
        $query .= " ORDER BY name ";
        $sizes_now = [];
        $result = DBConn::db_link()->query($query);
        while ($row = $result->fetch_row()) {
          $size = new Stones();
          $size->hydrate($row);
          $size->from_db = true;
          $sizes_now[] = $size;
        }
        $this->missing_sizes = $sizes_now;
      }

      $colors = [];
      if ($_SESSION['bsp_brand'] != "") {
        $colors = StoneColors::getColors($_SESSION['bsp_brand']);
      }
      $this->colors = $colors;
      $this->brands = AppModel::mapObjectIds(StoneBrands::find_all("WHERE brandId IN (" . implode(",", StoneBrands::getBretiBrandIds()) . ") AND NOT brandId IN (" . implode(",", StoneBrands::getBrandidsWithVensterbanken()) . ") ORDER BY displayOrder"), "brandId");
      if ($_SESSION['bsp_brand'] != "" && !isset($this->brands[$_SESSION['bsp_brand']])) {
        unset($_SESSION['bsp_brand']);
        ResponseHelper::redirect(reconstructQueryAdd());
      }
      $this->items = $stones;

    }

    private function getStonesBreti($edit = false) {

      $this->pager = new Pager();
      $this->pager->setWriteCount(true);
      $this->pager->handle();
      $usable_color_ids = [39, 40, 43, 51, 68, 54, 55];
      $colors = StoneColors::getColors(StoneBrands::getBretiBrandIds());
      $color_ids = [];
      foreach ($colors as $color) {
        if (!in_array($color->colorId, $usable_color_ids)) continue;
        $color_ids[] = $color->colorId;
      }

      $filt = ' ';
      if ($_SESSION['bsp_brand'] != "") {
        if ($_SESSION['bsp_brand'] != "") {
          $filt .= " AND brandId='" . $_SESSION['bsp_brand'] . "' ";
        }
        $filt .= " AND type!='" . Stones::TYPE_VENSTERBANK . "' ";
        if ($_SESSION['bsp_type'] != "") {
          $filt .= " AND type='" . $_SESSION['bsp_type'] . "' ";
        }
        if ($_SESSION['bsp_endstone'] != "") {
          $filt .= " AND endstone='" . $_SESSION['bsp_endstone'] . "' ";
        }
        if ($_SESSION['bsp_display'] != "") {
          $filt .= " AND display='" . $_SESSION['bsp_display'] . "' ";
        }
        if ($_SESSION['bsp_year'] == "now") {
          $filt .= " AND validFrom<='" . date("Y-m-d") . "' AND validTo>'" . date("Y-m-d") . "' ";
        }
        elseif ($_SESSION['bsp_year'] != "") {
          $filt .= " AND YEAR(validFrom)='" . $_SESSION['bsp_year'] . "' ";
        }
        if ($_SESSION['bsp_search'] != "") {
          $searchval = escapeForDB($_SESSION['bsp_search']);
          $filt .= " AND (";
          $filt .= " name LIKE '%" . $searchval . "%' ";
          $filt .= ")";
        }

      }

      $query = "SELECT * FROM " . Stones::getTablename() . " ";
      $query .= "JOIN " . StonePrices::getTablename() . " ON stone_prices.stoneId=stones.stoneId ";
      $query .= "WHERE 1 ";
      $query .= "AND brandId IN (" . implode(",", StoneBrands::getBretiBrandIds()) . ") ";
      $query .= " AND colorId IN (" . implode(",", $color_ids) . ") ";
      $query .= $filt;

      if (!$edit) {
        $result = DBConn::db_link()->query($query);
        $this->pager->count = $result->num_rows;
        if (!$this->pager->count) $this->pager->count = 0;
      }
      $query .= "ORDER BY name";
      if (!$edit) {
        $query .= $this->pager->getLimitQuery();
      }

      $result = DBConn::db_link()->query($query);
      $stones = [];
      while ($row = $result->fetch_row()) {
        $stone = new Stones();
        $stone->hydrate($row);
        $stone->from_db = true;
        $stone->price = new StonePrices();
        $stone->price->hydrate($row, count(Stones::columns));
        $stone->price->from_db = true;
        $stones[] = $stone;
      }
      return $stones;
    }

    public function executeBretistonepriceeditnew() {
      if (isset($_GET["editbatch"])) {
        $this->stonepriceeditbatch();
      }
      else {
        $this->bretistonepriceedit();
      }
    }

    private function bretistonepriceedit() {
      $stones = $this->getStonesBreti(true);

      $validFrom = $_GET["yearfrom"] . "-01-01";
      $colors = [];
      if (isset($_SESSION['sp_colors']) && count($_SESSION['sp_colors']) >= 2) {
        $colors = StoneColors::find_all_by(["colorId" => $_SESSION['sp_colors']]);
      }

      $form = new Form();
      $tel = 2;
      foreach ($stones as $stone) {
        $stone_geborsteld = '';
        $stone_beton = '';
        $stone_donker = '';

        $input = new Text("", "nostone_" . $stone->stoneId);
        $input->addClass("baseprice");
        $input->addAtribute("id", "nostone_" . $stone->stoneId);
        $input->addAtribute("tabindex", $tel);
        $input->setValue('NA');
        $input->setReadonly(true);
        $form->addElement($input);

        if ($stone->colorId !== '43') { //Als stone GEEN kleur zimbabwe heeft

          $stones_related = Stones::getStonesBySize($stone->sizeId);

          $stone->has_stone_geborsteld = false;
          $stone->has_stone_beton = false;
          $stone->has_stone_donker = false;

          foreach ($stones_related as $stone_related) {
            if ($stone_related->colorId == 46 || $stone_related->colorId == 44) {
              $stone_geborsteld = $stone_related;
              $stone->has_stone_geborsteld = true;
            }
            elseif ($stone_related->colorId == 45 || $stone_related->colorId == 47) {
              $stone_beton = $stone_related;
              $stone->has_stone_beton = true;
            }
            elseif ($stone_related->colorId == 41 || $stone_related->colorId == 66) {
              $stone_donker = $stone_related;
              $stone->has_stone_donker = true;
            }
          }

          $input = new Text("", "baseprice_" . $stone->stoneId);
          $input->addClass("baseprice");
          $input->addAtribute("id", "baseprice_" . $stone->stoneId);
          $input->addAtribute("tabindex", $tel);
          $price_in_this_year = StonePrices::find_by(["stoneId" => $stone->stoneId, "validFrom" => $validFrom]);
          if ($price_in_this_year) {
            $stone->updateexisting = $price_in_this_year;
            $input->setValue($price_in_this_year->price);
          }
          $form->addElement($input);

          $input = new Text("", "lichtgezoet_" . $stone->stoneId);
          $input->addClass("lichtgezoet");
          $input->addAtribute("id", "lichtgezoet_" . $stone->stoneId);
          $input->addAtribute("tabindex", -1);
          $input->setReadonly(true);
          if ($stone_geborsteld) {
            $price_in_this_year_geborsteld = StonePrices::find_by(["stoneId" => $stone_geborsteld->stoneId, "validFrom" => $validFrom]);
            if ($price_in_this_year_geborsteld) {
              $stone->updateexisting_geborsteld = $price_in_this_year_geborsteld;
              $input->setValue($price_in_this_year_geborsteld->price);
            }
            elseif ($price_in_this_year) {
              $base_price = $price_in_this_year->price;
              $input->setValue($base_price + 5);
            }
          }
          $form->addElement($input);

          $input = new Text("", "betonlook_" . $stone->stoneId);
          $input->addClass("betonlook");
          $input->addAtribute("name", "betonlook");
          $input->addAtribute("id", "betonlook_" . $stone->stoneId);
          $input->addAtribute("tabindex", -1);
          $input->setReadonly(true);
          if ($stone_beton) {
            $price_in_this_year_beton = StonePrices::find_by(["stoneId" => $stone_beton->stoneId, "validFrom" => $validFrom]);
            if ($price_in_this_year_beton) {
              $stone->updateexisting_beton = $price_in_this_year_beton;
              $input->setValue($price_in_this_year_beton->price);
            }
            elseif ($price_in_this_year) {
              $base_price = $price_in_this_year->price;
              $input->setValue($base_price + 5);
            }
          }
          $form->addElement($input);

          $input = new Text("", "donkergezoet_" . $stone->stoneId);
          $input->addClass("donkergezoet");
          $input->addAtribute("id", "donkergezoet_" . $stone->stoneId);
          $input->addAtribute("tabindex", -1);
          $input->setReadonly(true);
          if ($stone_donker) {
            $price_in_this_year_donker = StonePrices::find_by(["stoneId" => $stone_donker->stoneId, "validFrom" => $validFrom]);
            if ($price_in_this_year_donker) {
              $stone->updateexisting_donker = $price_in_this_year_donker;
              $input->setValue($price_in_this_year_donker->price);
            }
            elseif ($price_in_this_year) {
              $base_price = $price_in_this_year->price;
              $input->setValue($base_price + 18.50);
            }
          }
          $form->addElement($input);
        }
        else {
          $input = new Text("", "zimbabwe_" . $stone->stoneId);
          $input->addClass("zimbabwe");
          $input->addAtribute("id", "zimbabwe_" . $stone->stoneId);
          $input->addAtribute("tabindex", $tel);
          $price_in_this_year = StonePrices::find_by(["stoneId" => $stone->stoneId, "validFrom" => $validFrom]);
          if ($price_in_this_year) {
            $stone->updateexisting = $price_in_this_year;
            $input->setValue($price_in_this_year->price);
          }
          $form->addElement($input);
        }


        $input = new Text("", "price_" . $stone->stoneId);
        $input->addClass("price");
        $price_in_this_year = StonePrices::find_by(["stoneId" => $stone->stoneId, "validFrom" => $validFrom]);
        if ($price_in_this_year) {
          $stone->updateexisting = $price_in_this_year;
          $input->setValue($price_in_this_year->price);
        }
        $form->addElement($input);

        $tel++;
      }

      if (isset($_POST["go"]) || isset($_POST["go_list"])) {

        $form->setElementsValue($_POST);
        if ($form->isValid()) { //normally like this

          foreach ($stones as $stone) {
            if ($stone->colorId == '43') {
              $value_base = $form->getElement("zimbabwe_" . $stone->stoneId)->getValue();
            }
            else {
              $value_base = $form->getElement("baseprice_" . $stone->stoneId)->getValue();
            }

            $stone_price_before = StonePrices::find_by(['stoneId' => $stone->stoneId, "validFrom" => $validFrom]);
            if ($stone_price_before) {
              if ($stone_price_before->price == $form->getElement("baseprice_" . $stone->stoneId)->getValue()) continue; // Als prijs niet is veranderd, sla steen over
            }

            if (empty($value_base)) continue;

            $base_price = (float)$value_base;
            $value_lichtgezoet = (float)$base_price += 5;
            $base_price = $value_base;
            $value_betonlook = (float)$base_price += 5;
            $base_price = $value_base;
            $value_donkergezoet = (float)$base_price += 18.50;

            if (!empty($value_base) && is_numeric($value_base)) {

              if (isset($stone->updateexisting) || isset($stone->updateexisting_geborsteld) || isset($stone->updateexisting_donker) || isset($stone->updateexisting_beton)) {
                //dit is een bestaande prijs welke word aangepast.
                if (isset($stone->updateexisting)) {
                  $stone->updateexisting->price = $value_base;
                  $stone->updateexisting->validTo = "9999-12-31";
                  $stone->updateexisting->save();
                }
                if (isset($stone->updateexisting_geborsteld)) {
                  $stone->updateexisting_geborsteld->price = $value_lichtgezoet;
                  $stone->updateexisting_geborsteld->validTo = "9999-12-31";
                  $stone->updateexisting_geborsteld->save();
                }
                if (isset($stone->updateexisting_beton)) {
                  $stone->updateexisting_beton->price = $value_betonlook;
                  $stone->updateexisting_beton->validTo = "9999-12-31";
                  $stone->updateexisting_beton->save();
                }
                if (isset($stone->updateexisting_donker)) {
                  $stone->updateexisting_donker->price = (float)$base_price += 18.50;
                  $stone->updateexisting_donker->validTo = "9999-12-31";
                  $stone->updateexisting_donker->save();
                }
              }

              //huidige prijs validTo zetten
              $price_current = StonePrices::find_by(['stoneId' => $stone->stoneId, 'validFrom' => date("Y-m-d", strtotime($_GET["yearfrom"] . "-01-01 -1 YEAR"))]);
              if ($price_current) {
                $price_current->validTo = date("Y-m-d", strtotime($_GET["yearfrom"] . "-12-31 -1 YEAR"));
                $price_current->save();
              }

              //nieuwe prijs aanpassen/aanmaken
              $price_new = StonePrices::find_by(["stoneId" => $stone->stoneId, "validFrom" => $validFrom, "validTo" => "9999-12-31"]);
              if (!$price_new) {
                $price_new = new StonePrices();
                $price_new->stoneId = $stone->stoneId;
                $price_new->validFrom = $validFrom;
                $price_new->validTo = "9999-12-31";
              }
              $price_new->price = $value_base;
              $price_new->save();


              $stones_related = Stones::getStonesBySize($stone->sizeId);

              foreach ($stones_related as $stone_related) {
                if ($stone_related->colorId == 46 || $stone_related->colorId == 44) {
                  $stone_geborsteld = $stone_related;
                }
                elseif ($stone_related->colorId == 45 || $stone_related->colorId == 47) {
                  $stone_beton = $stone_related;
                }
                elseif ($stone_related->colorId == 41 || $stone_related->colorId == 66) {
                  $stone_donker = $stone_related;
                }
              }

              //geborsteld en betonlook
              if ($stone_geborsteld) {

                $price_current_geborsteld = StonePrices::find_by(['stoneId' => $stone_geborsteld->stoneId, 'validFrom' => date("Y-m-d", strtotime($_GET["yearfrom"] . "-01-01 -1 YEAR"))]);
                if ($price_current_geborsteld) {
                  $price_current_geborsteld->validTo = date("Y-m-d", strtotime($_GET["yearfrom"] . "-12-31 -1 YEAR"));
                  $price_current_geborsteld->save();
                }

                $geborsteld_price = StonePrices::find_by(["stoneId" => $stone_geborsteld->stoneId, "validFrom" => $validFrom, "validTo" => "9999-12-31"]);
                if (!$geborsteld_price) {
                  $geborsteld_price = new StonePrices();
                  $geborsteld_price->stoneId = $stone_geborsteld->stoneId;
                  $geborsteld_price->validFrom = $validFrom;
                  $geborsteld_price->validTo = "9999-12-31";
                }
                $geborsteld_price->price = $value_lichtgezoet;
                $geborsteld_price->save();
              }

              if ($stone_beton) {

                $price_current_beton = StonePrices::find_by(['stoneId' => $stone_beton->stoneId, 'validFrom' => date("Y-m-d", strtotime($_GET["yearfrom"] . "-01-01 -1 YEAR"))]);
                if ($price_current_beton) {
                  $price_current_beton->validTo = date("Y-m-d", strtotime($_GET["yearfrom"] . "-12-31 -1 YEAR"));
                  $price_current_beton->save();
                }

                $betonlook_price = StonePrices::find_by(["stoneId" => $stone_beton->stoneId, "validFrom" => $validFrom, "validTo" => "9999-12-31"]);
                if (!$betonlook_price) {
                  $betonlook_price = new StonePrices();
                  $betonlook_price->stoneId = $stone_beton->stoneId;
                  $betonlook_price->validFrom = $validFrom;
                  $betonlook_price->validTo = "9999-12-31";
                }
                $betonlook_price->price = $value_betonlook;
                $betonlook_price->save();
              }

              //donker gezoet
              if ($stone_donker) {

                $price_current_donker = StonePrices::find_by(['stoneId' => $stone_donker->stoneId, 'validFrom' => date("Y-m-d", strtotime($_GET["yearfrom"] . "-01-01 -1 YEAR"))]);
                if ($price_current_donker) {
                  $price_current_donker->validTo = date("Y-m-d", strtotime($_GET["yearfrom"] . "-12-31 -1 YEAR"));
                  $price_current_donker->save();
                }

                $donkergezoet_price = StonePrices::find_by(["stoneId" => $stone_donker->stoneId, "validFrom" => $validFrom, "validTo" => "9999-12-31"]);
                if (!$donkergezoet_price) {
                  $donkergezoet_price = new StonePrices();
                  $donkergezoet_price->stoneId = $stone_donker->stoneId;
                  $donkergezoet_price->validFrom = $validFrom;
                  $donkergezoet_price->validTo = "9999-12-31";
                }
                $donkergezoet_price->price = $value_donkergezoet;
                $donkergezoet_price->save();
              }

            }
            elseif (empty($value_base) && isset($stone->updateexisting)) {
              //er is een prijs in dit jaar, maar deze is leeg gemaakt. Verwijder database prijs.
              $stone->updateexisting->destroy();

              //laatste prijs einddatum tot oneindig zetten
              $price_last = StonePrices::find_by(["stoneId" => $stone->stoneId, "validTo" => date("Y-m-d", strtotime($_GET["yearfrom"] . "-12-31 -1 YEAR"))]);
              if ($price_last) {
                $price_last->validTo = "9999-12-31";
                $price_last->save();
              }
            }
          }

          MessageFlashCoordinator::addMessage("Prijzen opgeslagen");
          if (isset($_POST["go_list"])) {
            ResponseHelper::redirect(PageMap::getUrl('M_STONEPRICES_BRETI_NEW'));
          }
          ResponseHelper::redirect(reconstructQuery());
        }

      }

      $this->stones = $stones;
      $this->form = $form;
      $this->brands = AppModel::mapObjectIds(StoneBrands::getBrands(), "brandId");
      $this->colors = $colors;
      $this->template = "bretistonepriceeditSuccess.php";

    }


  }

