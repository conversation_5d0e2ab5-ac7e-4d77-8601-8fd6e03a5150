<?php

  use domain\multivers\service\InvoiceSave;
  use domain\reviews\service\PrepareReviews;
  use domain\stones\service\StoneSpeed;
  use gsdfw\domain\mail\service\WrapperService;
  use gsdfw\domain\mail\service\WrapperServiceCT;
  use gsdfw\domain\multivers\service\MultiversApi;

  require_once(DIR_CLASSES . 'Mails.php');

  class MailsFactory extends Mails {

    /**
     * Producten zijn geleverd email
     * @param $tos
     * @param $cargo_receipt_id
     * @param $quotationIds
     */
    public static function sendDeliveredEmail($tos, $cargo_receipt_id, $quotationIds) {
      if (count($tos) == 0) return;

      $subject = "Producten geleverd";
      $message = "Beste,<br/><br/>";
      $message .= "Uw producten zijn geleverd.<br/><br/>";
      $message .= "In de bijlage vind u uw vrachtbon en additionele informatie.<br/><br/>";
      $message .= "Met vriendelijke groet,<br/><br/>";
      $message .= "Raamdorpelelementen BV<br/><br/>";
      $message .= "Raambrug 9<br/>";
      $message .= "5531 AG Bladel, Nederland<br/>";
      $message .= "Tel.  0497-36.07.91<br/>";
      $message .= "Fax. 0497-38.09.71<br/>";
      $message .= "Email: <EMAIL><br/>";
      $message .= "Website: www.raamdorpel.nl<br/>";


      $files = [];

      $files[] = CargoReceipt::getPdf($cargo_receipt_id);

      $cargo_receipt = CargoReceipt::find_by(["cargoReceiptId" => $cargo_receipt_id]);
      if (count($quotationIds) > 0 && ($cargo_receipt->cargoReceiptType == 'standard' || $cargo_receipt->cargoReceiptType == 'factorypickup')) { //niet toevoegen bij pickup route
        $productionPdf = CargoReceipt::getProductionstaatPdf($quotationIds);
        if(file_exists($productionPdf)) {
          $files[] = $productionPdf;
        }
        else {
          logToFile("mail-error", "Productionstaat niet gevonden: " . $productionPdf);
        }
      }

      $cc_mails = [];
      $quotation = Quotations::find_by(['quotationId' => reset($quotationIds)]);
      $company = CrmCompanies::find_by(['companyId' => $quotation->companyId]);
      if ($cargo_receipt) {
        if ($company) {
          if (!empty($company->cargo_receipt_mail_1)) {
            $cc_mails[] = $company->cargo_receipt_mail_1;
          }
          if (!empty($company->cargo_receipt_mail_2)) {
            $cc_mails[] = $company->cargo_receipt_mail_2;
          }
          if (!empty($company->cargo_receipt_mail_3)) {
            $cc_mails[] = $company->cargo_receipt_mail_3;
          }
        }
      }

      $address = CrmAddresses::find_by(['addressId' => $company->visitAddressId]);

      $message .= "<br/>Adresgegevens:<br/>";
      $message .= "Straat: " . $address->street . "<br/>";
      $message .= "Nummer: " . $address->nr . ' ' . $address->extension . "<br/>";
      $message .= "Postcode: " . $address->zipcode . "<br/>";
      $message .= "Stad: " . $address->domestic . "<br/>";
      $message .= "Land: " . $address->country . "<br/>";
      $message .= "Bedrijfsnaam: " . $company->name . "<br/>";

      //quotationNumbers tonen
      $message .= "<br/>Offerte nummers:<br/>";
      foreach ($quotationIds as $id) {
        $quotation = Quotations::find_by(['quotationId' => $id]);
        $message .= $quotation->quotationNumber . "<br/>";
      }

      $cc_mails[] = MAIL_BART;

      $gsdmailer = GsdMailer::build($tos, $subject, $message);
      $gsdmailer->setCcs($cc_mails);
      $gsdmailer->setFiles($files);
      $gsdmailer->send();

    }

    /**
     * Producten zijn opgehaald email
     * @param array $tos
     * @param int $cargo_receipt_id
     * @param array $quotationIds
     * @param array $contretourIds
     */
    public static function sendPickupEmail($tos, $cargo_receipt_id, $quotationIds = [], $contretourIds = []) {

      if (count($tos) == 0) return;

      $subject = "Producten opgehaald";
      $message = "Beste,<br/><br/>";
      if (count($quotationIds) > 0 && count($contretourIds) > 0) {
        $message .= "Uw producten zijn opgehaald en containers retour bezorgd.";
      }
      elseif (count($contretourIds) > 0) {
        $message .= "De containers zijn retour bezorgd.";
      }
      else {
        $message .= "Uw producten zijn opgehaald.";
      }
      $message .= "<br/><br/>";
      $message .= "In de bijlage vind u uw vrachtbon en additionele informatie.<br/><br/>";
      $message .= "Met vriendelijke groet,<br/><br/>";
      $message .= "Raamdorpelelementen BV<br/><br/>";
      $message .= "Raambrug 9<br/>";
      $message .= "5531 AG Bladel, Nederland<br/>";
      $message .= "Tel.  0497-36.07.91<br/>";
      $message .= "Fax. 0497-38.09.71<br/>";
      $message .= "Email: <EMAIL><br/>";
      $message .= "Website: www.raamdorpel.nl<br/>";


      $cargo_receipt = CargoReceipt::find_by(["cargoReceiptId" => $cargo_receipt_id]);

      if ($cargo_receipt) {

        $files = [];
        $files[] = CargoReceipt::getPdf($cargo_receipt_id);
        if (count($quotationIds) > 0) {
          if ($cargo_receipt->cargoReceiptType == 'standard' || $cargo_receipt->cargoReceiptType == 'factorypickup') { //niet toevoegen bij pickup route
            $files[] = CargoReceipt::getProductionstaatPdf($quotationIds);
          }
        }

        $cc_mails = [];
        $quotation = Quotations::find_by(['quotationId' => reset($quotationIds)]);
        $company = CrmCompanies::find_by(['companyId' => $quotation->companyId]);
        if ($company) {
          if (!empty($company->cargo_receipt_mail_1) && $company->cargo_receipt_mail_1 !== '') {
            $cc_mails[] = $company->cargo_receipt_mail_1;
          }
          if (!empty($company->cargo_receipt_mail_2) && $company->cargo_receipt_mail_2 !== '') {
            $cc_mails[] = $company->cargo_receipt_mail_2;
          }
          if (!empty($company->cargo_receipt_mail_3) && $company->cargo_receipt_mail_3 !== '') {
            $cc_mails[] = $company->cargo_receipt_mail_3;
          }
        }

        $cc_mails[] = MAIL_BART;

        $gsdmailer = GsdMailer::build($tos, $subject, $message);
        $gsdmailer->setCc($cc_mails);
        $gsdmailer->setFiles($files);
        $gsdmailer->send();
      }
      else {
        GsdMailer::build(MAIL_BART, "Vrachtbon niet gevonden!? Nr: " . $cargo_receipt_id, "Vrachtbon niet gevonden!? Nr: " . $cargo_receipt_id)->send();
      }

    }

    /**
     * Containers zijn opgehaald email
     * @param $tos
     * @param $cargo_receipt_id
     * @param $quotationIds
     */
    public static function sendPickupcontainersEmail($tos, $cargo_receipt_id, $quotationIds = []) {

      if (count($tos) == 0) return;

      $cargo_receipt = CargoReceipt::find_by(["cargoReceiptId" => $cargo_receipt_id]);

      if (!$cargo_receipt) {
        GsdMailer::build(MAIL_BART, "Vrachtbon niet gevonden!? Nr: " . $cargo_receipt_id, "Vrachtbon niet gevonden!? Nr: " . $cargo_receipt_id . ".<br/>Geen mailtje naar klant verzonden. ")->send();
        return;
      }

      $cc_mails = [];
      $quotation = Quotations::find_by(['quotationId' => $quotationIds[0]]);
      $company = CrmCompanies::find_by(['companyId' => $quotation->companyId]);
      if ($company) {
        if (!empty($company->cargo_receipt_mail_1)) {
          $cc_mails[] = $company->cargo_receipt_mail_1;
        }
        if (!empty($company->cargo_receipt_mail_2)) {
          $cc_mails[] = $company->cargo_receipt_mail_2;
        }
        if (!empty($company->cargo_receipt_mail_3)) {
          $cc_mails[] = $company->cargo_receipt_mail_3;
        }
      }

      $files = [];

      $files[] = CargoReceipt::getPdf($cargo_receipt_id);

      if (count($quotationIds) > 0) {
        if ($cargo_receipt->cargoReceiptType == 'standard' || $cargo_receipt->cargoReceiptType == 'factorypickup') { //niet toevoegen bij pickup route
          $files[] = CargoReceipt::getProductionstaatPdf($quotationIds);
        }
      }

      $subject = "Producten opgehaald";
      $message = "Beste,<br/><br/>";
      $message .= "Wij hebben de lege containers opgehaald.<br/><br/>";
      $message .= "In de bijlage vind u uw vrachtbon en additionele informatie.<br/><br/>";
      $message .= "Heeft u nog ergens containers staan, maar u twijfelt?<br/>";
      $message .= "In het online-offerte-systeem kunt u bij \"offerte geschiedenis\" alle containers terug zien, als er nog een container leeg is dan horen we dat graag.<br/><br/>";
      $message .= "Met vriendelijke groet,<br/><br/>";
      $message .= "Raamdorpelelementen BV<br/><br/>";

      $bericht = (new WrapperService($message))->wrap();

      $gsdmailer = GsdMailer::build($tos, $subject, $bericht);
      $gsdmailer->setCc(MAIL_BART);
      $gsdmailer->setFiles($files);
      $gsdmailer->send();

    }


    /**
     * Containerretour email, action=1
     * @param $containers_retour_ids
     */
    public static function sendContainerretourEmail($containers_retour_ids) {

      if (count($containers_retour_ids) == 0) return;

      $message = "Beste,<br/><br/>";
      $message .= "Er zijn containers retour genomen met een opmerking:<br/><br/>";
      $sendcont = false;
      foreach (Containers::find_all_by(["containerId" => array_flip($containers_retour_ids), "action" => 1]) as $l_cont) {
        $message .= "Containernummer: " . $l_cont->containerNumber . "<br/>";
        $message .= "Opmerking: " . $l_cont->actionRemark . "<br/><br/>";
        $sendcont = true;
      }
      if ($sendcont) {
        $subject = "Containers retour";
        GsdMailer::build(MAIL_FROM, $subject, $message)->send();
      }

    }


    /**
     * Container niet op de zaak
     * @param $containerNumber
     */
    public static function containerNotHome($containerNumber) {

      $subject = "Fout! Bak / rek is niet aanwezig";
      $message = "Foutmelding,<br/><br/>";
      $message .= "Bak of rek " . $containerNumber . " staat volgens het systeem niet op de zaak!<br/>";
      $message .= "Er wordt geprobeert deze in te boeken. Boek de bak/ rek eerst terug!<br/><br/>";
      $message .= "<i>Dit bericht is automatisch gegenereerd.</i>";

      GsdMailer::build(MAIL_FROM, $subject, $message)->send();

    }

    /**
     * Container kan worden opgehaald. Verzend mail naar klant.
     * @param $quotationIds
     */
    public static function containerPickup($quotationIds) {
      if (count($quotationIds) == 0) return;

      $query = "SELECT quotations.* ";
      $query .= "FROM " . Quotations::getTablename() . " ";
      $query .= "JOIN " . QuotationsExtra::getTablename() . " ON quotations.quotationId=quotations_extra.quotationId AND addressDeliveryId=" . PICKUP_ADDRESS_ID . " ";
      $query .= "WHERE quotations.quotationId IN (" . implode(",", $quotationIds) . ") ";
      //echo $query;
      //logToFile("mysql",$query);

      $oResult = DBConn::db_link()->query($query);
      while ($row = $oResult->fetch_object()) {

        $user_row = SandboxUsers::find_by(["userId" => $row->userId]);

        if ($user_row && $user_row->email != "") {
          $sQuotationNumber = $row->quotationNumber . '-' . $row->quotationVersion . $row->quotationPart;

          $subject = "Raamdorpel.nl: order staat klaar voor afhalen";

          $html = file_get_contents(DIR_LANGUAGES_PROJECT . 'nl/gepakt.html');
          $html = str_replace("<!--@@ PROJECT_NAME @@-->", $row->projectName, $html);
          $html = str_replace("<!--@@ PROJECT_REF @@-->", ($row->projectReference == "" ? '-' : $row->projectReference), $html);
          $html = str_replace("<!--@@ QUOTATION_NR @@-->", $sQuotationNumber, $html);

          $bericht = (new WrapperService($html))->wrap();

          $gsdmailer = GsdMailer::build($user_row->email, $subject, $bericht);
          $gsdmailer->addCc(MAIL_BART);
          $gsdmailer->send();


        }

      }


    }

    /**
     * @param Containers[] $containers
     */
    public static function containerRetourApp($containers) {

      $user = gsdApiSession::getUser();

      $subject = "Containers retour geboekt";
      $message = "Beste,<br/><br/>";
      $message .= "Medewerker " . $user->name . " heeft de volgende containers retour geboekt:<br/><br/>";
      foreach ($containers as $c) {
        $message .= "- " . $c->containerNumber . "<br/>";
      }
      $message .= "<br/><br/><i>Dit bericht is automatisch gegenereerd.</i>";

      GsdMailer::build(MAIL_FROM, $subject, $message)->send();

    }

    /**
     * Verzend email van leverancier bestelling
     * @param StoneOrder $stoneorder
     */
    public static function sendOrder($stoneorder, $send = true) {
      $subject = "Bestelling raamdorpel elementen";
      if (!$send) {
        $subject = "LET OP: Deze mail is nog niet naar de leverancier gemaild! Bestelling raamdorpel elementen";
      }

      $message = "<style>td {padding-right: 15px;}</style>";

      $message .= "Beste,<br/><br/>";
      $message .= "Hierbij plaats ik de volgende bestelling:<br/>";

      $stoneprev = false;
      $message .= "<table>";
      foreach ($stoneorder->items as $item) {
        if ($item->size > 0) {
          $color = StoneColors::find_by(["colorId" => $item->stone->colorId]);
          if (!Stones::areSiblings($item->stone, $stoneprev)) {
            $message .= "<tr><td colspan='3'>&nbsp;</td></tr>";
            $message .= "<tr><td colspan='3'><B>Ref: " . $item->getRefname() . "</B></td></tr>";
          }
          $message .= "<tr>";
          $message .= "<td>" . $item->stone->name . "</td>";
          $message .= "<td>" . ($color ? $color->getFullname() : '') . "</td>";
          $message .= "<td style='text-align: right;width: 90px;' width='90'>" . $item->size . "</td>";
          $message .= "</tr>";
          $stoneprev = $item->stone;
        }
      }
      $message .= "<tr><td colspan='3' style='border-top: 1px solid grey;'></td></tr>";
      $message .= "</table>";
      $message .= "<br/><br/>";
      $message .= "Laat maar weten wanneer we de bestelling kunnen ophalen.<br/><br/>";
      $message .= "<br/>Bedankt en met vriendelijke groet,<br/><br/>";
      $message .= "Bart Tenbült<br/>";
      $message .= "Raamdorpelelementen BV<br/><br/>";
      $message .= "Raambrug 9<br/>";
      $message .= "5531 AG Bladel, Nederland<br/>";
      $message .= "Tel.  0497-36.07.91<br/>";
      $message .= "Fax. 0497-38.09.71<br/>";
      $message .= "Email: <EMAIL><br/>";
      $message .= "Website: www.raamdorpel.nl<br/>";

      $mailto = MAIL_FROM;
      $cc = '';
      $stonebrand = StoneBrands::find_by(["brandId" => $stoneorder->brand_id]);
      if ($stonebrand && ValidationHelper::isEmail($stonebrand->orderemail) && $send) {
        $mailto = $stonebrand->orderemail;
        $cc = MAIL_FROM;
      }

      $gsdmailer = GsdMailer::build($mailto, $subject, $message);
      $gsdmailer->addCc($cc);
      $gsdmailer->send();

    }

    /**
     * Verzend email naar bart van producten geleverd met verwacht-op-datum ingevuld
     * @param StoneOrderItem[] $stoneorderitems
     */
    public static function sendDeliveredExpectedproducts($stoneorderitems) {

      $hasreadydate = false;
      foreach ($stoneorderitems as $soi) {
        if ($soi->receiveddate != "" && $soi->supplierreadydate != "") {
          $hasreadydate = true;
          break;
        }
      }
      if (!$hasreadydate) return;


      $subject = "Producten met verwachte leverdatum zijn geleverd";

      $message = "<style>td {padding-right: 10px;}</style>";
      $message .= "Beste,<br/><br/>";
      $message .= "De volgende producten met een verwachte leverdatum zijn vandaag geleverd:<br/><br/>";
      $message .= "<table>";
      $message .= "<tr>";
      $message .= "<td><b>Naam</b></td>";
      $message .= "<td style='text-align: right'><b>Aantal besteld</b></td>";
      $message .= "<td style='text-align: right'><b>Aantal geleverd</b></td>";
      $message .= "<td style='text-align: right'><b>Verwachte leverdatum</b></td>";
      $message .= "</tr>";
      foreach ($stoneorderitems as $item) {
        if ($item->receivedsize > 0) {
          $message .= "<tr>";
          $message .= "<td>" . $item->stone->name . "</td>";
          $message .= "<td style='text-align: right'>" . $item->size . "</td>";
          $message .= "<td style='text-align: right'>" . $item->receivedsize . "</td>";
          $message .= "<td style='text-align: right'>" . $item->getSupplierreadydate() . "</td>";
          $message .= "</tr>";
        }
      }
      $message .= "</table>";
      $message .= "<br/><br/>";
      $message .= "<br/><i>Dit bericht is automatisch gegenereerd.</i>";

      GsdMailer::build(MAIL_FROM, $subject, $message)->send();

    }

    /**
     * Verzend email naar bart van producten geleverd
     * @param StoneOrderItem[] $stoneorderitems
     */
    public static function sendStonesDelivered($stoneorderitems, $remark) {

      $subject = "Stenen opgehaald door de vrachtwagen";

      $message = "<style>td {padding-right: 10px;}</style>";
      $message .= "Beste,<br/><br/>";

      if ($remark != "") {
        $message .= "Er zijn zojuist raamdorpelstenen opgepikt door de vrachtwagen.<br/>";
        $message .= "<b>Opmerking chauffeur:<br/>" . $remark . "</b><br/><br/>";
      }
      $message .= "Het betreft de volgende raamdorpelstenen:<br/><br/>";
      $message .= "<table>";
      $message .= "<tr>";
      $message .= "<td><b>Naam</b></td>";
      $message .= "<td style='text-align: right'><b>Besteld</b></td>";
      $message .= "<td style='text-align: right'><b>Geleverd</b></td>";
      $message .= "<td style='text-align: right'><b>Totaal geleverd</b></td>";
      $message .= "<td style='text-align: right'><b>Verwachte leverdatum</b></td>";
      $message .= "</tr>";
      foreach ($stoneorderitems as $item) {
        $color = '';
        if ($item->receivedsize < $item->size) {
          $color = 'color: red; font-weight: bold;';
        }
        if ($item->receivedsize_now > 0) {
          $stone = Stones::find_by(["stoneId" => $item->stone_id]);
          $message .= "<tr>";
          $message .= "<td style='" . $color . "'>" . $stone->name . "</td>";
          $message .= "<td style='text-align: right;" . $color . "'>" . $item->size . "</td>";
          $message .= "<td style='text-align: right;" . $color . "'>" . $item->receivedsize_now . "</td>";
          $message .= "<td style='text-align: right;" . $color . "'>" . $item->receivedsize . "</td>";
          $message .= "<td style='text-align: right;" . $color . "'><b>" . $item->getSupplierreadydate() . "</b></td>";
          $message .= "</tr>";
        }
      }
      $message .= "</table>";
      $message .= "<br/><br/>";
      $message .= "<br/><i>Dit bericht is automatisch gegenereerd.</i>";

      GsdMailer::build(MAIL_FROM, $subject, $message)->send();

    }


    /**
     * Verzend voorraad overzicht naar bart
     */
    public static function sendStockbalance($onlyunknown = false) {

      $subject = "Voorraad stenen balans";

      $message = "<style>td {padding-right: 10px;}</style>";
      $message .= "Beste,<br/><br/>";

      if ($onlyunknown) {
        $subject = "Voorraad stenen balans met ongeldige prijs";
        $message .= "In de bijlage vind u het maandelijkse overzicht van vooraad stenen met <b><i>ongeldige</i></b> prijs.";
        $pdf = new StockPdf(false);
        $filename = $pdf->generatePdf(true);
      }
      else {
        $message .= "In de bijlage vind u het maandelijkse overzicht van vooraad stenen.";
        $pdf = new StockPdf(false);
        $filename = $pdf->generatePdf();
      }

      $message .= "<br/><br/>";
      $message .= "<i>Dit bericht is automatisch gegenereerd.</i>";

      $gsdmailer = GsdMailer::build(MAIL_FROM, $subject, $message);
      $gsdmailer->addFile(DIR_TEMP . $filename);
      $gsdmailer->send();

    }


    /**
     * Verlies mail overzicht naar bart
     */
    public static function sendStockLoss() {

      $stoneloses = StoneLoss::find_all("WHERE DATE(insertTS)='" . date("Y-m-d", strtotime("-1 DAY")) . "'");
      if (count($stoneloses) == 0) return;

      $subject = "Stenen verlies";

      $message = "<style>td {padding-right: 10px;}</style>";
      $message .= "Beste,<br/><br/>";


      $message .= "Gisteren zijn er stenen als verlies geboekt, en verwijderd uit de voorraad. In deze mail een overzicht.";

      $message .= "<br/><br/><table>";
      $message .= "<tr>";
      $message .= "<td><b>Product</b></td>";
      $message .= "<td style='text-align: right'><b>Aantal verlies</b></td>";
      $message .= "</tr>";

      $stones = AppModel::mapObjectIds(Stones::find_all("ORDER BY name"), 'stoneId');
      foreach ($stoneloses as $stoneloss) {
        if (!isset($stones[$stoneloss->stone_id]->stoneloss)) $stones[$stoneloss->stone_id]->stoneloss = 0;
        $stones[$stoneloss->stone_id]->stoneloss += $stoneloss->stockloss;
      }

      foreach ($stones as $stone) {
        if (!isset($stone->stoneloss) || $stone->stoneloss == 0) continue;
        $message .= "<tr>";
        $message .= "<td>" . $stone->name . "</td>";
        $message .= "<td style='text-align: right;'>" . $stone->stoneloss . "</td>";
        $message .= "</tr>";
      }
      $message .= "</table>";
      $message .= "<br/><br/>";
      $message .= "<i>Dit bericht is automatisch gegenereerd.</i>";

      GsdMailer::build(MAIL_FROM, $subject, $message)->send();

    }

    /**
     * Verlies mail overzicht naar bart
     */
    public static function sendStonesDeliveredExtra($stones) {

      $subject = "Stenen opgehaald door de vrachtwagen - afgevuld";

      $message = "<style>td {padding-right: 10px;}</style>";
      $message .= "Beste,<br/><br/>";

      $message .= "De vrachtwagen is zojuist afgevuld met onderstaande stenen.<br/>";

      $message .= "<br/><br/><table>";
      $message .= "<tr>";
      $message .= "<td><b>Product</b></td>";
      $message .= "<td style='text-align: right'><b>Aantal geladen</b></td>";
      $message .= "<td style='text-align: right'><b>Voorraad</b></td>";
      $message .= "</tr>";

      foreach ($stones as $stone) {
        $message .= "<tr>";
        $message .= "<td>" . $stone->name . "</td>";
        $message .= "<td style='text-align: right;'>" . $stone->stockadded . "</td>";
        $message .= "<td style='text-align: right;'>" . $stone->stock . "</td>";
        $message .= "</tr>";
      }
      $message .= "</table>";
      $message .= "<br/><br/>";
      $message .= "<i>Dit bericht is automatisch gegenereerd.</i>";
      GsdMailer::build(MAIL_FROM, $subject, $message)->send();

    }

    /**
     * Send email validation email
     * @param SandboxUsers $suser
     */
    public static function sendConfirmEmailadres($suser, $redirecturl) {

      //$suser->email is encypted email
      $sActivatieURI = $_SESSION['site']->site_host->getDomain(true) . PageMap::getUrl(207) . '?action=confirmemail&code=' . $suser->password;
      if ($redirecturl) {
        $sActivatieURI .= "&redirect=" . urlencode($redirecturl);
      }

      $message = "<p>Beste " . $suser->getNaam() . ", </p>\n";
      $message .= "<p>Hartelijk dank voor uw interesse in Raamdorpelelementen.</p>\n";
      $message .= "<p>U heeft zich succesvol aangemeld voor Raamdorpel.nl.<br />\n";
      $message .= "	Om uw privacy te waarborgen, vragen we u om uw e-mailadres te bevestigen. Klik daarvoor op onderstaande link. Mocht dat niet werken, kopieer dan het adres in de adresbalk van uw webbrowser.</p>\n";
      $message .= "<p><a href=\"{$sActivatieURI}\">{$sActivatieURI}</a></p>\n";
      $message .= "<p>Na bevestiging kunt u onder andere:\n";
      $message .= "<ul>\n";
      $message .= "	<li>Offertes maken of wijzigen</li>\n";
      $message .= "	<li>Order status volgen</li>\n";
      $message .= "	<li>Facturen inzien</li>\n";
      $message .= "	<li>Producten bestellen in de webshop</li>\n";
      $message .= "</ul>\n";
      $message .= "</p>\n";
      $message .= "<p>U logt in met uw eigen e-mailadres (" . $suser->email . ") en het door uw gekozen wachtwoord.<br />\n";
      $message .= "	Uit veiligheidsoverwegingen is het wachtwoord niet meegestuurd. Mocht u uw wachtwoord ooit vergeten, dan kunt u op de inlogpagina een nieuw wachtwoord opvragen.</p>\n";
      $message .= "<p>Met vriendelijke groet,</p>\n";
      $message .= "<p><br /><b>Raamdorpelelementen BV</b></p>";
      $message .= "<p><i>Indien u zich niet heeft geregistreerd op onze website, klik dan niet op bovenstaande link. Uw gegevens worden dan niet in ons systeem opgenomen. Op al onze leveringen zijn onze algemene voorwaarden van toepassing. </i></p>\n";

      $bericht = (new WrapperService($message))->wrap();

      $subject = "Raamdorpel.nl account activeren";
      $gsdmailer = GsdMailer::build($suser->email, $subject, $bericht);
      $gsdmailer->addCc(MAIL_FROM);
      $gsdmailer->send();

    }


    /**
     * Send email validation email
     * @param SandboxUsers $suser
     */
    public static function sendConfirmEmailadresAddUser($suser) {

      //$suser->email is encypted email
      $sActivatieURI = $_SESSION['site']->site_host->getDomain(true) . PageMap::getUrl(207) . '?action=confirmemail&code=' . $suser->password;

      $message = "<p>Beste " . $suser->getNaam() . ", </p>\n";
      $message .= "<p>Een collega heeft u geregistreerd als gebruiker bij Raamdorpelelementen.</p>\n";
      $message .= "<p>Om uw privacy te waarborgen, vragen we u om uw e-mailadres te bevestigen. Klik daarvoor op onderstaande link. Mocht dat niet werken, kopieer dan het adres in de adresbalk van uw webbrowser.</p>\n";
      $message .= "<p><a href=\"{$sActivatieURI}\">{$sActivatieURI}</a></p>\n";
      $message .= "<p>Na bevestiging kunt u onder andere:\n";
      $message .= "<ul>\n";
      $message .= "	<li>Nieuwe offertes maken</li>\n";
      $message .= "	<li>Bestaande offertes wijzigen</li>\n";
      $message .= "	<li>Eenvoudig offertes beheren</li>\n";
      $message .= "	<li>Order status volgen</li>\n";
      $message .= "	<li>Producten bestellen in de webshop</li>\n";
      $message .= "</ul>\n";
      $message .= "</p>\n";
      $message .= "<p>U logt in met uw eigen e-mailadres (" . $suser->email . ") en het door uw gekozen wachtwoord.<br />\n";
      $message .= "	Uit veiligheidsoverwegingen is het wachtwoord niet meegestuurd. Mocht u uw wachtwoord ooit vergeten, dan kunt u op de inlogpagina een nieuw wachtwoord opvragen.</p>\n";
      $message .= "<p>Met vriendelijke groet,</p>\n";
      $message .= "<p><br /><b>Raamdorpelelementen BV</b></p>";
      $message .= "<p><i>Indien u zich niet heeft geregistreerd op onze website, klik dan niet op bovenstaande link. Uw gegevens worden dan niet in ons systeem opgenomen. Op al onze leveringen zijn onze algemene voorwaarden van toepassing. </i></p>\n";

      $bericht = (new WrapperService($message))->wrap();

      $subject = "Raamdorpel.nl account activeren";

      $gsdmailer = GsdMailer::build($suser->email, $subject, $bericht);
      $gsdmailer->addCc(MAIL_FROM);
      $gsdmailer->send();


    }


    /**
     * Send password forgot email, with link to reset password.
     * @param SandboxUsers $user (required)
     * @param Site $site (required)
     * @param string $customurl
     */
    public static function sendForgotPasswordLink($user, $site, $customurl = '') {

      $subject = __('Wachtwoord opnieuw instellen') . ' ' . $site->site_host->host;
      $hash = EncryptionHelper::encrypt($user->email . HASH_STRING . date("YmdHis"));
      $url = $site->site_host->getDomain(true);
      if ($customurl == '') {
        $url .= PageMap::getUrl("M_LOGIN") . "?type=passwordreset";
      }
      else {
        $url .= $customurl;
      }
      $url .= "&hash=" . $hash;
      $url .= "&id=" . $user->userId;

      $bericht = getLanguageFile($_SESSION['lang'], 'mail_passwordreset.html');
      $bericht = str_replace("[*HOST*]", StringHelper::getDomainOfUrl($site->site_host->getDomain(true)), $bericht);
      $bericht = str_replace("[*LINK*]", $url, $bericht);

      $bericht = str_replace("[*SIGNATURE*]", "", $bericht);

      //deze link is 30 minuten geldig
      $up = SandboxUsersPw::find_by(["userId" => $user->userId]);
      if (!$up) {
        //deze heeft nog geen GA key, genereren maar.
        $up = new SandboxUsersPw();
        $up->userId = $user->userId;
      }
      $up->hash = $hash;
      $up->validuntilTS = date("Y-m-d H:i:s", strtotime("+35 MINUTES"));  //35 minuten
      $up->save();

      $bericht = (new WrapperService($bericht, $_SESSION['lang']))->wrap();

      GsdMailer::build($user->email, $subject, $bericht)->send();
      logToFile('mails', "SanboxUser::sendForgotPasswordLink: " . print_r($user->email, true));
    }

    /**
     * Verzenden offerte webshop met pdf per email.
     * @param Quotations $quotation
     */
    public static function sendTenderEmailWebshop($quotation) {

      $user = SandboxUsers::find_by(["userId" => $quotation->userId]);

      $message = "<p>Beste [*AANHEF*]<br/><br/>";

      $message .= 'Deze e-mail bevat uw vrijblijvende offerte. De offerte is aanwezig als bijlage (attachment) evenals onze leveringsvoorwaarden.<br />
                    Wilt u deze offerte wijzigen of omzetten in een bestelling, klik dan op onderstaande link.<br/><br/>
                    <a href="[*URL*]">[*URL*]</a><br/><br/>';

      $message .= "Deze offerte is geldig tot 31 december " . $quotation->getQuotationDate("Y") . ".<br/><br/>";

      $message .= 'Met vriendelijke groet,<br/><br/>Bart Tenbült<br/><br/>';
      $message .= '<i>Op al onze leveringen zijn onze algemene voorwaarden van toepassing.</i><br/>';

      $url = $_SESSION["site"]->site_host->getDomain(true) . '/offerte?action=open&hash=' . urlencode(Quotations::encrypt($quotation->quotationId . '_' . $quotation->userId));
      $message = str_replace("[*AANHEF*]", $user->getAanhef(), $message);
      $message = str_replace("[*URL*]", $url, $message);

      $files = [];

      $pdf = new QuotationPdf($quotation->quotationId);
      $filename = $pdf->generatePdf();
      $files[] = DIR_TEMP . $filename;
      if (file_exists(DIR_PROJECT_FOLDER . 'templates/frontend/files/algemene-voorwaarden.pdf')) {
        $files[] = DIR_PROJECT_FOLDER . 'templates/frontend/files/algemene-voorwaarden.pdf';
      }

      $subject = 'Offerte aanvraag raamdorpels ' . $quotation->getQuotationNumberFull();

      $bericht = (new WrapperService($message))->wrap();

      $to = $user->email;
      $cc = MAIL_FROM;
      $from = MAIL_FROM;

      if ((isset($quotation->NoEmail) && $quotation->NoEmail == 1) || $user->noti_order_mail == 0) {
        $to = MAIL_FROM;
        $cc = '';
        $from = $user->email; //reply to cust
      }

      $gsdmailer = GsdMailer::build($to, $subject, $bericht);
      $gsdmailer->addCc($cc);
      $gsdmailer->setFiles($files);
      $gsdmailer->setReplyTo($from);
      $gsdmailer->send();

    }

    /**
     * Verzenden offerte elementen met pdf per email.
     * @param Quotations $quotation
     * @param OrderElements[] $elements
     * @param bool|string $message
     */
    public static function sendTenderEmail($quotation, $elements, $message = false) {

      $user = SandboxUsers::find_by(["userId" => $quotation->userId]);
      $stone = Stones::find_by(["stoneId" => $quotation->stoneId]);

      if ($message === false) { //pak standaard bericht
        $message = "<p>Beste [*AANHEF*]<br/><br/>";

        $message .= 'Deze e-mail bevat uw vrijblijvende offerte. De offerte is aanwezig als bijlage (attachment) evenals onze leveringsvoorwaarden.<br />
                      Wilt u deze offerte wijzigen of omzetten in een bestelling, klik dan op onderstaande link.<br/><br/>
                      <a href="[*URL*]">[*URL*]</a><br/><br/>';

        $message .= "Wij nemen vervolgens zo spoedig mogelijk contact met u op.<br />";
        $message .= "Deze offerte is geldig tot 31 december [*QUOT_YEAR*].<br/><br/>";

        if ($quotation->hasVerstek($elements, $stone)) {

          $message .= "<b>Verstekken berekenen</b><br/><br/>";
          $message .= "Als hulpmiddel bij het berekenen van de verstekken kunt u de volgende vier PDF documenten gebruiken:<br/>";
          $message .= "<a href=\"https://www.raamdorpel.nl/downloads/verstekken-invulformulier-uitwendige-hoek.pdf\">verstekken-invulformulier-uitwendige-hoek.pdf</a> <br /> \n\n";
          $message .= "<a href=\"https://www.raamdorpel.nl/downloads/verstekken-invulformulier-inwendige-hoek.pdf\">verstekken-invulformulier-inwendige-hoek.pdf</a> <br /> \n\n";
          $message .= "<a href=\"https://www.raamdorpel.nl/downloads/verstekken-invulformulier-uitwendige-hoek-van-675-135-graden.pdf\">verstekken-invulformulier-uitwendige-hoek-van-675-135-graden.pdf</a> <br /> \n\n";
          $message .= "<a href=\"https://www.raamdorpel.nl/downloads/verstekken-invulformulier-uitwendige-hoek-van-60-120-graden.pdf\">verstekken-invulformulier-uitwendige-hoek-van-60-120-graden.pdf</a> </p>\n\n";
        }

        $message .= 'Met vriendelijke groet,<br/><br/>Bart Tenbült<br/><br/>';
        $message .= '<i>Op al onze leveringen zijn onze algemene voorwaarden van toepassing.</i><br/>';
      }
      $host = "https://www.raamdorpel.nl";
      if (DEVELOPMENT) {
        $host = $_SESSION["site"]->site_host->getDomain(true);
      }
      $url = $host . '/offerte?action=open&hash=' . urlencode(Quotations::encrypt($quotation->quotationId . '_' . $quotation->userId));

      $message = str_replace("[*AANHEF*]", $user->getAanhef(), $message);
      $message = str_replace("[*URL*]", $url, $message);
      $message = str_replace("[*QUOT_YEAR*]", $quotation->getQuotationDate("Y"), $message);

      $files = [];

      $pdf = new QuotationPdf($quotation->quotationId);
      $filename = $pdf->generatePdf();
      $files[] = DIR_TEMP . $filename;
      if (file_exists(DIR_PROJECT_FOLDER . 'templates/frontend/files/algemene-voorwaarden.pdf')) {
        $files[] = DIR_PROJECT_FOLDER . 'templates/frontend/files/algemene-voorwaarden.pdf';
      }
      if ($stone && $stone->pdfLocation != "" && file_exists(DIR_ROOT_HTTPDOCS . $stone->pdfLocation)) {
        $files["doorsnede.pdf"] = DIR_ROOT_HTTPDOCS . $stone->pdfLocation;
      }

      $subject = 'Offerte aanvraag ';
      if ($stone) {
        $subject .= Stones::TYPES[$stone->getType()] . ' ';
      }
      $subject .= $quotation->getQuotationNumberFull();

      $bericht = (new WrapperService($message))->wrap();

      $to = $user->email;
      $cc = MAIL_FROM;
      $from = MAIL_FROM;

      if ((isset($quotation->NoEmail) && $quotation->NoEmail == 1) || $user->noti_order_mail == 0) {
        $to = MAIL_FROM;
        $cc = '';
        $from = $user->email; //reply to cust
      }

      if (isset($_GET["robert"])) {
        $to = $_GET["robert"];
        $cc = '';
        $from = MAIL_FROM;
      }

      $gsdmailer = GsdMailer::build($to, $subject, $bericht);
      $gsdmailer->addCc($cc);
      $gsdmailer->setFiles($files);
      $gsdmailer->setReplyTo($from);
      $gsdmailer->send();

    }

    /**
     * Verzend email besteld
     * @param Quotations $quotation
     */
    public static function sendOrderMail($quotation) {

      $user = SandboxUsers::getUserAndCompany($quotation->userId);
      $stone = Stones::find_by(["stoneId" => $quotation->stoneId]);

      $message = "<p>Beste " . $user->getAanhef() . "<br/><br/>";

      $message .= 'Bedankt voor het plaatsen van uw bestelling.<br/><br/>';

      if ($quotation->payedFlag == 1) {
        $message .= 'U heeft deze bestelling reeds afgerekend.<br/><br/>';
      }
      elseif (isset($user->company) && $user->company->payInAdvance == 1) {
        $message .= '<b>Let op: Uw account staat op vooruitbetalen.<br/>Voordat we gaan starten met de productie, dient u eerst uw bestelling af te rekenen via de pagina vooruitbetalen in uw raamdorpel account.</b><br/><br/>';
      }

      $message .= 'Bestelnummer: ' . $quotation->getQuotationNumberFull() . '<br/>';
      if ($quotation->projectName != "") {
        $message .= 'Projectnaam: ' . $quotation->projectName . '<br/>';
      }
      if ($quotation->projectReference != "") {
        $message .= 'Projectkenmerk: ' . $quotation->projectReference . '<br/>';
      }
      $message .= '<br/>';
      $message .= 'U kunt de verdere uitvoering van uw bestelling volgen op <a href="https://www.raamdorpel.nl/offerte">www.raamdorpel.nl/offerte</a><br/><br/>';
      $message .= 'Met vriendelijke groet,<br/><br/>Bart Tenbült<br/><br/>';
      $message .= '<i>Op al onze leveringen zijn onze algemene voorwaarden van toepassing.</i><br/>';

      $subject = 'Bestelling ' . $quotation->getQuotationNumberFull();

      $bericht = (new WrapperService($message))->wrap();

      $pdf = new QuotationPdf($quotation->quotationId);
      $filename = $pdf->generatePdf();
      $files[] = DIR_TEMP . $filename;

      if (file_exists(DIR_PROJECT_FOLDER . 'templates/frontend/files/algemene-voorwaarden.pdf')) {
        $files[] = DIR_PROJECT_FOLDER . 'templates/frontend/files/algemene-voorwaarden.pdf';
      }
      if ($stone && $stone->pdfLocation != "" && file_exists(DIR_ROOT_HTTPDOCS . $stone->pdfLocation)) {
        $files["doorsnede.pdf"] = DIR_ROOT_HTTPDOCS . $stone->pdfLocation;
      }

      $to_user = $user->email;
      $cc = MAIL_FROM;
      $from = MAIL_FROM;
      if ($user->noti_confirm_mail == 0) { //geen notificatie email naar klant
        $to_user = MAIL_FROM;
        $cc = '';
        $from = $user->email; //reply to cust
      }

      $gsdmailer = GsdMailer::build($to_user, $subject, $bericht);
      $gsdmailer->addCc($cc);
      $gsdmailer->setFiles($files);
      $gsdmailer->setReplyTo($from);
      $gsdmailer->send();


    }

    /**
     * @param Quotations $quotation
     * @deprecated op 18-06-2020
     * Verzend email wanneer factuur emailadres ontbreekt.
     */
    public static function sendMissingDataEmail($quotation) {

      //controlle alle data aanwezig
      if ($quotation->companyId != '') {
        $company = CrmCompanies::find_by(["companyId" => $quotation->companyId]);
        if ($company->sendByPost == 0) {
          $crm_ip = CrmInvoiceparties::find_by(["companyId" => $company->companyId]);
          if ($crm_ip && $crm_ip->email == "") { //er is geen factuur emailadres, vraag om gegevens
            $user = SandboxUsers::find_by(["userId" => $quotation->userId]);

            $message = 'Geachte heer/mevrouw,<br />
              <br />
              Wij willen u graag per email factureren. Hiervoor hebben wij wat extra gegevens nodig.<br />
              <br />
              U kunt inloggen op uw account op <a href="https://www.raamdorpel.nl/offerte">www.raamdorpel.nl/offerte</a>.<br />  
              Vervolgens kunt u onder het kopje <i>Mijn gegevens</i> uw factuur e-mailadres en IBAN-nummers invullen.<br />
              <br />

              <table>
              <tr>              
              <td style="padding: 10px 15px;background-color: #CE000C;border-radius: 5px;display: inline-block;">
                <a href="https://www.raamdorpel.nl/offerte" style="color: white;text-decoration: none;">Klik hier om uw factuur e-mailadres door te geven</a>
              </td>
              </tr>
              </table>
              
              <br /> 
              Behalve dat deze digitaliseringsslag gunstige effecten heeft op het gebied van het milieu en het<br />
              gebruik van papier en inkt, wordt traditionele post steeds duurder. Wij kunnen u niet garanderen dat<br />
              de papieren factuur gratis blijft.<br />
              <br />
              Alvast bedankt voor uw medewerking,<br /><br />
              Met vriendelijke groet,<br />
              <br />
              Bart Tenbült<br />';

            $subject = 'Raamdorpel.nl: factuur e-mailadres';

            $bericht = (new WrapperService($message))->wrap();

            $gsdmailer = GsdMailer::build($user->email, $subject, $bericht);
            $gsdmailer->addCc(MAIL_FROM);
            $gsdmailer->send();

          }
        }
      }

    }


    /**
     * @param Quotations $quotation
     * @param QuotationsExtra $quotations_extra
     * @param OrderElements $elements
     * @param Stones $stone
     */
    public static function sendPriceValidationEmail($quotation, $quotations_extra, $elements, $stone) {

      if (!$stone->isRaamdorpel()) {
        return;
      }

      $color = StoneColors::find_by(["colorId" => $stone->colorId]);

      $errorFlag = false;
      $extraInfo = '';

      $extraInfo .= "<p>Offerte nummer: " . $quotation->quotationNumber . "<br />";
      $extraInfo .= "quotationId: " . $quotation->quotationId . "<br />";
      $extraInfo .= "stoneId: " . $quotation->stoneId . "<br />";
      $extraInfo .= "stoneEnd: " . $quotation->endstone . "<br />";
      $extraInfo .= "colorId: " . $stone->colorId . "<br />";
      $extraInfo .= "colorName: " . $color->name . "<br />";
      $extraInfo .= "sizeId: " . $stone->sizeId . "<br />";


      //-- glue validation

      $glueprices = GluePrices::find_all_by(["sizeId" => $stone->sizeId], "AND validFrom <= '" . $quotations_extra->getQuotationAltPriceYear("Y-m-d") . "' AND validTo >= '" . $quotations_extra->getQuotationAltPriceYear("Y-m-d") . "'");

      $aRowGluePriceCount = count($glueprices);

      if ($aRowGluePriceCount > 1) {
        $extraInfo .= "<p>Er zijn meerdere glue waardes aanwezig, er mag maar 1 zijn.</p>";
        $errorFlag = true;
      }
      elseif ($aRowGluePriceCount == 0) {
        $extraInfo .= "<p>Er is geen glue prijs aanwezig. Er hoort een steen prijs te bestaan.</p>";
        $errorFlag = true;
      }
      else {
        $qlueprice = $glueprices[0];
        //-- geen einstenen
        $extraInfo .= "<b>Lijm prijs: " . $qlueprice->price . "</b> (Glueprices.id " . $qlueprice->id . ")<br/>";

        $minBedragGlue = 2;
        $maxBedragGlue = 15;
        if ($qlueprice->price < $minBedragGlue) {
          $extraInfo .= "<p style='color:red;'>Glue bedrag is kleiner dan " . $minBedragGlue . " euro.</p>";
          $errorFlag = true;
        }
        if ($qlueprice->price > $maxBedragGlue) {
          $extraInfo .= "<p style='color:red;'>Glue bedrag is groter dan " . $maxBedragGlue . " euro.</p>";
          $errorFlag = true;
        }
      }

      //-- stone price validaton

      $result = self::stonePriceValidationMail($stone, $quotations_extra, 0.7, 5);
      $extraInfo .= $result['txt'];
      if ($result['error'] != false) $errorFlag = true;

      $sibelings = $stone->getSibelings();
      $endstones = [];
      foreach ($elements as $element) {
        //pd($element);
        if ($element->leftEndstone != 0) {
          $endstones["left"] = $sibelings["left"];
        }
        if ($element->rightEndstone != 0) {
          $endstones["right"] = $sibelings["right"];
        }
        if ($element->leftEndstoneGrooves != 0) {
          $endstones["leftg"] = $sibelings["leftg"];
        }
        if ($element->rightEndstoneGrooves != 0) {
          $endstones["rightg"] = $sibelings["rightg"];
        }

      }

      foreach ($endstones as $endstone) {
        $result = self::stonePriceValidationMail($endstone, $quotations_extra, 2, 15);
        $extraInfo .= $result['txt'];
        if ($result['error'] != false) $errorFlag = true;
      }

      if ($errorFlag == true) {

        $message = "<p>Er is een offerte aangevraagd waarvan de steenprijs of lijmprijs te hoog of te laag is.</p>\n";
        $message .= '<p>Extrainfo: </p>';
        $message .= '<p>' . $extraInfo . '</p>';
        $message .= "<br/><i>Dit bericht is automatisch gegenereerd.</i>";

        $subject = "! Foute steen/lijm prijzen in offerte " . $quotation->getQuotationNumberFull();

        GsdMailer::build(MAIL_FROM, $subject, $message)->send();

      }

    }

    /**
     * @param Stones $stone
     * @param $quotations_extra
     * @param $minBedragSteen
     * @param $maxBedragSteen
     * @return array
     */
    private static function stonePriceValidationMail($stone, $quotations_extra, $minBedragSteen, $maxBedragSteen) {
      $extraInfo = "";
      $errorFlag = false;

      $stoneprices = StonePrices::find_all_by(["stoneId" => $stone->stoneId], "AND validFrom <= '" . $quotations_extra->getQuotationAltPriceYear("Y-m-d") . "' AND validTo >= '" . $quotations_extra->getQuotationAltPriceYear("Y-m-d") . "'");
      $aRowStonePriceCount = count($stoneprices);

      $type = '';
      if ($stone->endstone == Stones::ENDSTONE_LEFT) {
        $type .= "link ";
      }
      if ($stone->endstone == Stones::ENDSTONE_RIGHT) {
        $type .= "rechts ";
      }
      if ($stone->endstone == Stones::ENDSTONE_LEFTG) {
        $type .= "links groef ";
      }
      if ($stone->endstone == Stones::ENDSTONE_RIGHTG) {
        $type .= "rechts groef ";
      }

      if ($aRowStonePriceCount > 1) {
        $extraInfo .= "<p>Steen " . $type . ": er zijn meerdere waardes aanwezig, er mag maar 1 zijn.</p>";
        $errorFlag = true;
      }
      elseif ($aRowStonePriceCount == 0) {
        $extraInfo .= "<p>Steen " . $type . ": er is geen prijs aanwezig. Er hoort een steen prijs te bestaan.</p>";
        $errorFlag = true;
      }
      else {
        $stoneprice = $stoneprices[0];
        //-- geen einsteenen
        $extraInfo .= "<b>Steen ";
        $extraInfo .= $type . "prijs: " . $stoneprice->price . "</b> (StonePrice.id: " . $stoneprice->id . ")<br/>";
        if ($stoneprice->price < $minBedragSteen) {
          $extraInfo .= "<p style='color:red;'>Steen " . $type . "bedrag is kleiner dan " . $minBedragSteen . " euro.</p>";
          $errorFlag = true;
        }
        if ($stone->isKeramiek() && $stoneprice->price > $maxBedragSteen) {
          $extraInfo .= "<p style='color:red;'>Steen " . $type . "bedrag is groter dan " . $maxBedragSteen . " euro.</p>";
          $errorFlag = true;
        }
      }
      return ["txt" => $extraInfo, "error" => $errorFlag];
    }

    /**
     * Versturen email naar klant met bakken/rekken op locatie
     * @param array $group
     * @param string $compOrUserId
     */
    public static function sendContainersRetourReminder($group, $compOrUserId) {
      $tos = [];
      foreach (SandboxUsers::find_all_by(["userId" => $group["userIds"]]) as $user) {
        $tos[$user->email] = $user->email;
      }

      $quotationIds = [];
      foreach ($group["containers"] as $container) {
        foreach ($container->quotations as $quot) {
          $quotationIds[$quot->quotationId] = $quot->quotationId;
        }
      }

      foreach (CargoReceiptQuotation::find_all_by(['quotationId' => $quotationIds]) as $crq) {
        $cr = CargoReceipt::find_by(['cargoReceiptId' => $crq->cargoReceiptId]);
        if ($cr && $cr->signatureConsigneeMail != '' && ValidationHelper::isEmail($cr->signatureConsigneeMail)) {
          $tos[$cr->signatureConsigneeMail] = $cr->signatureConsigneeMail;
        }
      }

      //alleen verzenden als er nog containers staan die niet zijn gemarkeerd als op te halen.
      $can_send = false;
      foreach ($group["containers"] as $container) {
        if ($container->nextroute != 'true') {
          $can_send = true;
          break;
        }
      }
      if (!$can_send) return;

      $url = 'https://www.raamdorpel.nl/bakken-rekken?hash=' . User::encrypt($compOrUserId);

      $message = 'Geachte heer/mevrouw,<br />
              <br />
              Uit onze administratie is gebleken dat er nog een aantal rekken of bakken bij u op locatie staan.<br />
              Wanneer deze retour mogen, dan kun u dit doorgeven op <a href="' . $url . '">' . $url . '</a>.<br />  
              U kunt op deze pagina doorgeven welke bakken &amp; rekken retour mogen.<br />  
              <br />
              Hieronder ziet u een overzicht van de rekken en bakken welke nog niet retour zijn.<br /><br />';
      $message .= "<table>";
      $message .= "<tr>";
      $message .= "<td><b>Bak/Rek</b></td>";
      $message .= "<td><b>Leverdatum</b></td>";
      $message .= "<td><b>Offertenummers</b></td>";
      $message .= "<td><b>Project</b></td>";
      $message .= "<td><b>Kenmerk</b></td>";
      $message .= "<td><b>Locatie</b></td>";
      $message .= "<td><b>Afgemeld</b></td>";
      $message .= "</tr>";

      foreach ($group["containers"] as $container) {
        $message .= "<tr>";
        $message .= "<td>" . $container->containerNumber . "</td>";
        $message .= "<td>" . $container->deliverdate . "</td>";
        $message .= "<td>";
        foreach ($container->quotations as $quot) {
          $message .= $quot->getQuotationNumberFull() . '<br/>';
        }
        $message .= "</td>";
        $message .= "<td>";
        foreach ($container->quotations as $quot) {
          $message .= $quot->projectName . '<br/>';
        }
        $message .= "</td>";
        $message .= "<td>";
        foreach ($container->quotations as $quot) {
          $message .= $quot->projectReference . '<br/>';
        }
        $message .= "</td>";
        $message .= "<td>";
        foreach ($container->quotations as $quot) {
          $message .= $quot->getAddressFull(", ");
          break;
        }
        $message .= "</td>";
        $message .= "<td>";
        if ($container->nextroute == 'true') {
          $message .= 'Ja, word opgehaald';
        }
        $message .= "</td>";
        $message .= "</tr>";
      }
      $message .= "</table><br/>";
      $message .= "Met vriendelijke groet,<br/><br/>";
      $message .= "Raamdorpelelementen BV<br/><br/>";
      $message .= "<br/><i>Dit bericht is automatisch gegenereerd.</i><br/>";

      $subject = 'Raamdorpel.nl: bakken of rekken te retourneren';

      $bericht = (new WrapperService($message))->wrap();

      $gsdmailer = GsdMailer::build($tos, $subject, $bericht);
      $gsdmailer->addCc(MAIL_FROM);
      $gsdmailer->send();

    }

    /**
     * Versturen email naar klant met herinnering offerte
     * @param [] $group
     */
    public static function sendQuotationReminders() {

      //offertes gemaakt tussen 4 en 5 weken geleden
      $filt = "AND quotationdate>='" . date("Y-m-d", strtotime("-5 WEEKS")) . "' ";
      $filt .= "AND quotationdate<'" . date("Y-m-d", strtotime("-4 WEEKS")) . "' ";
      $filt .= "AND createdVia!='" . Quotations::CREATED_VIA_WEBSHOP . "' "; //geen herinnering sturen naar offertes via de webshop.s
      $filt .= "GROUP BY userId ";
      $quotations = Quotations::find_all_by(["flaggedForDeletion" => 0, "statusId" => 10], $filt);

      $bouwmateriaalhandel = CustomerGroups::getIdsByTypeid(2);

      foreach ($quotations as $quotation) {
        //pd($quotation->userId." ".$quotation->getQuotationDate());

        $user = SandboxUsers::find_by(["userId" => $quotation->userId]);
        if (!$user || !$user->hasCompany()) continue;
        $company = CrmCompanies::find_by(["companyId" => $user->companyId]);
        if (!$company || in_array($company->customerGroupId, $bouwmateriaalhandel)) {
          //bouwmetshandelaren niet mailen
          continue;
        }

        //heeft deze klant afgelopen jaar 1 bestelling geplaatsts?
        $hasOrderedCount = Quotations::count_all_by(["userId" => $quotation->userId, "flaggedForDeletion" => 0], "AND statusId>10 AND quotationdate>='" . date("Y-m-d", strtotime("-1 YEAR")) . "' ");
        if ($hasOrderedCount == 0) { //geen bestelling geplaatst afgelopen jaar. Mailen maar.

          $message = "Geachte heer/mevrouw,<br />
          <br />
          Enige tijd geleden heeft u een offerte aangevraagd met offertenummer " . $quotation->getQuotationNumberFull() . " voor de aanschaf van onze raamdorpelelementen.<br/>
          Helaas hebben wij tot op heden nog geen reactie van u mogen ontvangen. Mogelijk is onze offerte aan uw aandacht ontsnapt of heeft u nog vragen.<br/>
          We hebben de betreffende offerte als bijlage toevoegd.<br/><br/>
          Mocht u nog vragen hebben over onze producten, het bestelproces of levering van onze producten, aarzel dan niet en neem contact met ons op.<br/>
          Uiteraard willen wij u erop wijzen dat uw informatie- en offerteaanvraag geheel vrijblijvend zijn. <br/>
          Uiteraard is het ook ten allen tijde mogelijk een  nieuwe offerte te maken op " . '<a href="https://www.raamdorpel.nl">www.raamdorpel.nl</a><br/><br/>';
          $message .= "Met vriendelijke groet,<br/><br/>";
          $message .= "Raamdorpelelementen BV<br/><br/>";

          $pdf = new QuotationPdf($quotation->quotationId);
          $filename = $pdf->generatePdf();
          $files = [];
          $files[] = DIR_TEMP . $filename;

          $subject = 'Herinnering offerte aanvraag raamdorpels ' . $quotation->getQuotationNumberFull();

          $bericht = (new WrapperService($message))->wrap();

          $gsdmailer = GsdMailer::build($user->email, $subject, $bericht);
          $gsdmailer->addCc(MAIL_FROM);
          $gsdmailer->setFiles($files);
          $gsdmailer->send();


        }

      }

    }

    /**
     * Send email validation email
     * @param SandboxUsers $suser
     * @param CrmInvoiceparties||null $invoiceparty
     */
    public static function sendConfirmInvoiceEmailadres($suser, $invoiceparty = null) {

      //$suser->email is encypted email
      $email = false;
      if (!$suser->hasCompany()) {
        $email = $suser->invoice_email;
      }
      else {
        $email = $invoiceparty->email;
      }
      $sActivatieURI = $_SESSION['site']->site_host->getDomain(true) . PageMap::getUrlFrontendRouting(207) . '?action=confirminvoiceemail&code=' . $suser->password . '&email=' . rawurlencode(User::encrypt($email));

      $message = "<p>Beste " . $suser->getNaam() . ", </p>\n";
      $message .= "<p>We vragen u om uw factuur e-mailadres te bevestigen. Klik daarvoor op onderstaande link. Mocht dat niet werken, kopieer dan het adres in de adresbalk van uw webbrowser.</p>\n";
      $message .= "<p><a href=\"{$sActivatieURI}\">{$sActivatieURI}</a></p>\n";
      $message .= "<p>Met vriendelijke groet,</p>\n";
      $message .= "<p><br /><b>Raamdorpelelementen BV</b></p>";
      $message .= "<p><i>Indien u zich niet heeft geregistreerd op onze website, klik dan niet op bovenstaande link. Uw gegevens worden dan niet in ons systeem opgenomen. Op al onze leveringen zijn onze algemene voorwaarden van toepassing. </i></p>\n";

      $bericht = (new WrapperService($message))->wrap();

      $subject = "Raamdorpel.nl factuur e-mailadres bevestigen";
      $gsdmailer = GsdMailer::build($email, $subject, $bericht);
      $gsdmailer->send();

      $gsdmailer_bcc = GsdMailer::build(MAIL_FROM, $subject, $bericht);
      $gsdmailer_bcc->send();

    }

    /**
     * @param Quotations[] $quotations
     */
    public static function sendQuotationsPayedMail($quotations) {

      $llang = 'nl';

      $quotationsNumbers = [];
      foreach ($quotations as $quotation) {
        $quotationsNumbers[] = $quotation->quotationNumber;
      }

      $tos = [];
      $user = SandboxUsers::getUserAndCompany($quotations[0]->userId);
      if ($user && $user->email != "") {
        $tos[$user->email] = $user->email;
      }

      if (count($tos) > 0) {

        //verzend email met bedankt voor uw betaling.
        $html = getLanguageFile($llang, 'mail_quotations_paid.html');
        $html = str_replace("[*AANHEF*]", __("Geachte heer/mevrouw"), $html);
        $html = str_replace("[*QUOTATIONNUMBERS*]", "<li>" . implode("</li><li>", $quotationsNumbers) . "</li>", $html);

        $bericht = (new WrapperService($html))->wrap();

        $subject = __("Betaal bevestiging bestellingen");
        $gsdmailer = GsdMailer::build($tos, $subject, $bericht);
        $gsdmailer->send();

        $gsdmailer_bcc = GsdMailer::build(MAIL_FROM, $subject, $bericht);
        $gsdmailer_bcc->send();
      }

    }

    public static function sendSuspiciousQuotations() {

//    $equots = EmployeeQuotation::find_all("WHERE DATE(updateDate)='2019-09-30' GROUP BY quotationId");
      $equots = EmployeeQuotation::find_all("WHERE enddate>='" . date("Y-m-d H:05:00", strtotime("-1 HOURS")) . "' GROUP BY quotationId");
      $stone_speed = new StoneSpeed();
      $stone_speed->setFromDate(((int)date("Y") - 1) . "-01-01"); //kijken naar data afgelopen 1 tot 2 jaar
      $values = [];
      foreach ($equots as $eq) {

        $eqs = EmployeeQuotation::find_all_by(["quotationId" => $eq->quotationId]);
        if (count($eqs) > 1) continue; //maximaal 1 werknemer aan deze bestelling gewerkt
        $quotation = Quotations::getById($eq->quotationId);
        if ($quotation->toNumberQuotations == 1) continue; //geen genummerde bestellingen
        $quotation_extra = QuotationsExtra::find_by(["quotationId" => $quotation->quotationId]);
        if ($quotation_extra->prod_cm_per_hour == 0) continue; //er moet een productie snelheid aanwezig zijn
        $orderelements = OrderElements::find_all_by(["quotationId" => $quotation->id]);
        if (count($orderelements) >= 2) continue; //minstens 2 elementen

        $meter_setting = ceil($quotation->meters / 5) * 5;
        if ($meter_setting > 50) continue; //alleen bestellingen met een totaallengte kleiner dan 50 meter

        $employee = ProductionEmployees::find_by(['employeeId' => $eq->employeeId]);
        $employee_avg_stone = $stone_speed->getEmployeeStoneSpeed($employee, $quotation->stoneId);
        if (count($employee_avg_stone) != 1) continue; //ik krijg maar 1 steen terug, zo niet overslaan
        $employee_avg_stone = $employee_avg_stone[0];

        $cm_per_hour_normal = 0;
        if ($quotation->endstone == "false" || $quotation->endstone == "") { //zonder eindstenen
          $cm_per_hour_normal = $employee_avg_stone->cm_per_hour_noend;
        }
        else { //met eindstenen
          $cm_per_hour_normal = $employee_avg_stone->cm_per_hour_end;
        }

        $meter_perc_value = Setting::getValueByTypeAndCode("suspicious", $meter_setting); //pertage wat de medewerker sneller mag zijn
        $meter_perc = ((100 - $meter_perc_value) / 100);

        //pd($quotation_extra->prod_cm_per_hour . ' < ('.  $meter_perc."x".$cm_per_hour_normal." = ".($meter_perc * $cm_per_hour_normal).')');

        if ($quotation_extra->prod_cm_per_hour < $meter_perc * $cm_per_hour_normal) {
          //hij is veel sneller als normaal. email sturen.
          $val = [];
          $val["quotationNr"] = $quotation->getQuotationNumberFull();
          $val["employee"] = $employee->name;
          $val["meters"] = $quotation->meters;
          $val["speed"] = round($quotation_extra->prod_cm_per_hour / 100, 2);
          $val["speed_normal"] = round($cm_per_hour_normal / 100, 2);
          $val["deviation"] = floor(100 * (1 - ($val["speed"] / $val["speed_normal"])));
          $val["deviation_setting"] = $meter_perc_value;
          $val["stone"] = Stones::find_by(["stoneId" => $quotation->stoneId])->name;
          $values[] = $val;
        }

      }
      if (count($values) > 0) {
        $bericht = 'Beste,<br/><br/>Er zij bestellingen gevonden met een opvallende productie snelheid:<br/><br/>';
        $bericht .= '<table>';
        $bericht .= "<tr>";
        $bericht .= "<td>Bestelling</td>";
        $bericht .= "<td>Steen</td>";
        $bericht .= "<td>Medewerker</td>";
        $bericht .= "<td>Meters</td>";
        $bericht .= "<td>Productie snelheid<br/>m/uur</td>";
        $bericht .= "<td>Productie snelheid<br/> gemiddeld m/uur</td>";
        $bericht .= "<td>Sneller %</td>";
        $bericht .= "<td>Instelling %</td>";
        $bericht .= "</tr>";
        foreach ($values as $val) {
          $bericht .= "<tr>";
          $bericht .= "<td>" . $val["quotationNr"] . "</td>";
          $bericht .= "<td>" . $val["stone"] . "</td>";
          $bericht .= "<td style='text-align:right;'>" . $val["meters"] . "</td>";
          $bericht .= "<td>" . $val["employee"] . "</td>";
          $bericht .= "<td style='text-align:right;'>" . $val["speed"] . "</td>";
          $bericht .= "<td style='text-align:right;'>" . $val["speed_normal"] . "</td>";
          $bericht .= "<td style='text-align:right;'>" . $val["deviation"] . "%</td>";
          $bericht .= "<td style='text-align:right;'>" . $val["deviation_setting"] . "%</td>";
          $bericht .= "</tr>";
        }
        $bericht .= '</table><br/><br/>';
        $bericht .= "<i>Dit bericht is automatisch gegenereerd.</i>";
//      echo $bericht;
        $subject = "Bestellingen met opvallende productie snelheid";
        $gsdmailer = GsdMailer::build(MAIL_FROM, $subject, $bericht);
        $gsdmailer->send();

      }

    }


    /**
     * Verzend email naar bart met beschadigde stenen
     */
    public static function sendDamaged() {

      $time = strtotime("-1 MONTH");
      //$time = time();

      $query = "WHERE YEAR(insertTS)=" . date("Y", $time) . " AND MONTH(insertTS)=" . date("n", $time) . " ";
      $query .= "ORDER BY employeeId, insertTS DESC";
      $damage_quots = DamageQuotation::find_all($query);

      $quotIds = [];
      foreach ($damage_quots as $dq) {
        $quotIds[$dq->quotationId] = $dq->quotationId;
      }
      $quotations = AppModel::mapObjectIds(Quotations::find_all_by(["quotationId" => $quotIds]), "quotationId");
      foreach ($damage_quots as $dq) {
        $dq->quotation = $quotations[$dq->quotationId];
      }
      $damages = AppModel::mapObjectIds(Damage::find_all("ORDER BY name_nl"));
      $employees = AppModel::mapObjectIds(ProductionEmployees::find_all_by(["working" => 1], "ORDER BY name"), "employeeId");

      $subject = "Beschadigingen overzicht " . DateTimeHelper::strftime("%B %Y", $time);

      $message = "<style>td {padding-right: 10px;}</style>";
      $message .= "Beste,<br/><br/>";
      $message .= "De volgende beschadigingen zijn opgetreden in " . DateTimeHelper::strftime("%B %Y", $time) . ":<br/><br/>";
      $message .= "<table>";
      $message .= "<tr>";
      $message .= "<td><b>Medewerker</b></td>";
      $message .= "<td><b>Beschadiging</b></td>";
      $message .= "<td><b>Bestelling</b></td>";
      $message .= "<td><b>Datum</b></td>";
      $message .= "<td><b>Rek</b></td>";
      $message .= "</tr>";
      foreach ($damage_quots as $damage_quot) {
        $rack = GeneratedRackIds::find_by(["rackId" => $damage_quot->rackId]);
        $message .= "<tr>";
        $message .= "<td>" . $employees[$damage_quot->employeeId]->name . "</td>";
        $message .= "<td>" . $damages[$damage_quot->damageId]->name_nl . "</td>";
        $message .= "<td>" . $damage_quot->quotation->quotationNumber . "</td>";
        $message .= "<td>" . $damage_quot->getInsertTS("Y-m-d H:i") . "</td>";
        $message .= "<td>" . $rack->rackCode . "</td>";
        $message .= "</tr>";
      }
      $message .= "</table>";
      $message .= "<br/>";
      $message .= "Je kunt deze informatie ook terugvinden in de beheer omgeving.<br/> ";
      $message .= "<br/><br/>";
      $message .= "<i>Dit bericht is automatisch gegenereerd.</i>";

      GsdMailer::build(MAIL_FROM, $subject, $message)->send();

    }


    /**
     * Verzend email naar bart van op maat gemaakt
     * @param Quotations $quotation
     */
    public static function sendOpmaatgemaakt($quotation) {

      $subject = "Offerte op maat gemaakt: " . $quotation->getQuotationNumberFull();

      $message = "<style>td {padding-right: 10px;}</style>";
      $message .= "Beste,<br/><br/>";

      $message .= "Er is een op maat gemaakte offerte aangevraag van € 0.00.<br/>";
      $message .= "Completeer de offerte met de juiste prijzen, en verstuur de aangepaste offerte naar de klant.<br/>";
      $message .= "<br/><br/>";
      $message .= "<br/><i>Dit bericht is automatisch gegenereerd.</i>";

      GsdMailer::build(MAIL_FROM, $subject, $message)->send();

    }

    /**
     * Stuur een email naar klanten met vinkje noti_produced_mail aan om 18:15
     * van de orders die de afgelopen 24 uur geproduceerd zijn.
     * Ook als er nog geen company is gekoppeld.
     */
    public static function sendQuotationsProduced() {
      $from = date("Y-m-d H:i:00", strtotime("-1 DAY"));
//    $from = date("2023-02-13 00:00:00");

      //offertes gemaakt tussen 4 en 5 weken geleden
      $filt = "AND statusId>=40 AND statusId<55 "; //geproduceerd of hoger (extra check niet geleverd)
      $filt .= "AND produceDate>='" . $from . "' ";
      $filt .= "AND createdVia='" . Quotations::CREATED_VIA_WIZARD . "' ";
      $filt .= "ORDER BY productionDate ";
      $quotations = Quotations::find_all_by(["flaggedForDeletion" => 0], $filt);

      $orders_grouped = [];
      foreach ($quotations as $quotation) {
        //pd($quotation->userId . " " . $quotation->getQuotationDate() . " " . $quotation->getProduceDate());

        $user = SandboxUsers::find_by(["userId" => $quotation->userId]);
        if (!$user) continue;
        $group_key = "u-" . $user->userId; //groeperen per userid
        $company = CrmCompanies::find_by(["companyId" => $user->companyId]);
        if ($company) {
          $group_key = $company->companyId; //groeperen per bedrijf
        }


        if (!isset($orders_grouped[$group_key])) {
          $orders_grouped[$group_key] = [
            "company"    => $company ?? null,
            "users"      => [],
            "quotations" => [],
          ];
        }

        $orders_grouped[$group_key]["users"][$user->userId] = $user;

        $gps_rde = GpsbuddyRde::find_by(["quotationId" => $quotation->quotationId]);
        if ($gps_rde) {
          $quotation->route = GpsbuddyRoutes::find_by(["routeId" => $gps_rde->routeId]);
        }
        $orders_grouped[$group_key]["quotations"][] = $quotation;

      }

      foreach ($orders_grouped as $order) {

        $tos = [];
        foreach ($order["users"] as $luser) {
          if ($luser->noti_produced_mail == 0) continue;
          $tos[$luser->email] = $luser->email;
        }

        if (count($tos) == 0) continue; //geen enkele user wil deze email.

        $message = "";

        $message .= "Geachte heer/mevrouw,<br />
          <br />
          Vandaag zijn één of meerdere van uw bestellingen geproduceerd.<br/>
          In deze email vind u een overzicht van de productie status van uw bestelling(en).<br/>
          <br/>";

        $message .= '<table class="simple">';
        $message .= '<tr>';
        $message .= '<td><b>Bestelnummer</b></td>';
        $message .= '<td><b>Status</b></td>';
        $message .= '<td><b>Verwachte leverdatum</b></td>';
        $message .= '</tr>';

        /** @var Quotations $quotation */
        foreach ($order["quotations"] as $quotation) {
          $message .= '<tr>';
          $message .= '<td>' . $quotation->getQuotationNumberFull() . '</td>';
          $message .= '<td>' . Status::getName($quotation->statusId) . '</td>';
          $message .= '<td>' . (isset($quotation->route) ? $quotation->route->getDate() : 'Week ' . $quotation->dueDateWeek) . '</td>';
          $message .= '</tr>';
        }

        $message .= '</table>';

        $message .= '<br/>U kunt ook ten allen tijde inloggen op <a href="https://www.raamdorpel.nl/offerte">www.raamdorpel.nl</a> om de status van uw bestellingen in te zien.<br/>';
        $message .= 'Wilt u deze notificatie e-mail niet meer ontvangen, dan kun u dit uitzetten in uw account bij <i>Mijn instellingen</i>.<br/><br/>';
        $message .= "<br/>Met vriendelijke groet,<br/><br/>";
        $message .= "Raamdorpelelementen BV<br/><br/>";

        $subject = 'Overzicht geproduceerde bestellingen';

        $bericht = (new WrapperService($message))->wrap();

        $gsdmailer = GsdMailer::build($tos, $subject, $bericht);
        $gsdmailer->send();

      }

    }

    /**
     * Verzend nieuwe bestellingen mail naar breti
     * @param Quotations $quotation
     */
    public static function sendSupplierNeworders() {

      $filt = " JOIN quotations_extra ON quotations_extra.quotationId=quotations.quotationId  ";
      $filt .= "WHERE 1 ";
      $filt .= "AND DATE(supplier_show)='" . date("Y-m-d") . "' ";
      $filt .= "AND brandId IN (5,6,7) ";
      $filt .= " AND statusId>=30 AND statusId<=38  ";
      $filt .= "ORDER BY dueDate";
      $quotations = Quotations::find_all($filt);

      if (count($quotations) == 0) return;

      $subject = "Nieuwe bestellingen te produceren voor Raamdorpelelementen BV";

      $message = "Beste leverancier,<br/><br/>";
      $message .= "Er zijn nieuwe bestellingen welke in productie mogen.<br/>";
      $message .= 'Je kunt inloggen op <a href="https://api.raamdorpel.nl">api.raamdorpel.nl</a> voor meer informatie.<br/><br/>';
      $message .= "Het gaat om de volgende bestelnummers:<br/><br/>";
      foreach ($quotations as $quotation) {
        $message .= "- " . $quotation->getQuotationNumberFull() . "<br/>";
      }
      $message .= "<br/>";
      $message .= "<br/><i>Dit bericht is automatisch gegenereerd.</i><br/><br/><br/>";

      $bericht = (new WrapperService($message))->wrap();

      $gsdmailer = GsdMailer::build("<EMAIL>", $subject, $bericht);
      $gsdmailer->send();
    }

    /**
     * Send Breti done mail to Bart
     * @param Quotations $quotation
     * @return void
     */
    public static function sendBretiDoneEmail($quotation) {

      $filepath_gen = CargoReceipt::getProductionstaatPdf([$quotation->quotationId]);
      $filename_new = $quotation->getQuotationNumberFull() . '_productiestaat_rde.pdf';

      $message = "Beste,<br/><br/>";
      $message .= "Bestelling  " . $quotation->getQuotationNumberFull() . " kan worden opgehaald bij Breti.<br/>";
      $message .= "Aantal meters: " . round($quotation->meters, 1) . "<br/><br/>";
      $message .= "<i>Dit bericht is automatisch gegenereerd.</i>";

      $gsd_mailer = GsdMailer::build('<EMAIL>', "Breti bestelling ophalen: " . $quotation->getQuotationNumberFull(), $message);
      $gsd_mailer->addFile($filepath_gen, $filename_new);
      $gsd_mailer->send();
    }

    public static function sendInvoiceReminder($id, $reminder_nr, $templatecode) {
      $invoice = Invoices::find_by(['invoiceId' => $id]);
      if (!$invoice) return;
      $user = SandboxUsers::find_by(['userId' => $invoice->userId]);
      $invoice_party = CrmInvoiceparties::find_by(['companyId' => $user->companyId]);
      //$quotation = Quotations::find_by(['invoiceId' => $invoice->invoiceId]);
      //$file = Files::find_by(['quotationId' => $quotation->quotationId]);
      $quotations = Quotations::find_all_by(['invoiceId' => $invoice->invoiceId]);

      $projects = [];
      foreach ($quotations as $quotation) {
        $projects[] = $quotation->projectName . " (" . $quotation->quotationNumber . "), ";
      }

      if ($reminder_nr == 1) {
        $invoice->reminder1 = date("Y-m-d");
      }
      elseif ($reminder_nr == 2) {
        $invoice->reminder2 = date("Y-m-d");
      }
      elseif ($reminder_nr == 3) {
        $invoice->reminder3 = date("Y-m-d");
      }

      $email = $invoice_party->email;
      if (!empty($invoice_party->emailReminder)) {
        $email = $invoice_party->emailReminder;
      }

      if (!$user->hasCompany()) {
        if (!empty($user->invoice_email)) {
          $email = $user->invoice_email;
        }
        else {
          $email = $user->email;
        }
      }

      $files = [];

      $pdf = new InvoicePdf($invoice->invoiceId);
      $filename = $pdf->generatePdf();
      $files[] = DIR_TEMP . $filename;

      $comm_template = CommunicationTemplate::getByCodeAndLanguage($templatecode);
      $bericht = (new WrapperServiceCT($comm_template->getContent()))->wrap();
      $subject = $comm_template->getContent()->subject;

      $bericht = str_replace("{{date_today}}", date("Y-m-d"), $bericht);
      $bericht = str_replace("{{invoice_number}}", $invoice->invoiceNumber, $bericht);
      $bericht = str_replace("{{invoice_date}}", $invoice->dateInvoice, $bericht);
      $bericht = str_replace("{{total_project_value}}", $invoice->totalProjectValue, $bericht);
      $bericht = str_replace("{{project}}", implode(' ', $projects), $bericht);


      $invoice->save();
      $gsd_mailer = GsdMailer::build($email, $subject, $bericht);
      $gsd_mailer->addCc(MAIL_FROM);
      $gsd_mailer->setFiles($files);
      $gsd_mailer->send();
    }

    public static function sendNewInvoice($invoice, $mail_template) {
      $mayInvoice = true;
      $invoice = Invoices::find_by(['invoiceId' => $invoice->invoiceId]);
      $user = SandboxUsers::find_by(['userId' => $invoice->userId]);
      $company = CrmCompanies::find_by(['companyId' => $user->companyId]);
      $invoice_party = CrmInvoiceparties::find_by(['companyId' => $company->companyId]);

      $comm_template = CommunicationTemplate::getByCodeAndLanguage($mail_template);
      $bericht = (new WrapperServiceCT($comm_template->getContent()))->wrap();

      $subject = $comm_template->getContent()->subject;
      $subject = str_replace("{{invoice_number}}", $invoice->invoiceNumber, $subject);
      $bericht = str_replace("{{invoice_number}}", $invoice->invoiceNumber, $bericht);

      $invoiceMessage = $invoice->mollie_id ?
        'De betaling is ontvangen en de factuur is betaald.' :
        'Wij verzoeken u vriendelijk om voor tijdige betaling zorg te dragen.';

      $bericht = str_replace("{{invoice_message}}", $invoiceMessage, $bericht);

      if (!$user->hasCompany()) {
        if ($user->invoice_email != '') {
          $email = $user->invoice_email;
        }
        else {
          $email = $user->email;
        }
      }
      else {
        $email = $invoice_party->email;
      }

      if (!ValidationHelper::isEmail($email)) {
        $email = MAIL_FROM; // fall back to rde if the set email is invalid
        $subject .= ' Let op: geen geldige email aangegeven';
        $mayInvoice = false;
      }

      $filenames = [];

      //check of dit bedrijf een uitvoerdersbon nodig heeft
//      dumpe($company->executorTicket);
      if ($company->executorTicket) {
        $quotations = Quotations::find_all_by(['invoiceId' => $invoice->invoiceId]);
        $files = [];
        foreach ($quotations as $quotation) {
          $all_files = Files::find_all_by(['quotationId' => $quotation->quotationId, 'categoryId' => 3]);
          if (empty($all_files)) continue;
          $files[] = $all_files;
        }
        if (empty($files)) {
          $email = MAIL_FROM;
          $subject .= ' Let op: geen uitvoerdersbon';
          $mayInvoice = false;
        }
      }

      if ($mayInvoice) {

        //factuurdatum zetten
        $invoice->dateInvoice = date("Y-m-d");
        $invoice->save();

        //review mails zetten
        (new PrepareReviews($invoice))->execute();

        //versturen naar multivers
        $user = User::getUserWithOrganById(ADMIN_DEFAULT_ID);
        $multivers = Multivers::find_by(["organisation_id" => $user->organisation_id]);
        $multivers_api = new MultiversApi($multivers);
        $invoice_service = new InvoiceSave($multivers_api);
        try {
          if (!DEVELOPMENT && $invoice_service->add($invoice) === false) {
            logToFile("multivers_error", "Niet verzonden, factuurnummer " . $invoice->invoiceNumber . ': onbekende fout');
          }
        }
        catch (MultiversException $e) {
          logToFile("multivers_error", "Niet verzonden, factuurnummer " . $invoice->invoiceNumber . "\n" . $e->getMessage() . ' ' . $e->getTraceAsString());
        }

        //aanmaken pdf
        $pdf = new InvoicePdf($invoice->invoiceId);
        $filenames[] = DIR_TEMP . $pdf->generatePdf();
      }

      unset($_SESSION['invoice_not_send']);

      $gsd_mailer = GsdMailer::build($email, $subject, $bericht);
      $gsd_mailer->addCc(MAIL_FROM);
      $gsd_mailer->setFiles($filenames);
      $gsd_mailer->send();
    }

  }
