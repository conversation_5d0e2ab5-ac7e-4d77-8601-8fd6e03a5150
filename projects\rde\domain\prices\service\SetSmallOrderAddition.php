<?php

  namespace domain\prices\service;

  use OrderElements;
  use Projects;
  use Quotations;

  /**
   * Helper to add a "Klein ordertoeslag"
   */
  class SetSmallOrderAddition {

    const SMALL_ORDER_ADDITION_PRODUCT_ID = 1312;

    private Quotations $quotation;
    /** @var OrderElements[] */
    private array $elements;
    /** @var Projects[] */
    private array $projects;

    public function __construct(Quotations $quotation, array $elements, array $projects) {
      $this->quotation = $quotation;
      $this->elements = $elements;
      $this->projects = $projects;
    }

    public function hasSmallOrderAddition(): bool {

      //alleen bij beton
      if ($this->quotation->brandId != 3) return false;

      $totalLength = 0;
      foreach ($this->elements as $element) {
        $totalLength += $element->elementLengthTotal;
      }

      return $totalLength < 3000;
    }

    /**
     * Does this order already contain the small order addition?
     * For instance when an order is edited.
     * @return bool
     */
    public function containsSmallOrderAddition(): bool {
      if (!empty($this->projects)) {
        foreach ($this->projects as $project) {
          if ($project->product_id == self::SMALL_ORDER_ADDITION_PRODUCT_ID) {
            return true;
          }
        }
      }
      return false;
    }


    /**
     * Get small order project, to add to quotation
     * @return Projects
     */
    public function getProject(): Projects {
      $project = new Projects();
      $project->name = 'Klein ordertoeslag';
      $project->pieceprice = 25;
      $project->euro = 25;
      $project->quotationId = $this->quotation->quotationId;
      $project->orderNr = 1;
      $project->product_id = self::SMALL_ORDER_ADDITION_PRODUCT_ID;
      $project->productFromWebshop = 0;
      $project->webshopOnly = 0;
      $project->showOnProductionPage = 0;
      return $project;
    }

  }