<?php $price = StringHelper::getPriceDot($product->getPriceByUser()); ?>
<div class="col4 col6-s col12-xs">
  <div class="product-col">
    <div>
      <a href="<?php echo $product->getShopUrl() ?>" class="product-img-a">
        <?php if($product->getPhoto(true)): ?>
          <img src="<?php echo $product->getPhoto(true) ?>" alt="<?php echo escapeForInput($product->getName($_SESSION['lang'])) ?>" title="<?php echo escapeForInput($product->getName($_SESSION['lang'])) ?>" class="img-responsive" />
        <?php else: ?>
            <img class="img-responsive" src="<?php echo $_SESSION['site']->getTemplateUrl() ?>images/noimage.jpg" alt="<?php echo escapeForInput($product->getName($_SESSION['lang'])) ?>"/>
        <?php endif; ?>
      </a>
      <?php if($product->discount==1 && $product->price_discount_part>0): ?>
        <span class="discountflag bg-red ft-white">AANBIEDING</span>
      <?php endif; ?>
      <div class="price-dot-circle bg-white ft-white align-center">
				<span class="priceDot <?php if ($product->price_on_request) echo 'price-on-request'?> <?php if ($product->discount==1 && $product->price_discount_part > 0) echo 'priceDotDiscount'?>">
            <?php echo $product->getFormattedPrice() ?>
				</span>
      </div>
    </div>
    <div class="prcaption">
      <h4 class="ft-green">
        <a href="<?php echo $product->getShopUrl() ?>"><?php echo $product->getName($_SESSION['lang']) ?></a>
      </h4>
      <?php if($product->discount==1 && $product->price_discount_part>0): ?>
        <div class="price">
          <span class="price-old">
             Aanbieding: <del>€ <?php echo getLocalePrice($product->getPricePart(true)) ?></del>
          </span>
        </div>
      <?php endif; ?>
      <a class="btn btn-cart bg-moreinfo" href="<?php echo $product->getShopUrl() ?>" title="<?php echo escapeForInput($product->getName($_SESSION['lang'])) ?>">
        MEER INFO
        <i class="fa fa-chevron-circle-right ft-yellow bg-grey"></i>
      </a>
    </div>
  </div>
</div>
