<?php
class BaseInvoices extends AppModel
{
  const DB_NAME = 'rde_b1mo';
  const TABLE_NAME = 'invoices';
  const OM_CLASS_NAME = 'Invoices';
  const columns = ['invoiceId', 'userId', 'reminder1', 'reminder2', 'reminder3', 'reminder4', 'invoiceNumber', 'dateInvoice', 'data', 'paid', 'freightCosts', 'meterPrice', 'meterAmount', 'constructionList', 'mattingPickupDiscount', 'invoiceNotes', 'invoiceNotesAlert', 'debit', 'irrecoverable', 'totalProjectValue', 'iSent', 'rSent1', 'rSent2', 'rSent3', 'vatRegShifted', 'paid_with', 'mollie_id', 'send_multivers', 'payment_1', 'payment_2', 'payment_3', 'payment_4', 'payment_1_date', 'payment_2_date', 'payment_3_date', 'payment_4_date'];
  const field_structure = [
    'invoiceId'                   => ['type' => 'int', 'length' => '9', 'null' => false],
    'userId'                      => ['type' => 'int', 'length' => '11', 'null' => false],
    'reminder1'                   => ['type' => 'date', 'length' => '', 'null' => true],
    'reminder2'                   => ['type' => 'date', 'length' => '', 'null' => true],
    'reminder3'                   => ['type' => 'date', 'length' => '', 'null' => true],
    'reminder4'                   => ['type' => 'date', 'length' => '', 'null' => true],
    'invoiceNumber'               => ['type' => 'varchar', 'length' => '255', 'null' => false],
    'dateInvoice'                 => ['type' => 'date', 'length' => '', 'null' => true],
    'data'                        => ['type' => 'text', 'length' => '', 'null' => true],
    'paid'                        => ['type' => 'date', 'length' => '', 'null' => true],
    'freightCosts'                => ['type' => 'varchar', 'length' => '255', 'null' => true],
    'meterPrice'                  => ['type' => 'varchar', 'length' => '255', 'null' => true],
    'meterAmount'                 => ['type' => 'varchar', 'length' => '255', 'null' => true],
    'constructionList'            => ['type' => 'varchar', 'length' => '255', 'null' => true],
    'mattingPickupDiscount'       => ['type' => 'varchar', 'length' => '255', 'null' => true],
    'invoiceNotes'                => ['type' => 'text', 'length' => '', 'null' => true],
    'invoiceNotesAlert'           => ['type' => 'boolean', 'length' => '1', 'null' => false],
    'debit'                       => ['type' => 'boolean', 'length' => '1', 'null' => false],
    'irrecoverable'               => ['type' => 'int', 'length' => '11', 'null' => false],
    'totalProjectValue'           => ['type' => 'decimal', 'length' => '8,2', 'null' => false],
    'iSent'                       => ['type' => 'datetime', 'length' => '', 'null' => true],
    'rSent1'                      => ['type' => 'datetime', 'length' => '', 'null' => true],
    'rSent2'                      => ['type' => 'datetime', 'length' => '', 'null' => true],
    'rSent3'                      => ['type' => 'datetime', 'length' => '', 'null' => true],
    'vatRegShifted'               => ['type' => 'int', 'length' => '2', 'null' => false],
    'paid_with'                   => ['type' => 'varchar', 'length' => '20', 'null' => false],
    'mollie_id'                   => ['type' => 'mediumint', 'length' => '8', 'null' => true],
    'send_multivers'              => ['type' => 'boolean', 'length' => '1', 'null' => false],
    'payment_1'                   => ['type' => 'decimal', 'length' => '8,2', 'null' => true],
    'payment_2'                   => ['type' => 'decimal', 'length' => '8,2', 'null' => true],
    'payment_3'                   => ['type' => 'decimal', 'length' => '8,2', 'null' => true],
    'payment_4'                   => ['type' => 'decimal', 'length' => '8,2', 'null' => true],
    'payment_1_date'              => ['type' => 'datetime', 'length' => '', 'null' => true],
    'payment_2_date'              => ['type' => 'datetime', 'length' => '', 'null' => true],
    'payment_3_date'              => ['type' => 'datetime', 'length' => '', 'null' => true],
    'payment_4_date'              => ['type' => 'datetime', 'length' => '', 'null' => true],
  ];

  protected static $primary_key = ['invoiceId'];
  protected $auto_increment = 'invoiceId';

  public $invoiceId, $userId, $reminder1, $reminder2, $reminder3, $reminder4, $invoiceNumber, $dateInvoice, $data, $paid, $freightCosts, $meterPrice, $meterAmount, $constructionList, $mattingPickupDiscount, $invoiceNotes, $invoiceNotesAlert, $debit, $irrecoverable, $totalProjectValue, $iSent, $rSent1, $rSent2, $rSent3, $vatRegShifted, $paid_with, $mollie_id, $send_multivers, $payment_1, $payment_2, $payment_3, $payment_4, $payment_1_date, $payment_2_date, $payment_3_date, $payment_4_date;

  function __construct($obj_arr = [], &$error_codes = []) {
    parent::__construct();
    $this->setDefaults();
    if (!empty($obj_arr)) {
      $this->from_array($obj_arr);
      $this->valid_required_fields($error_codes);
    }
  }

  /**
  * Set default object values
  **/
  public function setDefaults() {
    $this->userId = 0;
    $this->invoiceNotesAlert = 0;
    $this->debit = 0;
    $this->irrecoverable = 0;
    $this->totalProjectValue = 0.00;
    $this->vatRegShifted = 3;
    $this->paid_with = 'bank';
    $this->send_multivers = 0;
  }

  /**
   * @param array $conditions (required)
   * @param string $raw_sql (optional)
   * @return Invoices[]
   **/
  public static function find_all_like($conditions, $raw_sql = '') {
    return parent::generic_find_all_like($conditions, static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param array $conditions (required)
   * @param string $raw_sql (optional)
   * @return Invoices[]
   **/
  public static function find_all_by($conditions, $raw_sql = '') {
    return parent::generic_find_all_by($conditions, static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param string $raw_sql (optional)
   * @return Invoices[]
   **/
  public static function find_all($raw_sql = '') {
    return parent::generic_find_all(static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param array $conditions (required)
   * @param string $raw_sql (optional)
   * @return Invoices
   **/
  public static function find_by($conditions, $raw_sql = '') {
    return parent::generic_find_by($conditions, static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param string $id (required)
   * @param string $raw_sql (optional)
   * @return Invoices
   **/
  public static function find_by_id($id, $raw_sql = '') {
    return parent::generic_find_by_id($id, static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param array $conditions (required)
   * @param string $raw_sql (optional)
   * @return int
   **/
  public static function count_all_by($conditions, $raw_sql = '') {
    return parent::generic_count_all_by($conditions, static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param array $conditions (required)
   * @param string $raw_sql (optional)
   * @return bool
   **/
  public static function delete_by($conditions, $raw_sql = '') {
    return parent::generic_delete_by($conditions, static::OM_CLASS_NAME, $raw_sql);
  }

}