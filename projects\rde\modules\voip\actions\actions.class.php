<?php
  class voipRdeActions extends voipActions {
  public function sendPushNotification($callType, $callerID): void {
    // get most recent call from the same caller id
    $recentCaller = RecentCallers::find_by(['callerId' => $callerID], 'ORDER BY callerAt DESC');

    // if the same caller id was used in the last 10 seconds, do not send the notification again (spam prevention)
    if ($recentCaller && strtotime($recentCaller->callerAt) > (time() - 10)) return;

    // save the caller id to the database
    $recentCaller = new RecentCallers([
      'callerId' => $callerID,
      'callerAt' => date('Y-m-d H:i:s'),
    ]);
    $recentCaller->save();

    parent::sendPushNotification($callType, $callerID);
  }
}
