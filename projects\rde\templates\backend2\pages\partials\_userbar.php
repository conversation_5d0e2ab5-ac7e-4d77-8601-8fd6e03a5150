<div class="userbar">

  <?php TemplateHelper::includeBackendIndexPartial("_userbar_logo.php", $current_action); ?>

  <div class="user-actions">

    <?php if (isset($_SESSION['userObject'])): ?>

      <div style="font-size: 1.4rem;">
        <a href="<?php echo SiteHost::getMainSiteHost(1)->getDomainSmart(true); ?>/offerte?adminhash=<?php echo EncryptionHelper::encrypt('ADMINUSER#15xx' . date('Ymd'), "#34tkdfQQss") ?>" target="_blank" style="padding-right: 15px;"><i class="ki-solid ki-double-right-arrow"></i> OFFERTE ADMIN</a>
        <a href="https://www.raamdorpel.nl/cms/crm/menu.php" target="_blank"><i class="ki-solid ki-exit-up"></i> OUDE CMS</a>
      </div>

      <?php if (isset($_SESSION['was_admin']) && $_SESSION['was_admin'] != ""): ?>
        <a href="#return2admin" id="return2admin" title="TERUG NAAR UW ACCOUNT"><i class="ki-solid ki-arrow-up-left"></i></a>
      <?php endif; ?>

      <?php TemplateHelper::includeBackendIndexPartial("_userbar_usermenu.php", $current_action); ?>

      <?php if ($_SESSION['userObject']->organisation->name != ""): ?>
        <div class="companyname"><?php echo $_SESSION['userObject']->organisation->name ?> </div>
      <?php endif; ?>

      <?php TemplateHelper::includeBackendIndexPartial("_userbar_notifications.php", $current_action); ?>

      <?php TemplateHelper::includeBackendIndexPartial("_userbar_language.php", $current_action); ?>


    <?php endif; ?>
  </div>

</div>
