<?php TemplateHelper::includePartial("_tabs.php","other") ?>
<h3>Bericht bewerken</h3>

<?php writeErrors($errors, true); ?>

<form action='<?php echo reconstructQuery() ?>' method='post' class="edit-form">
  <table class="default_table">
    <tr class="dataTableHeadingRow">
      <td>Eigenschap</td>
      <td>Waarde</td>
    </tr>
    <tr class="dataTableRow">
      <td class="head">Online</td>
      <td><input type="checkbox" value="1" <?php writeIfCheckedVal($imessage->online,1) ?> name="online" id="online"/></td>
    </tr>
    <tr class="dataTableRow">
      <td class="head">Datum</td>
      <td><?php echo getDateSelector('date',$imessage->getDate()) ?></td>
    </tr>
    <tr class="dataTableRow">
      <td class="head">Onderwerp</td>
      <td><input type="text" value="<?php echo escapeForInput($imessage->subject) ?>" name="subject" id="subject"/></td>
    </tr>
    <tr class="dataTableRow">
      <td class="head">Homepage</td>
      <td>
        <input type="checkbox" value="1" <?php writeIfCheckedVal($imessage->homepage,1) ?> name="homepage" id="homepage"/>
        <?php echo showHelpButton("Is dit bericht gemarkeerd als homepage, dan is dit bericht in zijn geheel zichtbaar op de homepage en niet in de berichten bak.")?>
      </td>
    </tr>
    <tr class="dataTableRow">
      <td class="head">Bouwbedrijven</td>
      <td><input type="checkbox" value="1" <?php writeIfCheckedVal($imessage->construction,1) ?> name="construction" id="construction"/></td>
    </tr>
    <tr class="dataTableRow">
      <td class="head">Handelaren</td>
      <td><input type="checkbox" value="1" <?php writeIfCheckedVal($imessage->constructiontraders,1) ?> name="constructiontraders" id="constructiontraders"/></td>
    </tr>
    <tr class="dataTableRow">
      <td class="head">Particulieren</td>
      <td><input type="checkbox" value="1" <?php writeIfCheckedVal($imessage->private,1) ?> name="private" id="private"/></td>
    </tr>
    <tr class="dataTableRow">
      <td class="head">Bericht</td>
      <td>
        <textarea class="ckeditor" name="message" id="message"><?php echo $imessage->message; ?></textarea>
      </td>
    </tr>
    </table>

  <input type="submit" name="verzend" value="Opslaan" class="gsd-btn gsd-btn-primary"/>
  <input type="submit" name="verzend_list" value="Opslaan en naar lijst" class="gsd-btn gsd-btn-secondary"/>
</form>
<?php echo TemplateHelper::includeJavascript('/gsdfw/includes/ckeditor4/ckeditor'); ?>
<?php echo TemplateHelper::includeJavascript('/gsdfw/includes/jsscripts/ckeditor4'); ?>
<script type="text/javascript">

  $(document).ready(function(){

    ckeditorInit();
    CKEDITOR.config['contentsCss'] = CKEDITOR.config['contentsCss'].concat([<?php echo getCkeditorStylesheets() ?>]);
    ckeditorScaytNl();
    ckeditorAllowScripts();

    <?php $_SESSION['filebrowserPath'] = DIR_UPLOADS.'inbox';?>
    CKEDITOR.config['filebrowserBrowseUrl'] = '//<?php echo Context::getSiteDomain() . URL_INCLUDES ?>ckeditor4/RichFilemanager/index.html';

  });
</script>
