<?php

  /**
   * Class clsContainerTag
   */
  class clsContainerTag {

    /**
     * @var int $containerId
     */
    private $containerId;

    /**
     * @var string $containerNumber
     */
    private $containerNumber;

    /**
     * @var array $tagIds
     */
    private $tagIds;

    /**
     * @var string $tagId
     */
    private $tagId;

    /**
     * @return int
     */
    public function getContainerId() {
      return $this->containerId;
    }

    /**
     * @param int $containerId
     */
    public function setContainerId($containerId) {
      $this->containerId = $containerId;
    }

    /**
     * @return string
     */
    public function getContainerNumber() {
      return $this->containerNumber;
    }

    /**
     * @param string $containerNumber
     */
    public function setContainerNumber($containerNumber) {
      $this->containerNumber = $containerNumber;
    }

    /**
     * @return array
     */
    public function getTagIds() {
      return $this->tagIds;
    }

    /**
     * @param array $tagIds
     */
    public function setTagIds($tagIds) {
      $this->tagIds = $tagIds;
    }

    /**
     * @return string
     */
    public function getTagId() {
      return $this->tagId;
    }

    /**
     * @param string $tagId
     */
    public function setTagId($tagId) {
      $this->tagId = $tagId;
    }

    /**
     * @return bool|string
     *
     * // checkContainerNumberExists
     * // return containerId
     */
    public function checkContainerNumberExists() {
      $container = Containers::find_by(["containerNumber" => $this->getContainerNumber()]);
      if ($container) return $container->containerId;
      return false;
    }

    /**
     * @return bool
     */
    public function addTag() {
      $tag = new ContainerTag();
      $tag->containerId = $this->containerId;
      $tag->tagId = $this->tagId;
      $tag->scanDate = date("Y-m-d H:i:s");
      $tag->save();
      return true;
    }

    /**
     * @return string
     */
    public function addTags() {
      $error = false;
      $errorData = '';

      $this->getContainerNumber();
      $resultContainerId = $this->checkContainerNumberExists();
      if ($resultContainerId !== false) {

        //-- check all tagID's if one is empty
        foreach ($this->tagIds as $key => $tagId) {
          if ($tagId === '') {
            $errorData .= 'TAG ID IS EMPTY:' . $tagId . ': - ';
            $error = true;
          }
        }

        //-- alleen tags toevoegen als ze allemaal een waarde hebben
        if ($error === false) {
          foreach ($this->tagIds as $key => $tagId) {
            $this->setContainerId($resultContainerId);
            $this->setTagId($tagId);
            $returnAddTag = $this->addTag();
            if ($returnAddTag === false) {
              $errorData .= 'INSERTSQL TAGID:' . $tagId . ': HAS FAILED ADDTAG - ';
              $error = true;
            }
          }
        }

      }
      else {
        $errorData .= 'Containernummer bestaat niet: ' . $this->containerNumber;
        $error = true;
      }

      if ($error === true) {
        RestUtils::sendResponseError($errorData);
      }
      else {
        RestUtils::sendResponseOK("Tag is succesvol gekoppeld aan deze container.");
      }

    }

  }
