<?php

  use domain\multivers\service\InvoicePaidSave;
  use domain\multivers\service\InvoiceSave;
  use domain\multivers\service\InvoiceSyncStatus;
  use gsdfw\domain\multivers\exception\MultiversException;
  use gsdfw\domain\multivers\service\MultiversApi;

  class multiversRdeActions extends multiversActions {

    public function executeInvoices() {

      $user = User::getUserWithOrganById(ADMIN_DEFAULT_ID);
      $multivers = Multivers::find_by(["organisation_id" => $user->organisation_id]);
      $multivers_api = new MultiversApi($multivers);

      if (isset($_GET["go"])) {

        set_time_limit(600); //10 minuten
        ini_set('memory_limit', '512M');
        ini_set('max_execution_time', 600); // 300 sec = 10 min


        //      testje ophalen invoice informatie:
        //      $customer_invoice = new CustomerInvoice($multivers_api);
        //      pd($customer_invoice->retrieveById("30340"));
        //      ResponseHelper::exit();

        if ($_GET["go"] == "syncinvoicestatus") {
          try {
            $sync_invoice_status = new InvoiceSyncStatus($multivers_api);
            $sync_invoice_status->sync();
            $this->sync_invoice_status = $sync_invoice_status;
          }
          catch (MultiversException $e) {
            MessageFlashCoordinator::addMessageAlert("Sync mislukt: " . $e->getMessage());
            logToFile("multivers_error", 'Foutmelding: ' . $e->getMessage() . ' ' . $e->getTraceAsString());
            ResponseHelper::redirect(reconstructQueryAdd());
          }
        }
        elseif ($_GET["go"] == "addinvoice") {
          $invoiceNumber = $_POST["invoiceNumber"];
          $invoice = Invoices::find_by(["invoiceNumber" => $invoiceNumber]);
          if (!$invoice) {
            MessageFlashCoordinator::addMessageAlert("Factuur met factuurnnummer " . $invoiceNumber . " niet gevonden.");
            ResponseHelper::redirect(reconstructQueryAdd());
          }
          if ($invoice->getDateInvoice() == "") {
            MessageFlashCoordinator::addMessageAlert("Factuur met factuurnnummer " . $invoiceNumber . " is nog niet gefactureerd, en kan nog niet verzonden worden.");
            ResponseHelper::redirect(reconstructQueryAdd());
          }

          //test invoiceid = 14459 heeft invoicenumber 29565
          try {
            $invoice_service = new InvoiceSave($multivers_api);
            $result = $invoice_service->add($invoice);
            if ($result === false) {
              MessageFlashCoordinator::addMessageAlert("Factuur niet verzonden.");
            }
            else {
              MessageFlashCoordinator::addMessage("Factuur " . $invoiceNumber . " succesvol verzonden.");
              logToFile("multivers_succes", $invoiceNumber . ': verzonden');
            }
          }
          catch (MultiversException $e) {
            MessageFlashCoordinator::addMessageAlert("Factuur niet verzonden: " . $e->getMessage());
            logToFile("multivers_error", 'Factuur niet verzonden: ' . $e->getMessage() . ' ' . $e->getTraceAsString());
          }
          ResponseHelper::redirect(reconstructQueryAdd());
        }
        elseif ($_GET["go"] == "sendinvoices") {
          $invoice_service = new InvoiceSave($multivers_api);
          $invoices = Invoices::getNotInMultiversInvoices("LIMIT 40");
          $send_invoices_result = [];
          try {
            foreach ($invoices as $invoice) {
              $result = false;
              if (!DEVELOPMENT) {
                $result = $invoice_service->add($invoice);
              }
              if ($result === false) {
                $send_invoices_result[] = $invoice->invoiceNumber . ': onbekende fout';
              }
              else {
                $send_invoices_result[] = $invoice->invoiceNumber . ': verzonden';
                logToFile("multivers_succes", $invoice->invoiceNumber . ': verzonden');
              }
            }
          }
          catch (MultiversException $e) {
            $send_invoices_result[] = 'Foutmelding verzenden facturen: ' . $e->getMessage();
            logToFile("multivers_error", 'Foutmelding verzenden facturen: ' . $e->getMessage() . ' ' . $e->getTraceAsString());
          }
          $this->send_invoices_result = $send_invoices_result;
        }
        elseif ($_GET["go"] == "synconlinepaid") {
          $invoice_paid_service = new InvoicePaidSave($multivers_api);
          $invoices = Invoices::getOnlinepaidInvoices("AND paid>='" . date("Y-m-d", strtotime("-7 DAY")) . "' ");
//          $invoices = [Invoices::find_by(["invoiceNumber"=>30730])];
          $synconlinepaid_result = [];
          if (count($invoices) == 0) {
            $synconlinepaid_result[] = 'Geen facturen gevonden in de afgelopen 7 dagen';
          }
          elseif (!DEVELOPMENT) {
            try {
              $invoice_paid_result = $invoice_paid_service->setpaid($invoices);
              if ($invoice_paid_result === false) {
                $synconlinepaid_result[] = 'Foutmelding op betaald zetten: onbekende fout';
              }
              else {
                if (count($invoice_paid_service->getChangedInvoices()) == 0) {
                  $synconlinepaid_result[] = 'Alle online facturen stonden reeds op betaald. (' . count($invoices) . ' stuks)';
                }
                else {
                  foreach ($invoice_paid_service->getChangedInvoices() as $invoiceNr) {
                    $synconlinepaid_result[] = $invoiceNr . ': betaald gezet';
                    logToFile("multivers_succes", $invoiceNr . ': betaald gezet');
                  }
                }
              }
            }
            catch (MultiversException $e) {
              $synconlinepaid_result[] = 'Foutmelding op betaald zetten: ' . $e->getMessage();
              logToFile("multivers_error", 'Foutmelding op betaald zetten: ' . $e->getMessage() . ' ' . $e->getTraceAsString());
            }
          }
          $this->synconlinepaid_result = $synconlinepaid_result;
        }


      }

      $this->multivers_api = $multivers_api;
    }

  }