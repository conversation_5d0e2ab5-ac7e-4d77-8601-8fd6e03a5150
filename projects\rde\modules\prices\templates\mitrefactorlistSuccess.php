<?php TemplateHelper::includePartial('_tabs.php', 'prices'); ?>

<form method="post" action="<?php echo reconstructQueryAdd() ?>">
    <div class="box">
      <input type="text" name="mp_search" value="<?php echo $_SESSION['mp_search'] ?>" placeholder="Zoeken..."/>
      <select name="mp_year" id="mp_year">
        <option value="now">Huidige factorlijst</option>
        <?php echo getOptionVal(2011, date("Y")+1, $_SESSION['mp_year']) ?>
      </select>
      <input type="submit" name="go" id="go" value="Zoeken" />
      <input type="submit" name="reset" id="reset" value="Reset filter" />
    </div>
  </form>
  <?php if($_SESSION['mp_year'] == "now"): ?>
    <div class="box">
      <form method="get">
        <input type="hidden" name="action" value="mitrefactoredit"/>
        Prijzen aanpassen welke ingaan vanaf 1 januari
        <select name="yearfrom" id="yearfrom">
          <?php echo getOptionVal(date("Y"), date("Y")+1, date("Y")+1) ?>
        </select>
        <input type="submit" name="edit" id="edit" value="Bewerk factorlijst" class="gsd-btn gsd-btn-primary" />
        <?php echo showHelpButton("Met deze knop kun je de verstekfactor per stuk aanpassen van de prijzen in deze lijst.","Bewerk factorlijst") ?>
      </form>

      <form method="post">
        <input type="hidden" name="action" value="mitrefactoredit"/>
        Toevoegen nieuwe factor
        <select name="missingsize" id="missingsize">
          <?php foreach($missing_sizes as $ms): ?>
            <option value="<?php echo $ms ?>"><?php echo $ms ?></option>
          <?php endforeach; ?>
        </select>
        <input type="submit" name="add" id="add" value="Toevoegen" />
        <?php echo showHelpButton("U kunt hier een factor toevoegen van een nieuw formaat. Deze word direct toegevoegd met factor 1 met startdatum 1 januari ".date("Y").", en kun je vervolgens bewerken.","Bewerk factorlijst") ?>
      </form>
    </div>
  <?php endif; ?>

  <?php $pager->writePreviousNext(); ?>

  <?php if(count($items)==0): ?>
    <section class="empty-list-state">
      <p><?php echo __('Er zijn geen items gevonden.') ?></p>
    </section>
  <?php else: ?>
    <table class="default_table" style="width: auto;">
      <tr class="dataTableHeadingRow">
        <td>Steenafmeting cm</td>
        <td style="text-align: right">Factor</td>
        <td style="text-align: center">Geldig van</td>
        <td style="text-align: center">Geldig tot</td>
      </tr>
      <?php
        /** @var MitrePrices $item */
        foreach($items as $item): ?>
        <tr class="dataTableRow trhover">
          <td><?php echo $item->stoneLength ?></td>
          <td style="text-align: right"><?php echo $item->factor ?></td>
          <td style="text-align: center"><?php echo $item->getValidFrom() ?></td>
          <td style="text-align: center"><?php echo $item->getValidTo()=="31-12-9999"?"-":$item->getValidTo() ?></td>
        </tr>
      <?php endforeach; ?>
    </table>
  <?php endif; ?>

<script>
  $(document).ready(function() {
    $("#mp_brand,#mp_display,#mp_type,#mp_endstone,#mp_year,.colors").change(function() {
      $("#go").click();
    })
  });
</script>
<style>
  #colors_wrapper {
  }
  #colors_wrapper label {
    padding: 5px 5px 0 0;
    display: inline-block;
  }

</style>