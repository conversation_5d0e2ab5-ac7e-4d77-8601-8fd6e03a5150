<section class="title-bar">
  <h1>
    <?php echo Navigation::getItem(Navigation::getActivePageId())->getName() ?>
  </h1>
  <?php TemplateHelper::includePartial("_tabs.php","other") ?>
</section>


<p id="code-error" class="notification-hidden alert alert-danger"></p>

<section class="module-description">
  <p>
    Op deze pagina kun je de koppeling tussen inbellen via de VOIP en de popupnotificaties instellen.<br>
    Indien 'Meldingen' en 'Pop-ups en omleidingen' op de site zijn toegestaan kun je je apparaat koppelen aan jouw account.<br>
    <PERSON>cht je deze hebben geblo<PERSON>, dan kun je deze via je browserinstellingen opnieuw instellen.
  </p>
</section>

<form action="<?php echo reconstructQueryAdd() ?>&action=saveToken" method="post" id="token-form">
  <label for="token">
    Jouw token
    <input id="token" name="token" type="text" readonly>
  </label>

  <input id="link-token" type="submit" class="gsd-btn gsd-btn-primary" value="Koppel token"/>
</form>

<?php if (!ArrayHelper::hasData($deviceTokens)): ?>
  <p>Er zijn nog geen apparaten gekoppeld.</p>
<?php else: ?>
  <table class="default_table">
    <tr class="dataTableHeadingRow">
      <td>Gebruiker</td>
      <td>Token</td>
      <td class="actions"></td>
    </tr>
    <?php foreach ($deviceTokens as $deviceToken): ?>
      <tr class="dataTableRow">
        <td><?php echo $deviceToken->userName; ?></td>
        <td><?php echo $deviceToken->value ?></td>
        <td><?php echo BtnHelper::getRemove(reconstructQueryAdd() . '&action=deletePushnotificationToken&tokenId=' . $deviceToken->id) ?></td>
      </tr>
    <?php endforeach; ?>
  </table>
<?php endif; ?>


<script type="module">
  // Import the functions you need from the SDKs you need
  import {initializeApp} from "https://www.gstatic.com/firebasejs/11.4.0/firebase-app.js";
  import {getMessaging, getToken} from "https://www.gstatic.com/firebasejs/11.4.0/firebase-messaging.js";

  // Your web app's Firebase configuration
  const configSettings = <?php echo json_encode(Config::get('FIREBASE_CONFIG')); ?>;
  if (!configSettings || Object.keys(configSettings).length === 0) {
    alert('Er is iets misgegaan met het ophalen van de configuratie.')
  }
  else {
    // Initialize Firebase
    const app = initializeApp(configSettings);
    const messaging = getMessaging(app);

    const existingToken = '<?php echo isset($deviceTokens[$_SESSION['userObject']->id]) ? $deviceTokens[$_SESSION['userObject']->id]->value : null ?>';
    const vapidKey = '<?php echo Config::get("FIREBASE_VAPID_KEY") ?>';

    if (Notification.permission === 'denied') {
      $("#code-error")
        .html("Je heb meldingen uitgeschakeld voor deze site. Om deze functionaliteit te kunnen gebruiken dien je permissie te geven vanuit je browserinstellingen.")
        .css('display', 'block');
    }
    else {
      navigator.serviceWorker.register("<?php echo TemplateHelper::getVersionedAsset('/projects/rde/includes/jsscripts/service-worker', '.js') ?>")
        .then(registration => {
          getToken(messaging, {
            serviceWorkerRegistration: registration,
            vapidKey: vapidKey
          })
            .then(async (currentToken) => {
              if (existingToken.length > 0 && existingToken !== currentToken) {
                $("#test-notification").css('display', 'none');
                $("#code-error")
                  .html("Token komt niet overeen met huidig apparaat. Klik op 'Koppel token' om deze opnieuw te koppelen.")
                  .css('display', 'block');
              }

              $("#token-form").css('display', 'inline-block');
              $("#token").val(currentToken);

            })
            .catch((err) => {
              console.log(err)
              $("#code-error")
                .html("Er is iets misgegaan. Probeer het later nog eens of neem contact op met GSD.")
                .css('display', 'block');
              console.error(err)
            });
        });
    }

  }

</script>

<style>
  .notification-hidden, #token-form {
    display: none;
  }

  #token-form {
    padding-bottom: 16px;
  }
</style>