<?php

  AppModel::loadBaseClass('BaseCargoReceipt');

  class CargoReceiptModel extends BaseCargoReceipt {


    /**
     * Get PDF Vrachtbon van live omgeving
     * @param int $cargo_receipt_id
     * @return string filepath
     */
    public static function getPdf($cargo_receipt_id) {
      $codeCheck = sha1(date('Ymd') . $cargo_receipt_id . 'Tf2S@gA3sDj*eE#ypOJKJ!heeuh');
      $url = "https://www.raamdorpel.nl/cms/crm/pdfgen/pdfCargoReciept.php?id=" . $cargo_receipt_id . "&code=" . $codeCheck;
      $filename = DIR_TEMP . $cargo_receipt_id . '.pdf';
      file_put_contents($filename, file_get_contents($url));
      return $filename;
    }


    /**
     * Get ProductionstaatPDF via url
     * @param $quotationIds
     * @return string filepath
     */
    public static function getProductionstaatPdf($quotationIds, $externalProductionpdf = false) {
      $quoteIds = implode('_', $quotationIds);
      $codeCheck = sha1(date('Ymd') . $quoteIds . 'Tf2S@gA3sDj*eE#ypOJKJ!heeuh');
      $url = SiteHost::getPrimary(1)->getDomainSmart(true);
      $url .= "/cms/crm/pdfgen/pdfProductionReceiptMultiple.php?group=" . $quoteIds . "&code=" . $codeCheck;
      if ($externalProductionpdf) {
        $url .= "&externalProductionpdf=1";
      }
      $filename = DIR_TEMP . $quoteIds . '.pdf';
      file_put_contents($filename, file_get_contents($url));
      return $filename;
    }

  }