<?php

  class planningRdeActions extends gsdActions {

    public function executeOverview() {
      $workers = Worker::getEmployees(0, 1);
      if (!isset($_SESSION["startdate"])) {
        $_SESSION["startdate"] = DateTimeHelper::getFirstOfWeekDate(date("Y-m-d"), 'U');
      }
      if (isset($_POST["volgende"])) {
        $_SESSION["startdate"] = strtotime("+7 DAYS", $_SESSION["startdate"]);
        ResponseHelper::redirect(reconstructQuery());
      }
      elseif (isset($_POST["vorige"])) {
        $_SESSION["startdate"] = strtotime("-7 DAYS", $_SESSION["startdate"]);
        ResponseHelper::redirect(reconstructQuery());
      }
      elseif (isset($_POST["vandaag"])) {
        unset($_SESSION["startdate"]);
        ResponseHelper::redirect(reconstructQuery());
      }

      $workersplanning = WorkerPlanhour::getPlanningForWeek($_SESSION["startdate"]);

      $sommatie = [];
      $sommatie['hourstotal'] = 0;
      foreach ($workers as $worker) {
        $worker->planning = [];
        if (isset($workersplanning[$worker->id])) {
          $worker->planning = $workersplanning[$worker->id];
        }
        $worker->verlof = $worker->getVerlof($_SESSION["startdate"]);
//      pd($worker->planning);
//      pd($worker->verlof);

        foreach ($worker->planning as $time => $hours) {
          if (!isset($sommatie[$time])) {
            $sommatie[$time]['hours'] = 0;
            $sommatie[$time]['employees'] = [];
          }
          $sommatie['hourstotal'] += $hours;
          $sommatie[$time]['hours'] += $hours;
          if ($hours > 0) {
            $sommatie[$time]['employees'][$worker->id] = $worker->id;
          }
        }
      }

      $this->sommatie = $sommatie;
      $this->workers = $workers;
    }

    public function executeHours() {

      if (isset($_POST['delall'])) { //verwijderen selectie
        if (!isset($_POST['selected'])) {
          $_SESSION['flash_message_red'] = "Selecteer minimaal 1 planuur.";
          ResponseHelper::redirect(reconstructQuery());
        }
        WorkerPlanhour::delete_by(["id" => $_POST['selected']]);
        $_SESSION['flash_message'] = "Planuren verwijderd.";
        ResponseHelper::redirect(reconstructQuery());
      }


      $this->pager = new Pager();
      $this->pager->setWriteCount(true);
      $this->pager->setRowsPerPage(100);
      $this->pager->handle();

      if (!isset($_SESSION['p_search'])) $_SESSION['p_search'] = '';
      if (isset($_POST['p_search'])) {
        $_SESSION['p_search'] = trim($_POST['p_search']);
      }

      if (!isset($_SESSION['p_searchuser'])) {
        $_SESSION['p_searchuser'] = '';
      }
      if (isset($_POST['p_searchuser'])) {
        $_SESSION['p_searchuser'] = trim($_POST['p_searchuser']);
        $_SESSION['sel_worker'] = Worker::find_by_id($_SESSION['p_searchuser']);
      }
      if (!isset($_SESSION['p_searchgroup'])) {
        $_SESSION['p_searchgroup'] = '';
      }
      if (isset($_POST['p_searchgroup'])) {
        $_SESSION['p_searchgroup'] = trim($_POST['p_searchgroup']);
      }
      if (!isset($_SESSION['p_searchout'])) {
        $_SESSION['p_searchout'] = '';
      }
      if (isset($_POST['p_searchout'])) {
        $_SESSION['p_searchout'] = trim($_POST['p_searchout']);
      }
      if (!isset($_SESSION['p_searchin'])) {
        $_SESSION['p_searchin'] = date("d-m-Y");
      }
      if (isset($_POST['p_searchin'])) {
        $_SESSION['p_searchin'] = trim($_POST['p_searchin']);
      }

      $filt = " WHERE 1";
      if ($_SESSION['p_search'] != "") {
        $searchStr = DbHelper::escape($_SESSION['p_search']);
        $filt .= " AND (";
        $filt .= "firstname LIKE '%" . $searchStr . "%' OR ";
        $filt .= "lastname LIKE '%" . $searchStr . "%' ";
        $filt .= ")";
      }
      if ($_SESSION['p_searchgroup'] != "") {
        $filt .= " AND worker.group=" . DbHelper::escape($_SESSION['p_searchgroup']);
      }
      $ids = [];
      foreach (Worker::find_all($filt) as $work) {
        $ids[] = $work->id;
      }

      $filt = " ";
      if ($_SESSION['p_searchuser'] != "") {
        $filt .= " AND worker_id=" . $_SESSION['p_searchuser'] . " ";
      }
      if ($_SESSION['p_searchin'] != '') {
        $filt .= " AND DATE(worker_planhour.date) >='" . date('Y-m-d', strtotime($_SESSION['p_searchin'])) . "'";
      }
      if ($_SESSION['p_searchout'] != '') {
        $filt .= " AND DATE(worker_planhour.date) <='" . date('Y-m-d', strtotime($_SESSION['p_searchout'])) . "'";
      }

      $workers_all = AppModel::mapObjectIds(Worker::find_all());
      $workers = [];
      foreach ($workers_all as $worker) {
        $workers[$worker->id] = $worker;
      }
      AppModel::mapObjectIds(Worker::find_all_by(["planning" => 1]));
      $hours = [];
      $count = 0;
      $verlof = [];
      if (count($ids) > 0) {
        $hours = WorkerPlanhour::find_all_by(["worker_id" => $ids], $filt . " ORDER BY date" . $this->pager->getLimitQuery());
        $count = WorkerPlanhour::count_all_by(["worker_id" => $ids], $filt);
        foreach ($hours as $hour) {
          $monday = DateTimeHelper::getFirstOfWeekDate($hour->getDate("Y-m-d"), 'U');
          if (!isset($verlof[$hour->worker_id][$monday])) {
            $verlof[$hour->worker_id][$monday] = $workers_all[$hour->worker_id]->getVerlof($monday);
          }
          $today = strtotime($hour->getDate("Y-m-d 00:00:00"));
          if (isset($verlof[$hour->worker_id][$monday][$today])) {
            $hour->verlof = $verlof[$hour->worker_id][$monday][$today];
          }
        }
      }

      $this->workers = $workers;
      $this->hours = $hours;
      $this->pager->count = $count;
      $this->groups = WorkerGroup::getAll();
      $this->selectable_workers = Worker::find_all_by(["planning" => 1, 'disabled' => 0]);
    }

    public function executeEdit() {
      $errors = [];

      $wph = new WorkerPlanhour();
      if (isset($_GET['id'])) {
        $wph = WorkerPlanhour::find_by_id($_GET['id']);
      }

      if (isset($_POST['verzend']) || isset($_POST['verzend_list'])) {
        $wph->worker_id = $_POST["worker_id"];
        $wph->date = getTSFromStr($_POST["date"]);
        $wph->hours = $_POST["hours"];

        if ($wph->worker_id == "") {
          $errors[] = "Werknemers is verplicht";
        }
        if ($wph->getDate() == "") {
          $errors[] = "Datum is verplicht";
        }
        if ($wph->hours == "") {
          $errors[] = "Uren is verplicht";
        }

        if (count($errors) == 0) {


          if ($wph->id == "") {
            $indb = WorkerPlanhour::find_by(["worker_id" => $wph->worker_id, "date" => $wph->date]);
            if ($indb) {
              $indb->destroy(); //gewoon opruimen en overschrijven
            }
          }
          $wph->save();

          $_SESSION['flash_message'] = "Plan uur is opgeslagen";
          if (isset($_POST['verzend_list'])) {
            ResponseHelper::redirect(PageMap::getUrl('M_PLANNING_HOURS'));
          }
          else {
            ResponseHelper::redirect(PageMap::getUrl('M_PLANNING_HOURS', ['action' => 'edit', 'id' => $wph->id]));
          }
        }

      }
      $this->errors = $errors;
      $this->wph = $wph;
      $this->workers = Worker::find_all_by(["planning" => 1, 'disabled' => 0]);
    }

    public function executeBatch() {
      $errors = [];

      if (isset($_POST['verzend']) || isset($_POST['verzend_list'])) {


        if (!isset($_POST["workers"])) {
          $errors[] = "Selecteer minstens 1 medewerker";
        }
        $nodays = true;
        foreach ($_POST["days"] as $day) {
          if ($day != "") {
            $nodays = false;
            if ($day > 24) {
              $errors["24"] = "Een dag kan niet meer dan 24 uur bevatten";
            }
          }
        }
        if ($nodays) {
          $errors[] = "Vul minsten 1 uur in";
        }
        $from = getTSFromStr($_POST['from']);
        $to = getTSFromStr($_POST['to']);
        if ($from == "" || $to == "") {
          $errors[] = "Datum van tot verplicht";
        }
        elseif (strtotime($from) > strtotime($to)) {
          $errors[] = "Datum van moet kleiner of gelijk zijn aan datum tot.";
        }

        if (count($errors) == 0) {
          foreach ($_POST["workers"] as $worker_id) {
            $worker = Worker::find_by_id($worker_id);
            $inDb = WorkerPlanhour::getFromTo($worker->id, $from, $to);

            for ($time = strtotime($from); $time <= strtotime($to); $time = strtotime("+1 DAYS", $time)) {
              $daynr = date("N", $time);
              $date = date("Y-m-d", $time);
              pd($date);
              if ($_POST["days"][$daynr] != "") {
                $wph = new WorkerPlanhour();
                if (isset($inDb[$date])) {
                  $wph = $inDb[$date];
                }
                $wph->worker_id = $worker->id;
                $wph->date = $date;
                $wph->hours = str_replace(",", ".", $_POST["days"][$daynr]);
                if ($wph->id != "" && $wph->hours == 0) {
                  $wph->destroy();
                }
                else {
                  $wph->save();
                }
              }
//            elseif(isset($inDb[$date])) {
//              $inDb[$date]->destroy();
//            }
            }

          }

          $_SESSION['flash_message'] = "Plan uren zijn aangemaakt";
          ResponseHelper::redirect(PageMap::getUrl('M_PLANNING_HOURS'));
        }

      }


      $this->errors = $errors;
      $this->workers = Worker::find_all_by(["planning" => 1, 'disabled' => 0]);
    }


    public function executeDelete() {
      if (isset($_GET['delid'])) {
        $ph = WorkerPlanhour::find_by_id($_GET['delid']);

        if ($ph) {
          $ph->destroy();
          $_SESSION['flash_message'] = "Item is verwijderd.";
          ResponseHelper::redirect(reconstructQueryAdd(['pageId']));
        }
      }

      $_SESSION['flash_message_red'] = "Item niet gevonden.";
      ResponseHelper::redirect(reconstructQueryAdd(['pageId']));
    }

  }

