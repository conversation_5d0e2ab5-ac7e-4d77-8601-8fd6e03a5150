<?php

  AppModel::loadBaseClass('BaseOrderElements');

  class OrderElementsModel extends BaseOrderElements {

    public function getMitre() {
      if ($this->leftMitreId != null && $this->rightMitreId != null) {
        return 'both';
      }
      elseif ($this->leftMitreId != null) {
        return 'left';
      }
      elseif ($this->rightMitreId != null) {
        return 'right';
      }
      return "none";
    }

    /**
     * Has elements with verstek
     * @param OrderElements[] $elements
     * @return bool
     */
    public static function hasVerstek($elements) {
      foreach ($elements as $element) {
        if ($element->getMitre() != "none") {
          return true;
        }
      }
      return false;
    }

    public function hasFlagWindowLeft() {
      //wanneer single en none, dan links pakken
      return $this->flagWindow == "double" || ($this->flagWindow == "single" && ($this->flagWindowSide == "left" || $this->flagWindowSide == "none" || $this->flagWindowSide == "both"));
    }

    public function hasFlagWindowRight() {
      return $this->flagWindow == "double" || ($this->flagWindow == "single" && ($this->flagWindowSide == "right" || $this->flagWindowSide == "both"));
    }

    /**
     * Berekend de maten van een element
     *
     * @param Quotations $quotation
     * @param StoneSizes $stone_size
     * @param Stones $stone
     */
    public function calculateValues($quotation, $quotations_extra, $stone_size, $stone) {

      if ($stone->isMuurafdekker()) {
        $this->calculateValuesMuurafdekkers($quotation, $quotations_extra, $stone_size, $stone);
        return;
      }
      if ($stone->isBeton() || $stone->isNatuursteen() || $stone->isIsosill()) {
        $this->calculateValuesMeters($quotation, $quotations_extra, $stone_size, $stone);
        return;
      }
      //keramisch
      if ($quotation->brandId == StoneBrands::BRAND_ID_STJORIS && $stone->isSpekband() && $stone->isKeramiek()) {
        $this->calculateValuesKeramischeSpekbandStjoris($quotation, $quotations_extra, $stone_size, $stone);
        return;
      }
      $this->calculateValuesCeramic($quotation, $quotations_extra, $stone_size, $stone);
    }


    /**
     * Berekend de maten van een element karamisch spekban stjoris
     * Deze heeft geen verstek
     *
     * @param Quotations $quotation
     * @param QuotationsExtra $quotations_extra
     * @param StoneSizes $stone_size
     * @param Stones $stone
     */
    public function calculateValuesKeramischeSpekbandStjoris($quotation, $quotations_extra, $stone_size, $stone) {
      $stoneWidthPlus10 = $stone_size->width * 10; //breedte steen in milimeter

      $this->stoneAmount = 0;
      $this->elementLength = $this->inputLength - $quotation->shorter;
      $this->elementLength = round($this->elementLength); //remove decimals
      $this->elementLengthTotal = $this->getTotalElementlength($quotation, $quotations_extra, false, false, $stone_size, $stone);

      //andere maten bij eindsteen stjoris??
      //$this->flagWindowSide


      //determine stoneAmount / divisionMeasure / fitStoneAmount / fitStoneLength

      $this->calculateFitStone($stoneWidthPlus10);
    }

    /**
     * Berekend de maten van een element karamisch
     *
     * @param Quotations $quotation
     * @param QuotationsExtra $quotations_extra
     * @param StoneSizes $stone_size
     * @param Stones $stone
     */
    public function calculateValuesCeramic($quotation, $quotations_extra, $stone_size, $stone) {
      $stoneWidthPlus10 = $stone_size->width * 10; //breedte steen in milimeter

      $this->stoneAmount = 0;
      //de elementlength = lengte van het element zoals gemeten - de hoek shortLength (bovenste rechte stuk hoek) - de speling - extra spacing vanwege hoeken.
      //dus niet alleen het rechtste stuk, het is gewoon minus de mitresteen breedte
      //dit is dus NIET de totaallengte van een element! (maar kan dat toevallig wel zijn)
      //deze waarde komt overeen met wat je ziet bij element maat in de wizard schermen
      $this->elementLength = 0;
      $mitreCount = 0;
      $leftMitre = $rightMitre = false;

      if ($this->leftMitreId != null) {
        $leftMitre = Mitres::find_by(["mitreId" => $this->leftMitreId]);
        if ($this->heartClickSize == 0) {
          $this->elementLength -= ($leftMitre->heartLength + $stone->getExtraSpacing() + $quotation->getVoegdikte());
        }
        $this->stoneAmount += $leftMitre->stoneCount;
        $mitreCount++;
      }
      if ($this->rightMitreId != null) {
        $rightMitre = Mitres::find_by(["mitreId" => $this->rightMitreId]);
        if ($this->heartClickSize == 0) {
          $this->elementLength -= ($rightMitre->heartLength + $stone->getExtraSpacing() + $quotation->getVoegdikte());
        }
        $this->stoneAmount += $rightMitre->stoneCount;
        $mitreCount++;
      }

      if ($mitreCount == 2) { // 2 schuine zijden
        if ($this->heartClickSize == 0) {
          $this->elementLength += $this->inputLength;
        }
        else {
          $this->elementLength = $this->inputLength - $leftMitre->longLength - $rightMitre->longLength - (2 * $stone->getExtraSpacing()) - (2 * $quotation->getVoegdikte());
        }
      }
      elseif ($mitreCount == 1) { // 1 schuine zijde
        if ($this->heartClickSize == 0) {
          $this->elementLength += $this->inputLength - ($quotation->shorter / 2);
        }
        else {
          if ($leftMitre) {
            $this->elementLength = $this->inputLength - ceil($leftMitre->longLength) - ($quotation->shorter / 2) - $stone->getExtraSpacing() - $quotation->getVoegdikte();
          }
          if ($rightMitre) {
            $this->elementLength = $this->inputLength - ceil($rightMitre->longLength) - ($quotation->shorter / 2) - $stone->getExtraSpacing() - $quotation->getVoegdikte();
          }
        }
      }
      elseif ($mitreCount == 0) { //geen schuine zijden
        $this->elementLength += $this->inputLength - $quotation->shorter;
      }


      //Flagwindow?
      if ($this->flagWindow == 'double') { //dubbel vlagkozijn, geen schuine zijden.
        $this->elementLength = $this->inputLength;
      }
      elseif ($this->flagWindow == 'single') {
        if ($mitreCount == 1) {
          //1 schuine zijde, haal alleen halve speling eraf
          $this->elementLength -= $quotation->shorter / 2;
        }
        else {
          //recht element, maakt niet of hartklikmaat/punt gemeten. Is altijd gehele lengte - halve speling
          $this->elementLength = $this->inputLength - ($quotation->shorter / 2);
        }
      }

      //determine stoneAmount / divisionMeasure / fitStoneAmount / fitStoneLength
      $this->calculateFitStone($stoneWidthPlus10);

      $this->elementLength = round($this->elementLength); //remove decimals
      $this->elementLengthTotal = $this->getTotalElementlength($quotation, $quotations_extra, $leftMitre, $rightMitre, $stone_size, $stone);
    }

    /**
     * Berekend de maten van een element beton / natuursteen
     * @param Quotations $quotation
     * @param QuotationsExtra $quotations_extra
     * @param StoneSizes $stone_size
     * @param Stones $stone
     */
    public function calculateValuesMeters($quotation, $quotations_extra, $stone_size, $stone) {

      //de elementlength = de input lengte - hoeken - spacing
      $this->elementLength = $this->inputLength;
      $mitreCount = 0;
      $leftMitre = $rightMitre = false;

      if ($this->leftMitreId != null) {
        $leftMitre = Mitres::find_by(["mitreId" => $this->leftMitreId]);
        if (($this->heartClickSize == 1 && $leftMitre->angle < 90) || ($this->heartClickSize == 0 && $leftMitre->angle > 90)) {
          $this->elementLength -= $this->getMitreWidth($stone->getType(), $stone_size->length, $leftMitre->angle);
        }
        $mitreCount++;
      }
      if ($this->rightMitreId != null) {
        $rightMitre = Mitres::find_by(["mitreId" => $this->rightMitreId]);
        //pd($this->getMitreWidth($stone->getType(), $stone_size->length, $rightMitre->angle));
        if (($this->heartClickSize == 1 && $rightMitre->angle < 90) || ($this->heartClickSize == 0 && $rightMitre->angle > 90)) {
          $this->elementLength -= $this->getMitreWidth($stone->getType(), $stone_size->length, $rightMitre->angle);
        }
        $mitreCount++;
      }

      if ($this->flagWindow == 'double') {
        //dubbel vlagkozijn (dus geen mitres): niet inkorten
      }
      elseif ($this->flagWindow == 'single') {
        //enkel vlagkozijn
        if ($mitreCount == 0) {
          //geen mitres: aan 0.5 inkorten
          $this->elementLength -= $quotation->shorter / 2;
        }
        elseif ($mitreCount == 1) {
          //1 mitre: niet inkorten
        }
      }
      else {
        //geen vlagkozijn
        if ($mitreCount == 0) {
          //geen mitres: 1 keer inkorten
          $this->elementLength -= $quotation->shorter;
        }
        elseif ($mitreCount == 1) {
          //1 mitre: 0,5 inkorten
          $this->elementLength -= $quotation->shorter / 2;
        }
        elseif ($mitreCount == 1) {
          //2 mitres: niet inkorten
        }
      }


      //extra spacing wanneer mitres
      $this->elementLength -= $mitreCount * $stone->getExtraSpacing();
      $this->elementLength = round($this->elementLength); //remove decimals
      $this->elementLengthTotal = $this->getTotalElementlength($quotation, $quotations_extra, $leftMitre, $rightMitre, $stone_size, $stone);

      $this->stoneAmount = 1;
      $this->divisionMeasure = 0;
      $this->fitStoneAmount = 0;
      $this->fitStoneLength = 0;

    }


    /**
     * Berekend de maten van een element beton / natuursteen
     * @param Quotations $quotation
     * @param QuotationsExtra $quotations_extra
     * @param StoneSizes $stone_size
     * @param Stones $stone
     */
    public function calculateValuesMuurafdekkers($quotation, $quotations_extra, $stone_size, $stone) {

      $mitreCount = 0;
      $leftMitre = $rightMitre = false;

      $this->elementLength = $this->inputLength;

      $oversteek = Round($stone_size->length * 10 - $quotations_extra->wall_thickness) / 2;
      $oversteek_calc = Round($oversteek) / 10;

      if ($this->leftEndstone == 1 && $this->rightEndstone == 1) {
        $this->elementLength += $oversteek + $oversteek - $quotation->shorter / 2;
      }
      elseif ($this->leftEndstone == 1 || $this->rightEndstone == 1) {
        $this->elementLength += $oversteek - $quotation->shorter / 2;
      }
      else {
        $this->elementLength -= $quotation->shorter;
      }

      if ($this->leftMitreId != null) {
        $leftMitre = Mitres::find_by(["mitreId" => $this->leftMitreId]);
        $this->elementLength += $this->getMitreWidth($stone->getType(), $oversteek_calc, $leftMitre->angle);
        $this->elementLength -= $this->getMitreWidth($stone->getType(), $stone_size->length, $leftMitre->angle);
        $mitreCount++;
      }
      if ($this->rightMitreId != null) {
        $rightMitre = Mitres::find_by(["mitreId" => $this->rightMitreId]);
        $this->elementLength += $this->getMitreWidth($stone->getType(), $oversteek_calc, $rightMitre->angle);
        $this->elementLength -= $this->getMitreWidth($stone->getType(), $stone_size->length, $rightMitre->angle);
        $mitreCount++;
      }


      //extra spacing wanneer mitres
      $this->elementLength -= $mitreCount * $stone->getExtraSpacing();
      $this->elementLength = round($this->elementLength); //remove decimals
      $this->elementLengthTotal = $this->getTotalElementlength($quotation, $quotations_extra, $leftMitre, $rightMitre, $stone_size, $stone);

      $this->stoneAmount = 1;
      $this->divisionMeasure = 0;
      $this->fitStoneAmount = 0;
      $this->fitStoneLength = 0;

      //muurafdekkers keramisch zijn stenen, bereken aantal stenen zoals ook bij kermamische raamdorpels.
      if ($stone->isKeramiek()) {
        $this->stoneAmount = 0;

        $stoneWidthPlus10 = $stone_size->width * 10; //breedte steen in milimeter

        if ($this->leftMitreId != null) {
          $leftMitre = Mitres::find_by(["mitreId" => $this->leftMitreId]);
          $this->stoneAmount += $leftMitre->stoneCount;
        }
        if ($this->rightMitreId != null) {
          $rightMitre = Mitres::find_by(["mitreId" => $this->rightMitreId]);
          $this->stoneAmount += $rightMitre->stoneCount;
        }

        //determine stoneAmount / divisionMeasure / fitStoneAmount / fitStoneLength
        $this->calculateFitStone($stoneWidthPlus10);
      }

    }

    /**
     * @param Quotations $quotation
     * @param $prices []
     * @param StoneSizes $stone_size
     * @param Stones $stone
     * @return bool
     */
    public function calculatePrice($quotation, $prices, $stone_size, $stone) {
      if ($stone->isNatuursteen() && $stone->isVensterbank()) {
        //vensterbanken worden uitgerekend met de m2 prijs.
        return $this->calculatePriceWindowsill($prices);
      }
      if ($stone->isNatuursteen() && $stone->isBalkje()) {
        return $this->calculatePriceBalkje($quotation, $stone);
      }
      if ($quotation->brandId > 2 || $stone->isIsosill()) {
        return $this->calculatePriceMeters($quotation, $prices);
      }
      return $this->calculatePriceCeramic($quotation, $prices, $stone_size);
    }

    /**
     * @param Quotations $quotation
     * @param $prices []
     * @param StoneSizes $stone_size
     * @return bool
     */
    public function calculatePriceCeramic($quotation, $prices, $stone_size) {
      if (!(is_float($prices['stonePrice']) && is_float($prices['gluePrice']) && is_float($prices['mitreFactor']))) {
        $this->elementWeight = 0;
        $this->elementPrice = 0.00;
        $this->totalPrice = 0.00;
        return false;
      }

      $stoneWidthPlus10 = $stone_size->width * 10;

      $endStones = 0;
      $dPriceMitres = 0.00;
      $dWeightMitres = 0.00;
      $dPriceStones = 0.00;

      if ($this->leftEndstoneGrooves > 0 || $this->rightEndstoneGrooves > 0) {
        $endStones = $this->leftEndstoneGrooves + $this->rightEndstoneGrooves;
        if ($endStones > 0) {
          $dPriceStones = (($this->stoneAmount - $endStones) * $prices['stonePrice']) + ($endStones * $prices['stonePriceGroove']);
        }
        else {
          $dPriceStones = $this->stoneAmount * $prices['stonePrice'];
        }
      }
      else {
        $endStones = $this->leftEndstone + $this->rightEndstone;
        if ($endStones > 0) {
          $dPriceStones = (($this->stoneAmount - $endStones) * $prices['stonePrice']) + ($endStones * $prices['stonePriceEnd']);
        }
        else {
          $dPriceStones = $this->stoneAmount * $prices['stonePrice'];
        }
      }

      $weightStones = ($this->elementLength / 1000 * $prices["stoneWeight"]);
      if ($endStones > 0) {
        $weightStones += (($prices["stoneWeightEnd"] - $prices["stoneWeight"]) * ($endStones * $stoneWidthPlus10 / 1000));
      }

      if ($quotation->endstone == 'true' && $this->stoneAmount == $this->fitStoneAmount) {
        // Endstone is a fit stone, so correct the weight
        $weightStones -= $endStones * ($stoneWidthPlus10 - $this->fitStoneLength) * $prices["stoneWeightEnd"] / 1000;
      }

      //Glue
      $dPriceGlue = ($prices["gluePrice"] / 1000) * $this->elementLength;

      //Mitres
      if (isset($this->leftMitre)) {
        //Stones, are already counted

        //Glue
        if ($this->leftMitre->shortLength > $this->leftMitre->longLength) {
          $dPriceGlue += ($prices["gluePrice"] / 1000) * ($this->leftMitre->shortLength + $quotation->getVoegdikte());
        }
        elseif ($this->leftMitre->shortLength < $this->leftMitre->longLength) {
          $dPriceGlue += ($prices["gluePrice"] / 1000) * ($this->leftMitre->longLength + $quotation->getVoegdikte());
        }

        //Saw
        $dPriceMitres += $prices["mitreFactor"] * $prices["stonePrice"];

        //Weight
        $dWeightMitres += $prices["stoneWeight"] * ($this->leftMitre->angle / 180) * (1000 / ($this->leftMitre->stoneCount * $stoneWidthPlus10));
      }

      if (isset($this->rightMitre)) {

        //Stones, are already counted

        //Glue
        if ($this->rightMitre->shortLength > $this->rightMitre->longLength) {
          $dPriceGlue += ($prices["gluePrice"] / 1000) * ($this->rightMitre->shortLength + $quotation->getVoegdikte());
        }
        elseif ($this->rightMitre->shortLength < $this->rightMitre->longLength) {
          $dPriceGlue += ($prices["gluePrice"] / 1000) * ($this->rightMitre->longLength + $quotation->getVoegdikte());
        }

        //Saw
        $dPriceMitres += $prices["mitreFactor"] * $prices["stonePrice"];

        //Weight
        $dWeightMitres += $prices["stoneWeight"] * ($this->rightMitre->angle / 180) * (1000 / ($this->rightMitre->stoneCount * $stoneWidthPlus10));

      }

      //Totals
      $this->elementPrice = round($dPriceStones + $dPriceGlue + $dPriceMitres, 2);
      $this->totalPrice = round($this->elementPrice * $this->amount, 2);
      $this->elementWeight = round($weightStones + $dWeightMitres, 2);

      return true;

    }


    /**
     * Berekenen op basis van meter prijs.
     * @param Quotations $quotation
     * @param $prices []
     * @return bool
     */
    public function calculatePriceMeters($quotation, $prices) {

      if (!(is_float($prices['stonePrice'])) || $prices['stonePrice'] == 0) {
        //op maat gemaakt element of geen prijs. alles naar 0.
        $this->elementPrice = 0.00;
        $this->totalPrice = 0.00;
        $this->elementWeight = 0;
        return false;
      }

      $meterPrice = $prices['stonePrice'];
      $price = ($meterPrice / 1000) * $this->elementLengthTotal;

      // dit is beton, en de lengte is minder dan 1 meter, minimaal 1 meter tellen.
      if ($quotation->brandId == 3 && $this->elementLengthTotal < 1000) {
        $price = $meterPrice;
      }

      $mitre_price = MitrePrice::getCurrent();
      if (isset($this->leftMitre)) {
        $price += $mitre_price;
      }
      if (isset($this->rightMitre)) {
        $price += $mitre_price;
      }

      $elementWeight = ($this->elementLengthTotal / 1000) * $prices["stoneWeight"];

      $this->elementPrice = round($price, 2);
      $this->totalPrice = round($this->elementPrice * $this->amount, 2);
      $this->elementWeight = round($elementWeight, 2);
      return true;

    }


    /**
     * Balkjes worden berekend per meter afgerond
     * Een balk prijs word berekend door lengte/breedte. Vervolgens zoek je daar de dichtsbijzijnste steen bij afgerond naar boven.
     * Oftwel 21, 36 word 30,40
     *
     * @param Quotations $quotation
     * @param $prices []
     * @return bool
     */
    public function calculatePriceBalkje($quotation, $stone) {

      //balke moet OrderElementSizes hebben.
      if (isset($this->order_element_sizes)) {
        $order_element_sizes = $this->order_element_sizes;
      }
      else {
        $order_element_sizes = OrderElementSize::getByElementId($this->elementId);
      }
      $width = $order_element_sizes["width"]->value;
      $height = $order_element_sizes["height"]->value;

      $search_width = ceil($width / 10) * 10;
      $search_height = ceil($height / 10) * 10;

      $stones = Stones::find_all_by(["display" => "true", "brandId" => $stone->brandId, "colorId" => $stone->colorId, "type" => Stones::TYPE_BALKJES]); //alle stenen van dit merk/type/kleur
      $sizeIds = []; //dit zijn alle mogelijke formaten van deze steen soort
      foreach ($stones as $lStone) {
        $sizeIds[$lStone->sizeId] = $lStone->sizeId;
      }
      $size = StoneSizes::find_by(["sizeId" => $sizeIds, "brandId" => $stone->brandId, "width" => $search_width / 10, "height" => $search_height / 10]); //er mag uiteindelijk maar 1 size overblijven.
      if (!$size) {
        //proberen met widht/height omgedraaid. Dat mag ook.
        $size = StoneSizes::find_by(["sizeId" => $sizeIds, "brandId" => $stone->brandId, "width" => $search_height / 10, "height" => $search_width / 10]); //er mag uiteindelijk maar 1 size overblijven.
        if (!$size) {
          throw new GsdException("Balk prijs formaat " . $search_width . "x" . $search_height . " niet gevonden.");
        }
      }

      $stoneFound = false;
      foreach ($stones as $lstone) {
        if ($lstone->sizeId == $size->sizeId) {
          $stoneFound = $lstone;
        }
      }

      if (!$stoneFound) {
        throw new GsdException("Balk steen niet gevonden bij formaat " . $width . "x" . $height . " (breedte x hoogte) niet gevonden.");
      }

      $prices = Quotations::getPrices(date("Y-m-d"), $quotation->userId, $stoneFound); //@todo: geselecteerde prijs heeft geen invloed op dit moment

      if ($prices === false || !(is_float($prices['stonePrice'])) || $prices['stonePrice'] == 0) {
        //op maat gemaakt element of geen prijs. alles naar 0.
        $this->elementPrice = 0.00;
        $this->totalPrice = 0.00;
        $this->elementWeight = 0;
        return false;
      }

      $meterPrice = $prices['stonePrice'];

      $price = ($meterPrice / 1000) * $this->elementLengthTotal;

      $mitre_price = MitrePrice::getCurrent();
      if (isset($this->leftMitre)) {
        $price += $mitre_price;
      }
      if (isset($this->rightMitre)) {
        $price += $mitre_price;
      }

      $elementWeight = ($this->elementLengthTotal / 1000) * $prices["stoneWeight"];

      $this->elementPrice = round($price, 2);
      $this->totalPrice = round($this->elementPrice * $this->amount, 2);
      $this->elementWeight = round($elementWeight, 2);
      return true;

    }

    public function calculatePriceWindowsill($prices) {

      if (!is_float($prices['stonePrice']) || !is_float($prices['stonePriceM2'])) {
        //op maat gemaakt element of geen prijs. alles naar 0.
        $this->elementPrice = 0.00;
        $this->totalPrice = 0.00;
        $this->elementWeight = 0;
        return false;
      }

//      $windowsillX1 = number_format(str_replace(',' , '.', $this->windowsill->x1), 1, '.', ',');
//      $windowsillX2 = number_format(str_replace(',' , '.', $this->windowsill->x2), 1, '.', ',');

      $price_m2 = $prices['stonePriceM2'];
      $opp = ($this->windowsill->x1 * $this->windowsill->x2) / 1000000; //oppervlakte
      $price = $price_m2 * $opp;

      $price += $prices['stonePrice'] * ($this->windowsill->x1 / 1000); // prijs per meter optellen
      $price += $prices['stonePricePiece']; //prijs per stuk erbij tellen.

      if ($prices['stonePriceMitre'] > 0) {
        $windowsillTemplate = Windowsill::find_by_id($this->windowsill->windowsill_id);
        if ($windowsillTemplate && $windowsillTemplate->mitre_factor > 0) {
          $price += $prices['stonePriceMitre'] * $windowsillTemplate->mitre_factor; //prijs per verstekhoek erbij tellen * mitre_factor
        }
      }

      $elementWeight = ($this->elementLengthTotal / 1000) * $prices["stoneWeight"];

      $this->elementPrice = round($price, 2);
      $this->totalPrice = round($this->elementPrice * $this->amount, 2);
      $this->elementWeight = round($elementWeight, 2);
      return true;
    }


    /**
     * @param OrderElements[] $elements
     * @return array
     */
    public static function calculateTotals($elements) {

      $vals = [
        "projectValue"               => 0,
        "meters"                     => 0,
        "weight"                     => 0,
        "stoneAmount"                => 0,
        "fullLength"                 => 0, //complete lengte inclusief mitres
        "totalLeftEndStones"         => 0,
        "totalRightEndStones"        => 0,
        "totalLeftEndStonesGrooves"  => 0,
        "totalRightEndStonesGrooves" => 0,
        "totalMiddlesStones"         => 0,
      ];


      $stone_total = 0;
      foreach ($elements as $element) {

        $vals["projectValue"] += $element->totalPrice;
        $vals["meters"] += round(($element->elementLength * $element->amount) / 1000, 2);
        $vals["weight"] += $element->elementWeight * $element->amount;
        $vals["stoneAmount"] += $element->stoneAmount * $element->amount;
        $vals["fullLength"] += $element->amount * ceil($element->elementLengthTotal);
        $vals["totalLeftEndStones"] += $element->leftEndstone * $element->amount;
        $vals["totalRightEndStones"] += $element->rightEndstone * $element->amount;
        $vals["totalLeftEndStonesGrooves"] += $element->leftEndstoneGrooves * $element->amount;
        $vals["totalRightEndStonesGrooves"] += $element->rightEndstoneGrooves * $element->amount;

        $stone_total += $element->amount * $element->stoneAmount; //stoneAmount van element houd al rekening met verlies e.d.

      }

      //de middlestones zijn alle stenen minus endstones
      $vals["totalMiddlesStones"] = $stone_total - $vals["totalLeftEndStones"] - $vals["totalRightEndStones"] - $vals["totalLeftEndStonesGrooves"] - $vals["totalRightEndStonesGrooves"];

      return $vals;
    }

    /**
     * De breedte van een hoeksteen a.d.h.v. de hoek en steen lengte.
     * LET OP: dit is dus zonder eventuele breedte stuk bij een keramische steen. (short_length)
     * @param $stone_type
     * @param $stone_length : in cm
     * @param $angle : in graden
     * @return int: milimeters
     */
    public function getMitreWidth($stone_type, $stone_length, $angle) {
      $mitre_length = 0;
      if ($stone_type == Stones::TYPE_MUURAFDEKKER) {
        //gemeten tot aan het midden
        $depth = $stone_length * 10;
        $mitre_length = $depth / tan(deg2rad($angle)); //= lengte van het verstek element.
      }
      elseif ($stone_type == Stones::TYPE_SPEKBAND) {
        //overstek is altijd 45 mm, ongeachte diepte spekband
        $depth = 45;
        if ($angle > 90) {
          $depth = ($stone_length * 10) - 45;
        }
        $mitre_length = $depth / tan(deg2rad($angle)); //= lengte van het verstek element.
      }
      elseif ($stone_type == Stones::TYPE_RAAMDORPEL) {
        //hartklik zit altijd 10 mmm onder de breedte
        $depth = ($stone_length * 10) - 10;
        if ($angle > 90) {
          $depth = 10;
        }
        $mitre_length = $depth / tan(deg2rad($angle)); //= lengte van het verstek element.
      }
      if ($mitre_length < 0) {
        //altijd positieve waarde
        $mitre_length *= -1;
      }
      return round($mitre_length);

    }

    /**
     * @param Quotations $quotation
     * @param Stones $stone
     * @return float|int
     */
    public function getSpelingLeft($quotation, $stone) {
      if (($this->getMitre() == "right" && $this->flagWindow == "single") || $this->flagWindow == "double" || $this->flagWindowSide == "left") {
        return 0;
      }
      if ($quotation->maySelectEndstone($stone) && $this->leftEndstone == 1) {
        //endstone is gekozen in wizard, en er is een linker eindsteen aangegeven. geen spacing.
        return 0;
      }
      if ($this->getMitre() == "both" || $this->getMitre() == "left") {
        return $stone->getExtraSpacing();
      }
      if ($this->getMitre() == "right" || $this->getMitre() == "none") {
        return $quotation->shorter / 2;
      }
      return 0;
    }

    /**
     * @param Quotations $quotation
     * @param Stones $stone
     * @return float|int
     */
    public function getSpelingRight($quotation, $stone) {
      if (($this->getMitre() == "left" && $this->flagWindow == "single") || $this->flagWindow == "double" || $this->flagWindowSide == "right") {
        return 0;
      }
      if ($quotation->maySelectEndstone($stone) && $this->rightEndstone == 1) {
        //endstone is gekozen in wizard, en er is een rechter eindsteen aangegeven. geen spacing.
        return 0;
      }
      if ($this->getMitre() == "both" || $this->getMitre() == "right") {
        return $stone->getExtraSpacing();
      }
      if ($this->getMitre() == "left" || $this->getMitre() == "none") {
        return $quotation->shorter / 2;
      }
      return 0;
    }

    /**
     * Bereken de element lengte min de spacing op basis van de inputLength.
     * Moet hetzelfde zijn als getoond in de wizard bij element maat.
     * @param Quotations $quotation
     * @return float|int
     */
    public function getElementlengthMinSpacing($quotation, $stone) {
      return $this->inputLength - $this->getSpelingLeft($quotation, $stone) - $this->getSpelingRight($quotation, $stone);
    }

    /**
     * Get total elementLength based on elementLength
     * @param Quotations $quotation
     * @param QuotationsExtra $quotations_extra
     * @param Mitres|bool $leftMitre
     * @param Mitres|bool $rightMitre
     * @param StoneSizes|bool $stone_size
     * @param Stones|null $stone
     * @return false|float|int
     */
    public function getTotalElementlength($quotation, $quotations_extra, $leftMitre = false, $rightMitre = false, $stone_size = false, $stone = null) {

      $totalElementLength = $this->elementLength;

      if ($stone->isKeramiek() && ($stone->isRaamdorpel() || $stone->isSpekband())) {

        $mitreLength = 0;
        //      pd($this->referenceName);
        //      pd($totalElementLength);
        if ($leftMitre) {
          $mitreLength += $leftMitre->shortLength > $leftMitre->longLength ? $leftMitre->shortLength : $leftMitre->longLength;
          //        if($leftMitre->shortLength > $leftMitre->longLength) {
          //          $mitreLength +=  $leftMitre->shortLength;
          //          if($this->heartClickSize==0) {
          //            $mitreLength += $leftMitre->shortLength - $leftMitre->heartLength;
          //          }
          //        }
          //        else {
          //          $mitreLength += $leftMitre->longLength;
          //          if($this->heartClickSize==0) {
          //            $mitreLength += $leftMitre->heartLength - $leftMitre->shortLength;
          //          }
          //        }
        }
        if ($rightMitre) {
          $mitreLength += $rightMitre->shortLength > $rightMitre->longLength ? $rightMitre->shortLength : $rightMitre->longLength;

          //        if($rightMitre->shortLength > $rightMitre->longLength) {
          //          $mitreLength +=  $rightMitre->shortLength;
          //          if($this->heartClickSize==0) {
          //            $mitreLength += $rightMitre->shortLength - $rightMitre->heartLength;
          //          }
          //        }
          //        else {
          //          $mitreLength += $rightMitre->longLength;
          //          if($this->heartClickSize==0) {
          //            $mitreLength += $rightMitre->heartLength - $rightMitre->shortLength;
          //          }
          //        }
        }

        $totalElementLength += ceil($mitreLength);

        if ($this->getMitre() == "both") {
          $totalElementLength += 2 * $quotation->getVoegdikte();
        }
        elseif ($this->getMitre() == "left") {
          $totalElementLength = floor($totalElementLength + $quotation->getVoegdikte());
        }
        elseif ($this->getMitre() == "right") {
          $totalElementLength = floor($totalElementLength + $quotation->getVoegdikte());
        }
      }
      else {
        //gewoon de elementLenght + lengte mitres
        if ($leftMitre) {
          $totalElementLength += $this->getMitreWidth($stone->getType(), $stone_size->length, $leftMitre->angle);
        }
        if ($rightMitre) {
          $totalElementLength += $this->getMitreWidth($stone->getType(), $stone_size->length, $rightMitre->angle);
        }
      }


      //    pd($this->elementLength);
      //    pd($totalElementLength);
      return ceil($totalElementLength);
    }


    public function destroy() {
      foreach (OrderElementWindowsill::find_all_by(["element_id" => $this->elementId]) as $oew) {
        $oew->destroy();
      }
      foreach (OrderElementSize::find_all_by(["element_id" => $this->elementId]) as $oes) {
        $oes->destroy();
      }
      parent::destroy();
    }

    public function calculateFitStone(float|int $stoneWidth): void {
      $iDefaultDividerMin = $stoneWidth + 3;
      $iDefaultDivider = $stoneWidth + 3.5;
      $iDefaultDividerMax = $stoneWidth + 4.5;

      $iStenenMin = ceil(($this->elementLength + 3.5) / $iDefaultDividerMin);
      $iStenenStd = ceil(($this->elementLength + 3.5) / $iDefaultDivider);
      $iStenenMax = ceil(($this->elementLength + 3.5) / $iDefaultDividerMax);

      if ($iStenenMin - $iStenenMax >= 1) {
        // No fitstone by changeing the division
        $this->stoneAmount += $iStenenMax;
        $this->divisionMeasure = round($this->elementLength / $iStenenMax, 2);      // LET OP! Element zonder voeg!
        $this->fitStoneAmount = 0;
        $this->fitStoneLength = 0;
      }
      else {
        $this->stoneAmount += $iStenenStd;
        $this->divisionMeasure = $iDefaultDivider;
        $this->fitStoneAmount = 2;
        $this->fitStoneLength = floor($stoneWidth - (((($iStenenStd * $this->divisionMeasure) - 3.5) - $this->elementLength) / 2));

        // Small elements - up to 6 stones
        if ($iStenenStd >= 2 && $iStenenStd <= 6 && $this->fitStoneLength <= 65) {
          $se = SmallElements::find_by(["elementLength" => $this->elementLength]);
          if ($se) {
            $this->stoneAmount = $this->stoneAmount - $iStenenStd + $se->stoneAmount;
            $this->fitStoneAmount = $se->fitStoneAmount;
            $this->fitStoneLength = $se->fitStoneLength;
          }
        }
      }
    }
  }