<?php
class BaseStoneOrderItem extends AppModel
{
  const DB_NAME = 'rde_b1mo';
  const TABLE_NAME = 'stone_order_item';
  const OM_CLASS_NAME = 'StoneOrderItem';
  const columns = ['id', 'stone_order_id', 'stone_id', 'name', 'size', 'loadreference', 'senddate', 'supplierreadydate', 'receiveddate', 'receivedsize'];
  const field_structure = [
    'id'                          => ['type' => 'mediumint', 'length' => '8', 'null' => false],
    'stone_order_id'              => ['type' => 'mediumint', 'length' => '8', 'null' => false],
    'stone_id'                    => ['type' => 'int', 'length' => '4', 'null' => false],
    'name'                        => ['type' => 'text', 'length' => '', 'null' => true],
    'size'                        => ['type' => 'int', 'length' => '8', 'null' => false],
    'loadreference'               => ['type' => 'varchar', 'length' => '20', 'null' => true],
    'senddate'                    => ['type' => 'datetime', 'length' => '', 'null' => true],
    'supplierreadydate'           => ['type' => 'datetime', 'length' => '', 'null' => true],
    'receiveddate'                => ['type' => 'datetime', 'length' => '', 'null' => true],
    'receivedsize'                => ['type' => 'int', 'length' => '8', 'null' => false],
  ];

  protected static $primary_key = ['id'];
  protected $auto_increment = 'id';

  public $id, $stone_order_id, $stone_id, $name, $size, $loadreference, $senddate, $supplierreadydate, $receiveddate, $receivedsize;

  function __construct($obj_arr = [], &$error_codes = []) {
    parent::__construct();
    $this->setDefaults();
    if (!empty($obj_arr)) {
      $this->from_array($obj_arr);
      $this->valid_required_fields($error_codes);
    }
  }

  public function valid(&$error_codes = []) {
    $errors = [];
    foreach(self::columns as $column) {
      if ($column != $this->auto_increment)
      {
        call_user_func_array([$this, 'v_' . $column], [&$errors]);
      }
    }
    if(empty($errors)) {
      return true;
    }
    $error_codes = array_merge($error_codes, $errors);
    return false;
  }

  public function valid_required_fields(&$error_codes = []) {
    $errors = [];
    foreach (self::columns as $column) {
      $function_name = "v_$column";
      if ($column!=$this->auto_increment) {
        call_user_func_array([$this, $function_name], [&$errors]);
      }
    }
    if (empty($errors)) {
      return true;
    }
    $error_codes = array_merge($error_codes, $errors);
    return false;
  }

  public function setDefaults() {
    $this->receivedsize = 0;
  }



  public function v_id(&$error_codes = []) {
    if (!is_null($this->id) && strlen($this->id) > 0 && self::valid_mediumint($this->id, '8')) {
      return true;
    }
    $error_codes[] = 'id';
    return false;
  }

  public function v_stone_order_id(&$error_codes = []) {
    if (!is_null($this->stone_order_id) && strlen($this->stone_order_id) > 0 && self::valid_mediumint($this->stone_order_id, '8')) {
      return true;
    }
    $error_codes[] = 'stone_order_id';
    return false;
  }

  public function v_stone_id(&$error_codes = []) {
    if (!is_null($this->stone_id) && strlen($this->stone_id) > 0 && self::valid_int($this->stone_id, '4')) {
      return true;
    }
    $error_codes[] = 'stone_id';
    return false;
  }

  public function v_name(&$error_codes = []) {
    if (is_null($this->name) || strlen($this->name) == 0 || self::valid_text($this->name)) {
      return true;
    }
    $error_codes[] = 'name';
    return false;
  }

  public function v_size(&$error_codes = []) {
    if (!is_null($this->size) && strlen($this->size) > 0 && self::valid_int($this->size, '8')) {
      return true;
    }
    $error_codes[] = 'size';
    return false;
  }

  public function v_loadreference(&$error_codes = []) {
    if (is_null($this->loadreference) || strlen($this->loadreference) == 0 || self::valid_varchar($this->loadreference, '20')) {
      return true;
    }
    $error_codes[] = 'loadreference';
    return false;
  }

  public function v_senddate(&$error_codes = []) {
    if (is_null($this->senddate) || strlen($this->senddate) == 0 || self::valid_datetime($this->senddate)) {
      return true;
    }
    $error_codes[] = 'senddate';
    return false;
  }

  public function v_supplierreadydate(&$error_codes = []) {
    if (is_null($this->supplierreadydate) || strlen($this->supplierreadydate) == 0 || self::valid_datetime($this->supplierreadydate)) {
      return true;
    }
    $error_codes[] = 'supplierreadydate';
    return false;
  }

  public function v_receiveddate(&$error_codes = []) {
    if (is_null($this->receiveddate) || strlen($this->receiveddate) == 0 || self::valid_datetime($this->receiveddate)) {
      return true;
    }
    $error_codes[] = 'receiveddate';
    return false;
  }

  public function v_receivedsize(&$error_codes = []) {
    if (!is_null($this->receivedsize) && strlen($this->receivedsize) > 0 && self::valid_int($this->receivedsize, '8')) {
      return true;
    }
    $error_codes[] = 'receivedsize';
    return false;
  }

  /**
  *@param array $conditions (required)
  *@param String $raw_sql (optional)
  *@return StoneOrderItem[]
  **/
  public static function find_all_like($conditions, $raw_sql = '') { return parent::generic_find_all_like($conditions, static::OM_CLASS_NAME, $raw_sql); }
  /**
  *@param array $conditions (required)
  *@param String $raw_sql (optional)
  *@return StoneOrderItem[]
  **/
  public static function find_all_by($conditions, $raw_sql = '') { return parent::generic_find_all_by($conditions, static::OM_CLASS_NAME, $raw_sql); }
  /**
  *@param String $raw_sql (optional)
  *@return StoneOrderItem[]
  **/
  public static function find_all($raw_sql = '') { return parent::generic_find_all(static::OM_CLASS_NAME, $raw_sql); }
  /**
  *@param array $conditions (required)
  *@param String $raw_sql (optional)
  *@return StoneOrderItem
  **/
  public static function find_by($conditions, $raw_sql = '') { return parent::generic_find_by($conditions, static::OM_CLASS_NAME, $raw_sql); }
  /**
  *@param String $id (required)
  *@param String $raw_sql (optional)
  *@return StoneOrderItem
  **/
  public static function find_by_id($id, $raw_sql = '') { return parent::generic_find_by_id($id, static::OM_CLASS_NAME, $raw_sql); }
  /**
  *@param array $conditions (required)
  *@param String $raw_sql (optional)
  *@return int
  **/
  public static function count_all_by($conditions, $raw_sql = '') { return parent::generic_count_all_by($conditions, static::OM_CLASS_NAME, $raw_sql); }
  /**
  *@param array $conditions (required)
  *@param String $raw_sql (optional)
  *@return bool
  **/
  public static function delete_by($conditions, $raw_sql = '') { return parent::generic_delete_by($conditions, static::OM_CLASS_NAME, $raw_sql); }
}