<?php TemplateHelper::includePartial('_tabs.php', 'orders', compact(["quotation", "quotation_extra", "company", "sandboxuser"])); ?>
<br/>
<form class="edit-form" method="post">
  <div class="address-selection">
    <strong>Selecteer afleveradres:</strong><br><br>
    <div class="radio-options">
      <label class="radio-label">
        <input type="radio" name="address_select" value="visit" <?php if ($quotation_extra->addressDeliveryId == $visitAddress->addressId) echo 'checked' ?>>
        Bezoekadres - <?= "$visitAddress->street $visitAddress->nr, $visitAddress->zipcode $visitAddress->domestic" ?>
      </label>
      <label class="radio-label">
        <input type="radio" name="address_select" value="pickup" <?php if ($quotation_extra->addressDeliveryId == $pickupAddress->addressId) echo 'checked' ?>>
        Zelf afhalen - <?= "$pickupAddress->street $pickupAddress->nr, $pickupAddress->zipcode $pickupAddress->domestic" ?>
      </label>
      <?php foreach($deliveryAddresses as $address): ?>
        <label class="radio-label">
          <input type="radio" 
            name="address_select" 
            value="delivery_<?= $address->addressId ?>"
            data-street="<?= htmlspecialchars($address->street) ?>"
            data-number="<?= htmlspecialchars($address->nr) ?>"
            data-extension="<?= htmlspecialchars($address->extension) ?>"
            data-zipcode="<?= htmlspecialchars($address->zipcode) ?>"
            data-city="<?= htmlspecialchars($address->domestic) ?>"
            data-country="<?= htmlspecialchars($address->country) ?>"
            data-extra-info="<?= htmlspecialchars($address->extraInfo ?? "") ?>"
            <?php if ($quotation_extra->addressDeliveryId == $address->addressId) echo 'checked' ?>
          >
          Werfadres - <?= "$address->street $address->nr, $address->zipcode $address->domestic" ?>
        </label>
      <?php endforeach; ?>
      <label class="radio-label">
        <input type="radio" name="address_select" value="custom" <?php if (!$selectedAddress) echo 'checked' ?>>
        Bezorgen op locatie
      </label>
    </div>
  </div>
  <div class="form1-container">
    <div id="form">
      <table class="default_table table-border default_table_center" style="border: 1px solid var(--gray-200); border-radius: 12px;">
        <tr class="dataTableHeadingRow">
          <td>Eigenschap</td>
          <td>Waarde</td>
        </tr>
        <tr class="dataTableRow">
          <td>Straat</td>
          <td><input <?php if($selectedAddress) echo 'readonly' ?> id="street" name="street" type="text" value="<?php echo $selectedAddress?->street ?? $quotation->street ?>"></td>
        </tr>
        <tr class="dataTableRow">
          <td>Nummer</td>
          <td><input <?php if($selectedAddress) echo 'readonly' ?> id="number" name="number" type="number" value="<?php echo $selectedAddress?->nr ?? $quotation->nr?>"></td>
        </tr>
        <tr class="dataTableRow">
          <td>Extensie</td>
          <td><input <?php if($selectedAddress) echo 'readonly' ?> id="extension" name="extension" type="text" value="<?php echo $selectedAddress?->extension ?? $quotation->ext?>"></td>
        </tr>
        <tr class="dataTableRow">
          <td>Postcode</td>
          <td><input <?php if($selectedAddress) echo 'readonly' ?> id="zip" name="zip" type="text" value="<?php echo $selectedAddress?->zipcode ?? $quotation->zipcode?>"></td>
        </tr>
        <tr class="dataTableRow">
          <td>Plaats</td>
          <td><input <?php if($selectedAddress) echo 'readonly' ?> id="city" name="city" type="text" value="<?php echo $selectedAddress?->domestic ?? $quotation->domestic?>"></td>
        </tr>
        <tr class="dataTableRow">
          <td>Land</td>
          <td>
            <select <?php if($selectedAddress) echo 'disabled' ?> id="country" name="country">
              <option value="NL" <?php echo ($selectedAddress?->country === 'NL') ? 'selected' : ''; ?>>NL</option>
              <option value="BE" <?php echo ($selectedAddress?->country === 'BE') ? 'selected' : ''; ?>>BE</option>
              <option value="DE" <?php echo ($selectedAddress?->country === 'DE') ? 'selected' : ''; ?>>DE</option>
            </select>
          </td>
        </tr>
      </table>
      <div style="padding: 15px 15px 0 15px;">
        <input type="submit" name="save_address" value="Opslaan" class="gsd-btn gsd-btn-primary">
      </div>
    </div>

    <div class="form-column-extra" style="padding-top: 15px">
      <div style="margin-bottom: 20px;">
        <label for="dispatchAppointment"><strong>Opmerking voor chauffeur</strong></label><br><br>
        <textarea rows="5" id="dispatchAppointment" name="dispatchAppointment"><?php echo $quotation->dispatchAppointment ?></textarea>
      </div>
      <div class="extra-info-box" style="display: <?php echo $selectedAddress?->extraInfo ? 'block' : 'none' ?>;">
        <label for="adresInstructie"><strong>Adres instructie</strong></label><br>
        <p id="extraInfo"><?php echo htmlspecialchars($selectedAddress?->extraInfo ?? '') ?></p>
      </div>
    </div>

    <div class="form-column-map">
      <input type="hidden" name="lat" id="lat" value=""/>
      <input type="hidden" name="lng" id="lng" value=""/>
      <input type="button" id="get_coordinates" value="<?php echo __('Coördinaten ophalen'); ?>" title="<?php echo __('Coördinaten ophalen a.d.h.v. adres gegevens'); ?>"/>
      <div id="google_maps" style="width: 100%; height: 300px; margin-top: 1em"></div>
      <input id="pac-input" type="text" placeholder="<?php echo __('Ga naar een locatie'); ?>" style="display: none;"/>
    </div>
  </div>
</form>
<div>
  <form method="post" class="edit-form" enctype="multipart/form-data">
    <table class="default_table table-border" style="width: 60%; border: 1px solid var(--gray-200); border-radius: inherit; ">
      <tr class="dataTableHeadingRow">
        <td>Eigenschap</td>
        <td>Waarde</td>
      </tr>
      <?php if (count($files)): ?>
        <tr class="dataTableRow">
          <td>
            Bijlage
          </td>
          <td class="appendix">
            <?php foreach ($files as $file): ?>
              <div>
                <a target="_blank" href="<?php echo reconstructQueryAdd() . "&action=openAppendix&file_id=$file->fileId" ?>"><?php echo $file->filename ?></a>
                <?php echo BtnHelper::getRemove(reconstructQueryAdd(['pageId']) . "action=deleteAppendix&file_id=$file->fileId", __('Verwijder bijlage')) ?>
              </div>
            <?php endforeach; ?>
          </td>
        </tr>
      <?php endif; ?>
      <tr class="dataTableRow">
        <td>Bijlage uploaden</td>
        <td class="upload-appendix"><?php echo $uploader->getInputs(); ?></td>
      </tr>
      <tr class="dataTableRow">
        <td>Uitvoerder</td>
        <td>
          <select name="personId" id="executor" style="height: 32px">
            <option value="">Selecteer een uitvoerder...</option>
            <?php foreach ($executors as $executor): ?>
              <option value="<?php echo $executor->personId ?>" <?php if ($executor->personId == $quotation->executorPersonId) echo 'selected' ?>>
                <?php echo $executor->firstName . ' ' . $executor->lastName ?>
              </option>
            <?php endforeach; ?>
          </select>
          <a class="gsd-btn gsd-btn-secondary" href="<?php echo $addExecutorLink ?>">Nieuwe uitvoerder aanmaken</a>
        </td>
      </tr>
      <tr class="dataTableRow">
        <td>Naam</td>
        <td><input type="text" name="name" id="name" readonly value="<?php echo $quotation->executor ?>"></td>
      </tr>
      <tr class="dataTableRow">
        <td>Mobiele telefoon</td>
        <td><input type="text" name="tel" id="tel" readonly value="<?php echo $quotation->executorMobile ?>"></td>
      </tr>
      <tr class="dataTableRow">
        <td>Email</td>
        <td><input type="text" name="email" id="email" readonly value="<?php echo $quotation->executorMail ?>"></td>
      </tr>
      <tr class="dataTableRow">
        <td>Opmerking</td>
        <td><textarea name="deliveryNotes"><?php echo $quotation->deliveryNotes ?></textarea></td>
      </tr>
    </table>
    <div style="padding: 15px;">
      <input type="submit" name="save_executor" value="Opslaan" class="gsd-btn gsd-btn-primary">
    </div>
  </form>
</div>
<style>
  .appendix {
    display: flex;
    flex-direction: column;
    gap: 1em;
  }
  .appendix > div {
    display: flex;
    align-items: center;
    gap: 1em;
  }

  .upload-appendix input {
    padding-left: 0;
  }

  .gsd-btn-secondary {
    padding: .5em 1em;
    margin-left: 1em;
  }

  #google_maps {
    display: block !important;
  }

  .form1-container {
    display: flex;
    justify-content: space-between;
    margin-bottom: 15px;
  }

  .form1-container > #form {
    flex: 1;
    margin-right: 20px;
  }

  .form1-container > .form-column-extra {
    flex: 1;
    margin-right: 20px;
  }

  .form1-container > .form-column-map {
    width: 500px;
    margin-right: 20px;
  }

  #form {
    width: -webkit-fill-available;
  }

  .form-label {
    display: flex;
    justify-content: space-between;
    width: 42%;
    height: 42px;
    align-items: center;
  }

  .form-label > input {
    max-width: 206px;
    height: 32px;
  }

  .form-label > textarea {
    max-width: 206px;
  }

  .dataTableRow {
    height: 43px !important;
  }

  .checkbox-address {
    width: 40%;
    font-weight: bold;
    padding-left: 15px;
  }

  .label-address {
    display: flex;
  }

  .table-border {
    border: 1px solid var(--gray-200);
    border-radius: inherit;
  }
  .address-selection {
    padding: 15px;
  }

  .radio-options {
    display: flex;
    flex-direction: column;
    gap: 10px;
  }

  .radio-label {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px;
    cursor: pointer;
  }

  .radio-label input[type="radio"] {
    margin: 0;
  }
</style>
<script type="text/javascript">
  let map;
  let marker;
  const startup = true;

  async function initmap() {
    const mapOptions = {
      center: new google.maps.LatLng(52.2, 5.5),
      zoom: 15,
      scrollwheel: false,
      mapTypeId: google.maps.MapTypeId.ROADMAP,
      mapId: "route_map_id" // Required for Advanced Markers
    };

    map = new google.maps.Map(document.getElementById("google_maps"), mapOptions);
  }

  $(document).ready(function () {
    initmap();
    findLocation();

    $("#get_coordinates").click(function () {
      let url = '<?php echo reconstructQueryAdd(['pageId']) ?>action=findlatlng';
      const street = $("#street");
      const number = $("#number");
      const city = $("#city");
      const country = $("#country");
      const zip = $("#zip");

      if (street.val()) url += '&street=' + street;
      if (number.val()) url += '&nr=' + number.val();
      if (city.val()) url += '&city=' + city.val();
      if (country.val()) url += '&country=' + country.val();
      if (zip.val()) url += '&zipcode=' + zip.val().replace(/\s+/g, '');

      $.getJSON(url, async function (data) {
        let latLng;
        const { AdvancedMarkerElement } = await google.maps.importLibrary("marker");

        if (!data) {
          swal("Kaart", "We kunnen de locatie van dit adres op de kaart niet vinden. Controleer of u het adres wel kunt vinden met google maps, of versleep de marker handmatig op de juiste plaats.");
          //toon marker op locatie eigenaar
          latLng = new google.maps.LatLng(<?php echo $_SESSION['userObject']->organisation->lat ?>,<?php echo $_SESSION['userObject']->organisation->lng ?>);
          if (!marker) {
            marker = new AdvancedMarkerElement({
              position: latLng,
              map: map,
              gmpDraggable: true
            });
            marker.addListener('drag', function () {
              const position = marker.position;
              $("#lat").val(position.lat);
              $("#lng").val(position.lng);
            });
          }
          map.setCenter(latLng);
          marker.position = latLng;
          $("#lat").val(latLng.lat());
          $("#lng").val(latLng.lng());
        }
        else {
          latLng = new google.maps.LatLng(data.latitude, data.longitude);
          if (typeof latLng !== 'undefined') {
            if (!marker) {
              marker = new AdvancedMarkerElement({
                position: latLng,
                map: map,
                gmpDraggable: true
              });
              marker.addListener('drag', function () {
                const position = marker.position;
                $("#lat").val(position.lat);
                $("#lng").val(position.lng);
              });
            }
            map.setCenter(latLng);
            marker.position = latLng;
            $("#lat").val(data.latitude);
            $("#lng").val(data.longitude);
          }
        }
      });
    });

    async function findLocation() {
      let url = '<?php echo reconstructQueryAdd(['pageId']) ?>action=findlatlng';
      const street = $("#street");
      const number = $("#number");
      const city = $("#city");
      const country = $("#country");
      const zip = $("#zip");

      if (street.val()) url += '&street=' + street;
      if (number.val()) url += '&nr=' + number.val();
      if (city.val()) url += '&city=' + city.val();
      if (country.val()) url += '&country=' + country.val();
      if (zip.val()) url += '&zipcode=' + zip.val().replace(/\s+/g, '');

      $.getJSON(url, async function (data) {
        let latLng;
        const { AdvancedMarkerElement } = await google.maps.importLibrary("marker");

        if (!data) {
          swal("Kaart", "We kunnen de locatie van dit adres op de kaart niet vinden. Controleer of u het adres wel kunt vinden met google maps, of versleep de marker handmatig op de juiste plaats.");
          //toon marker op locatie eigenaar
          latLng = new google.maps.LatLng(<?php echo $_SESSION['userObject']->organisation->lat ?>,<?php echo $_SESSION['userObject']->organisation->lng ?>);
          if (!marker) {
            marker = new AdvancedMarkerElement({
              position: latLng,
              map: map,
              gmpDraggable: true
            });
            marker.addListener('drag', function () {
              const position = marker.position;
              $("#lat").val(position.lat);
              $("#lng").val(position.lng);
            });
          }
          map.setCenter(latLng);
          marker.position = latLng;
          $("#lat").val(latLng.lat());
          $("#lng").val(latLng.lng());
        }
        else {
          latLng = new google.maps.LatLng(data.latitude, data.longitude);
          if (typeof latLng !== 'undefined') {
            if (!marker) {
              marker = new AdvancedMarkerElement({
                position: latLng,
                map: map,
                gmpDraggable: true
              });
              marker.addListener('drag', function () {
                var position = marker.position;
                $("#lat").val(position.lat);
                $("#lng").val(position.lng);
              });
            }
            map.setCenter(latLng);
            marker.position = latLng;
            $("#lat").val(data.latitude);
            $("#lng").val(data.longitude);
          }
        }
      });
    }

    let executors = <?php echo json_encode($executor_arr); ?>;
    const el_executors = $("#executor");
    const result = [];

    for (const i in executors)
      result.push([i, executors[i]]);

    el_executors.change(function () {
      if (executors) {
        $("#name").val(executors[el_executors.val()]['name']);
        $("#tel").val(executors[el_executors.val()]['mobile']);
        $("#email").val(executors[el_executors.val()]['email']);
      }
    });
  });

  const checkbox_visit = $("#checkbox_visit");
  const checkbox_pickup = $("#checkbox_pickup");
  const checkbox_location = $("#checkbox_location");
  const checkbox_delivery = $("#checkbox_delivery");

  const street = $("#street");
  const number = $("#number");
  const extension = $("#extension");
  const zip = $("#zip");
  const city = $("#city");
  const country = $("#country");
  const extraInfo = $("#extraInfo");

  $("input[name='address_select']").change(function () {
    const selected = $(this).val();

    // reset veld-eigenschappen standaard naar leesbaar en inschakelbaar
    const setReadOnly = (isReadOnly) => {
      street.prop('readonly', isReadOnly);
      number.prop('readonly', isReadOnly);
      extension.prop('readonly', isReadOnly);
      zip.prop('readonly', isReadOnly);
      city.prop('readonly', isReadOnly);
      country.prop('disabled', isReadOnly);
    };

    switch (selected) {
      case 'visit':
        street.val(<?= json_encode($visitAddress->street) ?>);
        number.val(<?= json_encode($visitAddress->nr) ?>);
        extension.val(<?= json_encode($visitAddress->extension) ?>);
        zip.val(<?= json_encode($visitAddress->zipcode) ?>);
        city.val(<?= json_encode($visitAddress->domestic) ?>);
        country.val(<?= json_encode($visitAddress->country) ?>);
        extraInfo.text(<?= json_encode($visitAddress->extraInfo) ?>);
        setReadOnly(true);
        break;

      case 'pickup':
        street.val('Raambrug');
        number.val(9);
        extension.val('');
        zip.val('5531AG');
        city.val('Bladel');
        country.val('NL');
        extraInfo.text('');
        setReadOnly(true);
        break;

      case 'custom':
        street.val(<?= json_encode($quotation->street) ?>);
        number.val(<?= json_encode($quotation->nr) ?>);
        extension.val(<?= json_encode($quotation->ext) ?>);
        zip.val(<?= json_encode($quotation->zipcode) ?>);
        city.val(<?= json_encode($quotation->domestic) ?>);
        country.val(<?= json_encode($quotation->country) ?>);
        extraInfo.text('');
        setReadOnly(false);
        break;

      default:
        // Werfadres (radio met data-attributes)
        const radio = $(this);
        street.val(radio.data('street'));
        number.val(radio.data('number'));
        extension.val(radio.data('extension'));
        zip.val(radio.data('zipcode'));
        city.val(radio.data('city'));
        country.val(radio.data('country'));
        extraInfo.text(radio.data('extra-info'));
        setReadOnly(true);
    }

    $(".extra-info-box").css('display', !extraInfo.text() ? 'none' : 'block')

    $("#get_coordinates").click();
  });
</script>