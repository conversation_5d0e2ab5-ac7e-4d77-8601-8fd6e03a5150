<?php
class BaseGpsbuddyRde extends AppModel
{
  const DB_NAME = 'rde_route';
  const TABLE_NAME = 'gpsbuddy_rde';
  const OM_CLASS_NAME = 'GpsbuddyRde';
  const columns = ['logId', 'bakId', 'routeId', 'quotationId'];
  const field_structure = [
    'logId'                       => ['type' => 'int', 'length' => '7', 'null' => false],
    'bakId'                       => ['type' => 'int', 'length' => '4', 'null' => false],
    'routeId'                     => ['type' => 'int', 'length' => '7', 'null' => false],
    'quotationId'                 => ['type' => 'int', 'length' => '11', 'null' => false],
  ];

  protected static $primary_key = ['logId'];
  protected $auto_increment = 'logId';

  public $logId, $bakId, $routeId, $quotationId;

  function __construct($obj_arr = [], &$error_codes = []) {
    parent::__construct();
    $this->setDefaults();
    if (!empty($obj_arr)) {
      $this->from_array($obj_arr);
      $this->valid_required_fields($error_codes);
    }
  }

  public function valid(&$error_codes = []) {
    $errors = [];
    foreach(self::columns as $column) {
      if ($column != $this->auto_increment)
      {
        call_user_func_array([$this, 'v_' . $column], [&$errors]);
      }
    }
    if(empty($errors)) {
      return true;
    }
    $error_codes = array_merge($error_codes, $errors);
    return false;
  }

  public function valid_required_fields(&$error_codes = []) {
    $errors = [];
    foreach (self::columns as $column) {
      $function_name = "v_$column";
      if ($column!=$this->auto_increment) {
        call_user_func_array([$this, $function_name], [&$errors]);
      }
    }
    if (empty($errors)) {
      return true;
    }
    $error_codes = array_merge($error_codes, $errors);
    return false;
  }

  public function setDefaults() {
  }



  public function v_logId(&$error_codes = []) {
    if (!is_null($this->logId) && strlen($this->logId) > 0 && self::valid_int($this->logId, '7')) {
      return true;
    }
    $error_codes[] = 'logId';
    return false;
  }

  public function v_bakId(&$error_codes = []) {
    if (!is_null($this->bakId) && strlen($this->bakId) > 0 && self::valid_int($this->bakId, '4')) {
      return true;
    }
    $error_codes[] = 'bakId';
    return false;
  }

  public function v_routeId(&$error_codes = []) {
    if (!is_null($this->routeId) && strlen($this->routeId) > 0 && self::valid_int($this->routeId, '7')) {
      return true;
    }
    $error_codes[] = 'routeId';
    return false;
  }

  public function v_quotationId(&$error_codes = []) {
    if (!is_null($this->quotationId) && strlen($this->quotationId) > 0 && self::valid_int($this->quotationId, '11')) {
      return true;
    }
    $error_codes[] = 'quotationId';
    return false;
  }

  /**
  *@param array $conditions (required)
  *@param String $raw_sql (optional)
  *@return GpsbuddyRde[]
  **/
  public static function find_all_like($conditions, $raw_sql = '') { return parent::generic_find_all_like($conditions, static::OM_CLASS_NAME, $raw_sql); }
  /**
  *@param array $conditions (required)
  *@param String $raw_sql (optional)
  *@return GpsbuddyRde[]
  **/
  public static function find_all_by($conditions, $raw_sql = '') { return parent::generic_find_all_by($conditions, static::OM_CLASS_NAME, $raw_sql); }
  /**
  *@param String $raw_sql (optional)
  *@return GpsbuddyRde[]
  **/
  public static function find_all($raw_sql = '') { return parent::generic_find_all(static::OM_CLASS_NAME, $raw_sql); }
  /**
  *@param array $conditions (required)
  *@param String $raw_sql (optional)
  *@return GpsbuddyRde
  **/
  public static function find_by($conditions, $raw_sql = '') { return parent::generic_find_by($conditions, static::OM_CLASS_NAME, $raw_sql); }
  /**
  *@param String $id (required)
  *@param String $raw_sql (optional)
  *@return GpsbuddyRde
  **/
  public static function find_by_id($id, $raw_sql = '') { return parent::generic_find_by_id($id, static::OM_CLASS_NAME, $raw_sql); }
  /**
  *@param array $conditions (required)
  *@param String $raw_sql (optional)
  *@return int
  **/
  public static function count_all_by($conditions, $raw_sql = '') { return parent::generic_count_all_by($conditions, static::OM_CLASS_NAME, $raw_sql); }
  /**
  *@param array $conditions (required)
  *@param String $raw_sql (optional)
  *@return bool
  **/
  public static function delete_by($conditions, $raw_sql = '') { return parent::generic_delete_by($conditions, static::OM_CLASS_NAME, $raw_sql); }
}