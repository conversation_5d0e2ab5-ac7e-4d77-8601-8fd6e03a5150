MIGRATIE:

- leegmaken databases staging
DROP TABLE `auction_bids`, `auction_item`, `brand`, `brand_organ`, `car`, `car_content`, `car_image`, `category`, `category_content`, `category_image`, `category_product`, `cat_org_discount`, `color`, `communication_template`, `communication_template_content`, `communication_template_send`, `country`, `credit`, `description`, `discountgroup`, `discountgroup_organ`, `discount_code`, `discount_perc`, `factory_order`, `factory_product`, `factory_product_origin`, `faq`, `faq_cat`, `faq_category`, `faq_category_content`, `faq_content`, `gallery_image`, `gallery_image_content`, `image_content`, `invoice`, `invoice_option`, `invoice_product`, `invoice_product_option`, `invoice_repeat`, `invoice_repeat_invoice`, `invoice_status`, `job_queue`, `log`, `login_ip_address`, `mail_file_box`, `mail_log`, `message`, `multivers`, `newsletter_member`, `orders`, `order_crate`, `order_file`, `order_message`, `order_message_file`, `order_product_picked`, `order_product_picked_origin`, `order_repeat`, `order_repeat_order`, `organisation`, `organisation_address`, `organisation_link`, `organisation_payment`, `organisation_profile`, `packs_date_skip`, `page`, `page_content`, `page_event`, `page_event_subscription`, `page_file`, `page_gallery`, `page_image`, `page_option`, `page_product`, `paylink`, `privilege_user`, `product`, `product_change`, `product_code`, `product_combi`, `product_cont`, `product_content`, `product_cont_content`, `product_cont_discount`, `product_cont_option`, `product_discount_perc`, `product_file`, `product_image`, `product_image_content`, `product_linked`, `product_option`, `product_related`, `product_stock_change`, `product_user_price`, `searched`, `service_product`, `service_product_invoice`, `setting`, `shipping_country`, `simpleagenda`, `site`, `site_host`, `site_message`, `site_message_content`, `smoelen`, `systemlog`, `tag`, `tooltip`, `url_redirect`, `user`, `user_file`, `user_login`, `user_login_rememberme`, `user_profile`, `virtual_stock`, `voip_call`, `voip_extension_mapping`, `voip_user`, `warehouse`, `warehouse_box`, `warehouse_product`, `warehouse_product_change`, `wishlist`, `wishlist_item`, `worker`, `worker_assets`, `worker_baseleave`, `worker_cust_day`, `worker_cust_week`, `worker_function`, `worker_group`, `worker_hours`, `worker_planhour`, `worker_profile`, `worker_project`, `worker_project_hours`, `worker_screen`, `worker_screen_ip`, `worker_sheet`, `worker_workdays`, `worker_yearly_credit`, `work_experience`, `youtube`, `youtube_content`;
DROP TABLE `cargo_receipt`, `cargo_receipt_quotation`, `containers`, `containers_quotations`, `container_img`, `container_tag`, `customer_codes`, `customer_groups`, `customer_paytermincreases`, `customer_productincreases`, `customer_rating_emails`, `damage`, `damage_quotation`, `driving_routes`, `email_to_clients`, `employee_quotation`, `files`, `generated_rackid_quotationids`, `generated_rack_ids`, `generated_rack_ids_employees`, `generated_rack_id_pdfs`, `globals`, `glue_prices`, `imessage`, `imessage_read`, `invoices`, `invoices_irrecoverable`, `invoices_old`, `mitres`, `mitre_price`, `mitre_prices`, `mollie`, `montage`, `montage_quotation`, `montage_quotation_empl`, `old_offertes`, `old_users`, `order_elementparts`, `order_elements`, `order_element_size`, `order_element_windowsill`, `production_employees`, `production_order`, `projects`, `quotations`, `quotations_custom_stone`, `quotations_extra`, `quotations_old`, `quotation_status`, `quote_status_35`, `route_title`, `sandbox_users`, `sandbox_users_pw`, `seam_color`, `seam_stone_color_link`, `settings`, `small_elements`, `spareparts`, `spareparts_prices`, `status`, `status_photo`, `stones`, `stone_brands`, `stone_category`, `stone_colors`, `stone_loss`, `stone_order`, `stone_order_item`, `stone_order_item_quotation`, `stone_prices`, `stone_sizes`, `stone_types`, `telephone_incoming_call`, `vat_rates`, `vgm`, `vgm_check`, `windowsill`;
DROP TABLE `auth_cookie`, `auth_group`, `auth_group_pages`, `auth_menu`, `auth_menu_pages`, `auth_pages`, `auth_users`, `auth_user_group`, `crm_addresses`, `crm_bankaccounts`, `crm_categories`, `crm_companies`, `crm_countries`, `crm_invoiceparties`, `crm_persons`, `migrate_company`, `migrate_persons`;
DROP TABLE `gpsbuddy_rde`, `gpsbuddy_routes`, `gpsbuddy_trucks`;


Offline pagina tonen
crm.php hernoemen
cron.php hernoemen

Cron job uit oude server

Dumpen database:
- alles exporteren + foreigen keys ignore aan NIET rde_website
- unzip localhost.sql
- sql uploaden en importeren: mysql -u rde_root -pZd1wXzJFL9 < localhost.sql

even beheer.raamdorpel.nl aanmaken

Te draaien bij livegang:
// al gebeurt: leegmaken /httpdocs/gsd/uploads/rde/images/catalog/
wget -r -N -l inf -nH --cut-dirs=1 ftp://php7:<EMAIL>/httpdocs/images
wget -r -N -l inf -nH --cut-dirs=1 ftp://php7:<EMAIL>/httpdocs/filesystem
wget -r -N -l inf -nH --cut-dirs=1 ftp://php7:<EMAIL>/httpdocs/temp_pdf
wget -r -N -l inf -nH --cut-dirs=1 ftp://php7:<EMAIL>/httpdocs/gsd/temp
wget -r -N -l inf -nH --cut-dirs=1 ftp://php7:<EMAIL>/httpdocs/gsd/logs
wget -r -N -l inf -nH --cut-dirs=1 ftp://php7:<EMAIL>/httpdocs/gsd/uploads

find -type f -exec chmod 644 {} \;
find -type d -exec chmod 755 {} \;

even map cms hernoemen

Draaien GSDupdater

Ondertussen starten met ip-adressen aanpassen naar ************** in d-hosting panel. (oude ip *************)

Vervolgens verhuizing starten

Cron jobs:
wget -O /dev/null -q "https://beheer.raamdorpel.nl/cron.php" elke 5 minuten
wget -O /dev/null -q "https://www.raamdorpel.nl/cms/scripts/cronjobforpdfmail.php" elke 5 minuten

Verwijderen:
http://site.raamdorpel.nl/
http://beheer.raamdorpel.nl/

Map verwijderen op oude server:
/httpdocs/filesystem/extradata_/

DONE


Mappen:
- 3rdparty: vendors extern librarys
- _pd: globale variables
- b1mo: oude code
- classes: oude classes map
- cms: main map
- cms/framework: bevat oude code Koen Cremers
- downloads: laten staan, dit zijn pdf templates
- filesystem: uploads / gegenereerde bestanden / facturen fysieke bestanden
- image / images: waarschijnlijk voor de website
- include: daniel kijkt na.
- js: daniel kijkt na.
- nlnew2: [VERWIJDERD]
- prefab-raamdorpel: robert nakijken nog gebruikt [VERWIJDERD]
- static: websites. robert nakijken nog gebruikt [VERWIJDERD]
- temp_pdf: facturen robert kijkt na of deze regelmatig leeggegooid mag worden. [CRON SCHRIJVEN DIE LEEG MAAKT]


CMS:
- map: alles van de landkaart. Map 39 werk versie
- crm: bevat het nieuwe gedeelte
- barcode scanner maakt odbc connectie met database rde_b1mo. Sticker voor op element.
- er draait 1 cronjob: cronjobforpdfmail.php elke minuut

RDE LOKAAL:
na download:
- www.example.com.local vervangen door raamdorpel.nl.rde.localhost
  wachtwoord aanpassen naar leeg
- cms/classes/clsAuth.php bovenin: error_reporting(E_ALL & ~E_NOTICE);
- framework/classes/clsMysql.php: error_reporting(E_ALL & ~E_NOTICE);
- error_reporting(E_ALL) verwijderen
- rde_cms ipadres aanpassen naar 127.0.0.1

- op productie pagina zet een harde controle op ip-adres [heb ik aangepast]
- phpqrcode: waar gebruikt? nog nodig? [Vewijderd]

Vragen bart:
- nog voldoende ruimte voor backup

Todo:
- temp_pdf: cronjob maken die deze map leegt.
- rde losmaken van gsdfw
- rde daniel samenvoegen met huidige rde


Een gedeelte van het CMS werkt met XSL-T
https://www.w3schools.com/xml/xsl_if.asp
In PHP grofweg bijv:
clsGeneral.php: de data word verzameld uit de database en op een array gezet.
clsFormGeneral.php: de data word vervolgens omget in een DOMDocument. De variablen worden beschikbaar gemaakt element op het DOMdocument.
clsPageBuilder.php: getPage() zet deze XML met een XSLT om in een html document.