<?php
class BaseImessage extends AppModel
{
  const DB_NAME = 'rde_b1mo';
  const TABLE_NAME = 'imessage';
  const OM_CLASS_NAME = 'Imessage';
  const columns = ['id', 'user_id', 'company_id', 'subject', 'message', 'date', 'construction', 'constructiontraders', 'private', 'possible_readers', 'online', 'homepage'];
  const field_structure = [
    'id'                          => ['type' => 'mediumint', 'length' => '8', 'null' => false],
    'user_id'                     => ['type' => 'mediumint', 'length' => '8', 'null' => true],
    'company_id'                  => ['type' => 'mediumint', 'length' => '8', 'null' => true],
    'subject'                     => ['type' => 'varchar', 'length' => '255', 'null' => false],
    'message'                     => ['type' => 'text', 'length' => '', 'null' => false],
    'date'                        => ['type' => 'date', 'length' => '', 'null' => true],
    'construction'                => ['type' => 'boolean', 'length' => '1', 'null' => false],
    'constructiontraders'         => ['type' => 'boolean', 'length' => '1', 'null' => false],
    'private'                     => ['type' => 'boolean', 'length' => '1', 'null' => false],
    'possible_readers'            => ['type' => 'int', 'length' => '6', 'null' => true],
    'online'                      => ['type' => 'boolean', 'length' => '1', 'null' => false],
    'homepage'                    => ['type' => 'boolean', 'length' => '1', 'null' => false],
  ];

  protected static $primary_key = ['id'];
  protected $auto_increment = 'id';

  public $id, $user_id, $company_id, $subject, $message, $date, $construction, $constructiontraders, $private, $possible_readers, $online, $homepage;

  function __construct($obj_arr = [], &$error_codes = []) {
    parent::__construct();
    $this->setDefaults();
    if (!empty($obj_arr)) {
      $this->from_array($obj_arr);
      $this->valid_required_fields($error_codes);
    }
  }

  public function valid(&$error_codes = []) {
    $errors = [];
    foreach(self::columns as $column) {
      if ($column != $this->auto_increment)
      {
        call_user_func_array([$this, 'v_' . $column], [&$errors]);
      }
    }
    if(empty($errors)) {
      return true;
    }
    $error_codes = array_merge($error_codes, $errors);
    return false;
  }

  public function valid_required_fields(&$error_codes = []) {
    $errors = [];
    foreach (self::columns as $column) {
      $function_name = "v_$column";
      if ($column!=$this->auto_increment) {
        call_user_func_array([$this, $function_name], [&$errors]);
      }
    }
    if (empty($errors)) {
      return true;
    }
    $error_codes = array_merge($error_codes, $errors);
    return false;
  }

  public function setDefaults() {
    $this->construction = 0;
    $this->constructiontraders = 0;
    $this->private = 0;
    $this->possible_readers = 0;
    $this->online = 1;
    $this->homepage = 0;
  }



  public function v_id(&$error_codes = []) {
    if (!is_null($this->id) && strlen($this->id) > 0 && self::valid_mediumint($this->id, '8')) {
      return true;
    }
    $error_codes[] = 'id';
    return false;
  }

  public function v_user_id(&$error_codes = []) {
    if (is_null($this->user_id) || strlen($this->user_id) == 0 || self::valid_mediumint($this->user_id, '8')) {
      return true;
    }
    $error_codes[] = 'user_id';
    return false;
  }

  public function v_company_id(&$error_codes = []) {
    if (is_null($this->company_id) || strlen($this->company_id) == 0 || self::valid_mediumint($this->company_id, '8')) {
      return true;
    }
    $error_codes[] = 'company_id';
    return false;
  }

  public function v_subject(&$error_codes = []) {
    if (!is_null($this->subject) && strlen($this->subject) > 0 && self::valid_varchar($this->subject, '255')) {
      return true;
    }
    $error_codes[] = 'subject';
    return false;
  }

  public function v_message(&$error_codes = []) {
    if (!is_null($this->message) && strlen($this->message) > 0 && self::valid_text($this->message)) {
      return true;
    }
    $error_codes[] = 'message';
    return false;
  }

  public function v_date(&$error_codes = []) {
    if (is_null($this->date) || strlen($this->date) == 0 || self::valid_date($this->date)) {
      return true;
    }
    $error_codes[] = 'date';
    return false;
  }

  public function v_construction(&$error_codes = []) {
    if (!is_null($this->construction) && strlen($this->construction) > 0 && self::valid_tinyint($this->construction, '1')) {
      return true;
    }
    $error_codes[] = 'construction';
    return false;
  }

  public function v_constructiontraders(&$error_codes = []) {
    if (!is_null($this->constructiontraders) && strlen($this->constructiontraders) > 0 && self::valid_tinyint($this->constructiontraders, '1')) {
      return true;
    }
    $error_codes[] = 'constructiontraders';
    return false;
  }

  public function v_private(&$error_codes = []) {
    if (!is_null($this->private) && strlen($this->private) > 0 && self::valid_tinyint($this->private, '1')) {
      return true;
    }
    $error_codes[] = 'private';
    return false;
  }

  public function v_possible_readers(&$error_codes = []) {
    if (is_null($this->possible_readers) || strlen($this->possible_readers) == 0 || self::valid_int($this->possible_readers, '6')) {
      return true;
    }
    $error_codes[] = 'possible_readers';
    return false;
  }

  public function v_online(&$error_codes = []) {
    if (!is_null($this->online) && strlen($this->online) > 0 && self::valid_tinyint($this->online, '1')) {
      return true;
    }
    $error_codes[] = 'online';
    return false;
  }

  public function v_homepage(&$error_codes = []) {
    if (!is_null($this->homepage) && strlen($this->homepage) > 0 && self::valid_tinyint($this->homepage, '1')) {
      return true;
    }
    $error_codes[] = 'homepage';
    return false;
  }

  /**
  *@param array $conditions (required)
  *@param String $raw_sql (optional)
  *@return Imessage[]
  **/
  public static function find_all_like($conditions, $raw_sql = '') { return parent::generic_find_all_like($conditions, static::OM_CLASS_NAME, $raw_sql); }
  /**
  *@param array $conditions (required)
  *@param String $raw_sql (optional)
  *@return Imessage[]
  **/
  public static function find_all_by($conditions, $raw_sql = '') { return parent::generic_find_all_by($conditions, static::OM_CLASS_NAME, $raw_sql); }
  /**
  *@param String $raw_sql (optional)
  *@return Imessage[]
  **/
  public static function find_all($raw_sql = '') { return parent::generic_find_all(static::OM_CLASS_NAME, $raw_sql); }
  /**
  *@param array $conditions (required)
  *@param String $raw_sql (optional)
  *@return Imessage
  **/
  public static function find_by($conditions, $raw_sql = '') { return parent::generic_find_by($conditions, static::OM_CLASS_NAME, $raw_sql); }
  /**
  *@param String $id (required)
  *@param String $raw_sql (optional)
  *@return Imessage
  **/
  public static function find_by_id($id, $raw_sql = '') { return parent::generic_find_by_id($id, static::OM_CLASS_NAME, $raw_sql); }
  /**
  *@param array $conditions (required)
  *@param String $raw_sql (optional)
  *@return int
  **/
  public static function count_all_by($conditions, $raw_sql = '') { return parent::generic_count_all_by($conditions, static::OM_CLASS_NAME, $raw_sql); }
  /**
  *@param array $conditions (required)
  *@param String $raw_sql (optional)
  *@return bool
  **/
  public static function delete_by($conditions, $raw_sql = '') { return parent::generic_delete_by($conditions, static::OM_CLASS_NAME, $raw_sql); }
}