<?php
class BaseStoneColors extends AppModel
{
  const DB_NAME = 'rde_b1mo';
  const TABLE_NAME = 'stone_colors';
  const OM_CLASS_NAME = 'StoneColors';
  const columns = ['colorId', 'brandId', 'short', 'name', 'glaced', 'display', 'common', 'discount', 'hexcolor', 'filename'];
  const field_structure = [
    'colorId'                     => ['type' => 'int', 'length' => '3', 'null' => false],
    'brandId'                     => ['type' => 'tinyint', 'length' => '2', 'null' => false],
    'short'                       => ['type' => 'varchar', 'length' => '15', 'null' => true],
    'name'                        => ['type' => 'varchar', 'length' => '150', 'null' => false],
    'glaced'                      => ['type' => 'enum', 'length' => '2', 'null' => false, 'enums' => ['false','true']],
    'display'                     => ['type' => 'enum', 'length' => '2', 'null' => false, 'enums' => ['false','true']],
    'common'                      => ['type' => 'enum', 'length' => '2', 'null' => false, 'enums' => ['false','true']],
    'discount'                    => ['type' => 'decimal', 'length' => '6,2', 'null' => false],
    'hexcolor'                    => ['type' => 'varchar', 'length' => '10', 'null' => true],
    'filename'                    => ['type' => 'varchar', 'length' => '100', 'null' => true],
  ];

  protected static $primary_key = ['colorId'];
  protected $auto_increment = 'colorId';

  public $colorId, $brandId, $short, $name, $glaced, $display, $common, $discount, $hexcolor, $filename;

  function __construct($obj_arr = [], &$error_codes = []) {
    parent::__construct();
    $this->setDefaults();
    if (!empty($obj_arr)) {
      $this->from_array($obj_arr);
      $this->valid_required_fields($error_codes);
    }
  }

  /**
  * Set default object values
  **/
  public function setDefaults() {
    $this->brandId = 1;
    $this->glaced = 'true';
    $this->display = 'true';
    $this->common = 'false';
    $this->discount = 0.00;
  }

  /**
   * @param array $conditions (required)
   * @param string $raw_sql (optional)
   * @return StoneColors[]
   **/
  public static function find_all_like($conditions, $raw_sql = '') {
    return parent::generic_find_all_like($conditions, static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param array $conditions (required)
   * @param string $raw_sql (optional)
   * @return StoneColors[]
   **/
  public static function find_all_by($conditions, $raw_sql = '') {
    return parent::generic_find_all_by($conditions, static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param string $raw_sql (optional)
   * @return StoneColors[]
   **/
  public static function find_all($raw_sql = '') {
    return parent::generic_find_all(static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param array $conditions (required)
   * @param string $raw_sql (optional)
   * @return StoneColors
   **/
  public static function find_by($conditions, $raw_sql = '') {
    return parent::generic_find_by($conditions, static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param string $id (required)
   * @param string $raw_sql (optional)
   * @return StoneColors
   **/
  public static function find_by_id($id, $raw_sql = '') {
    return parent::generic_find_by_id($id, static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param array $conditions (required)
   * @param string $raw_sql (optional)
   * @return int
   **/
  public static function count_all_by($conditions, $raw_sql = '') {
    return parent::generic_count_all_by($conditions, static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param array $conditions (required)
   * @param string $raw_sql (optional)
   * @return bool
   **/
  public static function delete_by($conditions, $raw_sql = '') {
    return parent::generic_delete_by($conditions, static::OM_CLASS_NAME, $raw_sql);
  }

}