<?php
class BaseCrmPersons extends AppModel
{
  const DB_NAME = 'rde_cms';
  const TABLE_NAME = 'crm_persons';
  const OM_CLASS_NAME = 'CrmPersons';
  const columns = ['personId', 'pid', 'companyId', 'gender', 'firstName', 'lastName', 'department', 'jobtitle', 'phone', 'mobile', 'fax', 'email', 'birthday', 'country', 'notes', 'dateCreated', 'lastUpdated', 'flagForDeletion', 'flagForDirectMail', 'flagForExecutor'];
  const field_structure = [
    'personId'                    => ['type' => 'int', 'length' => '8', 'null' => false],
    'pid'                         => ['type' => 'int', 'length' => '11', 'null' => true],
    'companyId'                   => ['type' => 'int', 'length' => '7', 'null' => false],
    'gender'                      => ['type' => 'enum', 'length' => '2', 'null' => true, 'enums' => ['female','male']],
    'firstName'                   => ['type' => 'varchar', 'length' => '50', 'null' => true],
    'lastName'                    => ['type' => 'varchar', 'length' => '75', 'null' => true],
    'department'                  => ['type' => 'varchar', 'length' => '35', 'null' => true],
    'jobtitle'                    => ['type' => 'varchar', 'length' => '35', 'null' => true],
    'phone'                       => ['type' => 'varchar', 'length' => '15', 'null' => true],
    'mobile'                      => ['type' => 'varchar', 'length' => '15', 'null' => true],
    'fax'                         => ['type' => 'varchar', 'length' => '15', 'null' => true],
    'email'                       => ['type' => 'varchar', 'length' => '150', 'null' => true],
    'birthday'                    => ['type' => 'date', 'length' => '', 'null' => true],
    'country'                     => ['type' => 'varchar', 'length' => '2', 'null' => false],
    'notes'                       => ['type' => 'text', 'length' => '', 'null' => true],
    'dateCreated'                 => ['type' => 'date', 'length' => '', 'null' => false],
    'lastUpdated'                 => ['type' => 'timestamp', 'length' => '', 'null' => false],
    'flagForDeletion'             => ['type' => 'boolean', 'length' => '1', 'null' => false],
    'flagForDirectMail'           => ['type' => 'boolean', 'length' => '1', 'null' => false],
    'flagForExecutor'             => ['type' => 'boolean', 'length' => '1', 'null' => false],
  ];

  protected static $primary_key = ['personId'];
  protected $auto_increment = 'personId';

  public $personId, $pid, $companyId, $gender, $firstName, $lastName, $department, $jobtitle, $phone, $mobile, $fax, $email, $birthday, $country, $notes, $dateCreated, $lastUpdated, $flagForDeletion, $flagForDirectMail, $flagForExecutor;

  function __construct($obj_arr = [], &$error_codes = []) {
    parent::__construct();
    $this->setDefaults();
    if (!empty($obj_arr)) {
      $this->from_array($obj_arr);
      $this->valid_required_fields($error_codes);
    }
  }

  public function valid(&$error_codes = []) {
    $errors = [];
    foreach(self::columns as $column) {
      if ($column != $this->auto_increment)
      {
        call_user_func_array([$this, 'v_' . $column], [&$errors]);
      }
    }
    if(empty($errors)) {
      return true;
    }
    $error_codes = array_merge($error_codes, $errors);
    return false;
  }

  public function valid_required_fields(&$error_codes = []) {
    $errors = [];
    foreach (self::columns as $column) {
      $function_name = "v_$column";
      if ($column!=$this->auto_increment) {
        call_user_func_array([$this, $function_name], [&$errors]);
      }
    }
    if (empty($errors)) {
      return true;
    }
    $error_codes = array_merge($error_codes, $errors);
    return false;
  }

  public function setDefaults() {
    $this->country = 'NL';
    $this->flagForDeletion = 0;
    $this->flagForDirectMail = 0;
    $this->flagForExecutor = 0;
  }



  public function v_personId(&$error_codes = []) {
    if (!is_null($this->personId) && strlen($this->personId) > 0 && self::valid_int($this->personId, '8')) {
      return true;
    }
    $error_codes[] = 'personId';
    return false;
  }

  public function v_pid(&$error_codes = []) {
    if (is_null($this->pid) || strlen($this->pid) == 0 || self::valid_int($this->pid, '11')) {
      return true;
    }
    $error_codes[] = 'pid';
    return false;
  }

  public function v_companyId(&$error_codes = []) {
    if (!is_null($this->companyId) && strlen($this->companyId) > 0 && self::valid_int($this->companyId, '7')) {
      return true;
    }
    $error_codes[] = 'companyId';
    return false;
  }

  public function v_gender(&$error_codes = []) {
    if ($this->gender == 'female') { return true; }
    if ($this->gender == 'male') { return true; }
    if (is_null($this->gender)) { return true; }
    $error_codes[] = 'gender';
    return false;
  }

  public function v_firstName(&$error_codes = []) {
    if (is_null($this->firstName) || strlen($this->firstName) == 0 || self::valid_varchar($this->firstName, '50')) {
      return true;
    }
    $error_codes[] = 'firstName';
    return false;
  }

  public function v_lastName(&$error_codes = []) {
    if (is_null($this->lastName) || strlen($this->lastName) == 0 || self::valid_varchar($this->lastName, '75')) {
      return true;
    }
    $error_codes[] = 'lastName';
    return false;
  }

  public function v_department(&$error_codes = []) {
    if (is_null($this->department) || strlen($this->department) == 0 || self::valid_varchar($this->department, '35')) {
      return true;
    }
    $error_codes[] = 'department';
    return false;
  }

  public function v_jobtitle(&$error_codes = []) {
    if (is_null($this->jobtitle) || strlen($this->jobtitle) == 0 || self::valid_varchar($this->jobtitle, '35')) {
      return true;
    }
    $error_codes[] = 'jobtitle';
    return false;
  }

  public function v_phone(&$error_codes = []) {
    if (is_null($this->phone) || strlen($this->phone) == 0 || self::valid_varchar($this->phone, '15')) {
      return true;
    }
    $error_codes[] = 'phone';
    return false;
  }

  public function v_mobile(&$error_codes = []) {
    if (is_null($this->mobile) || strlen($this->mobile) == 0 || self::valid_varchar($this->mobile, '15')) {
      return true;
    }
    $error_codes[] = 'mobile';
    return false;
  }

  public function v_fax(&$error_codes = []) {
    if (is_null($this->fax) || strlen($this->fax) == 0 || self::valid_varchar($this->fax, '15')) {
      return true;
    }
    $error_codes[] = 'fax';
    return false;
  }

  public function v_email(&$error_codes = []) {
    if (is_null($this->email) || strlen($this->email) == 0 || self::valid_varchar($this->email, '150')) {
      return true;
    }
    $error_codes[] = 'email';
    return false;
  }

  public function v_birthday(&$error_codes = []) {
    if (is_null($this->birthday) || strlen($this->birthday) == 0 || self::valid_date($this->birthday)) {
      return true;
    }
    $error_codes[] = 'birthday';
    return false;
  }

  public function v_country(&$error_codes = []) {
    if (!is_null($this->country) && strlen($this->country) > 0 && self::valid_varchar($this->country, '2')) {
      return true;
    }
    $error_codes[] = 'country';
    return false;
  }

  public function v_notes(&$error_codes = []) {
    if (is_null($this->notes) || strlen($this->notes) == 0 || self::valid_text($this->notes)) {
      return true;
    }
    $error_codes[] = 'notes';
    return false;
  }

  public function v_dateCreated(&$error_codes = []) {
    if (!is_null($this->dateCreated) && strlen($this->dateCreated) > 0 && self::valid_date($this->dateCreated)) {
      return true;
    }
    $error_codes[] = 'dateCreated';
    return false;
  }

  public function v_lastUpdated(&$error_codes = []) {
    if (!is_null($this->lastUpdated) && strlen($this->lastUpdated) > 0 && self::valid_timestamp($this->lastUpdated)) {
      return true;
    }
    $error_codes[] = 'lastUpdated';
    return false;
  }

  public function v_flagForDeletion(&$error_codes = []) {
    if (!is_null($this->flagForDeletion) && strlen($this->flagForDeletion) > 0 && self::valid_tinyint($this->flagForDeletion, '1')) {
      return true;
    }
    $error_codes[] = 'flagForDeletion';
    return false;
  }

  public function v_flagForDirectMail(&$error_codes = []) {
    if (!is_null($this->flagForDirectMail) && strlen($this->flagForDirectMail) > 0 && self::valid_tinyint($this->flagForDirectMail, '1')) {
      return true;
    }
    $error_codes[] = 'flagForDirectMail';
    return false;
  }

  public function v_flagForExecutor(&$error_codes = []) {
    if (!is_null($this->flagForExecutor) && strlen($this->flagForExecutor) > 0 && self::valid_tinyint($this->flagForExecutor, '1')) {
      return true;
    }
    $error_codes[] = 'flagForExecutor';
    return false;
  }

  /**
  *@param array $conditions (required)
  *@param String $raw_sql (optional)
  *@return CrmPersons[]
  **/
  public static function find_all_like($conditions, $raw_sql = '') { return parent::generic_find_all_like($conditions, static::OM_CLASS_NAME, $raw_sql); }
  /**
  *@param array $conditions (required)
  *@param String $raw_sql (optional)
  *@return CrmPersons[]
  **/
  public static function find_all_by($conditions, $raw_sql = '') { return parent::generic_find_all_by($conditions, static::OM_CLASS_NAME, $raw_sql); }
  /**
  *@param String $raw_sql (optional)
  *@return CrmPersons[]
  **/
  public static function find_all($raw_sql = '') { return parent::generic_find_all(static::OM_CLASS_NAME, $raw_sql); }
  /**
  *@param array $conditions (required)
  *@param String $raw_sql (optional)
  *@return CrmPersons
  **/
  public static function find_by($conditions, $raw_sql = '') { return parent::generic_find_by($conditions, static::OM_CLASS_NAME, $raw_sql); }
  /**
  *@param String $id (required)
  *@param String $raw_sql (optional)
  *@return CrmPersons
  **/
  public static function find_by_id($id, $raw_sql = '') { return parent::generic_find_by_id($id, static::OM_CLASS_NAME, $raw_sql); }
  /**
  *@param array $conditions (required)
  *@param String $raw_sql (optional)
  *@return int
  **/
  public static function count_all_by($conditions, $raw_sql = '') { return parent::generic_count_all_by($conditions, static::OM_CLASS_NAME, $raw_sql); }
  /**
  *@param array $conditions (required)
  *@param String $raw_sql (optional)
  *@return bool
  **/
  public static function delete_by($conditions, $raw_sql = '') { return parent::generic_delete_by($conditions, static::OM_CLASS_NAME, $raw_sql); }
}