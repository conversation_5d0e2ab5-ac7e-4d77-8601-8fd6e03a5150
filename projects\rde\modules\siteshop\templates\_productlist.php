<?php if(isset($categories) && count($categories) > 0) : ?>
  <div id="search_categories">
    <div class="panel-heading">
      <h3 class="panel-title">
        Categorieën
      </h3>
    </div>
    <div class="row">
      <?php foreach ($categories as $subcat) : ?>
        <?php TemplateHelper::includePartial('_categoryitem.php', 'siteshop', ['categoryitem' => $subcat]); ?>
      <?php endforeach; ?>
    </div>
  </div>
<?php endif; ?>

<?php if(isset($brands) && count($brands) > 0) : ?>
  <div id="search_brands">
    <div class="panel-heading">
      <h3 class="panel-title">
        Merken
      </h3>
    </div>
    <div class="row">
      <?php foreach ($brands as $id => $brand) : ?>
        <?php TemplateHelper::includePartial("_branditem.php", 'siteshop', ['branditem' => $brand]); ?>
      <?php endforeach; ?>
    </div>
  </div>
<?php endif; ?>

<?php if(isset($products) && count($products) > 0) : ?>
  <!-- <div style="padding: 0 20px;" ><hr class="sp" style="width: 100%;"/></div> -->
  <div id="search_products">
    <div style="margin: 0px 20px 0px 0px;">
      <div class="panel-heading">
        <h3 class="panel-title">
          Producten
        </h3>
      </div>
      <?php if(isset($pager)) echo $pager->writePreviousNextResp(); ?>
    </div>
    <div class="row">
      <?php foreach ($products as $product) : ?>
        <?php TemplateHelper::includePartial('_productitem.php', 'siteshop', ['product' => $product]); ?>
      <?php endforeach; ?>
      <div class="col-md-12">
        <?php if(isset($pager)) echo $pager->writePreviousNextResp() ?>
      </div>
    </div>
  </div>
<?php endif; ?>

<?php if(count($products)==0 && (!isset($categories) || count($categories)==0) && (!isset($brands) || count($brands)==0)): ?>
  Er zijn geen producten, merken of productgroepen gevonden.<br/><br/>
<?php endif ?>

<div class="clear"></div>