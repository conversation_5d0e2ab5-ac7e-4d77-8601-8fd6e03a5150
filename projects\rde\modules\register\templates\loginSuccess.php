<section>

  <div class="contenttxt">
    <?php if (isset($fromwebshop)): ?>
      <h1>Bestellen - inloggen of een nieuwe account aanmaken</h1>
      <p>
        Hier kun u inloggen of een nieuw account aanmaken.
      </p>
    <?php else: ?>
      <h1>Inloggen of een account aanmaken</h1>
      <p>
        Op deze pagina kunt u inloggen op ons systeem, of een nieuw account aanmaken.
        <Br/><Br/>
      </p>
    <?php endif; ?>
  </div>
  <div class="row">
    <div class="col6 col12-xs">
      <h1>Inloggen</h1>
      <br/>
      <?php if ($login_error !== false): ?>
        <div class="alert alert-danger">
          <?php echo $login_error ?>
        </div>
      <?php endif; ?>
      <form method="post">

        <div class="form-row">
          <div>
            <input type="email" class="form-input" name="email"
                   value="<?php if (isset($_POST["email"])) echo $_POST["email"]; elseif (isset($_GET["email"])) echo $_GET["email"]; ?>" size="40" maxlength="255"
                   placeholder="<?php echo __("E-mailadres") ?>" required/>
          </div>
        </div>

        <div class="form-row">
          <div>
            <input type="password" class="form-input" name="password" id="password" value="" size="30" maxlength="50" placeholder="<?php echo __("Wachtwoord") ?>" required/>
          </div>
        </div>

        <div class="form-row">
          <div>
            <br/>
            <input type="submit" value="<?php echo __("Inloggen") ?>" class="btn btn-primary" name="inloggen" id="inloggen"/>
            &nbsp;&nbsp;<a href="?" id="passwordforgotten">Wachtwoord vergeten</a>
          </div>
        </div>
        <br/><br/>
      </form>
    </div>
    <div class="col6 col12-xs">
      <h1>Nieuwe klant?</h1>
      <br/>
      Bent u voor de eerste keer hier?<br/>
      Klik op de onderstaande knop om een account aan te maken, zodat u eenvoudig offertes kunt samenstellen.<br/>
      <br/><br/>
      <a href="<?php echo PageMap::getUrl(207) . (isset($fromwebshop) ? "?fromwebshop=1" : "") ?>" class="btn btn-primary">REGISTEREN</a>
      <br/><br/>
    </div>

  </div>
</section>
<script type="text/javascript">

  function passwordforgotten() {
    swal({
      title: 'Wachtwoord vergeten',
      text: 'Vul het e-mailadres in waarmee u inlogt op ons systeem.',
      input: 'email',
      inputPlaceholder: 'Uw e-mailadres',
      showCancelButton: true,
      confirmButtonText: 'Opvragen',
      inputValidator: function (value) {
        return new Promise(function (resolve, reject) {
          if (validateEmail(value)) {
            //resolve(value); // fulfilled
            swal.showLoading();
            $.post("?action=passwordforgotten", {emailforgotten: value}, function (data) {
              if (data.success) {
                swal("Wachtwoord instellen", data.message, "success").then(function (text) {
                  location.reload();
                }).catch(swal.noop);
              }
              else {
                swal("Foutmelding", data.message, "error").then(function (text) {
                }).catch(swal.noop);
              }
            }, "json");

          }
          else {
            swal("Foutmelding", "Voer een geldig e-mailadres in.", "error").then(function (result) {
              passwordforgotten();
            })
            //reject(new Error('Voer een geldig e-mailadres in.')); // reject

          }
        })
      }
    }).catch(swal.noop);
  }

  $(document).ready(function () {
    $("#passwordforgotten").click(function (e) {
      e.preventDefault();
      passwordforgotten();
    });
  });

</script>
