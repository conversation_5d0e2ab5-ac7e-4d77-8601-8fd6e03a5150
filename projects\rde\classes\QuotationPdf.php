<?php

  /**
   * Genereer offerte/bestelling PDF
   */
  class QuotationPdf extends GSDPDF {

    private $show = false;
    private Quotations $quotation;
    private QuotationsExtra|bool $quotation_extra;
    /** @var OrderElements[] $elements */
    private array $elements;
    /** @var Mitres[] $mitres */
    private array $mitres;
    /** @var Stones $stone */
    private $stone;
    /** @var StoneSizes $size */
    private $size = false;

    private $variant = "standard";

    public function __construct($quotationId) {
      parent::__construct();

      $this->AddFont('Nunito', '', 'Nunito-Regular.ttf', true);
      $this->AddFont('Nunito', 'B', 'Nunito-Bold.ttf', true);
      $this->SetAutoPageBreak(true, 30);

      $this->quotation = Quotations::find_by(["quotationId" => $quotationId]);
      if (!($this->quotation->offerteType == '' || $this->quotation->offerteType == 'RDE')) {
        die("Onbekend offerteType");
      }
      $this->quotation_extra = QuotationsExtra::find_by(["quotationId" => $this->quotation->quotationId]);
      if(!$this->quotation_extra) {
        throw new GsdException("QuotationExtra niet gevonden. Quotation id: ".$this->quotation->quotationId);
      }
      $this->elements = OrderElements::find_all_by(["quotationId" => $this->quotation->quotationId]);
      $this->stone = Stones::find_by(["stoneId" => $this->quotation->stoneId]);
      if ($this->stone) {
        $this->size = StoneSizes::find_by(["sizeId" => $this->stone->sizeId]);
      }
    }

    /**
     * @return bool
     */
    public function isShow(): bool {
      return $this->show;
    }

    /**
     * @param bool $show
     */
    public function setShow(bool $show): void {
      $this->show = $show;
    }


    /**
     * @return string
     */
    public function getVariant(): string {
      return $this->variant;
    }

    /**
     * @param string $variant
     */
    public function setVariant(string $variant): void {
      $this->variant = $variant;
    }


    public function Header() {
      //$this->Image('C:\Work\www\HTTPDOCS\html\rde\filesystem\raamdorpel\clients\quotation\elementparts\105883\159915.png',null,null,0,0,'png');
      $this->Image(DIR_PROJECT_FOLDER . "templates/backend2/images/logopdf.png", null, null, 90);
      $this->SetFont('Nunito', '', 10);
      $this->Cell(25);
      $this->Cell(0, 5, '', 0, 1, 'L', false);

      $this->SetLeftMargin(104);
      $this->SetY(10.5);
      $this->SetFont('', 'B');
      $this->Cell(91, 4, "Raamdorpelelementen BV", 0, 1, "R");
      $this->SetFont('', '');
      $this->Cell(91, 4, "Raambrug 9", 0, 1, "R");
      $this->Cell(91, 4, "5531 AG Bladel", 0, 1, "R");
      $this->Cell(91, 4, "0497 36 07 91", 0, 1, "R");
      $this->Cell(91, 4, "<EMAIL>", 0, 1, "R");
      $this->Cell(91, 4, "www.raamdorpel.nl", 0, 1, "R");
      $this->Ln();
      $this->Ln(3.5);

      $this->SetX(10);
      $this->SetLeftMargin(10);
    }

    // Footer
    // - Public
    // Adds HeBlad Raamdorpelelementen page footer
    public function Footer() {

      $this->setY(-25);
      $this->SetFont('Nunito', 'B', 10);
      $this->Cell(0, 7, 'Raamdorpel.nl - Raamdorpelelementen BV', 'T', 1, 'L', false);

      $this->SetFont('Nunito', '', 10);
      $this->SetTextColor(128, 128, 128);
      $this->Cell(15, 5, 'Adres:');
      $this->SetTextColor(0, 0, 0);
      $this->Cell(50, 5, 'Raambrug 9', 0, 0, 'L', false);
      $this->SetTextColor(128, 128, 128);
      $this->Cell(25, 5, 'Telefoon:');
      $this->SetTextColor(0, 0, 0);
      $this->Cell(35, 5, '0497 - 36.07.91', 0, 0, 'L', false);
      $this->SetTextColor(128, 128, 128);
      $this->Cell(25, 5, 'E-mail:');
      $this->SetTextColor(0, 0, 0);
      $this->Cell(35, 5, '<EMAIL>', 0, 1, 'L', false);

      $this->Cell(15);
      $this->Cell(50, 5, '5531 AG Bladel', 0, 0, 'L', false);
      $this->SetTextColor(128, 128, 128);
      $this->Cell(25, 5, 'Fax:');
      $this->SetTextColor(0, 0, 0);
      $this->Cell(35, 5, '0497 - 38.09.71', 0, 0, 'L', false);
      $this->SetTextColor(128, 128, 128);
      $this->Cell(25, 5, 'Website:');
      $this->SetTextColor(0, 0, 0);
      $this->Cell(35, 5, 'www.raamdorpel.nl', 0, 1, 'L', false);

    }

    /**
     * @return string filename
     */
    public function generatePdf() {

      if ($this->getVariant() == "vensterbanken") {
        $this->addWindowsills();
      }
      else {
        $this->addMainpage();
        $this->addMitreElements();
        $this->addWindowsills();
      }


      return $this->generate();
    }

    private function addMainpage() {

      $quotation = $this->quotation;
      $stone = $this->stone;
      $quotations_extra = $this->quotation_extra;
      $size = $this->size;

      $elementTotals = OrderElements::calculateTotals($this->elements);
      $custom_stone = QuotationsCustomStone::find_by(["quotationId" => $quotation->quotationId]);
      $sandbox_user = SandboxUsers::find_by(["userId" => $quotation->userId]);
      $brand = $color = false;
      if ($stone) {
        $brand = StoneBrands::find_by(["brandId" => $stone->brandId]);
        $color = StoneColors::find_by(["colorId" => $stone->colorId]);
      }
      $customer_code = CustomerCodes::find_by(["codeId" => $quotation->codeId]);
      $spareparts = Spareparts::getValues();
      if ($stone && $stone->isKeramiek() && $stone->isRaamdorpel()) {
        $this->mitres = AppModel::mapObjectIds(Mitres::getMitresByStoneId($quotation->stoneId), "mitreId");
      }
      else {
        $this->mitres = AppModel::mapObjectIds(Mitres::getMitresByStoneId(Config::get("MITRE_FALLBACK_STONE_ID")), "mitreId");
      }
      $projects = Projects::find_all_by(["quotationId" => $quotation->quotationId], "ORDER BY orderNr");

      $type = "offerte";
      if ($quotation->statusId >= 20) { //bestelling
        $type = "bestelling";
      }


      $company = false;
      $crm_invoiceparty = false;
      $companyName = $sandbox_user->companyName;
      $address = $sandbox_user->getAddress();
      $zipcode = $sandbox_user->zipcode;
      $domestic = $sandbox_user->domestic;
      $country = $sandbox_user->country;

      $fullNameContactPerson = $sandbox_user->getNaam();
      $contactpersonPhone = $sandbox_user->phone;
      $personMobile = $sandbox_user->mobile;
      $personEmail = $sandbox_user->email;

      if ($quotation->companyId != "" && $quotation->companyId != "0") {
        $company = CrmCompanies::find_by(["companyId" => $quotation->companyId]);
        $companyName = $company->name;

        $crm_address = CrmAddresses::find_by(["addressId" => $company->postAddressId]);
        if ($crm_address) {
          $address = $crm_address->getAddress();
          $zipcode = $crm_address->zipcode;
          $domestic = $crm_address->domestic;
          $country = $crm_address->country;
        }

        //bedrijfspersoon gebruiken
        $crm_person = CrmPersons::find_by(["personId" => $sandbox_user->personId, "flagForDeletion" => 0]);
        if ($crm_person) {
          $fullNameContactPerson = $crm_person->getNaam();
          $personMobile = $crm_person->mobile;
          $personEmail = $crm_person->email;
        }

        //bedrijfsgegevens gebruiken
        $contactpersonPhone = $company->phone;

        $crm_invoiceparty = CrmInvoiceparties::find_by(["companyId" => $company->companyId]);

      }

      $this->AddPage();

      //      $this->setSourceFile(DIR_PROJECT_FOLDER."templates/backend2/files/test.pdf");
      //      $tplidx = $this->importPage(1);
      //      $this->setTplidx($tplidx);
      //      $this->hasTemplate = true;
      //      $this->useTemplate($this->tplidx, 0, 0);

      $quotation_title = ucfirst($type);
      $quotation_title .= ' ' . $quotation->getQuotationNumberFull();
      if ($stone) {
        $quotation_title .= ' - ' . Stones::TYPES[$stone->getType()];
      }

      $this->SetCreator('raamdorpel.nl');
      $this->SetAuthor('Raamdorpelelementen BV');
      $this->SetTitle($quotation_title, true);
      $this->SetKeywords('Raamdorpel ' . $quotation->getQuotationNumberFull() . ' ' . $quotation->projectName . ' ' . $quotation->projectReference . ' raamdorpels', true);

      $this->AliasNbPages();

      $this->Image('https://www.raamdorpel.nl/3rdparty/barcodegen/datamatrix.php?text=' . $quotation->getQuotationNumberFull(), 175, 40, 20, 20, 'PNG');

      //Quotenumber
      $this->SetFont('Nunito', '', 18);
      $this->Cell(0, 6, $quotation_title, 0, 1, 'L', false);

      // Date and pagenumber
      $this->Ln(5);
      $this->SetFont('Nunito', 'B', 10);
      if ($type == "bestelling") {
        $this->Cell(30, 5, 'Besteldatum');
        $this->SetFont('Nunito', '', 10);
        $this->Cell(60, 5, $quotation->getProductionDate(), 0, 0, 'L', false);
      }
      else {
        $this->Cell(30, 5, 'Offertedatum');
        $this->SetFont('Nunito', '', 10);
        $this->Cell(60, 5, $quotation->getQuotationDate(), 0, 0, 'L', false);
      }
      $this->SetFont('Nunito', 'B', 10);
      $this->Cell(30, 5, 'Pagina');
      $this->SetFont('Nunito', '', 10);
      $this->Cell(60, 5, $this->PageNo() . ' / {nb}', 0, 1, 'L', false);

      $this->Ln(5);
      $y = $this->GetY();

      //Customer and projectinfo
      $this->writeStandard("Bedrijfsnaam", $companyName);
      $this->writeStandard("Adres", $address);
      $this->writeStandard("", $zipcode . ' ' . $domestic . ' ' . $country);
      $this->writeStandard("Telefoonnummer", $contactpersonPhone);
      if ($crm_invoiceparty && $crm_invoiceparty->vatRegNo != "") {
        $this->writeStandard("BTW-nummer", $crm_invoiceparty->vatRegNo);
      }
      $this->Ln();
      $this->writeStandard("Contactpersoon", $fullNameContactPerson);
      if ($personMobile != "") {
        $this->writeStandard("Mobiel", $personMobile);
      }
      $this->writeStandard("E-mailadres", $personEmail);

      $bottomY = $this->GetY();
      $this->SetY($y);
      $this->SetLeftMargin(100);

      if ($quotation->projectName != "") {
        $this->writeStandard("Projectnaam", $quotation->projectName);
      }
      if ($quotation->projectReference != "") {
        $this->writeStandard("Uw kenmerk", $quotation->projectReference);
      }
      if ($quotation->createdVia != Quotations::CREATED_VIA_WEBSHOP) {

        $this->writeStandard("Merk", $brand->name);

        $colortxt = "Divers";
        if ($color) {
          $colortxt = $color->name;
          if ($stone->isKeramiek()) {
            $colortxt .= ' ' . ($color->glaced == 'true' ? 'geglazuurd' : 'ongeglazuurd');
          }
          $colortxt .= ' ' . $color->short;
          $colortxt = trim($colortxt);
        }
        $this->writeStandard("Kleur", $colortxt);
        $this->writeStandard("Model", $size && !$stone->isBalkje() ? $size->name : 'Divers');

        if ($custom_stone) {
          $this->writeStandard("Diepte", $custom_stone->depth . " mm");
          $this->writeStandard("Dikte", $custom_stone->thickness . " mm");
          $this->writeStandard("Hoogte", $custom_stone->height . " mm");
          $this->writeStandard("Breedte klik", $custom_stone->width_click . " mm");
          $this->writeStandard("Hoogte klik", $custom_stone->height_click . " mm");
        }

        if ($quotations_extra->wall_thickness != "" && $quotations_extra->wall_thickness != 0) {
          $this->writeStandard("Muurdikte", $quotations_extra->wall_thickness);
        }

        //-- Eindstenen
        if ($stone->isKeramiek() && $stone->isRaamdorpel()) {
          $stoneEnd = '';
          if ($quotation->endstone == 'true_grooves') {
            $stoneEnd = 'Ja, groeven';
          }
          elseif ($quotation->endstone == 'true') {
            $stoneEnd = 'Ja, opstaande zijkanten';
          }
          elseif ($quotation->endstone == 'false') {
            $stoneEnd = 'Nee';
          }
          $this->writeStandard("Eindstenen", $stoneEnd);
        }
        if ($elementTotals["fullLength"] != 0) {
          $this->writeStandard("Meters", round(($elementTotals["fullLength"] / 1000), 2) . " m");
        }
      }

      //-- Bezorg adres
      $this->writeStandard($quotations_extra->addressDeliveryId == 20357 ? 'Ophalen op' : "Afleveradres", $quotation->getAddress());

      //-- Bezorgadres 2de rij
      $this->writeStandard("", $quotation->zipcode . ' ' . $quotation->domestic . ' ' . $quotation->country);

      if ($this->GetY() > $bottomY) {
        $bottomY = $this->GetY();
      }

      $this->SetY($bottomY);

      //-----------start elementen---------------
      $this->SetLeftMargin(10);
      $aWidth = [30, 20, 30, 30, 20, 30, 20];

      $elementCount = 0;
      $this->Ln(10);
      if (count($this->elements) > 0) {
        if ($stone->isVensterbank()) {
          $aWidth = [30, 21, 15, 25, 22, 48, 20];
          $elementCount = $this->writeElementsVensterbank($aWidth);
        }
        elseif ($stone->isBalkje()) {
          $aWidth = [30, 25, 25, 25, 25, 35, 20];
          $elementCount = $this->writeElementsBalkje($aWidth);
        }
        else {
          //niet vensterbanken.
          $elementCount = $this->writeElements($aWidth);
        }

        $this->Cell($aWidth[0] + $aWidth[1] + $aWidth[2] + $aWidth[3] + $aWidth[4] + $aWidth[5] + $aWidth[6], 0, '', 'T', true);

        //er zijn projecten en elementen, toon subtotaal
        if (count($projects) > 0) {
          $this->Cell($aWidth[0], 5, "", 0, 0, 'L');
          $this->Cell($aWidth[1], 6, $elementCount . "", 'T', 0, 'L');
          $this->Cell($aWidth[2] + $aWidth[3], 5, "", 0, 0, 'L');
          $this->SetFont('Nunito', 'B');
          $this->Cell($aWidth[4] + $aWidth[5], 5, "Subtotaal", 0, 0, 'R');
          $this->Cell($aWidth[6], 5, "€ " . number_format($quotation->projectValue, 2, ',', '.'), 0, 1, 'R');
        }


      }

      $total = $quotation->projectValue;

      if (count($projects) > 0) {

        $prWidth = [123, 20, 17, 20];
        $this->SetWidths($prWidth);

        $hasExtraProducts = false;
        foreach ($projects as $projectline) {
          if ($projectline->product_id != "") {
            $hasExtraProducts = true;
            break;
          }
        }

        $colname = "";
        if (count($this->elements) > 0) {
          $colname = "Extra producten - ";
        }
        $colname .= "Omschrijving";

        $this->Ln();
        $this->SetFont('Nunito', 'B', 10);
        $this->Cell($prWidth[0], 5, $colname, 'B', 0, 'L', false);
        $this->Cell($prWidth[1], 5, 'Aantal', 'B', 0, 'R', false);
        $this->Cell($prWidth[2], 5, 'Stukprijs', 'B', 0, 'R', false);
        $this->Cell($prWidth[3], 5, "Totaal", 'B', 1, 'R', false);
        $this->SetFont('Nunito', '', 10);

        if ($hasExtraProducts) {

          $this->SetFont('Nunito', '');
          foreach ($projects as $projectline) {
            if ($projectline->product_id == "") continue;
            $row_data = [
              ['text' => $projectline->name, 'align' => 'L', 'border' => ""],
              ['text' => $projectline->size, 'align' => 'R', 'border' => ""],
              ['text' => "€ " . number_format($projectline->pieceprice, 2, ',', '.'), 'align' => 'R', 'border' => ""],
              ['text' => "€ " . number_format($projectline->euro, 2, ',', '.'), 'align' => 'R', 'border' => ""],
            ];
            $this->Row($row_data, 5);
            $total += $projectline->euro;


          }
        }

        foreach ($projects as $projectline) {
          if ($projectline->product_id != "") continue;
          $this->Cell($prWidth[0], 5, $projectline->name, 0, 0, 'L');
          $this->Cell($prWidth[1], 5, $projectline->size, 0, 0, 'R');
          $this->Cell($prWidth[2], 5, "€ " . number_format($projectline->pieceprice, 2, ',', '.'), 0, 0, 'R');
          $this->Cell($prWidth[3], 5, "€ " . number_format($projectline->euro, 2, ',', '.'), 0, 1, 'R');
          $total += $projectline->euro;
        }


        if (count($this->elements) > 0) {
          $this->SetFont('Nunito', 'B', 10);
          $this->Cell($aWidth[0] + $aWidth[1] + $aWidth[2] + $aWidth[3] + $aWidth[4] + $aWidth[5], 5, "Subtotaal", "T", 0, 'R');
          $this->Cell($aWidth[6], 5, "€ " . number_format($total - $quotation->projectValue, 2, ',', '.'), "T", 1, 'R');
          $this->Ln(4);
        }
        else {
          $this->Cell($aWidth[0] + $aWidth[1] + $aWidth[2] + $aWidth[3] + $aWidth[4] + $aWidth[5] + $aWidth[6], 5, "", 'T', 1, 'L');
        }

        if ($quotation->freightCosts != "" || $quotation->createdVia == Quotations::CREATED_VIA_WEBSHOP) { //vrachtkosten
          $this->Cell($aWidth[0] + $aWidth[1] + $aWidth[2] + $aWidth[3] + $aWidth[4], 5, "", 0, 0, 'L', false);
          $this->Cell($aWidth[5], 5, "Vrachtkosten", '', 0, 'R', false);
          $this->Cell($aWidth[6], 5, "€ " . number_format($quotation->freightCosts, 2, ',', '.'), '', 1, 'R', false);
          $total += $quotation->freightCosts;
        }

        //Total
        $this->SetFont('Nunito', 'B', 10);
        $this->Cell($aWidth[0] + $aWidth[1] + $aWidth[2] + $aWidth[3] + $aWidth[4], 5, "", 0, 0, 'L', false);
        $this->Cell($aWidth[5], 5, "Totaal", '', 0, 'R', false);
        $this->Cell($aWidth[6], 5, "€ " . number_format($total, 2, ',', '.'), '', 1, 'R', false);
      }
      else { //geen extra velden
        //Total
        $this->Cell($aWidth[0], 5, "", 0, 0, 'L', false);
        $this->Cell($aWidth[1], 6, $elementCount . "", 'T', 0, 'L', false);
        $this->Cell($aWidth[2] + $aWidth[3], 5, "", 0, 0, 'L', false);
        $this->SetFont('Nunito', 'B', 10);
        $this->Cell($aWidth[4], 5, "", 0, 0, 'L', false);
        $this->Cell($aWidth[5], 5, "Totaal", '', 0, 'R', false);
        $this->Cell($aWidth[6], 5, "€ " . number_format($total, 2, ',', '.'), '', 1, 'R', false);
      }

      $this->Ln(7);

      if ($stone && $size->isOpmaatgemaakt() && $total == 0) {
        $this->SetTextColor(255, 0, 0);
        $this->MultiCell(0, 4, 'LET OP: Dit is een maatwerk offerte van 0 euro, en nog niet compleet. U ontvangt van ons zo spoedig mogelijk een offerte met de juiste prijsstelling. ', 0, 'L', false);
        $this->SetTextColor(0, 0, 0);
        $this->Ln(7);
      }

      if (trim($quotation->customerNotes) != '' || $quotation->deliveryNotes != "" || ($stone && trim($stone->alert) != '')) {

        $this->SetFont('Nunito', 'B', 10);
        $this->Cell(0, 4, 'Opmerkingen', 0, 1, 'L', false);
        $this->SetFont('Nunito', '', 10);
        $this->SetTextColor(21, 122, 21);
        if ($stone && trim($stone->alert) != '') {
          $this->MultiCell(0, 4, $stone->alert, 0, 'L', false);
        }
        if ($stone && $quotation->deliveryNotes != '') {
          $this->MultiCell(0, 4, $quotation->deliveryNotes, 0, 'L', false);
        }
        $this->SetTextColor(255, 0, 0);
        if (trim($quotation->customerNotes) != '') {
          $this->MultiCell(0, 4, $quotation->customerNotes, 0, 'L', false);
        }
        $this->SetTextColor(0, 0, 0);
        $this->Ln(7);

      }

      //--- de offerte heeft een code id.
      //-- tonen of dit om een particulier gaat of niet
      // Particulier = 7
      // Doorstart = 22

      //-- bouwbedrijf
      //-- 3,4,5,6,10,11,12,13,14,15

      //-- bouwmateriaal handel
      //-- 1,2,8,9,16,17,18,19,20,21
      $bouwmateriaalhandel = CustomerGroups::getIdsByTypeid(2);

      $this->SetFont('Nunito', '', 10);
      if ($elementTotals["fullLength"] != 0 && ($quotation->offerteType == '' || ($quotation->offerteType == 'RDE' && in_array($customer_code->groupId, $bouwmateriaalhandel))) && $elementTotals["fullLength"] < 100000) {
        $std_text = 'Gezien het totaal minder is dan 100 meter kunnen de elementen die langer zijn dan 2400 mm in delen worden geleverd. Deze, evenals verstekdelen, kunt u met het gratis bijgeleverde voegsel aan elkaar leggen.';
        if ($stone->isBeton() || $stone->isNatuursteen()) {
          $std_text = 'Gezien het totaal minder is dan 100 meter kunnen de elementen die langer zijn dan 2200 mm in delen worden geleverd.';
          if ($stone->isBeton() && $stone->isRaamdorpel()) {
            $std_text .= ' Deze, evenals verstekdelen, kunt u met het gratis bijgeleverde voegsel aan elkaar leggen.';
          }
        }
        $this->MultiCell(0, 4, $std_text, 0, 'L', false);
        $this->Ln(7);
      }

      if ($customer_code->groupId == 7 && !$custom_stone) {
        $this->SetTextColor(255, 0, 0);
        $this->MultiCell(0, 4, 'Voor aannemers en de bouwmaterialenhandel zijn er speciale kortingen. Om hiervoor in aanmerking te komen dient u dit via e-mail aan te vragen.', 0, 'L', false);
        $this->SetTextColor(0, 0, 0);
        $this->Ln(7);
      }

      if (in_array($customer_code->groupId, $bouwmateriaalhandel)) { //bouwmetshandelaren

        if ($quotation->offerteType == '' && $elementTotals["fullLength"] != 0) {

          $stonecount = 0;
          $iEndstoneL = 0;
          $iEndstoneR = 0;
          $iEndstoneLG = 0;
          $iEndstoneRG = 0;
          foreach ($this->elements as $element) {
            $stonecount += ($element->amount * $element->stoneAmount);
            $iEndstoneL += $element->leftEndstone * $element->amount;
            $iEndstoneR += $element->rightEndstone * $element->amount;
            $iEndstoneLG += $element->leftEndstoneGrooves * $element->amount;
            $iEndstoneRG += $element->rightEndstoneGrooves * $element->amount;
          }
          $prices = Quotations::getPrices($quotations_extra->getQuotationAltPriceYear("Y-m-d"), $quotation->userId, $stone);

          $option2price = 0;
          $option3price = $quotation->projectValue - ($quotation->meters * $spareparts['CMFREIGHTDISCOUNT']);

          $sOption1 = 'De offerte zoals hierboven gespecificeerd. Transportkosten: € ' . number_format($spareparts['FREIGHTCOST'], 2, ',', '.') . '. Indien u meerdere bestellingen heeft en deze in één levering gecombineerd kunnen worden, worden er maar één keer transportkosten gerekend. Voor (deel)leveringen met een totaal boven de 40m worden er geen vrachtkosten gerekend. Totaalprijs: € ' . number_format($quotation->projectValue, 2, ',', '.') . ' excl. eventuele vrachtkosten.';
          $sOption2 = '';
          $sOption3 = 'De offerte zoals hierboven staat gespecificeerd. U haalt zelf de elementen op. De lege bakken en rekken halen wij gratis op uw werf op. U betaalt geen transportkosten en ontvangt bovendien een additionele korting van € ' . number_format($quotation->meters * $spareparts['CMFREIGHTDISCOUNT'], 2, ',', '.') . ' (€ ' . number_format($spareparts['CMFREIGHTDISCOUNT'], 2, ',', '.') . '/meter). Totaalprijs € ' . number_format($option3price, 2, ',', '.') . '.';

          if ($quotation->endstone == 'true_grooves') {
            $option2price = $quotation->projectValue - ($stonecount * $prices['stonePrice']) - (($iEndstoneLG + $iEndstoneRG) * ($prices['stonePriceGroove'] - $prices['stonePrice'])) - ($quotation->meters * $spareparts['CMGLUEDISCOUNT']);
            $sOption2 = 'U levert zelf ' . ($stonecount - $iEndstoneLG - $iEndstoneRG) . ' stenen';
            $sOption2 .= ', ' . $iEndstoneLG . ' linkse en ' . $iEndstoneRG . ' rechste eindstenen met groeven';
            $sOption2 .= ' bij ons af en haalt later de elementen weer op. De lege bakken en rekken worden gratis op uw werf opgehaald. ';
            $sOption2 .= 'Totaalprijs: € ' . number_format($option2price, 2, ',', '.');
          }
          elseif ($quotation->endstone == 'true') {
            $option2price = $quotation->projectValue - ($stonecount * $prices['stonePrice']) - (($iEndstoneL + $iEndstoneR) * ($prices['stonePriceEnd'] - $prices['stonePrice'])) - ($quotation->meters * $spareparts['CMGLUEDISCOUNT']);
            $sOption2 = 'U levert zelf ' . ($stonecount - $iEndstoneL - $iEndstoneR) . ' stenen';
            $sOption2 .= ', ' . $iEndstoneL . ' linkse en ' . $iEndstoneR . ' rechste eindstenen met opstaande zijkant ';
            $sOption2 .= ' bij ons af en haalt later de elementen weer op. De lege bakken en rekken worden gratis op uw werf opgehaald. ';
            $sOption2 .= 'Totaalprijs: € ' . number_format($option2price, 2, ',', '.');
          }
          elseif ($quotation->endstone == 'false') {
            $option2price = $quotation->projectValue - ($stonecount * $prices['stonePrice']) - (($iEndstoneL + $iEndstoneR) * ($prices['stonePriceEnd'] - $prices['stonePrice'])) - ($quotation->meters * $spareparts['CMGLUEDISCOUNT']);
            $sOption2 = 'U levert zelf ' . ($stonecount) . ' stenen';
            $sOption2 .= ' bij ons af en haalt later de elementen weer op. De lege bakken en rekken worden gratis op uw werf opgehaald. ';
            $sOption2 .= 'Totaalprijs: € ' . number_format($option2price, 2, ',', '.');
          }

          if ($type == "offerte") {
            $this->MultiCell(0, 4, 'Speciaal voor bouwmateriaalhandelaren bestaan de volgende mogelijkheden voor deze offerte. Deze kun u aangeven bij het omzetten van uw offerte in een bestelling.');
            $this->Cell(5, 4, '1.', 0, 0, 'R', false);
            $this->MultiCell(175, 4, $sOption1, 0, 'L', false);
            $this->Cell(5, 4, '2.', 0, 0, 'R', false);
            $this->MultiCell(175, 4, $sOption2, 0, 'L', false);
            $this->Cell(5, 4, '3.', 0, 0, 'R', false);
            $this->MultiCell(175, 4, $sOption3, 0, 'L', false);
            $this->Ln(4);
            $this->SetFont('Nunito', 'B', 10);
            $this->Cell(0, 4, 'Aanvullende Voorwaarden voor gebruik bakken en rekken', 0, 1, 'L', false);

            $this->SetFont('Nunito', '', 10);
            $sText = 'Wanneer u voor optie 2 of 3 kiest, betekent dit dat u de elementen zelf vervoert. Elementen worden door ons verpakt in kratten en/of rekken. ' .
              'Deze kunt u 60 dagen gratis gebruiken, vanaf het moment dat de elementen klaar staan op onze werf in Bladel. Binnen die periode dient u ons te contacteren voor het ophalen van de lege bakken en/of rekken. ' .
              'Indien u de bakken of rekken langer nodig heeft, betaalt u € ' . number_format($spareparts['CMBENCHRENTAL'], 2, ',', '.') . ' per rek per dag en € ' . number_format($spareparts['CMCRATERENTAL'], 2, ',', '.') . " per bak per dag aan huur.\n" .
              'Vier weken na opdrachtdatum staan de elementen klaar, tenzij u bij de opdrachtbevestiging uitdrukkelijk anders aangeeft middels een leverschema.';
            $this->MultiCell(0, 4, $sText, 0, 'L', false);

            if ($quotation->pickup == 'false') {
              $this->MultiCell(0, 4, 'De elementen worden door ons geleverd. Voor (deel)leveringen onder de 40m geldt een toeslag van € ' . number_format($spareparts['FREIGHTCOST'], 2, ',', '.') . ' voor transportkosten. Indien u meerdere bestellingen heeft en deze in één levering gecombineerd kunnen worden, wordt deze toeslag maar één keer gerekend.', 0, 'L', false);
            }
            elseif ($quotation->pickup == 'true') {
              $this->MultiCell(0, 4, 'U komt de elementen afhalen bij ons in Bladel.', 0, 'L', false);
            }

          }
          else {
            $this->MultiCell(0, 4, 'U heeft gekozen voor de volgende optie:');
            if ($quotation->mattingOnlyGlueFlag == 1) {
              $this->MultiCell(175, 4, $sOption2, 0, 'L', false);
            }
            elseif ($quotation->mattingRemovalDiscountFlag == 1) {
              $this->MultiCell(175, 4, $sOption3, 0, 'L', false);
            }
            else {
              $this->MultiCell(175, 4, $sOption1, 0, 'L', false);
            }
            $this->Ln(4);
          }


        }

      }
      elseif ($quotation->pickup == 'false' && $elementTotals["fullLength"] != 0) {
        if (in_array($customer_code->groupId, CustomerGroups::getIdsByTypeid(1))) {
          if ($company && $company->introDiscount == 1) {
            $this->SetTextColor(255, 0, 0);
            $this->SetFont('Nunito', 'B');
            $this->MultiCell(0, 4, 'Graag wil ik u kennis laten maken met onze raamdorpelelementen, daarom bied ik u een introductiekorting aan.', 0, 'L');
            $this->MultiCell(0, 4, 'Op uw eerste levering krijgt u een korting van €50,- welke gelijk staat aan de vrachtkosten.', 0, 'L');
            $this->MultiCell(0, 4, 'De eerste levering wordt dus franco geleverd !', 0, 'L');
            $this->SetFont('Nunito', '');
            $this->SetTextColor(0, 0, 0);
          }
          else {
            $this->MultiCell(0, 4, 'Transportkosten: € ' . number_format($spareparts['FREIGHTCOST'], 2, ',', '.') . '. Indien u meerdere bestellingen heeft en deze in één levering gecombineerd kunnen worden, worden er maar één keer transportkosten gerekend. Voor (deel)leveringen met een totaal boven de 40m worden er geen vrachtkosten gerekend.', 0, 'L');
          }
        }
      }
      elseif ($quotation->pickup == 'true') {
        $this->MultiCell(0, 4, 'U komt de elementen afhalen bij ons in Bladel.', 0, 'L');
      }

      $footline = "";
      if ($type == 'offerte') {
        $footline .= "Offerte geldig tot " . $quotation->getQuotationDate("31-12-Y") . ". ";
      }
      $footline .= "Prijzen excl. BTW";
      if ($quotation->pickup == 'false' && $quotation->createdVia != Quotations::CREATED_VIA_WEBSHOP) {
        $footline .= " en bezorging";
      }
      $footline .= ". Levering conform Algemene Voorwaarden Raamdorpelelementen BV, te vinden op www.raamdorpel.nl.";
      $this->Ln(5);
      $this->MultiCell(0, 4, $footline, 0, 'L', false);

    }

    private function generate() {
      $this->SetDisplayMode('real');

      $filename = $this->quotation->getQuotationNumberFull() . '.pdf';
      if ($this->isShow()) {
        $this->Output("I", $filename);
      }
      else {
        $this->Output("F", DIR_TEMP . $filename);
      }
      return $filename;
    }

    public function writeStandard($name, $value) {
      $this->SetFont('Nunito', 'B', 10);
      $this->Cell(30, 5, $name);
      $this->SetFont('Nunito', '', 10);
      $this->Cell(60, 5, $value, 0, 1, 'L', false);
    }

    private function addMitreElements() {
      $stone = $this->stone;
      $quotation = $this->quotation;
      $counter = 0;
      foreach ($this->elements as $element) {

        if ($element->getMitre() == "none") continue;

        $leftMitre = $rightMitre = false;
        if ($element->leftMitreId != "") $leftMitre = $this->mitres[$element->leftMitreId];
        if ($element->rightMitreId != "") $rightMitre = $this->mitres[$element->rightMitreId];

        $aantalElementenPerPagina = 5;
        if ($counter % $aantalElementenPerPagina == 0) {
          $this->AddPage();
          $this->SetFont('Nunito', '', 10);
        }

        $startingYvalue = 45;
        $startingXvalue = 9;
        $newYValue = $counter % $aantalElementenPerPagina * 43;

        $this->setXY($startingXvalue, $startingYvalue + $newYValue);
        $this->SetTextColor(197, 44, 27);
        $this->SetFont('Nunito', 'B');
        $this->Cell(0, 7, 'Element ' . $element->referenceName, 0, 1, 'L', false);
        $this->SetFont('Nunito', '');
        $this->SetTextColor(0, 0, 0);
        $this->Cell(15, 4);


        $mitredir = DIR_ROOT_HTTPDOCS . 'images/mitresnew/';
        if ($this->stone->isMuurafdekker()) {
          $mitredir .= "muurafdekker/";
        }
        elseif ($this->stone->isBalkje()) {
          $mitredir .= "balkjes/";
        }
        elseif ($this->stone->isSpekband() && $this->stone->isKeramiek() && $quotation->brandId == StoneBrands::BRAND_ID_STJORIS) {
          $mitredir .= "spekband-stjoris/";
        }
        elseif ($this->stone->isSpekband() && $this->stone->isKeramiek()) {
          $mitredir .= "spekband/";
        }
        elseif ($element->heartClickSize == 0) {
          $mitredir .= "raamdorpel-hartklik/";
        }
        else {
          $mitredir .= "raamdorpel-totaandepunt/";
        }

        if ($element->heartClickSize == 0) {

          //------------------------- heartclicksize = 0 HARTKLIKMAAT-------------------------
          if (!($this->stone->isSpekband() && $this->stone->isKeramiek() && $quotation->brandId == StoneBrands::BRAND_ID_STJORIS)) {
            if ($leftMitre) {
              $this->setXY($startingXvalue, ($startingYvalue + 25) + $newYValue);
              $this->Cell(0, 7, $leftMitre->angle . ' °', 0, 1, 'L', false);
            }
            if ($rightMitre) {
              $this->setXY(-30, ($startingYvalue + 25) + $newYValue);
              $this->Cell(0, 7, $rightMitre->angle . ' °', 0, 1, 'L', false);
            }
          }


          $this->setXY(58, ($startingYvalue + 5) + $newYValue);
          $this->Cell(0, 7, 'Ingevoerde element maat: ' . $element->inputLength . ' mm', 0, 1, 'L', false);

          if (($this->stone->isSpekband() && $this->stone->isKeramiek() && $quotation->brandId == StoneBrands::BRAND_ID_STJORIS)) {

          }
          elseif ($stone->isRaamdorpel()) {
            $this->setXY(58, ($startingYvalue + 10) + $newYValue);
            $this->Cell(0, 7, 'Elementlengte: ' . $element->elementLengthTotal . ' mm', 0, 1, 'L', false);
          }
          else {
            $this->setXY(58, ($startingYvalue + 10) + $newYValue);
            $this->Cell(0, 7, 'Hart klikmaat element: ' . $element->getElementlengthMinSpacing($quotation, $stone) . ' mm', 0, 1, 'L', false);
          }

          if ($leftMitre) {
            $this->Image($mitredir . 'left/' . floor($leftMitre->angle) . '.png', 37, ($startingYvalue + 20) + $newYValue, 0, 0, 'png');
          }
          else {
            $imagefilename = "90.png";
            if ($this->stone->isSpekband() && $this->stone->isKeramiek() && $quotation->brandId == StoneBrands::BRAND_ID_STJORIS && $element->leftEndstone == 1) {
              $imagefilename = "endstone.png";
            }
            $this->Image($mitredir . 'left/' . $imagefilename, 37, ($startingYvalue + 20) + $newYValue, 0, 0, 'png');

          }
          $this->Image($mitredir . 'middle_long.png', 58.25, ($startingYvalue + 20) + $newYValue, 0, 0, 'png');
          if ($rightMitre) {
            $this->Image($mitredir . 'right/' . floor($rightMitre->angle) . '.png', 124, ($startingYvalue + 20) + $newYValue, 0, 0, 'png');
          }
          else {
            $imagefilename = "90.png";
            if ($this->stone->isSpekband() && $this->stone->isKeramiek() && $quotation->brandId == StoneBrands::BRAND_ID_STJORIS && $element->rightEndstone == 1) {
              $imagefilename = "endstone.png";
            }
            $this->Image($mitredir . 'right/' . $imagefilename, 124, ($startingYvalue + 20) + $newYValue, 0, 0, 'png');
          }

          if ($this->stone->isSpekband() && $this->stone->isKeramiek() && $quotation->brandId == StoneBrands::BRAND_ID_STJORIS) {
            if ($element->leftEndstone == 1) {
              $this->SetTextColor(122, 122, 122);
              $this->setXY(24, ($startingYvalue + 20) + $newYValue);
              $this->Cell(0, 7, 'eindsteen', 0, 1, 'L', false);
            }
          }
          else {
            $this->setXY($startingXvalue, ($startingYvalue + 15) + $newYValue);
            $this->SetTextColor(122, 122, 122); //grijs
            $this->Cell(0, 7, 'Speling ' . $element->getSpelingLeft($quotation, $stone) . ' mm', 0, 1, 'L', false);

            if ($element->flagWindow == 'single' && $element->getSpelingLeft($quotation, $stone) == 0) {
              $this->setXY($startingXvalue, ($startingYvalue + 20) + $newYValue);
              $this->Cell(0, 7, 'Enkel vlagkozijn', 0, 1, 'L', false);
            }

          }


          $this->SetTextColor(0, 0, 0);

          if ($this->stone->isSpekband() && $this->stone->isKeramiek() && $quotation->brandId == StoneBrands::BRAND_ID_STJORIS) {
            if ($element->rightEndstone == 1) {
              $this->SetTextColor(122, 122, 122);
              $this->setXY(167, ($startingYvalue + 20) + $newYValue);
              $this->Cell(0, 7, 'eindsteen', 0, 1, 'L', false);
            }
          }
          else {
            //-- Speling rechts
            $this->setXY(167, ($startingYvalue + 15) + $newYValue);

            $this->SetTextColor(122, 122, 122);
            $this->Cell(0, 7, "Speling " . $element->getSpelingRight($quotation, $stone) . ' mm', 0, 1, 'L', false);

            if ($element->flagWindow === 'single' && $element->getSpelingRight($quotation, $stone) === 0) {
              $this->setXY(167, ($startingYvalue + 20) + $newYValue);
              $this->Cell(0, 7, 'Enkel vlagkozijn', 0, 1, 'L', false);
            }

          }

          $this->SetTextColor(0, 0, 0);


        }
        else {

          //------------------------- heartclicksize = 1 TOT AAN DE PUNT

          if ($leftMitre) {
            $this->setXY(10, ($startingYvalue + 13) + $newYValue);
            $this->Cell(0, 7, $leftMitre->angle . ' °', 0, 1, 'L', false);
          }
          if ($rightMitre) {
            $this->setXY(-45, ($startingYvalue + 13) + $newYValue);
            $this->Cell(0, 7, $rightMitre->angle . ' °', 0, 1, 'L', false);
          }

          if ($leftMitre) {
            $this->Image($mitredir . 'left/' . floor($leftMitre->angle) . '.png', 37, ($startingYvalue + 10) + $newYValue, 0, 0, 'png');
          }
          else {
            $this->Image($mitredir . 'left/90.png', 37, ($startingYvalue + 10) + $newYValue, 0, 0, 'png');
          }
          $this->Image($mitredir . 'middle_long.png', 58.25, ($startingYvalue + 10) + $newYValue, 0, 0, 'png');
          if ($rightMitre) {
            $this->Image($mitredir . 'right/' . floor($rightMitre->angle) . '.png', 124, ($startingYvalue + 10) + $newYValue, 0, 0, 'png');
          }
          else {
            $this->Image($mitredir . 'right/90.png', 124, ($startingYvalue + 10) + $newYValue, 0, 0, 'png');
          }


          $this->setXY(68, ($startingYvalue + 27) + $newYValue);
          $this->Cell(0, 7, 'Ingevoerde element maat: ' . $element->inputLength . ' mm', 0, 1, 'L', false);

          $this->setXY(68, ($startingYvalue + 32) + $newYValue);
          $this->Cell(0, 7, 'Element maat: ' . $element->getElementlengthMinSpacing($quotation, $stone) . ' mm', 0, 1, 'L', false);

          $this->setXY($startingXvalue, ($startingYvalue + 22) + $newYValue);
          //-- dit moet grijs worden.
          $this->SetTextColor(122, 122, 122);
          $this->Cell(0, 7, 'Speling ' . $element->getSpelingLeft($quotation, $stone) . ' mm', 0, 1, 'L', false);

          if ($element->flagWindow === 'single' && $element->getSpelingLeft($quotation, $stone) === 0) {
            $this->setXY($startingXvalue, ($startingYvalue + 27) + $newYValue);
            $this->Cell(0, 7, 'Enkel vlagkozijn', 0, 1, 'L', false);
          }

          $this->SetTextColor(0, 0, 0);

          $this->setXY(177, ($startingYvalue + 22) + $newYValue);
          //-- dit moet grijs worden.
          $this->SetTextColor(122, 122, 122);
          $this->Cell(0, 7, "Speling " . $element->getSpelingRight($quotation, $stone) . ' mm', 0, 1, 'L', false);

          if ($element->flagWindow === 'single' && $element->getSpelingRight($quotation, $stone) === 0) {
            $this->setXY(174, ($startingYvalue + 27) + $newYValue);
            $this->Cell(0, 7, 'Enkel vlagkozijn', 0, 1, 'L', false);
          }

          $this->SetTextColor(0, 0, 0);

        }

        $this->setXY(10, ($startingYvalue + 40) + $newYValue);
        $this->SetDrawColor(122, 122, 122);
        $this->Cell(0, 8, '', 'T', 1, 'L', false);
        $this->SetDrawColor(0, 0, 0);

        $counter++;

      }

    }

    private function addWindowsills() {
      if (!$this->stone || !($this->stone->isNatuursteen() && $this->stone->isVensterbank())) return;
      $elementIds = [];
      foreach ($this->elements as $element) {
        $elementIds[$element->elementId] = $element->elementId;
      }

      $order_element_windowsills = AppModel::mapObjectIds(OrderElementWindowsill::find_all_by(["element_id" => $elementIds]), "element_id");

      $startingYvalue = 45;
      $startingXvalue = 10;

      $counter = 0;
      foreach ($this->elements as $element) {

//        if($counter % 2 == 0) {
//          $this->AddPage();
//          $currentY = $startingYvalue;
//        }
//        else {
//          $currentY = $startingYvalue + 100;
//        }

        $currentY = $startingYvalue;
        $this->AddPage();

        $windowsill = $order_element_windowsills[$element->elementId];
        $windowsill_template = Windowsill::find_by_id($windowsill->windowsill_id);

        $this->setXY($startingXvalue, $currentY);
        $this->SetTextColor(197, 44, 27);
        $this->SetFont('Nunito', 'B');
        $this->Cell(0, 7, 'Element ' . $element->referenceName, 0, 1, 'L', false);
        $this->SetFont('Nunito', '');
        $this->SetTextColor(0, 0, 0);

        $this->SetFont('Nunito', 'B');
        $this->Cell(50, 5, "Aantal", '', 0, 'L');
        $this->SetFont('Nunito', '');
        $this->Cell(20, 5, $element->amount, 0, 1, 'R', false);

        $sizes = 0;
        for ($tel = 1; $tel <= 6; $tel++) {
          if ($windowsill->{'x' . $tel} == "") continue;
          $this->SetFont('Nunito', 'B');
          $this->Cell(50, 5, 'X' . $tel . ": " . $windowsill_template->{'x' . $tel}, '', 0, 'L');
          $this->SetFont('Nunito', '');
          if ($windowsill_template->getMetric($tel) === 'mm') {
            $this->Cell(20, 5, number_format($windowsill->{'x' . $tel},0) . ' ' . $windowsill_template->getMetric($tel), '', 1, 'R');
          } else {
            $this->Cell(20, 5, $windowsill->{'x' . $tel} . ' ' . $windowsill_template->getMetric($tel), '', 1, 'R');
          }
          $sizes++;
        }
        $sizes -= 2;

        if ($windowsill->remark_cust != "") {
          $this->SetFont('Nunito', 'B');
          $this->Cell(50, 5, "Uw opmerking:", '', 0, 'L');
          $this->SetFont('Nunito', '');
          $this->MultiCell(135, 5, $windowsill->remark_cust, 0, 'L');
          $sizes += $this->customNbLines(135, $windowsill->remark_cust);
        }

        $image_filepath = DIR_ROOT_HTTPDOCS . "images/thresholds/vensterbanken/" . $windowsill_template->imagefilename;
        if ($windowsill_template->imagefilename != "" && file_exists($image_filepath)) {
          $this->Image($image_filepath, $startingXvalue, $currentY + 25 + ($sizes * 5), 0, 0, 'png');
        }
        else {
          $this->Ln();
          $this->Cell(0, 7, 'Geen afbeelding beschikbaar', 0, 1, 'L');
        }


        //        $this->setXY(10, ($startingYvalue + 80));
//        $this->SetDrawColor(122, 122, 122);
//        $this->Cell(0, 8, '', 'T', 1, 'L', false);
//        $this->SetDrawColor(0, 0, 0);


        $counter++;
      }


    }

    private function writeElements($aWidth) {
      $quotation = $this->quotation;
      $quotations_extra = $this->quotation_extra;

      $showVlagkozijn = $this->stone->isRaamdorpel();
      $showEindsteenKolom = false;
      $vlagkozijnTitle = $showVlagkozijn ? 'Vlagkozijn' : '';
      $verstekTitle = ($this->stone->isSpekband() && $quotation->brandId == StoneBrands::BRAND_ID_STJORIS) ? "Hoek" : "Verstek";
      if (($this->stone->isSpekband() && $quotation->brandId == StoneBrands::BRAND_ID_STJORIS) || $this->stone->isMuurafdekker()) {
        $vlagkozijnTitle = "Eindsteen";
        $showEindsteenKolom = true;
      }
      //Headers
      $this->SetFont('Nunito', 'B', 10);
      $this->Cell($aWidth[0], 5, 'Kenmerk', 'B', 0, 'L', false);
      $this->Cell($aWidth[1], 5, 'Aantal', 'B', 0, 'L', false);
      if ($this->stone->isRaamdorpel()) {
        $this->Cell($aWidth[2], 5, 'Kozijn (mm)', 'B', 0, 'R', false);
      }
      else {
        $this->Cell($aWidth[2], 5, 'Muurmaat (mm)', 'B', 0, 'R', false);
      }
      $this->Cell($aWidth[3], 5, 'Element (mm)', 'B', 0, 'R', false);
      $this->Cell($aWidth[4], 5, $verstekTitle, 'B', 0, 'L', false);
      $this->Cell($aWidth[5], 5, $vlagkozijnTitle, 'B', 0, 'L', false);
      $this->Cell($aWidth[6], 5, "Totaal", 'B', 1, 'R', false);
      $this->SetFont('Nunito', '', 10);

      $elementCount = 0;

      foreach ($this->elements as $element) {
        if (true || $element->elementLengthTotal == 0) {
          //fallback
          $leftMitre = $rightMitre = false;
          if ($element->leftMitreId != "") $leftMitre = $this->mitres[$element->leftMitreId];
          if ($element->rightMitreId != "") $rightMitre = $this->mitres[$element->rightMitreId];

          $element->elementLengthTotal = $element->getTotalElementlength($quotation, $quotations_extra, $leftMitre, $rightMitre, $this->size, $this->stone);
          $element->save();

        }

        $miteLabel = '';
        if ($element->getMitre() == "both") {
          $miteLabel = 'Beide';
        }
        elseif ($element->getMitre() == "left") {
          $miteLabel = 'Links';
        }
        elseif ($element->getMitre() == "right") {
          $miteLabel = 'Rechts';
        }

        $sFlagwindow = '';
        if ($showVlagkozijn) {
          switch ($element->flagWindow) {
            case 'single':
              $sFlagwindow = 'Enkel';
              break;
            case 'double':
              $sFlagwindow = 'Dubbel';
              break;
          }
        }
        elseif ($showEindsteenKolom) {
          if ($element->leftEndstone == 1 && $element->rightEndstone == 1) {
            $sFlagwindow = 'Beide';
          }
          elseif ($element->leftEndstone == 1) {
            $sFlagwindow = 'Links';
          }
          elseif ($element->rightEndstone == 1) {
            $sFlagwindow = 'Rechts';
          }
          else {
            $sFlagwindow = 'Nee';
          }
        }

        //Total elements
        $elementCount += $element->amount;

        $this->Cell($aWidth[0], 5, $element->referenceName, 0, 0, 'L', false);
        $this->Cell($aWidth[1], 5, $element->amount, 0, 0, 'L', false);
        $this->Cell($aWidth[2], 5, $element->inputLength, 0, 0, 'R', false);
        $this->Cell($aWidth[3], 5, $element->elementLengthTotal, 0, 0, 'R', false);
        $this->Cell($aWidth[4], 5, $miteLabel, 0, 0, 'L', false);
        $this->Cell($aWidth[5], 5, $sFlagwindow, 0, 0, 'L', false);
        $this->Cell($aWidth[6], 5, "€ " . number_format($element->totalPrice, 2, ',', '.'), 0, 1, 'R', false);
      }
      return $elementCount;
    }

    private function writeElementsVensterbank($aWidth) {

      $this->SetFont('Nunito', 'B', 10);

      $this->Cell($aWidth[0], 5, 'Kenmerk', 'B', 0, 'L', false);
      $this->Cell($aWidth[1], 5, 'Aantal', 'B', 0, 'L', false);
      $this->Cell($aWidth[2], 5, 'Lengte (mm)', 'B', 0, 'R', false);
      $this->Cell($aWidth[3], 5, 'Breedte (mm)', 'B', 0, 'R', false);
      $this->Cell($aWidth[4], 5, 'Model', 'B', 0, 'L', false);
      $this->Cell($aWidth[5], 5, "", 'B', 0, 'L', false);
      $this->Cell($aWidth[6], 5, "Totaal", 'B', 1, 'R', false);
      $this->SetFont('Nunito', '', 10);


      $elementIds = [];
      foreach ($this->elements as $element) {
        $elementIds[$element->elementId] = $element->elementId;
      }

      $order_element_windowsills = AppModel::mapObjectIds(OrderElementWindowsill::find_all_by(["element_id" => $elementIds]), "element_id");

      $elementCount = 0;
      foreach ($this->elements as $element) {

        $windowsill = $order_element_windowsills[$element->elementId];
        $windowsill_template = Windowsill::find_by_id($windowsill->windowsill_id);
        $windowsillX1 = number_format(str_replace(',' , '.', $windowsill->x1), 1, '.', ',');
        $windowsillX2 = number_format(str_replace(',' , '.', $windowsill->x2), 1, '.', ',');

        //Total elements
        $elementCount += $element->amount;

        $this->Cell($aWidth[0], 5, $element->referenceName, 0, 0, 'L', false);
        $this->Cell($aWidth[1], 5, $element->amount, 0, 0, 'L', false);
        $this->Cell($aWidth[2], 5, number_format($windowsill->x1), 0, 0, 'R', false);
        $this->Cell($aWidth[3], 5, number_format($windowsill->x2), 0, 0, 'R', false);
        $this->Cell($aWidth[4] + $aWidth[5], 5, $windowsill_template->name, 0, 0, 'L', false);
        $this->Cell($aWidth[6], 5, "€ " . number_format($element->totalPrice, 2, ',', '.'), 0, 1, 'R', false);
      }
      return $elementCount;
    }

    private function writeElementsBalkje($aWidth) {

      $this->SetFont('Nunito', 'B', 10);

      $this->Cell($aWidth[0], 5, 'Kenmerk', 'B', 0, 'L', false);
      $this->Cell($aWidth[1], 5, 'Aantal', 'B', 0, 'L', false);
      $this->Cell($aWidth[2], 5, 'Lengte (mm)', 'B', 0, 'R', false);
      $this->Cell($aWidth[3], 5, 'Breedte (mm)', 'B', 0, 'R', false);
      $this->Cell($aWidth[4], 5, 'Hoogte (mm)', 'B', 0, 'R', false);
      $this->Cell($aWidth[5], 5, "Verstek", 'B', 0, 'L', false);
      $this->Cell($aWidth[6], 5, "Totaal", 'B', 1, 'R', false);
      $this->SetFont('Nunito', '', 10);


      $elementIds = [];
      foreach ($this->elements as $element) {
        $elementIds[$element->elementId] = $element->elementId;
      }

      $elementCount = 0;
      foreach ($this->elements as $element) {

        $order_element_sizes = OrderElementSize::getByElementId($element->elementId);

        $miteLabel = '';
        if ($element->getMitre() == "both") {
          $miteLabel = 'Beide';
        }
        elseif ($element->getMitre() == "left") {
          $miteLabel = 'Links';
        }
        elseif ($element->getMitre() == "right") {
          $miteLabel = 'Rechts';
        }

        $this->Cell($aWidth[0], 5, $element->referenceName, 0, 0, 'L', false);
        $this->Cell($aWidth[1], 5, $element->amount, 0, 0, 'L', false);
        $this->Cell($aWidth[2], 5, $element->inputLength, 0, 0, 'R', false);
        $this->Cell($aWidth[3], 5, $order_element_sizes["width"]->value, 0, 0, 'R', false);
        $this->Cell($aWidth[4], 5, $order_element_sizes["height"]->value, 0, 0, 'R', false);
        $this->Cell($aWidth[5], 5, $miteLabel, 0, 0, 'L', false);
        $this->Cell($aWidth[6], 5, "€ " . number_format($element->totalPrice, 2, ',', '.'), 0, 1, 'R', false);

        $elementCount += $element->amount;

      }
      return $elementCount;
    }


  }