<section class="title-bar">
  <h1>
    <?php echo Navigation::getItem(Navigation::getActivePageId())->getName() ?>
  </h1>
  <?php TemplateHelper::includePartial("_tabs.php","other") ?>
</section>

<div class="box">
  <a href="?action=damageedit" class="gsd-btn gsd-btn-primary">Nieuw beschadingstype</a>
</div>

<?php if(count($damages)==0): ?>
  <br/>
  Geen items gevonden.
<?php else: ?>
  <table class="default_table" style="width: auto">
    <tr class="dataTableHeadingRow">
      <td>Naam NL</td>
      <td>Naam PL</td>
      <td class="gsd-svg-icon-width-2">Acties</td>
    </tr>
    <?php foreach($damages as $damage): ?>
      <tr class="dataTableRow trhover">
        <td><?php echo $damage->name_nl ?></td>
        <td><?php echo $damage->name_pl ?></td>
        <td>
          <?php echo BtnHelper::getEdit(reconstructQuery(['action']) . 'action=damageedit&id=' . $damage->id) ?>
          <?php echo BtnHelper::getRemove(reconstructQuery(['action', 'id']) . 'action=damagedelete&id=' . $damage->id) ?>
        </td>
      </tr>
    <?php endforeach; ?>

  </table>
<?php endif; ?>
