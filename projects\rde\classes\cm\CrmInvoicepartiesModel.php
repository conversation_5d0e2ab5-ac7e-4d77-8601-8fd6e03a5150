<?php

  AppModel::loadBaseClass('BaseCrmInvoiceparties');

  class CrmInvoicepartiesModel extends BaseCrmInvoiceparties {


    /**
     * Create invoiceParty if non existend
     * @param CrmCompanies $company
     * @return false|CrmInvoiceparties
     */
    public static function create($company) {
      if ($company->invoicePartyId != "") return false;
      $ip = new CrmInvoiceparties();
      $ip->companyId = $company->companyId;
      $ip->attendant = "Crediteurenadministratie";
      if ($company->postAddressId != "") {
        $ip->addressId = $company->postAddressId;
      }
      else {
        $ip->addressId = $company->visitAddressId;
      }
      $ip->invoiceTerm = $company->paymentTerm;
      $ip->save();
      return $ip;
    }

  }