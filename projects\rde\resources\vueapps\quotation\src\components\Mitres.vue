<template>

  <div id="step3-popup-bg" v-if="showPopupBG"></div>

  <div class="wizard_inleiding">
    Vul in onderstaand lijst uw elementmaten in. U kunt ook tabs enters gebruiken bij de invoer.
  </div>

  <div class="alert alert-danger" v-if="errors.length>0">
    Er zijn foutmeldingen opgetreden, controleer uw invoer:
    <ul>
      <li v-for="error in errors" :key="error">{{error}}</li>
    </ul>
  </div>

  <div id="step3" class="wizard" v-cloak v-if="!loading">

    <div class="wizard_inleiding">
      <div v-if="showGemeten">
        U kunt uw verstek meten op 2 manieren.
        <ul>
          <li>Hartklikmaat: hart van de klik (opstaande rand die onder het kozijn gaat / korte zijde) </li>
          <li>Tot aan de punt: lange zijde tot aan de punt gemeten</li>
        </ul>
        Indien u deze gegevens niet bij de hand heeft, kunt u ervoor kiezen de offerte te berekenen op basis van 45 graden hoeken.
        Onderop deze pagina vindt u nog extra informatie.
      </div>
      <div v-if="!showGemeten">
        Deze elementen worden berekend a.d.h.v. de muurlengte. Er word extra lengte toevoegd i.v.m. verstek hoeken.
      </div>
    </div>

    <form method="post" :ref="setForm">

      <div class="form-row" v-if="showGemeten">
        <label class="col3 col-form-label">Hoe gemeten</label>
        <div class="col1">
          <div v-if="is_valid.heartClickSize" class="input-validation-icon">
            <img src="/projects/rde/templates/frontend/images/check_green.svg" alt="" width="20" />
          </div>
        </div>
        <div class="col7">
          <input type="button" :value="gemetenText()" name="gemeten" @click="openGemetenbox" class="form-input form-btn" />
          <input type="hidden" v-model="quotation.heartClickSize" name="heartClickSize" />
        </div>
        <div class="col1">
          <GsdPopper content="Op welke wijze heeft u de raamdorpel elementen gemeten? Hart klik maat of tot aan de punt?">
            <a class="question-mark-inline fa fa-info-circle"></a>
          </GsdPopper>
        </div>
      </div>

      <div class="scroller-x" v-if="is_valid['heartClickSize']">
        <div class="form-row elementrow" v-for="(element, key) in elements" :key="key">
          <div v-if="element.mitre!='none'">
            <div class="form-row elementrow-header">
              <div class="col2 col-form-label" style="font-weight: bold;">
                {{element.referenceName}}
              </div>
              <div class="col3 col-form-label" style="text-align: right;">
                {{ lengthTitle }}
              </div>
              <div class="col2">
                <input type="text" v-model="element.inputLength" @focus="$event.target.select()"  :name="'elements['+key+'][inputLength]'"  :ref="'inputLength_'+key" @change="validateInputLengthInput(element, key, true)" placeholder="Maat..."  class="inputnumber form-input wizard_inputmm"  :class="{inputerror: element.hasInputLengthError}" maxlength="5"/>
                <span>mm</span>
              </div>
              <div class="col3 col-form-label" style="text-align: right;">
                Element maat
              </div>
              <div class="col2">
                <input type="text" :value="calcElementtotallength(element)" name="elementotallength" class="inputnumber form-input wizard_inputmm" readonly style="font-weight: bold;"/>
                <span>mm</span>
              </div>
            </div>
            <div class="form-row elementrow-drawing">
              <div class="col2">
                <div class="select" v-if="element.leftMitreId!=null">
                  <select class="sizeId" v-model="element.leftMitreId" :name="'elements['+key+'][leftMitreId]'" >
                    <optgroup v-for="(group, name) in mitresSelect" :label="name" :key="name">
                      <option v-for="mitre in group" :value="mitre.mitreId" :key="mitre.mitreId">
                        {{ mitre.angle}}
                      </option>
                    </optgroup>
                  </select>
                </div>
                <div class="vlagkozijnlbl" v-else-if="element.flagWindow=='single'">
                  {{vlagkozijnTitle}}
                </div>
              </div>
              <div class="col1">
                <GsdPopper>
                  <a class="question-mark-inline fa fa-info-circle"></a>
                  <template #content>
                    {{spelingLeft(element)}} mm
                  </template>
                </GsdPopper>
              </div>
              <div class="col6 col12-xs mitre_div_parent">
                <div class="mitre_div">
                  <img :src="getLeftImage(element)"><img :src="getMiddleImage(element)" class="mitre_middle"><img :src="getRightImage(element)">
                </div>
              </div>
              <div class="col1">
                <GsdPopper>
                  <a class="question-mark-inline fa fa-info-circle"></a>
                  <template #content>
                    {{spelingRight(element)}} mm
                  </template>
                </GsdPopper>
              </div>
              <div class="col2">
                <div class="select" v-if="element.rightMitreId!=null">
                  <select class="sizeId" v-model="element.rightMitreId" :name="'elements['+key+'][rightMitreId]'" >
                    <optgroup v-for="(group, name) in mitresSelect" :label="name" :key="name">
                      <option v-for="mitre in group" :value="mitre.mitreId" :key="mitre.mitreId">
                        {{ mitre.angle}}
                      </option>
                    </optgroup>
                  </select>
                </div>
                <div class="vlagkozijnlbl" v-else-if="element.flagWindow=='single'">
                  {{vlagkozijnTitle}}
                </div>
              </div>

            </div>
          </div>
        </div>
      </div>

      <br/><br/>

      <button @click.prevent="clickPrev()"  type="button" name="prev" id="prev" class="btn" style="float: left;"><i class="fa fa-chevron-left"></i> Vorige stap</button>
      <button @click.prevent="clickNext()" type="button" name="next" id="next" class="btn" style="float: right;" v-bind:disabled="!validate()">Doorgaan <i class="fa fa-chevron-right"></i></button>

    </form>

    <div class="questionbox" id="mitrebox" v-if="gemetenboxShow">
      <div class="questionbox_title">Hoe heeft u gemeten?</div>
      <div class="questionbox_content">
        <p>Op welke wijze heeft u de raamdorpel elementen gemeten?</p>

        <div>
          <input type="radio" v-model="quotation.heartClickSize" value="0" id="gemeten_0" @change="selectGemeten()"> <label for="gemeten_0"><img src="/images/mitres/hart-klik-maat.png" alt="Hart klik maat" class="exampleimg"></label>
        </div>
        <div>
          <input type="radio" v-model="quotation.heartClickSize" value="1" id="gemeten_1" @change="selectGemeten()" style="margin-top: -37px;"> <label for="gemeten_1"><img src="/images/mitres/tot-aan-de-punt.png" alt="Verstekhoek aan linker elementeinde" class="exampleimg"></label>
        </div>
        <p></p>
        <p class="center">
          <input type="button" name="btnGemeten" value="Sluiten" @click="selectGemeten()" class="btn">
        </p>
      </div>
    </div>

  </div>

</template>

<script>

import MathHelper from './../helpers/MathHelper.js';
import DevelopmentHelper from './../helpers/DevelopmentHelper.js';
import GsdPopper from './../components/GsdPopper.vue';
import ErrorHelper from "../helpers/ErrorHelper";

export default {
  name: 'Mitres',
  components: {
    GsdPopper,
  },
  data() {
    return {
      loading: true,
      is_valid: {},
      is_active: {},
      input_errors: {},
      errors: [],
      form: null,

      quotation: {},
      quotation_extra: {},
      stone: {},
      elements: {},
      stone_size: {},
      mitresSelect : {},
      quotationExtraSpacing :  "",

      showGemeten: false,
      gemetenboxShow: false,
      mitreimageroot: '',
      showPopupBG : false,

    }
  },
  created() {
    this.fetchData();
  },
  computed: {
    lengthTitle() {
      return "Ingevoerde "+(this.stone.type==="raamdorpel"?'kozijnmaat':'muurmaat');
    },
    vlagkozijnTitle() {
      return (this.stone.type==="raamdorpel"?'vlagkozijn enkel':'eindsteen');
    }
  },
  methods: {
    fetchData() {
      fetch('?action=wizard&step=3&json=1', {
        headers: {'Content-type': 'application/json'},
      })
          .then(res => res.json())
          .then((response) => {

            if (ErrorHelper.handleError(this, response)) return;

            this.quotation = response.data.quotation;
            this.quotation_extra = response.data.quotation_extra;
            this.elements = response.data.elements;
            this.mitres = response.data.mitres;
            this.stone_size = response.data.stone_size;
            this.stone = response.data.stone;
            this.showGemeten = response.data.showGemeten;
            this.mitresSelect = response.data.mitresSelect;
            this.quotationExtraSpacing = response.data.quotationExtraSpacing;
            this.errors = response.data.errors;

            this.validate();
            this.setImageRoot();

            this.loading = false;

          })
          .catch((error) => {
            this.errors = [];
            this.errors.push("Server fout opgetreden. Probeer het later nog eens, of neem contact op (" + error + ")");
          });
    },
    postData(direction) {

      const formData = new FormData(this.form); // reference to form element
      formData.append(direction, 1);

      fetch('?action=wizard&step=3&json=1', {
        method: "POST",
        headers: {
          // 'Content-type': 'application/json',
          "Accept": "application/json",   // expected data sent back
        },
        body: formData
      })
          .then(res => res.json())
          .then((response) => {

            if (ErrorHelper.handleError(this, response)) return;

            this.errors = response.data.errors;

            if ("redirect" in response) {
              location.href = response.redirect;
            }

          })
          .catch((error) => {
            this.errors = [];
            this.errors.push("Server fout opgetreden. Probeer het later nog eens, of neem contact op (" + error + ")");
          });
    },
    clickPrev() {
      this.postData("prev");
    },
    clickNext() {
      this.postData("next");
    },
    gemetenText: function () {
      if(this.quotation.heartClickSize==1) {
        return 'Tot aan de punt'
      }
      else if(this.quotation.heartClickSize==0) {
        return 'Hart klik maat'
      }
      return 'Selecteer een meetmethode'
    },
    spelingLeft: function(element) {
      if(element.mitre=='right' && element.flagWindow=='single') {
        return 0;
      }
      if(element.mitre=='both' || element.mitre=='left') {
        return this.quotationExtraSpacing;
      }
      return this.quotation.shorter/2;
    },
    spelingRight: function(element) {
      if(element.mitre=='left' && element.flagWindow=='single') {
        return 0;
      }
      if(element.mitre=='both' || element.mitre=='right') {
        return this.quotationExtraSpacing;
      }
      return this.quotation.shorter/2;
    },
    //popups
    openGemetenbox: function() {
      this.gemetenboxShow = true;
      this.showPopupBG = true;
    },
    selectGemeten: function () {
      this.gemetenboxShow = false;
      this.setImageRoot();
      this.showPopupBG = false;
      this.validate();
    },
    setImageRoot: function () {
      this.mitreimageroot = "";
      if(DevelopmentHelper.isDevelopment()) {
        this.mitreimageroot += "http://www.raamdorpel.nl.rde.localhost";
      }
      this.mitreimageroot += "/images/mitresnew/";
      if (this.stone.type === "muurafdekker") {
        this.mitreimageroot += "muurafdekker/";
      } else if (this.stone.type === "spekband") {
        this.mitreimageroot += "spekband/";
      } else if (this.quotation.heartClickSize == 0) {
        this.mitreimageroot += "raamdorpel-hartklik/";
      } else {
        this.mitreimageroot += "raamdorpel-totaandepunt/";
      }
    },

    //validation
    validate: function () {
      var allvalid = false;

      if(this.quotation.heartClickSize==0 || this.quotation.heartClickSize==1) {
        allvalid = true;
        this.is_valid['heartClickSize'] = true;
      }
      else {
        this.is_valid['heartClickSize'] = false;
      }

      if(allvalid) {
        for (var i = 0; i < this.elements.length; i++) {
          if(!this.validateInputLengthInput(this.elements[i], i, false)) {
            allvalid = false;
          }
        }
      }

      return allvalid;

    },
    validateInputLengthInput: function (element, key, show) {
      var valid = true;
      if(!MathHelper.isNumeric(element.inputLength) || element.inputLength<0) {
        element.inputLength = 0;
      }
      else if(element.inputLength != parseInt(element.inputLength, 10)) {
        element.inputLength = Math.floor(element.inputLength);
      }

      if(element.inputLength<100) {
        valid = false;
        if(show) {
          this.$swal("Foutmelding", "Kozijnmaat in mm moet minimaal 100 mm zijn.", "error");
          element.hasInputLengthError = true;
        }
      }
      if(valid && show) {
        element.hasInputLengthError = false;
      }
      return valid;

    },

    //other
    getLeftImage: function (element) {
      if(element.leftMitreId!=null) {
        if(this.mitres[element.leftMitreId]) {
          return this.mitreimageroot + "left/" + Math.floor(this.mitres[element.leftMitreId].angle) + ".png";
        }
      }
      return this.mitreimageroot + "left/90.png";
    },
    getMiddleImage: function () {
      return this.mitreimageroot + "middle.png";
    },
    getRightImage: function (element) {
      if(element.rightMitreId!=null) {
        if(this.mitres[element.rightMitreId]) {
          return this.mitreimageroot + "right/" + Math.floor(this.mitres[element.rightMitreId].angle) + ".png";
        }
      }
      return this.mitreimageroot + "right/90.png";

    },
    calcElementtotallength: function (element) {
      if(this.stone.type==="muurafdekker") {
        return this.calculateElementLengthMuurafdekker(element);
      }
      return this.calculateElementLengthDefault(element);
    },
    calculateElementLengthDefault: function (element) {
      var total	= 0;
      if (element.mitre == 'both') { // 2 schuine kanten
        total = element.inputLength - (2 * this.quotationExtraSpacing);
      }
      else if(element.flagWindow == 'single') {
        total = element.inputLength - this.quotationExtraSpacing;
      }
      else {
        total = element.inputLength - this.quotationExtraSpacing - (0.5 * this.quotation.shorter);
      }
      return total;
    },
    calculateElementLengthMuurafdekker: function (element) {
      var total	= 0;
      var muurdikte = parseInt(this.quotation_extra.wall_thickness);
      var oversteek = Math.round(this.stone_size.length*10 - muurdikte) / 2;
      //var oversteek_calc = Math.round(this.stone_size.length*10 - oversteek);
      var oversteek_calc = Math.round(oversteek);
      if (element.mitre == 'both') { // 2 schuine kanten
        total = element.inputLength - (2 * this.quotationExtraSpacing);
      }
      else if(element.flagWindow == 'single') {
        total = element.inputLength - this.quotationExtraSpacing;
      }
      else {
        total = element.inputLength - this.quotationExtraSpacing - (0.5 * this.quotation.shorter);
      }

      if(element.flagWindow == 'single') {
        total += oversteek;
      }


      if(element.leftMitreId!=null) {
        let l_angle = this.mitres[element.leftMitreId].angle;
        total += Math.round(oversteek_calc / Math.tan(MathHelper.deg2rad(l_angle)));
      }
      if(element.rightMitreId!=null) {
        let r_angle = this.mitres[element.rightMitreId ].angle;
        total += Math.round(oversteek_calc / Math.tan(MathHelper.deg2rad(r_angle)));
      }

      return total;
    },
    setForm(el) {
      this.form = el;
    }

  },

}

</script>

<style>

#step3-popup-bg {
  overflow: auto;
  overflow-y: scroll;
  position: fixed;
  bottom: 0;
  right: 0;
  width: auto;
  height: auto;
  top: 0;
  left: 0;
  z-index: 900;
  background: #0e0e0ebd;
}

</style>

