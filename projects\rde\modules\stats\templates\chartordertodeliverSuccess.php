<h3>Te bezorgen meters per maand</h3>
<PERSON><PERSON><PERSON> vind u een overzicht van het aantal meters nog te bezorgen per maand in vergelijking met het voorgaande jaar.<br/>
Alle bestellingen die nog niet geleverd zijn, worden meegeteld in de meters. Het getal is inclusief de aangeven maand.<br/>
Dit word bepaald aan het hand van alle bestellingen die nog niet geleverd zijn t/m deze maand MIN alle bestelling die geleverd zijn t/m deze maand.
<Br/>
<div class="filter">
  Filter:
  <form>
    <select name="type" id="<?php echo $chartid ?>_type">
      <option value="">Selecteer type...</option>
      <?php foreach(Stones::TYPES as $mk=>$mname): ?>
        <option value="<?php echo $mk ?>" <?php writeIfSelectedVal($type,$mk); ?>><?php echo $mname ?></option>
      <?php endforeach; ?>
    </select>
    <select name="material" id="<?php echo $chartid ?>_material">
      <option  value="">Selecteer materiaal...</option>
      <?php foreach(Stones::MATERIALS as $mk=>$mname): ?>
        <option value="<?php echo $mk ?>" <?php writeIfSelectedVal($material,$mk); ?>><?php echo $mname ?></option>
      <?php endforeach; ?>
    </select>
    <select name="year" id="<?php echo $chartid ?>_year">
<!--      <option value="">Selecteer jaar...</option>-->
      <?php for($tel=date("Y");$tel>=2014;$tel--): ?>
        <option value="<?php echo $tel ?>" <?php writeIfSelectedVal($year,$tel); ?>><?php echo $tel ?></option>
      <?php endfor; ?>
    </select>
  </form>
</div>
<div class="rdechart" id="<?php echo $chartid ?>_chart"></div>
<table>
  <?php foreach($totals as $y=>$total):
    if($total==0) continue;
    ?>
    <tr>
      <td style="font-weight: bold;"><?php echo $y ?> gemiddelde meters per maand te leveren</td>
      <td style="text-align: right;"><?php echo number_format($total/12,0,"",".") ?> meter</td>
    </tr>
  <?php endforeach; ?>
</table>

<script>
  $(document).ready(function() {
    createBarchart("<?php echo $chartid ?>_chart", <?php echo json_encode($chartdata) ?>);

    $("#<?php echo $chartid ?>_material,#<?php echo $chartid ?>_year,#<?php echo $chartid ?>_type").change(function() {
      getChart("<?php echo $chartid ?>");
    })

  });
</script>