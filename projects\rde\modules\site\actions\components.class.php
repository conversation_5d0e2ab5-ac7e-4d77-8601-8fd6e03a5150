<?php

  use gsdfw\domain\google\service\GoogleService;

  class siteComponents extends gsdComponents {

    public function executeCookiebanner() {

    }

    public function executeRebus() {
      $errors = [];

      try {
        if (!isset($_POST['send'])) return;

        ValidationHelper::isSpamFreePost();

        if (empty($_POST['name'])) $errors[] = "Naam";

        if (!ValidationHelper::isEmail($_POST['email'])) $errors[] = "E-mailadres";

        if (empty($_POST['answer'])) $errors[] = "Antwoord";

        if (!$this->validateRecaptcha()) $errors[] = "Hela<PERSON>, u bent gedetecteerd als robot. Probeer het nogmaals.";

        if (empty($errors)) {
          $this->sendRebusEmail();
          ResponseHelper::redirectMessage("Bedankt voor uw deelname!", reconstructQuery());
        }
      } catch (GsdException) {
        $errors[] = "Er is een fout opgetreden bij het versturen van het formulier. Probeer het later nog eens.";
      } finally {
        $this->errors = $errors;
      }
    }

    /**
     * @throws GsdException
     */
    private function sendRebusEmail(): void {
      $subject = "Rebus antwoord vanuit " . $_SERVER['HTTP_HOST'];

      // Sanitize input
      $name = htmlspecialchars($_POST['name']);
      $email = htmlspecialchars($_POST['email']);
      $answer = htmlspecialchars($_POST['answer']);

      $message = <<<HTML
        <table>
            <tr><td>Naam:</td><td>$name</td></tr>
            <tr><td>E-mailadres:</td><td>$email</td></tr>
            <tr><td>Antwoord op rebus:</td><td>$answer</td></tr>
        </table>
        HTML;

      $gsd_mailer = GsdMailer::build(MAIL_FROM, $subject, $message);
      $gsd_mailer->setReplyTo($_POST['email']);
      $gsd_mailer->send();
    }

    private function validateRecaptcha(): bool {
      $recaptcha_code = $_POST['g-recaptcha-response'] ?? $_POST['recaptcha'] ?? false;
      $recaptcha_ignore = isset($_GET["recaptcha-ignore"]) || (isset($this->recaptcha_ignore) && $this->recaptcha_ignore);

      return GoogleService::validateRecaptcha($recaptcha_code, $recaptcha_ignore);
    }
  }