<?php TemplateHelper::includePartial('_tabs.php', 'prices'); ?>

<h3>Bewerk betaaltermijn opslag</h3>

<?php writeErrors($form->getErrors(), true); ?>

<form method="post">
  <table class="default_table" style="width: auto;">
    <tr class="dataTableHeadingRow">
      <td>Item</td>
      <td>Instelling</td>
    </tr>
    <tr class="dataTableRow trhover">
      <td><PERSON><PERSON><PERSON> vanaf</td>
      <td>
        01-01-<?php echo $_GET["yearfrom"] ?>
      </td>
    </tr>
  </table>
  <br/>

  <table class="default_table" style="width: auto;">
    <tr class="dataTableHeadingRow">
      <td style="width: 190px;">Naam</td>
      <?php foreach (Config::get("RDE_PAYMENTTERMS") as $term): ?>
        <td style="text-align: center; width: 40px"><?php echo $term ?></td>
      <?php endforeach; ?>
    </tr>
    <?php
      /** @var CustomerGroups $group */
      foreach($groups as $group): ?>
        <tr class="dataTableRow trhover">
          <td><?php echo $group->name ?></td>
          <?php foreach (Config::get("RDE_PAYMENTTERMS") as $term): ?>
            <td style="text-align: center">
              <?php if($form->hasElement("increase_".$group->groupId."_".$term)): ?>
                <?php $form->getElement("increase_".$group->groupId."_".$term)->render(); ?>
              <?php else: ?>
                x
              <?php endif; ?>
            </td>
          <?php endforeach; ?>
        </tr>
      <?php endforeach; ?>
  </table>
  <br/>
  <input type="submit" name="go" value="Opslaan" />
  <input type="submit" name="go_list" value="Opslaan en naar lijst" />

</form>
<script type="text/javascript">
  $(document).ready(function () {
    $(".price").focus(function() {
      $(this).select();
    });


    $("input[type=number]").change( function() {
      if($(this).val()!="") {
        var val = $(this).val();
        //console.log(val);
        val = decimalNL(val,2);
        $(this).val(val);
      }
    });

  });

</script>
<style>
  input[type=number] {
    width: 80px;
    text-align: right;
  }
</style>