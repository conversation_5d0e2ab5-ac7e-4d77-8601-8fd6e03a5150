<?php
class BaseSmallElements extends AppModel
{
  const DB_NAME = 'rde_b1mo';
  const TABLE_NAME = 'small_elements';
  const OM_CLASS_NAME = 'SmallElements';
  const columns = ['seId', 'elementLength', 'stoneAmount', 'fitStoneAmount', 'fitStoneLength'];
  const field_structure = [
    'seId'                        => ['type' => 'int', 'length' => '3', 'null' => false],
    'elementLength'               => ['type' => 'decimal', 'length' => '5,1', 'null' => false],
    'stoneAmount'                 => ['type' => 'smallint', 'length' => '3', 'null' => false],
    'fitStoneAmount'              => ['type' => 'smallint', 'length' => '3', 'null' => false],
    'fitStoneLength'              => ['type' => 'smallint', 'length' => '3', 'null' => false],
  ];

  protected static $primary_key = ['seId'];
  protected $auto_increment = 'seId';

  public $seId, $elementLength, $stoneAmount, $fitStoneAmount, $fitStoneLength;

  function __construct($obj_arr = [], &$error_codes = []) {
    parent::__construct();
    $this->setDefaults();
    if (!empty($obj_arr)) {
      $this->from_array($obj_arr);
      $this->valid_required_fields($error_codes);
    }
  }

  /**
  * Set default object values
  **/
  public function setDefaults() {
  }

  /**
   * @param array $conditions (required)
   * @param string $raw_sql (optional)
   * @return SmallElements[]
   **/
  public static function find_all_like($conditions, $raw_sql = '') {
    return parent::generic_find_all_like($conditions, static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param array $conditions (required)
   * @param string $raw_sql (optional)
   * @return SmallElements[]
   **/
  public static function find_all_by($conditions, $raw_sql = '') {
    return parent::generic_find_all_by($conditions, static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param string $raw_sql (optional)
   * @return SmallElements[]
   **/
  public static function find_all($raw_sql = '') {
    return parent::generic_find_all(static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param array $conditions (required)
   * @param string $raw_sql (optional)
   * @return SmallElements
   **/
  public static function find_by($conditions, $raw_sql = '') {
    return parent::generic_find_by($conditions, static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param string $id (required)
   * @param string $raw_sql (optional)
   * @return SmallElements
   **/
  public static function find_by_id($id, $raw_sql = '') {
    return parent::generic_find_by_id($id, static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param array $conditions (required)
   * @param string $raw_sql (optional)
   * @return int
   **/
  public static function count_all_by($conditions, $raw_sql = '') {
    return parent::generic_count_all_by($conditions, static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param array $conditions (required)
   * @param string $raw_sql (optional)
   * @return bool
   **/
  public static function delete_by($conditions, $raw_sql = '') {
    return parent::generic_delete_by($conditions, static::OM_CLASS_NAME, $raw_sql);
  }

}