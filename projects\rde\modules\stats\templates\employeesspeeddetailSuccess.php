<?php
  TemplateHelper::includePartial('_tabs.php',"stats");
?>
<h3>Details: <?php echo $employee->name ?></h3>

<div class="box">
  <form method="post">

    Productiedatum van
    <?php echo getDateSelector("employee_glue_from",$_SESSION["employee_glue_from"]) ?>
    tot
    <?php echo getDateSelector("employee_glue_to",$_SESSION["employee_glue_to"]) ?>
    <input type="submit" value="Zoeken" name="search"/>
    <?php echo showHelpButton("Bij het filteren op productiedatum word er alleen gekeken naar bestellingen die geproduceerd zijn in deze periode. Er word dus ook alleen vergeleken met collega bestellingen uit deze periode.") ?>
  </form>
</div>


<?php if(empty($speed)): ?>
  Geen geldige metingen gevonden.
<?php else: ?>
  <table class="default_table" style="width: auto; min-width: 600px;">
    <tr class="dataTableHeadingRow">
      <td>Item</td>
      <td>Instellingen</td>
    </tr>
    <tr class="dataTableRow trhover">
      <td>Snelheid:</td>
      <td ><?php echo $speed ?></td>
    </tr>
  </table>
<?php endif; ?>

<?php if(count($stones)!=0): ?>
  <br/>
  <table class="default_table" style="width: auto; min-width: 600px;">
    <tr class="dataTableHeadingRow">
      <td>Steen</td>
      <td style="text-align: right;">
        Aantal orders<Br/>
        <?php echo showHelpButton("Aantal orders van deze steen van deze medewerker","Aantal orders") ?>
      </td>
      <td style="text-align: right;">
        Aantal meters<Br/>
        <?php echo showHelpButton("Aantal meters van deze steen van deze medewerker. Ongeacht met of zonder eindsteen","Aantal meters") ?>
      </td>
      <td style="text-align: right;">Gemid. snelheid
        <br/>
        Zonder eindsteen<br/>
        m/u
        <?php echo showHelpButton("Gemidddelde verlijmsnelheid in meter per uur van deze medewerker van deze steen.","Gemidddelde snelheid deze medewerker") ?>

      </td>
      <td style="text-align: right;">
        Gemid. snelheid alle<br/>
        Zonder eindsteen<br/>
        m/u
        <?php echo showHelpButton("Gemidddelde verlijmsnelheid in meter per uur van alle medewerkers van deze steen.","Gemidddelde snelheid alle medewerkers") ?>
      </td>
      <td style="text-align: right;">
        Percentage<br/>
        Zonder eindsteen<br/>
        %
        <?php echo showHelpButton("Hoeveel procent is deze medewerker sneller of langzamer dan de gemiddelde verlijmsnelheid van deze steen.","Percentage") ?>
      </td>
      <td style="text-align: right;">Gemid. snelheid
        <br/>
        Met eindsteen<br/>
        m/u
        <?php echo showHelpButton("Gemidddelde verlijmsnelheid in meter per uur van deze medewerker van deze steen.","Gemidddelde snelheid deze medewerker") ?>

      </td>
      <td style="text-align: right;">
        Gemid. snelheid alle<br/>
        Met eindsteen<br/>
        m/u
        <?php echo showHelpButton("Gemidddelde verlijmsnelheid in meter per uur van alle medewerkers van deze steen.","Gemidddelde snelheid alle medewerkers") ?>
      </td>
      <td style="text-align: right;">
        Percentage<br/>
        Met eindsteen<br/>
        %
        <?php echo showHelpButton("Hoeveel procent is deze medewerker sneller of langzamer dan de gemiddelde verlijmsnelheid van deze steen.","Percentage") ?>
      </td>
    </tr>
    <?php foreach($stones as $stone): ?>
      <tr class="dataTableRow trhover">
        <td><?php echo $stone->name ?></td>
        <td style="text-align: right;"><?php echo $stone->total_orders ?></td>
        <td style="text-align: right;"><?php echo $stone->total_meters ?></td>
        <?php if($stone->cm_per_hour_noend==0): ?>
          <td colspan="3"></td>
        <?php else: ?>
          <td style="text-align: right;"><?php echo getLocalePrice($stone->cm_per_hour_noend/100) ?></td>
          <td style="text-align: right;"><?php echo getLocalePrice($stone->cm_per_hour_avg_noend/100) ?></td>
          <td style="text-align: right;"><?php echo floor($stone->procent_noend) ?> %</td>
        <?php endif ?>
        <?php if($stone->cm_per_hour_end==0): ?>
          <td colspan="3"></td>
        <?php else: ?>
          <td style="text-align: right;"><?php echo getLocalePrice($stone->cm_per_hour_end/100) ?></td>
          <td style="text-align: right;"><?php echo getLocalePrice($stone->cm_per_hour_avg_end/100) ?></td>
          <td style="text-align: right;"><?php echo floor($stone->procent_end) ?> %</td>
        <?php endif; ?>
      </tr>
    <?php endforeach; ?>
    <tr class="dataTableHeadingRow">
      <td>Totalen:</td>
      <td style="text-align: right"><?php echo $total_orders ?></td>
      <td style="text-align: right"><?php echo $total_meters ?></td>
      <td></td>
      <td></td>
      <td></td>
      <td></td>
      <td></td>
      <td></td>
    </tr>
  </table>
<?php endif; ?>
