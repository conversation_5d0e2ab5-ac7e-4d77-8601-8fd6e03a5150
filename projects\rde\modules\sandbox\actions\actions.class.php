<?php

  use Gsd\DataTable\DataTable;
  use Gsd\Form\Elements\Checkbox;
  use Gsd\Form\Elements\Select;
  use Gsd\Form\Elements\Option;
  use Gsd\Form\ModelForm;

  class sandboxRdeActions extends gsdActions {

    public function executeList() {

      if (isset($_GET["openUserId"])) {
        $sb = SandboxUsers::getById($_GET["openUserId"]);
        if ($sb) {
          if ($sb->hasCompany()) {
            ResponseHelper::redirect("?action=companyedit&id=" . $sb->userId);
          }
          ResponseHelper::redirect("?action=useredit&id=" . $sb->userId);
        }
        ResponseHelper::redirect(reconstructQueryAdd());
      }

      if (!isset($_SESSION['sb_search'])) $_SESSION['sb_search'] = '';
      if (!isset($_SESSION['sb_search_part'])) $_SESSION['sb_search_part'] = '';
      if (!isset($_SESSION['sb_type'])) $_SESSION['sb_type'] = 'link';
      if (!isset($_SESSION['o_rows_per_page'])) $_SESSION['o_rows_per_page'] = 30;
      if (isset($_POST['rows_per_page'])) $_SESSION['o_rows_per_page'] = $_POST['rows_per_page'];

      // Search via query parameter
      if (isset($_GET['search'])) {
        $_SESSION['sb_search'] = trim($_GET['search']);
        $_SESSION['sb_type'] = '';
        ResponseHelper::redirect(reconstructQuery(['search']));
      }

      if (isset($_POST['go'])) {
        $_SESSION['sb_search'] = trim($_POST['sb_search']);
        $_SESSION['sb_type'] = trim($_POST['sb_type']);
        ResponseHelper::redirect(reconstructQuery());
      }

      //pager properties
      $this->pager = new Pager();
      $this->pager->setWriteRowsPerPageOptions(true, $_SESSION['o_rows_per_page']);
      $this->pager->setWriteCount(true);
      $this->pager->setWritePrint(true);
      $this->pager->addOptionToRowsPerPage(1000);
      $this->pager->addOptionToRowsPerPage(2500);
      $this->pager->handle();
      //einde pager props

      $filt = "WHERE 1 ";
      if ($_SESSION['sb_type'] == "link") { //te koppelen
        $filt .= " AND private='false' AND (companyId = 0 OR personId = 0 OR companyId IS NULL OR personId IS NULL) ";
      }
      elseif ($_SESSION['sb_type'] == "part") { //door particulieren
        $filt .= " AND private='true' ";
        $filt .= " AND (companyId = 0 OR companyId IS NULL) ";
      }
      elseif ($_SESSION['sb_type'] == "comp") {  //door bedrijven
        $filt .= " AND private='false' ";
        $filt .= " AND companyId != 0 AND NOT companyId IS NULL ";
      }
      if ($_SESSION['sb_search'] != "") {
        $searchval = escapeForDB($_SESSION['sb_search']);
        $filt .= " AND (";
        $filt .= " companyName LIKE '%" . $searchval . "%' OR ";
        $filt .= " email LIKE '%" . $searchval . "%' OR ";
        $filt .= " street LIKE '%" . $searchval . "%' OR ";
        $filt .= " zipcode LIKE '%" . $searchval . "%' OR ";
        $filt .= " domestic LIKE '%" . $searchval . "%' OR ";
        $filt .= " CONCAT(firstName, ' ', lastName) LIKE '%" . $searchval . "%'";
        $filt .= ")";
      }

      $this->pager->count = SandboxUsers::count_all_by([], $filt);
      if (!$this->pager->count) $this->pager->count = 0;

      $filt .= $this->pager->getLimitQuery();

      $sbusers = SandboxUsers::find_all($filt);
      $this->sandboxusers = $sbusers;
    }

    /**
     * Search company
     */
    public function executeCompanysearch() {
      $sandboxuser = SandboxUsers::find_by(["userId" => $_GET["id"]]);
      $companies = CrmCompanies::getRelatedCompanies($sandboxuser->companyName, $sandboxuser->zipcode, $sandboxuser->domestic, $sandboxuser->street, $sandboxuser->nr);

      $this->companies = $companies;
      $this->sandboxuser = $sandboxuser;
    }

    /**
     * Create company
     */
    public function executeCompanycreate() {

      $sandboxuser = SandboxUsers::find_by(["userId" => $_GET["id"]]);

      $company = new CrmCompanies();
      $person = new CrmPersons();
      $visitAddress = new CrmAddresses();
      $invoiceParty = new CrmInvoiceparties();

      //vullen vanuit sandbox
      $company->name = $sandboxuser->companyName;
      $company->tradeRegNo = $sandboxuser->tradeRegNo;
      $company->paymentTerm = $sandboxuser->payterm;
      $company->phone = $sandboxuser->phone;
      $company->email = $sandboxuser->email;

      $person->gender = $sandboxuser->gender;
      $person->firstName = $sandboxuser->firstName;
      $person->lastName = $sandboxuser->lastName;
      //$person->phone = $sandboxuser->phone;
      $person->mobile = $sandboxuser->mobile;
      $person->email = $sandboxuser->email;
      $person->country = $sandboxuser->country;

      $visitAddress->type = CrmAddresses::TYPE_VISIT;
      $visitAddress->street = $sandboxuser->street;
      $visitAddress->nr = $sandboxuser->nr;
      $visitAddress->extension = $sandboxuser->extension;
      $visitAddress->zipcode = $sandboxuser->zipcode;
      $visitAddress->domestic = $sandboxuser->domestic;
      $visitAddress->country = $sandboxuser->country;

      $invoiceParty->attendant = "Crediteurenadministratie";
      $invoiceParty->email = $sandboxuser->invoice_email;
      $invoiceParty->emailConfirmed = $sandboxuser->invoice_email_confirmed;

      $form_sandboxuser = new ModelForm("sb");
      $form_sandboxuser->addClass("edit-form");
      $form_sandboxuser->buildElementsFromModel($sandboxuser);
      $select = new Select("Particuliere klant", "private", $sandboxuser->private);
      $select->addOption(new Option("true", "Ja"));
      $select->addOption(new Option("false", "nee"));
      $form_sandboxuser->addElement($select);
      $form_sandboxuser->setElementsLabel([
        "private" => "Particuliere klant",
      ], true);

      $form_company = $this->createCompanyForm($company);
      $form_visit = $this->createAddressForm($visitAddress);
      $form_person = $this->createPersonForm($person);
      $form_invoiceparty = $this->createInvoicepartyForm($invoiceParty);


      if (isset($_POST['go'])) {

        $form_company->setElementsAndObjectValue($_POST, $company);
        $form_visit->setElementsAndObjectValue($_POST, $visitAddress);
        $form_person->setElementsAndObjectValue($_POST, $person);
        $form_sandboxuser->setElementsAndObjectValue($_POST, $sandboxuser);
        $form_invoiceparty->setElementsAndObjectValue($_POST, $invoiceParty);

        if ($form_company->isValid() && $form_visit->isValid() && $form_invoiceparty->isValid()) {

          $company->save();

          $person->companyId = $company->companyId;
          $person->country = $visitAddress->country;
          $person->save();

          $visitAddress->companyId = $company->companyId;
          $visitAddress->save();

          $company->visitAddressId = $visitAddress->addressId;
          $company->postAddressId = $visitAddress->addressId;
//          if($company->customerGroupId!="") {
//            $cg = CustomerGroups::find_by(["groupId"=>$company->customerGroupId]);
//            $company->paymentTerm = $cg->paymentTerm;
//          }

          $invoiceParty->companyId = $company->companyId;
          if ($company->postAddressId != "") {
            $invoiceParty->addressId = $company->postAddressId;
          }
          else {
            $invoiceParty->addressId = $company->visitAddressId;
          }
          $invoiceParty->invoiceTerm = $company->paymentTerm;
          $invoiceParty->save();

          $company->invoicePartyId = $invoiceParty->invoicePartyId;
          $company->save();


          $sandboxuser->companyId = $company->companyId;
          $sandboxuser->personId = $person->personId;
          $sandboxuser->save();

          $sandboxuser->copyFromPerson($person); //copy person data to sandbox
          $sandboxuser->copyFromCompany($company); //copy company data to sandbox
          $sandboxuser->copyFromAddress($visitAddress); //copy address data to sandbox
          $sandboxuser->save();

          //ofertes van de userId, moet ook aan deze company
          foreach (Quotations::find_all_by(["userId" => $sandboxuser->userId]) as $quot) {
            $quot->companyId = $company->companyId;
            $quot->save();
          }

          MessageFlashCoordinator::addMessage("Bedrijf en persoon gekoppeld aan sandbox gebruiker.");
          ResponseHelper::redirect(PageMap::getUrl('M_SANDBOXUSERS'));

        }
      }

      $this->step = 1;
      $this->form_company = $form_company;
      $this->form_visit = $form_visit;
      $this->form_person = $form_person;
      $this->form_sandboxuser = $form_sandboxuser;
      $this->form_invoiceparty = $form_invoiceparty;
      $this->sandboxuser = $sandboxuser;

    }

    public function executeUseredit() {

      $sandboxuser = SandboxUsers::find_by(["userId" => $_GET["id"]]);

      $form_sandboxuser = new ModelForm("sb");
      $form_sandboxuser->addClass("edit-form");
      $form_sandboxuser->buildElementsFromModel($sandboxuser);

      $select = new Select("Particuliere klant", "private", $sandboxuser->private);
      $select->addOption(new Option("true", "Ja"));
      $select->addOption(new Option("false", "nee"));
      $form_sandboxuser->addElement($select);

      $select = new Select("Geslacht", "gender", $sandboxuser->gender);
      $select->addOption(new Option("", "Selecteer geslacht..."));
      $select->addOption(new Option("male", "Man"));
      $select->addOption(new Option("female", "Vrouw"));
      $form_sandboxuser->addElement($select);

      $select = new Select("Land", "country", $sandboxuser->country);
      $select->addOption(new Option("", "Selecteer land..."));
      $select->addOption(new Option("NL", "Nederland"));
      $select->addOption(new Option("BE", "België"));
      $select->addOption(new Option("DE", "Duitsland"));
      $form_sandboxuser->addElement($select);

      $form_sandboxuser->setElementsLabel([
        "gender"                  => "Geslacht",
        "firstName"               => "Voornaam",
        "lastName"                => "Achternaam",
        "phone"                   => "Telefoon",
        "mobile"                  => "Mobiel",
        "email"                   => "Email",
        "private"                 => "Particuliere klant",
        "companyName"             => "Bedrijfsnaam",
        "tradeRegNo"              => "KVK-nummer",
        "payterm"                 => "Betaaltermijn",
        "street"                  => "Straat",
        "nr"                      => "Nummer",
        "extension"               => "Extensie",
        "zipcode"                 => "Postcode",
        "domestic"                => "Plaats",
        "country"                 => "Land",
        "invoice_email"           => "Factuur e-mailadres",
        "invoice_email_confirmed" => "Factuur e-mailadres bevestigd",
        "blocked"                 => "Geblokkeerd",
        "blockedbyadmin"          => "Geblokkeerd door Bart",
        "supplier"                => "Referentie",
        "lastLogin"               => "Datum laatste login",
        "ip"                      => "IP-adres laatste login",
        "created"                 => "Datum aangemaakt",
        "lastUpdate"              => "Datum aangepast",
      ], true);
      $form_sandboxuser->setElementsDisabled([
        "ip",
        "created",
        "lastLogin",
        "lastUpdate",
        "supplier",
      ]);

      if (isset($_POST['go']) || isset($_POST['go_list'])) {

        $form_sandboxuser->setElementsAndObjectValue($_POST, $sandboxuser);

        if ($form_sandboxuser->isValid()) {

          $sandboxuser->save();

          MessageFlashCoordinator::addMessage("Sanbox user opgeslagen.");
          if (isset($_POST['go_list'])) {
            ResponseHelper::redirect(PageMap::getUrl('M_SANDBOXUSERS'));
          }
          ResponseHelper::redirect(reconstructQuery());


        }
      }

      $this->form_sandboxuser = $form_sandboxuser;
      $this->sandboxuser = $sandboxuser;

    }


    public function executeCompanyedit() {

      $sandboxuser = SandboxUsers::find_by(["userId" => $_GET["id"]]);
      if (!$sandboxuser) {
        ResponseHelper::redirectNotFound();
      }

      ValidationHelper::isSpamFreePost();

      $company = CrmCompanies::find_by(["companyId" => $sandboxuser->companyId]);
      $person = CrmPersons::find_by(["personId" => $sandboxuser->personId]);
      $visitAddress = CrmAddresses::find_by(["companyId" => $sandboxuser->companyId]);
      $invoiceParty = CrmInvoiceparties::find_by(["companyId" => $sandboxuser->companyId]);
//dumpe($sandboxuser); //@TODO personId is niet altijd ingevuld, geef melding
      $form_sandboxuser = new ModelForm("sb");
      $form_sandboxuser->addClass("edit-form");
      $form_sandboxuser->buildElementsFromModel($sandboxuser);
      $select = new Select("Particuliere klant", "private", $sandboxuser->private);
      $select->addOption(new Option("true", "Ja"));
      $select->addOption(new Option("false", "nee"));
      $form_sandboxuser->addElement($select);
      $form_sandboxuser->setElementsLabel([
        "private" => "Particuliere klant",
      ], true);

      $blocked = new Checkbox("Geblokkeerd", "blocked", $sandboxuser->blocked);
      $blocked->setCheckedValue("true")->setUncheckedValue("false");
      $form_sandboxuser->addElement($blocked);

      $blockedByAdmin = new Checkbox("Geblokkeerd door Bart", "blockedbyadmin", $sandboxuser->blockedbyadmin);
      $blockedByAdmin->setCheckedValue("true")->setUncheckedValue("false");
      $form_sandboxuser->addElement($blockedByAdmin);

      $form_company = $this->createCompanyForm($company);
      $form_visit = $this->createAddressForm($visitAddress);
      $form_person = $this->createPersonForm($person);
      $form_invoiceparty = $this->createInvoicepartyForm($invoiceParty);

      if (isset($_POST['go']) || isset($_POST['go_list'])) {

        $form_company->setElementsAndObjectValue($_POST, $company);
        $form_visit->setElementsAndObjectValue($_POST, $visitAddress);
        $form_person->setElementsAndObjectValue($_POST, $person);
        $form_sandboxuser->setElementsAndObjectValue($_POST, $sandboxuser);
        $form_invoiceparty->setElementsAndObjectValue($_POST, $invoiceParty);

        if ($form_company->isValid() && $form_visit->isValid() && $form_invoiceparty->isValid()) {

          $person->country = $visitAddress->country;
          $person->save();

          $company->save();
          $visitAddress->save();
          $sandboxuser->save();
          $invoiceParty->save();

          $sandboxuser->copyFromPerson($person); //copy person data to sandbox
          $sandboxuser->copyFromCompany($company); //copy company data to sandbox
          $sandboxuser->copyFromAddress($visitAddress); //copy address data to sandbox
          $sandboxuser->copyFromInvoiceparty($invoiceParty);
          $sandboxuser->save();

          MessageFlashCoordinator::addMessage("Bedrijf opgeslagen.");
          if (isset($_POST['go_list'])) {
            ResponseHelper::redirect(PageMap::getUrl('M_SANDBOXUSERS'));
          }
          ResponseHelper::redirect(reconstructQuery());


        }
      }

      $this->step = 1;
      $this->form_company = $form_company;
      $this->form_visit = $form_visit;
      $this->form_person = $form_person;
      $this->form_sandboxuser = $form_sandboxuser;
      $this->form_invoiceparty = $form_invoiceparty;
      $this->sandboxuser = $sandboxuser;

    }

    public function executeCompanyeditNEW() {

      $company = CrmCompanies::find_by(["companyId" => $_GET["id"]]);
      $visitAddress = CrmAddresses::find_by(["companyId" => $company->companyId]);
      $invoiceParty = CrmInvoiceparties::find_by(["companyId" => $company->companyId]);

      $double_address = 0;
      if ($company->postAddressId != $company->visitAddressId) {
        $double_address = 1;
      }

      $post_address = $visitAddress;
      if (CrmAddresses::find_by(['companyId' => $company->companyId, 'type' => 'post'])) {
        $post_address = CrmAddresses::find_by(['companyId' => $company->companyId, 'type' => 'post']);
      }

      $form_company = $this->createCompanyForm($company);
      $form_company_address = $this->createCompanyAddressForm($company);
      $form_visit = $this->createAddressForm($visitAddress, $double_address);
      $form_post = $this->createAddressForm($post_address, $double_address);

      if (isset($_POST['go']) || isset($_POST['go_list'])) {

        $form_company->setElementsAndObjectValue($_POST, $company);
        $form_company_address->setElementsAndObjectValue($_POST, $company);
        $form_visit->setElementsAndObjectValue($_POST, $visitAddress);

        if ($form_company->isValid() && $form_visit->isValid() && $form_company_address->isValid()) {

          $company->save();
          $visitAddress->save();
          $invoiceParty->save();

          MessageFlashCoordinator::addMessage("Bedrijf opgeslagen.");
          if (isset($_POST['go_list'])) {
            ResponseHelper::redirect(PageMap::getUrl('M_BEDRIJVENGIDS_LIST'));
          }
          ResponseHelper::redirect(reconstructQuery());


        }
      }

      $this->form_company = $form_company;
      $this->form_company_address = $form_company_address;
      $this->is_double_address = $double_address;
      $this->form_visit = $form_visit;
      $this->form_post = $form_post;

    }


    /**
     * Compare company
     */
    public function executeCompanycompare() {

      if (isset($_POST['cancel'])) {
        ResponseHelper::redirect(reconstructQuery(["action", "relatedid"]) . 'action=companysearch');
      }

      $company = CrmCompanies::find_by(["companyId" => $_GET["relatedid"]]);
      $visitAddress = CrmAddresses::find_by(["addressId" => $company->visitAddressId]);
      $invoiceParty = CrmInvoiceparties::find_by(["companyId" => $company->companyId]);
      if (!$invoiceParty) {
        MessageFlashCoordinator::addMessageAlert("Geen invoice_party bij dit bedrijf. Dat kan niet, bedrijfdata corrupt.");
        ResponseHelper::redirect(PageMap::getUrl("M_SANDBOXUSERS"));
      }
      $sandboxuser = SandboxUsers::find_by(["userId" => $_GET["id"]]);

      $company_source = new CrmCompanies();
      $visitAddress_source = new CrmAddresses();
      $invoiceParty_source = new CrmInvoiceparties();

      //vullen vanuit sandbox
      $company_source->name = $sandboxuser->companyName;
      $company_source->tradeRegNo = $sandboxuser->tradeRegNo;
      $company_source->paymentTerm = $sandboxuser->payterm;
      $company_source->phone = $sandboxuser->phone;
      $company_source->email = $sandboxuser->email;

      $visitAddress_source->type = "visit";
      $visitAddress_source->street = $sandboxuser->street;
      $visitAddress_source->nr = $sandboxuser->nr;
      $visitAddress_source->extension = $sandboxuser->extension;
      $visitAddress_source->zipcode = $sandboxuser->zipcode;
      $visitAddress_source->domestic = $sandboxuser->domestic;
      $visitAddress_source->country = $sandboxuser->country;

      $invoiceParty_source->attendant = "Crediteurenadministratie";
      $invoiceParty_source->email = $sandboxuser->invoice_email;
      $invoiceParty_source->emailConfirmed = $sandboxuser->invoice_email_confirmed;

      $form_company = $this->createCompanyForm($company);//target company
      $form_visit = $this->createAddressForm($visitAddress);//target address
      $form_invoiceparty = $this->createInvoicepartyForm($invoiceParty);

      $form_company_source = $this->createCompanyForm($company_source)->setName("source")->buildUniqueElementIds()->setElementsDisabled(true);
      //hide some elements
      $form_company_source->getElement("categoryId")->addClass("inputhidden");
      $form_company_source->getElement("customerGroupId")->addClass("inputhidden");
      $form_company_source->getElement("introDiscount")->addClass("inputhidden");
      $form_company_source->getElement("stJoris")->addClass("inputhidden");
      $form_company_source->getElement("terca")->addClass("inputhidden");
      $form_company_source->getElement("url")->addClass("inputhidden");

      $form_visit_source = $this->createAddressForm($visitAddress_source)
        ->setName("source")
        ->buildUniqueElementIds()
        ->setElementsDisabled(true);

      $form_invoiceparty_source = $this->createInvoicepartyForm($invoiceParty_source)
        ->setName("source")
        ->buildUniqueElementIds()
        ->setElementsDisabled(true);

      if (isset($_POST['go'])) {

        $form_company->setElementsAndObjectValue($_POST, $company);
        $form_visit->setElementsAndObjectValue($_POST, $visitAddress);
        $form_invoiceparty->setElementsAndObjectValue($_POST, $invoiceParty);

        if ($form_company->isValid() && $form_visit->isValid() && $form_invoiceparty->isValid()) {

          $visitAddress->save();
          $invoiceParty->save();

//          if($company->customerGroupId!="") {
//            $cg = CustomerGroups::find_by(["groupId"=>$company->customerGroupId]);
//            $company->paymentTerm = $cg->paymentTerm;
//          }

          $company->save();

          $sandboxuser->copyFromCompany($company); //copy corrected company data to sandbox
          $sandboxuser->copyFromAddress($visitAddress); //copy corrected address data to sandbox
          $sandboxuser->copyFromInvoiceparty($invoiceParty);
          $sandboxuser->save();

          MessageFlashCoordinator::addMessage("Bedrijf gegevens geupdate");
          ResponseHelper::redirect(reconstructQuery(["action", "relatedid"]) . 'action=personsearch&companyid=' . $company->companyId);

        }
      }

      $this->step = 1;
      $this->form_company = $form_company;
      $this->form_visit = $form_visit;
      $this->form_invoiceparty = $form_invoiceparty;

      $this->form_company_source = $form_company_source;
      $this->form_visit_source = $form_visit_source;
      $this->form_invoiceparty_source = $form_invoiceparty_source;

      $this->sandboxuser = $sandboxuser;

    }

    /**
     * Search person
     */
    public function executePersonsearch() {
      $sandboxuser = SandboxUsers::find_by(["userId" => $_GET["id"]]);
      $company = CrmCompanies::find_by(["companyId" => $_GET["companyid"]]);
      $persons = CrmPersons::find_all_by(["companyId" => $company->companyId]);

      $this->sandboxuser = $sandboxuser;
      $this->company = $company;
      $this->persons = $persons;
    }


    /**
     * Compare person
     */
    public function executePersoncompare() {

      if (isset($_POST['cancel'])) {
        ResponseHelper::redirect(reconstructQuery(["action", "relatedpersonid"]) . 'action=personsearch');
      }

      $sandboxuser = SandboxUsers::find_by(["userId" => $_GET["id"]]);
      $company = CrmCompanies::find_by(["companyId" => $_GET["companyid"]]);
      $person = CrmPersons::find_by(["personId" => $_GET["relatedpersonid"]]);

      $person_source = new CrmPersons();
      $person_source->gender = $sandboxuser->gender;
      $person_source->firstName = $sandboxuser->firstName;
      $person_source->lastName = $sandboxuser->lastName;
//      $person_source->phone = $sandboxuser->phone;
      $person_source->mobile = $sandboxuser->mobile;
      $person_source->email = $sandboxuser->email;
      $person_source->country = $sandboxuser->country;

      $form_person = $this->createPersonForm($person);//target company
      $form_person_source = $this->createPersonForm($person_source)->setName("source")->buildUniqueElementIds()->setElementsDisabled(true);

      if (isset($_POST['go'])) {

        $form_person->setElementsAndObjectValue($_POST, $person);

        if ($form_person->isValid()) {

          $person->save();


          //er word een bestaand persoon geupdate, bij een bestaand bedrijf.
          //een persoon kan meerdere sandboxusers hebben.
          //alles updaten behalve emailadres
          $sandboxusers_old = SandboxUsers::find_all_by(["personId" => $person->personId]);
          $visitAddress = CrmAddresses::find_by(["addressId" => $company->visitAddressId]);
          foreach ($sandboxusers_old as $sandboxuser_old) {
            //huidig person heeft al een sanbox user gekoppeld
            $sandboxuser_old->copyFromPerson($person, true); //copy corrected person data to sandbox EXCEPT EMAIL. User can log in with old email and password
            $sandboxuser_old->copyFromCompany($company); //copy corrected company data to sandbox
            $sandboxuser_old->copyFromAddress($visitAddress); //copy corrected address data to sandbox
            $sandboxuser_old->save();
          }

          //Huidige sandboxuser word gekoppeld.
          $sandboxuser->copyFromPerson($person); //copy corrected person data to sandbox
          //Sandbox koppelen aan bestaand bedrijf.
          $sandboxuser->companyId = $company->companyId;
          //Sandbox koppelen aan bestaande persoon.
          $sandboxuser->personId = $person->personId;
          $sandboxuser->save();

          //ofertes van de userId, moet ook aan deze company
          foreach (Quotations::find_all_by(["userId" => $sandboxuser->userId]) as $quot) {
            $quot->companyId = $company->companyId;
            $quot->save();
          }


          //            //quotations en invoices om linken
//            foreach(Quotations::find_all_by(["userId"=>$sandboxuser->userId]) as $quot) {
//              $quot->userId = $sandboxuser_old->userId;
//              $quot->save();
//            }
//            foreach(Invoices::find_all_by(["userId"=>$sandboxuser->userId]) as $invoice) {
//              $invoice->userId = $sandboxuser_old->userId;
//              $invoice->save();
//            }

          //sansboxuser en quotations etc. doorzetten naar deze persoon
          MessageFlashCoordinator::addMessage("Bedrijf en persoon succesvol geupdate, en koppelingen gemaakt naar bestaand bedrijf.");
          ResponseHelper::redirect(PageMap::getUrl('M_SANDBOXUSERS'));

        }
      }

      $this->step = 1;
      $this->form_person = $form_person;
      $this->form_person_source = $form_person_source;
      $this->sandboxuser = $sandboxuser;

    }

    public function executePersondelete() {
      $person = CrmPersons::find_by(["personId" => $_GET["id"]]);

      if (isset($_GET['id'])) {
        $person->flagForDeletion = 1;
        $person->save();
      }

      ResponseHelper::redirectMessage("Persoon verwijderd.", reconstructQuery());
    }

    public function executePersonedit() {
      $person = CrmPersons::find_by(['personId' => $_GET['id']]);

      $form_person = $this->createPersonForm($person);

      if (isset($_POST['go']) || isset($_POST['go_list'])) {
        $form_person->setElementsAndObjectValue($_POST, $person);

        if ($form_person->isValid()) {
          $person->save();

          MessageFlashCoordinator::addMessage("Persoon opgeslagen.");
          if (isset($_POST['go_list'])) {
            ResponseHelper::redirect(PageMap::getUrl('M_BEDRIJVENGIDS_LIST'));
          }
          ResponseHelper::redirect(reconstructQuery());
        }
      }

      $this->form_person = $form_person;
    }

    /**
     * @todo moet personedit zijn
     * Compare person
     */
    public function executePersoncreate() {

      if (isset($_POST['cancel'])) {
        ResponseHelper::redirect(reconstructQuery(["action", "relatedpersonid"]) . 'action=personsearch');
      }

      $sandboxuser = SandboxUsers::find_by(["userId" => $_GET["id"]]);
      $company = CrmCompanies::find_by(["companyId" => $_GET["companyid"]]);
      $address = CrmAddresses::find_by(['addressId' => $company->visitAddressId]);
      $persons = CrmPersons::find_all_by(['companyId' => $company->companyId]);

      $person = new CrmPersons();
      if ($sandboxuser) {
        $person->gender = $sandboxuser->gender;
        $person->firstName = $sandboxuser->firstName;
        $person->lastName = $sandboxuser->lastName;
        $person->phone = $sandboxuser->phone;
        $person->mobile = $sandboxuser->mobile;
        $person->email = $sandboxuser->email;
        $person->country = $sandboxuser->country;
      }

      $form_person = $this->createPersonForm($person);//target company

      if (isset($_POST['go'])) {

        $form_person->setElementsAndObjectValue($_POST, $person);

        if ($form_person->isValid()) {

          $person->companyId = $company->companyId;
          $person->save();

          $sandboxuser->copyFromPerson($person); //copy corrected person data to sandbox
          //er word een nieuw person aangemaakt, bij bestaand bedrijf. Sandbox koppelen aan bedrijf.
          $sandboxuser->companyId = $company->companyId;
          //er word een nieuw person aangemaakt, dus sandbox blijft bestaan en gekoppeld aan dit person
          $sandboxuser->personId = $person->personId;
          $sandboxuser->save();

          //ofertes van de userId, moet ook aan deze company
          foreach (Quotations::find_all_by(["userId" => $sandboxuser->userId]) as $quot) {
            $quot->companyId = $company->companyId;
            $quot->save();
          }

          MessageFlashCoordinator::addMessage("Bedrijf en persoon succesvol geupdate, en koppelingen gemaakt naar bestaand bedrijf.");
          ResponseHelper::redirect(PageMap::getUrl('M_SANDBOXUSERS'));

        }
      }

      $this->step = 1;
      $this->form_person = $form_person;
      $this->sandboxuser = $sandboxuser;
      $this->company = $company;
      $this->address = $address;
      $this->persons = $persons;

    }


    /**
     * @param $company
     * @return ModelForm
     */
    private function createCompanyForm($company) {

      $form_company = new ModelForm("company");
      $form_company->addClass("edit-form");
      $form_company->buildElementsFromModel($company);

      $select = new Select("Categorie", "categoryId", $company->categoryId);
      $select->addOption(new Option("", "Selecteer categorie..."));
      foreach (CrmCategories::getCategories() as $cat) {
        $select->addOption(new Option($cat->categoryId, $cat->name));
      }
      $form_company->addElement($select);

      $select = new Select("Klantgroep", "customerGroupId", $company->customerGroupId);
      $select->addOption(new Option("", "Selecteer klantgroep..."));
      foreach (CustomerGroups::find_all("ORDER BY name") as $cgroup) {
        $select->addOption(new Option($cgroup->groupId, $cgroup->name));
      }
      $form_company->addElement($select);

      $form_company->setElementsLabel([
        "companyId"           => "Company id",
        "name"                => "Bedrijfsnaam",
        "phone"               => "Telefoonnummer",
        "email"               => "E-mailadres",
        "tradeRegNo"          => "KVK-nummer",
        "establishmentNumber" => "Vestigingsnummer",
        "receiveDM"           => "Ontvangt nieuwsbrief",
        "blocked"             => "Geblokkeerd",
        "url"                 => "Website",
        "categoryId"          => "Categorie",
        "customerGroupId"     => "Klantgroep",
        "paymentTerm"         => "Betaaltermijn",
        "introDiscount"       => "Introductie korting",
        "stJoris"             => "Merk St. Joris",
        "terca"               => "Merk Wienerberger",
        "notes"               => "Notities",
      ], true);
      $form_company->setElementsRequired([
        "name",
      ])->getElement("tradeRegNo")->setTextAfterElement('<a href="https://www.kvk.nl/zoeken/handelsregister/?kvknummer=' . $company->tradeRegNo . '" style="padding: 5px;" class="searchkvk" title="Zoek op kvk.nl...">' . IconHelper::getOpenPage() . '</a>');
      $form_company->getElement("companyId")->setReadonly(true);

      return $form_company;

    }

    /**
     * @param CrmAddresses $visitAddress
     * @param $double_address
     * @return ModelForm
     */
    private function createAddressForm($visitAddress, $double_address = false) {

      $form_visit = new ModelForm("visit");
      $form_visit->addClass("edit-form");
      $form_visit->buildElementsFromModel($visitAddress);

      $select = new Select("Land", "country", $visitAddress->country);
      $select->addOption(new Option("", "Selecteer land..."));
      $select->addOption(new Option("NL", "Nederland"));
      $select->addOption(new Option("BE", "België"));
      $select->addOption(new Option("DE", "Duitsland"));
      $form_visit->addElement($select);

      $form_visit->setElementsLabel([
        "street"    => "Straat",
        "nr"        => "Nummer",
        "extension" => "Extensie",
        "zipcode"   => "Postcode",
        "domestic"  => "Plaats",
        "country"   => "Land",
      ], true);
      $form_visit->setElementsRequired([
        "street",
      ]);

      if ($visitAddress->type == CrmAddresses::TYPE_POST) {
        if ($double_address) {
          $form_visit->setElementsReadonly([
            "street"    => "Straat",
            "nr"        => "Nummer",
            "extension" => "Extensie",
            "zipcode"   => "Postcode",
            "domestic"  => "Plaats",
            "country"   => "Land",
          ]);
        }
      }

      return $form_visit;
    }

    private function createAddressesForm($address) {

      $form_visit = new ModelForm("adres");
      $form_visit->addClass("edit-form");
      $form_visit->buildElementsFromModel($address);

      $select = new Select("Land", "country", $address->country);
      $select->addOptionHelper("", "Selecteer land...");
      $select->addOption(new Option("NL", "Nederland"));
      $select->addOption(new Option("BE", "België"));
      $select->addOption(new Option("DE", "Duitsland"));
      $form_visit->addElement($select);

      $form_visit->setElementsLabel([
        "street"    => "Straat",
        "nr"        => "Nummer",
        "extension" => "Extensie",
        "zipcode"   => "Postcode",
        "domestic"  => "Plaats",
        "country"   => "Land",
        "mapExtra"  => "Nuttige punten",
      ], true);
      $form_visit->setElementsRequired([
        "street",
      ]);

      return $form_visit;
    }

    /**
     * @param $company
     * @return ModelForm
     */
    private function createCompanyAddressForm($company) {
      $form_company = new ModelForm("company_address");
      $form_company->addClass("edit-form");
      $form_company->buildElementsFromModel($company);

      $form_company->setElementsLabel([
        "noDelivery"          => "Geen levering op dit adres",
        "phone"               => "Telefoonnummer",
        "fax"                 => "Fax",
        "email"               => "E-mail",
        "establishmentNumber" => "Vestigingsnummer",
        "receiveDMA"          => "Ontvangt nieuwsbrief",
        "blocked"             => "Geblokkeerd",
        "url"                 => "Website",
      ], true);
      $form_company->setElementsRequired([
        "name",
      ])->getElement("email")->setTextAfterElement(BtnHelper::getEmail($company->email));

      return $form_company;

    }

    /**
     * @param CrmPersons $person
     * @return ModelForm
     */
    private function createPersonForm($person) {
      $form_person = new ModelForm("person");
      $form_person->addClass("edit-form");
      $form_person->buildElementsFromModel($person);

      $select = new Select("Geslacht", "gender", $person->gender);
      $select->addOption(new Option("", "Selecteer geslacht..."));
      $select->addOption(new Option("male", "Man"));
      $select->addOption(new Option("female", "Vrouw"));
      $form_person->addElement($select);

      $select_country = new Select("Land", "country", $person->country);
      $select_country->addOption(new Option("", "Selecteer land..."));
      $select_country->addOption(new Option("NL", "Nederland"));
      $select_country->addOption(new Option("BE", "België"));
      $select_country->addOption(new Option("DE", "Duitsland"));
      $form_person->addElement($select_country);

      $form_person->setElementsLabel([
        "gender"            => "Geslacht",
        "firstName"         => "Voornaam",
        "lastName"          => "Achternaam",
        "department"        => "Afdeling",
        "jobtitle"          => "Functie",
        "phone"             => "Telefoon",
        "mobile"            => "Mobiel",
        "email"             => "Email",
        "fax"               => "Fax",
        "flagForDirectMail" => "Direct E-mail pers.",
        "country"           => "Land",
        "notes"             => "Notities",
        "flagForExecutor"   => "Uitvoerder",
        "flagForDeletion"   => "Verwijderen",
      ], true);
      $form_person->setElementsRequired([
        "lastName",
      ]);

      return $form_person;
    }

    /**
     * @param CrmInvoiceparties $invoiceparty
     * @return ModelForm
     */
    private function createInvoicepartyForm($invoiceparty) {
      $form_invoiceparty = new ModelForm("invoiceparty");
      $form_invoiceparty->addClass("edit-form");
      $form_invoiceparty->buildElementsFromModel($invoiceparty);
      $form_invoiceparty->setElementsLabel([
        "email"             => "Factuur e-mailadres",
        "emailReminder"     => "Aanmaningen e-mailadres",
        "emailConfirmed"    => "Factuur e-mailadres bevestigd",
        "vatRegNo"          => "BTW-nummer",
        "invoicePartyNotes" => "Notities",
      ], true);
      $form_invoiceparty->setElementsRequired([
        "attendant",
        "email",
      ]);

      return $form_invoiceparty;
    }

    private function createInvoiceparty_companyForm($company) {
      $form_invoiceparty_company = new ModelForm("invoiceparty_company");
      $form_invoiceparty_company->addClass("edit-form");
      $form_invoiceparty_company->buildElementsFromModel($company);
      $form_invoiceparty_company->setElementsLabel([
        "payInAdvance"   => "Vooruit betalen",
        "executorTicket" => "Uitvoerders bon",
      ], true);

      return $form_invoiceparty_company;
    }

    private function createInvoiceparty_addressForm($address) {
      $form_invoiceparty_address = new ModelForm("invoiceparty_company");
      $form_invoiceparty_address->addClass("edit-form");
      $form_invoiceparty_address->buildElementsFromModel($address);
      $form_invoiceparty_address->setElementsLabel([
        "street"    => "Straat",
        "nr"        => "Huisnummer",
        "extension" => "Toevoeging",
        "zipcode"   => "Postcode",
        "domestic"  => "Stad",
        "country"   => "Land",
      ], true);
      $form_invoiceparty_address->setElementsReadonly([
        "street",
        "nr",
        "extension",
        "zipcode",
        "domestic",
        "country",
      ]);

      return $form_invoiceparty_address;
    }


    public function executeExport() {

      if (isset($_POST["export"])) {

        ini_set('memory_limit', '1200M');
        ini_set('max_execution_time', 300); // 300 sec = 5 min

        $header = [
          "companyId",
          "companyName",
          "kvk",
          "directMailCampagnes",
          "directMailAlgemeen",
          "categoryName",
          "bezoekStraat",
          "bezoekNr",
          "bezoekExtensie",
          "bezoekPostcode",
          "bezoekPlaats",
          "bezoekLand",
          "postStraat",
          "postNr",
          "postExtensie",
          "postPostcode",
          "postPlaats",
          "postLand",
          "companyPhone",
          "companyEmail",
          "klantgroep",
          "betalingstermijn",
          "notities_klant",
          "projectInfo_overige",
          "notities_algemeen",
          "verwijderdBedrijf",
          "introductiekorting",
          "personId",
          "firstName",
          "lastName",
          "jobtitle",
          "phone",
          "mobile",
          "email",
          "flagForDeletion",
          "flagForDirectMail",
        ];

        $filename = 'Bedrijvengids_' . date("dmYHis") . '.csv';
        $filepath = DIR_TEMP . $filename;

        $outputBuffer = fopen($filepath, 'w'); //write to browser
        fputs($outputBuffer, chr(0xEF) . chr(0xBB) . chr(0xBF)); //utf8 correctie
        fputcsv($outputBuffer, $header, ';', escape: '\\');

        $companies = AppModel::mapObjectIds(CrmCompanies::find_all("ORDER BY companyId DESC"), "companyId");
        $categories = AppModel::mapObjectIds(CrmCategories::getCategories(), "categoryId");
        $customerGroups = AppModel::mapObjectIds(CustomerGroups::find_all(), "groupId");

        $tel = 0;
        foreach (array_chunk($companies, 500, true) as $chunk) {
          $this->exportCompanyBatch($chunk, $outputBuffer, $categories, $customerGroups);
          $tel++;
//          if($tel > 10) break;
        }
        fclose($outputBuffer);

        FileHelper::getFileAndOuput($filepath, $filename, true); //te groot voor deze functie
//        ResponseHelper::redirect("/temp/".$filename);
        ResponseHelper::exit();

      }
    }

    /**
     * @param CrmCompanies[] $companies
     * @param $outputBuffer
     * @param $categories
     * @param $customerGroups
     */
    private function exportCompanyBatch($companies, $outputBuffer, $categories, $customerGroups) {
      $addressIds = [];
      $cids = [];
      foreach ($companies as $company) {
        $addressIds[$company->visitAddressId] = $company->visitAddressId;
        $addressIds[$company->postAddressId] = $company->postAddressId;
        $cids[] = $company->companyId;
      }
      $addresses = AppModel::mapObjectIds(CrmAddresses::find_all_by(["addressId" => $addressIds]), "addressId");

      foreach (CrmPersons::find_all_by(["companyId" => $cids]) as $person) {
        if (!isset($companies[$person->companyId]->persons)) {
          $companies[$person->companyId]->persons = [];
        }
        $companies[$person->companyId]->persons[] = $person;
      }

      foreach ($companies as $company) {
        if (!isset($company->persons)) continue;
        $visit = $addresses[$company->visitAddressId] ?? false;
        $post = $addresses[$company->postAddressId] ?? false;
        /** @var CrmPersons $person */
        foreach ($company->persons as $person) {
          $row = [];
          $row[] = $company->companyId;
          $row[] = $company->name;
          $row[] = $company->tradeRegNo;
          $row[] = $company->receiveDMA;
          $row[] = $company->receiveDMA;

          $row[] = isset($categories[$company->categoryId]) ? $categories[$company->categoryId]->name : '';

          if ($visit) {
            $row[] = $visit->street;
            $row[] = $visit->nr;
            $row[] = $visit->extension;
            $row[] = $visit->zipcode;
            $row[] = $visit->domestic;
            $row[] = $visit->country;
          }
          else {
            $row = array_merge($row, ["", "", "", "", "", ""]);
          }

          if ($post) {
            $row[] = $post->street;
            $row[] = $post->nr;
            $row[] = $post->extension;
            $row[] = $post->zipcode;
            $row[] = $post->domestic;
            $row[] = $post->country;
          }
          else {
            $row = array_merge($row, ["", "", "", "", "", ""]);
          }

          $row[] = $company->phone;
          $row[] = $company->email;

          $row[] = isset($customerGroups[$company->customerGroupId]) ? $customerGroups[$company->customerGroupId]->name : '';
          $row[] = $company->paymentTerm;
          $row[] = $company->customerNotes;
          $row[] = $company->projectInfo;
          $row[] = $company->notes;
          $row[] = $company->flagForDeletion;
          $row[] = $company->introDiscount;

          $row[] = $person->personId;
          $row[] = $person->firstName;
          $row[] = $person->lastName;
          $row[] = $person->jobtitle;
          $row[] = $person->phone;
          $row[] = $person->mobile;
          $row[] = $person->email;
          $row[] = $person->flagForDeletion;
          $row[] = $person->flagForDirectMail;

          fputcsv($outputBuffer, $row, ';', escape: '\\');
        }

      }

      $companies = null;

    }

    public function executeUserslist() {
      $this->executeUserslistFilters();
    }

    public function executeUserslistFilters() {

      $dataTable = new DataTable('userslist');
      $dataTable->setRequestUrl(reconstructQueryAdd() . "action=userslistajax");
      $dataTable->addColumnHelper("name", "Bedrijfsnaam");
      $dataTable->addColumnHelper("person", "Persoon");
      $dataTable->addColumnHelper("categoryId", "Categorie");
      $dataTable->addColumnHelper("email", "E-mail");
      $dataTable->addColumnHelper("actions", "Acties");

      $dataTable->getColumn("actions")->setSortable(false);

      $dataTable->setDefaultSort("name", "desc");
      $dataTable->addSearchInput();

      $category_select = new Select('Categorie', 'category');
      $category_select->addOptionHelper('', __("Filter op categorie..."));
      foreach (CrmCategories::getCategories() as $code => $category) {
        $category_select->addOptionHelper($category->categoryId, $category->name);
      }
      $dataTable->getForm()->addElement($category_select);

      $is_deleted_check = new Checkbox('Toon verwijderde bedrijven', 'deleted_companies');
      $dataTable->getForm()->addElement($is_deleted_check);

      $dataTable->addSearchReset();

      $dataTable->handleRequest($_POST);
      $this->dataTable = $dataTable;
    }

    public function executeUserslistajax() {
      $this->executeUserslistFilters();

      $filter_query = "";
      $filter_query .= "LEFT JOIN " . CrmCompanies::getTablename() . " ON crm_persons.companyId = crm_companies.companyId ";
      $filter_query .= "WHERE 1 ";

      if ($this->dataTable->hasFormElementValue("search")) {
        $searchstr = DbHelper::escape($this->dataTable->getFormElementValue("search"));
        $filter_query .= "AND ( ";
        $filter_query .= "crm_companies.name LIKE '%" . $searchstr . "%' ";
        $filter_query .= "OR crm_persons.firstName LIKE '%" . $searchstr . "%' ";
        $filter_query .= "OR crm_persons.lastName LIKE '%" . $searchstr . "%' ";
        // @todo: $filtquery .= " OR CONCAT(user.firstname, ' ', user.insertion, ' ', user.lastname) LIKE '%" . $searchval . "%'";

        $filter_query .= ") ";
      }

      if ($this->dataTable->hasFormElementValue("category")) {
        $filter_query .= "AND crm_companies.categoryId = " . DbHelper::escape($this->dataTable->getFormElementValue("category")) . " ";
      }

      if ($this->dataTable->hasFormElementValue("deleted_companies")) {
        $filter_query .= "AND crm_companies.flagForDeletion = 1 ";
      }

      /** TOTALS */
      $total_count = CrmPersons::count_all_by([]);
      $total_count_filtered = CrmPersons::count_all_by([], $filter_query);

      /** GET DATA */
      $query = "SELECT * FROM " . CrmPersons::getTablename() . " ";
      $query .= $filter_query;
      $query .= $this->dataTable->getSortQuery();
      $query .= $this->dataTable->getPager()->getLimitQuery();

      $result = DBConn::db_link()->query($query);

      $table_data = [];
      while ($row = $result->fetch_array()) {
        $column_count = 0;

        $person = (new CrmPersons())->hydrateNext($row, $column_count);
        $company = (new CrmCompanies())->hydrateNext($row, $column_count);
        $category = CrmCategories::find_by(['categoryId' => $company->categoryId]);

        $actions_html = "";
        $actions_html .= " " . BtnHelper::getEdit(PageMap::getUrl("M_BEDRIJVENGIDS_EDIT") . '?id=' . $person->personId);

        $person_fullname = $person->firstName . ' ' . $person->lastName;

        $table_data[] = [
          'DT_RowId'   => $person->personId,
          'name'       => '<a href="' . PageMap::getUrl("M_BEDRIJVENGIDS_PERSONS") . '?companyid=' . $company->companyId . '" >' . $company->name . '</a>',
          'person'     => '<a href="' . PageMap::getUrl("M_BEDRIJVENGIDS_PERSONS") . '?action=personedit&companyid=' . $company->companyId . '&id=' . $person->personId . '" >' . $person_fullname . '</a>',
          'categoryId' => $category->name,
          'email'      => $person->email,
          'actions'    => $actions_html,
        ];
      }

      /** RETURN DATA AS JSON */
      ResponseHelper::exitAsJson([
        'data'            => $table_data,
        'recordsTotal'    => $total_count,
        'recordsFiltered' => $total_count_filtered,
        'draw'            => (int)$_POST['draw'],
      ]);
    }

    public function executeUserOverview() {
      $company = CrmCompanies::find_by(['companyId' => $_GET['companyid']]);
      if (!$company) {
        ResponseHelper::redirectNotFound("Bedrijf niet gevonden.");
      }
      $sandboxuser = SandboxUsers::find_by(['companyId' => $company->companyId]);
      $persons = CrmPersons::find_all_by(['companyId' => $company->companyId]);
      $address = CrmAddresses::find_by(['addressId' => $company->visitAddressId]);

      //@todo: change to get
      if (isset($_POST['create_person'])) {
        $this->executePersoncreate();
      }

      $this->persons = $persons;
      $this->sandboxuser = $sandboxuser;
      $this->company = $company;
      $this->address = $address;
    }

    public function executeFinancialedit() {
      $company = CrmCompanies::find_by(['companyId' => $_GET['id']]);
      if (!$company) {
        ResponseHelper::redirectNotFound("Bedrijf niet gevonden.");
      }
      $sandboxuser = SandboxUsers::find_by(['companyId' => $company->companyId]);
      $persons = CrmPersons::find_all_by(['companyId' => $company->companyId]);
      $visit_address = CrmAddresses::find_by(['addressId' => $company->visitAddressId]);
      $post_address = CrmAddresses::find_by(['addressId' => $company->postAddressId]);
      $invoiceparty = CrmInvoiceparties::find_by(['companyId' => $company->companyId]);
      $quotations = Quotations::find_all_by(['companyId' => $company->companyId]);

      if (count($quotations) > 0) {
        $invoiceDayCounter = 0;
        $invoiceCounter = 0;
        foreach ($quotations as $key => $quotation) {
          $invoice = Invoices::find_by(['invoiceId' => $quotation->invoiceId]);
          if (!$invoice) continue;
          $date1 = new DateTime($invoice->dateInvoice);
          $date2 = new DateTime($invoice->paid);
          $interval = $date1->diff($date2);
          $invoiceDayCounter = $invoiceDayCounter + $interval->format('%a');
          $invoiceCounter++;
        }
        $sAverageAmountInvoice = $invoiceDayCounter / $invoiceCounter;
        $sAverageAmountInvoice = round($sAverageAmountInvoice, 2) . ' dagen';
      }
      else {
        $sAverageAmountInvoice = 'Er zijn nog geen facturen aanwezig';
      }

      $form_invoiceparty = $this->createInvoicepartyForm($invoiceparty);
      $form_invoiceparty_company = $this->createInvoiceparty_companyForm($company);
      $form_invoiceparty_address = $this->createInvoiceparty_addressForm($visit_address);

      $this->form_invoiceparty = $form_invoiceparty;
      $this->form_invoiceparty_company = $form_invoiceparty_company;
      $this->form_invoiceparty_address = $form_invoiceparty_address;
      $this->persons = $persons;
      $this->sandboxuser = $sandboxuser;
      $this->company = $company;
      $this->address = $visit_address;
      $this->quotation_days = $sAverageAmountInvoice;
    }

    public function executeAddressoverview() {
      $company = CrmCompanies::find_by(['companyId' => $_GET['id']]);
      $sandboxuser = SandboxUsers::find_by(['companyId' => $company->companyId]);
      $persons = CrmPersons::find_all_by(['companyId' => $company->companyId]);
      $address = CrmAddresses::find_by(['addressId' => $company->visitAddressId]);
      $addresses = CrmAddresses::find_all_by(['companyId' => $company->companyId]);

      $this->persons = $persons;
      $this->sandboxuser = $sandboxuser;
      $this->company = $company;
      $this->address = $address;
      $this->addresses = $addresses;
    }

    public function executeAddressedit() {
      $address = CrmAddresses::find_by(['addressId' => $_GET['id']]);

      if (!$address) {
        $address = new CrmAddresses();
      }

      $form_address = $this->createAddressesForm($address);

      if (isset($_POST['go']) || isset($_POST['go_list'])) {
        $form_address->setElementsAndObjectValue($_POST, $address);

        if ($form_address->isValid()) {
          $address->save();

          MessageFlashCoordinator::addMessage("Persoon opgeslagen.");
          if (isset($_POST['go_list'])) {
            ResponseHelper::redirect(PageMap::getUrl('M_BEDRIJVENGIDS_LIST'));
          }
          ResponseHelper::redirect(reconstructQuery());
        }
      }

      $this->address = $address;
      $this->form_address = $form_address;
      Context::addJavascript("https://maps.googleapis.com/maps/api/js?v=3.exp&libraries=places&key=" . LocationHelper::getGoogleMapsKey());
    }

    public function executeAddressdelete() {
      $address = CrmAddresses::find_by(["addressId" => $_GET["id"]]);

      if (isset($_GET['id'])) {
        $address->flagForDeletion = 1;
        $address->save();
      }

      MessageFlashCoordinator::addMessage("Locatie verwijderd.");
      ResponseHelper::redirect(reconstructQuery());

    }

    public function executeFindlatlng() {
      $countries = Organisation::getCountries();

      $zipcode = '';
      $street = '';
      $nr = '';
      $city = '';
      $countrysub = '';

      if (isset($_GET['street']))
        $street = $_GET['street'];
      if (isset($_GET['nr']))
        $nr = $_GET['nr'];
      if (isset($_GET['zipcode']))
        $zipcode = $_GET['zipcode'];
      if (isset($_GET['city']))
        $city = $_GET['city'];
      if (isset($_GET['country']) && $_GET['country'] != '')
        $countrysub = $_GET['country'];

      $country = "Nederland";
      if ($countrysub != "") {
        if (isset($countries[$countrysub])) {
          $country = $countries[$countrysub];
        }
      }
      if ($country == 'Engeland') {
        $country = 'Verenigd Koninkrijk';
      }

      $latlng = LocationHelper::retrieveLatLngGoogle($zipcode, $street . ' ' . $nr, $city, $country);
      ResponseHelper::exitAsJson($latlng);

    }

  }

