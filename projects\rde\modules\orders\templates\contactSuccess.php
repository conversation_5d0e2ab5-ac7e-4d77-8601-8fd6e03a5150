<?php TemplateHelper::includePartial('_tabs.php', 'orders', compact(["quotation", "quotation_extra", "company", "sandboxuser"])); ?>
<div class="contact-container">
  <?php if($comp): ?>
    <div style="margin-right: 100px; width: 500px">
      <h2>
        Bedrijfsgegevens
        <a target="_blank" href="<?php echo PageMap::getUrl("M_BEDRIJVENGIDS_EDIT") . '?companyid=' . $comp->companyId ?>">
          <?php echo IconHelper::getOpenPage() ?>
        </a>
      </h2>
      <table class="default_table">
        <tr class="dataTableHeadingRow">
          <td>Eigenschap</td>
          <td>Waarde</td>
        </tr>
        <tr class="dataTableRow">
          <td>Bedrijfsnaam</td>
          <td><?php echo $comp->name ?></td>
        </tr>
        <tr class="dataTableRow">
          <td>Adres</td>
          <td><?php echo $comp_address->street . ' ' . $comp_address->nr ?></td>
        </tr>
        <tr class="dataTableRow">
          <td>Postcode</td>
          <td><?php echo $comp_address->zipcode . ' ' . $comp_address->domestic ?></td>
        </tr>
        <tr class="dataTableRow">
          <td>Land</td>
          <td><?php echo $comp_address->country ?></td>
        </tr>
        <tr class="dataTableRow">
          <td>Telefoonnummer</td>
          <td><?php echo $comp->phone ?></td>
        </tr>
        <tr class="dataTableRow">
          <td>Email algemeen</td>
          <td  class="email">
            <?php echo $comp->email ?>
            <?php echo BtnHelper::getEmail($comp->email) ?>
          </td>
        </tr>
        <tr class="dataTableRow">
          <td>Website</td>
          <td><?php echo $comp->url ?></td>
        </tr>
        <tr class="dataTableRow">
          <td>Notities</td>
          <td><?php echo $comp->notes ?></td>
        </tr>
      </table>
    </div>
  <?php endif; ?>
  <div style="width: 500px">
    <h2>
      Sandboxusergegevens
      <a target="_blank" href="<?php echo PageMap::getUrl("M_SANDBOXUSERS") . '?action=useredit&id=' . $sand_user->userId ?>">
        <?php echo IconHelper::getOpenPage() ?>
      </a>
    </h2>
    <table class="default_table">
      <tr class="dataTableHeadingRow">
        <td>Eigenschap</td>
        <td>Waarde</td>
      </tr>
      <tr class="dataTableRow">
        <td>Voornaam</td>
        <td><?php echo $sand_user->firstName ?></td>
      </tr>
      <tr class="dataTableRow">
        <td>Achternaam</td>
        <td><?php echo $sand_user->lastName ?></td>
      </tr>
      <tr class="dataTableRow">
        <td>Afdeling</td>
        <td><?php if($sand_person) echo $sand_person->jobtitle ?></td>
      </tr>
      <tr class="dataTableRow">
        <td>Functie</td>
        <td><?php if($comp_address) echo $comp_address->country ?></td>
      </tr>
      <tr class="dataTableRow">
        <td>Telefoonnummer</td>
        <td><?php echo $sand_user->phone ?></td>
      </tr>
      <tr class="dataTableRow">
        <td>Mobiel</td>
        <td><?php echo $sand_user->mobile ?></td>
      </tr>
      <tr class="dataTableRow">
        <td>Email</td>
        <td class="email">
          <?php echo $sand_user->email ?>
          <?php echo BtnHelper::getEmail($sand_user->email) ?>
        </td>
      </tr>
      <tr class="dataTableRow">
        <td>Notities</td>
        <td><?php if($sand_person) echo $sand_person->notes ?></td>
      </tr>
    </table>
  </div>
</div>

<style>
  .contact-container {
    display: flex;
    flex-direction: row;
  }
  .email {
    display: inline-flex;
    align-items: center;
    gap: .5em;
  }
</style>
