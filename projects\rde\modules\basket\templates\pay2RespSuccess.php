<section id="basket">
  <div>
    <h1><?php echo $wizard_name ?> - verzendkosten</h1>

    <?php include("_wizardheader.php"); ?>

    <?php writeErrors($errors); ?>

    Selecteer uw betaalmethode.<br/>
    <br/>


    <div id="step2" class="wizard basket-step-2">
      <form method="post">

        <?php foreach ($paymethods as $paymethod) : ?>
          <div class="form-row">
            <div class="col1">
              <input type="radio" name="paymethod" id="paymethod_<?php echo $paymethod->getKey(); ?>" value="<?php echo $paymethod->getKey(); ?>" <?php echo $quotation->paymentMethod==$paymethod->getKey() ? "checked" : ""; ?> />
            </div>
            <div class="col2">
              <?php if($paymethod->getLogoUrl()): ?>
                <label  for="paymethod_<?php echo $paymethod->getKey(); ?>" class="payment_logo">
                  <img src="<?php echo $paymethod->getLogoUrl() ?>" />
                </label>
              <?php endif; ?>
            </div>
            <div class="col7">
              <label for="paymethod_<?php echo $paymethod->getKey(); ?>"><?php echo $paymethod->getTitle(); ?></label>
            </div>
          </div>
        <?php endforeach; ?>

        <br/><br/>
        <button type="submit" name="prev" id="prev" class="btn" style="float: left;" formnovalidate><i class="fa fa-chevron-left"></i> <?php echo __("Vorige stap"); ?></button>
        <button type="submit" name="next" id="next" class="btn" style="float: right;"><?php echo __("Doorgaan"); ?> <i class="fa fa-chevron-right"></i></button>

      </form>

    </div>


  </div>
</section>
