<div class="contenttxt">
  <h1>Berichten</h1>
</div>
<?php if(count($imessages)==0): ?>
  Er zijn geen berichten gevonden.
<?php else: ?>

  <table class="download" id="offertetable">
    <tbody>
    <tr>
      <th style="min-width: 110px;">Onderwerp</th>
      <th style="min-width: 92px;">Bericht</th>
      <th style="width: 120px;"><PERSON><PERSON> meer</th>
    </tr>
    <?php
      /** @var Imessage $imessage */
      foreach($imessages as $imessage): ?>
        <tr class="trhover <?php if(!isset($imessages_read[$imessage->id])) echo 'imessage_unread' ?>">
          <td>
            <a href="?action=show&id=<?php echo $imessage->id ?>">
              <?php echo $imessage->subject ?>
            </a>
          </td>
          <td><?php echo $imessage->getMessageTeaser() ?>...</td>
          <td>
            <a href="?action=show&id=<?php echo $imessage->id ?>" title="Toon extra informatie"><i class="fa fa-chevron-circle-right"></i> Lees meer</a>
          </td>
        </tr>
      <?php endforeach; ?>
    </tbody>
  </table>

<?php endif; ?>
