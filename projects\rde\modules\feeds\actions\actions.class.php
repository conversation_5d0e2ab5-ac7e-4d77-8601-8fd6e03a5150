<?php

  class feedsRdeActions extends feedsActions {

    public function executeSitemap() {
      $this->ignoreproducts = false;
      $this->ignore_pageids = [277, 350, 211, 290, "M_BRANDS", "M_PRODUCTS", "M_SHOP_CATEGORY"];
      parent::executeSitemap();
    }

    /**
     * Outputs a Google shopping feed XML
     * see: https://support.google.com/merchants/answer/7052112
     * /nl/googleshopping
     */
    public function executeGoogleshopping() {

      $domain = $_SESSION['site']->site_host->getDomain(true);

      $writer = new XMLWriter();

      // Output directly to the user
      $writer->openURI('php://output');
      $writer->startDocument('1.0', "UTF-8");
      $writer->setIndent(4);

      $writer->startElement('rss');
      $writer->writeAttribute('version', '2.0');
      $writer->writeAttribute('xmlns:g', 'http://base.google.com/ns/1.0');

      $writer->startElement('channel');
      $writer->writeElement('title', 'Raamdorpel.nl');
      $writer->writeElement('link', $domain);
      $writer->writeElement('description', 'Kant en klare raamdorpelelementen van keramiek, natuursteen en beton');

      $products = Product::getAllProducts("WHERE product.void=0 AND online_custshop=1");
      $brands = Brand::getBrandsAr();

      $categories = AppModel::mapObjectIds(Category::find_all("WHERE void=0 AND online_custshop=1 ORDER BY parent_id, sort"));
      $categories_idd = AppModel::mapObjectIds($categories);

      $counter = 0;
      foreach ($products as $product) {

        if (!isset($categories[$product->category_id])) continue; //cat is offline

        $cat = $categories[$product->category_id];
        $catstr = $cat->getName();
        $parentinvalid = false;
        if (isset($categories_idd[$cat->parent_id])) {
          $parent = $categories_idd[$cat->parent_id];
          $catstr = $parent->getName() . ' > ' . $catstr;
          while ($parent->parent_id != null) {
            if (!isset($categories_idd[$parent->parent_id])) { //schijnbaar is de parent void of niet online.
              $parentinvalid = true;
              break;
            }
            $parent = $categories_idd[$parent->parent_id];
            $catstr = $parent->getName() . ' > ' . $catstr;
          }
        }
        if ($parentinvalid) {
          continue;
        }

        $writer->startElement('item');
        $writer->writeElement('title', $product->getName());
        $writer->writeElement('link', $domain . $product->getUrl());
        $desc = trim(strip_tags_allowed($product->content->description));
        if ($desc == "") {
          $desc = $product->getName();
        }
        $writer->writeElement('description', $desc);
        $writer->writeElement('g:id', "RDE-" . $product->id);
        $writer->writeElement('g:condition', 'new');
        $writer->writeElement('g:price', StringHelper::asMoney($product->getPriceByUser(), false) . ' EUR');
        $writer->writeElement('g:availability', 'in stock');
        $imageurl = $domain . '/projects/rde/templates/frontend/images/noimage.jpg';
        if ($product->getPhoto()) {
          $imageurl = $domain . $product->getPhoto();
        }
        $writer->writeElement('g:image_link', $imageurl);
//        $writer->writeElement('g:gtin', ''); // EAN code?
        if ($product->brand_id != "") {
          $writer->writeElement('g:brand', $brands[$product->brand_id]->getName());
        }
        $writer->writeElement('g:mpn', "RDE" . $product->id);
        $writer->writeElement('g:product_type', $catstr);
        $writer->endElement();

        $counter++;
//        if($counter++ == 9) break; // debugging
      }

      $writer->endElement();
      $writer->endElement();
      $writer->endDocument();

      header("Content-type: text/xml");
      $writer->flush();

      $this->template = null;
    }


  }