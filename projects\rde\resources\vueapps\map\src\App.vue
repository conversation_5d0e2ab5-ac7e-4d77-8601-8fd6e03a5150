<template>
  <v-app>
    <v-main id="main-container">
      <Filter
        :quotationsByComp="quotationsByComp"
        :statuses="statuses"
        :selectedCompany="selectedCompany"
        :companyQuotations="companyQuotations"
        @selectedQuotations="updateQuotations"
        class="filter-column"
      />
      <Map
        :companies="companyIds"
        :quotations-by-comp="quotationsByComp"
        :statuses="statuses"
        @selectedCompanyChange="updateSelectedCompany"
        class="map-column"
      />
      <Route class="route-column" />
    </v-main>
  </v-app>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import axios from 'axios'
import Map from './components/Map.vue'
import Filter from './components/Filter.vue'
import Route from './components/Route.vue'

const quotationsByComp = ref([])
const errors = ref([])
const companyIds = ref([])
const selectedCompany = ref(null)
const companyQuotations = ref([])
const statuses = ref([])
const selectedStatus = ref([])

const getQuotations = async () => {
  try {
    const { data } = await axios.get('?action=getquotationsandcompanies')
    companyIds.value = Object.keys(data)
    quotationsByComp.value = data
  } catch (error) {
    errors.value = [`Server fout opgetreden. Probeer het later nog eens, of neem contact op (${error})`]
  }
}

const updateSelectedCompany = async (newSelectedCompany) => {
  selectedCompany.value = newSelectedCompany
  try {
    const { data } = await axios.get('?action=getquotationsbycompany', {
      params: { companyId: selectedCompany.value }
    })
    companyQuotations.value = data
  } catch (error) {
    errors.value = [`Server fout opgetreden. Probeer het later nog eens, of neem contact op (${error})`]
  }
}

const updateQuotations = async (newSelectedStatus) => {
  selectedStatus.value = newSelectedStatus
  companyIds.value = []

  try {
    const { data } = await axios.get('?action=getquotationsandcompanies', {
      params: { status: selectedStatus.value }
    })
    companyIds.value = Object.keys(data)
    quotationsByComp.value = data
  } catch (error) {
    errors.value = [`Server fout opgetreden. Probeer het later nog eens, of neem contact op (${error})`]
  }
}

const getStatuses = async () => {
  try {
    const { data } = await axios.get('?action=getstatuses')
    statuses.value = data
  } catch (error) {
    errors.value = [`Server fout opgetreden. Probeer het later nog eens, of neem contact op (${error})`]
  }
}

onMounted(() => {
  getQuotations()
  getStatuses()
})
</script>

<style>
#main-container {
  display: flex;
  width: 100%;
  height: 100vh;
}

.filter-column {
  width: 300px;
  height: 100%;
  overflow-y: auto;
  flex-shrink: 0;
}

.map-column {
  flex-grow: 1;
  height: 100%;
  position: relative;
}

.route-column {
  width: 250px;
  height: 100%;
  overflow-y: auto;
  flex-shrink: 0;
}
</style>