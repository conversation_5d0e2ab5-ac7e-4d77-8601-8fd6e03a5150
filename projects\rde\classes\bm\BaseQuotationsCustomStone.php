<?php
class BaseQuotationsCustomStone extends AppModel
{
  const DB_NAME = 'rde_b1mo';
  const TABLE_NAME = 'quotations_custom_stone';
  const OM_CLASS_NAME = 'QuotationsCustomStone';
  const columns = ['id', 'quotationId', 'depth', 'thickness', 'height', 'width_click', 'height_click'];
  const field_structure = [
    'id'                          => ['type' => 'int', 'length' => '11', 'null' => false],
    'quotationId'                 => ['type' => 'int', 'length' => '11', 'null' => true],
    'depth'                       => ['type' => 'smallint', 'length' => '4', 'null' => false],
    'thickness'                   => ['type' => 'smallint', 'length' => '4', 'null' => false],
    'height'                      => ['type' => 'smallint', 'length' => '4', 'null' => false],
    'width_click'                 => ['type' => 'smallint', 'length' => '4', 'null' => false],
    'height_click'                => ['type' => 'smallint', 'length' => '4', 'null' => false],
  ];

  protected static $primary_key = ['id'];
  protected $auto_increment = 'id';

  public $id, $quotationId, $depth, $thickness, $height, $width_click, $height_click;

  function __construct($obj_arr = [], &$error_codes = []) {
    parent::__construct();
    $this->setDefaults();
    if (!empty($obj_arr)) {
      $this->from_array($obj_arr);
      $this->valid_required_fields($error_codes);
    }
  }

  /**
  * Set default object values
  **/
  public function setDefaults() {
    $this->width_click = 20;
    $this->height_click = 20;
  }

  /**
   * @param array $conditions (required)
   * @param string $raw_sql (optional)
   * @return QuotationsCustomStone[]
   **/
  public static function find_all_like($conditions, $raw_sql = '') {
    return parent::generic_find_all_like($conditions, static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param array $conditions (required)
   * @param string $raw_sql (optional)
   * @return QuotationsCustomStone[]
   **/
  public static function find_all_by($conditions, $raw_sql = '') {
    return parent::generic_find_all_by($conditions, static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param string $raw_sql (optional)
   * @return QuotationsCustomStone[]
   **/
  public static function find_all($raw_sql = '') {
    return parent::generic_find_all(static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param array $conditions (required)
   * @param string $raw_sql (optional)
   * @return QuotationsCustomStone
   **/
  public static function find_by($conditions, $raw_sql = '') {
    return parent::generic_find_by($conditions, static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param string $id (required)
   * @param string $raw_sql (optional)
   * @return QuotationsCustomStone
   **/
  public static function find_by_id($id, $raw_sql = '') {
    return parent::generic_find_by_id($id, static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param array $conditions (required)
   * @param string $raw_sql (optional)
   * @return int
   **/
  public static function count_all_by($conditions, $raw_sql = '') {
    return parent::generic_count_all_by($conditions, static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param array $conditions (required)
   * @param string $raw_sql (optional)
   * @return bool
   **/
  public static function delete_by($conditions, $raw_sql = '') {
    return parent::generic_delete_by($conditions, static::OM_CLASS_NAME, $raw_sql);
  }

}