<?php
class BaseSandboxUsers extends AppModel
{
  const DB_NAME = 'rde_b1mo';
  const TABLE_NAME = 'sandbox_users';
  const OM_CLASS_NAME = 'SandboxUsers';
  const columns = ['userId', 'companyId', 'personId', 'oldUserId', 'companyName', 'tradeRegNo', 'street', 'nr', 'extension', 'zipcode', 'domestic', 'country', 'payterm', 'gender', 'firstName', 'lastName', 'phone', 'fax', 'mobile', 'email', 'invoice_email', 'invoice_email_confirmed', 'salt', 'password', 'lastDeliveryAddressId', 'blocked', 'blockedbyadmin', 'private', 'noti_produced_mail', 'noti_delivery_mail', 'noti_order_mail', 'noti_confirm_mail', 'ip', 'created', 'lastLogin', 'lastUpdate', 'supplier', 'sms', 'sms_delivered'];
  const field_structure = [
    'userId'                      => ['type' => 'int', 'length' => '8', 'null' => false],
    'companyId'                   => ['type' => 'int', 'length' => '7', 'null' => true],
    'personId'                    => ['type' => 'int', 'length' => '8', 'null' => true],
    'oldUserId'                   => ['type' => 'int', 'length' => '11', 'null' => false],
    'companyName'                 => ['type' => 'varchar', 'length' => '200', 'null' => true],
    'tradeRegNo'                  => ['type' => 'varchar', 'length' => '12', 'null' => true],
    'street'                      => ['type' => 'varchar', 'length' => '150', 'null' => true],
    'nr'                          => ['type' => 'int', 'length' => '5', 'null' => false],
    'extension'                   => ['type' => 'varchar', 'length' => '10', 'null' => true],
    'zipcode'                     => ['type' => 'varchar', 'length' => '6', 'null' => false],
    'domestic'                    => ['type' => 'varchar', 'length' => '150', 'null' => false],
    'country'                     => ['type' => 'varchar', 'length' => '2', 'null' => false],
    'payterm'                     => ['type' => 'int', 'length' => '2', 'null' => false],
    'gender'                      => ['type' => 'enum', 'length' => '2', 'null' => true, 'enums' => ['female','male']],
    'firstName'                   => ['type' => 'varchar', 'length' => '255', 'null' => false],
    'lastName'                    => ['type' => 'varchar', 'length' => '255', 'null' => false],
    'phone'                       => ['type' => 'varchar', 'length' => '15', 'null' => true],
    'fax'                         => ['type' => 'varchar', 'length' => '15', 'null' => true],
    'mobile'                      => ['type' => 'varchar', 'length' => '15', 'null' => true],
    'email'                       => ['type' => 'varchar', 'length' => '150', 'null' => false],
    'invoice_email'               => ['type' => 'varchar', 'length' => '150', 'null' => true],
    'invoice_email_confirmed'     => ['type' => 'boolean', 'length' => '1', 'null' => false],
    'salt'                        => ['type' => 'varchar', 'length' => '20', 'null' => false],
    'password'                    => ['type' => 'varchar', 'length' => '40', 'null' => false],
    'lastDeliveryAddressId'       => ['type' => 'int', 'length' => '8', 'null' => true],
    'blocked'                     => ['type' => 'enum', 'length' => '2', 'null' => false, 'enums' => ['false','true']],
    'blockedbyadmin'              => ['type' => 'enum', 'length' => '2', 'null' => false, 'enums' => ['false','true']],
    'private'                     => ['type' => 'enum', 'length' => '2', 'null' => false, 'enums' => ['false','true']],
    'noti_produced_mail'          => ['type' => 'boolean', 'length' => '1', 'null' => false],
    'noti_delivery_mail'          => ['type' => 'boolean', 'length' => '1', 'null' => false],
    'noti_order_mail'             => ['type' => 'boolean', 'length' => '1', 'null' => false],
    'noti_confirm_mail'           => ['type' => 'boolean', 'length' => '1', 'null' => false],
    'ip'                          => ['type' => 'varchar', 'length' => '255', 'null' => true],
    'created'                     => ['type' => 'datetime', 'length' => '', 'null' => true],
    'lastLogin'                   => ['type' => 'datetime', 'length' => '', 'null' => true],
    'lastUpdate'                  => ['type' => 'datetime', 'length' => '', 'null' => true],
    'supplier'                    => ['type' => 'varchar', 'length' => '255', 'null' => true],
    'sms'                         => ['type' => 'boolean', 'length' => '1', 'null' => false],
    'sms_delivered'               => ['type' => 'boolean', 'length' => '1', 'null' => false],
  ];

  protected static $primary_key = ['userId'];
  protected $auto_increment = 'userId';

  public $userId, $companyId, $personId, $oldUserId, $companyName, $tradeRegNo, $street, $nr, $extension, $zipcode, $domestic, $country, $payterm, $gender, $firstName, $lastName, $phone, $fax, $mobile, $email, $invoice_email, $invoice_email_confirmed, $salt, $password, $lastDeliveryAddressId, $blocked, $blockedbyadmin, $private, $noti_produced_mail, $noti_delivery_mail, $noti_order_mail, $noti_confirm_mail, $ip, $created, $lastLogin, $lastUpdate, $supplier, $sms, $sms_delivered;

  function __construct($obj_arr = [], &$error_codes = []) {
    parent::__construct();
    $this->setDefaults();
    if (!empty($obj_arr)) {
      $this->from_array($obj_arr);
      $this->valid_required_fields($error_codes);
    }
  }

  /**
  * Set default object values
  **/
  public function setDefaults() {
    $this->oldUserId = 0;
    $this->country = 'NL';
    $this->payterm = 14;
    $this->invoice_email_confirmed = 0;
    $this->blocked = 'true';
    $this->blockedbyadmin = 'false';
    $this->private = 'false';
    $this->noti_produced_mail = 1;
    $this->noti_delivery_mail = 1;
    $this->noti_order_mail = 1;
    $this->noti_confirm_mail = 1;
    $this->sms = 0;
    $this->sms_delivered = 0;
  }

  /**
   * @param array $conditions (required)
   * @param string $raw_sql (optional)
   * @return SandboxUsers[]
   **/
  public static function find_all_like($conditions, $raw_sql = '') {
    return parent::generic_find_all_like($conditions, static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param array $conditions (required)
   * @param string $raw_sql (optional)
   * @return SandboxUsers[]
   **/
  public static function find_all_by($conditions, $raw_sql = '') {
    return parent::generic_find_all_by($conditions, static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param string $raw_sql (optional)
   * @return SandboxUsers[]
   **/
  public static function find_all($raw_sql = '') {
    return parent::generic_find_all(static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param array $conditions (required)
   * @param string $raw_sql (optional)
   * @return SandboxUsers
   **/
  public static function find_by($conditions, $raw_sql = '') {
    return parent::generic_find_by($conditions, static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param string $id (required)
   * @param string $raw_sql (optional)
   * @return SandboxUsers
   **/
  public static function find_by_id($id, $raw_sql = '') {
    return parent::generic_find_by_id($id, static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param array $conditions (required)
   * @param string $raw_sql (optional)
   * @return int
   **/
  public static function count_all_by($conditions, $raw_sql = '') {
    return parent::generic_count_all_by($conditions, static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param array $conditions (required)
   * @param string $raw_sql (optional)
   * @return bool
   **/
  public static function delete_by($conditions, $raw_sql = '') {
    return parent::generic_delete_by($conditions, static::OM_CLASS_NAME, $raw_sql);
  }

}