<script src="https://cdnjs.cloudflare.com/ajax/libs/fetch/2.0.3/fetch.js"></script>
<section class="contenttxt">

  <h1>Offerte bestellen - <?php echo $quotation->getQuotationNumberFull() ?></h1>

  <?php if($quotation->statusId == Status::STATUS_NEW): ?>

    <p>Op deze pagina kunt u uw offerte omzetten in een bestelling.<br/>

    <?php writeErrors($errors, true); ?>

    <div id="confirm" v-cloak="">

      <div id="step5" class="wizard">
        <form method="post">

          <div class="form-row">
            <label class="col4 col-form-label"><?php echo __("Bekijk offerte") ?></label>
            <div class="col7 col12-xs col-form-label">
              <a href="?action=pdf&id=<?php echo $quotation->quotationId ?>" target="_blank" class="btn btn-primary"><i class="icon-calculator"></i> BEKIJK OFFERTE <?php echo $quotation->getQuotationNumberFull() ?></a>
              <?php if($stone->alert!=''): ?>
                <p class="input-error-message" style="line-height: 23px;display: inline-block;"><?php echo $stone->alert ?></p>
              <?php endif; ?>
            </div>
          </div>


          <div class="form-row">
            <label class="col3 col-form-label"><?php echo __('Project naam') ?></label>
            <div class="col1">
              <div class="input-validation-icon" v-if="!is_valid && form_submit_error_fields.hasOwnProperty('projectName')">
                <img src="<?php echo $site->getTemplateUrl() ?>images/exclamation-circle.svg" :title="form_submit_error_fields['projectName']" width="20">
              </div>
              <div v-if="is_valid.projectName" class="input-validation-icon">
                <img src="<?php echo $site->getTemplateUrl() ?>images/check_green.svg" alt="" width="20" />
              </div>
            </div>
            <div class="col7">
              <input type="text" v-model="quotation.projectName" name="projectName" :ref="'projectName'" placeholder="<?php echo __('Project naam') ?>"  class="form-input" :class="{ 'has-error': !is_valid }" maxlength="30" />
              <p class="input-error-message" v-if="input_errors.projectName"> {{ input_errors.projectName }}</p>
            </div>
            <div class="col1">
              <a class="question-mark qtipa fa fa-info-circle" title="Naam van project of offerte"></a>
            </div>
          </div>

          <div class="form-row">
            <label class="col3 col-form-label"><?php echo __('Project referentie') ?></label>
            <div class="col1">
              <div class="input-validation-icon" v-if="!is_valid && form_submit_error_fields.hasOwnProperty('projectReference')">
                <img src="<?php echo $site->getTemplateUrl() ?>images/exclamation-circle.svg" :title="form_submit_error_fields['projectReference']" width="20">
              </div>
              <div class="input-validation-icon">
                <img src="<?php echo $site->getTemplateUrl() ?>images/check_green.svg" alt="" width="20" />
              </div>
            </div>
            <div class="col7">
              <input type="text" v-model="quotation.projectReference" name="projectReference" :ref="'projectReference'"  placeholder="<?php echo __('Project referentie') ?>" class="form-input" :class="{ 'has-error': !is_valid }"  maxlength="20" />
            </div>
            <div class="col1">
              <a class="question-mark qtipa fa fa-info-circle" title="Uw referentie"></a>
            </div>
          </div>

          <div class="form-row">
            <label class="col3 col-form-label"><?php echo __('Leverweek') ?></label>
            <div class="col1">
              <div class="input-validation-icon" v-if="!is_valid && form_submit_error_fields.hasOwnProperty('projectReference')">
                <img src="<?php echo $site->getTemplateUrl() ?>images/exclamation-circle.svg" :title="form_submit_error_fields['projectReference']" width="20">
              </div>
              <div class="input-validation-icon">
                <img src="<?php echo $site->getTemplateUrl() ?>images/check_green.svg" alt="" width="20" />
              </div>
            </div>
            <div class="col7">
              <div class="select">
                <select v-model="week" name="week" >
                  <?php foreach($weeks as $nr=>$name): ?>
                    <option value="<?php echo $nr ?>" <?php if($name['closed']) echo 'disabled' ?>><?php echo $name['name'] ?></option>
                  <?php endforeach; ?>
                </select>
              </div>
              <p class="input-error-message" v-if="input_errors.week"> {{ input_errors.week }}</p>
            </div>
            <div class="col1">
              <a class="question-mark qtipa fa fa-info-circle" title="Binnen 4 werkweken na opdrachtdatum kunnen de elementen worden geleverd. U kunt optioneel een eerdere weeknummer selecteren. In dat geval nemen we contact op."></a>
            </div>
          </div>

          <div class="form-row">
            <label class="col3 col-form-label"><?php echo __('Afleveradres') ?></label>
            <div class="col1">
              <div class="input-validation-icon">
                <img src="<?php echo $site->getTemplateUrl() ?>images/check_green.svg" alt="" width="20" />
              </div>
            </div>
            <div class="col7">
              <div class="select" >
                <select name="addressDeliveryId" v-model="quotation_extra.addressDeliveryId">
                  <option v-for="address in addresses" :value="address.addressId">
                    {{ getAddressFormatted(address) }}
                  </option>
                </select>
              </div>
            </div>
            <div class="col1">
              <a class="question-mark qtipa fa fa-info-circle" title="Selecteer uw afleveradres, of maak een nieuwe afleveradres aan. U kunt de raamdorpels ook komen ophalen, hiermee bespaart u verzendkosten."></a>
            </div>
          </div>

          <div v-if="quotation_extra.addressDeliveryId=='NEW'" id="wizard_delivery">
            <div>
              <label class="col3 col-form-label" style="font-weight: bold; padding-bottom: 10px;"><?php echo __('Nieuw afleveradres') ?></label>
              <div class="col9">
              </div>
            </div>
            <div class="form-row">
              <label class="col3 col-form-label"><?php echo __("Postcode + nummer") ?> <span class="form-arterisk">*</span></label>
              <div class="col1">
                <div v-if="is_valid_nr" class="input-validation-icon">
                  <img src="<?php echo $site->getTemplateUrl() ?>images/check_green.svg" alt="" width="20" />
                </div>
              </div>
              <div class="col3">
                <input type="text" class="form-input" v-model="quotation.zipcode" :ref="'zipcode'"  @change="validateZipcode(true)" name="zipcode" id="zipcode" placeholder="<?php echo __("Postcode") ?>" required maxlength="6" />
              </div>
              <div class="col2">
                <input type="text" class="form-input" name="nr" v-model="quotation.nr" :ref="'nr'" @change="validateNr(true)" placeholder="<?php echo __("Huisnummer") ?>" required="required" maxlength="5"/>
              </div>
              <div class="col2">
                <input type="text" class="form-input" name="ext" v-model="quotation.ext"  placeholder="<?php echo __("Toevoeging") ?>" maxlength="10"/>
              </div>
            </div>

            <div class="form-row">
              <label class="col3 col-form-label"><?php echo __("Plaats + straat") ?> <span class="form-arterisk">*</span></label>
              <div class="col1">
                <div v-if="is_valid_zipcode && is_valid_domestic" class="input-validation-icon">
                  <img src="<?php echo $site->getTemplateUrl() ?>images/check_green.svg" alt="" width="20" />
                </div>
              </div>
              <div class="col3">
                <input type="text" class="form-input" v-model="quotation.street" :ref="'street'" name="street" id="street" placeholder="<?php echo __("Straat") ?>" required @change="validateStreet(true)" readonly/>
              </div>
              <div class="col4">
                <input type="text" class="form-input" v-model="quotation.domestic" :ref="'domestic'" @change="validateDomestic(true)" name="domestic" id="domestic" placeholder="<?php echo __("Plaats") ?>" required readonly />
              </div>
            </div>

            <div class="form-row">
              <label class="col3 col-form-label"><?php echo __("Land") ?> <span class="form-arterisk">*</span></label>
              <div class="col1">
                <div class="input-validation-icon">
                  <img src="<?php echo $site->getTemplateUrl() ?>images/check_green.svg" alt="" width="20" />
                </div>
              </div>
              <div class="col7">
                <div class="select" >
                  <select name="country" id="country" v-model="quotation.country" :ref="'country'" required  @change="validateCountry(true)" >
                    <option value="BE">België</option>
                    <option value="DE">Duitsland</option>
                    <option value="NL">Nederland</option>
                  </select>
                </div>
                <div v-if="quotation.country!='NL'" style="color: #CE000C; padding-top: 5px;">Let op: levering buiten Nederland kan extra bezorgkosten met zich meebrengen.</div>
              </div>
            </div>


          </div>

          <div class="form-row">
            <label class="col3 col-form-label">Bijzonderheden levering</label>
            <div class="col1">
              <div class="input-validation-icon">
              </div>
            </div>
            <div class="col8">
              <label style="line-height: 1.5;display: flex;margin: 10px  0 10px 0;">
                <div>
                  <input type="checkbox" value="1" v-model="delivery_reach" name="delivery_reach" id="delivery_reach" class="form-checkbox"/>
                </div>
                <div>
                  Een vrachtwagen met oplegger kan de locatie goed bereiken<Br/>
                  Geen lengte / breedte / gewicht beperking
                </div>
              </label>
            </div>
            <div id="delivery_notes_container" v-if="!delivery_reach">
              <div class="col4">
              </div>
              <div class="col7">
                <textarea class="form-input" name="deliveryNotes" id="deliveryNotes" placeholder="<?php echo __("Toelichting bijzonderheden levering") ?>"><?php echo $quotation->deliveryNotes; ?></textarea>
              </div>
            </div>
          </div>


          <div class="form-row" v-if="showSms">
            <label class="col3 col-form-label">SMS/Whatsapp</label>
            <div class="col1">
              <div class="input-validation-icon">

              </div>
            </div>
            <div class="col8">
              <label style="line-height: 1.5;display: flex;margin: 10px  0 10px 0;">
                <div>
                  <input type="checkbox" value="1" v-model="quotation_extra.sms" name="sms" id="sms" class="form-checkbox" :true-value="1"/>
                </div>
                <div>
                  Verstuur een SMS/Whatsapp als de chauffeur onderweg is naar het afleveradres
                </div>
              </label>
              <label style="line-height: 1.5;display: flex;margin: 10px  0 10px 0;">
                <div>
                  <input type="checkbox" value="1" v-model="quotation_extra.sms_delivered" name="sms" id="sms" class="form-checkbox" :true-value="1"/>
                </div>
                <div>
                  Verstuur een SMS/Whatsapp als uw bestelling bezorgd is op het afleveradres
                </div>
              </label>
              <div v-if="quotation_extra.sms==1 || quotation_extra.sms_delivered==1">
                <div style="display: block;">
                  <input type="text" class="form-input" name="smsnumber" v-model="quotation_extra.smsnumber" size="25" maxlength="15" placeholder="Mobiel nummer..." style="width: 250px;" />
                </div>
                <div style="display: block;margin: 10px 0;">
                  Wilt u altijd een SMS/Whatsapp ontvangen? Dit kunt u instellen bij <a href="/mijn-instellingen">Mijn instellingen</a>
                </div>
              </div>
            </div>
          </div>


          <?php if($showOptions): ?>
            <div class="form-row confirmoptions">
              <label class="col12 col-form-label"><?php echo __("Opties") ?></label>
              <div class="col1 col-form-label">
                <input type="hidden" value="1" name="showoptions" />
                <input type="radio" value="1" name="option" id="option1" checked/>
              </div>
              <div class="col11 col-form-label">
                <label for="option1">
                  1. <?php echo $sOption1 ?>
                </label>
              </div>
              <div class="col1 col-form-label">
                  <input type="radio" value="mattingOnlyGlueFlag" name="option" id="option2"/>
              </div>
              <div class="col11 col-form-label">
                <label for="option2">
                  2. <?php echo $sOption2 ?> <span class="error">*</span>
                </label>
              </div>

              <div class="col1 col-form-label">
                  <input type="radio" value="mattingRemovalDiscountFlag" name="option" id="option3"/>
              </div>
              <div class="col11 col-form-label">
                <label for="option3">
                    3. <?php echo $sOption3 ?> <span class="error">*</span>
                </label>
              </div>

              <div class="col12 col-form-label">
                <label>
                  <br/>
                  <span class="error">*</span> <?php echo $optionsExtra ?>
                </label>
              </div>

            </div>
          <?php endif; ?>


          <div class="form-row">
            <label class="col4 col-form-label"><?php echo __("Algemene voorwaarden") ?></label>
            <div class="col8 col12-xs">
              <label style="line-height: 2.5">
                Door verder te gaan ga je akkoord met onze <a href="https://www.raamdorpel.nl/algemene-voorwaarden" target="_blank">Algemene Voorwaarden</a>
              </label>
            </div>
          </div>

          <div class="form-row">
            <label class="col4 col-form-label"><?php echo __("Opmerkingen") ?></label>
            <div class="col7">
              <textarea class="form-input" name="customerNotes" id="customerNotes" placeholder="<?php echo __("Uw opmerkingen of vragen") ?>"><?php echo $quotation->customerNotes ?></textarea>
            </div>
          </div>

          <a href="<?php echo $returnurl ?>" style="padding: 10px 0;display: inline-block"><i class="icon-arrow-left"></i> Terug naar start pagina</a>
          <button type="submit" name="order" id="order" class="btn" style="float: right;" @click="validate(true)">
            <?php echo $payonline?__("Bestellen en online afrekenen").' <i class="fa fa-chevron-right"></i>':' <i class="fa fa-check"></i>'.__("Bestellen"); ?>
          </button>

        </form>
      </div>


    </p>

  </div>


  <?php else: ?>
    <p>Offerte <?php echo $quotation->getQuotationNumberFull() ?> is al in bestelling of geleverd.</p>
  <?php endif; ?>

</section>

<script type="text/javascript">

  var firsttime = true;
  var form_submit_error_fields = parseJson('<?php echo json_encode($errors) ?>');
  var quotation = parseJson('<?php echo StringHelper::escapeJson(json_encode(AppModel::plainObject($quotation, ["mitre", "heartClickSize"]))) ?>');
  var quotation_extra = parseJson('<?php echo json_encode(AppModel::plainObject($quotation_extra, [])) ?>');
  var addresses = <?php echo json_encode(AppModel::plainObjects($addresses)) ?>;

  var app = Vue.createApp({
    data() {
      return {
        is_valid: {
          'projectName': false
        },
        all_valid: false,
        is_active: {},
        form_submit_error_fields: form_submit_error_fields,
        input_errors: {},

        quotation: quotation,
        quotation_extra: quotation_extra,
        addresses: addresses,
        week: <?php echo $dueweek ?>,
        delivery_reach: <?php echo (isset($_POST["delivery_reach"]) || (!isset($_POST["delivery_reach"]) && !isset($_POST["projectName"]) && $quotation->deliveryNotes == "")) ? 'true' : 'false'; ?>,

        is_valid_street: false,
        is_valid_nr: false,
        is_valid_zipcode: false,
        is_valid_domestic: false,

        showSms: <?php echo (!isset($_SESSION['userObject']) || $_SESSION['userObject']->sms == 0)?'true':'false' ?>,
      }
    },

    /*********************
     Mounted
     *********************/
    // execute this when the vue instance is loaded
    mounted() {
      this.projectNameChanged();
      this.validate(false);

      <?php if(isset($errors["street"]) || isset($errors["domestic"])): //on error customer may enter street and city ?>
      $('#street,#domestic').prop("readonly",false);
      <?php endif; ?>

      firsttime = false;
    },
    watch: {
      'quotation_extra.addressDeliveryId': function() {
        if(quotation_extra.addressDeliveryId=="NEW") {
          //word zichtbaar gezet, leegmaken velden.
          this.quotation.street = '';
          this.quotation.nr = '';
          this.quotation.zipcode = '';
          this.quotation.domestic = '';
          this.quotation.ext = '';
        }
        this.validate(false);
      },
      'quotation.projectName': function() {
        this.projectNameChanged();
      },
      week: function() {
        this.weekChanged();
      },
    },
    computed: {
    },
    methods: {
      projectNameChanged: function () {
        this.is_valid['projectName'] = false;
        if(!this.quotation.projectName || this.quotation.projectName=="") {
          this.input_errors['projectName'] = "Project naam is verplicht";
        }
        else {
          this.is_valid['projectName'] = true;
          delete this.input_errors['projectName'];
        }

      },
      weekChanged: function () {
        var dueDateWeekSet = <?php echo $dueweek ?>;
        if(this.week<dueDateWeekSet) {
          this.input_errors['week'] = "De gekozen leverweek is eerder dan onze standaard leverweek. Na onze controle, kunt u in uw account bij offertes de definitieve leverweek inzien.";
        }
        else {
          delete this.input_errors['week'];
        }
      },

      //validation
      validate: function (show) {
        var allvalid = true;
        if(!this.validateProjectName(show)) {
          allvalid = false;
          // this.all_valid = allvalid;
          // return;
        }
        if(this.quotation_extra.addressDeliveryId=='NEW') {
          if(this.validateZipcode(show) && this.validateNr(show)) {
            if(this.quotation.country!="NL") { //only use postcode api in netherlands
              $('#street,#domestic').prop("readonly",false);
              allvalid = true;
              if(!this.validateStreet(show)) allvalid = false;
              if(!this.validateDomestic(show)) allvalid = false;
              this.all_valid = allvalid;
              return;
            }
            else {
              $('#street,#domestic').prop("readonly",true);
            }
            //beide valid, haal adres op
            fetch("<?php echo reconstructQuery() ?>&action=postcodeapi&zipcode=" + this.quotation.zipcode+"&nr=" + this.quotation.nr)
              .then(result => {
                return result.json()
              })
              .then(res => {
                if (res.error) {
                  this.errors.push(res.error);
                } else {
                  if (res.success) {
                    if (res.success==="open") {
                      //no credits at postcode api, customer can enter the info himself
                      $('#street,#domestic').prop("readonly",false);
                    }
                    else {
                      this.quotation.street = res.data.street;
                      this.quotation.domestic = res.data.city;
                    }
                  }
                  else {
                    this.quotation.street = "";
                    this.quotation.domestic = "";
                  }
                  var allvalid = true;
                  if(!this.validateStreet(show)) allvalid = false;
                  if(!this.validateDomestic(show)) allvalid = false;
                  this.all_valid = allvalid;
                }
              });
          }
          else {
            allvalid = false;
          }
          if(allvalid) {
            if(!this.validateStreet(show)) allvalid = false;
            if(!this.validateDomestic(show)) allvalid = false;
          }
        }
        else if(this.quotation_extra.addressDeliveryId=="") {
          allvalid = false;
        }

        this.all_valid = allvalid;

      },
      validateProjectName: function (show) {
        var valid = true;
        if(!this.quotation.projectName || this.quotation.projectName=="") {
          valid = false;
          if(show) $(this.$refs.projectName).addClass('inputerror');
        }
        else {
          if(show) $(this.$refs.projectName).removeClass('inputerror');
        }
        return valid;
      },
      validateStreet: function (show) {
        var valid = true;
        if(!this.quotation.street || this.quotation.street=="") {
          valid = false;
          if(show) $(this.$refs.street).addClass('inputerror');
        }
        else {
          if(show) $(this.$refs.street).removeClass('inputerror');
        }
        this.is_valid_street = valid;
        if(show) {
          this.validate(false);
        }
        return valid;
      },
      validateNr: function (show) {
        var valid = true;
        if(!this.quotation.nr || this.quotation.nr=="") {
          valid = false;
          if(show) $(this.$refs.nr).addClass('inputerror');
        }
        else {
          if(show) $(this.$refs.nr).removeClass('inputerror');
        }
        this.is_valid_nr = valid;
        if(show) {
          this.validate(false);
        }
        return valid;
      },
      validateZipcode: function (show) {
        var valid = true;
        if(!this.quotation.zipcode || this.quotation.zipcode=="") {
          valid = false;
          if(show) $(this.$refs.zipcode).addClass('inputerror');
        }
        else {
          if(show) $(this.$refs.zipcode).removeClass('inputerror');
        }
        this.is_valid_zipcode = valid;
        if(show) {
          this.validate(false);
        }
        return valid;
      },
      validateDomestic: function (show) {
        var valid = true;
        if(!this.quotation.domestic || this.quotation.domestic=="") {
          valid = false;
          if(show) $(this.$refs.domestic).addClass('inputerror');
        }
        else {
          if(show) $(this.$refs.domestic).removeClass('inputerror');
        }
        this.is_valid_domestic = valid;
        if(show) {
          this.validate(false);
        }
        return valid;
      },
      validateCountry: function (show) {
        if(show) {
          this.validate(false);
        }
        return true;
      },
      getAddressFormatted: function (address) {
        var str = "";
        if(address.addressId==20357) {
          str = "Ophalen - Raambrug 9, 5531 AG Bladel, Nederland";
        }
        else if(address.addressId=="NEW") {
          str = "Nieuw afleveradres";
        }
        else {
          str = "Afleveren - ";
          if(address.type=="visit") {
            str += "Bezoekadres - ";
          }
          if(address.title!="" && address.title!=null) {
            str += address.title+" - ";
          }
          str += address.street+" "+ address.nr;
          if(address.extension!="" && address.extension!=null) {
            str += " "+address.extension;
          }
          str += ", "+address.zipcode+" "+address.domestic+", NL";
        }
        return str;
      }


    }

  });

  var vm = app.mount("#confirm");

  $(document).ready( function() {
    $("#order").click (function(event) {
      if(!vm.all_valid) {
        event.preventDefault();
        return;
      }
    });
  });


</script>