<?php

  AppModel::loadBaseClass('BaseStoneOrder');

  class StoneOrderModel extends BaseStoneOrder {

    public static $stati = [
      "new"       => "Te bestellen",
      "ordered"   => "Besteld",
      "delivered" => "Geleverd",
    ];

    public function getSenddate($format = "d-m-Y H:i") {
      if ($this->senddate != "") {
        return date($format, strtotime($this->senddate));
      }
      return '';
    }

    public function getRecieveddate($format = "d-m-Y H:i") {
      if ($this->receiveddate != "") {
        return date($format, strtotime($this->receiveddate));
      }
      return '';
    }

    /**
     * Haal alle orders op met status ordered, en check of deze helemaal verwerkt zijn.
     * Zo ja, pas status aan naar delivered
     */
    public static function checkStatus() {

      foreach (StoneOrder::find_all_by(["status" => "ordered"]) as $stoneorder) {
        $items = StoneOrderItem::find_all_by(["stone_order_id" => $stoneorder->id], "AND receiveddate IS NULL");
        if (count($items) == 0) {
          $stoneorder->receiveddate = date("Y-m-d H:i:s");
          $stoneorder->status = 'delivered';
          $stoneorder->save();
        }
      }

    }

  }