<?php

  use gsdfw\domain\gpsbuddy\service\GpsbuddyApi;

  class externalRdeActions extends externalActions {

    public function executeTrucklocations() {

      if (in_array($_SERVER["REMOTE_ADDR"], Config::get("RDE_VALID_IP_ADDRESSES"))) {
        $gpsbuddy = GpsbuddyApi::getInstance();
        $gpsbuddy->init();
        foreach (GpsbuddyTrucks::getActiveTrucks() as $tr) {
          $gpsbuddy->addTruck($tr->truckId, $tr->gpsbuddyId);
        }
        if ($gpsbuddy->login()) {
          $result = $gpsbuddy->getCurrenttruckLocations(false);
          ResponseHelper::exitAsJson($result);
        }
      }

      ResponseHelper::exitAsJson(["result" => false]);

    }

    public function executeSendtenderemail() {

      //aanroepen vanuit de andere domeinen
      $allowedOrigins = [
        "http://www.raamdorpel.nl",
        "https://www.raamdorpel.nl",
        "http://api.raamdorpel.nl",
        "https://api.raamdorpel.nl",
        "http://beheer.raamdorpel.nl",
        "https://beheer.raamdorpel.nl",
        "http://beheer.raamdorpel.nl.rde.localhost",
      ];
      if (in_array($_SERVER["HTTP_ORIGIN"], $allowedOrigins)) {
        header("Access-Control-Allow-Origin: " . $_SERVER["HTTP_ORIGIN"]);
      }

      //echo sha1(date('Ymd') . '105003' . 'Tf2S@gA3sDj*eE#ypOJKJ!heeuh');
      $result = ["success" => false, "message" => ""];

      if (in_array($_SERVER["REMOTE_ADDR"], Config::get("RDE_VALID_IP_ADDRESSES"))) {

        if (!isset($_POST["quotationId"])) {
          $result["message"] = "quotationId niet gezet";
        }
        elseif (!isset($_POST["code"]) || $_POST["code"] != sha1(date('Ymd') . $_POST["quotationId"] . 'Tf2S@gA3sDj*eE#ypOJKJ!heeuh')) {
          $result["message"] = "code invalid";
        }
        else {
          $quotation = Quotations::find_by(["quotationId" => $_POST["quotationId"]]);
          if (!$quotation) {
            $result["message"] = "quotation niet gevonden";
          }
          else {
            $elements = OrderElements::find_all_by(["quotationId" => $quotation->quotationId]);
            $message = false;
            if (isset($_POST["message"])) {
              $message = $_POST["message"];
            }
            MailsFactory::sendTenderEmail($quotation, $elements, $message);
            $result["success"] = true;
            $result["message"] = "message send";
          }
        }


      }

      ResponseHelper::exitAsJson($result);

    }

    public function executeProductietekening() {
      if (!in_array($_SERVER["REMOTE_ADDR"], Config::get("RDE_VALID_IP_ADDRESSES"))) {
        ResponseHelper::redirectAlertMessage("Geen toegang: " . $_SERVER["REMOTE_ADDR"]);
      }
      if (isset($_GET["quotationId"])) {
        $draw = new DrawElements();
        $elementId = false;
        if (isset($_GET["elementId"])) {
          $elementId = $_GET["elementId"];
        }
        $draw->setSeperateFiles(isset($_GET["elementsave"]) && $_GET["elementsave"] == "true");
        $elementsgenerated = $draw->generate($_GET["quotationId"], $elementId);

        if ($draw->isSeperateFiles()) {
          ResponseHelper::exitAsJson(["succes" => "true", "elementsgenerated" => $elementsgenerated]);
        }
        else { //direct naar output
          if ($elementsgenerated == 0) {
            echo 'Geen elementen met tekeningen.';
          }
          ResponseHelper::exit();
        }
      }
      echo "Geen quotationId gevonden.";
      ResponseHelper::exit();
    }

    public function executeQuotationpdf() {
      if (!in_array($_SERVER["REMOTE_ADDR"], Config::get("RDE_VALID_IP_ADDRESSES"))) {
        echo "false";
        ResponseHelper::exit();
      }
      if (isset($_GET["quotationId"])) {
        if (!isset($_GET["hash"])) ResponseHelper::redirectAlertMessage("Geen toegang");
        $hash_decode = Quotations::decrypt($_GET["hash"]);
        $parts = explode("_", $hash_decode);
        $quotation = false;
        if (count($parts) == 3) {
          $quotationId = $parts[1];
          $userId = $parts[2];
          $quotation = Quotations::find_by(["quotationId" => $quotationId, "userId" => $userId, "flaggedForDeletion" => 0]);
        }
        if (!$quotation) {
          echo "false";
          ResponseHelper::exit();
        }

        $pdf = new QuotationPdf($quotation->quotationId);
        $pdf->setShow(true);
        if (isset($_GET["variant"])) {
          $pdf->setVariant($_GET["variant"]);
        }
        $pdf->generatePdf();
      }
      ResponseHelper::exit();
    }

    public function executeProductionreceiptpdf() {
      if (!in_array($_SERVER["REMOTE_ADDR"], Config::get("RDE_VALID_IP_ADDRESSES"))) {
        echo "false";
        ResponseHelper::exit();
      }
      if (isset($_GET["quotationId"])) {
        if (!isset($_GET["hash"])) ResponseHelper::redirectAlertMessage("Geen toegang");
        $hash_decode = Quotations::decrypt($_GET["hash"]);
        $parts = explode("_", $hash_decode);
        $quotation = false;
        if (count($parts) == 3) {
          $quotationId = $parts[1];
          $userId = $parts[2];
          $quotation = Quotations::find_by(["quotationId" => $quotationId, "userId" => $userId, "flaggedForDeletion" => 0]);
        }
        if (!$quotation) {
          echo "false";
          ResponseHelper::exit();
        }

        $pdf = new ProductionReceiptPdf($quotation->quotationId);
        $pdf->setSupplierExternal(true);
        $pdf->generate();
      }
      exit();
    }

  }