<?php TemplateHelper::includePartial('_tabs.php', 'orders', compact(["quotation", "quotation_extra", "company", "sandboxuser"])); ?>
<form method="post" class="edit-form">
  <table class="default_table">
    <tr class="dataTableHeadingRow">
      <td>Eigenschap</td>
      <td>Waarde</td>
    </tr>
  <?php
    foreach ($formOther->getElements() as $element):
      $element->renderRow();
    endforeach;
  ?>
    <tr class="dataTableRow">
      <td class="head">Acties</td>
      <td>
        <a id="editQuotation" href="<?php echo $userLoginLink ?>" target="_blank" class="gsd-btn gsd-btn-secondary gsd-btn-small">Wijzig offerte</a>
        <a id="extraProducts" href="<?php echo $userLoginLinkExtraProd ?>" target="_blank" class="gsd-btn gsd-btn-secondary gsd-btn-small">Extra producten</a>
        <a id="orderPage" href="<?php echo $userLoginLinkOrderConfirm ?>" target="_blank" class="gsd-btn gsd-btn-secondary gsd-btn-small">Bestelpagina</a>
      </td>
    </tr>
    <tr class="dataTableRow">
      <td class="head">Gelijmd door</td>
      <td>
        <?php foreach ($employees as $employee): ?>
            <span><?php echo "{$employee->productionEmployee->name}, {$employee->date->format('d-m-Y H:i:s')}"; ?></span><br>
        <?php endforeach; ?>
      </td>
    </tr>
    <tr class="dataTableRow">
      <td class="head">Datum en tijd</td>
      <td>
        <?php if (count($employees)): ?>
          <span><?php echo end($employees)->date->format('d-m-Y H:i:s'); ?></span><br>
        <?php endif; ?>
      </td>
    </tr>
  </table>
  <input type="submit" name="go" value="Opslaan" class="gsd-btn gsd-btn-primary"/>
  <input type="submit" name="go_list" value="Opslaan en naar lijst" class="gsd-btn gsd-btn-secondary"/>
</form>