<?php

  AppModel::loadBaseClass('BaseVgmCheck');

  class VgmCheckModel extends BaseVgmCheck {


    public static $codes = [
      "1"  => "Orde en netheid",
      "2"  => "Kantine-, kleed-, toilet- ,wasruimte",
      "3"  => "Alarmkaart / EHBO-middelen / BHV    ",
      "4"  => "Observatie Onveilig Gedrag",
      "5"  => "Zijn er blusmiddelen aanwezig?",
      "6"  => "Elektra/ oriëntatieverlichting",
      "7"  => "Rolsteigers",
      "8"  => "Introductie / voorlichting, instructie",
      "9"  => "Scheiding, opslag en afval",
      "10" => "Opslag gevaarlijke stoffen",
      "11" => "Ladders / trappen",
      "12" => "Doeltreffend gebruik / PBM",
      "13" => "Keuring machines en gereedschappen",
      "14" => "Veiligheidsbladen (VIB)",
      "50" => "Benodigde middelen aanwezig",
      "51" => "Hijsmiddelen",
      "52" => "Stofvrij werken (afzuiging)",
      "53" => "Vaste steigers (keuring)",
      "54" => "Naleven veiligheid onderaannemers en derden",
    ];

    public static $checks = [
      "1" => "OK",
      "2" => "NOK",
      "3" => "NVT",
    ];

    public function getCheckDesc() {
      if ($this->check > 0) {
        return VgmCheck::$checks[$this->check];
      }
      return "";
    }

  }