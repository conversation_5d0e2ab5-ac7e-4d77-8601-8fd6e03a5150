<?php TemplateHelper::includePartial("_tabs.php", 'orders') ?>

<?php $dataTable->render(); ?>

<script type="text/javascript">

  $(document).ready(function () {
    <?php echo $dataTable->renderJSconfig() ?>

    <?php if ($page === '3'): ?>
      // preset of 100 rows per page for page 3
      $('#<?php echo $dataTable->getName() ?>_length').find('select').val(100).change();
    <?php endif; ?>
    <?php if ($disablePagination): ?>
      $('.dataTables_paginate').hide();
      $('.dataTables_length').hide();
      $('.dataTables_info').hide();
      $('.content-block').css('padding', '0');
    <?php endif; ?>

    gsdSelect("status_filter", 'Status... ');
    gsdSelect("year_filter", 'Jaar... ');

    // Select field for company filter
    $('#companyid').select2({
      ajax: {
        url: '?action=companySearchAjax',
        dataType: 'json',
        delay: 250,
        data: (params) => ({ search: params.term, type: "public" }),
        processResults: (data, params) => {
          if (!params.term) data = [{ id: 0, text: 'All bedrijven' }, ...data];
          return { results: data };
        },
      }
    });

    // Set company filter
    $(document).on('click', '.company-filter', function() {
      // empty the search field
      $('#search').val('');

      const newOption = new Option($(this).data('name'), $(this).data('id'), true, true);
      $('#companyid').append(newOption).trigger('change');
    });
  });
</script>

