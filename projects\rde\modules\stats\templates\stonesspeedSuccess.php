<?php
  TemplateHelper::includePartial('_tabs.php',"stats");
?>

<div class="box">
  <form method="post">

    <input type="text" placeholder="Zoek steen..." name="stat_search" value="<?php echo $_SESSION["stat_search"] ?>"/>

    Productiedatum van
    <?php echo getDateSelector("stat_from",$_SESSION["stat_from"]) ?>
    tot
    <?php echo getDateSelector("stat_to",$_SESSION["stat_to"]) ?>

    <label><input type="checkbox" value="1" name="stat_endstones" <?php writeIfCheckedVal($_SESSION["stat_endstones"],1) ?>/> met eindstenen</label>


    <select name="stat_employee">
      <option value="">Selecteer medewerker...</option>
      <?php foreach(ProductionEmployees::getWorkingNotTesters() as $pe): ?>
        <option value="<?php echo $pe->employeeId ?>" <?php if($pe->employeeId==$_SESSION["stat_employee"]) echo 'selected'; ?>><?php echo $pe->name ?></option>
      <?php endforeach ?>
    </select>


    <input type="submit" value="Zoeken" name="search"/>
  </form>
</div>


<?php if(count($stats)==0): ?>
  Er zijn geen resultaten.
<?php else: ?>
  <table class="default_table" style="width: auto; min-width: 600px;">
    <tr class="dataTableHeadingRow">
      <td>Steen
        <a class="fa fa-sort-up" href="?sort=stone&dir=up" style="font-size: 15px;"></a>
        <a class="fa fa-sort-down" href="?sort=stone&dir=down" style="font-size: 15px;"></a>
      </td>
      <td style="text-align: right;">Aantal orders
        <a class="fa fa-sort-up" href="?sort=total_orders&dir=up" style="font-size: 15px;"></a>
        <a class="fa fa-sort-down" href="?sort=total_orders&dir=down" style="font-size: 15px;"></a>
      </td>
      <td style="text-align: right;">Totaal m
        <a class="fa fa-sort-up" href="?sort=total_meters&dir=up" style="font-size: 15px;"></a>
        <a class="fa fa-sort-down" href="?sort=total_meters&dir=down" style="font-size: 15px;"></a>
      </td>
      <td style="text-align: right;">m / uur
        <a class="fa fa-sort-up" href="?sort=cm_per_hour&dir=up" style="font-size: 15px;"></a>
        <a class="fa fa-sort-down" href="?sort=cm_per_hour&dir=down" style="font-size: 15px;"></a>
      </td>
    </tr>
    <?php
      $total_meters = $total_orders = 0;
      foreach($stats as $stat):
        $total_meters += $stat["total_meters"];
        $total_orders += $stat["total_orders"];
        ?>
        <tr class="dataTableRow trhover">
          <td>
            <?php if(empty($stat["stone"])): ?>
              Onbekend/verwijderd
            <?php else: ?>
            <a href="?action=stonesspeeddetail&stoneid=<?php echo $stat["stone"]->stoneId ?>"><?php echo $stat["stone"]->name ?></a>
            <?php endif; ?>
          </td>
          <td style="text-align: right;"><?php echo $stat["total_orders"] ?></td>
          <td style="text-align: right;"><?php echo getLocalePrice($stat["total_meters"]) ?></td>
          <td style="text-align: right;"><?php echo getLocalePrice($stat["cm_per_hour"]/100) ?></td>
        </tr>
      <?php endforeach; ?>
    <tr class="dataTableRow topborder">
      <td style="text-align: right;font-weight: bold;"></td>
      <td style="text-align: right;font-weight: bold;"><?php echo $total_orders ?></td>
      <td style="text-align: right;font-weight: bold;"><?php echo floor($total_meters) ?></td>
      <td style="text-align: right;font-weight: bold;"></td>
    </tr>
  </table>
<?php endif; ?>
