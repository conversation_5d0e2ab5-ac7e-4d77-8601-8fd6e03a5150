<div class="contenttxt">
  <?php echo process_text($page->content->content1); ?>
</div>

<?php
  /** @var \gsdfw\domain\klantenvertellen\service\Klantenvertellen $kv */
  $xml = $kv->getXML();
  if($xml!=false):
    foreach($xml->reviews->reviews as $review):
    ?>
    <div class="homereview_item">
      <div>
        <div class="review_star">
          <span class="review_star_star">★</span>
          <span class="review_star_score"><?php echo $review->rating ?></span>
        </div>
        <h3 class="homereview_title"><?php echo $review->title ?></h3>
        <p class="homereview_teaser"><?php if($review->remark) echo $review->remark; ?></p>
        <p class="homereview_teaser"><?php echo $review->reviewAuthor; ?> uit <?php echo $review->city; ?> op <?php echo date("d-m-Y",strtotime($review->dateSince)) ?></p>
      </div>
      <?php $kv->printReviewStructuredData($review) ?>
    </div>
  <?php endforeach; ?>
<?php endif; ?>

<?php TemplateHelper::includePartial("_pagefiles.php",'site',array('site'=>$site,'pagefiles'=>$pagefiles,'page'=>$page)) ?>
<?php if(Config::get('PAGE_IMAGES_360_ENABLED', true)) TemplateHelper::includePartial("_pageimages360.php",'site',array('site'=>$site,'images'=>$images360,'page'=>$page)) ?>
<?php TemplateHelper::includePartial("_pageimages.php",'site',array('site'=>$site,'images'=>$images,'page'=>$page)) ?>
<?php TemplateHelper::includePartial("_youtube.php",'site',array('page'=>$page)) ?>
<?php echo Tag::getTagsString($page, $_SESSION['lang']) ?>
