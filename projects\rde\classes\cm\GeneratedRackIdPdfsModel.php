<?php

  AppModel::loadBaseClass('BaseGeneratedRackIdPdfs');

  class GeneratedRackIdPdfsModel extends BaseGeneratedRackIdPdfs {

    public static function getNewPdfId() {
      $query = "SELECT MAX(rackPdfId) as max_rack_pdf_id FROM " . GeneratedRackIdPdfs::getTablename() . " ";
      $result = DBConn::db_link()->query($query);
      if ($row = $result->fetch_assoc()) {
        return $row['max_rack_pdf_id'] += 1;
      }
      return 0;
    }

  }

  