<?php
class BaseProductionEmployees extends AppModel
{
  const DB_NAME = 'rde_b1mo';
  const TABLE_NAME = 'production_employees';
  const OM_CLASS_NAME = 'ProductionEmployees';
  const columns = ['employeeId', 'name', 'scannerNumberId', 'scannerSerialId', 'working'];
  const field_structure = [
    'employeeId'                  => ['type' => 'int', 'length' => '3', 'null' => false],
    'name'                        => ['type' => 'varchar', 'length' => '200', 'null' => false],
    'scannerNumberId'             => ['type' => 'int', 'length' => '11', 'null' => false],
    'scannerSerialId'             => ['type' => 'text', 'length' => '', 'null' => false],
    'working'                     => ['type' => 'boolean', 'length' => '1', 'null' => false],
  ];

  protected static $primary_key = ['employeeId'];
  protected $auto_increment = 'employeeId';

  public $employeeId, $name, $scannerNumberId, $scannerSerialId, $working;

  function __construct($obj_arr = [], &$error_codes = []) {
    parent::__construct();
    $this->setDefaults();
    if (!empty($obj_arr)) {
      $this->from_array($obj_arr);
      $this->valid_required_fields($error_codes);
    }
  }

  public function valid(&$error_codes = []) {
    $errors = [];
    foreach(self::columns as $column) {
      if ($column != $this->auto_increment)
      {
        call_user_func_array([$this, 'v_' . $column], [&$errors]);
      }
    }
    if(empty($errors)) {
      return true;
    }
    $error_codes = array_merge($error_codes, $errors);
    return false;
  }

  public function valid_required_fields(&$error_codes = []) {
    $errors = [];
    foreach (self::columns as $column) {
      $function_name = "v_$column";
      if ($column!=$this->auto_increment) {
        call_user_func_array([$this, $function_name], [&$errors]);
      }
    }
    if (empty($errors)) {
      return true;
    }
    $error_codes = array_merge($error_codes, $errors);
    return false;
  }

  public function setDefaults() {
    $this->working = 0;
  }



  public function v_employeeId(&$error_codes = []) {
    if (!is_null($this->employeeId) && strlen($this->employeeId) > 0 && self::valid_int($this->employeeId, '3')) {
      return true;
    }
    $error_codes[] = 'employeeId';
    return false;
  }

  public function v_name(&$error_codes = []) {
    if (!is_null($this->name) && strlen($this->name) > 0 && self::valid_varchar($this->name, '200')) {
      return true;
    }
    $error_codes[] = 'name';
    return false;
  }

  public function v_scannerNumberId(&$error_codes = []) {
    if (!is_null($this->scannerNumberId) && strlen($this->scannerNumberId) > 0 && self::valid_int($this->scannerNumberId, '11')) {
      return true;
    }
    $error_codes[] = 'scannerNumberId';
    return false;
  }

  public function v_scannerSerialId(&$error_codes = []) {
    if (!is_null($this->scannerSerialId) && strlen($this->scannerSerialId) > 0 && self::valid_text($this->scannerSerialId)) {
      return true;
    }
    $error_codes[] = 'scannerSerialId';
    return false;
  }

  public function v_working(&$error_codes = []) {
    if (!is_null($this->working) && strlen($this->working) > 0 && self::valid_tinyint($this->working, '1')) {
      return true;
    }
    $error_codes[] = 'working';
    return false;
  }

  /**
  *@param array $conditions (required)
  *@param String $raw_sql (optional)
  *@return ProductionEmployees[]
  **/
  public static function find_all_like($conditions, $raw_sql = '') { return parent::generic_find_all_like($conditions, static::OM_CLASS_NAME, $raw_sql); }
  /**
  *@param array $conditions (required)
  *@param String $raw_sql (optional)
  *@return ProductionEmployees[]
  **/
  public static function find_all_by($conditions, $raw_sql = '') { return parent::generic_find_all_by($conditions, static::OM_CLASS_NAME, $raw_sql); }
  /**
  *@param String $raw_sql (optional)
  *@return ProductionEmployees[]
  **/
  public static function find_all($raw_sql = '') { return parent::generic_find_all(static::OM_CLASS_NAME, $raw_sql); }
  /**
  *@param array $conditions (required)
  *@param String $raw_sql (optional)
  *@return ProductionEmployees
  **/
  public static function find_by($conditions, $raw_sql = '') { return parent::generic_find_by($conditions, static::OM_CLASS_NAME, $raw_sql); }
  /**
  *@param String $id (required)
  *@param String $raw_sql (optional)
  *@return ProductionEmployees
  **/
  public static function find_by_id($id, $raw_sql = '') { return parent::generic_find_by_id($id, static::OM_CLASS_NAME, $raw_sql); }
  /**
  *@param array $conditions (required)
  *@param String $raw_sql (optional)
  *@return int
  **/
  public static function count_all_by($conditions, $raw_sql = '') { return parent::generic_count_all_by($conditions, static::OM_CLASS_NAME, $raw_sql); }
  /**
  *@param array $conditions (required)
  *@param String $raw_sql (optional)
  *@return bool
  **/
  public static function delete_by($conditions, $raw_sql = '') { return parent::generic_delete_by($conditions, static::OM_CLASS_NAME, $raw_sql); }
}