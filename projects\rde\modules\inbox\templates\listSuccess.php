<section class="title-bar">
  <h1>
    <?php echo Navigation::getItem(Navigation::getActivePageId())->getName() ?>
  </h1>
  <?php TemplateHelper::includePartial("_tabs.php","other") ?>
</section>

<div class="locations">

  Op deze pagina vindt u een overzicht van uw berichten.<br/><br/>

  <a href="<?php echo reconstructQuery(['action']) ?>action=edit" class="gsd-btn gsd-btn-primary"><?php echo __('Nieuw bericht toevoegen') ?></a>
  <br/><br/>
  <?php if(count($imessages)==0): ?>
    Er zijn nog geen berichten gevonden.
  <?php else: ?>
    <table class="default_table" style="width: auto;">
      <tr class="dataTableHeadingRow">
        <td style="width: 30px;">Online</td>
        <td style="width: 30px;">Homepage</td>
        <td style="width: 100px;">Datum</td>
        <td>Titel</td>
        <td>Mogelijke lezers</td>
        <td>Gelezen door</td>
        <td class="gsd-svg-icon-width-2">Acties</td>
      </tr>
      <?php foreach($imessages as $imessage): ?>
        <tr class="dataTableRow trhover">
          <td>
            <?php if($imessage->online==1): ?>
              <img src="/images/16_em_check.png" border="0"/>
            <?php endif; ?>
          </td>
          <td>
            <?php if($imessage->homepage==1): ?>
              <img src="/images/16_em_check.png" border="0"/>
            <?php endif; ?>
          </td>
          <td><?php echo $imessage->getDate() ?></td>
          <td><?php echo $imessage->subject ?></td>
          <td><?php echo $imessage->possible_readers ?></td>
          <td><?php echo $imessage->getReadBy() ?></td>
          <td>
            <?php echo BtnHelper::getRemove(reconstructQueryAdd(['pageId']).'action=delete&id='.$imessage->id) ?>
            <?php echo BtnHelper::getEdit(reconstructQueryAdd(['pageId']).'action=edit&id='.$imessage->id) ?>
          </td>
        </tr>
      <?php endforeach; ?>
    </table>
  <?php endif; ?>

</div>