<?php
class BaseContainerImg extends AppModel
{
  const DB_NAME = 'rde_b1mo';
  const TABLE_NAME = 'container_img';
  const OM_CLASS_NAME = 'ContainerImg';
  const columns = ['id', 'containerId', 'quotationId', 'filename', 'remark', 'insertDate', 'statusPhotoId'];
  const field_structure = [
    'id'                          => ['type' => 'int', 'length' => '11', 'null' => false],
    'containerId'                 => ['type' => 'int', 'length' => '11', 'null' => true],
    'quotationId'                 => ['type' => 'int', 'length' => '11', 'null' => true],
    'filename'                    => ['type' => 'varchar', 'length' => '255', 'null' => false],
    'remark'                      => ['type' => 'text', 'length' => '', 'null' => true],
    'insertDate'                  => ['type' => 'datetime', 'length' => '', 'null' => false],
    'statusPhotoId'               => ['type' => 'int', 'length' => '11', 'null' => false],
  ];

  protected static $primary_key = ['id'];
  protected $auto_increment = 'id';

  public $id, $containerId, $quotationId, $filename, $remark, $insertDate, $statusPhotoId;

  function __construct($obj_arr = [], &$error_codes = []) {
    parent::__construct();
    $this->setDefaults();
    if (!empty($obj_arr)) {
      $this->from_array($obj_arr);
      $this->valid_required_fields($error_codes);
    }
  }

  public function valid(&$error_codes = []) {
    $errors = [];
    foreach(self::columns as $column) {
      if ($column != $this->auto_increment)
      {
        call_user_func_array([$this, 'v_' . $column], [&$errors]);
      }
    }
    if(empty($errors)) {
      return true;
    }
    $error_codes = array_merge($error_codes, $errors);
    return false;
  }

  public function valid_required_fields(&$error_codes = []) {
    $errors = [];
    foreach (self::columns as $column) {
      $function_name = "v_$column";
      if ($column!=$this->auto_increment) {
        call_user_func_array([$this, $function_name], [&$errors]);
      }
    }
    if (empty($errors)) {
      return true;
    }
    $error_codes = array_merge($error_codes, $errors);
    return false;
  }

  public function setDefaults() {
  }



  public function v_id(&$error_codes = []) {
    if (!is_null($this->id) && strlen($this->id) > 0 && self::valid_int($this->id, '11')) {
      return true;
    }
    $error_codes[] = 'id';
    return false;
  }

  public function v_containerId(&$error_codes = []) {
    if (is_null($this->containerId) || strlen($this->containerId) == 0 || self::valid_int($this->containerId, '11')) {
      return true;
    }
    $error_codes[] = 'containerId';
    return false;
  }

  public function v_quotationId(&$error_codes = []) {
    if (is_null($this->quotationId) || strlen($this->quotationId) == 0 || self::valid_int($this->quotationId, '11')) {
      return true;
    }
    $error_codes[] = 'quotationId';
    return false;
  }

  public function v_filename(&$error_codes = []) {
    if (!is_null($this->filename) && strlen($this->filename) > 0 && self::valid_varchar($this->filename, '255')) {
      return true;
    }
    $error_codes[] = 'filename';
    return false;
  }

  public function v_remark(&$error_codes = []) {
    if (is_null($this->remark) || strlen($this->remark) == 0 || self::valid_text($this->remark)) {
      return true;
    }
    $error_codes[] = 'remark';
    return false;
  }

  public function v_insertDate(&$error_codes = []) {
    if (!is_null($this->insertDate) && strlen($this->insertDate) > 0 && self::valid_datetime($this->insertDate)) {
      return true;
    }
    $error_codes[] = 'insertDate';
    return false;
  }

  public function v_statusPhotoId(&$error_codes = []) {
    if (!is_null($this->statusPhotoId) && strlen($this->statusPhotoId) > 0 && self::valid_int($this->statusPhotoId, '11')) {
      return true;
    }
    $error_codes[] = 'statusPhotoId';
    return false;
  }

  /**
  *@param array $conditions (required)
  *@param String $raw_sql (optional)
  *@return ContainerImg[]
  **/
  public static function find_all_like($conditions, $raw_sql = '') { return parent::generic_find_all_like($conditions, static::OM_CLASS_NAME, $raw_sql); }
  /**
  *@param array $conditions (required)
  *@param String $raw_sql (optional)
  *@return ContainerImg[]
  **/
  public static function find_all_by($conditions, $raw_sql = '') { return parent::generic_find_all_by($conditions, static::OM_CLASS_NAME, $raw_sql); }
  /**
  *@param String $raw_sql (optional)
  *@return ContainerImg[]
  **/
  public static function find_all($raw_sql = '') { return parent::generic_find_all(static::OM_CLASS_NAME, $raw_sql); }
  /**
  *@param array $conditions (required)
  *@param String $raw_sql (optional)
  *@return ContainerImg
  **/
  public static function find_by($conditions, $raw_sql = '') { return parent::generic_find_by($conditions, static::OM_CLASS_NAME, $raw_sql); }
  /**
  *@param String $id (required)
  *@param String $raw_sql (optional)
  *@return ContainerImg
  **/
  public static function find_by_id($id, $raw_sql = '') { return parent::generic_find_by_id($id, static::OM_CLASS_NAME, $raw_sql); }
  /**
  *@param array $conditions (required)
  *@param String $raw_sql (optional)
  *@return int
  **/
  public static function count_all_by($conditions, $raw_sql = '') { return parent::generic_count_all_by($conditions, static::OM_CLASS_NAME, $raw_sql); }
  /**
  *@param array $conditions (required)
  *@param String $raw_sql (optional)
  *@return bool
  **/
  public static function delete_by($conditions, $raw_sql = '') { return parent::generic_delete_by($conditions, static::OM_CLASS_NAME, $raw_sql); }
}