<?php

  AppModel::loadBaseClass('BaseCrmAddresses');

  class CrmAddressesModel extends BaseCrmAddresses {

    const TYPE_VISIT = 'visit';
    const TYPE_POST = 'post';
    const TYPE_INVOICE = 'invoice';
    const TYPE_DELIVERY = 'delivery';
    const TYPE_MAP = 'map';
    const TYPES = [
      self::TYPE_VISIT    => 'Bezoekadres',
      self::TYPE_POST     => 'Postadres',
      self::TYPE_INVOICE  => 'Factuuradres',
      self::TYPE_DELIVERY => 'Werfadres',
      self::TYPE_MAP      => 'Kaartadres',
    ];

    /**
     * @return string
     */
    public function getAddress(): string {
      $str = "";
      if ($this->street != "") {
        $str .= $this->street;
      }
      if ($this->nr != "") {
        $str .= " " . $this->nr;
      }
      if ($this->extension != "") {
        $str .= " " . $this->extension;
      }
      return trim($str);
    }

    public function getAddressFormatted($break = "\n") {
      $str = $this->getAddress();
      $str .= $break;
      if ($this->zipcode != "") {
        $str .= $this->zipcode;
      }
      if ($this->domestic != "") {
        $str .= " " . $this->domestic;
      }
      return trim($str);
    }

    /**
     * For display only, so escaping single quotes to html codes
     * @param $nl
     * @param $showcountry
     * @return array|string|string[]
     */
    public function getAddressFormattedHeader($nl = '<br/>', $showcountry = true): string {
      $string = "";
      if ($this->street != null) {
        if ($this->street . $this->nr . $this->extension != "") {
          $string .= trim($this->street . " " . $this->nr . " " . $this->extension);
        }
        if ($this->zipcode . $this->domestic != "") {
          $string .= $nl;
          $string .= $this->zipcode . " " . $this->domestic;
        }
        if ($showcountry && $this->country != "") {
          $string .= $nl;
          $string .= Country::getCountryByCode(strtolower($this->country))->name_nl;
        }
      }
      return displayAsHtml($string);
    }

    /**
     * @param Quotations $quote
     * @param string $type
     *
     * @return  CrmAddresses
     */
    public static function hasAddress($quote, $type) {
      return CrmAddresses::find_by(["nr" => $quote->nr, "zipcode" => $quote->zipcode, "type" => $type]);
    }

    /**
     * @param Quotations $quote
     * @return  CrmAddresses
     */
    public static function getAddressObj($quote) {
      $address = CrmAddresses::hasAddress($quote, 'visit');
      if (!$address) {
        $address = CrmAddresses::hasAddress($quote, 'delivery');
      }
      if (!$address) {
        $address = CrmAddresses::hasAddress($quote, 'map');
      }
      if (!$address) { //dan maar aanmaken
        $address = new CrmAddresses();
        $address->companyId = $_SESSION['userObject']->companyId;
        $address->type = CrmAddresses::TYPE_MAP;
        $address->street = $quote->street;
        $address->nr = $quote->nr;
        $address->extension = $quote->ext;
        $address->zipcode = $quote->zipcode;
        $address->domestic = $quote->domestic;
        $address->country = $quote->country;
        $address->date = date("Y-m-d H:i:s");
      }
      return $address;
    }

    public function save(&$errors = []) {
      if ($this->from_db == false) {
        $this->date = date('Y-m-d H:i:s');
      }
      return parent::save($errors);
    }

  }