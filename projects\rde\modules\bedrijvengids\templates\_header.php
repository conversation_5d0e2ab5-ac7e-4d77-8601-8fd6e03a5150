<ul id="tabnav" class="nav nav-tabs">
  <?php echo Navigation::writeNavigationNodes(Navigation::getItem('M_BEDRIJVENGIDS_LIST')->getChildren()); ?>
</ul>
<table class="default_table">
  <tr class="dataTableHeadingRow">
    <td>Naam</td>
    <td>Adres</td>
    <td>Contactpersoon</td>
  </tr>
  <tr class="dataTableRow">
    <?php if ($sandboxuser): ?>
      <td><?php echo $sandboxuser->companyName ?></td>
      <td><?php echo $sandboxuser->getAddressFormatted() ?></td>
      <td>
        <?php echo $sandboxuser->getNaam() ?><br/>
        <?php echo $sandboxuser->phone ?>, <?php echo $sandboxuser->mobile ?><br/>
        <?php echo $sandboxuser->email ?><br/>
      </td>
    <?php else: ?>
    <td><?php echo $company->name ?></td>
    <td>
      <?php if($address) echo $address->getAddressFormattedHeader() ?>
    </td>
    <?php if (!empty($persons)): ?>
      <td>
        <?php echo $persons[0]->firstName . " " . $persons[0]->lastName ?><br/>
        <?php echo $persons[0]->phone ?>, <?php echo $persons[0]->mobile ?><br/>
        <?php echo $persons[0]->email ?><br/>
      </td>
    <?php endif; ?>
  </tr>
  <?php endif; ?>
</table>
<br/>
