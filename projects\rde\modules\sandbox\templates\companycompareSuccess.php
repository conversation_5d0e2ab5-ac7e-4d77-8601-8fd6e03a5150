<?php
  TemplateHelper::includePartial('_tabs.php', 'sandbox');
  include("_header.php")
?>

<h3>2. <PERSON><PERSON><PERSON><PERSON> verge<PERSON> en aanvullen</h3>

  <?php writeErrors(array_merge($form_company->getErrors(),$form_visit->getErrors()), true); ?>

  <form method="post" name="form" id="form" class="edit-form">

    <table class="default_table">
      <tr class="dataTableHeadingRow">
        <td>Bedrijfsgegevens</td>
        <td>Doel</td>
        <td><a href="#" class="copy_all fa fa-chevron-left" title="Alles kopieëren"></a></td>
        <td>Bron</td>
      </tr>
      <?php foreach ($form_company->getElements() as $element): ?>
        <tr class="dataTableRow">
          <td class="head"><?php echo $element->getLabel() ?> <?php if($element->isRequired()): ?><span class="asterisk">*</span><?php endif; ?></td>
          <td><?php $element->render() ?></td>
          <td>
            <?php if(!$form_company_source->getElement($element->getId())->hasClass("inputhidden")): ?>
            <a href="#" class="copy fa fa-chevron-left <?php if($form_company->getElement($element->getId())->getValue()!=$form_company_source->getElement($element->getId())->getValue()) echo 'copy_diff' ?>" title="Kopieër regel"></a>
            <?php endif;  ?>
          </td>
          <td>
            <?php if(!$form_company_source->getElement($element->getId())->hasClass("inputhidden")): ?>
              <?php $form_company_source->getElement($element->getId())->render() ?>
            <?php endif; ?>
          </td>
        </tr>
      <?php endforeach; ?>
      <tr class="dataTableHeadingRow">
        <td colspan="4">Bezoek adres</td>
      </tr>
      <?php foreach ($form_visit->getElements() as $element): ?>
        <tr class="dataTableRow">
          <td class="head"><?php echo $element->getLabel() ?> <?php if($element->isRequired()): ?><span class="asterisk">*</span><?php endif; ?></td>
          <td><?php $element->render() ?></td>
          <td><a href="#" class="copy fa fa-chevron-left <?php if($form_visit->getElement($element->getId())->getValue()!=$form_visit_source->getElement($element->getId())->getValue()) echo 'copy_diff' ?>" title="Kopieër regel"></a></td>
          <td><?php $form_visit_source->getElement($element->getId())->render() ?></td>
        </tr>
      <?php endforeach; ?>
      <tr class="dataTableHeadingRow">
        <td colspan="4">Facturatie</td>
      </tr>
      <?php foreach ($form_invoiceparty->getElements() as $element): ?>
        <tr class="dataTableRow">
          <td class="head"><?php echo $element->getLabel() ?> <?php if($element->isRequired()): ?><span class="asterisk">*</span><?php endif; ?></td>
          <td><?php $element->render() ?></td>
          <td><a href="#" class="copy fa fa-chevron-left <?php if($form_invoiceparty->getElement($element->getId())->getValue()!=$form_invoiceparty_source->getElement($element->getId())->getValue()) echo 'copy_diff' ?>" title="Kopieër regel"></a></td>
          <td><?php $form_invoiceparty_source->getElement($element->getId())->render() ?></td>
        </tr>
      <?php endforeach; ?>
    </table>

    <input type="submit" name="cancel" value="Vorige stap"/>
    <input type="submit" name="go" value="Opslaan en volgende stap" class="gsd-btn gsd-btn-primary"/>

  </form>

  <script type="text/javascript">
    $(document).ready(function() {

      $(".readonly").attr("readonly", true);

      $(".copy").click(function(event) {
        var from = $(this).parent().next().find("input,select,textarea");
        var to = $(this).parent().prev().find("input,select,textarea");
        to.each(function(index) {
          if($(this).attr('type')=='checkbox') {
            $(this).prop('checked', $(from[index]).is(':checked'));
            $("#post_equal").change();
            $("#exists").change();
          }
          else {
            $(this).val($(from[index]).val());
          }
        });
        $(this).removeClass("copy_diff");
        event.preventDefault();
      });

      $(".copy_all").click(function(event) {
        $(".copy").each(function() {
          $(this).click();
        });
        event.preventDefault();
      });

      $(".searchkvk").click(function(event) {
        zoekinkvk($("#tradeRegNo").val());
        event.preventDefault();
      });

    });
  </script>
