<section class="title-bar">
  <h1>
    <?php echo Navigation::getItem(Navigation::getActivePageId())->getName() ?>
  </h1>
  <?php TemplateHelper::includePartial("_tabs.php","other") ?>
</section>

<link rel="stylesheet" type="text/css" href="/gsdfw/includes/fileuploader/client/fileuploader.css"/>
<script src="/gsdfw/includes/fileuploader/client/fileuploader.js" type="text/javascript"></script>
<h3>Overzicht container foto's</h3>
<form action="<?php echo reconstructQuery() ?>" method="post" enctype="multipart/form-data">
  <div class="box">
    <input type="text" value="<?php echo $_SESSION["img_search"] ?>" name="img_search" id="img_search" placeholder="Zoeken offertenummer..."/>
    Foto gemaakt tussen <?php echo getDateSelector("img_from", $_SESSION["img_from"]) ?> en <?php echo getDateSelector("img_to", $_SESSION["img_to"]) ?>
    <input type="submit" value="Zoeken" name="go" id="go"/>
    <a href="" class="gsd-btn gsd-btn-secondary" id="toggle_delete">Verwijderen tonen/verbergen</a>
    <input type="submit" value="Geselecteerde beelden verwijderen" id="delete_go" name="delete_go" style="display: none;"/>
  </div>

  <div id="pincontainer">
    <?php include("_images.php") ?>
    <div class="infinite-loading" id="infinite-loading" style="display:none;">&nbsp;</div>
  </div>

</form>


<script type="text/javascript">

  gsdModalImage = new GsdModal("image-modal-2");
  gsdModalImage.init();

  $(document).ready(function () {

    var pincontainer = $("#pincontainer");

    new SimpleLightbox(".allimagegallery", {
      fileExt: false,
    });

    $(".opendialog").on("click", function (e) {
      e.preventDefault();
      gsdModalImage.open($(this).attr("href"), $(this).attr("title"));
    });

    //check if all images are loaded
    pincontainer.imagesLoaded(function () {
      var height = pincontainer.height();

      //INFINITE SCROLL
      var $footer = $('.footermenu'),
        opts = {
          offset: '120%',
          onlyOnScroll: false
        };

      $footer.waypoint(function (event, direction) {
        $footer.waypoint('disable');

        var $more, $newMore;

        $.get($('.infinite-more-link:last').attr('href'), function (data) {

          $('.infinite-more-link:last').replaceWith(data);
          //pincontainer.append($data);
          pincontainer.imagesLoaded(function () {

            $footer.waypoint('enable');
            if ($("#delete_go").is(":visible")) {
              $(".ch_delete").show();
              $("#delete_go").show();
            }
            else {
              $(".ch_delete").hide();
              $("#delete_go").hide();
            }

          });
        }, opts);
      });

      $("#toggle_delete").click(function (e) {
        e.preventDefault();
        if ($("#delete_go").is(":visible")) {
          $(".ch_delete").hide();
          $("#delete_go").hide();
        }
        else {
          $(".ch_delete").show();
          $("#delete_go").show();
        }
      });
      $("#delete_go").click(function (e) {
        if (!confirm("Weet u zeker dat u de geselecteerde beelden wilt verwijderen?")) {
          e.preventDefault();
        }
      });

    });
  });

</script>
