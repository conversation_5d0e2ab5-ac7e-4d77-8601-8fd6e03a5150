<?php
class BaseOrderElementWindowsill extends AppModel
{
  const DB_NAME = 'rde_b1mo';
  const TABLE_NAME = 'order_element_windowsill';
  const OM_CLASS_NAME = 'OrderElementWindowsill';
  const columns = ['id', 'element_id', 'windowsill_id', 'x1', 'x2', 'x3', 'x4', 'x5', 'x6', 'remark_cust'];
  const field_structure = [
    'id'                          => ['type' => 'mediumint', 'length' => '8', 'null' => false],
    'element_id'                  => ['type' => 'int', 'length' => '12', 'null' => true],
    'windowsill_id'               => ['type' => 'int', 'length' => '8', 'null' => true],
    'x1'                          => ['type' => 'decimal', 'length' => '12,2', 'null' => true],
    'x2'                          => ['type' => 'decimal', 'length' => '12,2', 'null' => true],
    'x3'                          => ['type' => 'decimal', 'length' => '12,2', 'null' => true],
    'x4'                          => ['type' => 'decimal', 'length' => '12,2', 'null' => true],
    'x5'                          => ['type' => 'decimal', 'length' => '12,2', 'null' => true],
    'x6'                          => ['type' => 'decimal', 'length' => '12,2', 'null' => true],
    'remark_cust'                 => ['type' => 'text', 'length' => '', 'null' => true],
  ];

  protected static $primary_key = ['id'];
  protected $auto_increment = 'id';

  public $id, $element_id, $windowsill_id, $x1, $x2, $x3, $x4, $x5, $x6, $remark_cust;

  function __construct($obj_arr = [], &$error_codes = []) {
    parent::__construct();
    $this->setDefaults();
    if (!empty($obj_arr)) {
      $this->from_array($obj_arr);
      $this->valid_required_fields($error_codes);
    }
  }

  /**
  * Set default object values
  **/
  public function setDefaults() {
  }

  /**
   * @param array $conditions (required)
   * @param string $raw_sql (optional)
   * @return OrderElementWindowsill[]
   **/
  public static function find_all_like($conditions, $raw_sql = '') {
    return parent::generic_find_all_like($conditions, static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param array $conditions (required)
   * @param string $raw_sql (optional)
   * @return OrderElementWindowsill[]
   **/
  public static function find_all_by($conditions, $raw_sql = '') {
    return parent::generic_find_all_by($conditions, static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param string $raw_sql (optional)
   * @return OrderElementWindowsill[]
   **/
  public static function find_all($raw_sql = '') {
    return parent::generic_find_all(static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param array $conditions (required)
   * @param string $raw_sql (optional)
   * @return OrderElementWindowsill
   **/
  public static function find_by($conditions, $raw_sql = '') {
    return parent::generic_find_by($conditions, static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param string $id (required)
   * @param string $raw_sql (optional)
   * @return OrderElementWindowsill
   **/
  public static function find_by_id($id, $raw_sql = '') {
    return parent::generic_find_by_id($id, static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param array $conditions (required)
   * @param string $raw_sql (optional)
   * @return int
   **/
  public static function count_all_by($conditions, $raw_sql = '') {
    return parent::generic_count_all_by($conditions, static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param array $conditions (required)
   * @param string $raw_sql (optional)
   * @return bool
   **/
  public static function delete_by($conditions, $raw_sql = '') {
    return parent::generic_delete_by($conditions, static::OM_CLASS_NAME, $raw_sql);
  }

}