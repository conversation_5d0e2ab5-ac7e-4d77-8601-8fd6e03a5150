<?php //@TODO styling verbeteren ?>

<div class="box">
  <form method="post" action="<?php echo reconstructQueryAdd() ?>">
    <select name="years" id="years">
      <option value="">Filter op jaar...</option>
      <?php for($year=intval(date("Y"))+1;$year>=2011;$year--): ?>
        <option value="<?php echo $year ?>" <?php writeIfSelectedVal($_SESSION['years'], $year) ?>><?php echo $year ?></option>
      <?php endfor; ?>
    </select>
    <input type="submit" name="go" id="go" value="Zoeken" />

  </form>
</div>

<div class="default_table default_table_center quotation_table">
  <div style="display: flex; justify-content: space-between; height: 40px; align-items: center; background-color: #f6f9fd; font-weight: bold; padding: 5px" >
    <div class="invoice-row">Dropdown</div>
    <div class="invoice-row">Datum</div>
    <div class="invoice-row">Aantal offertes</div>
    <div class="invoice-row">Prijs</div>
    <div class="invoice-row">PDF</div>
  </div>
  <?php
    /** @var BretiInvoice $breti_invoice */
    foreach ($breti_invoices as $key => $breti_invoice): ?>
      <div>
        <div style="display: flex; justify-content: space-between; height: 35px; align-items: center; padding: 5px;" class="trhover dataTableRow" id="toggle-div-<?php echo $key ?>">
          <div class="invoice-row"><p class="arrrow" id="rotate-<?php echo $key ?>">›</p></div>
          <div class="invoice-row"><?php echo DateTimeHelper::convertFormat($breti_invoice->breti_invoice_date, 'Y-m-d H:i:s', 'd-m-Y') ?></div>
          <div class="invoice-row"><?php echo $breti_invoice->count ?></div>
          <div class="invoice-row"><?php echo StringHelper::asMoney($breti_invoice->total_project_value) ?></div>
          <div class="invoice-row"><?php echo BtnHelper::getPrintPDF('?action=downloadbretipdf&quotation_ids=' . implode(',' , $breti_invoice->quotation_ids) . '&nr=' . $breti_invoice->breti_invoice_number, __('Bekijk pdf'), '_blank') ?></div>
        </div>
        <div style=" justify-content: space-between; height: unset" id="quotation_table_<?php echo $key ?>" class="dataTableRow trhover visible">
          <table style="width: 100%">
            <tr class="dataTableHeadingRow">
              <td>Project naam</td>
              <td>Offerte nummer</td>
              <td>Merk</td>
              <td>Model</td>
              <td>Meters</td>
              <td>Opmerkingen</td>
              <td>PDF</td>
            </tr>
          <?php foreach ($breti_invoice->breti_quotations as $breti_quotation): ?>
            <tr class="dataTableRow trhover">
              <td><?php echo $breti_quotation->quotation->projectName ?></td>
              <td><?php echo $breti_quotation->quotation->getQuotationNumberFull() ?></td>
              <td><?php echo $breti_quotation->quotation->brands[$breti_quotation->quotation->brandId]->name ?></td>
              <td><?php echo $breti_quotation->quotation->size->name ?></td>
              <td><?php echo $breti_quotation->quotation->meters ?></td>
              <td><?php echo $breti_quotation->quotation->productionNotes ?></td>
              <td><?php echo BtnHelper::getPrintPDF('?action=downloadpdf&id=' . $breti_quotation->quotation->quotationId, __('Bekijk pdf'), '_blank') ?></td>
            </tr>
          <?php endforeach; ?>
          </table>
        </div>
      </div>

      <script>
        $(document).ready(function () {
          $("#toggle-div-<?php echo $key ?>").on('click', function (event) {
            $('#quotation_table_<?php echo $key ?>').toggleClass("visible");
          });
        });

        $("#toggle-div-<?php echo $key ?>").click(function () {
          $("#rotate-<?php echo $key ?>").toggleClass("down");
        })
      </script>
    <?php endforeach; ?>

  <h3>Totaal bedrag: €<?php echo $invoice_total; ?></h3>
</div>

<style>
 .visible {
    display: none;
  }

 .invoice-row {
   width: 100px;
   text-align: center;
 }

 .arrrow {
   font-size: 2em;
   -moz-transition: all .2s linear;
   -webkit-transition: all .2s linear;
   transition: all .2s linear;
   user-select: none;
 }

 .down {
   -moz-transform:rotate(90deg);
   -webkit-transform:rotate(90deg);
   transform-origin: center;
   transform:rotate(90deg);
 }
</style>