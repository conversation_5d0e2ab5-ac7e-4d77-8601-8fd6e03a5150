<div class="row">
  <div class="col6 col12-xs">
    <div class="contenttxt">
      <?php echo process_text($page->content->content1); ?>
    </div>
  </div>
  <div class="col6 col12-xs contenttxt">
    <h2>Modellenconfigurator</h2>
    <p>Hier kunt u heel makkelijk de gewenste merk, kleur, model en eindsteen kiezen voor uw keramische raamdorpels.
      Kunt u de gewenste model in combinatie met de kleur niet selecteren neem dan <a href="/contact">contact</a> op voor de mogelijkheden.
      <br/>
      <b>Let op:</b> onderstaande modelconfigurator geeft alleen onze keramische raamdorpels weer.
      Wanneer u een account aanmaakt en vrijblijvend de offerte wizard invult, ziet u ook onze overige producten zoals beton en natuursteen.
    </p>

    <div id="models" class="wizard" v-cloak="">
      <form method="post">

        <div class="form-row" :class="{ disabled: !is_active_offerteVariant }" style="display: none;">
          <label class="col3 col-form-label"><?php echo __('Product') ?></label>
          <div class="col1">
            <div class="input-validation-icon" v-if="!is_valid && form_submit_error_fields.hasOwnProperty('offerteVariant')">
              <img src="<?php echo $site->getTemplateUrl() ?>images/exclamation-circle.svg" alt="let op"  :title="form_submit_error_fields['offerteVariant']" width="20">
            </div>
            <div v-if="is_valid.offerteVariant && is_active_offerteVariant" class="input-validation-icon">
              <img src="<?php echo $site->getTemplateUrl() ?>images/check_green.svg" alt="afgerond" width="20" />
            </div>
          </div>
          <div class="col7">
            <div class="select">
              <select name="brandId" class="brandId" v-model="quotation.offerteVariant" :disabled="!is_active_offerteVariant" :class="{ 'has-error': !quotation.brandId && form_submit_error_fields.hasOwnProperty('offerteVariant') }">
                <option value="">
                  <?php echo __('Kies uw product') ?>...
                </option>
                <option v-for="(offerteVariant, index) in offerteVariants" :value="index">
                  {{ offerteVariant }}
                </option>
              </select>
            </div>
          </div>
          <div class="col1">
            <a class="question-mark qtipa fa fa-info-circle" v-bind:title="help_offerteVariant"></a>
          </div>
        </div>

        <div class="form-row" :class="{ disabled: !is_active_brandId }">
          <label class="col3 col-form-label"><?php echo __('Merk') ?></label>
          <div class="col1">
            <div class="input-validation-icon" v-if="!is_valid && form_submit_error_fields.hasOwnProperty('brandId')">
              <img src="<?php echo $site->getTemplateUrl() ?>images/exclamation-circle.svg" alt="let op"  :title="form_submit_error_fields['brandId']" width="20">
            </div>
            <div v-if="is_valid.brandId && is_active_brandId" class="input-validation-icon">
              <img src="<?php echo $site->getTemplateUrl() ?>images/check_green.svg" alt="afgerond" width="20" />
            </div>
          </div>
          <div class="col7">
            <div class="select">
              <select name="brandId" class="brandId" v-model="quotation.brandId" :disabled="!is_active_brandId" :class="{ 'has-error': !quotation.brandId && form_submit_error_fields.hasOwnProperty('brandId') }">
                <option value="">
                  <?php echo __('Kies een merk') ?>...
                </option>
                <option v-for="brand in possible_brands" :value="brand.brandId">
                  {{ brand.name }}
                </option>
              </select>
            </div>
          </div>
          <div class="col1">
            <a class="question-mark qtipa fa fa-info-circle" v-bind:title="help_brandId"></a>
          </div>
        </div>

        <div class="form-row" :class="{ disabled: !is_active_colorId }">
          <label class="col3 col-form-label"><?php echo __('Kleur') ?></label>
          <div class="col1">
            <div class="input-validation-icon" v-if="!is_valid && form_submit_error_fields.hasOwnProperty('colorId')">
              <img src="<?php echo $site->getTemplateUrl() ?>images/exclamation-circle.svg" alt="let op"  :title="form_submit_error_fields['brandId']" width="20">
            </div>
            <div v-if="is_valid.colorId && is_active_colorId" class="input-validation-icon">
              <img src="<?php echo $site->getTemplateUrl() ?>images/check_green.svg" alt="afgerond" width="20" />
            </div>
          </div>
          <div class="col7">
            <div class="select">
              <select name="colorId" class="colorId" v-model="quotation.colorId" :disabled="!is_active_colorId" :class="{ 'has-error': !quotation.colorId && form_submit_error_fields.hasOwnProperty('colorId') }">
                <option value="">
                  <?php echo __('Kies een kleur') ?>...
                </option>
                <optgroup v-for="(group, name) in possible_colors_grouped" :label="name">
                  <option v-for="color in group" :value="color.colorId">
                    {{ color.name }} {{ color.short!=""?" - "+color.short:"" }}
                  </option>
                </optgroup>
              </select>
            </div>
          </div>
          <div class="col1">
            <a class="question-mark qtipa fa fa-info-circle" v-bind:title="help_colorId"></a>
          </div>
        </div>


        <div class="form-row" :class="{ disabled: !is_active_sizeId }">
          <label class="col3 col-form-label"><?php echo __('Model') ?></label>
          <div class="col1">
            <div class="input-validation-icon" v-if="!is_valid && form_submit_error_fields.hasOwnProperty('sizeId')">
              <img src="<?php echo $site->getTemplateUrl() ?>images/exclamation-circle.svg" alt="let op"  :title="form_submit_error_fields['brandId']" width="20">
            </div>
            <div v-if="is_valid.sizeId && is_active_sizeId" class="input-validation-icon">
              <img src="<?php echo $site->getTemplateUrl() ?>images/check_green.svg" alt="afgerond" width="20" />
            </div>
          </div>
          <div class="col7">
            <div class="select">
              <select name="sizeId" class="sizeId" v-model="quotation.sizeId" :disabled="!is_active_sizeId" :class="{ 'has-error': !quotation.sizeId && form_submit_error_fields.hasOwnProperty('sizeId') }">
                <option value="">
                  <?php echo __('Kies een model') ?>...
                </option>
                <optgroup v-for="(group, name) in possible_sizes" :label="name">
                  <option v-for="size in group" :value="size.sizeId">
                    {{ size.name}}
                  </option>
                </optgroup>
              </select>
            </div>
          </div>
          <div class="col1">
            <a href="/prefab-raamdorpels/neggemaattabel" target="_blank" class="question-mark qtipa fa fa-info-circle" v-bind:title="help_sizeId"></a>
          </div>
        </div>

        <div class="form-row" :class="{ disabled: !is_active_endstone }">
          <label class="col3 col-form-label"><?php echo __('Eindsteen') ?></label>
          <div class="col1">
            <div class="input-validation-icon" v-if="!is_valid && form_submit_error_fields.hasOwnProperty('endstone')">
              <img src="<?php echo $site->getTemplateUrl() ?>images/exclamation-circle.svg" alt="let op"  :title="form_submit_error_fields['endstone']" width="20">
            </div>
            <div v-if="is_valid.endstone && is_active_endstone" class="input-validation-icon">
              <img src="<?php echo $site->getTemplateUrl() ?>images/check_green.svg" alt="afgerond" width="20" />
            </div>
          </div>
          <div class="col7">
            <div class="select">
              <select name="endstone" class="endstone" v-model="quotation.endstone" :disabled="!is_active_endstone" :class="{ 'has-error': !quotation.endstone && form_submit_error_fields.hasOwnProperty('endstone') }">
                <option value="">
                  <?php echo __('Kies een elementeinde') ?>...
                </option>
                <option v-for="endstonetype in possible_endstonetypes" :value="endstonetype.value">
                  {{ endstonetype.text }}
                </option>
              </select>
            </div>
          </div>
          <div class="col1">
            <a class="question-mark qtipa fa fa-info-circle" v-bind:title="help_endstone"></a>
          </div>
        </div>

        <div class="form-row"  v-if="all_valid" >
          <div class="col12">
            <img v-bind:src="stoneimage" class="stoneimage" />
          </div>
          <div class="col1">
          </div>
        </div>

      </form>

    </div>

  </div>
</div>




<script type="text/javascript">

  blockEnterSubmit();

  var form_submit_error_fields = [];
  var quotation = parseJson('<?php echo StringHelper::escapeJson(json_encode($quotation)) ?>');
  var offerteVariants = [];
  var brands = <?php echo json_encode($brands) ?>;
  var colors = parseJson('<?php echo json_encode(array_values($colors)) ?>');
  var sizes = <?php echo json_encode($sizes) ?>;
  var firsttime = true;

  var app = Vue.createApp({
    data() {
      return {
        is_valid: {},
        all_valid: false,
        is_active: {},
        form_submit_error_fields: form_submit_error_fields,
        input_errors: {},

        brands: brands,
        offerteVariants: offerteVariants,
        colors: colors,
        sizes: sizes,

        quotation: quotation,
        possible_brands: [],
        color: false,
        stone: false,
        stoneimage: '',

        is_active_offerteVariant: true,
        is_active_brandId: false,
        is_active_colorId: false,
        is_active_sizeId: false,
        is_active_endstone: false,

        help_offerteVariant: 'Selecteer uw product.',
        help_brandId: 'Selecteer uw merk.',
        help_colorId: 'St. Joris en Wienerberger kennen vele verschillende kleuren. Voor speciale kleuren kunt u altijd contact opnemen.',
        help_sizeId: 'Er zijn maar liefst 11 verschillende modellen waardoor het moeilijk is om het juiste model te kiezen. Klik dit informatie bolletje om meer informatie over de verschillende maten te verkijgen.',
        help_endstone: 'Eindstenen zijn raamdorpelstenen met opstaande zijkantjes tegen de neggekant aan. Dit is ter voorkoming van okselvlekken op de gevel langs het kozijn. Wanneer u de optie "Nee (geglazuurde zijkantjes)" kiest, zijn de zijkantjes ter plaatse van de neggekanten geglazuurd.',
      }
    },
    mounted() {

      // because the variables can have a value (from session or post) we execute their related functions
      // for a proper validated setup
      this.offerteVariantChanged();
      this.brandIdChanged();
      this.colorIdChanged();
      this.sizeIdChanged();
      this.endstoneChanged();
      firsttime = false;
    },
    watch: {
      'quotation.offerteVariant': function() {
        this.offerteVariantChanged();
      },
      'quotation.brandId': function() {
        this.brandIdChanged();
      },
      'quotation.colorId': function() {
        this.colorIdChanged();
      },
      'quotation.sizeId': function() {
        this.sizeIdChanged();
      },
      'quotation.endstone': function() {
        this.endstoneChanged();
      },
    },
    computed: {
      possible_colors_grouped: function () {
        var filtered_array = {
          "Ongeglazuurd":[],
          "Geglazuurd - standaard":[],
          "Geglazuurd - speciaal":[],
        };
        for (var i = 0; i < this.colors.length; i++) {
          if (this.colors[i].brandId == this.quotation.brandId) {
            var group;
            if(this.colors[i].glaced) {
              if(this.colors[i].common) {
                group = filtered_array["Geglazuurd - standaard"];
              }
              else {
                group = filtered_array["Geglazuurd - speciaal"];
              }
            }
            else {
              group = filtered_array["Ongeglazuurd"];
            }

            group.push(this.colors[i])
          }
        }
        if(filtered_array["Ongeglazuurd"].length ==0) delete filtered_array["Ongeglazuurd"];
        if(filtered_array["Geglazuurd - standaard"].length ==0) delete filtered_array["Geglazuurd - standaard"];
        if(filtered_array["Geglazuurd - speciaal"].length ==0) delete filtered_array["Geglazuurd - speciaal"];
        return filtered_array;
      },
      possible_sizes: function () {

        var filtered_array = {
          "Standaard maten":[],
          "Speciale maten":[],
        };
        var ids = [];
        for (var i = 0; i < this.sizes.length; i++) {
          if (this.sizes[i].brandId == this.quotation.brandId && this.sizes[i].stone.colorId == this.quotation.colorId) {
            var group;
            if (this.sizes[i].common=="true") {
              group = filtered_array["Standaard maten"];
            }
            else {
              group = filtered_array["Speciale maten"];
            }

            if (ids.indexOf(this.sizes[i].sizeId) === -1) {
              ids.push(this.sizes[i].sizeId);
              group.push(this.sizes[i]);
            }
          }
        }
        if(filtered_array["Standaard maten"].length ==0) delete filtered_array["Standaard maten"];
        if(filtered_array["Speciale maten"].length ==0) delete filtered_array["Speciale maten"];
        return filtered_array;
      },
      possible_endstonetypes: function () {
        var filtered_array = [];
        var hasnormal = false;
        var hasgrooves = false;
        for (var i = 0; i < this.sizes.length; i++) {
          if (this.sizes[i].brandId == this.quotation.brandId && this.sizes[i].stone.colorId == this.quotation.colorId && this.sizes[i].sizeId == this.quotation.sizeId) {
            if(this.sizes[i].stone.endstone!='false') {
              if(this.sizes[i].stone.endstone=='left' || this.sizes[i].stone.endstone=='right') {
                hasnormal = true;
              }
              else if(this.sizes[i].stone.endstone=='leftg' || this.sizes[i].stone.endstone=='rightg') {
                hasgrooves = true;
              }
            }
          }
        }
        if(hasnormal) {
          filtered_array.push({value: 'true', text: "Ja (opstaande zijkantjes)"});
        }
        if(hasgrooves) {
          filtered_array.push({value: 'true_grooves', text: "Ja (groeven)"});
        }

        if(this.color && !this.color.glaced) { //ijzerklinker
          filtered_array.push({value: 'false', text: "Nee (ongeglazuurde zijkantjes)"});
        }
        else {
          filtered_array.push({value: 'false', text: "Nee (geglazuurde zijkantjes)"});
        }

        // return [];
        return filtered_array;
      },
    },
    methods: {
      offerteVariantChanged: function () {
        if(!this.is_active_offerteVariant) return;

        this.is_valid['offerteVariant'] = false;

        if(!this.quotation.offerteVariant || this.quotation.offerteVariant=="") {
          this.quotation.brandId = '';
          this.is_active_brandId = false;
          this.quotation.colorId = '';
          this.is_active_colorId = false;
          this.quotation.sizeId = '';
          this.is_active_sizeId = false;
          this.quotation.endstone = '';
          this.is_active_endstone = false;
          this.input_errors['offerteVariant'] = "Uw product is verplicht";
          this.all_valid = false;
        }
        else {
          this.is_valid['offerteVariant'] = true;
          this.is_active_brandId = true;

          var brandIds = [1,2];
          // if(this.quotation.offerteVariant=="" || this.quotation.offerteVariant=="keramische_raamdorpel"  || this.quotation.offerteVariant=="keramische_spekbanden" || this.quotation.offerteVariant=="keramische_muurafdekker") {
          //   brandIds = [1,2];
          // }
          // else if(this.quotation.offerteVariant=="beton_raamdorpel" || this.quotation.offerteVariant=="beton_spekbanden" || this.quotation.offerteVariant=="beton_muurafdekker") {
          //   brandIds = [3];
          // }
          // else if(this.quotation.offerteVariant=="natuursteen_raamdorpel" || this.quotation.offerteVariant=="natuursteen_muurafdekker") {
          //   brandIds = [4,5,6,7];
          // }
          // else if(this.quotation.offerteVariant=="natuursteen_spekbanden") {
          //   brandIds = [6,7];
          // }
          this.possible_brands = [];
          for(var k in brandIds) {
            var brandId = brandIds[k];
            for(var b in this.brands) {
              var brand = this.brands[b];
              if(brand.brandId==brandId) {
                this.possible_brands.push(brand);
                break;
              }
            }
          }

        }
        if(!firsttime) {
          if(this.possible_brands.length==0) {
            swal('Merken','Er zijn nog een merken beschikbaar voor dit product. Neem contact op voor meer informatie.', 'error').catch(swal.noop);
          }
          this.quotation.brandId = '';
          this.quotation.colorId = '';
          this.quotation.sizeId = '';
          this.is_active_sizeId = false;
          this.quotation.endstone = '';
          this.is_active_endstone = false;
        }
      },
      brandIdChanged: function () {
        if(!this.is_active_brandId) return;

        this.is_valid['brandId'] = false;

        if(!this.quotation.brandId || this.quotation.brandId=="") {
          this.is_active_colorId = false;
          this.quotation.colorId = '';
          this.quotation.sizeId = '';
          this.is_active_sizeId = false;
          this.quotation.endstone = '';
          this.is_active_endstone = false;
          this.input_errors['brandId'] = "Merk is verplicht";
          this.all_valid = false;
        }
        else {
          this.is_valid['brandId'] = true;
          this.is_active_colorId = true;
        }
        if(!firsttime) {
          this.quotation.colorId = '';
          this.quotation.sizeId = '';
          this.is_active_sizeId = false;
          this.quotation.endstone = '';
          this.is_active_endstone = false;
        }
      },
      colorIdChanged: function () {
        if(!this.is_active_colorId) return;
        this.is_valid['colorId'] = false;
        if(!this.quotation.colorId || this.quotation.colorId=="") {
          this.is_active_sizeId = false;
          this.quotation.sizeId = '';
          this.quotation.endstone = '';
          this.input_errors['colorId'] = "Merk is verplicht";
          this.all_valid = false;
        }
        else {
          for (var i = 0; i < this.colors.length; i++) {
            if (this.colors[i].colorId == this.quotation.colorId) {
              this.color = this.colors[i];
              break;
            }
          }
          this.is_valid['colorId'] = true;
          this.is_active_sizeId = true;
        }
        if(!firsttime) {
          this.quotation.sizeId = '';
          this.quotation.endstone = '';
          this.is_active_endstone = false;
        }
      },
      sizeIdChanged: function () {
        if(!this.is_active_sizeId) return;
        this.is_valid['sizeId'] = false;
        if(!this.quotation.sizeId || this.quotation.sizeId=="") {
          this.quotation.endstone = '';
          this.is_active_endstone = false;
          this.input_errors['sizeId'] = "Model is verplicht";
          this.all_valid = false;
        }
        else {
          for (var i = 0; i < this.sizes.length; i++) {
            if (this.sizes[i].brandId == this.quotation.brandId && this.sizes[i].stone.colorId == this.quotation.colorId && this.sizes[i].sizeId == this.quotation.sizeId) {
              if(this.sizes[i].stone.endstone=='false') {
                this.stone = this.sizes[i].stone;
                break;
              }
            }
          }
          this.is_valid['sizeId'] = true;
          this.is_active_endstone = true;
        }
        if(!firsttime) {
          this.quotation.endstone = '';
        }
      },
      endstoneChanged: function () {
        if(!this.is_active_endstone) return;
        this.is_valid['endstone'] = false;
        if(this.quotation.endstone==="") {
          this.input_errors['endstone'] = "Eindsteen is verplicht";
          this.all_valid = false;
        }
        else {
          this.is_valid['endstone'] = true;
          this.is_active_endstone = true;

          var lstoneimage = "//www.raamdorpel.nl/images/thresholds/geen.jpg";
          if(this.stone.image!='' && (this.quotation.endstone=="false" || this.quotation.endstone==false)) {
            lstoneimage = "//www.raamdorpel.nl/images/thresholds/"+this.stone.image;
          }
          else {
            //kijk of er een foto is van de kantjes
            for (var i = 0; i < this.sizes.length; i++) {
              if (this.sizes[i].brandId == this.quotation.brandId && this.sizes[i].stone.colorId == this.quotation.colorId && this.sizes[i].sizeId == this.quotation.sizeId) {
                if(this.sizes[i].stone.endstone!='false' && this.sizes[i].stone.image!="") {
                  lstoneimage = "//www.raamdorpel.nl/images/thresholds/"+this.sizes[i].stone.image;
                  break;
                }
              }
            }
          }
          this.stoneimage = lstoneimage;
          this.all_valid = true;
        }
      },
    }

  });

  app.mount("#models");

</script>


