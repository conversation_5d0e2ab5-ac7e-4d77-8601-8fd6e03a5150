<?php

  AppModel::loadBaseClass('BaseFiles');

  /**
   * Type is altijd 1, oftewel PDF. Mag uiteindelijk verwijderd worden.
   */
  class FilesModel extends BaseFiles {

    const CATEGORY_PRODUCTION = 1;
    const CATEGORY_DELIVERY = 2;
    const CATEGORY_INVOICE = 3;
    const CATEGORY_COMPANY = 4;

    /**
     * @return Files[]
     */
    public static function getFilesByQuotationidsCategoryid(array $quotationIds, int $categoryId) {
      return Files::find_all_by(["quotationId" => $quotationIds, "categoryId" => $categoryId], "ORDER BY documentDate DESC, title ");
    }


    /**
     * @return Files[]
     */
    public static function getFinanceFiles() {
      $compIds = [];
      $files = Files::find_all_by(["categoryId" => 4, "showInDocuments" => 1], "AND companyId!=0 ORDER BY documentDate DESC, companyId, title ");
      foreach ($files as $file) {
        $compIds[$file->companyId] = $file->companyId;
      }
      $companies = AppModel::mapObjectIds(CrmCompanies::find_all_by(["companyId" => $compIds]), "companyId");
      foreach ($files as $file) {
        $file->company = $companies[$file->companyId];
      }
      return $files;
    }

    public function getDocumentdate($format = 'd-m-Y') {
      if ($this->documentDate == "0000-00-00" || $this->documentDate == "") {
        return "";
      }
      return date($format, strtotime($this->documentDate));
    }

    public function getUploaddate($format = 'd-m-Y') {
      if ($this->uploadDate == "0000-00-00" || $this->uploadDate == "") {
        return "";
      }
      return date($format, strtotime($this->uploadDate));
    }


    public static function fetchFilesFromMailbox() {

      $config = Config::get("RDE_MAIL_ACCOUNTS");

      $obj = new stdClass();
      $obj->serverurl = $config["MAIL_FILE_BOX_SERVER"];
      $obj->serverport = $config["MAIL_FILE_BOX_SERVER_PORT"];
      $obj->security = $config["MAIL_FILE_BOX_SERVER_SECURITY"];
      $obj->username = $config["document"]["MAIL_FILE_BOX_USERNAME"];
      $obj->password = $config["document"]["MAIL_FILE_BOX_PASSWORD"];

      $imap = new Imap();
      $imap->mailaccount = $obj;
      $imap->imap = $imap->openImapByEA();
      if ($imap->imap === false) {
        throw new GsdException("FilesModel - fetchFilesFromMailbox: connection to IMAP server failed: " . $obj->serverurl);
      }

      $nrofmessages = $imap->getNumMsg();
      $filecount = 0;
      if ($nrofmessages > 0) {

        $messages = $imap->getOverview("1:" . $nrofmessages);
        //echo ($nrofmessages-6).":".$nrofmessages;
        //$messages = $imap->getOverview(($nrofmessages-6).":".($nrofmessages-1));

        foreach ($messages as $key => $message) {
          $msgno = $imap->getMsgno($message->uid);
          $emailMessage = new ImapEmail($imap->imap, $msgno);
          $emailMessage->getAttachments = true;
          $emailMessage->fetch();

          $headers = $imap->getHeaderinfo($msgno);

          if ($headers->from[0]->host != "raamdorpel.nl") {
            //email heeft geen afzender van @raamdorpel.nl
            logToFile("imap", "Niet van afzender @raamdorpel.nl. Bericht verwijderd.");
            $range = $message->uid . ':' . $message->uid;
            $imap->delete($range);
            continue;
          }

          if (isset($emailMessage->attachments)) {

            Files::createDirectory();

            foreach ($emailMessage->attachments as $part) {
              $orignal_filename = $part['filename'];

              $path_info = pathinfo($orignal_filename);
              $filename = StringHelper::slugify($path_info['filename']) . "_" . date('YmdHis') . "." . $path_info['extension'];

              $file = new Files();
              $file->title = $orignal_filename;
              $file->folder = Files::getDirectory();
              $file->filename = $filename;
              $file->documentDate = date("Y-m-d H:i:s");
              $file->uploadDate = date("Y-m-d H:i:s");
              $file->categoryId = 4;
              $file->typeId = 1;
              $file->save();

              file_put_contents(Files::getFullDirectory() . $filename, $part['data']);

              $filecount++;
            }
          }

          //verwijderen van server
          $range = $message->uid . ':' . $message->uid;
          $imap->delete($range);

        }
      }
      return $filecount;
    }

    public static function getDirectory() {
      return "/filesystem/emaildocs/" . date('Y') . "/" . date('m') . "/";
    }

    public function getFilenamepath() {
      return Files::getDirectory() . $this->filename;
    }

    public static function createDirectory() {
      if (!file_exists(Files::getFullDirectory())) {
        if (!mkdir(Files::getFullDirectory(), 0777, true)) {
          throw new GsdException("Kan directory niet aanmaken: " . Files::getDirectory());
        }
      }
      return Files::getDirectory();
    }

    public static function getFullDirectory() {
      return DIR_ROOT_HTTPDOCS . substr(Files::getDirectory(), 1);
    }

    public function destroy() {
      if (empty($this->filename) && file_exists($this->getFilenamepath())) {
        unlink($this->getFilenamepath());
      }
      parent::destroy();
    }


  }