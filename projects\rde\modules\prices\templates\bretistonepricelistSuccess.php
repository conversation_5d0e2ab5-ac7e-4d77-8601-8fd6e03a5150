<?php TemplateHelper::includePartial('_tabs.php', 'prices'); ?>

<form method="post" action="<?php echo reconstructQueryAdd() ?>">

    Hieronder zie je de prijzen voor belgisch arduin - licht geschuurd.
    <?php echo showHelpButton("Automatisch worden de overige prijzen uitgerekend als afgeleide van deze. Bij Overige > Algemene instellingen kun je de basis opslag aanpassen. Wil je een nieuwe prijs toevoegen, voeg deze dan eerst belgisch arduin - licht geschuurd prijs toe bij 'Steen prijzen'.") ?>

    <div class="box">
      <input type="text" name="bsp_search" value="<?php echo $_SESSION['bsp_search'] ?>" placeholder="Zoeken..."/>
      <select name="bsp_display"  id="bsp_display">
        <option value="">Selecteer zichtbaar...</option>
        <option value="true" <?php if($_SESSION['bsp_display']=="true") echo 'selected'; ?>>Ja</option>
        <option value="false" <?php if($_SESSION['bsp_display']=="false") echo 'selected'; ?>>Nee</option>
      </select>
      <select name="bsp_year" id="bsp_year">
        <option value="now">Huidige prijslijst</option>
        <?php for($year=date("Y")+1;$year>=2011;$year--): ?>
          <option value="<?php echo $year ?>" <?php writeIfSelectedVal($_SESSION['sp_year'], $year) ?>><?php echo $year ?></option>
        <?php endfor ?>
      </select>
      <input type="submit" name="go" id="go" value="Zoeken" />
      <input type="submit" name="reset" id="reset" value="Reset filter" />
    </div>
  </form>

  <div class="box">

    <?php if(count($items)!=0): ?>
      <form method="get">
        <input type="hidden" name="action" value="bretistonepriceedit"/>
        Prijzen aanpassen welke ingaan vanaf 1 januari <select name="yearfrom" id="yearfrom">
          <?php echo getOptionVal(date("Y"), date("Y")+1, date("Y")+1) ?>
        </select>
        <input type="submit" name="edit" id="edit" value="Bewerk prijslijst" class="gsd-btn gsd-btn-primary" />
        <?php echo showHelpButton("Met deze knop kun je de steen prijs per stuk aanpassen van de prijzen in deze lijst.","Bewerk prijslijst") ?>

      </form>
    <?php endif; ?>

  </div>


  <?php $pager->writePreviousNext(); ?>

  <?php if(count($items)==0): ?>
    <?php if($_SESSION["sp_brand"]!=""): ?>
      <section class="empty-list-state">
        <p><?php echo __('Er zijn geen items gevonden.') ?></p>
      </section>
    <?php endif; ?>
  <?php else: ?>
    <table class="default_table" style="width: auto;">
      <tr class="dataTableHeadingRow">
        <td>Naam</td>
        <td>Type</td>
        <td style="text-align: left">Kleur</td>
        <td style="text-align: center">Eindsteen</td>
        <td style="text-align: center">Online</td>
        <td style="text-align: right">Prijs</td>
        <td style="text-align: center">Geldig van</td>
        <td style="text-align: center">Geldig tot</td>
      </tr>
      <?php
        /** @var Stones $item */
        foreach($items as $item): ?>
        <tr class="dataTableRow trhover">
          <td><?php echo $item->name ?></td>
          <td><?php echo Stones::TYPES[$item->type] ?></td>
          <td><?php if(isset($colors[$item->colorId])) echo $colors[$item->colorId]->name ?></td>
          <td style="text-align: center"><?php echo $item->endstone=="true"?"Ja":"Nee" ?></td>
          <td style="text-align: center"><?php echo $item->display=="true"?"Ja":"Nee" ?></td>
          <td style="text-align: right"><?php echo StringHelper::asMoney($item->price->price) ?></td>
          <td style="text-align: center"><?php echo $item->price->getValidFrom() ?></td>
          <td style="text-align: center"><?php echo $item->price->getValidTo()=="31-12-9999"?"-":$item->price->getValidTo() ?></td>
        </tr>
      <?php endforeach; ?>
    </table>
  <?php endif; ?>

<script>
  $(document).ready(function() {
    $("#sp_brand,#sp_display,#sp_type,#sp_endstone,#sp_year,.colors").change(function() {
      $("#go").click();
    })
  });
</script>
<style>
  #colors_wrapper {
  }
  #colors_wrapper label {
    padding: 5px 5px 0 0;
    display: inline-block;
  }

</style>