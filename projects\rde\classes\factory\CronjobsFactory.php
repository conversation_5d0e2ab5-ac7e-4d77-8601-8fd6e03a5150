<?php

  use domain\multivers\service\InvoicePaidSave;
  use gsdfw\domain\multivers\exception\MultiversException;
  use gsdfw\domain\multivers\service\MultiversApi;

  class CronjobsFactory extends Cronjobs {

    /**
     * Run from cron.php
     */
    public static function execute() {

//      if(isset($_GET["admin"])) {
//        TimeregistrationMailsFactory::sendWorkerHoursPdf();
//        CronjobsFactory::mailContainersRetourReminder(true);
//        MailsFactory::sendQuotationReminders();
//        MailsFactory::sendQuotationsProduced();

//        CronjobsFactory::importKleuren(true);
//        CronjobsFactory::importLosseStenen(true);
//        CronjobsFactory::importStandaardlengtes(true);
//        CronjobsFactory::setMultiversOnlinePaidInvoices(true);
//        echo 'ADMIN DONE';
//        ResponseHelper::exit();
//      }

      //CronjobsFactory::backupMysql();
      CronjobsFactory::checkPlannedAreCheckedIn();
      CronjobsFactory::fetchFilesFromMailbox();
      CronjobsFactory::fetchFilesFromMailboxPDF();

      CronjobsFactory::timeregistrationSendWorkerHoursPdf();
      CronjobsFactory::timeregistrationSendDeviantHours();
      CronjobsFactory::timeregistrationAlmostInvalidId();
      CronjobsFactory::timeregistrationAlmostEndOfContract();

      CronjobsFactory::mailStockBalance();
      CronjobsFactory::mailStockLoss();
      CronjobsFactory::mailContainersRetourReminder();
      CronjobsFactory::mailQuotationReminders();
      CronjobsFactory::mailQuotationsProduced();

      CronjobsFactory::calculateProductionStats();
      CronjobsFactory::calculateProductionStatsNew();
      CronjobsFactory::mailSuspiciousQuotations();
      CronjobsFactory::mailDamages();

      CronjobsFactory::importKleuren();
      CronjobsFactory::importLosseStenen();
      CronjobsFactory::importStandaardlengtes();

      CronjobsFactory::sendSupplierNeworders();

      CronjobsFactory::setMultiversOnlinePaidInvoices();

      //CronjobsFactory::cleanSandboxusers();
      CronjobsFactory::cleanLogDir();
      CronjobsFactory::executeCleanDirTemp();
      CronjobsFactory::cleanMailLog();
      CronjobsFactory::cleanRecentCallers();
    }

    /**
     * Elke 5 minuten ophalen bestanden
     */
    public static function fetchFilesFromMailbox() {
      logToFile("cron", "fetchFilesFromMailbox start");
      try {
        MailFileBox::fetchFilesFromMailbox();
      }
      catch (GsdException $e) {
        logToFile("cron_error", $e->getMessage());
      }
      logToFile("cron", "fetchFilesFromMailbox end");
    }

    /**
     * Elke 5 minuten ophalen PDF bestanden
     */
    public static function fetchFilesFromMailboxPDF() {
      logToFile("cron", "fetchFilesFromMailboxPDF start");
      try {
        Files::fetchFilesFromMailbox();
      }
      catch (GsdException $e) {
        logToFile("cron_error", $e->getMessage());
      }
      logToFile("cron", "fetchFilesFromMailboxPDF end");
    }

    /**
     * Elke dag om 9:00 controleren of iedereen die is ingeplanned ingeklokt is.
     */
    public static function checkPlannedAreCheckedIn() {
      if (date("H:i") == "09:00" || DEVELOPMENT) {
        logToFile("cron", "checkPlannedAreCheckedIn start");
        WorkerPlanhour::checkPlannedAreCheckedIn();
        logToFile("cron", "checkPlannedAreCheckedIn end");
      }
    }

    /**
     * Elke maandag om 19:00 verzenden naar werknemers
     */
    public static function timeregistrationSendWorkerHoursPdf() {
      if (date("N H:i") == "1 19:00" || DEVELOPMENT) {
        logToFile("cron", "sendWorkerHoursPdf start");

        TimeregistrationMailsFactory::sendWorkerHoursPdf();

        logToFile("cron", "sendWorkerHoursPdf end");
      }
    }

    /**
     * Elke zaterdag om 19:00 verkeerde uren sturen naar bart
     */
    public static function timeregistrationSendDeviantHours() {
      if (date("N H:i") == "6 19:00" || DEVELOPMENT) {
        logToFile("cron", "sendWorkerHoursPdf start");

        TimeregistrationMailsFactory::sendDeviantHours();

        logToFile("cron", "sendWorkerHoursPdf end");
      }
    }

    public static function timeregistrationAlmostInvalidId() {
      if (date("N H:i") == "5 08:00" || DEVELOPMENT) {
        logToFile("cron", "almost invalid id/passport started");
        TimeregistrationMailsFactory::sendAlmostInvalidId(MAIL_BART);
        logToFile("cron", "almost invalid id/passport ended");
      }
    }

    /**
     *
     * Verstuur een mail wanneer contract binnen 1 maand verloopt
     * Elke 5e werkdag om 8 uur
     */
    public static function timeregistrationAlmostEndOfContract() {
      if (date("N H:i") == "5 08:00" || DEVELOPMENT) {
        logToFile("cron", "almost end of contract started");
        TimeregistrationMailsFactory::sendAlmostEndOfContract(MAIL_BART);
        logToFile("cron", "almost end of contract ended");
      }
    }

    /**
     * Verstuur 1ste van de maand overzicht van voorraad kosten
     */
    public static function mailStockBalance() {
      if (date("d H:i") == "01 06:30" || DEVELOPMENT) {
        logToFile("cron", "mailStockBalance started");
        MailsFactory::sendStockBalance();
        MailsFactory::sendStockBalance(true);
        logToFile("cron", "mailStockBalance ended");
      }
    }


    /**
     *
     * Elke dat kijken of er verlies boekingen zijn geweest.
     * Elke dag om
     */
    public static function mailStockLoss() {
      if (date("H:i") == "07:00" || DEVELOPMENT) {
        logToFile("cron", "mailstockloss");
        MailsFactory::sendStockLoss();
        logToFile("cron", "mailstockloss");
      }
    }

    /**
     *
     * Elke dag recente bellers opschonen
     * Om 3 uur 's nachts
     */
    public static function cleanRecentCallers() {
      if (date("H:i") == "03:00" || DEVELOPMENT) {
        logToFile("cron", "cleanRecentCallers");
        RecentCallers::delete_by([]);
        logToFile("cron", "cleanRecentCallers");
      }
    }

    /**
     * Elke maandag ochtend.
     * Email naar klant te retourneren containers
     */
    public static function mailContainersRetourReminder($force = false) {
      if (date("N H:i") == "1 07:00" || DEVELOPMENT || $force) {
        logToFile("cron", "mailContainersRetourReminder");

        //containers zijn minstens 1 maand op locatie
        $cqsAway = ContainersQuotations::find_all("WHERE returnDate IS NULL AND NOT deliverDate IS NULL");
        $quotationIds = [];
        $containersIds = [];
        foreach ($cqsAway as $cq) {
          $quotationIds[$cq->quotationId] = $cq->quotationId;
          $containersIds[$cq->containerId] = $cq->containerId;
        }

        //alle geleverde quotations
        $filt = "AND quotations.statusId>=55 ";
        //      $filt .= "AND quotations.companyId=21896";
//        $filt .= " LIMIT 300";
        $quotations = AppModel::mapObjectIds(Quotations::find_all_by(["flaggedForDeletion" => 0, "quotationId" => $quotationIds], $filt), "quotationId");
        //containers, niet de kappotte/inStock=A
        $containers = AppModel::mapObjectIds(Containers::find_all_by(["containerId" => $containersIds], "AND inStock!='A' AND containerNumber<8000"), "containerId");

        $grouped = []; //bij companyId or UserId;
        foreach ($cqsAway as $cq) {

          if (!isset($quotations[$cq->quotationId])) continue;
          if (!isset($containers[$cq->containerId])) continue;

          $container = $containers[$cq->containerId];
          $quotation = $quotations[$cq->quotationId];

          $key = $quotation->companyId;
          if ($quotation->companyId == "") {
            $key = "U" . $quotation->userId;
          }
          $group = null;
          if (isset($grouped[$key])) {
            $group = $grouped[$key];
          }
          else {
            $group = ["moreAs3" => false, "moreAs1" => false, "userIds" => [], "containers" => []];
          }

          $group["userIds"][$quotation->userId] = $quotation->userId;

          $l_container = null;
          if (isset($group["containers"][$container->containerId])) {
            $l_container = $group["containers"][$container->containerId];
          }
          else {
            $l_container = $container;
            $l_container->quotations = [];
          }

          if ($cq->nextroute == 'true') {
            //als er 1-tje op nextroute staat, dan word de container opgehaald.
            $l_container->nextroute = 'true';
          }

          if (empty($cq->deliverdatetime) || $cq->deliverdatetime < intval($cq->getDeliverDate("U"))) {
            //altijd oudste leverdatum
            $l_container->deliverdate = $cq->getDeliverDate();
            $l_container->deliverdatetime = intval($cq->getDeliverDate("U"));
          }

          $l_container->quotations[] = $quotation;
          $group["containers"][$container->containerId] = $l_container;

          if (intval($cq->getDeliverDate("U")) < strtotime("-3 MONTHS")) {
            $group["moreAs3"] = true;
          }
          elseif (intval($cq->getDeliverDate("U")) < strtotime("-1 MONTHS")) {
            $group["moreAs1"] = true;
          }

          $grouped[$key] = $group;
        }

        $isFirstMondayOfMonth = date('j') < 7;
        $sendcounter = 0;

        foreach ($grouped as $compOrUserId => $group) {

          if ($group["moreAs1"] && ($group["moreAs3"] || $isFirstMondayOfMonth)) { //eerste maandag van de maand en langer als 1 maand op locatie, of wanneer er bakken zijn die meer dan 3 maanden op locatie staan.

            //sorteer nog even
            usort($group["containers"], function ($a, $b) {
              return $a->deliverdatetime - $b->deliverdatetime;
            });

            foreach ($group["containers"] as $container) {
              usort($container->quotations, function ($a, $b) {
                return strcmp($a->getQuotationNumberFull(), $b->getQuotationNumberFull());
              });
            }

            MailsFactory::sendContainersRetourReminder($group, $compOrUserId);
            $sendcounter++;

          }
        }
        logToFile("cron", "mailContainersRetourReminder. send: " . $sendcounter);
      }
    }

    /**
     * Elke dinsdag ochtend.
     * Email naar klant te retourneren containers
     */
    public static function mailQuotationReminders() {
      if (date('N H:i') == "2 07:00" || DEVELOPMENT) {
        logToFile("cron", "mailQuotationReminders start");
        MailsFactory::sendQuotationReminders();
        logToFile("cron", "mailQuotationReminders end");
      }
    }

    /**
     * Elke dag om 18:15
     * Email naar klant geproduceerde orders van de dag
     */
    public static function mailQuotationsProduced() {
      if (date('H:i') == "18:15" || DEVELOPMENT) {
        logToFile("cron", "mailQuotationsProduced start");
        MailsFactory::sendQuotationsProduced();
        logToFile("cron", "mailQuotationsProduced end");
      }
    }


    public static function importKleuren($force = false) {
      if (date('H:i') == "06:00" || DEVELOPMENT || $force) {
        logToFile("cron", "importKleuren start");

        $prodcatid = 3;
        $allproducts = AppModel::mapObjectIds(Product::getAllProducts('AND category_product.category_id=' . $prodcatid, 'nl'), "supplier_code");
        foreach (StoneColors::getDisplayColors([1, 2]) as $color) {
          $supplier_code = "COLOR_" . $color->colorId;
          $product = new Product();
          $content = new ProductContent();
          if (isset($allproducts[$supplier_code])) {
            $product = $allproducts[$supplier_code];
            $content = $product->content;
          }
          else {
            $content->name = $color->name;
            $product->online_custshop = 1;
            $product->online_uc = 1;
            $product->online_admin = 1;
            $product->brand_id = $color->brandId;
            $product->supplier_code = $supplier_code;
            $product->vatgroup = 2;
            $product->discountgroup_id = 3; //koudglazuur
            $content->locale = 'nl';
            $product->price_part = 1; //even een lege prijs
            $product->shipping_cat = 'A'; //briefpost
            $product->code = $color->short;
          }

          $product->save();

          $content->product_id = $product->id;
          $content->save();

          $catprod = CategoryProduct::getCategoryProduct($product->id, $prodcatid);
          if (!$catprod) {
            $catprod = new CategoryProduct();
            $catprod->product_id = $product->id;
            $catprod->category_id = $prodcatid;
            $catprod->online = 1;
            $catprod->save();
          }
        }

        logToFile("cron", "importKleuren end");
      }
    }

    public static function importLosseStenen($force = false) {
      if (date('H:i') == "06:00" || DEVELOPMENT || $force) {
        logToFile("cron", "importLosseStenen start");

        $prodcatid = 1;
        $allproducts = AppModel::mapObjectIds(Product::getAllProducts('AND category_product.category_id=' . $prodcatid, 'nl'), "supplier_code");
        $stoneprices = StonePrices::getLatestPrices();
        $brands = AppModel::mapObjectIds(Brand::find_all());

        foreach (Stones::getDisplayStones() as $stone) {

          if (!isset($brands[$stone->brandId])) continue; // merk staat niet in rde_api, wel bij stones. skippen. eerst merk aanmaken.

          $supplier_code = "STONE_" . $stone->stoneId;
          $product = new Product();
          $content = new ProductContent();
          if (isset($allproducts[$supplier_code])) {
            $product = $allproducts[$supplier_code];
            $content = $product->content;
          }
          else {
            $content->name = $stone->name;
            $product->online_custshop = 1;
            $product->online_uc = 1;
            $product->online_admin = 1;
            $product->brand_id = $stone->brandId;
            $product->supplier_code = $supplier_code;
            $product->vatgroup = 2;
            $content->locale = 'nl';
            $product->shipping_cat = 'B'; //pakketpost
            $product->weight = $stone->weight * 1000;
          }

          if ($stone->stoneIncreaseGroup == "A") {
            $product->discountgroup_id = 1;
          }
          elseif ($stone->stoneIncreaseGroup == "B") {
            $product->discountgroup_id = 6;
          }
          elseif ($stone->stoneIncreaseGroup == "C") {
            $product->discountgroup_id = 8;
          }
          elseif ($stone->stoneIncreaseGroup == "D") {
            $product->discountgroup_id = 9;
          }
          else { //stoneIncreaseGroup is niet gezet, dan offline halen
            $product->online_custshop = 0;
            $product->online_uc = 0;
            $product->online_admin = 0;
          }

          $product->code = $stone->short;

          if (!isset($stoneprices[$stone->stoneId]))
            continue; //geen prijs gedefineerd, overlaan.
          $product->price_part = round($stoneprices[$stone->stoneId]->price, 2);

          //images
          $filesource = DIR_ROOT_HTTPDOCS . 'images/thresholds/' . $stone->image;
          if ($stone->image != "" && file_exists($filesource)) {
            $path_parts = pathinfo($filesource);
            $ext = strtolower($path_parts['extension']);

            $thumb = 'pr_' . $product->id . '_' . time() . '_main_thumb.' . $ext;
            $orig = 'pr_' . $product->id . '_' . time() . '_main_orig.' . $ext;

            //opruimen oude beelden
            if ($product->main_image_thumb != "" && file_exists(DIR_UPLOAD_CAT . $product->main_image_thumb)) {
              unlink(DIR_UPLOAD_CAT . $product->main_image_thumb);
            }
            if ($product->main_image_orig != "" && file_exists(DIR_UPLOAD_CAT . $product->main_image_orig)) {
              unlink(DIR_UPLOAD_CAT . $product->main_image_orig);
            }

            $product->main_image_thumb = $thumb;
            $product->main_image_orig = $orig;

            ImageHelper::resizeToFixedSize($filesource, DIR_UPLOAD_CAT . $thumb, IMAGES_THUMB_WIDTH, IMAGES_THUMB_HEIGHT);
            ImageHelper::resizeImageGD($filesource, DIR_UPLOAD_CAT . $orig, IMAGES_ORIG_WIDTH, IMAGES_ORIG_HEIGHT, true);
          }

          $product->save();

          $content->product_id = $product->id;
          $content->buildDefaultUrl();
          $content->buildUrlUnique();
          $content->save();

          $catprod = CategoryProduct::getCategoryProduct($product->id, $prodcatid);
          if (!$catprod) {
            $catprod = new CategoryProduct();
            $catprod->product_id = $product->id;
            $catprod->category_id = $prodcatid;
            $catprod->online = 1;
            $catprod->save();
          }
        }

        logToFile("cron", "importLosseStenen end");
      }
    }

    /**
     * Importeer steen informatie standaard le
     */
    public static function importStandaardlengtes($force = false) {
      if (date('H:i') == "06:00" || DEVELOPMENT || $force) {
        logToFile("cron", "importStandaardlengtes start");

        $catfilt = 'AND category_product.category_id IN (19,20,21) ';
        $allproducts = Product::getAllProducts($catfilt, 'nl');

        foreach ($allproducts as $product) {
          $stoneId = substr($product->supplier_code, 6);
          if (!is_numeric($stoneId)) continue;

          $stone = Stones::find_by(["stoneId" => $stoneId]);
          if ($stone->display == "false") {
            //niet zichtbaar dan ook offline voor klant
            foreach (CategoryProduct::find_all_by(["product_id" => $product->id]) as $cat_prod) {
              $cat_prod->online = 0;
              $cat_prod->save();
            }
            $product->online_custshop = 0;
            $product->save();
            continue;
          }

          $product->discountgroup_id = 12;

          $stone_price = Quotations::getPrices(date("Y-m-d"), '', $stone);
          if ($stone_price === false) continue;

          $product->price_part = round($stone->getMeterprice($stone_price), 2);

          $product->save();
        }

        logToFile("cron", "importStandaardlengtes end");
      }
    }

    /**
     * Elke dag om 4 uur.
     * In het oude systeem hadden we niet altijd een startdate.
     * @param bool $force
     */
    public static function calculateProductionStats(bool $force = false): void {
      if (date('H:i') == '04:00' || DEVELOPMENT || $force) {
        logToFile("cron", "calculateProductionStats start");
        $startdate = date("Y-m-d", strtotime("-1 WEEK"));
        $enddate = date("Y-m-d");
        if (DEVELOPMENT) {
          $startdate = date("Y-m-d", strtotime("-23 WEEK"));
          $enddate = date("Y-m-d", strtotime("-22 WEEK"));
        }

        $filter = "WHERE enddate>'" . $startdate . "' AND enddate<='" . $enddate . "' ";
//        $filter = "";
        $filter .= "ORDER BY id DESC ";
//        $filter .= "LIMIT 30000,70000 ";
        $equotations = EmployeeQuotation::find_all($filter);

        foreach ($equotations as $equotation) {
          $eqs = EmployeeQuotation::find_all_by(["quotationId" => $equotation->quotationId]);

          $quotation_extra = QuotationsExtra::find_by(["quotationId" => $equotation->quotationId]);
          if (!$quotation_extra) {
            continue;
          }
          $quotation = Quotations::find_by(["quotationId" => $equotation->quotationId]);

          $valid = true;
          if (count($eqs) != 1) {
            $valid = false;
          }
          $eq = $eqs[0];

          if ($eq->getStartdateFormatted("dmY") != $eq->getEnddateFormatted("dmY")) {
            $valid = false;
          }
          elseif (!$quotation) {
            $valid = false;
          }
          elseif ($quotation->meters == 0) {
            $valid = false;
          }

          //pak van deze employee de laatst afwerkte order
          $eq_prev = EmployeeQuotation::find_by(["employeeId" => $eq->employeeId], "AND enddate<'" . $eq->enddate . "' ORDER BY enddate DESC LIMIT 1");
          if (!$eq_prev) {
            $valid = false;
          }

          if (!$valid) {
            //geen geldige snelheid, en er staat wel iets in database. Leeg maken.
            if ($quotation_extra->prod_cm_per_hour != null) {
              $quotation_extra->prod_cm_per_hour = null;
              $quotation_extra->prod_employee_id = null;
              $quotation_extra->save();
            }
            continue;
          }

          $minutes = ($eq->getEnddateFormatted("U") - $eq_prev->getEnddateFormatted("U")) / 60;
          $prevTime = intval($eq_prev->getEnddateFormatted("Hi"));
          $curTime = intval($eq->getEnddateFormatted("Hi"));
          if (($prevTime <= 1005 && $curTime >= 1010) || ($prevTime <= 1505 && $curTime >= 1510)) {
            //er zit koffie pauze tussen
            $minutes -= 15;
          }
          elseif ($prevTime <= 1235 && $curTime >= 1255) {
            //er zit lunch pauze tussen
            $minutes -= 30;
          }

          $quotation_extra = QuotationsExtra::find_by(["quotationId" => $equotation->quotationId]);
          if (!$quotation_extra) continue;
          $speed_calc = floor(($quotation->meters / ($minutes / 60)) * 100);

          if ($speed_calc < 200 || $speed_calc > 1700) {
            //ongeldige snelheid
            if ($quotation_extra->prod_cm_per_hour != null) {
              //geen geldige snelheid, en er staat wel iets in database. Leeg maken.
              $quotation_extra->prod_cm_per_hour = null;
              $quotation_extra->prod_employee_id = null;
              $quotation_extra->save();
            }
            continue;
          }

          $quotation_extra->prod_cm_per_hour = $speed_calc;
          $quotation_extra->prod_employee_id = $equotation->employeeId;
          $quotation_extra->save();

        }

        logToFile("cron", "calculateProductionStats end");
      }
    }


    /**
     * Elke dag om 4 uur.
     * Met de nieuwe app hebben we een start en enddate
     * @param bool $force
     */
    public static function calculateProductionStatsNew(bool $force = false): void {
      if (date('H:i') == '04:00' || DEVELOPMENT || $force) {
        logToFile("cron", "calculateProductionStatsNew start");
        $startdate = date("Y-m-d", strtotime("-1 WEEK"));
        $enddate = date("Y-m-d");
        if (DEVELOPMENT) {
          $startdate = date("Y-m-d", strtotime("-22 WEEK"));
          $enddate = date("Y-m-d", strtotime("-21 WEEK"));
        }

        $filter = "WHERE startdate>='" . $startdate . "' AND enddate<'" . $enddate . "' ";
//        $filter = "";
        $filter .= "ORDER BY id DESC ";
//        $filter .= "LIMIT 10000, 100000";
        $equotations = EmployeeQuotation::find_all($filter);

        foreach ($equotations as $equotation) {
          $eqs = EmployeeQuotation::find_all_by(["quotationId" => $equotation->quotationId]);

          $quotation_extra = QuotationsExtra::find_by(["quotationId" => $equotation->quotationId]);
          if (!$quotation_extra) {
            continue;
          }
          $quotation = Quotations::find_by(["quotationId" => $equotation->quotationId]);

          $valid = true;
          if (count($eqs) != 1) {
            $valid = false;
          }
          $eq = $eqs[0];

          if ($eq->getStartdateFormatted("dmY") != $eq->getEnddateFormatted("dmY")) {
            $valid = false;
          }
          elseif (!$quotation) {
            $valid = false;
          }
          elseif ($quotation->meters == 0) {
            $valid = false;
          }

          if (!$valid) {
            //geen geldige snelheid, en er staat wel iets in database. Leeg maken.
            if ($quotation_extra->prod_cm_per_hour != null) {
              $quotation_extra->prod_cm_per_hour = null;
              $quotation_extra->prod_employee_id = null;
              $quotation_extra->save();
            }
            continue;
          }

          $minutes = ((int)$eq->getEnddateFormatted("U") - (int)$eq->getStartdateFormatted("U")) / 60;
          $prevTime = intval($eq->getStartdateFormatted("Hi"));
          $curTime = intval($eq->getEnddateFormatted("Hi"));
          if (($prevTime <= 1005 && $curTime >= 1010) || ($prevTime <= 1505 && $curTime >= 1510)) {
            //er zit koffie pauze tussen
            $minutes -= 15;
          }
          elseif ($prevTime <= 1235 && $curTime >= 1255) {
            //er zit lunch pauze tussen
            $minutes -= 30;
          }

          if ($minutes == 0) continue;

          $speed_calc = floor(($quotation->meters / ($minutes / 60)) * 100);
          if ($speed_calc < 200 || $speed_calc > 1700) {
            //ongeldige snelheid
            if ($quotation_extra->prod_cm_per_hour != null) {
              //geen geldige snelheid, en er staat wel iets in database. Leeg maken.
              $quotation_extra->prod_cm_per_hour = null;
              $quotation_extra->prod_employee_id = null;
              $quotation_extra->save();
            }
            continue;
          }

          $quotation_extra->prod_cm_per_hour = $speed_calc;
          $quotation_extra->prod_employee_id = $equotation->employeeId;
          $quotation_extra->save();


        }

        logToFile("cron", "calculateProductionStatsNew end");
      }
    }

    /**
     * Set online paid invoices on paid in multivers
     * Elke dag om 7 uur
     * @throws Exception
     */
    public static function setMultiversOnlinePaidInvoices($force = false) {
      if (date('H:i') == "07:00" || DEVELOPMENT || $force) {

        logToFile("cron", "setMultiversOnlinePaidInvoices start");

        try {
          $user = User::getUserWithOrganById(ADMIN_DEFAULT_ID);
          $multivers = Multivers::find_by(["organisation_id" => $user->organisation_id]);
          $multivers_api = new MultiversApi($multivers);

          $invoice_paid_service = new InvoicePaidSave($multivers_api);
          $invoices = Invoices::getOnlinepaidInvoices("AND paid>='" . date("Y-m-d", strtotime("-1 DAY")) . "' ");
          $invoice_paid_service->setpaid($invoices);
        }
        catch (MultiversException $e) {
          logToFile("multivers_error", $e->getMessage() . " " . $e->getTraceAsString());
        }

        logToFile("cron", "setMultiversOnlinePaidInvoices end");
      }
    }


    /**
     * Elk uur.
     */
    public static function mailSuspiciousQuotations() {
      if ((date('i') == "05") || DEVELOPMENT) {
        logToFile("cron", "mailSuspiciousQuotations start");
        MailsFactory::sendSuspiciousQuotations();
        logToFile("cron", "mailSuspiciousQuotations end");
      }
    }

    /**
     * elke 1ste van de maand om 8 uur sochtends
     * Email naar bart beschadigingen
     */
    public static function mailDamages() {
      if (date('d H:i') == "01 08:00" || DEVELOPMENT) {
        logToFile("cron", "mailDamages start");
        MailsFactory::sendDamaged();
        logToFile("cron", "mailDamages end");
      }
    }

    /**
     * elke avond om 22:00
     * Email naar breti met nieuwe bestellingen
     */
    public static function sendSupplierNeworders() {
      if (date('H:i') == "22:00" || DEVELOPMENT) {
        logToFile("cron", "sendSupplierNeworders start");
        MailsFactory::sendSupplierNeworders();
        logToFile("cron", "sendSupplierNeworders end");
      }
    }

    /**
     * Elke maandag avond om 22:00
     * Opruimen oude sandboxusers
     */
    public static function cleanSandboxusers() {
      if (date("N H:i") == "1 22:00" || DEVELOPMENT) {
        logToFile("cron", "cleanSandboxusers start");

        foreach (SandboxUsers::find_all("WHERE lastLogin<'" . date("Y-m-d", strtotime("-3 YEARS")) . "'") as $su) {
//          pd($su->userId);
          $hasQuotation = Quotations::find_by(["userId" => $su->userId]);
          if ($hasQuotation) {
            continue;
          }
          $hasQuotation = Quotations::find_by(["companyId" => $su->companyId]);
          if ($hasQuotation) {
            continue;
          }
          $hasInvoice = Invoices::find_by(["userId" => $su->userId]);
          if ($hasInvoice) {
            continue;
          }
          $company = false;
          if ($su->companyId != "") {
            $company = CrmCompanies::find_by(["companyId" => $su->companyId]);
//            if($company) {
//              continue;
//            }
          }
          echo $su->getNaam() . " - " . ($company ? $company->name : ' GEEN BEDRIJF') . " - " . $su->getLastLogin() . "<br/>";
        }
        echo 'done';

        logToFile("cron", "cleanSandboxusers end");
      }
    }

    /**
     * Once a day clean all files in the temp_pdf folder older then 1 day
     * @param bool $force : execute regardless of time
     */
    public static function executeCleanDirTemp(bool $force = false): void {
      if ((date('H') == '02' && date('i') == "15") || DEVELOPMENT || $force) {
        parent::executeCleanDirTemp($force);
        FileHelper::cleanup(DIR_ROOT_HTTPDOCS . "temp_pdf/", true);
      }
    }

  }