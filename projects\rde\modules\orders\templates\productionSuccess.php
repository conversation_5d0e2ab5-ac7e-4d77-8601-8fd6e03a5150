<style>
  .default_table {
    max-width: 60%;
  }
  .default_table tr.dataTableRow td {
    width: 50%;
    padding: 5px;
  }
</style>

<?php TemplateHelper::includePartial('_tabs.php', 'orders', compact(["quotation", "quotation_extra", "company", "sandboxuser"])); ?>
<?php writeErrors($errors, true); ?>

<form method="post">

  <table class="default_table">
    <tr class="dataTableHeadingRow">
      <td>Eigenschap</td>
      <td>Waarde</td>
    </tr>
    <?php
      foreach ($form->getElements() as $name => $element):
        if (isset($shortLabels[$name])):
    ?>
      <tr class="dataTableRow">
        <td class="head" title="<?php echo $element->getLabel(); ?>"><?php echo $shortLabels[$name]; ?></td>
        <td><?php echo $element->render(false); ?></td>
      </tr>
    <?php
        else:
          $element->renderRow();
        endif;
      endforeach;
    ?>
    <?php
      foreach ($formQE->getElements() as $key => $element):
        if (in_array($key, ['stoneOrdered', 'stoneOrderedDate', 'stoneDeliveryDate', 'totalMiddlesStones', 'totalLeftEndStones', 'totalRightEndStones', 'totalLeftEndStonesGrooves'])) continue;
        $element->renderRow();
      endforeach;
    ?>

    <?php
      if (isset($formAddress)) {
        foreach ($formAddress->getElements() as $element):
          $element->renderRow();
        endforeach;
      }
    ?>
  </table>

  <br>

  <table class="default_table">
    <tr class="dataTableHeadingRow">
      <td>Eigenschap</td>
      <td>Waarde</td>
    </tr>
    <?php
      foreach ($formQE->getElements() as $key => $element):
        if (!in_array($key, ['stoneOrdered', 'stoneOrderedDate', 'stoneDeliveryDate', 'totalMiddlesStones', 'totalLeftEndStones', 'totalRightEndStones', 'totalLeftEndStonesGrooves'])) continue;
        $element->renderRow();
      endforeach;
    ?>
    <!-- stenen-->
    <tr class="dataTableRow">
      <td style="font-weight: bold">Merk</td>
      <td><?php echo $stone_brand; ?></td>
    </tr>
    <tr class="dataTableRow">
      <td style="font-weight: bold">Kleur</td>
      <td><?php echo $stone_color; ?></td>
    </tr>
    <tr class="dataTableRow">
      <td style="font-weight: bold">Afmetingen</td>
      <td><?php echo $stone_size; ?></td>
    </tr>
  </table>

  <br>
  <input type="submit" name="go" value="Opslaan" class="gsd-btn gsd-btn-primary"/>
  <input type="submit" name="go_list" value="Opslaan en naar lijst" class="gsd-btn gsd-btn-secondary"/>
</form>


