<?php TemplateHelper::includePartial("_tabs.php", 'orders') ?>

<form method="post" class="edit-form">
  <table class="default_table">
    <tr class="dataTableHeadingRow">
      <td>Eigenschap</td>
      <td>Waarde</td>
    </tr>
    <?php
      foreach ($form->getElements() as $element):
        $element->renderRow();
      endforeach;
    ?>
  </table>
  <input type="submit" name="go" value="Opslaan" class="gsd-btn gsd-btn-primary"/>
  <input type="submit" name="go_list" value="Opslaan en naar lijst" class="gsd-btn gsd-btn-secondary"/>
</form>

<?php if (empty($containerQuotations)): ?>
  Geen offertes gevonden bij deze bak of rek.
<?php else: ?>
  <div>
    <table class="default_table default_table_center">
      <tr class="dataTableHeadingRow">
        <td><PERSON><PERSON><PERSON>zig</td>
        <td>Bak nummer</td>
        <td>Aflver datum</td>
        <td>Retour datum</td>
        <td>Offerte nummer</td>
        <td>Bedrijf</td>
        <td>Plaats</td>
      </tr>
      <?php foreach ($containerQuotations as $containerQuotation): ?>
        <tr class="dataTableRow trhover">
          <td><?php echo $containerQuotation->cont_in_stock ?></td>
          <td><?php echo $container->containerNumber ?></td>
          <td><?php echo $containerQuotation->getDeliverDate() ?></td>
          <td><?php echo $containerQuotation->getReturnDate() ?></td>
          <td>
            <a href="<?php echo PageMap::getUrl("M_RDE_ORDERS_GENERAL").'?id=' . $containerQuotation->quotation->quotationId  ?>">
              <?php echo $containerQuotation->quotation->getQuotationNumberFull() ?>
            </a>
          </td>
          <td>
            <?php if(isset($containerQuotation->company)): ?>
              <a href="<?php echo PageMap::getUrl("M_BEDRIJVENGIDS_EDIT"). $containerQuotation->company->companyId ?>">
                <?php echo $containerQuotation->company->name ?>
              </a>
            <?php endif; ?>
          </td>
          <td>
            <?php echo $containerQuotation->quotation->domestic ?>
          </td>
        </tr>
      <?php endforeach; ?>
    </table>
  </div>
<?php endif;