<section class="title-bar">
  <h1>
    <?php echo Navigation::getItem(Navigation::getActivePageId())->getName() ?>
  </h1>
  <?php TemplateHelper::includePartial('_tabs.php', 'other'); ?>
  <?php TemplateHelper::includePartial('_tabs.php','planning'); ?>
</section>

<form method="post">
  <div>
    <div style="width: 33.3%; float: left;">
      <input type="submit" name="vorige" value="<< <?php echo __("VORIGE WEEK") ?>" class="gsd-btn gsd-btn-secondary" style="float: left;">
      <input type="submit" name="vandaag" value="Naar vandaag" <?php if($_SESSION["startdate"] == DateTimeHelper::getFirstOfWeekDate(date("Y-m-d"),'U')) echo "disabled" ?> class="gsd-btn gsd-btn-primary" style="float: left; margin-left: 15px;">
    </div>
    <div style="width: 33.3%; float: left; text-align: center;font-size: 14px;font-weight: bold;">
      Week <?php echo date("W",$_SESSION["startdate"]); ?>:
      <?php echo showdate($_SESSION["startdate"]) . " - " . showdate(strtotime("+6 DAYS",$_SESSION["startdate"])); ?>
    </div>
    <div style="width: 33.3%; float: left; text-align: right;">
      <input type="submit" name="volgende" value="VOLGENDE WEEK >>" class="gsd-btn gsd-btn-secondary">
    </div>
  </div>
  <div class="clear"></div><br/>
  <table class="default_table">
    <tr class="dataTableHeadingRow">
      <td>Werknemers</td>
      <?php for($time=$_SESSION["startdate"];$time<strtotime("+7 DAYS",$_SESSION["startdate"]);$time=strtotime("+1 DAY",$time)): ?>
        <td style="text-align: right;"><?php echo showdate($time) ?></td>
      <?php endfor; ?>
      <td style="text-align: right;">Totalen</td>
    </tr>
    <?php foreach($workers as $worker): ?>
      <tr class="dataTableRow trhover">
        <td><?php echo $worker->getNaam() ?></td>
        <?php
          $employeeweekhours = 0;
          for($time=$_SESSION["startdate"];$time<strtotime("+7 DAYS",$_SESSION["startdate"]);$time=strtotime("+1 DAY",$time)):
            $hours = 0;
            if(isset($worker->planning[$time])):
              $hours = $worker->planning[$time];
            endif;
            $employeeweekhours += $hours;
            ?>
          <td style="text-align: right; <?php if($employeeweekhours!=0 && isset($worker->verlof[$time])): ?>color: red;<?php endif; ?>" <?php
          if($employeeweekhours!=0 && isset($worker->verlof[$time])): ?>
            class="qtipa" title="Let op: deze werknemer heeft ook <?php echo -1*$worker->verlof[$time] ?> snipperuren vandaag"
          <?php endif; ?>><?php if($hours!=0) echo number_format($hours,2) ?></td>
        <?php endfor; ?>
        <td style="text-align: right;"><?php echo number_format($employeeweekhours,2) ?></td>
      </tr>
    <?php endforeach; ?>
    <tr class="dataTableHeadingRow topborder">
      <td>Totaal uren:</td>
      <?php for($time=$_SESSION["startdate"];$time<strtotime("+7 DAYS",$_SESSION["startdate"]);$time=strtotime("+1 DAY",$time)): ?>
        <td style="text-align: right;"><?php echo isset($sommatie[$time])?number_format($sommatie[$time]['hours'],2):0 ?></td>
      <?php endfor; ?>
      <td style="text-align: right;"><?php echo number_format($sommatie['hourstotal'],2) ?></td>
    </tr>
    <tr class="dataTableHeadingRow nobottomborder">
      <td>Aantal werknemers:</td>
      <?php for($time=$_SESSION["startdate"];$time<strtotime("+7 DAYS",$_SESSION["startdate"]);$time=strtotime("+1 DAY",$time)): ?>
        <td style="text-align: right;"><?php echo isset($sommatie[$time])?count($sommatie[$time]['employees']):0 ?></td>
      <?php endfor; ?>
      <td></td>
    </tr>
  </table>

</form>