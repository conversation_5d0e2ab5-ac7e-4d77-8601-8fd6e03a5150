<?php

  namespace domain\quotations\service;

  use Category;
  use Product;
  use ProductContent;
  use Projects;
  use Quotations;
  use SandboxUsers;
  use StoneColors;
  use Stones;
  use StoneSizes;

  class GetExtraProducts {

    var $quotation;
    var $stone;
    var $category_parent;

    var $categories = [];
    var $online_product_filt = '';
    var $online_cat_filt = '';

    /**
     * GetExtraProducts constructor.
     *
     * @param Quotations $quotation
     * @param Stones $stone
     */
    public function __construct($quotation, $stone) {
      $this->quotation = $quotation;
      $this->stone = $stone;

      if ($this->stone->isKeramiek()) {
        $this->category_parent = Category::find_by_id(17);
      }
      elseif ($this->stone->isNatu<PERSON>teen()) {
        $this->category_parent = Category::find_by_id(15);
      }
      elseif ($this->stone->isBeton()) {
        $this->category_parent = Category::find_by_id(14);
      }

      if (SandboxUsers::isAdmin()) {
        $this->online_product_filt .= "AND (product.online_uc=1 OR product.online_admin=1) ";
        $this->online_cat_filt .= "AND (category.online_uc=1 OR category.online_admin=1) ";
      }
      else {
        $this->online_product_filt .= "AND product.online_uc=1 ";
        $this->online_cat_filt .= "AND category.online_uc=1 ";
      }

    }

    /**
     * @return array
     */
    public function fetch() {

      if ($this->category_parent == null) {
        return [];
      }

      $ignore = [];

      if ($this->stone->isKeramiek()) {
        $this->getByCatergoryId(32); //desinfectie
        $this->getStones(1);
        $this->getAfstandhouders(2);
        $this->getColors(3);
        $ignore = [1, 2, 3, 32]; //32=desinfectie
      }

      foreach (Category::getChildren($this->category_parent) as $child) {
        if (in_array($child->id, $ignore)) continue;
        $this->getByCatergoryId($child->id);
      }

      if (SandboxUsers::isAdmin()) {
        $other = Category::find_by_id(18);
        foreach (Category::getChildren($other) as $child) {
          if (in_array($child->id, $ignore)) continue;
          $this->getByCatergoryId($child->id, "Overige: ");
        }
      }

      return $this->categories;
    }

    private function getStones($category_id) {

      $stones = array_merge([$this->stone], $this->stone->getSibelings());

      //toevoegen stenen
      $category = Category::getCategoryAndContent($category_id);
      if ($category->online_uc == 1 || (SandboxUsers::isAdmin() && $category->online_admin == 1)) {
        $category->projects = [];
        $catname = "";
        $standardproduct = false;
        foreach ($stones as $lstone) {
          $project = new Projects();
          $project->product = Product::find_by(["supplier_code" => "STONE_" . $lstone->stoneId, "void" => 0], $this->online_product_filt);
          if ($project->product) {
            $project->product_id = $project->product->id;
            $project->product->content = ProductContent::getByProductIdAndLang($project->product->id);
            if ($lstone->endstone == 'left') {
              $project->name = "Eindsteen links";
            }
            elseif ($lstone->endstone == 'right') {
              $project->name = "Eindsteen rechts";
            }
            elseif ($lstone->endstone == Stones::ENDSTONE_LEFTG || $lstone->endstone == Stones::ENDSTONE_RIGHTG) {
              $project->name = "Eindsteen groef";
            }
            else {
              $project->name = "Standaard";
            }
            $category->projects[$project->product_id] = $project;
            if (!$standardproduct) {
              $standardproduct = $project->product;
            }
          }

          if ($catname == "") {
            $brand = $lstone->brandId == 1 ? "StJoris" : "Wienerberger";
            $color = StoneColors::find_by(["colorId" => $lstone->colorId]);
            $size = StoneSizes::find_by(["sizeId" => $lstone->sizeId]);
            $catname .= $brand . " " . $color->name . " " . $size->getSizeString() . " cm (" . intval($size->click) . " cm klikhoogte)";
          }
        }
        $category->content->name = "Steen: " . $catname;

        //een standaard product heeft ook nog links rechts geglazuurd als optie.
        if ($standardproduct) {
          $glaced_left = new Projects();
          $glaced_left->glaced_left = 1;
          $glaced_left->product = $standardproduct;
          $glaced_left->product_id = $standardproduct->id;
          $glaced_left->name = "Geglazuurd link";
          $category->projects[$glaced_left->product_id . '_glacedleft'] = $glaced_left;
          $glaced_right = new Projects();
          $glaced_right->glaced_right = 1;
          $glaced_right->product = $standardproduct;
          $glaced_right->product_id = $standardproduct->id;
          $glaced_right->name = "Geglazuurd rechts";
          $category->projects[$glaced_right->product_id . '_glacedright'] = $glaced_right;
        }

        if (count($category->projects) > 0)
          $this->categories[] = $category;
      }

    }

    private function getAfstandhouders($category_id) {
      //toevoegen ventikliks/afstandhouders
      $category = Category::getCategoryAndContent($category_id);
      $size = StoneSizes::find_by(["sizeId" => $this->quotation->sizeId]);

      if ($category->online_uc == 1 || (SandboxUsers::isAdmin() && $category->online_admin == 1)) {
        $category->projects = [];
        foreach (Product::getProductsByCategoryId(2, $this->online_product_filt) as $product) {
          if ($product->brand_id != "" && $product->brand_id != $this->stone->brandId)
            continue; //moet van merk zijn als gezet
          if ($this->stone->brandId == 2) { //wienerberger, dan juiste maat tonen
            if ($product->id == 680 && $size->click != 2)
              continue; //2cm normale klik
            if ($product->id == 697 && $size->click != 3.5)
              continue; //3.5cm klik forte
            if ($product->id == 696 && $size->click != 4)
              continue; //4cm klik dan hoge nok
          }
          $project = new Projects();
          $project->product_id = $product->id;
          $project->name = $product->content->name;
          $project->product = $product;
          $category->projects[$project->product_id] = $project;
        }
        if (count($category->projects) > 0)
          $this->categories[] = $category;
      }
    }

    private function getColors($category_id) {
      //toevoegen kleuren
      $category = Category::getCategoryAndContent($category_id);
      if ($category->online_uc == 1 || (SandboxUsers::isAdmin() && $category->online_admin == 1)) {
        $category->projects = [];
        $stone_color = StoneColors::find_by(["colorId" => $this->stone->colorId]);
        $color_product = Product::find_by(["supplier_code" => "COLOR_" . $stone_color->colorId, "void" => 0], $this->online_product_filt);
        if ($color_product) {
          $color_product->content = ProductContent::getByProductIdAndLang($color_product->id);
          $project = new Projects();
          $project->product_id = $color_product->id;
          $project->name = $color_product->content->name;
          $project->product = $color_product;
          $category->projects[$project->product_id] = $project;
        }
        if (count($category->projects) > 0) $this->categories[] = $category;
      }

    }

    /**
     * Get cats by id
     * @param        $catId
     * @param string $catname_prefix : extra categoryname
     */
    private function getByCatergoryId($catId, $catname_prefix = "") {
      foreach (Category::getCategoriesAndContent("nl", $this->online_cat_filt . " AND category.id=" . $catId . " ") as $category) {
        $category->projects = [];
        foreach (Product::getProductsByCategoryId($category->id, $this->online_product_filt) as $product) {
          $project = new Projects();
          $project->product_id = $product->id;
          $project->name = $product->content->name;
          $project->product = $product;
          $category->projects[$project->product_id] = $project;
        }
        $category->content->name = $catname_prefix . $category->content->name;
        if (count($category->projects) > 0) $this->categories[] = $category;
      }
    }

  }