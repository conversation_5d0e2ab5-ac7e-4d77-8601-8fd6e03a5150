<?php if($meterprice>0): ?>
  <a href="/offerte?action=wizard&step=1&stoneId=<?php echo $stoneId ?>" class="btn btn-primary call-to-action  animated pulse" id="new_offer">
    <i class="icon-calculator"></i> Kant en klare raamdorpel elementen vanaf <?php echo $meterprice ?> € / meter <i class="fa fa-chevron-right"></i>
  </a>
<?php endif; ?>
<a href="#" class="btn btn-primary call-to-action" id="show_stones">
  Losse raamdorpel stenen per stuk <i class="fa fa-chevron-right"></i>
</a>
<div id="product_orderform">
  <input type="hidden" value="<?php echo $product->id ?>" name="product_id"/>
  <input type="hidden" value="<?php echo $stoneId ?>" name="stoneId"/>
  <?php foreach($stoneproducts as $type=>$sc): ?>
    <div class="product-stone-div">
      <div style="font-weight: bold;width:  200px;">
        <?php echo showHelpButton($sc["help"],$sc["name"]) ?>
        <?php echo $sc["name"] ?>
      </div>
      <div>
        <b>€ <span class="ft-green bold"><span class="stukprijs"><?php echo getLocalePrice($sc["price"]) ?></span> per stuk</span></b>
      </div>
      <div style="margin-left: auto;">
        <?php if($type=="leftmitre" || $type=="rightmitre"): ?>
          Hoek:
          <div class="select" style="width: 80px; display: inline-block;margin-right: 10px;">
            <select name="mitre[<?php echo $type ?>Id]" class="mitre">
              <?php foreach($mitresSelect as $label=>$optgroup): ?>
                <optgroup label="<?php echo $label ?>">
                  <?php foreach($optgroup as $mitre): ?>
                    <option value="<?php echo $mitre->mitreId ?>"><?php echo $mitre->angle ?> °</option>
                  <?php endforeach; ?>
                </optgroup>
              <?php endforeach; ?>
            </select>
          </div>
        <?php endif; ?>
        Aantal:
        <input title="Aantal producten" class="form-input productsize" type="text" value="" name="size[<?php echo $type ?>]" style="width: 70px;"/>
        <div style="font-size: 16px; display:inline-block;padding: 4px;">
          <a href="#" class="mininput fa fa-minus-circle" title="Aantal -1"></a>
          <a href="#" class="plusinput fa fa-plus-circle" title="Aantal +1"></a>
        </div>
      </div>
    </div>
  <?php endforeach; ?>
  <div class="cart-button" style="max-width: 250px;margin-left: auto;margin-top: 10px;">
    <button class="btn btn-cart bg-red" type="submit" name="add" title="<?php echo escapeForInput($product->getName($_SESSION['lang'])) ?>">
      IN WINKELMANDJE
      <i class="fa fa-shopping-cart ft-yellow bg-grey"></i>
    </button>
  </div>
</div>
<div class="row" style="padding-top: 15px;">
  <div class="col7 col12-xs contenttxt">
    <h1>Kant en klare raamdorpel elementen?</h1>

    Gebruik dan onze offerte tool.<br/>
    Dit is een handige tool waarmee u eenvoudig offertes kunt maken.
    Ook voor de complexe raamdorpel elementen met bijvoorbeeld verstek.<br />
    <br />

    <a href="/offerte?action=wizard&step=1&stoneId=<?php echo $stoneId ?>" class="btn btn-primary" id="new_offer"><i class="icon-calculator"></i> RAAMDORPELS OP MAAT</a>

    <Br/><Br/>
    <br/>
  </div>
  <div class="col5 col12-xs" id="usp">
    <h4>Voordelen raamdorpel elementen</h4>
    <ul>
      <li><i class="icon-pencil"></i> Op maat gemaakt</li>
      <li><i class="icon-wrench"></i> Eenvoudige montage</li>
      <li><i class="icon-badge"></i> Volledig ingewassen</li>
      <li><i class="icon-speech"></i> Slijtvaste voegen</li>
      <li><i class="icon-arrow-right"></i> Verstek? Geen probleem</li>
      <li><i class="icon-like"></i> Kant en klaar</li>
      <li><i class="icon-mouse"></i> Eenvoudig online bestellen</li>
    </ul>
    <br/>
  </div>
</div>
<script type="text/javascript">
  var mitrePrices = <?php echo json_encode($mitrePrices) ?>;
  $(document).ready(function(){

    $(".mitre").change(function() {
      var price = mitrePrices[$(this).val()];
      $(this).parents(".product-stone-div").find(".stukprijs").text(decimalNL(price));
    });
    $(".mitre").change();

    $("#show_stones").click(function(e) {
      e.preventDefault();
      // $("#product_orderform").toggle();
      if($("#product_orderform").is(":visible")) {
        $("#product_orderform").fadeOut(100);
      }
      else {
        $("#product_orderform").fadeIn(250);
      }

      // const element =  document.querySelector('#product_orderform')
      // element.classList.add('animated', 'fadeIn')
    });
    $("#product_orderform").hide();

  });
</script>