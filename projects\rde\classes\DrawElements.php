<?php

  class DrawElements {

    public $canvas;
    public $image_width = 850;
    public $image_height = 1684;
    public $margin_top = 50;
    public $margin_left = 40;
    public $margin_bottom = 40;

    public $element_height = 35;
    public $element_width = 128;
    public $element_topline_y_offset = 6;
    public $element_drawing_height = 153;

    /** @var Quotations */
    public $quotation;
    /** @var QuotationsExtra */
    public $quotation_extra;
    /** @var Stones */
    public $stone;
    /** @var StoneSizes */
    public $stoneSize;
    /** @var OrderElementparts */
    public $parts;
    /** @var OrderElements */
    public $elements;

    private $seperateFiles = false;

    public $font = DIR_INCLUDES . 'pdf/fonts/unifont/arial.ttf';
    public $font_bold = DIR_INCLUDES . 'pdf/fonts/unifont/arialbd.ttf';

    /**
     * @return bool
     */
    public function isSeperateFiles() {
      return $this->seperateFiles;
    }

    /**
     * @param bool $seperateFiles
     */
    public function setSeperateFiles($seperateFiles) {
      $this->seperateFiles = $seperateFiles;
    }


    /**
     * @param int $quotationId
     * @param int|boolean $elementId
     * @return int: has drawings
     */
    public function generate($quotationId, $elementId) {
      $this->quotation = Quotations::find_by(["quotationId" => $quotationId]);
      if (!$this->quotation) {
        die("Onbekende quotation");
      }
      $this->quotation_extra = QuotationsExtra::find_by(["quotationId" => $quotationId]);
      $this->stone = Stones::find_by(["stoneId" => $this->quotation->stoneId]);
      $this->stoneSize = StoneSizes::find_by(["sizeId" => $this->stone->sizeId]);
      if ($elementId !== false) {
        $this->elements = OrderElements::find_all_by(["quotationId" => $this->quotation->quotationId, "elementId" => $elementId]);
      }
      else {
        $this->elements = OrderElements::find_all_by(["quotationId" => $this->quotation->quotationId]);
      }
      $this->parts = AppModel::mapObjectIds(OrderElementparts::find_all_by(["quotationId" => $this->quotation->quotationId]), "elementId");

      if ($this->stone->isMuurafdekker()) {
        $this->element_topline_y_offset = 18;
      }
      elseif ($this->stone->isSpekband()) {
        $this->element_topline_y_offset = 21;
      }

      /** @var Mitres */
      $mitres = [];

      foreach ($this->elements as $k => $element) {
        if (!isset($this->parts[$element->elementId])) {
          $show = false;
          if ($element->getMitre() != "none") {
            //er is verstek, tonen.
            $show = true;
          }
          elseif ($element->hasFlagWindowLeft() || $element->hasFlagWindowRight()) {
            //vlagkozijn. Tonen.
            $show = true;
          }
          elseif ($this->quotation->endstone == "true" && ($element->leftEndstone == 0 || $element->rightEndstone == 0)) {
            //eindsteen staat aan op offerte, maar op element niet. Dit is afwijkende, tonen.
            $show = true;
          }
          elseif ($this->quotation->endstone == "true_grooves" && ($element->leftEndstoneGrooves == 0 || $element->rightEndstoneGrooves == 0)) {
            //eindsteen groeven staat aan op offerte, maar op element niet. Dit is afwijkende, tonen.
            $show = true;
          }
          elseif ($this->quotation->endstone == "false" && ($element->leftEndstone == 1 || $element->rightEndstone == 1 || $element->leftEndstoneGrooves == 1 || $element->rightEndstoneGrooves == 1)) {
            //geen eindstenen, maar wel aangeven. Dit is afwijkende, tonen.
            $show = true;
          }

          if (!$show) {
            unset($this->elements[$k]);
          }
        }
      }

      $dir = DIR_ROOT_HTTPDOCS . "filesystem/raamdorpel/clients/quotation/elementparts/" . $this->quotation->quotationId . '/';

      if (count($this->elements) == 0) {
        if ($this->isSeperateFiles()) { //altijd map leeggooien bij genereren
          if (file_exists($dir)) { //directory bestaat, even weggooien
            FileHelper::removeFolderRecursively($dir);
          }
          mkdir($dir);
        }
        return 0;
      }

      foreach ($this->elements as $k => $element) {
        //mitres toevoegen
        if ($element->leftMitreId != "") {
          if (!isset($mitres[$element->leftMitreId])) {
            $mitres[$element->leftMitreId] = Mitres::find_by(["mitreId" => $element->leftMitreId]);
            //            $mitres[$element->leftMitreId] = Mitres::find_by(array("mitreId"=>14)); //150 graden
          }
          $element->leftMitre = $mitres[$element->leftMitreId];
        }
        if ($element->rightMitreId != "") {
          if (!isset($mitres[$element->rightMitreId])) {
            $mitres[$element->rightMitreId] = Mitres::find_by(["mitreId" => $element->rightMitreId]);
          }
          $element->rightMitre = $mitres[$element->rightMitreId];
        }
      }

      if ($this->isSeperateFiles()) {
        $this->margin_top = 10;
        $this->margin_left = 5;
        $this->element_drawing_height = 78;
        $this->image_height = $this->element_drawing_height + $this->margin_top + $this->margin_bottom;

        if ($this->isSeperateFiles()) { //altijd map leeggooien bij genereren
          if (file_exists($dir)) { //directory bestaat, even weggooien
            FileHelper::removeFolderRecursively($dir);
          }
          $result = mkdir($dir);
          if ($result === false) {
            die("Kan map niet aanmaken: " . $dir);
          }
        }

        foreach ($this->elements as $element) {
          //$element->heartClickSize = 0; heartClickSize=1 tot aan de punt heartClickSize=0 = hartklikmaat
          $this->canvas = imagecreatetruecolor($this->image_width, $this->image_height);
          imageantialias($this->canvas, true);
          $this->drawRectangle(0, 0, $this->image_width, $this->image_height);
          imagefilledrectangle($this->canvas, 0, 0, $this->image_width, $this->image_height, imagecolorallocate($this->canvas, 255, 255, 255));

          $this->drawElement($element);
          $this->output($dir . $element->elementId . ".png");
        }
      }
      else {
        $this->image_height = count($this->elements) * $this->element_drawing_height + $this->margin_top + $this->margin_bottom;
        $this->canvas = imagecreatetruecolor($this->image_width, $this->image_height);
        imageantialias($this->canvas, true);
        $this->drawRectangle(0, 0, $this->image_width, $this->image_height);
        imagefilledrectangle($this->canvas, 0, 0, $this->image_width, $this->image_height, imagecolorallocate($this->canvas, 255, 255, 255));

        $rowtel = 0;
        foreach ($this->elements as $element) {

          $this->drawElement($element, $rowtel);
          $rowtel++;
        }
//        ResponseHelper::exit();
        $this->output();
      }

      return count($this->elements);
    }

    /**
     * @param OrderElements $element
     * @param int $rowtel
     */
    public function drawElement($element, $rowtel = 0) {

      /** @var OrderElementparts $partinfo */
      $partinfo = false;
      if (isset($this->parts[$element->elementId])) {
        $partinfo = $this->parts[$element->elementId];
      }

//      dumpe($this);

      $startx = $this->margin_left;
      $starty = $this->margin_top + ($rowtel * $this->element_drawing_height);

      //name
      //$element->referenceName = "134567891234W";
      imagettftext($this->canvas, 12, 0, $startx, $starty + 5, $this->getColor(), $this->font_bold, $element->referenceName);

      $startx += 80;
//      $element->heartClickSize=0;
      $starty += 15;
      if ($element->heartClickSize == 0) {
        $starty += 45;
      }

      $coltel = 0;
      $elementparts = [];
      if ($partinfo === false) { //er is geen deel element informatie beschikbaar
        $epart = [];
        $epart['count'] = $element->amount;
        $epart['length'] = $element->elementLength;
        $epart['lengthfull'] = $element->elementLength;
        if ($element->hasFlagWindowLeft() && !($this->stone->isKeramiek() && $this->stone->isSpekband()) && $this->quotation->brandId != 1 && !$this->stone->isNatuursteen()) {
          $epart['paintleft'] = true;
        }
        if ($element->hasFlagWindowRight() && !($this->stone->isKeramiek() && $this->stone->isSpekband()) && $this->quotation->brandId != 1 && !$this->stone->isNatuursteen()) {
          $epart['paintright'] = true;
        }
        if ($element->leftEndstoneGrooves == 1) {
          $epart['groovesleft'] = true;
        }
        if ($element->rightEndstoneGrooves == 1) {
          $epart['groovesright'] = true;
        }
        if ($element->leftEndstone == 1) {
          $epart['endstoneleft'] = true;
        }
        if ($element->rightEndstone == 1) {
          $epart['endstoneright'] = true;
        }
        if (isset($element->leftMitre)) {
          $epart['leftMitre'] = $element->leftMitre;
          if (($this->stone->isNatuursteen() && $this->stone->isRaamdorpel()) || ($this->stone->isBeton() && $this->stone->isRaamdorpel())) {
            if ($element->heartClickSize == 0) { //hartklik
//              $epart['lengthfull'] += $element->leftMitre->heartLength;
            }
            else { //punt
              $epart['lengthfull'] = $element->elementLengthTotal;
              //$epart['lengthfull'] += $element->leftMitre->longLength;
            }
          }
          elseif (($this->stone->isKeramiek() && $this->stone->isSpekband())) {
            $epart['lengthfull'] += 50;
          }
          elseif ($this->stone->isMuurafdekker()) {
            $epart['lengthfull'] = $element->elementLengthTotal;
          }
          else {
            if ($element->heartClickSize == 0) { //hartklik
              $epart['lengthfull'] += $element->leftMitre->heartLength + $this->quotation->getVoegdikte();
            }
            else { //punt
              $epart['lengthfull'] += $element->leftMitre->longLength + $this->quotation->getVoegdikte();
            }
          }
        }
        if (isset($element->rightMitre)) {
          $epart['rightMitre'] = $element->rightMitre;
          if (($this->stone->isNatuursteen() && $this->stone->isRaamdorpel()) || ($this->stone->isBeton() && $this->stone->isRaamdorpel())) {
            if ($element->heartClickSize == 0) {
              //$epart['lengthfull'] = $element->elementLength;
            }
            else {
              $epart['lengthfull'] = $element->elementLengthTotal;
              //$epart['lengthfull'] += $element->rightMitre->longLength;
            }
          }
          elseif (($this->stone->isKeramiek() && $this->stone->isSpekband())) {
            $epart['lengthfull'] += 50;
          }
          elseif ($this->stone->isMuurafdekker()) {
            $epart['lengthfull'] = $element->elementLengthTotal;
          }
          else {
            if ($element->heartClickSize == 0) {
              $epart['lengthfull'] += $element->rightMitre->heartLength + $this->quotation->getVoegdikte();
            }
            else {
              $epart['lengthfull'] += $element->rightMitre->longLength + $this->quotation->getVoegdikte();
            }
          }
        }

//        pd($element->referenceName);
//        pd($epart['lengthfull']);
//        pd($element->getElementlengthMinSpacing($this->quotation));

        $elementparts[] = $epart;
      }
      else { // er zijn deel elementen
//        dumpe($partinfo);
        //deel 1
        $epart = [];
        $epart['count'] = $partinfo->aAmount1;
        $epart['length'] = $partinfo->aLength1;
        $epart['lengthfull'] = $partinfo->aLength1;
        if ($element->hasFlagWindowLeft()) {
          $epart['paintleft'] = true;
        }
        if ($element->leftEndstoneGrooves == 1) {
          $epart['groovesleft'] = true;
        }
        if ($element->leftEndstone == 1) {
          $epart['endstoneleft'] = true;
        }
        $epart = $this->calcLeftIcon($element, $epart);
        if ($partinfo->bLength1 == "" || $partinfo->bLength1 == 0) {
          $epart = $this->calcRightIcon($element, $epart);
        }

        $elementparts[] = $epart;

        //deel 2
        if ($partinfo->bLength1 != "" && $partinfo->bLength1 != 0) {
          $epart = [];
          $epart['count'] = $partinfo->bAmount1;
          $epart['length'] = $partinfo->bLength1;
          $epart['lengthfull'] = $partinfo->bLength1;
          if ($partinfo->cLength1 == "" || $partinfo->cLength1 == 0) {
            $epart = $this->calcRightIcon($element, $epart);
          }
          $elementparts[] = $epart;
        }

        //deel 3
        if ($partinfo->cLength1 != "" && $partinfo->cLength1 != 0) {
          $epart = [];
          $epart['count'] = $partinfo->cAmount1;
          $epart['length'] = $partinfo->cLength1;
          $epart['lengthfull'] = $partinfo->cLength1;
          if ($partinfo->dLength1 == "" || $partinfo->dLength1 == 0) {
            $epart = $this->calcRightIcon($element, $epart);
          }
          $elementparts[] = $epart;
        }

        //deel 4
        if ($partinfo->dLength1 != "" && $partinfo->dLength1 != 0) {
          $epart = [];
          $epart['count'] = $partinfo->dAmount1;
          $epart['length'] = $partinfo->dLength1;
          $epart['lengthfull'] = $partinfo->dLength1;
          $epart = $this->calcRightIcon($element, $epart);
          $elementparts[] = $epart;
        }

      }

//      pd($elementparts);
//      ResponseHelper::exit();

      foreach ($elementparts as $el_part) {
        $this->drawElementPart($el_part, $element, $startx, $starty, $coltel);
        $coltel++;
      }

    }


    private function drawElementPart($el_part, $element, $startx, $starty, $coltel) {
      if ($this->stone->isNatuursteen() || $this->stone->isBeton()) {
        $this->drawElementPartNatuursteenBeton($el_part, $element, $startx, $starty, $coltel);
        return;
      }
      $this->drawElementPartStandard($el_part, $element, $startx, $starty, $coltel);
    }


    private function drawElementPartNatuursteenBeton($el_part, $element, $startx, $starty, $coltel) {
      $refs = ['A', 'B', 'C', 'D'];

      $colx = $startx + ($coltel * ($this->element_width + 10));

      $el_y = $starty + 10;

      //mitres
      $x_left_totallength_mitre = $colx;
      $x_right_totallength_mitre = $colx + $this->element_width;
      if (isset($el_part['leftMitre'])) {
        $x_left_totallength_mitre -= $this->writeMitreIcon($colx + 5, $el_y, "left", $el_part['leftMitre'], $element->heartClickSize) - 5;
      }
      if (isset($el_part['rightMitre'])) {
        $x_right_totallength_mitre += $this->writeMitreIcon($colx - 5 + $this->element_width - 12, $el_y, "right", $el_part['rightMitre'], $element->heartClickSize) - 5;
      }

      imageline($this->canvas, $colx, $el_y, $colx + $this->element_width, $el_y, $this->getColor());
      imageline($this->canvas, $colx, $el_y + $this->element_height, $colx + $this->element_width, $el_y + $this->element_height, $this->getColor());
      imageline($this->canvas, $colx, $el_y + $this->element_topline_y_offset, $colx + $this->element_width, $el_y + $this->element_topline_y_offset, $this->getColor());

      //in element text
      $fontsize = 10;
      $inel_text = $refs[$coltel] . ' - ' . $el_part["count"] . ' st.';
      $y_text = $starty + 36;
      if ($this->stone->isSpekband()) {
        $y_text = $starty + 26;
      }
      elseif ($this->stone->isMuurafdekker()) {
        $y_text = $starty + 24.5;
      }
      imagettftext($this->canvas, $fontsize, 0, $colx + 25, $y_text, $this->getColor(), $this->font, $inel_text);

      //paint
      if (isset($el_part['paintleft'])) {
        $this->writePaintIcon($colx - 15, $el_y);
      }
      if (isset($el_part['paintright'])) {
        $this->writePaintIcon($colx + $this->element_width + 10, $el_y, "right");
      }

      //groeves
      if (isset($el_part['groovesleft'])) {
        $this->writeGroovesIcon($colx, $el_y);
      }
      if (isset($el_part['groovesright'])) {
        $this->writeGroovesIcon($colx + $this->element_width - 20, $el_y, "right");
      }

      //endstones
      if (isset($el_part['endstoneleft'])) {
        $this->writeEndIcon($colx, $el_y);
      }
      if (isset($el_part['endstoneright'])) {
        $this->writeEndIcon($colx + $this->element_width - 12, $el_y, "right");
      }

      //arrows
      $y_arrow2 = $starty - 5;
      $y_dotted_bottom = $starty + 8;

      $x_arrow2_left = $x_left_totallength_mitre;
      $x_arrow2_right = $x_right_totallength_mitre;

      if (isset($el_part['leftMitre']) || isset($el_part['rightMitre'])) {
        $y_dotted_bottom += $this->element_height;
        if ($this->stone->isMuurafdekker()) {
          if (isset($el_part['leftMitre'])) {
            $x_arrow2_left -= 2 * ($this->element_topline_y_offset / tan(deg2rad($el_part['leftMitre']->angle)));
          }
          if (isset($el_part['rightMitre'])) {
            $x_arrow2_right += 2 * ($this->element_topline_y_offset / tan(deg2rad($el_part['rightMitre']->angle)));
          }
        }
      }

      $this->drawArrow($x_arrow2_left, $y_arrow2, $x_arrow2_right, $y_arrow2, round($el_part['lengthfull']));
      $grey = imagecolorallocate($this->canvas, 158, 158, 158);
      imagedashedline($this->canvas, $x_arrow2_left, $y_arrow2, $x_arrow2_left, $y_dotted_bottom, $grey);
      imagedashedline($this->canvas, $x_arrow2_right, $y_arrow2, $x_arrow2_right, $y_dotted_bottom, $grey);


      if (!isset($el_part['leftMitre'])) {
        imageline($this->canvas, $colx, $el_y, $colx, $el_y + $this->element_height, $this->getColor());
      }
      if (!isset($el_part['rightMitre'])) {
        imageline($this->canvas, $colx + $this->element_width, $el_y, $colx + $this->element_width, $el_y + $this->element_height, $this->getColor());
      }

    }


    private function drawElementPartStandard($el_part, $element, $startx, $starty, $coltel) {
      $refs = ['A', 'B', 'C', 'D'];

      $colx = $startx + ($coltel * ($this->element_width + 10));

      $el_y = $starty + 10;

      //mitres
      $x_left_totallength_mitre = $colx;
      $x_right_totallength_mitre = $colx + $this->element_width;
      if (isset($el_part['leftMitre'])) {
        $x_left_totallength_mitre -= $this->writeMitreIcon($colx, $el_y, "left", $el_part['leftMitre'], $element->heartClickSize);
      }
      if (isset($el_part['rightMitre'])) {
        $x_right_totallength_mitre += $this->writeMitreIcon($colx + $this->element_width - 12, $el_y, "right", $el_part['rightMitre'], $element->heartClickSize);
      }

      //element rectangle
      $this->drawRectangle($colx, $el_y, $this->element_width, $this->element_height);
      imageline($this->canvas, $colx, $el_y + $this->element_topline_y_offset, $colx + $this->element_width, $el_y + $this->element_topline_y_offset, $this->getColor());

      //in element text
      $fontsize = 10;
      $inel_text = $refs[$coltel] . ' - ' . $el_part["count"] . ' st.';
      $y_text = $starty + 36;
      if ($this->stone->isSpekband()) {
        $y_text = $starty + 26;
      }
      elseif ($this->stone->isMuurafdekker()) {
        $y_text = $starty + 24.5;
      }
      imagettftext($this->canvas, $fontsize, 0, $colx + 25, $y_text, $this->getColor(), $this->font, $inel_text);

      //paint
      if (isset($el_part['paintleft'])) {
        $this->writePaintIcon($colx - 15, $el_y);
      }
      if (isset($el_part['paintright'])) {
        $this->writePaintIcon($colx + $this->element_width + 10, $el_y, "right");
      }

      //groeves
      if (isset($el_part['groovesleft'])) {
        $this->writeGroovesIcon($colx, $el_y);
      }
      if (isset($el_part['groovesright'])) {
        $this->writeGroovesIcon($colx + $this->element_width - 20, $el_y, "right");
      }

      //endstones
      if (isset($el_part['endstoneleft'])) {
        $this->writeEndIcon($colx, $el_y);
      }
      if (isset($el_part['endstoneright'])) {
        $this->writeEndIcon($colx + $this->element_width - 12, $el_y, "right");
      }

      //arrows
      $y_arrow1 = $starty - 2;
      $y_arrow2 = $starty - 25;
      if ($element->heartClickSize == 1) { //tot aan de punt
        $y_arrow1 = $starty + $this->element_height + 35;
        $y_arrow2 = $y_arrow1 + 25;
      }

      $first_arrow_txt = '';
      if ($el_part["count"] > 1) {
        $first_arrow_txt .= $el_part["count"] . '  x  ';
      }
      $first_arrow_txt .= round($el_part['length']);
      $this->drawArrow($colx, $y_arrow1, $colx + $this->element_width, $y_arrow1, $first_arrow_txt);

      if (isset($el_part['leftMitre']) || isset($el_part['rightMitre'])) {
        $x_arrow2_left = $x_left_totallength_mitre;
        $x_arrow2_right = $x_right_totallength_mitre;
        if ($this->stone->isSpekband()) {
          if (isset($el_part['leftMitre'])) {
            $x_arrow2_left -= ($this->element_topline_y_offset / tan(deg2rad($el_part['leftMitre']->angle)));
          }
          if (isset($el_part['rightMitre'])) {
            $x_arrow2_right += ($this->element_topline_y_offset / tan(deg2rad($el_part['rightMitre']->angle)));
          }
          $this->drawArrow($x_arrow2_left, $y_arrow2, $x_arrow2_right, $y_arrow2, round($el_part['lengthfull']));
        }
        elseif ($this->stone->isMuurafdekker()) {
          if (isset($el_part['leftMitre'])) {
            $x_arrow2_left -= 2 * ($this->element_topline_y_offset / tan(deg2rad($el_part['leftMitre']->angle)));
          }
          if (isset($el_part['rightMitre'])) {
            $x_arrow2_right += 2 * ($this->element_topline_y_offset / tan(deg2rad($el_part['rightMitre']->angle)));
          }
          $this->drawArrow($x_arrow2_left, $y_arrow2, $x_arrow2_right, $y_arrow2, round($el_part['lengthfull']));
        }
        else {
          $this->drawArrow($x_arrow2_left, $y_arrow2, $x_arrow2_right, $y_arrow2, round($el_part['lengthfull']));
        }


        if (isset($el_part['leftMitre'])) {
          if (($this->stone->isMuurafdekker() || $this->stone->isSpekband()) && $el_part['leftMitre']->angle !== 90) {
            $grey = imagecolorallocate($this->canvas, 158, 158, 158);
            $y_dotted_bottom = $y_arrow1 + $this->element_topline_y_offset + 8;
            if (($this->stone->isKeramiek() && $this->stone->isSpekband()) && $this->quotation->brandId == StoneBrands::BRAND_ID_STJORIS) { //stjoris
              $y_dotted_bottom -= 20;
            }
            elseif ($this->stone->isMuurafdekker()) {
              $y_dotted_bottom += 20;
            }
            imagedashedline($this->canvas, $x_arrow2_left, $y_arrow2 + 8, $x_arrow2_left, $y_dotted_bottom, $grey);
          }
        }
        if (isset($el_part['rightMitre'])) {
          if (($this->stone->isMuurafdekker() || $this->stone->isSpekband()) && $el_part['rightMitre']->angle !== 90) {
            $y_dotted_bottom = $y_arrow1 + $this->element_topline_y_offset + 8;
            if (($this->stone->isKeramiek() && $this->stone->isSpekband()) && $this->quotation->brandId == StoneBrands::BRAND_ID_STJORIS) { //stjoris
              $y_dotted_bottom -= 20;
            }
            elseif ($this->stone->isMuurafdekker()) {
              $y_dotted_bottom += 20;
            }
            $grey = imagecolorallocate($this->canvas, 158, 158, 158);
            imagedashedline($this->canvas, $x_arrow2_right, $y_arrow2 + 8, $x_arrow2_right, $y_dotted_bottom, $grey);
          }
        }

      }

    }

    private function calcLeftIcon($element, $epart) {
      if (!isset($element->leftMitre)) {
        return $epart;
      }

      $epart['leftMitre'] = $element->leftMitre;
      if ($this->stone->isKeramiek()) {
        if ($element->heartClickSize == 0) {
          $epart['lengthfull'] += $element->leftMitre->heartLength + $this->quotation->getVoegdikte();
        }
        else {
          $epart['lengthfull'] += $element->leftMitre->longLength + $this->quotation->getVoegdikte();
        }
      }
      return $epart;
    }

    /**
     * @param OrderElements $element
     * @param array $epart
     * @return array
     */
    private function calcRightIcon($element, $epart) {
      if ($element->hasFlagWindowRight()) {
        $epart['paintright'] = true;
      }
      if ($element->rightEndstoneGrooves == 1) {
        $epart['groovesright'] = true;
      }
      if ($element->rightEndstone == 1) {
        $epart['endstoneright'] = true;
      }
      if (isset($element->rightMitre)) {
        $epart['rightMitre'] = $element->rightMitre;
        if ($this->stone->isKeramiek()) {
          if ($element->heartClickSize == 0) { //hartklik gemeten
            $epart['lengthfull'] += $element->rightMitre->heartLength + $this->quotation->getVoegdikte();
          }
          else {
            $epart['lengthfull'] += $element->rightMitre->longLength + $this->quotation->getVoegdikte();
          }
        }
      }
      return $epart;
    }

    public function drawRectangle($x, $y, $width, $height, $color = false) {
      if (!$color) {
        $color = $this->getColor();
      }
      imagerectangle($this->canvas, $x, $y, $x + $width, $y + $height, $color);
    }

    /**
     * Output generated canvas
     * @param null $filename : path to filename
     */
    public function output($filename = null) {
      if ($filename == null) {
        header('Content-Type: image/png');
        header("Pragma: no-cache");
        header("Cache-Control: no-cache");
      }
      //imagejpeg($this->canvas, $filename, 100);
      imagepng($this->canvas, $filename);
      imagedestroy($this->canvas);
      if ($filename == null) {
        ResponseHelper::exit();
      }
    }

    public function getColor($color = "black") {
      return imagecolorallocate($this->canvas, 0, 0, 0);
    }

    public function writePaintIcon($x, $starty, $variant = "left") {
      $stepsize = 5;
      $starty += 3;
      for ($tel = 0; $tel < 3; $tel++) {
        $y = $starty + ($tel * 10);
        imageline($this->canvas, $x, $y, $x + $stepsize, $y + $stepsize, $this->getColor());
        imageline($this->canvas, $x + $stepsize, $y + $stepsize, $x, $y + (2 * $stepsize), $this->getColor());
      }
      if ($variant == "left") {
        imagettftext($this->canvas, 10, 0, $x - 31, $starty + 13, $this->getColor(), $this->font, "Verf");
      }
      elseif ($variant == "right") {
        imagettftext($this->canvas, 10, 0, $x + 14, $starty + 13, $this->getColor(), $this->font, "Verf");
      }
    }

    public function writeGroovesIcon($x, $y, $variant = "left") {
      $x += 5;
      $y += $this->element_topline_y_offset + 5;
      imageline($this->canvas, $x, $y, $x, $y + $this->element_height - $this->element_topline_y_offset - 10, $this->getColor());
      imageline($this->canvas, $x + 5, $y + 4, $x + 5, $y + $this->element_height - $this->element_topline_y_offset - 13, $this->getColor());
      imageline($this->canvas, $x + 10, $y, $x + 10, $y + $this->element_height - $this->element_topline_y_offset - 10, $this->getColor());

      if ($variant == "left") {
        imagettftext($this->canvas, 10, 0, $x - 79, $y + 20, $this->getColor(), $this->font, "Groeven");
      }
      elseif ($variant == "right") {
        imagettftext($this->canvas, 10, 0, $x + 38, $y + 20, $this->getColor(), $this->font, "Groeven");
      }
    }

    public function writeEndIcon($x, $y, $variant = "left") {
      $white = imagecolorallocate($this->canvas, 255, 255, 255);
      if ($this->stone->isMuurafdekker()) {
        $y_1 = $y + $this->element_height - $this->element_topline_y_offset + 1;
        $y_2 = $y + $this->element_topline_y_offset + $this->element_height - $this->element_topline_y_offset;
        if ($variant == "left") {
          $x_to = $x + ($this->element_height / 2);
          imageline($this->canvas, $x + 1, $y_1, $x_to, $y_1, $white);
          imageline($this->canvas, $x, $y, $x_to, $y_1, $this->getColor());
          imageline($this->canvas, $x_to, $y_1, $x, $y_2, $this->getColor());
          imagettftext($this->canvas, 10, 0, $x - 86, $y + 24, $this->getColor(), $this->font, "Eindsteen");
        }
        elseif ($variant == "right") {
          $x += ($this->element_height / 2) - 5;
          $x_to = $x - ($this->element_height / 2);
          imageline($this->canvas, $x - 1, $y_1, $x_to, $y_1, $white);
          imageline($this->canvas, $x, $y, $x_to, $y_1, $this->getColor());
          imageline($this->canvas, $x_to, $y_1, $x, $y_2, $this->getColor());
          imagettftext($this->canvas, 10, 0, $x + 29, $y + 26, $this->getColor(), $this->font, "Eindsteen");
        }
      }
      else {
        $x += 6;
        $y += $this->element_topline_y_offset;
        $y_to = $y + $this->element_height - $this->element_topline_y_offset;
        imageline($this->canvas, $x, $y, $x, $y_to, $this->getColor());
        if ($variant == "left") {
          imagettftext($this->canvas, 10, 0, $x - 86, $y + 24, $this->getColor(), $this->font, "Eindsteen");
        }
        elseif ($variant == "right") {
          imagettftext($this->canvas, 10, 0, $x + 29, $y + 26, $this->getColor(), $this->font, "Eindsteen");
        }
      }
    }

    /**
     * @param $x
     * @param $y
     * @param string $side left/right
     * @param Mitres $mitre
     * @param int $heartClickSize
     * @return int
     */
    public function writeMitreIcon($x, $y, $side, $mitre, $heartClickSize) {
      if ($this->stone->isSpekband() && $this->quotation->brandId == StoneBrands::BRAND_ID_STJORIS) { //spekband stjoris
        return $this->writeMitreIconSpekbandStjoris($x, $y, $side);
      }
      return $this->writeMitreIconStandard($x, $y, $side, $mitre, $heartClickSize);
    }

    /**
     * @param $x
     * @param $y
     * @param string $side left/right
     * @param Mitres $mitre
     * @param int $heartClickSize
     * @return int
     */
    public function writeMitreIconStandard($x, $y, $side, $mitre, $heartClickSize) {

      //      echo $variant; ResponseHelper::exit();
      //angle tussen de 30 en 150 graden
      //      $mitre->angle = 122;

      $totallength_mitre_part = 0;

      if ($side == "left") {
        $totallength_mitre_part = 5;
        $x += -5;
        $topline_width = 10;
        $topline2_width = 10;
        $bottomline_width = 10;
        if ($mitre->angle < 90) {
          $bottomline_width += $this->element_height / tan(deg2rad($mitre->angle));
          $topline2_width += $this->element_topline_y_offset / tan(deg2rad($mitre->angle));
          if ($heartClickSize == 1) {
            $totallength_mitre_part += $bottomline_width;
          }
          else {
            $totallength_mitre_part += 10;
          }
        }
        else {
          $topline_width += -1 * ($this->element_height / tan(deg2rad($mitre->angle)));
          $topline2_width += -1 * (($this->element_height - $this->element_topline_y_offset) / tan(deg2rad($mitre->angle)));
          if ($heartClickSize == 1) {
            $totallength_mitre_part += 10;
          }
          else {
            $totallength_mitre_part += $topline_width;
          }
        }

        $topline_width = round($topline_width);
        $bottomline_width = round($bottomline_width);

        if (!$this->stone->isNatuursteen() && !$this->stone->isBeton()) {
          imageline($this->canvas, $x, $y, $x, $y + $this->element_height, $this->getColor());
        }
        imageline($this->canvas, $x - $topline_width, $y, $x, $y, $this->getColor());
        //onderlijn, 2e top lijn, berekenend mb. $aanliggende_klein
        if ($this->stone->type != Stones::TYPE_BALKJES) {
          imageline($this->canvas, $x - $topline2_width, $y + $this->element_topline_y_offset, $x, $y + $this->element_topline_y_offset, $this->getColor());
        }

        //onderlijn, berekend mb. $aanliggende
        imageline($this->canvas, $x - $bottomline_width, $y + $this->element_height, $x, $y + $this->element_height, $this->getColor());
        //schuine lijn
        imageline($this->canvas, $x - $bottomline_width, $y + $this->element_height, $x - $topline_width, $y, $this->getColor());

        $textoffset = 22;
        if ($mitre->angle > 50 && $mitre->angle < 138) {
          $textoffset = 52;
        }
        $textY = $y + 25;
        if ($this->stone->isMuurafdekker()) {
          $textY += 7;
          if ($mitre->angle >= 138) {
            $textoffset = 52;
          }
        }
        elseif ($this->stone->isSpekband()) {
          $textY += 8;
          if ($mitre->angle >= 138) {
            $textoffset = 52;
          }
        }
        if (floor($mitre->angle) != 90) {
          imagettftext($this->canvas, 8, 0, $x - $textoffset, $textY, $this->getColor(), $this->font, floor($mitre->angle) . "°");
        }
      }
      elseif ($side == "right") {
        $totallength_mitre_part = 5;
        $x += 17;
        $topline_width = 10;
        $topline2_width = 10;
        $bottomline_width = 10;
        if ($mitre->angle < 90) {
          $bottomline_width += $this->element_height / tan(deg2rad($mitre->angle));
          $topline2_width += $this->element_topline_y_offset / tan(deg2rad($mitre->angle));
          if ($heartClickSize == 1) {
            $totallength_mitre_part += $bottomline_width;
          }
          else {
            $totallength_mitre_part += 10;
          }
        }
        else {
          $topline_width += -1 * ($this->element_height / tan(deg2rad($mitre->angle)));
          $topline2_width += -1 * (($this->element_height - $this->element_topline_y_offset) / tan(deg2rad($mitre->angle)));
          if ($heartClickSize == 1) {
            $totallength_mitre_part += 10;
          }
          else {
            $totallength_mitre_part += $topline_width;
          }
        }

        $topline_width = round($topline_width);
        $bottomline_width = round($bottomline_width);

        if (!$this->stone->isNatuursteen() && !$this->stone->isBeton()) {
          imageline($this->canvas, $x, $y, $x, $y + $this->element_height, $this->getColor());
        }
        imageline($this->canvas, $x + $topline_width, $y, $x, $y, $this->getColor());

        if ($this->stone->type != Stones::TYPE_BALKJES) {
          //onderlijn, 2e top lijn, berekenend mb. $aanliggende_klein
          imageline($this->canvas, $x + $topline2_width, $y + $this->element_topline_y_offset, $x, $y + $this->element_topline_y_offset, $this->getColor());
        }

        //onderlijn, bergekend mb. $aanliggende
        imageline($this->canvas, $x + $bottomline_width, $y + $this->element_height, $x, $y + $this->element_height, $this->getColor());
        //schuine lijn
        imageline($this->canvas, $x + $bottomline_width, $y + $this->element_height, $x + $topline_width, $y, $this->getColor());

        $textoffset = 3;
        if ($mitre->angle > 50 && $mitre->angle < 138) {
          $textoffset = 52;
        }
        $textY = $y + 25;
        if ($this->stone->isMuurafdekker()) {
          $textY += 7;
          if ($mitre->angle >= 138) {
            $textoffset = 52;
          }
        }
        elseif ($this->stone->isSpekband()) {
          $textY += 8;
          if ($mitre->angle >= 138) {
            $textoffset = 52;
          }
        }
        if (floor($mitre->angle) != 90) {
          imagettftext($this->canvas, 8, 0, $x + $textoffset, $textY, $this->getColor(), $this->font, floor($mitre->angle) . "°");
        }
      }
      return $totallength_mitre_part; //this is used for total length
    }

    /**
     * @param $x
     * @param $y
     * @param string $side left/right
     * @return int
     */
    public function writeMitreIconSpekbandStjoris($x, $y, $side) {

      if ($side == "left") {
        $x += -5;
        $topline_width = 14;

        $this->drawCircle(48, 48, $x, $y + $this->element_height - 14);
        $white = imagecolorallocate($this->canvas, 255, 255, 255);
        imagefilledrectangle($this->canvas, $x, $y + 6, $x + 14, $y + 36, $white);
        imagefilledrectangle($this->canvas, $x - 14, $y + 6, $x + 14, $y + 23, $white);

        //rechtse lijn
        imageline($this->canvas, $x, $y, $x, $y + $this->element_height, $this->getColor());
        //top lijn
        imageline($this->canvas, $x - $topline_width, $y, $x, $y, $this->getColor());
        //linkse lijn
        imageline($this->canvas, $x - $topline_width, $y + $this->element_height - 12, $x - $topline_width, $y, $this->getColor());

      }
      elseif ($side == "right") {
        $x += 17;
        $topline_width = 14;

        $this->drawCircle(48, 48, $x, $y + $this->element_height - 14);
        $white = imagecolorallocate($this->canvas, 255, 255, 255);
        imagefilledrectangle($this->canvas, $x - 14, $y + 6, $x, $y + 40, $white);
        imagefilledrectangle($this->canvas, $x, $y + 6, $x + 14, $y + 23, $white);

        //linkse lijn
        imageline($this->canvas, $x, $y, $x, $y + $this->element_height, $this->getColor());
        //top lijn
        imageline($this->canvas, $x + $topline_width, $y, $x, $y, $this->getColor());
        //rechtselijn
        imageline($this->canvas, $x + $topline_width, $y + $this->element_height - 12, $x + $topline_width, $y, $this->getColor());

      }
      return -2; //this is used for total length
    }

    public function drawArrow($x, $y, $tox, $toy, $text = "") {
      $myArrow = new GDArrow();
      $myArrow->image = $this->canvas;
      $myArrow->color = $this->getColor();
      $myArrow->x1 = $x;
      $myArrow->y1 = $y;
      $myArrow->x2 = $tox;
      $myArrow->y2 = $toy;
      $myArrow->angle = 25;
      $myArrow->radius = 5;
      $myArrow->drawGDArrow();

      $myArrow = new GDArrow();
      $myArrow->image = $this->canvas;
      $myArrow->color = $this->getColor();
      $myArrow->x1 = $tox;
      $myArrow->y1 = $toy;
      $myArrow->x2 = $x;
      $myArrow->y2 = $y;
      $myArrow->angle = 25;
      $myArrow->radius = 5;
      $myArrow->drawGDArrow();

      //bars left / right
      imageline($this->canvas, $x, $y - 4, $x, $y + 4, $this->getColor());
      imageline($this->canvas, $tox, $toy - 4, $tox, $toy + 4, $this->getColor());

      if ($text != "") {
        $fontsize = 11;
        $box = imagettfbbox($fontsize, 0, $this->font, $text);
        $min_x = min([$box[0], $box[2], $box[4], $box[6]]);
        $max_x = max([$box[0], $box[2], $box[4], $box[6]]);
//        $min_y = min( array($box[1], $box[3], $box[5], $box[7]) );
//        $max_y = max( array($box[1], $box[3], $box[5], $box[7]) );
        $boxwidth = ($max_x - $min_x);
        $xpostext = $x + (($tox - $x) / 2) - $boxwidth / 2;
        imagettftext($this->canvas, $fontsize, 0, (int)$xpostext, $y - 4, $this->getColor(), $this->font, $text);
      }
    }


    function drawCirclePixel($img, $centerX, $centerY, $deltaX, $deltaY, $color) {
      imagesetpixel($img, $centerX + $deltaX, $centerY + $deltaY, $color);
      imagesetpixel($img, $centerX - $deltaX, $centerY + $deltaY, $color);
      imagesetpixel($img, $centerX + $deltaX, $centerY - $deltaY, $color);
      imagesetpixel($img, $centerX - $deltaX, $centerY - $deltaY, $color);
    }


    public function drawCircle($width, $height, $centerX, $centerY) {
      $img = $this->canvas;

      $color = $this->getColor();
      $radiusX = ($width - 20) / 2;
      $radiusY = ($height - 20) / 2;
      static $maxTransparency = 0x7F; // 127
      $radiusX2 = $radiusX * $radiusX;
      $radiusY2 = $radiusY * $radiusY;
      // upper and lower halves
      $quarter = round($radiusX2 / sqrt($radiusX2 + $radiusY2));
      for ($x = 0; $x <= $quarter; $x++) {
        $y = $radiusY * sqrt(1 - $x * $x / $radiusX2);
        $error = $y - floor($y);
        $transparency = round($error * $maxTransparency);
        $alpha = $color | ($transparency << 24);
        $alpha2 = $color | (($maxTransparency - $transparency) << 24);
        $this->drawCirclePixel($img, $centerX, $centerY, $x, floor($y), $alpha);
        $this->drawCirclePixel($img, $centerX, $centerY, $x, floor($y) + 1, $alpha2);
      }

      // right and left halves
      $quarter = round($radiusY2 / sqrt($radiusX2 + $radiusY2));
      for ($y = 0; $y <= $quarter; $y++) {
        $x = $radiusX * sqrt(1 - $y * $y / $radiusY2);
        $error = $x - floor($x);
        $transparency = round($error * $maxTransparency);
        $alpha = $color | ($transparency << 24);
        $alpha2 = $color | (($maxTransparency - $transparency) << 24);
        $this->drawCirclePixel($img, $centerX, $centerY, floor($x), $y, $alpha);
        $this->drawCirclePixel($img, $centerX, $centerY, floor($x) + 1, $y, $alpha2);
      }

    }


    public static function generatedMitres() {

      $dir_hart = DIR_ROOT_HTTPDOCS . 'images/mitresnew/raamdorpel-hartklik/';
      $dir_punt = DIR_ROOT_HTTPDOCS . 'images/mitresnew/raamdorpel-totaandepunt/';
      $dir_spekband = DIR_ROOT_HTTPDOCS . 'images/mitresnew/spekband/';
      $dir_muurafdekkers = DIR_ROOT_HTTPDOCS . 'images/mitresnew/muurafdekker/';
      $dir_spekband_stjoris = DIR_ROOT_HTTPDOCS . 'images/mitresnew/spekband-stjoris/';
      $dir_balkjes = DIR_ROOT_HTTPDOCS . 'images/mitresnew/balkjes/';

      $mitres = Mitres::find_all("GROUP BY angle");
      $mitre90 = new Mitres();
      $mitre90->angle = 90;
      $mitres[] = $mitre90;
      foreach ($mitres as $mitre) {

        $draw = new DrawElements();

        if (false) {
          //raamdorpel-hartklik - links
          $type = "left";
          $filepath = $dir_hart . $type . "/" . intval($mitre->angle) . ".png";
          $draw->generateMitreImage($mitre, 0, $filepath, $type);
        }

        if (false) {
          //raamdorpel-hartklik - rechts
          $type = "right";
          $filepath = $dir_hart . $type . "/" . intval($mitre->angle) . ".png";
          $draw->generateMitreImage($mitre, 0, $filepath, $type);
        }

        $draw->quotation = new Quotations();
        $draw->stone = new Stones();

        if (false) {
          //raamdorpel-totaanpunt - links
          $draw->stone->type = Stones::TYPE_RAAMDORPEL;
          $draw->stone->material = Stones::MATERIAL_KERAMIEK;
          $type = "left";
          $filepath = $dir_punt . $type . "/" . intval($mitre->angle) . ".png";
          $draw->generateMitreImage($mitre, 1, $filepath, $type);
        }

        if (false) {
          //raamdorpel-totaanpunt - rechts
          $draw->stone->material = Stones::MATERIAL_KERAMIEK;
          $draw->stone->type = Stones::TYPE_RAAMDORPEL;
          $type = "right";
          $filepath = $dir_punt . $type . "/" . intval($mitre->angle) . ".png";
          $draw->generateMitreImage($mitre, 1, $filepath, $type);
        }

        if (false) {
          //muurafdekker - links
          $draw->stone->material = Stones::MATERIAL_KERAMIEK;
          $draw->stone->type = Stones::TYPE_MUURAFDEKKER;
          $type = "left";
          $filepath = $dir_muurafdekkers . $type . "/" . intval($mitre->angle) . ".png";
          $draw->generateMitreImage($mitre, 0, $filepath, $type);
        }

        if (false) {
          //muurafdekker - rechts
          $draw->stone->material = Stones::MATERIAL_KERAMIEK;
          $draw->stone->type = Stones::TYPE_MUURAFDEKKER;
          $type = "right";
          $filepath = $dir_muurafdekkers . $type . "/" . intval($mitre->angle) . ".png";
          $draw->generateMitreImage($mitre, 0, $filepath, $type);
        }

        if (false) {
          //spekband - links
          $draw->stone->material = Stones::MATERIAL_KERAMIEK;
          $draw->stone->type = Stones::TYPE_SPEKBAND;
          $type = "left";
          $filepath = $dir_spekband . $type . "/" . intval($mitre->angle) . ".png";
          $draw->generateMitreImage($mitre, 0, $filepath, $type);
        }

        if (false) {
          //spekband - links
          $draw->stone->material = Stones::MATERIAL_KERAMIEK;
          $draw->stone->type = Stones::TYPE_SPEKBAND;
          $type = "right";
          $filepath = $dir_spekband . $type . "/" . intval($mitre->angle) . ".png";
          $draw->generateMitreImage($mitre, 0, $filepath, $type);
        }

        if (true) {
          //balkjes - links
          $draw->stone->material = Stones::MATERIAL_NATUURSTEEN;
          $draw->stone->type = Stones::TYPE_BALKJES;
          $type = "left";
          $filepath = $dir_balkjes . $type . "/" . intval($mitre->angle) . ".png";
          $draw->generateMitreImage($mitre, 0, $filepath, $type);
        }

        if (true) {
          //balkjes - rechts
          $draw->stone->material = Stones::MATERIAL_NATUURSTEEN;
          $draw->stone->type = Stones::TYPE_BALKJES;
          $type = "right";
          $filepath = $dir_balkjes . $type . "/" . intval($mitre->angle) . ".png";
          $draw->generateMitreImage($mitre, 0, $filepath, $type);
        }

      }

      if (true) {

//        $mitre45 = new Mitres();
//        $mitre45->angle = 45;
//
//        $draw->quotation->offerteVariant = Quotations::VARIANT_KS;
//        $draw->quotation->brandId=1;
//        $type = "left";
//        $filepath = $dir_spekband_stjoris.$type."/" . intval($mitre45->angle).".png";
//        $draw->generateMitreImage($mitre45, 0, $filepath, $type);
//
//
//        $draw->quotation->offerteVariant = Quotations::VARIANT_KS;
//        $draw->quotation->brandId=1;
//        $type = "right";
//        $filepath = $dir_spekband_stjoris.$type."/" . intval($mitre45->angle).".png";
//        $draw->generateMitreImage($mitre45, 0, $filepath, $type);

//        $mitre90 = new Mitres();
//        $mitre90->angle = 90;
//
//        $draw->quotation->offerteVariant = Quotations::VARIANT_KS;
//        $draw->quotation->brandId=1;
//        $type = "left";
//        $filepath = $dir_spekband_stjoris.$type."/90.png";
//        $draw->generateMitreImage($mitre45, 0, $filepath, $type);
//
//        $draw->quotation->offerteVariant = Quotations::VARIANT_KS;
//        $draw->quotation->brandId=1;
//        $type = "right";
//        $filepath = $dir_spekband_stjoris.$type."/90.png";
//        $draw->generateMitreImage($mitre45, 0, $filepath, $type);
      }

    }

    /**
     * @param Mitres $mitre
     * @param $heartclicksize
     * @param $filepath
     * @param $side : left / right
     */
    public function generateMitreImage($mitre, $heartclicksize, $filepath, $side) {

      $this->element_topline_y_offset = 6;
      if ($this->stone->isMuurafdekker()) {
        $this->element_topline_y_offset = 18;
      }
      elseif ($this->stone->isSpekband()) {
        $this->element_topline_y_offset = 21;
      }

      $this->image_width = 80;
      $this->image_height = 60;

      $this->canvas = imagecreatetruecolor($this->image_width, $this->image_height);
      imageantialias($this->canvas, true);
//      imagesavealpha($this->canvas, true);
      $this->drawRectangle(0, 0, $this->image_width, $this->image_height);
      imagefilledrectangle($this->canvas, 0, 0, $this->image_width, $this->image_height, imagecolorallocate($this->canvas, 255, 255, 255));
//      $trans_colour = imagecolorallocatealpha($this->canvas, 0, 0, 0, 127);
//      imagefill($this->canvas, 0, 0, $trans_colour);

      //imagettftext($this->canvas, 11, 0, 0, 10, $this->getColor(), $this->font, "ROBERT");

      $element = new OrderElements();
      $startx = 85;
      $starty = 18 - 45;
      if ($element->heartClickSize == 0) {
        $starty += 45;
      }

      $x_arrow1 = $startx;
      $y_arrow1 = $starty - 10;

      if ($heartclicksize == 1) { //tot aan de punt
        $starty -= 10;
        $y_arrow1 = $starty + $this->element_height + 8;
      }

      if ($this->stone->isMuurafdekker()) {
        $oversteek = 5;
        if ($side == "left") {
          if ($mitre->angle <= 90) {
            $x_arrow1 -= (($this->element_height - $oversteek) / tan(deg2rad($mitre->angle)));
          }
          else {
            $x_arrow1 -= ($oversteek / tan(deg2rad($mitre->angle)));
          }
        }
        elseif ($side == "right") {
          $startx = -18;
          $x_arrow1 = -7;
          if ($mitre->angle <= 90) {
            $x_arrow1 += (($this->element_height - $oversteek) / tan(deg2rad($mitre->angle)));
          }
          else {
            $x_arrow1 += ($oversteek / tan(deg2rad($mitre->angle)));
          }
        }
      }
      elseif ($this->stone->isSpekband()) {
        if ($side == "left") {
          $x_arrow1 -= ($this->element_topline_y_offset / tan(deg2rad($mitre->angle)));
        }
        elseif ($side == "right") {
          $startx = -18;
          $x_arrow1 = -7;
          $x_arrow1 += ($this->element_topline_y_offset / tan(deg2rad($mitre->angle)));
        }
      }
      else {
        if ($side == "left") {
          if ($heartclicksize == 0) {
            $x_arrow1 -= ((8 / tan(deg2rad($mitre->angle))) / 2);
          }
        }
        elseif ($side == "right") {
          $startx = -18;
          $x_arrow1 = -7;
          if ($heartclicksize == 0) {
            if ($mitre->angle == 90) {
              $x_arrow1 += 2;
            }
            else {
              $x_arrow1 += ((8 / tan(deg2rad($mitre->angle))) / 2);
            }
          }
          else {
            $x_arrow1 += 1;
          }
        }
      }

      $width = $this->writeMitreIcon($startx, $starty, $side, $mitre, $heartclicksize);

      if ($side == "left") {
        if (($this->stone->isKeramiek() && $this->stone->isSpekband()) && $this->quotation->brandId == StoneBrands::BRAND_ID_STJORIS) {
          imageline($this->canvas, $startx - 7, $starty, $startx - 7, $starty + $this->element_topline_y_offset, $this->getColor());
        }
        $x_arrow1 -= $width;
        $this->drawArrow($x_arrow1, $y_arrow1, $startx + $this->element_width, $y_arrow1);
      }
      elseif ($side == "right") {
        if (($this->stone->isKeramiek() && $this->stone->isSpekband()) && $this->quotation->brandId == StoneBrands::BRAND_ID_STJORIS) {
          imageline($this->canvas, 0, $starty, 0, $starty + $this->element_topline_y_offset, $this->getColor());
        }
        $x_arrow1 += $width;
        $this->drawArrow(-10, $y_arrow1, $x_arrow1, $y_arrow1);
      }

      if ($this->stone->isSpekband() && $mitre->angle !== 90) {
        $grey = imagecolorallocate($this->canvas, 158, 158, 158);
        $y_dotted_bottom = $y_arrow1 + $this->element_topline_y_offset + 8;
        if (($this->stone->isKeramiek() && $this->stone->isSpekband()) && $this->quotation->brandId == StoneBrands::BRAND_ID_STJORIS) { //stjoris
          $y_dotted_bottom -= 20;
        }
        imagedashedline($this->canvas, $x_arrow1, $y_arrow1 + 8, $x_arrow1, $y_dotted_bottom, $grey);
      }
      elseif ($this->stone->isMuurafdekker() && $mitre->angle !== 90) {
        $grey = imagecolorallocate($this->canvas, 158, 158, 158);
        $oversteek = 5;
        if ($mitre->angle <= 90) {
          $y_dotted_bottom = $starty + $this->element_height - $oversteek;
        }
        else {
          $y_dotted_bottom = $starty;
        }
        imagedashedline($this->canvas, $x_arrow1, $y_arrow1 + 8, $x_arrow1, $y_dotted_bottom, $grey);
      }

      $this->output($filepath);
//      $this->output();
//      ResponseHelper::exit();
    }


  }