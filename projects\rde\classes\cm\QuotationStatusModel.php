<?php

  AppModel::loadBaseClass('BaseQuotationStatus');

  class QuotationStatusModel extends BaseQuotationStatus {

    /**
     * @param $quotationId
     * @param $statusId
     * @param $userId
     */
    public static function register($quotationId, $statusId, $userId) {

      if ($userId == "") return;

      $sm = new QuotationStatus();
      $sm->quotationId = $quotationId;
      $sm->statusId = $statusId;
      $sm->insertUserId = $userId;
      $sm->insertTS = date('Y-m-d H:i:s');
      $sm->save();
    }

    public static function registerBatch($quotationIds, $statusId, $userId) {

      if ($userId == "" || $userId === false || count($quotationIds) == 0) return;

      foreach ($quotationIds as $quotationId) {
        QuotationStatus::register($quotationId, $statusId, $userId);
      }
    }

  }