.qtip-default.qtip-def {
  border: 1px solid #B94A48;
  color: #67767d;
  font-size: 14px;
  line-height: 18px;
  background-color: #ffffff;
  border-radius: 5px 5px;
  padding: 5px;
}

.qtip {
  max-width: 400px !important;
}

.qtip-content ul {
  padding: 0 15px;
  margin: 5px 0;
}

.header-bar {
  padding: 10px;
  margin-top: 20px;
  background: #ce000c;
  color: #ffffff;
}

.qtip-default .qtip-titlebar {
  background: none;
}


#tooltip {
  border-radius: 4px 4px 0 0;
  background-color: white;
  display: none;
  box-shadow: 2px 5px 10px rgb(0 0 0 / 30%);
}

#tooltip-title {
  background-color: #ce000c;
  color: white;
  padding: 10px 15px;
  border-radius: 4px 4px 0 0;
  font-size: 13px;
  font-weight: bold;
}

#tooltip-content {
  background-color: white;
  color: black;
  font-size: 13px;
  padding: 10px 15px;
  border-radius: 4px 4px 0 0;
}

#tooltip-arrow,
#tooltip-arrow::before {
  position: absolute;
  width: 12px;
  height: 12px;
  background: inherit;
  border: 1px solid #b4b4b4;
  z-index: -1;
}

#tooltip-arrow {
  visibility: hidden;
}

#tooltip-arrow::before {
  visibility: visible;
  content: '';
  transform: rotate(45deg);
}

#tooltip[data-popper-placement^='top'] > #tooltip-arrow {
  bottom: -6px;
}

#tooltip[data-popper-placement^='bottom'] > #tooltip-arrow {
  top: -6px;
}

#tooltip[data-popper-placement^='left'] > #tooltip-arrow {
  right: -6px;
}

#tooltip[data-popper-placement^='right'] > #tooltip-arrow {
  left: -6px;
}
