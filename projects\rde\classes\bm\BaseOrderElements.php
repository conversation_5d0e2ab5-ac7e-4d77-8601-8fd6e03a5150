<?php
class BaseOrderElements extends AppModel
{
  const DB_NAME = 'rde_b1mo';
  const TABLE_NAME = 'order_elements';
  const OM_CLASS_NAME = 'OrderElements';
  const columns = ['elementId', 'quotationId', 'referenceName', 'amount', 'stoneAmount', 'inputLength', 'elementLength', 'elementLengthTotal', 'elementWeight', 'elementPrice', 'totalPrice', 'flagWindow', 'flagWindowSide', 'divisionMeasure', 'fitStoneAmount', 'fitStoneLength', 'leftEndstone', 'rightEndstone', 'leftEndstoneGrooves', 'rightEndstoneGrooves', 'leftMitreId', 'rightMitreId', 'heartClickSize'];
  const field_structure = [
    'elementId'                   => ['type' => 'int', 'length' => '12', 'null' => false],
    'quotationId'                 => ['type' => 'int', 'length' => '10', 'null' => false],
    'referenceName'               => ['type' => 'varchar', 'length' => '15', 'null' => false],
    'amount'                      => ['type' => 'smallint', 'length' => '5', 'null' => false],
    'stoneAmount'                 => ['type' => 'smallint', 'length' => '5', 'null' => false],
    'inputLength'                 => ['type' => 'mediumint', 'length' => '7', 'null' => false],
    'elementLength'               => ['type' => 'mediumint', 'length' => '7', 'null' => false],
    'elementLengthTotal'          => ['type' => 'mediumint', 'length' => '7', 'null' => false],
    'elementWeight'               => ['type' => 'decimal', 'length' => '5,2', 'null' => false],
    'elementPrice'                => ['type' => 'decimal', 'length' => '8,2', 'null' => false],
    'totalPrice'                  => ['type' => 'decimal', 'length' => '8,2', 'null' => false],
    'flagWindow'                  => ['type' => 'enum', 'length' => '3', 'null' => false, 'enums' => ['false','single','double']],
    'flagWindowSide'              => ['type' => 'enum', 'length' => '4', 'null' => false, 'enums' => ['none','left','right','both']],
    'divisionMeasure'             => ['type' => 'decimal', 'length' => '4,1', 'null' => false],
    'fitStoneAmount'              => ['type' => 'tinyint', 'length' => '2', 'null' => true],
    'fitStoneLength'              => ['type' => 'tinyint', 'length' => '3', 'null' => true],
    'leftEndstone'                => ['type' => 'boolean', 'length' => '1', 'null' => false],
    'rightEndstone'               => ['type' => 'boolean', 'length' => '1', 'null' => false],
    'leftEndstoneGrooves'         => ['type' => 'boolean', 'length' => '1', 'null' => false],
    'rightEndstoneGrooves'        => ['type' => 'boolean', 'length' => '1', 'null' => false],
    'leftMitreId'                 => ['type' => 'int', 'length' => '5', 'null' => true],
    'rightMitreId'                => ['type' => 'int', 'length' => '5', 'null' => true],
    'heartClickSize'              => ['type' => 'boolean', 'length' => '1', 'null' => false],
  ];

  protected static $primary_key = ['elementId'];
  protected $auto_increment = 'elementId';

  public $elementId, $quotationId, $referenceName, $amount, $stoneAmount, $inputLength, $elementLength, $elementLengthTotal, $elementWeight, $elementPrice, $totalPrice, $flagWindow, $flagWindowSide, $divisionMeasure, $fitStoneAmount, $fitStoneLength, $leftEndstone, $rightEndstone, $leftEndstoneGrooves, $rightEndstoneGrooves, $leftMitreId, $rightMitreId, $heartClickSize;

  function __construct($obj_arr = [], &$error_codes = []) {
    parent::__construct();
    $this->setDefaults();
    if (!empty($obj_arr)) {
      $this->from_array($obj_arr);
      $this->valid_required_fields($error_codes);
    }
  }

  /**
  * Set default object values
  **/
  public function setDefaults() {
    $this->elementLengthTotal = 0;
    $this->flagWindow = 'false';
    $this->flagWindowSide = 'none';
    $this->leftEndstone = 0;
    $this->rightEndstone = 0;
    $this->leftEndstoneGrooves = 0;
    $this->rightEndstoneGrooves = 0;
    $this->heartClickSize = 0;
  }

  /**
   * @param array $conditions (required)
   * @param string $raw_sql (optional)
   * @return OrderElements[]
   **/
  public static function find_all_like($conditions, $raw_sql = '') {
    return parent::generic_find_all_like($conditions, static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param array $conditions (required)
   * @param string $raw_sql (optional)
   * @return OrderElements[]
   **/
  public static function find_all_by($conditions, $raw_sql = '') {
    return parent::generic_find_all_by($conditions, static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param string $raw_sql (optional)
   * @return OrderElements[]
   **/
  public static function find_all($raw_sql = '') {
    return parent::generic_find_all(static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param array $conditions (required)
   * @param string $raw_sql (optional)
   * @return OrderElements
   **/
  public static function find_by($conditions, $raw_sql = '') {
    return parent::generic_find_by($conditions, static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param string $id (required)
   * @param string $raw_sql (optional)
   * @return OrderElements
   **/
  public static function find_by_id($id, $raw_sql = '') {
    return parent::generic_find_by_id($id, static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param array $conditions (required)
   * @param string $raw_sql (optional)
   * @return int
   **/
  public static function count_all_by($conditions, $raw_sql = '') {
    return parent::generic_count_all_by($conditions, static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param array $conditions (required)
   * @param string $raw_sql (optional)
   * @return bool
   **/
  public static function delete_by($conditions, $raw_sql = '') {
    return parent::generic_delete_by($conditions, static::OM_CLASS_NAME, $raw_sql);
  }

}