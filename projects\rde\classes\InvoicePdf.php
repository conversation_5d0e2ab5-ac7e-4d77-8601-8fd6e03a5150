<?php

  class InvoicePdf extends GSDPDF {

    private $show = false;
//    private Quotations $quotations;
    private SandboxUsers $users;
    private CrmInvoiceparties $crmInvoiceparties;
    private Invoices $invoices;

    public function __construct($invoiceId) {
      parent::__construct();

      $this->AddFont('Nunito', '', 'Nunito-Regular.ttf', true);
      $this->AddFont('Nunito', 'B', 'Nunito-Bold.ttf', true);
      $this->SetAutoPageBreak(true, 30);

      $this->invoice = Invoices::find_by(['invoiceId' => $invoiceId]);
      $this->user = SandboxUsers::find_by(['userId' => $this->invoice->userId]);
      $this->crmInvoiceparty = CrmInvoiceparties::find_by(['companyId' => $this->user->companyId]);
      $this->quotations = Quotations::find_all_by(['invoiceId' => $this->invoice->invoiceId]);
      $this->company = CrmCompanies::find_by(['companyId' => $this->user->companyId]);
    }

    /**
     * @return bool
     */
    public function isShow(): bool {
      return $this->show;
    }

    /**
     * @param bool $show
     */
    public function setShow(bool $show): void {
      $this->show = $show;
    }

    public function Header() {
      //$this->Image('C:\Work\www\HTTPDOCS\html\rde\filesystem\raamdorpel\clients\quotation\elementparts\105883\159915.png',null,null,0,0,'png');
      $this->SetFont('Nunito', '', 10);
      $this->Cell(25);
      $this->Cell(0, 5, '', 0, 1, 'L', false);

      $this->SetLeftMargin(104);
      $this->SetY(10.5);
      $this->SetFont('', 'B');
      $this->Cell(91, 4, "Raamdorpelelementen BV", 0, 1, "R");
      $this->SetFont('', '');
      $this->Cell(91, 4, "Raambrug 9", 0, 1, "R");
      $this->Cell(91, 4, "5531 AG Bladel", 0, 1, "R");
      $this->Cell(91, 4, "0497 36 07 91", 0, 1, "R");
      $this->Cell(91, 4, "<EMAIL>", 0, 1, "R");
      $this->Cell(91, 4, "www.raamdorpel.nl", 0, 1, "R");
      $this->Ln();
      $this->Ln(3.5);

      $this->SetX(10);
      $this->SetLeftMargin(10);
    }

    public function Footer() {

//      $this->setY(-25);
//      $this->SetFont('Nunito', 'B', 10);
//      $this->Cell(0, 7, 'Raamdorpel.nl - Raamdorpelelementen BV', 'T', 1, 'L', false);
//
//      $this->SetFont('Nunito', '', 10);
//      $this->SetTextColor(128, 128, 128);
//      $this->Cell(15, 5, 'Adres:');
//      $this->SetTextColor(0, 0, 0);
//      $this->Cell(50, 5, 'Raambrug 9', 0, 0, 'L', false);
//      $this->SetTextColor(128, 128, 128);
//      $this->Cell(25, 5, 'Telefoon:');
//      $this->SetTextColor(0, 0, 0);
//      $this->Cell(35, 5, '0497 - 36.07.91', 0, 0, 'L', false);
//      $this->SetTextColor(128, 128, 128);
//      $this->Cell(25, 5, 'E-mail:');
//      $this->SetTextColor(0, 0, 0);
//      $this->Cell(35, 5, '<EMAIL>', 0, 1, 'L', false);
//
//      $this->Cell(15);
//      $this->Cell(50, 5, '5531 AG Bladel', 0, 0, 'L', false);
//      $this->SetTextColor(128, 128, 128);
//      $this->Cell(25, 5, 'Fax:');
//      $this->SetTextColor(0, 0, 0);
//      $this->Cell(35, 5, '0497 - 38.09.71', 0, 0, 'L', false);
//      $this->SetTextColor(128, 128, 128);
//      $this->Cell(25, 5, 'Website:');
//      $this->SetTextColor(0, 0, 0);
//      $this->Cell(35, 5, 'www.raamdorpel.nl', 0, 1, 'L', false);

    }

    public function startNewPage() {
      $invoice = $this->invoice;
      $invoice_date = $invoice->dateInvoice;
      if (!$invoice->dateInvoice) {
        $invoice_date = date("Y-m-d");
      }
      $invoice_date_new = explode("-", $invoice_date);
      $invoice_date_year = $invoice_date_new[0];
      $invoice_date_month = $invoice_date_new[1];
      $invoice_date_day = $invoice_date_new[2];
      $user = $this->user;
      $expiration_date = date("d-m-Y", mktime(0, 0, 0, $invoice_date_month, (int)$invoice_date_day + $user->payterm, $invoice_date_year));
      $company = $this->company;
      $quotations = $this->quotations;
      $invoice_party = $this->crmInvoiceparty;
      $bankaccount = CrmBankaccounts::find_by(['companyId' => $company->companyId], "AND `debit` = 1");
      $first_page_counter = 1;
      $page_counter = 0;
      $all_page_counter = 0;

      $extra_meter_price = isset($invoice->meterPrice) ? (float)$invoice->meterPrice : 0.0;
      $extra_meter_amount = isset($invoice->meterAmount) ? (float)$invoice->meterAmount : 0.0;
      $invoiceExtraConstructionList = isset($invoice->constructionList) ? (float)$invoice->constructionList : 0.0;

      if(isset($invoice->constructionList) && $invoice->constructionList!=='') {
        $invoiceExtraConstructionList = $invoice->constructionList;
      }
      else {
        $invoiceExtraConstructionList = 0;
      }

      $adres = CrmAddresses::find_by(['addressId' => $company->postAddressId]);
      if (!$company->postAddressId) {
        $adres = CrmAddresses::find_by(['addressId' => empty($invoice_party->differentaddressId) ? $invoice_party->addressId : $invoice_party->differentaddressId]);
      }

      $this->AddPage();
      $this->Image(DIR_PROJECT_FOLDER . 'templates/backend2/images/factuurpapier6.jpg', 0, 0, 210, 297);

      $this->SetFont('Nunito', '', 10);
      $this->Cell(25);
      $this->Cell(0, 5, '', 0, 1, 'L', false);

      $this->SetLeftMargin(104);
      $this->SetY(10.5);
      $this->SetFont('', 'B');
      $this->Cell(91, 4, "Raamdorpelelementen BV", 0, 1, "R");
      $this->SetFont('', '');
      $this->Cell(91, 4, "Raambrug 9", 0, 1, "R");
      $this->Cell(91, 4, "5531 AG Bladel", 0, 1, "R");
      $this->Cell(91, 4, "0497 36 07 91", 0, 1, "R");
      $this->Cell(91, 4, "<EMAIL>", 0, 1, "R");
      $this->Cell(91, 4, "www.raamdorpel.nl", 0, 1, "R");
      $this->Ln();
      $this->Ln(3.5);

      $this->SetX(10);
      $this->SetLeftMargin(10);

      $writeHeight = 5;
      $cellHeight = 5;
      $cellHeightTopRight = 5;
      $xyCoordinatesStart = 49;
      $xyCoordinatesFromLeft = 106;

      $this->SetFont('Arial', '', 12);
      $this->SetDrawColor(50, 50, 50);
      $this->SetTextColor(0, 0, 0);
      $this->SetLeftMargin(10);
      $this->SetTopMargin(0);
      $this->SetFillColor(200, 200, 200);
      $this->SetLineWidth(0.2);
      $this->SetDrawColor(255, 255, 255);

      $this->setXY($xyCoordinatesFromLeft, $xyCoordinatesStart);
      $this->Cell(60, $cellHeightTopRight, $company->name);
      $this->Ln();
      $this->setXY($xyCoordinatesFromLeft, $xyCoordinatesStart + $cellHeightTopRight);
      $this->Cell(60, $cellHeightTopRight, 'T.a.v. ' . $user->firstName . ' ' . $user->lastName);
      $this->Ln();
      $this->setXY($xyCoordinatesFromLeft, $xyCoordinatesStart + ($cellHeightTopRight * 2));
      $this->Cell(60, $cellHeightTopRight, $adres->street . ' ' . $adres->nr . ' ' . $adres->extension);
      $this->Ln();
      $this->setXY($xyCoordinatesFromLeft, $xyCoordinatesStart + ($cellHeightTopRight * 3));
      $this->Cell(60, $cellHeightTopRight, $adres->zipcode . ' ' . $adres->domestic);
      $this->Ln();
      $this->Ln();
      $this->Ln();

      $this->setXY(10, 45);
      $this->SetFont('Arial', '', 10);
      $this->SetFont('', 'B');
      $this->Cell(33, $writeHeight, "");
      $this->Cell(50, $writeHeight, '');
      $this->Ln();
      $this->SetFont('');
      $this->Cell(33, $writeHeight, "Bankrekeningnr.: ");
      $this->Cell(50, $writeHeight, 'NL87 INGB 0009 2363 46');
      $this->Ln();
      $this->SetFont('');
      $this->Cell(33, $writeHeight, "Swift code: ");
      $this->Cell(50, $writeHeight, 'INGB NL 2A');
      $this->Ln();
      $this->Ln();

      //--------------------------------------------------------------------------

      $this->SetFont('Arial', '', 12);
      $this->SetFont('', 'B');
      $this->Cell(35, $writeHeight, "Factuurnummer: ");
      $this->Cell(50, $writeHeight, $invoice->invoiceNumber);
      $this->Ln();
      $this->SetFont('Arial', '', 10);
      $this->SetFont('');
      $this->Cell(33, $writeHeight, "Factuurdatum: ");
      $this->Cell(50, $writeHeight, DateTimeHelper::convertFormat($invoice->dateInvoice, "Y-m-d", "d-m-Y"));
      $this->Ln();
      $this->Cell(33, $writeHeight, "Vervaldatum: ");
      $this->Cell(50, $writeHeight, $expiration_date);
      $this->Ln();
      if (!empty($invoice_party->vatRegNo)) {
        $this->Cell(33, $writeHeight, "Uw BTW nr.: ");
        $this->Cell(50, $writeHeight, $invoice_party->vatRegNo);
      }
      $this->Ln();
      $this->Cell(33, $cellHeight, "Pagina's ", 1);
      $this->Cell(50, $cellHeight, $page_counter . '/' . $all_pagecounter, 1);
      $this->Ln();
      $this->Ln();

    }

    private function addMainpage() {
      $invoice = $this->invoice;
      $invoice_date = $invoice->dateInvoice;
      if (!$invoice->dateInvoice) {
        $invoice_date = date("Y-m-d");
      }
      $invoice_date_new = explode("-", $invoice_date);
      $invoice_date_year = $invoice_date_new[0];
      $invoice_date_month = $invoice_date_new[1];
      $invoice_date_day = $invoice_date_new[2];
      $user = $this->user;
      $expiration_date = date("d-m-Y", mktime(0, 0, 0, $invoice_date_month, (int)$invoice_date_day + $user->payterm, $invoice_date_year));
      $company = $this->company;
      $quotations = $this->quotations;
      $invoice_party = $this->crmInvoiceparty;
      $bankaccount = CrmBankaccounts::find_by(['companyId' => $company->companyId], "AND `debit` = 1");
      $first_page_counter = 1;
      $page_counter = 0;
      $all_page_counter = 0;

      $extra_meter_price = isset($invoice->meterPrice) ? (float)$invoice->meterPrice : 0.0;
      $extra_meter_amount = isset($invoice->meterAmount) ? (float)$invoice->meterAmount : 0.0;
      $invoiceExtraConstructionList = isset($invoice->constructionList) ? (float)$invoice->constructionList : 0.0;

      if(isset($invoice->constructionList) && $invoice->constructionList!=='') {
        $invoiceExtraConstructionList = $invoice->constructionList;
      }
      else {
        $invoiceExtraConstructionList = 0;
      }

      $adres = CrmAddresses::find_by(['addressId' => empty($invoice_party->differentaddressId) ? $invoice_party->addressId : $invoice_party->differentaddressId]);

      $this->AddPage();
      $this->Image(DIR_PROJECT_FOLDER . 'templates/backend2/images/factuurpapier6.jpg', 0, 0, 210, 297);

      $this->SetFont('Nunito', '', 10);
      $this->Cell(25);
      $this->Cell(0, 5, '', 0, 1, 'L', false);

      $this->SetLeftMargin(104);
      $this->SetY(10.5);
      $this->SetFont('', 'B');
      $this->Cell(91, 4, "Raamdorpelelementen BV", 0, 1, "R");
      $this->SetFont('', '');
      $this->Cell(91, 4, "Raambrug 9", 0, 1, "R");
      $this->Cell(91, 4, "5531 AG Bladel", 0, 1, "R");
      $this->Cell(91, 4, "0497 36 07 91", 0, 1, "R");
      $this->Cell(91, 4, "<EMAIL>", 0, 1, "R");
      $this->Cell(91, 4, "www.raamdorpel.nl", 0, 1, "R");
      $this->Ln();
      $this->Ln(3.5);

      $this->SetX(10);
      $this->SetLeftMargin(10);

      $this->SetCreator('raamdorpel.nl');
      $this->SetAuthor('Raamdorpelelementen BV');
      $this->SetLeftMargin(10);
      $this->SetTopMargin(0);

      $writeHeight = 5;
      $cellHeight = 5;
      $cellHeightTopRight = 5;
      $xyCoordinatesStart = 49;
      $xyCoordinatesFromLeft = 106;

      $this->SetFont('Arial', '', 12);
      $this->SetDrawColor(50, 50, 50);
      $this->SetTextColor(0, 0, 0);
      $this->SetLeftMargin(10);
      $this->SetTopMargin(0);
      $this->SetFillColor(200, 200, 200);
      $this->SetLineWidth(0.2);
      $this->SetDrawColor(255, 255, 255);

      $this->setXY($xyCoordinatesFromLeft, $xyCoordinatesStart);
      $this->Cell(60, $cellHeightTopRight, $company->name);
      $this->Ln();
      $this->setXY($xyCoordinatesFromLeft, $xyCoordinatesStart + $cellHeightTopRight);
      $this->Cell(60, $cellHeightTopRight, 'T.a.v. ' . $user->firstName . ' ' . $user->lastName);
      $this->Ln();
      $this->setXY($xyCoordinatesFromLeft, $xyCoordinatesStart + ($cellHeightTopRight * 2));
      $this->Cell(60, $cellHeightTopRight, $adres->street . ' ' . $adres->nr . ' ' . $adres->extension);
      $this->Ln();
      $this->setXY($xyCoordinatesFromLeft, $xyCoordinatesStart + ($cellHeightTopRight * 3));
      $this->Cell(60, $cellHeightTopRight, $adres->zipcode . ' ' . $adres->domestic);
      $this->Ln();
      $this->Ln();
      $this->Ln();

      $this->setXY(10, 45);
      $this->SetFont('Arial', '', 10);
      $this->SetFont('', 'B');
      $this->Cell(33, $writeHeight, "");
      $this->Cell(50, $writeHeight, '');
      $this->Ln();
      $this->SetFont('');
      $this->Cell(33, $writeHeight, "Bankrekeningnr.: ");
      $this->Cell(50, $writeHeight, 'NL87 INGB 0009 2363 46');
      $this->Ln();
      $this->SetFont('');
      $this->Cell(33, $writeHeight, "Swift code: ");
      $this->Cell(50, $writeHeight, 'INGB NL 2A');
      $this->Ln();
      $this->Ln();

      //--------------------------------------------------------------------------

      $this->SetFont('Arial', '', 12);
      $this->SetFont('', 'B');
      $this->Cell(35, $writeHeight, "Factuurnummer: ");
      $this->Cell(50, $writeHeight, $invoice->invoiceNumber);
      $this->Ln();
      $this->SetFont('Arial', '', 10);
      $this->SetFont('');
      $this->Cell(33, $writeHeight, "Factuurdatum: ");
      $this->Cell(50, $writeHeight, DateTimeHelper::convertFormat($invoice->dateInvoice, "Y-m-d", "d-m-Y"));
      $this->Ln();
      $this->Cell(33, $writeHeight, "Vervaldatum: ");
      $this->Cell(50, $writeHeight, $expiration_date);
      $this->Ln();
      if (!empty($invoice_party->vatRegNo)) {
        $this->Cell(33, $writeHeight, "Uw BTW nr.: ");
        $this->Cell(50, $writeHeight, $invoice_party->vatRegNo);
      }
      $this->Ln();

      $total_amount_added = 0;

      $special_freight_cost = [];
      $special_freight_counter = 0;
      $special_freight_cost_check_flag = false;
      $quotation_already_get = [];
      $overal_counter = 0;
      $overal_counter_max = 20;

      foreach ($quotations as $key => $quotation) {

        if ($overal_counter != 0 && ($overal_counter % $overal_counter_max) == 0) {
          $first_page_counter++;
        }
        $overal_counter++;
        $special_freight_cost_check = $quotation->specialFreightCost;

        if ($special_freight_cost_check == 1) {
          $special_freight_cost[$special_freight_counter]['quotationId'] = $quotation->quotationId;
          $special_freight_cost[$special_freight_counter]['price'] = $quotation->specialFreightCostPrice * 100;
          $special_freight_counter++;
          $special_freight_cost_check_flag = true;
        }

        $projects_by_quotation[] = Projects::find_by(['quotationId' => $quotation->quotationId]);
        if (count($projects_by_quotation) > 0) {
          foreach ($projects_by_quotation as $project) {
            if ($overal_counter != 0 && ($overal_counter % $overal_counter_max) == 0) {
              $first_page_counter++;
            }
            $overal_counter++;
          }
        }

        $matting_removal_discount_flag = $quotation->mattingRemovalDiscountFlag;
        if ($matting_removal_discount_flag == 1) {
          if ($overal_counter != 0 && ($overal_counter % $overal_counter_max) == 0) {
            $first_page_counter++;
          }
        }

        $matting_only_glue_flag = $quotation->mattingOnlyGlueFlag;

        if ($matting_only_glue_flag == 1) {
          if ($overal_counter != 0 && ($overal_counter % $overal_counter_max) == 0) {
            $first_page_counter++;
          }
        }
      }

      $projects_by_invoice = Projects::find_by(['invoiceId' => $invoice->invoiceId]);
      if ($projects_by_invoice != false) {
        foreach ($projects_by_invoice as $project) {
          if ($overal_counter != 0 && ($overal_counter % $overal_counter_max) == 0) {
            $first_page_counter++;
          }
          $overal_counter++;
        }
      }

      if ($extra_meter_price > 0) {
        if ($overal_counter != 0 && ($overal_counter % $overal_counter_max) == 0) {
          $first_page_counter++;
        }
        $overal_counter++;
      }

      $highest_price = 0;
      $highest_quotation_id = 0;
      for ($i = 0; $i < count($special_freight_cost); $i++) {
        if ($highest_price < $special_freight_cost[$i]['price']) {
          $highest_price = $special_freight_cost[$i]['price'];
          $highest_quotation_id = $special_freight_cost[$i]['quotationId'];
        }
      }

      if ($highest_price > 0) {
        if ($overal_counter != 0 && ($overal_counter % $overal_counter_max) == 0) {
          $first_page_counter++;
        }
        $overal_counter++;
      }

      if ($special_freight_cost_check_flag && $highest_price == 0) {
        if ($overal_counter != 0 && ($overal_counter % $overal_counter_max) == 0) {
          $first_page_counter++;
        }
        $overal_counter++;
      }

      //-- deze moet gereset worden.
      $overal_counter = 0;
      $groepArray = [];

      $all_page_counter = $first_page_counter;

      $this->Cell(33, $cellHeight, "Pagina's ", 1);
      $this->Cell(50, $cellHeight, $page_counter . '/' . $all_page_counter, 1);
      $this->Ln();
      $this->Ln();

      $cellHeight = 6;
      $this->SetDrawColor(255, 255, 255);
      $this->SetFont('Arial', '', 12);
      $this->SetDrawColor(50, 50, 50);

      $cellWidthAantal = 15;
      $cellWidthPrijsExBtw = 24;
      $cellWidthPrijsTotal = 30;
      $cellWidthProjectNr = 28;
      $cellWidthProjectNaam = 93;

      //-- eerste rij
      $this->setXY(10, 95);

      $this->SetFont('Arial', 'B', 10);
      $this->Cell($cellWidthProjectNr, $cellHeight, "Projectnr.", 0, 0, "", true);
      $this->Cell($cellWidthProjectNaam, $cellHeight, "Projectnaam", 0, 0, "", true);
      $this->Cell($cellWidthPrijsExBtw, $cellHeight, "Prijs ex. BTW", 0, 0, "", true);
      $this->Cell($cellWidthAantal, $cellHeight, "Aantal", 0, 0, "R", true);
      $this->Cell($cellWidthPrijsTotal, $cellHeight, "Totaal prijs", 0, 0, "R", true);
      $this->Ln();
      $this->SetFont('Arial', '');

      //100% webshop

      $session_total_price_sum = '';

      $stone = Stones::find_by(['stoneId' => $quotation->stoneId]);
      if ($stone) {
        if ($stone->stoneId === '0') {
          $invoice_id = $quotation->quotationNumber;
          $invoice_split = $quotation->quotationPart;
          $invoice_order_number = $quotation->quotationVersion;

          $invoice_id_complete = $invoice_id . "." . $invoice_order_number;
          if ($invoice_split > 0) {
            $invoice_id_complete .= " - " . $oClsConvert->convertSplitNumberToCharachter($invoice_split);
          }

          $this->Cell($cellWidthProjectNr, $cellHeight, $invoice_id_complete, 0, 0, "", true);

          $full_project_info = $quotation->projectName;
          if ($quotation->projectReference !== '') {
            $full_project_info .= ' - ' . $quotation->projectReference;
          }

          $this->Cell($cellWidthProjectNaam, $cellHeight, $full_project_info, 0, 0, "", true);

          if($invoiceExtraConstructionList === '') {
            $invoiceExtraConstructionList = 0;
          }
          $total_amount_added = $total_amount_added + $invoiceExtraConstructionList;

          $this->Ln();
          $this->SetDrawColor(255, 255, 255);
        }
      }

      if ($invoiceExtraConstructionList > 0) {
        $invoiceExtraConstructionList = number_format((float)$invoiceExtraConstructionList, 2, '.', '');
        $invoiceExtraConstructionListComma = str_replace(".", ",", $invoiceExtraConstructionList);
        $pdf->setXY(133, 100);
        //-- Prijs ex. BTW
        //-- $cellWidthPrijsExBtw = 21
        $pdf->Cell($cellWidthPrijsExBtw, $cellHeight, $invoiceExtraConstructionListComma, 0, 0, "R", true);
        //-- Aantal
        //-- $cellWidthAantal
        $pdf->Cell($cellWidthAantal, $cellHeight, '1', 0, 0, "R", true);
        $pdf->Cell($cellWidthPrijsTotal, $cellHeight, $invoiceExtraConstructionListComma, 0, 0, "R", true);
        $pdf->Ln();
        $total_amount_added = (float)$total_amount_added + (float)$invoiceExtraConstructionList;
        $pdf->SetDrawColor(255, 255, 255);
      }
      else {
        $special_freight_cost = [];

        foreach ($quotations as $key => $quotation) {

          $invoice_id = $quotation->quotationNumber;
          $invoice_split = $quotation->quotationPart;
          $invoice_order_number = $quotation->quotationVersion;

          if ($invoice_split > 0) {
            $invoice_id_complete = $invoice_id . "." . $invoice_order_number . " - " . $this->convertSplitNumberToCharachter($invoice_split);
          }
          else {
            $invoice_id_complete = $invoice_id . "." . $invoice_order_number;
          }

          //-- hoofdprijs element
          if ($quotation->projectValue !== '0.00') {

            if ($overal_counter != 0 && ($overal_counter % $overal_counter_max) == 0) {
              $page_counter++;
              $this->startNewPage();
            }
            $overal_counter++;
            $special_freight_cost_check = $quotation->specialFreightCost;

            if ($special_freight_cost_check == 1) {
              $special_freight_cost_price = $quotation->specialFreightCostPrice;
              $special_freight_cost[$special_freight_counter]['quotationId'] = $quotation->quotationId;
              $special_freight_cost[$special_freight_counter]['price'] = $quotation->specialFreightCostPrice * 100;
              $special_freight_counter++;
              $special_freight_cost_check_flag = true;
            }

            $project_name = $quotation->projectName;
            $project_reference = $quotation->projectReference;
            //-- deze is altijd 1 behalve als er gelegd wordt.
            $session_amount = 1;
            $session_total_price_sum = $quotation->projectValue;

            $session_total_price = number_format((float)$session_total_price_sum, 2, '.', '');
            $session_total_price_sum = number_format((float)$session_total_price_sum, 2, '.', '');

            $this->SetFillColor(235, 235, 235);
            // Projectnr
            $this->Cell($cellWidthProjectNr, $cellHeight, $invoice_id_complete, 0, 0, "", true);
            if ($project_reference != '') {
              $name_value = $project_reference . " - " . $project_name;
            }
            else {
              $name_value = $project_name;
            }

            $this->Cell($cellWidthProjectNaam, $cellHeight, $name_value, 0, 0, "", true);
            $this->Cell($cellWidthPrijsExBtw, $cellHeight, StringHelper::asMoney($session_total_price), 0, 0, "R", true);
            $this->Cell($cellWidthAantal, $cellHeight, $session_amount, 0, 0, "R", true);
            $this->Cell($cellWidthPrijsTotal, $cellHeight, StringHelper::asMoney($session_total_price_sum), 0, 0, "R", true);
            $this->Ln();

            $total_amount_added += (float)$session_total_price_sum;

          }

          $projects = Projects::find_all_by(['quotationId' => $quotation->quotationId]);

          if (count($projects) > 0) {
            foreach ($projects as $project_key => $project) {

              if ($overal_counter != 0 && ($overal_counter % $overal_counter_max) == 0) {
                $page_counter++;
                $this->startNewPage();
              }

              $this->Cell($cellWidthProjectNr, $cellHeight, $invoice_id_complete, 0, 0, "", true);
              $this->Cell($cellWidthProjectNaam, $cellHeight, $project->name, 0, 0, "", true);

              $price_per_piece = $project->pieceprice;
              if ($project->pieceprice === '0.00') {
                $price_per_piece = $project->euro;
              }

              $this->Cell($cellWidthPrijsExBtw, $cellHeight, StringHelper::asMoney($price_per_piece), 0, 0, "R", true);

              //-- aantal van projects.size
              $this->Cell($cellWidthAantal, $cellHeight, $project->size, 0, 0, "R", true);

              //-- Project data
              $this->Cell($cellWidthPrijsTotal, $cellHeight, StringHelper::asMoney($project->euro), 0, 0, "R", true);
              $this->Ln();
              $total_amount_added += $project->euro;

              $overal_counter++;

            }
          }

          if ($quotation->mattingRemovalDiscountFlag == 1) {
            $matting_removal_discount = $quotation->mattingRemovalDiscount;
            $matting_removal_discount = str_replace(".", "", $matting_removal_discount);
            $matting_removal_discount = (float) $matting_removal_discount / 100;
            $matting_removal_discount = number_format($matting_removal_discount, 2, '.', '');

            if ($overal_counter != 0 && ($overal_counter % $overal_counter_max) == 0) {
              $page_counter++;
              $this->startNewPage();
            }

            $this->Cell($cellWidthProjectNr, $cellHeight, $invoice_id_complete, 0, 0, "", true);
            $this->Cell($cellWidthProjectNaam, $cellHeight, 'afhaal korting', 0, 0, "", true);
            $this->Cell($cellWidthPrijsExBtw, $cellHeight, '', 0, 0, "R", true);
            $this->Cell($cellWidthAantal, $cellHeight, '', 0, 0, "R", true);

            $this->Cell($cellWidthPrijsTotal, $cellHeight, StringHelper::asMoney($matting_removal_discount), 0, 0, "R", true);
            $this->Ln();

            $total_amount_added += (float)$matting_removal_discount;
          }

          $matting_only_glue_flag = $quotation->mattingOnlyGlueFlag;
          $matting_only_glue = $quotation->mattingOnlyGlue;
          $matting_only_glue = str_replace(".", "", $matting_only_glue);

//        $session_price_until_cents = $session_total_price_sum;
//
//        $session_price_until_cents = str_replace(",", "", $session_price_until_cents);
          $session_price_until_cents = str_replace(".", "", $session_total_price_sum);

          $matting_only_glue_end_price = (int)$matting_only_glue - (int)$session_price_until_cents;
          $matting_only_glue_end_price = $matting_only_glue_end_price / 100;

          $matting_only_glue_end_price = number_format((float)$matting_only_glue_end_price, 2, '.', '');

          if ($matting_only_glue_flag == 1) {

            if ($overal_counter != 0 && ($overal_counter % $overal_counter_max) == 0) {
              $page_counter++;
              $this->startNewPage();
            }

            $this->Cell($cellWidthProjectNr, $cellHeight, $invoice_id_complete, 0, 0, "", true);
            $this->Cell($cellWidthProjectNaam, $cellHeight, 'alleen verlijmen...', 0, 0, "", true);
            $this->Cell($cellWidthPrijsExBtw, $cellHeight, '', 0, 0, "R", true);
            $this->Cell($cellWidthAantal, $cellHeight, '', 0, 0, "R", true);
            $this->Cell($cellWidthPrijsTotal, $cellHeight, StringHelper::asMoney($matting_only_glue_end_price), 0, 0, "R", true);
            $this->Ln();

            $total_amount_added += (float)$matting_only_glue_end_price;

          }
        }
      }


      //-- dit is project data. Deze wordt niet altijd gevuld
      //-- bijvoorbeeld als er geen vracht kosten zijn.

      if ($projects_by_invoice) {
        foreach ($projects_by_invoice as $project_key => $project) {

          if ($overal_counter != 0 && ($overal_counter % $overal_counter_max) == 0) {
            $page_counter++;
            $this->startNewPage();
          }

          $overal_counter++;

          //-- met deze waarde kan ik kijken welke projecten zijn gekoppeld.
          //-- ik heb de quotationId nodig.
          $this->Cell($cellWidthProjectNr, $cellHeight, "", 0, 0, "", true);
          $this->Cell($cellWidthProjectNaam, $cellHeight, $project->name, 0, 0, "", true);
          $this->Cell($cellWidthPrijsExBtw, $cellHeight, StringHelper::asMoney($project->euro), 0, 0, "R", true);
          $this->Cell($cellWidthAantal, $cellHeight, "", 0, 0, "R", true);
          $this->Cell($cellWidthPrijsTotal, $cellHeight, StringHelper::asMoney($project->euro), 0, 0, "R", true);
          $this->Ln();
          $total_amount_added += $project->euro;

        }
      }

      if ($extra_meter_price > 0) {

        if ($overal_counter != 0 && ($overal_counter % $overal_counter_max) == 0) {
          $page_counter++;
          $this->startNewPage();
        }

        $overal_counter++;

        $total_meter_price = $extra_meter_price * $extra_meter_amount;
        $this->Cell($cellWidthProjectNr, $cellHeight, "-", 0, 0, "", true);
        $this->Cell($cellWidthProjectNaam, $cellHeight, "Leggen elementen (prijs per meter)", 0, 0, "", true);
        $this->Cell($cellWidthPrijsExBtw, $cellHeight, StringHelper::asMoney($extra_meter_price), 0, 0, "R", true);
        $this->Cell($cellWidthAantal, $cellHeight, $extra_meter_amount, 0, 0, "R", true);
        $this->Cell($cellWidthPrijsTotal, $cellHeight, StringHelper::asMoney($total_meter_price), 0, 0, "R", true);
        $this->Ln();
        $total_amount_added += $total_meter_price;

      }

      $extra_shipping_costs = $invoice->freightCosts;
      $shipping_costs = number_format((float)$extra_shipping_costs, 2, '.', '');

      $this->SetLineWidth(0.2);
      $this->SetDrawColor(0, 0, 0);

      //-- Vrachtkosten
      $this->Cell(160, $cellHeight, "Vrachtkosten", 0, 0, "R", false);
      $this->Cell($cellWidthPrijsTotal, $cellHeight, StringHelper::asMoney($shipping_costs), "T", 0, "R", true);
      $this->Ln();
      $count_special_freight_cost = count($special_freight_cost);

      $highestPrice = 0;
      $highestQuotationId = 0;
      for ($i = 1; $i < $count_special_freight_cost + 1; $i++) {
        if ($highestPrice < $special_freight_cost[$i]['price']) {
          $highestPrice = $special_freight_cost[$i]['price'];
          $highestQuotationId = $special_freight_cost[$i]['quotationId'];
        }
      }

      if ($highestPrice > 0) {

        if ($overal_counter != 0 && ($overal_counter % $overal_counter_max) == 0) {
          $page_counter++;
          $this->startNewPage();
        }

        $overal_counter++;

        //-- show vrachtkosten.
        $shipping_costs_temp = str_replace(".", "", $shipping_costs);
        $shipping_costs_temp = (int)$shipping_costs_temp;
        $agreed_shipping_costs = $highestPrice - $shipping_costs_temp;
        $agreed_shipping_costs = $agreed_shipping_costs / 100;
        $agreed_shipping_costs_string = number_format((float)$agreed_shipping_costs, 2, '.', '');
        $total_amount_added = $total_amount_added + $agreed_shipping_costs;
        $this->Cell(160, $cellHeight, "Afgesproken vrachtkosten", 0, 0, "R", false);
        $this->Cell($cellWidthPrijsTotal, $cellHeight, StringHelper::asMoney($agreed_shipping_costs_string), 0, 0, "R", true);
        $this->Ln();

      }

      if ($special_freight_cost_check_flag && $highestPrice == 0) {

        if ($overal_counter != 0 && ($overal_counter % $overal_counter_max) == 0) {
          $page_counter++;
          $this->startNewPage();
        }

        $overal_counter++;

        $shipping_costs_temp = str_replace(".", "", $shipping_costs);
        $shipping_costs_temp = (int)$shipping_costs_temp;
        //-- bij 0 moet er -50 komen
        $agreed_shipping_costs = 0 - $shipping_costs_temp;
        $agreed_shipping_costs = $agreed_shipping_costs / 100;
        $agreed_shipping_costs_string = number_format((float)$agreed_shipping_costs, 2, '.', '');
        $total_amount_added = $total_amount_added + $agreed_shipping_costs;
        $this->Cell(160, $cellHeight, "Afgesproken vrachtkosten", 0, 0, "R", false);
        $this->Cell($cellWidthPrijsTotal, $cellHeight, StringHelper::asMoney($agreed_shipping_costs_string), 0, 0, "R", true);
        $this->Ln();

      }

      $total_amount_added += (float)$shipping_costs;

      if (!empty($invoice->mattingPickupDiscount)) {

        if ($overal_counter != 0 && ($overal_counter % $overal_counter_max) == 0) {
          $page_counter++;
          $this->startNewPage();
        }

        $overal_counter++;

        $pickup_discount = number_format((float)$invoice->mattingPickupDiscount, 2, '.', '');
        $this->Cell(160, $cellHeight, "Afhaalkorting", 0, 0, "R");
        $this->Cell($cellWidthPrijsTotal, $cellHeight, StringHelper::asMoney($pickup_discount), 0, 0, "R", true);
        $this->Cell(3, $cellHeight, "-", 0, 0, "R");
        $this->Ln();
        $total_amount_added = $total_amount_added - $invoice->mattingPickupDiscount;

      }

      //-- subtotal
      $subtotal_amount = $total_amount_added;
      $subtotal_amount = number_format((float)$subtotal_amount, 2, '.', '');
      $this->Cell(160, $cellHeight, "Subtotaal", 0, 0, "R");
      $this->Cell($cellWidthPrijsTotal, $cellHeight, StringHelper::asMoney($subtotal_amount), "T", 0, "R", true);
      $this->Ln();

      //-- nieuwe belasting tarief van 21%
      //-- per 01-10-2012
      $exp_date = "2012-10-01";
      $invoice_date = strtotime($invoice_date);
      $expiration_date_2 = strtotime($exp_date);

      //-- check if BTW has been set.
      $vat_reg_shifted = VatRates::find_by(['id' => $invoice->vatRegShifted]);

      if ($invoice_date >= $expiration_date_2) {
        $vat_rate_math = $vat_reg_shifted->vatRate / 100;
        $btw = $subtotal_amount * $vat_rate_math;
        $btw_string = 'BTW ' . $vat_reg_shifted->vatRate . '%';
      }
      else {
        //-- alle oude offertes zijn 19% BTW
        $btw = $subtotal_amount * 0.19;
        $btw_string = 'BTW 19%';
      }

      $btw = number_format((float)$btw, 2, '.', '');
      $this->Cell(160, $cellHeight, $btw_string, 0, 0, "R");
      $this->Cell($cellWidthPrijsTotal, $cellHeight, StringHelper::asMoney($btw), "B", 0, "R", true);
      $this->Ln();

      //-- factuur totaal
      $total_amount = (float)$subtotal_amount + (float)$btw;
      $total_amount = number_format((float)$total_amount, 2, '.', '');
      $this->Cell(160, $cellHeight, "Factuur totaal", 0, 0, "R");
      $this->Cell($cellWidthPrijsTotal, $cellHeight, StringHelper::asMoney($total_amount), "T", 0, "R", true);
      $this->Ln();

      if ($bankaccount) {
        //-- optionele incasso blok
        $this->setXY(8, 220);
        $this->SetFont('Arial', 'B', 10);
        $this->Cell(150, $cellHeight, "Het bedrag wordt omstreeks " . $expiration_date . " afgeschreven van rekening " . $bankaccount->IBAN . ".", 0, 0, "L", false);
        $this->SetFont('');
      }

      if ($invoice->paid != null) {

        //----------------------------------------------------------------------
        //-- factuur is betaald als dit getoond wordt.

        if ($bankaccount) {

          $this->setXY(34, 208);
          $this->SetFont('Arial', '', 10);
          $this->SetFont('');

          //-- rood
          $this->SetTextColor(255, 0, 0);
          $this->SetDrawColor(255, 0, 0);

          //-----------------------------

          $this->SetFont('Arial', '', 20);
          $this->SetFont('');

          $this->setXY(107, 71);

          //-- dit is de rode outline
          $this->Cell(93, 20, "", 1, 0, "", false);

          // Betaald op: positionering
          $this->setXY(107, 78.5);
          $this->Cell(54, $cellHeight, "BETAALD OP: ", 0, 0, "C", false);

          //-- $paidDutchDate positionering
          $this->setXY(145, 78.5);
          $paid_date = DateTimeHelper::convertFormat($invoice->paid, "Y-m-d", "d-m-Y");
          $this->Cell(64, $cellHeight, $paid_date, 0, 0, "C", false);

          //-- dit is de doos om de Ontvangen heen
          $this->setXY(45, 208);

        }
        else {

          //-- rood
          $this->SetTextColor(255, 0, 0);
          $this->SetDrawColor(255, 0, 0);

          $this->SetFont('Arial', '', 20);
          $this->SetFont('');

          $this->setXY(107, 71);

          //-- dit is de rode outline
          $this->Cell(93, 20, "", 1, 0, "", false);

          // Betaald op: positionering
          $this->setXY(107, 78.5);
          $this->Cell(54, $cellHeight, "BETAALD OP: ", 0, 0, "C", false);

          //-- $paidDutchDate positionering
          $this->setXY(145, 78.5);
          $paid_date = DateTimeHelper::convertFormat($invoice->paid, "Y-m-d", "d-m-Y");
          $this->Cell(64, $cellHeight, $paid_date, 0, 0, "C", false);

          //-- dit is de doos om de Ontvangen heen
          $this->setXY(45, 218);

        }
        //-- zwart
        $this->SetTextColor(0, 0, 0);
        $this->SetDrawColor(0, 0, 0);
      }

      //-- onderste blok
      $this->SetLeftMargin(12);
      $this->setXY(12, 235);
      $this->SetFont('Arial', '', 10);
      $this->SetFont('');

      $cellWidthTweedeRij = 51;

      $this->Cell(50, $cellHeight, "Gelieve bij betaling vermelden", 0, 0, "L", false);
      $this->Cell($cellWidthTweedeRij, $cellHeight, $invoice->invoiceNumber, 0);

      $this->Cell(40, $cellHeight, "Totaal bedrag exclusief", 0, 0, "R", false);
      $this->Cell(44, $cellHeight, StringHelper::asMoney($subtotal_amount), 0, 0, "R", false);
      $this->Ln();

      $this->Cell(50, $cellHeight, "Betalingstermijn", 0, 0, "L", false);
      if ($company) {
        $this->Cell($cellWidthTweedeRij, $cellHeight, $company->paymentTerm . ' dagen', 0);
      }
      else {
        $this->Cell($cellWidthTweedeRij, $cellHeight, $user->payterm . ' dagen', 0);
      }

      $this->Cell(40, $cellHeight, "Totaal bedrag BTW", 0, 0, "R", false);
      $this->Cell(44, $cellHeight, StringHelper::asMoney($btw), 0, 0, "R", false);
      $this->Ln();

      $this->Cell(50, $cellHeight, "Uiterlijke betaaldatum", 0, 0, "L", false);
      $this->Cell($cellWidthTweedeRij, $cellHeight, $expiration_date, 0);

      $this->Cell(40, $cellHeight, "", 0, 0, "R", false);
      $this->Cell(44, $cellHeight, '', 0, 0, "R", false);
      $this->Ln();

      $this->SetFont('Arial', '', 13);
      $this->SetFont('', 'B');

      $this->Cell(50, $cellHeight, "", 0, 0, "R", false);
      $this->Cell($cellWidthTweedeRij, $cellHeight, '', 0);

      $this->Cell(40, $cellHeight, "Te voldoen", 0, 0, "R", false);
      $this->Cell(44, $cellHeight, StringHelper::asMoney($total_amount), 0, 0, "R", false);
      $this->Ln();

      $this->SetLeftMargin(10);

      //-- probeer hier een cel te plaatsen die hieroverheen gaat.
      $this->setXY(9, 230);
      $this->Cell(191, 30, "", 1, 0, "R", false);

    }

    private function convertSplitNumberToCharachter($number) {

      switch ($number) {

        case 97:
          $character = 'A';
          break;
        case 98:
          $character = 'B';
          break;
        case 99:
          $character = 'C';
          break;
        case 100:
          $character = 'D';
          break;
        case 101:
          $character = 'E';
          break;
        case 102:
          $character = 'F';
          break;
        case 103:
          $character = 'G';
          break;
        case 104:
          $character = 'H';
          break;
        case 105:
          $character = 'I';
          break;
        case 106:
          $character = 'J';
          break;
        case 107:
          $character = 'K';
          break;
        case 108:
          $character = 'L';
          break;
        case 109:
          $character = 'M';
          break;
        case 110:
          $character = 'N';
          break;
        case 111:
          $character = 'O';
          break;
        case 112:
          $character = 'P';
          break;
        case 113:
          $character = 'Q';
          break;
        case 114:
          $character = 'R';
          break;
        case 115:
          $character = 'S';
          break;
        case 116:
          $character = 'T';
          break;
        case 117:
          $character = 'U';
          break;
        case 118:
          $character = 'V';
          break;
        case 119:
          $character = 'W';
          break;
        case 120:
          $character = 'X';
          break;
        case 121:
          $character = 'Y';
          break;
        case 122:
          $character = 'Z';
          break;
        case 123:
          $character = 'AA';
          break;
        case 124:
          $character = 'AB';
          break;
        case 125:
          $character = 'AC';
          break;
        case 126:
          $character = 'AD';
          break;
        case 127:
          $character = 'AE';
          break;
        case 128:
          $character = 'AF';
          break;
        case 129:
          $character = 'AG';
          break;
        case 130:
          $character = 'AH';
          break;
        case 131:
          $character = 'AI';
          break;
        case 132:
          $character = 'AJ';
          break;
        case 133:
          $character = 'AK';
          break;
        case 134:
          $character = 'AL';
          break;
        case 135:
          $character = 'AM';
          break;
        case 136:
          $character = 'AN';
          break;
        case 137:
          $character = 'AO';
          break;
        case 138:
          $character = 'AP';
          break;


      }

      return $character;
    }

    function addDocumentsToPdf($pdf) {

      $quotations = $this->quotations;

      foreach ($quotations as $key => $quotation) {

        $files = Files::find_all_by(['quotationId' => $quotation->quotationId, 'categoryId' => 3], "ORDER BY quotationId DESC");

        foreach ($files as $key2 => $file) {
          $fullFilePath = DIR_ROOT_HTTPDOCS . $file->folder . '/' . $file->filename;
          $pdf = $this->addPdfLocationToFile($pdf, $fullFilePath);
        }

      }

      return $pdf;

    }

    /**
     * @param $pdf
     * @param $fullFilePath
     * @return mixed
     */
    function addPdfLocationToFile($pdf, $fullFilePath) {
      $pagecount = $pdf->setSourceFile($fullFilePath);
      for ($i = 1; $i <= $pagecount; $i++) {
        $tplidx = $pdf->ImportPage($i);
        $pdf->addPage();
        $pdf->useTemplate($tplidx, 10, 10, 190);
      }

      return $pdf;

    }

    public function writeStandard($name, $value) {
      $this->SetFont('Nunito', 'B', 10);
      $this->Cell(30, 5, $name);
      $this->SetFont('Nunito', '', 10);
      $this->Cell(60, 5, $value, 0, 1, 'L', false);
    }

    public function generatePdf() {
      $this->addMainpage();
      $this->addDocumentsToPdf($this);

      return $this->generate();
    }

    public function generate() {
      $this->SetDisplayMode('real');

      $filename = $this->invoice->invoiceNumber . '.pdf';
      if ($this->isShow()) {
        $this->Output("I", $filename);
      }
      else {
        $this->Output("F", DIR_TEMP . $filename);
      }
      return $filename;
    }

  }