<?php
class BaseMontageQuotation extends AppModel
{
  const DB_NAME = 'rde_b1mo';
  const TABLE_NAME = 'montage_quotation';
  const OM_CLASS_NAME = 'MontageQuotation';
  const columns = ['id', 'montage_id', 'quotation_id', 'done', 'specie', 'specie_dikte', 'meters', 'arbeidsduur', 'bereikbaar_achter', 'bereikbaar_voor', 'slopen', 'voegen', 'remark'];
  const field_structure = [
    'id'                          => ['type' => 'mediumint', 'length' => '8', 'null' => false],
    'montage_id'                  => ['type' => 'mediumint', 'length' => '8', 'null' => false],
    'quotation_id'                => ['type' => 'int', 'length' => '11', 'null' => true],
    'done'                        => ['type' => 'boolean', 'length' => '1', 'null' => false],
    'specie'                      => ['type' => 'int', 'length' => '6', 'null' => false],
    'specie_dikte'                => ['type' => 'int', 'length' => '6', 'null' => false],
    'meters'                      => ['type' => 'int', 'length' => '6', 'null' => false],
    'arbeidsduur'                 => ['type' => 'float unsigned', 'length' => '', 'null' => false],
    'bereikbaar_achter'           => ['type' => 'tinyint', 'length' => '3', 'null' => true],
    'bereikbaar_voor'             => ['type' => 'tinyint', 'length' => '3', 'null' => true],
    'slopen'                      => ['type' => 'tinyint', 'length' => '3', 'null' => true],
    'voegen'                      => ['type' => 'tinyint', 'length' => '3', 'null' => true],
    'remark'                      => ['type' => 'text', 'length' => '', 'null' => true],
  ];

  protected static $primary_key = ['id'];
  protected $auto_increment = 'id';

  public $id, $montage_id, $quotation_id, $done, $specie, $specie_dikte, $meters, $arbeidsduur, $bereikbaar_achter, $bereikbaar_voor, $slopen, $voegen, $remark;

  function __construct($obj_arr = [], &$error_codes = []) {
    parent::__construct();
    $this->setDefaults();
    if (!empty($obj_arr)) {
      $this->from_array($obj_arr);
      $this->valid_required_fields($error_codes);
    }
  }

  public function valid(&$error_codes = []) {
    $errors = [];
    foreach(self::columns as $column) {
      if ($column != $this->auto_increment)
      {
        call_user_func_array([$this, 'v_' . $column], [&$errors]);
      }
    }
    if(empty($errors)) {
      return true;
    }
    $error_codes = array_merge($error_codes, $errors);
    return false;
  }

  public function valid_required_fields(&$error_codes = []) {
    $errors = [];
    foreach (self::columns as $column) {
      $function_name = "v_$column";
      if ($column!=$this->auto_increment) {
        call_user_func_array([$this, $function_name], [&$errors]);
      }
    }
    if (empty($errors)) {
      return true;
    }
    $error_codes = array_merge($error_codes, $errors);
    return false;
  }

  public function setDefaults() {
    $this->done = 0;
    $this->specie = 0;
    $this->specie_dikte = 0;
    $this->meters = 0;
    $this->arbeidsduur = 0;
  }



  public function v_id(&$error_codes = []) {
    if (!is_null($this->id) && strlen($this->id) > 0 && self::valid_mediumint($this->id, '8')) {
      return true;
    }
    $error_codes[] = 'id';
    return false;
  }

  public function v_montage_id(&$error_codes = []) {
    if (!is_null($this->montage_id) && strlen($this->montage_id) > 0 && self::valid_mediumint($this->montage_id, '8')) {
      return true;
    }
    $error_codes[] = 'montage_id';
    return false;
  }

  public function v_quotation_id(&$error_codes = []) {
    if (is_null($this->quotation_id) || strlen($this->quotation_id) == 0 || self::valid_int($this->quotation_id, '11')) {
      return true;
    }
    $error_codes[] = 'quotation_id';
    return false;
  }

  public function v_done(&$error_codes = []) {
    if (!is_null($this->done) && strlen($this->done) > 0 && self::valid_tinyint($this->done, '1')) {
      return true;
    }
    $error_codes[] = 'done';
    return false;
  }

  public function v_specie(&$error_codes = []) {
    if (!is_null($this->specie) && strlen($this->specie) > 0 && self::valid_int($this->specie, '6')) {
      return true;
    }
    $error_codes[] = 'specie';
    return false;
  }

  public function v_specie_dikte(&$error_codes = []) {
    if (!is_null($this->specie_dikte) && strlen($this->specie_dikte) > 0 && self::valid_int($this->specie_dikte, '6')) {
      return true;
    }
    $error_codes[] = 'specie_dikte';
    return false;
  }

  public function v_meters(&$error_codes = []) {
    if (!is_null($this->meters) && strlen($this->meters) > 0 && self::valid_int($this->meters, '6')) {
      return true;
    }
    $error_codes[] = 'meters';
    return false;
  }

  public function v_arbeidsduur(&$error_codes = []) {
    return true;
  }

  public function v_bereikbaar_achter(&$error_codes = []) {
    if (is_null($this->bereikbaar_achter) || strlen($this->bereikbaar_achter) == 0 || self::valid_tinyint($this->bereikbaar_achter, '3')) {
      return true;
    }
    $error_codes[] = 'bereikbaar_achter';
    return false;
  }

  public function v_bereikbaar_voor(&$error_codes = []) {
    if (is_null($this->bereikbaar_voor) || strlen($this->bereikbaar_voor) == 0 || self::valid_tinyint($this->bereikbaar_voor, '3')) {
      return true;
    }
    $error_codes[] = 'bereikbaar_voor';
    return false;
  }

  public function v_slopen(&$error_codes = []) {
    if (is_null($this->slopen) || strlen($this->slopen) == 0 || self::valid_tinyint($this->slopen, '3')) {
      return true;
    }
    $error_codes[] = 'slopen';
    return false;
  }

  public function v_voegen(&$error_codes = []) {
    if (is_null($this->voegen) || strlen($this->voegen) == 0 || self::valid_tinyint($this->voegen, '3')) {
      return true;
    }
    $error_codes[] = 'voegen';
    return false;
  }

  public function v_remark(&$error_codes = []) {
    if (is_null($this->remark) || strlen($this->remark) == 0 || self::valid_text($this->remark)) {
      return true;
    }
    $error_codes[] = 'remark';
    return false;
  }

  /**
  *@param array $conditions (required)
  *@param String $raw_sql (optional)
  *@return MontageQuotation[]
  **/
  public static function find_all_like($conditions, $raw_sql = '') { return parent::generic_find_all_like($conditions, static::OM_CLASS_NAME, $raw_sql); }
  /**
  *@param array $conditions (required)
  *@param String $raw_sql (optional)
  *@return MontageQuotation[]
  **/
  public static function find_all_by($conditions, $raw_sql = '') { return parent::generic_find_all_by($conditions, static::OM_CLASS_NAME, $raw_sql); }
  /**
  *@param String $raw_sql (optional)
  *@return MontageQuotation[]
  **/
  public static function find_all($raw_sql = '') { return parent::generic_find_all(static::OM_CLASS_NAME, $raw_sql); }
  /**
  *@param array $conditions (required)
  *@param String $raw_sql (optional)
  *@return MontageQuotation
  **/
  public static function find_by($conditions, $raw_sql = '') { return parent::generic_find_by($conditions, static::OM_CLASS_NAME, $raw_sql); }
  /**
  *@param String $id (required)
  *@param String $raw_sql (optional)
  *@return MontageQuotation
  **/
  public static function find_by_id($id, $raw_sql = '') { return parent::generic_find_by_id($id, static::OM_CLASS_NAME, $raw_sql); }
  /**
  *@param array $conditions (required)
  *@param String $raw_sql (optional)
  *@return int
  **/
  public static function count_all_by($conditions, $raw_sql = '') { return parent::generic_count_all_by($conditions, static::OM_CLASS_NAME, $raw_sql); }
  /**
  *@param array $conditions (required)
  *@param String $raw_sql (optional)
  *@return bool
  **/
  public static function delete_by($conditions, $raw_sql = '') { return parent::generic_delete_by($conditions, static::OM_CLASS_NAME, $raw_sql); }
}