<?php

  class RdeHelper {

    /**
     * Converts an integer to alphabeth letter(s)
     *
     * @param int $int
     * @return string
     */
    public static function intToAlpha($int) {
      $letters_arr = [
        'A',
        'B',
        'C',
        'D',
        'E',
        'F',
        'G',
        'H',
        'I',
        'J',
        'K',
        'L',
        'M',
        'N',
        'O',
        'P',
        'Q',
        'R',
        'S',
        'T',
        'U',
        'V',
        'W',
        'X',
        'Y',
        'Z',
      ];
      if ($int <= 25) {
        return $letters_arr[$int];
      }
      else { //$int > 25
        $dividend = ($int + 1);
        $alpha = '';
        $modulo = 0;
        while ($dividend > 0) {
          $modulo = ($dividend - 1) % 26;
          $alpha = $letters_arr[$modulo] . $alpha;
          $dividend = floor((($dividend - $modulo) / 26));
        }

        return $alpha;
      }
    }

    /**
     * Converts characters to int value based on alphabeth
     * @param string $characters
     * @return int
     */
    public static function AlphaToInt($characters) {
      $current_character_index = -1;
      $alpha_beth_flipped = array_flip(['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z']);

      //0 = A, 25 = Z
      $string_length = strlen($characters);
      for ($i = 0; $i < $string_length; $i++) {
        $current_character_index += ($alpha_beth_flipped[$characters[$i]] + 1) * pow(26, ($string_length - $i - 1));
      }

      return $current_character_index;
    }

    /**
     * Converts an integer to alphabeth letter(s)
     *
     * @param int $int
     * @return string
     */
    public static function asciiIntToAlpha($int) {

      //-- in onze systeem wordt alles op ascii characters gedaan
      //-- dus tellen begint bij 97 moet dan 0 worden
      //-- 98 wordt dan 1
      $int -= 97;

      $letters_arr = [
        'A',
        'B',
        'C',
        'D',
        'E',
        'F',
        'G',
        'H',
        'I',
        'J',
        'K',
        'L',
        'M',
        'N',
        'O',
        'P',
        'Q',
        'R',
        'S',
        'T',
        'U',
        'V',
        'W',
        'X',
        'Y',
        'Z',
      ];
      if ($int <= 25) {
        return $letters_arr[$int];
      }
      else { //$int > 25
        $dividend = ($int + 1);
        $alpha = '';
        $modulo = 0;
        while ($dividend > 0) {
          $modulo = ($dividend - 1) % 26;
          $alpha = $letters_arr[$modulo] . $alpha;
          $dividend = floor((($dividend - $modulo) / 26));
        }

        return $alpha;
      }
    }

    /**
     * Converts characters to int value based on alphabeth
     * @param string $characters
     * @return int
     */
    public static function AlphaToAsciiInt($characters) {
      $current_character_index = -1;
      $alpha_beth_flipped = array_flip(['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z']);

      //0 = A, 25 = Z
      $string_length = strlen($characters);
      for ($i = 0; $i < $string_length; $i++) {
        $current_character_index += ($alpha_beth_flipped[$characters[$i]] + 1) * pow(26, ($string_length - $i - 1));
      }

      //-- in onze systeem wordt alles op ascii characters gedaan
      //-- dus tellen begint bij 0 moet dan 97(A) worden
      //-- 1 wordt dan 98(B)
      return $current_character_index + 97;
    }

  }