<h3>Bestelling meters per maand</h3>
<PERSON><PERSON><PERSON> vind u een overzicht van het aantal meters bestellingen per maand in vergelijking met het voorgaande jaar.<Br/>
<div class="filter">
  Filter:
  <form>
    <select name="type" id="<?php echo $chartid ?>_type">
      <option value="">Selecteer type...</option>
      <?php foreach(Stones::TYPES as $mk=>$mname): ?>
        <option value="<?php echo $mk ?>" <?php writeIfSelectedVal($type,$mk); ?>><?php echo $mname ?></option>
      <?php endforeach; ?>
    </select>
    <select name="material" id="<?php echo $chartid ?>_material">
      <option  value="">Selecteer materiaal...</option>
      <?php foreach(Stones::MATERIALS as $mk=>$mname): ?>
        <option value="<?php echo $mk ?>" <?php writeIfSelectedVal($material,$mk); ?>><?php echo $mname ?></option>
      <?php endforeach; ?>
    </select>
    <select name="year" id="<?php echo $chartid ?>_year">
<!--      <option value="">Selecteer jaar...</option>-->
      <?php for($tel=date("Y");$tel>=2014;$tel--): ?>
        <option value="<?php echo $tel ?>" <?php writeIfSelectedVal($year,$tel); ?>><?php echo $tel ?></option>
      <?php endfor; ?>
    </select>
  </form>
</div>
<div class="rdechart" id="<?php echo $chartid ?>_chart"></div>
<table>
  <?php foreach($totals as $y=>$total):
    if($total==0) continue;
    if($y=="compare" && $totals[date("Y")]==0) continue;
    ?>
    <tr>
      <td style="font-weight: bold;">
        <?php if($y=="compare"):
          $growth = round((($totals[date("Y")]/$totals["compare"])-1)*100,1);
          ?>
          <?php echo "Vergeleken met ".(date("Y")-1)." deze datum" ?>
          <span style="color: <?php if($growth<5) echo 'red';if($growth>5) echo 'green'; ?> ;">(groei <?php echo $growth ?> %)</span>
        <?php else: ?>
          <?php echo "Totaal ".$y; ?>
        <?php endif; ?>
      </td>
      <td style="text-align: right;"><?php echo number_format($total,0,"",".") ?></td>
    </tr>
  <?php endforeach; ?>
</table>

<script>
  $(document).ready(function() {
    createBarchart("<?php echo $chartid ?>_chart", <?php echo json_encode($chartdata) ?>);

    $("#<?php echo $chartid ?>_material,#<?php echo $chartid ?>_year,#<?php echo $chartid ?>_type").change(function() {
      getChart("<?php echo $chartid ?>");
    })

  });
</script>