<?php TemplateHelper::includePartial('_tabs.php', 'prices'); ?>

<h3>Bewerk steen prijzen</h3>

<?php writeErrors($form->getErrors(), true); ?>

<form method="post">
  <table class="default_table" style="width: auto;">
    <tr class="dataTableHeadingRow">
      <td>Item</td>
      <td>Instelling</td>
    </tr>
    <tr class="dataTableRow trhover">
      <td>Merk</td>
      <td><?php echo $brands[$_SESSION['bsp_brand']]->name ?></td>
    </tr>
    <?php if(count($colors)>0): ?>
      <tr class="dataTableRow trhover">
        <td>K<PERSON>uren</td>
        <td>
          <?php
            $colornames = [];
            foreach($colors as $color):
              $colornames[] = $color->name;
            endforeach;
            echo implode(", ", $colornames);
          ?>
        </td>
      </tr>
    <?php endif; ?>
    <tr class="dataTableRow trhover">
      <td><PERSON><PERSON><PERSON> vanaf</td>
      <td>
        01-01-<?php echo $_GET["yearfrom"] ?>
      </td>
    </tr>
  </table>
  <br/>

  <div>
    <p style="color: red;" >NA = Niet aanwezig (deze steen bestaat nog niet in het systeem) </p>
  </div>

  <table class="default_table" style="width: auto;">
    <tr class="dataTableHeadingRow">
      <td>Naam</td>
      <td>Type</td>
      <td style="text-align: center">Eindsteen</td>
      <td style="text-align: center">Online</td>
      <?php if ($stones[0] || $stones[0]->colorId !== '43'): ?>
        <td>Licht gezoet geschuurd</td>
        <td>Licht gezoet geborsteld</td>
        <td style="width: 100px;">Betonlook</td>
        <td>Donkergezoet</td>
      <?php else: ?>
        <td style="width: 100px">Zimbabwe</td>
      <?php endif; ?>
      <td>Huidige prijs <?php echo showHelpButton("Dit is prijs welk op dit moment gebruikt word voor nieuwe offertes.","Huidige prijs") ?></td>
    </tr>
    <?php
      $a = 'NA';
      /** @var Stones $item */
      foreach($stones as $item): ?>
        <tr class="dataTableRow trhover product-row" data-id="<?php echo $item->stoneId ?>">
          <td><?php echo $item->name ?></td>
          <td><?php echo Stones::TYPES[$item->type] ?></td>
          <td style="text-align: center"><?php echo Stones::ENDSTONES[$item->endstone] ?></td>
          <td style="text-align: center"><?php echo $item->display=="true"?"Ja":"Nee" ?></td>
          <?php if ($item->colorId !== '43'): ?>
            <td>€ <?php $form->getElement("baseprice_".$item->stoneId)->render() ?></td>
            <td>€ <?php ($item->has_stone_geborsteld) ? $form->getElement("lichtgezoet_".$item->stoneId)->render() : $form->getElement("nostone_".$item->stoneId)->render() ?></td>
            <td>€ <?php ($item->has_stone_beton) ? $form->getElement("betonlook_".$item->stoneId)->render() : $form->getElement("nostone_".$item->stoneId)->render() ?></td>
            <td>€ <?php ($item->has_stone_donker) ? $form->getElement("donkergezoet_".$item->stoneId)->render() : $form->getElement("nostone_".$item->stoneId)->render() ?></td>
          <?php else: ?>
           <td>€ <?php $form->getElement("zimbabwe_".$item->stoneId)->render() ?></td>
          <?php endif; ?>
          <td style="text-align: right"><?php echo StringHelper::asMoney($item->price->price) ?></td>
        </tr>

        <script>
          $("#baseprice_<?php echo $item->stoneId ?>").change( function() {
            if($(this).val()!=="") {
              var val = $(this).val();
              val = decimalNL(val,2);

              var lichtgezoet_geb = Number(val) + Number(5);
              var betonlook = Number(val) + Number(5);
              var donkergezoet = Number(val) + Number(18.50);

              $("#lichtgezoet_<?php echo $item->stoneId ?>").val(decimalNL(lichtgezoet_geb,2));
              $("#betonlook_<?php echo $item->stoneId ?>").val(decimalNL(betonlook,2));
              $("#donkergezoet_<?php echo $item->stoneId ?>").val(decimalNL(donkergezoet,2));
            }
          });
        </script>
      <?php endforeach; ?>
  </table>

  <br/>
  <input type="submit" name="go" value="Opslaan" />
  <input type="submit" name="go_list" value="Opslaan en naar lijst" />

</form>
<script type="text/javascript">
  $(document).ready(function () {
    $(".buyprice,.factor,.price").focus(function() {
      $(this).select();
    });

    $(".buyprice,.factor").change( function() {
      if($(this).val()!="") {
        var val = $(this).val();
        //console.log(val);
        val = decimalNL(val,3);
        $(this).val(val);
      }

      var row = $(this).parent().parent();
      var inkoop = parseFloat(row.find(".buyprice").val());
      var factor = parseFloat(row.find(".factor").val());
      var price = row.find(".price");
      if(!isNaN(inkoop) && !isNaN(factor)) {
        price.val(decimalNL(inkoop*factor,3))
      }
    });

    $(".price").change( function() {
      if($(this).val()!="") {
        var val = $(this).val();
        //console.log(val);
        val = decimalNL(val,2);
        $(this).val(val);
      }
    });

  });

</script>
<style>
  input.price,input.baseprice, input.lichtgezoet, input.betonlook, input.donkergezoet, input.zimbabwe, input.factor, input.currentprice {
    width: 80px;
    text-align: right;
  }

  input.lichtgezoet, input.betonlook, input.donkergezoet {
   color: black !important;
  }
</style>