<?php TemplateHelper::includePartial('_tabs.php','lstock'); ?>
<form method="post" action="<?php echo reconstructQuery() ?>">
  <div class="box">
    <input type="text" name="st_search" value="<?php echo $_SESSION['st_search'] ?>" placeholder="Zoeken..."/>
    <select name="st_brand">
      <option value="">Selecteer leverancier...</option>
      <?php foreach($stonebrands as $sb): ?>
        <option value="<?php echo $sb->brandId ?>" <?php if($_SESSION['st_brand']==$sb->brandId) echo 'selected'; ?>><?php echo $sb->name ?></option>
      <?php endforeach; ?>
    </select>
    <input type="submit" name="go" value="Zoeken" />

    <a href="?action=stockpdf" target="_blank" class="gsd-btn gsd-btn-secondary">Bekijk PDF voorraad overzicht</a>
    <a href="?action=stockpdfinvalid" target="_blank" class="gsd-btn gsd-btn-secondary">Bekijk PDF voorraad overzicht met ongeldige prijs</a>

  </div>

  <?php $pager->writePreviousNext(); ?>

	<?php	if(count($stones)==0): ?>
		<br/>Er zijn geen stenen gevonden
  <?php else: ?>
		<table class="default_table" style="width: auto;">
		<tr class="dataTableHeadingRow">
      <td>Code</td>
      <td>Kleur</td>
      <td style="text-align: right;">Voorraad</td>
      <td style="text-align: right;">Besteld, niet geleverd<br/><?php echo showInfoButton("Dit zijn het aantal stenen wat besteld is en nog niet geleverd. Eventuele deelleveringen zijn hierin al verwerkt.") ?></td>
      <td>Label</td>
		</tr>
		<?php foreach($stones as $item): ?>
			<tr class="dataTableRow trhover">
        <td><?php echo $item->name ?></td>
        <td><?php echo $item->color->getFullname() ?></td>
        <td style="text-align: right;">
          <input style="text-align: right;" type="text" name="stock[<?php echo $item->stoneId ?>]" class="size" value="<?php echo $item->stock ?>"/>
        </td>
        <td style="text-align: right;"><?php echo isset($alreadyOrdered[$item->stoneId])?$alreadyOrdered[$item->stoneId]:0 ?></td>
        <td style="text-align: right">
          <?php echo BtnHelper::getPrintPDF("?action=generatelabel&id=".$item->stoneId,"Print Label") ?>
        </td>
			</tr>
    <?php endforeach; ?>
		</table><br/>
	<?php endif; ?>

  <input type="submit" name="go" value="Opslaan" class="gsd-btn gsd-btn-primary"/>

</form>
<script type="text/javascript">
  $(document).ready(function() {
    $(".size").change(function (event) {
      if($(this).val()!="") {
        var val = parseInt($(this).val());
        if(!isNaN(val)) {
          $(this).val(val);
        }
        else {
          $(this).val(0);
        }
      }
      else {
        $(this).val(0);
      }
    });

  });
</script>
