<?php TemplateHelper::includePartial('_tabs.php', 'stones'); ?>

<div class="box">
    <form method="post" action="<?php echo reconstructQueryAdd() ?>">
      <input type="text" name="stone_search" value="<?php echo $_SESSION['stone_search'] ?>" placeholder="Zoeken..."/>
      <select name="stone_display"  id="stone_display">
        <option value="">Selecteer zichtbaar...</option>
        <option value="1" <?php if($_SESSION['stone_display']=="1") echo 'selected'; ?>>Ja</option>
        <option value="0" <?php if($_SESSION['stone_display']=="0") echo 'selected'; ?>>Nee</option>
      </select>
      <input type="submit" name="go" id="go" value="Zoeken" />

      <a href="?action=windowsilledit" class="gsd-btn gsd-btn-primary">Toevoegen nieuw vensterbank bovenaanzicht</a>
    </form>

    <input type="button" id="sortablecontrol" name="" value="Sorteren aanzetten"  class="gsd-btn gsd-btn-secondary"/> <?php echo showHelpButton("Door op 'sorteren aanzetten' te klikken wordt het mogelijk om de rijen van de tabel te verslepen, en zo de volgorde van de vensterbanken aan te passen.",'Sorteren'); ?>
    <br/><br/>
    <div id="message"></div>


  </div>

  <?php $pager->writePreviousNext(); ?>

  <?php if(count($items)==0): ?>
    <br/>
    Er zijn nog geen items gevonden.
  <?php else: ?>
    <table class="default_table" style="width: auto;" id="sortable">
      <tr class="dataTableHeadingRow nodrop nodrag">
        <td class="sort-td"></td>
        <td>Naam</td>
        <td>x1</td>
        <td>x2</td>
        <td>x3</td>
        <td>x4</td>
        <td>x5</td>
        <td>x6</td>
        <td>Factor</td>
        <td>Zichtbaar</td>
        <td class="gsd-svg-icon-width-2">Actie</td>
      </tr>
      <?php
        /** @var Windowsill $item */
        foreach($items as $item): ?>
        <tr class="dataTableRow trhover" id="<?php echo $item->id ?>">
          <td class="sort-td"><span class="fa fa-bars"></span></td>
          <td><?php echo $item->name ?></td>
          <td><?php echo $item->x1 ?></td>
          <td><?php echo $item->x2 ?></td>
          <td><?php echo $item->x3 ?></td>
          <td><?php echo $item->x4 ?></td>
          <td><?php echo $item->x5 ?></td>
          <td><?php echo $item->x6 ?></td>
          <td><?php if($item->mitre_factor!=0) echo $item->mitre_factor ?></td>
          <td>
            <?php if($item->online): ?>
              <?php echo IconHelper::getCheckboxGreen() ?>
            <?php endif; ?>
          </td>
          <td>
            <?php echo BtnHelper::getRemove(reconstructQuery(['action', 'id']) . 'action=windowsilldelete&delid=' . $item->id) ?>
            <?php echo BtnHelper::getEdit(reconstructQueryAdd(['pageId']).'action=windowsilledit&id='.$item->id) ?>
          </td>
        </tr>
      <?php endforeach; ?>
    </table>
  <?php endif; ?>
<script>
  $(document).ready(function() {

    $("#stone_brand,#stone_display,#stone_color,#stone_type").change(function() {
      $("#go").click();
    })

    gsdRowSorter("<?php echo reconstructQuery() ?>action=windowsillmove&");

  });
</script>