<tr class="dataTableRow mower_tr">
  <td class="head">
    Steen ID / Kleur ID
  </td>
  <td>
    <input type="text" name="supplier_code" value="<?php echo escapeForInput($product->supplier_code) ?>"/>
    Steen ID = altijd formaat STONE_{stoneId} bijv STONE_31  / Kleur ID = altijd formaat COLOR_{colorId} bijv COLOR_31
  </td>
</tr>
<input type="hidden" name="productoptions_helper" value="1" />
<tr class="dataTableRow mower_tr">
  <td class="head">
    Maten
    <?php echo showHelpButton("Dit is alleen voor standaard element lengtes. Voer hier de maten in mm in ; gescheiden.") ?>
  </td>
  <td>
    <input type="text" name="productoptions[lengths]" value="<?php if(isset($productoptions["lengths"])) echo escapeForInput($productoptions["lengths"]->value) ?>"/>
  </td>
</tr>
