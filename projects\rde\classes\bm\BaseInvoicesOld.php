<?php
class BaseInvoicesOld extends AppModel
{
  const DB_NAME = 'rde_b1mo';
  const TABLE_NAME = 'invoices_old';
  const OM_CLASS_NAME = 'InvoicesOld';
  const columns = ['invoiceOldId', 'oldUserId', 'oldInvoiceId', 'userId', 'companyId', 'personId', 'folder', 'filename', 'uploadDate', 'factuurNummer', 'factuurDatum', 'data', 'betaald', 'aanmaning1', 'aanmaning2', 'aanmaning3', 'aanmaning4'];
  const field_structure = [
    'invoiceOldId'                => ['type' => 'int', 'length' => '11', 'null' => false],
    'oldUserId'                   => ['type' => 'int', 'length' => '11', 'null' => false],
    'oldInvoiceId'                => ['type' => 'int', 'length' => '11', 'null' => false],
    'userId'                      => ['type' => 'int', 'length' => '11', 'null' => true],
    'companyId'                   => ['type' => 'int', 'length' => '11', 'null' => true],
    'personId'                    => ['type' => 'int', 'length' => '11', 'null' => true],
    'folder'                      => ['type' => 'varchar', 'length' => '255', 'null' => false],
    'filename'                    => ['type' => 'varchar', 'length' => '255', 'null' => false],
    'uploadDate'                  => ['type' => 'datetime', 'length' => '', 'null' => false],
    'factuurNummer'               => ['type' => 'varchar', 'length' => '40', 'null' => false],
    'factuurDatum'                => ['type' => 'date', 'length' => '', 'null' => true],
    'data'                        => ['type' => 'text', 'length' => '', 'null' => false],
    'betaald'                     => ['type' => 'date', 'length' => '', 'null' => true],
    'aanmaning1'                  => ['type' => 'date', 'length' => '', 'null' => true],
    'aanmaning2'                  => ['type' => 'date', 'length' => '', 'null' => true],
    'aanmaning3'                  => ['type' => 'date', 'length' => '', 'null' => true],
    'aanmaning4'                  => ['type' => 'date', 'length' => '', 'null' => true],
  ];

  protected static $primary_key = ['invoiceOldId'];
  protected $auto_increment = 'invoiceOldId';

  public $invoiceOldId, $oldUserId, $oldInvoiceId, $userId, $companyId, $personId, $folder, $filename, $uploadDate, $factuurNummer, $factuurDatum, $data, $betaald, $aanmaning1, $aanmaning2, $aanmaning3, $aanmaning4;

  function __construct($obj_arr = [], &$error_codes = []) {
    parent::__construct();
    $this->setDefaults();
    if (!empty($obj_arr)) {
      $this->from_array($obj_arr);
      $this->valid_required_fields($error_codes);
    }
  }

  /**
  * Set default object values
  **/
  public function setDefaults() {
  }

  /**
   * @param array $conditions (required)
   * @param string $raw_sql (optional)
   * @return InvoicesOld[]
   **/
  public static function find_all_like($conditions, $raw_sql = '') {
    return parent::generic_find_all_like($conditions, static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param array $conditions (required)
   * @param string $raw_sql (optional)
   * @return InvoicesOld[]
   **/
  public static function find_all_by($conditions, $raw_sql = '') {
    return parent::generic_find_all_by($conditions, static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param string $raw_sql (optional)
   * @return InvoicesOld[]
   **/
  public static function find_all($raw_sql = '') {
    return parent::generic_find_all(static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param array $conditions (required)
   * @param string $raw_sql (optional)
   * @return InvoicesOld
   **/
  public static function find_by($conditions, $raw_sql = '') {
    return parent::generic_find_by($conditions, static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param string $id (required)
   * @param string $raw_sql (optional)
   * @return InvoicesOld
   **/
  public static function find_by_id($id, $raw_sql = '') {
    return parent::generic_find_by_id($id, static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param array $conditions (required)
   * @param string $raw_sql (optional)
   * @return int
   **/
  public static function count_all_by($conditions, $raw_sql = '') {
    return parent::generic_count_all_by($conditions, static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param array $conditions (required)
   * @param string $raw_sql (optional)
   * @return bool
   **/
  public static function delete_by($conditions, $raw_sql = '') {
    return parent::generic_delete_by($conditions, static::OM_CLASS_NAME, $raw_sql);
  }

}