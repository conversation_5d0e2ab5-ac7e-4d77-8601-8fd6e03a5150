<?php

  class BretiProductionReceiptPdf extends GSDPDF {

    protected $supplierExternal = false;
    protected $multiple = false;

    public function __construct($quotation_ids) {

      parent::__construct();

      $this->AddFont('Nunito', '', 'Nunito-Regular.ttf', true);
      $this->AddFont('Nunito', 'B', 'Nunito-Bold.ttf', true);
      $this->SetAutoPageBreak(true, 30);

      $this->quotations = Quotations::find_all('WHERE quotationId IN(' . $quotation_ids . ') ');
    }

    /**
     * @param bool $supplierExternal
     */
    public function setSupplierExternal(bool $supplierExternal): void {
      $this->supplierExternal = $supplierExternal;
    }

    /**
     * @return bool
     */
    public function isSupplierExternal(): bool {
      return $this->supplierExternal;
    }

    /**
     * @param bool $multiple
     */
    public function setMultiple(bool $multiple): void {
      $this->multiple = $multiple;
    }

    /**
     * @return bool
     */
    public function isMultiple(): bool {
      return $this->multiple;
    }

    public function build() {

      $quotations = $this->quotations;

      $count = 0;
      $y = 25;
      $y_top_row = 5;
      $y_second_row = 12;
      $y_notes = 68;
      foreach ($quotations as $quotation) {
        if ($count == 1 || $count == 2) {
          $y_top_row += 85;
          $y_second_row += 85;
          $y += 85;
          $y_notes += 85;
        }

        if ($count == 0 || $count == 3) {
          $count = 0;
          $y = 25;
          $y_top_row = 5;
          $y_second_row = 12;
          $y_notes = 68;
          $this->addPage();
        }

        $count++;

        $quotation_extra = QuotationsExtra::find_by(['quotationId' => $quotation->quotationId]);
        $driving_routes = DrivingRoutes::find_by([], ' WHERE ' . substr($quotation->zipcode, 0, -2) . ' BETWEEN driving_routes.start AND driving_routes.end ');
        $stones = Stones::find_by(['stoneId' => $quotation->stoneId]);
//        dumpe($stones);
        $stone_size = StoneSizes::find_by(['sizeId' => $stones->sizeId]);
        $stone_brand = StoneBrands::find_by(['brandId' => $stones->brandId]);
        $stone_color = StoneColors::find_by(['colorId' => $stones->colorId]);
        $order_elements = OrderElements::find_all_by(['quotationId' => $quotation->quotationId]);

        $this->SetFont('Arial', '', 8);
        $this->SetDrawColor(50, 50, 50);
        $this->SetTextColor(0, 0, 0);
        $top_margin = 0;
        if ($count == 1 || $count == 2) {
          $top_margin += 200;
        }
        $this->SetLeftMargin(10);
        $this->SetTopMargin($top_margin);

        $this->SetAutoPageBreak(true, 2);

        $fontSizeMainData = 10;
        $cellHeightTopRight = 10;
        $xyCoordinatesStart = 5;
        $xyCoordinatesFromLeft = 5;

        $this->setXY($xyCoordinatesFromLeft, $xyCoordinatesStart);

        $this->SetLineWidth(0.2);
        $this->SetDrawColor(255, 0, 0);
        $this->SetFillColor(0, 255, 0);

        $this->SetFont('Arial', 'B', 16);
        $this->setXY(10, $y_top_row);
        $this->Cell(50, 10, 'Productiestaat', 0, 1);
        $this->SetDrawColor(255, 0, 0);

        if ($quotation->quotationPart !== null) {
          $quotation_part = RdeHelper::intToAlpha($quotation->quotationPart);
          $quotationNumber = $quotation->quotationNumber . "-" . $quotation->quotationVersion . '-' . $quotation_part;
        }
        else {
          $quotationNumber = $quotation->quotationNumber . "-" . $quotation->quotationVersion;
        }

        $this->setXY(10, $y_second_row);
        $this->Cell(50, $cellHeightTopRight, $quotationNumber, 0);

        //- left, top
        $this->setXY(0, 0);

        //-- location, x, y, w, h, tyle, link
        $this->Image('https://www.raamdorpel.nl/3rdparty/barcodegen/datamatrix.php?text=' . $quotationNumber, 105, $y_top_row, 13, 13, 'PNG');

        if ($this->isSupplierExternal()) {
          $this->SetFont('Arial', 'B', 16);
          $this->setXY(67, $y_top_row);
          $this->Cell(22, 6, (string)$quotation_extra->addressDeliveryId);
          $this->SetFont('Arial', 'B', 16);
        }

        //-- zwart text
        $this->SetTextColor(0, 0, 0);
        $this->setXY(0, 0);

        //-- code
        $this->setXY(200, $y_top_row);

        if (isset($driving_routes->code)) {
          //-- w, h
          $this->Cell(5, 10, $driving_routes->code, 0);
        }
        else {
          //-- w, h
          $this->Cell(5, 10, '', 0);
        }

        $cellHeightMainData = 5;
        $cellWidthMainData = 50;
        $cellWidthMainData1 = 30;
        $cellWidthMainData2 = 70;

        $this->setXY(37, 25);
        $this->SetFont('Arial', '', $fontSizeMainData);

        if ($quotation->productionNotes != '') {

          // Opmerkingen
          $this->setXY(10, $y_notes);
          $this->SetFont('Arial', 'B', $fontSizeMainData);
          $this->Cell($cellWidthMainData1, $cellHeightMainData, 'Opmerkingen', 0);

          $this->SetTextColor(255, 0, 0);

          $this->setXY(42, $y_notes);
          $this->SetFont('Arial', '', $fontSizeMainData);
          $this->Multicell(170, $cellHeightMainData, $quotation->productionNotes, 0);

        }

        $this->SetTextColor(0, 0, 0);
//
//      $this->SetLeftMargin(105);
        $this->setXY(10, $y);
        $this->SetFont('Arial', '', $fontSizeMainData);
        $cellWidthMainDataRight = 34;
        $cellHeightMainDataRight = 6;

        // Projectnaam
        $this->SetFont('Arial', 'B');
        $this->Cell($cellWidthMainDataRight, $cellHeightMainDataRight, 'Projectnaam', 0);
        $this->SetFont('Arial', '');
        $this->Cell($cellWidthMainDataRight, $cellHeightMainDataRight, $quotation->projectName, 0);
        $this->Ln();

        // Kenmerk
        $this->SetFont('Arial', 'B');
        $this->Cell($cellWidthMainDataRight, $cellHeightMainDataRight, 'Kenmerk', 0);
        $this->SetFont('Arial', '');
        $this->Cell($cellWidthMainDataRight, $cellHeightMainDataRight, $quotation->projectReference, 0);
        $this->Ln();

        // Merk

        $this->SetFont('Arial', 'B');
        $this->Cell($cellWidthMainDataRight, $cellHeightMainDataRight, 'Merk', 0);
        $this->SetFont('Arial', '');
        $this->Cell($cellWidthMainDataRight, $cellHeightMainDataRight, $stone_brand->name, 0);
        $this->Ln();

        // Kleur
        if ($stone_brand->stoneTypeId == 2 || $stone_brand->stoneTypeId == 3) {
          $color = $stone_color->short . ' ' . $stone_color->name;
        }
        else {
          if ($stone_color->glaced == 'true') {
            $color = $stone_color->short . ' ' . $stone_color->name . ' geglazuurd';
          }
          else {
            $color = $stone_color->short . ' ' . $stone_color->name . ' ongeglazuurd';
          }
        }

        $this->SetFont('Arial', 'B');
        $this->Cell($cellWidthMainDataRight, $cellHeightMainDataRight, 'Kleur', 0);
        $this->SetFont('Arial', '');
        $this->Cell($cellWidthMainDataRight, $cellHeightMainDataRight, trim($color), 0);
        $this->Ln();

        // Model
//      $oStones->sizeId = $stoneDescription->sizeId;
        $model_name = $stone_size->short;
        if ($stones->type == "balkjes") {
          $model_name = "Divers";
        }

        if ($quotation->endstone == 'true_grooves') {
          $stone_end = 'Ja, groeven';
        }
        elseif ($quotation->endstone == 'true') {
          $stone_end = 'Ja, opstaande zijkanten';
        }
        elseif ($quotation->endstone == 'false') {
          $stone_end = 'Nee';
        }

        $this->SetFont('Arial', 'B');
        $this->Cell($cellWidthMainDataRight, $cellHeightMainDataRight, 'Model', 0);
        $this->SetFont('Arial', '');
        $this->Cell($cellWidthMainDataRight, $cellHeightMainDataRight, $model_name, 0);
        $this->Ln();

        if (!in_array($stone_brand->stoneTypeId, [2, 3])) {
          // Eindstenen
          $this->SetFont('Arial', 'B');
          $this->Cell($cellWidthMainDataRight, $cellHeightMainDataRight, 'Eindstenen', 0);
          $this->SetFont('Arial', '');
          $this->Cell($cellWidthMainDataRight, $cellHeightMainDataRight, $stone_end, 0);
          $this->Ln();
        }

        // Aantal meter
        $this->SetFont('Arial', 'B');
        $this->Cell($cellWidthMainDataRight, $cellHeightMainDataRight, 'Aant. meter', 0);
        $this->SetFont('Arial', '');
        $this->Cell($cellWidthMainDataRight, $cellHeightMainDataRight, $quotation->meters, 0);
        $this->Ln();

        $total_element_count = count($order_elements);

        if ($quotation->stoneCategoryId == 13) { //natuursteen_vensterbank
          $this->SetFont('Arial', 'B');
          $this->Cell($cellWidthMainDataRight, $cellHeightMainDataRight, 'Oppervlakte m2', 0);
          $this->SetFont('Arial', '');
          $opp = 0;

          for ($i = 0; $i < $total_element_count; $i++) {
            $windowsill = OrderElementWindowsill::find_by(['element_id' => $order_elements[$i]->elementId]);
            $opp += ($windowsill->x1 * $windowsill->x2) / 1000000 * $order_elements[$i]->amount;
          }

          $this->Cell($cellWidthMainDataRight, $cellHeightMainDataRight, (string)round($opp, 2), 0);
          $this->Ln();
        }

        // Aantal delingen:
        //-- always subtract 3 voor de last 3

        if ($order_elements[0]->heartClickSize == 1) {
          $heartClickSizeText = 'tot aan de punt';
        }
        else {
          $heartClickSizeText = 'hart klik gemeten';
        }

        $aCount4 = 0;
        $aForm['elements4'][$aCount4]['label'] = 'type gekozen maat';
        $aForm['elements4'][$aCount4]['span'] = ['id' => 'spanAdmin', 'value' => $heartClickSizeText, 'class' => 'divTextFC'];

        $aCount5 = 0;
        $aantalHoekjes = '';
        for ($i = 0; $i < $total_element_count; $i++) {
          // Totaal aantal meter hoger dan 100 meter?
          // Alle elementen
          if ($quotation->meters >= 100) {
            $element = intval($order_elements[$i]->elementLength);
            $aantal = intval($order_elements[$i]->amount);
            $aantalHoekjes = intval($aantalHoekjes);
            $aantalHoekjes += $aantal * floor((ceil($element / 2800) / 2));
          }
          else {
            $element = intval($order_elements[$i]->elementLength);
            $aantal = intval($order_elements[$i]->amount);
            $aantalHoekjes = intval($aantalHoekjes);
            $aantalHoekjes += $aantal * floor((ceil($element / 2300) / 2));
          }
        }

        // Aantal delingen:
        if (!in_array($stone_brand->stoneTypeId, [2, 3])) {
          $this->SetFont('Arial', 'B');
          $this->Cell($cellWidthMainDataRight, $cellHeightMainDataRight, 'Aantal delingen', 0);
          $this->SetFont('Arial', '');
          $this->Cell($cellWidthMainDataRight, $cellHeightMainDataRight, $aantalHoekjes, 0);
          $this->Ln();
        }

        $this->setXY(10, $y);
        $this->SetLeftMargin(105);

        // Aantal meter
        $this->SetFont('Arial', 'B');
        $this->Cell($cellWidthMainDataRight, $cellHeightMainDataRight, 'Aantal meter', 0);
        $this->SetFont('Arial', '');
        $this->Cell($cellWidthMainDataRight, $cellHeightMainDataRight, $quotation->meters, 0);
        $this->Ln();

        //Prijs per m.
//        $stoneprice_obj = StonePrices::find_by(['stoneId' => $stones->stoneId], 'AND YEAR(validFrom) =' . date("Y"));
        $stoneprice_obj = StonePrices::find_by(['stoneId' => $stones->stoneId]);
        $stoneprice = 0;
        if ($stoneprice_obj) {
          $stoneprice = $stoneprice_obj->price;
        }

        $this->SetFont('Arial', 'B');
        $this->Cell($cellWidthMainDataRight, $cellHeightMainDataRight, 'Prijs per m.', 0);
        $this->SetFont('Arial', '');
        $this->Cell($cellWidthMainDataRight, $cellHeightMainDataRight, StringHelper::asMoney($stoneprice), 0);
        $this->Ln();

        //Prijs totaal
        $price_total = $quotation->meters * $stoneprice;

        $this->SetFont('Arial', 'B');
        $this->Cell($cellWidthMainDataRight, $cellHeightMainDataRight, 'Prijs totaal', 0);
        $this->SetFont('Arial', '');
        $this->Cell($cellWidthMainDataRight, $cellHeightMainDataRight, StringHelper::asMoney($price_total), 0);
        $this->Ln();

        //Aantal verstekken
        $aantal = 0;
        $mitre_ids = [];
        $mitre_price = '';

        foreach ($order_elements as $order_element) {
          if (!empty($order_element->leftMitreId)) {
            $aantal++;
            $left_mitre = Mitres::find_by(['mitreId' => $order_element->leftMitreId]);
            $left_mitre_price = MitrePrices::find_all_by([], 'WHERE stoneLength = ' . $left_mitre->stoneLength . ' ORDER BY validFrom DESC LIMIT 1');

            $mitre_ids[] .= $order_element->leftMitreId;
            $mitre_price = $stoneprice * $left_mitre_price[0]->factor;
          }
          if (!empty($order_element->rightMitreId)) {
            $aantal++;
            $right_mitre = Mitres::find_by(['mitreId' => $order_element->rightMitreId]);
            $right_mitre_price = MitrePrices::find_all_by([], 'WHERE stoneLength = ' . $right_mitre->stoneLength . ' ORDER BY validFrom DESC LIMIT 1');

            if (!$mitre_price) {
              $mitre_price = $stoneprice * $right_mitre_price[0]->factor;
            }
            $mitre_ids[] .= $order_element->rightMitreId;
          }
        }

        $this->SetFont('Arial', 'B');
        $this->Cell($cellWidthMainDataRight, $cellHeightMainDataRight, 'Aant. verstekken', 0);
        $this->SetFont('Arial', '');
        $this->Cell($cellWidthMainDataRight, $cellHeightMainDataRight, $aantal, 0);
        $this->Ln();

        //Verstek prijs
        $this->SetFont('Arial', 'B');
        $this->Cell($cellWidthMainDataRight, $cellHeightMainDataRight, 'Verstekprijs', 0);
        $this->SetFont('Arial', '');
        $this->Cell($cellWidthMainDataRight, $cellHeightMainDataRight, $mitre_price, 0);
        $this->Ln();
      }

    }

    public function footer() {

    }

    public function generate() {

      $this->build();

      $filename = 'productiestaat_breti.pdf';

      if ($this->isMultiple()) {
        $this->Output("F", DIR_TEMP . $filename);

      }
      else {
        $this->Output("I", $filename);
      }

      return $filename;

    }

     }