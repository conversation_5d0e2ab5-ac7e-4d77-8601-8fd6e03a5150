<?php

  use domain\quotations\service\GetBrandIdsByStone;
  use domain\quotations\service\GetStoneCategoryInfo;

  trait quotationStep1Actions {


    public function executeWizardstep1() {

      if (isset($_GET["stoneId"])) {
        //er word expliciet een steen geopend
        unset($_SESSION["wizard"]);

        $stone = Stones::find_by(["stoneId" => $_GET["stoneId"]]);
        $stone_brand = StoneBrands::find_by(["brandId" => $stone->brandId]);
        $quotation = new Quotations();
        if (isset($_SESSION['userObject'])) {
          $quotation->userId = $_SESSION['userObject']->userId;
          $quotation->companyId = $_SESSION['userObject']->companyId;
        }
        //steen is van een bepaalde variant en materiaal
        $quotation->stoneCategoryId = $stone->category_id;
        $quotation->projectName = "";
        $quotation->brandId = $stone->brandId;
        //tijdelijke properties
        $quotation->colorId = $stone->colorId;
        $quotation->sizeId = $stone->sizeId;
        $quotation->endstone = $stone->endstone;
        $quotation->shorter = "";

        $_SESSION["wizard"]['quotation'] = $quotation;
        ResponseHelper::redirect(reconstructQuery(["stoneId"]));
      }

      $this->step = 1;
      if (isset($_GET["json"])) {
        if (isset($_POST["next"]) || isset($_POST["prev"])) {
          $this->step1Post();
        }
        else {
          $this->step1Get();
        }
      }

      $this->quotation = $this->initWizard();
    }

    public function step1Get() {

      $quotation = $this->initWizard();
      if ($quotation->stoneCategoryId == "") {
        //er is geen stoneCategoryId gekozen. Waarschijnlijk sessie verlopen, redirect.
        ResponseHelper::responseError("Geen steen category gekozen. U word doorgestuurd naar stap 1.", $this->wizardurl);
      }
      $quotationInfo = new GetStoneCategoryInfo($quotation->stoneCategoryId);
      $brandIds = (new GetBrandIdsByStone($quotationInfo->getType(), $quotationInfo->getMaterial()))->getBrandids();

      $brand_filt = [];
      if (!SandboxUsers::isAdmin()) $brand_filt["display"] = "true";
      $brand_filt["brandId"] = $brandIds;
      $brands = AppModel::mapObjectIds(StoneBrands::find_all_by($brand_filt, "ORDER BY displayOrder"), "brandId");

      $stone = false;
      if ($quotation->stoneId != "" && $quotation->stoneId != 0) {
        $stone = Stones::find_by(["stoneId" => $quotation->stoneId]);
      }

      //alleen merken tonen waarop rechten.
      if (isset($_SESSION['userObject']) && !$_SESSION['userObject']->isPrivate() && $_SESSION['userObject']->hasCompany()) {
        if ($_SESSION['userObject']->company->stJoris == 0) {
          unset($brands[StoneBrands::BRAND_ID_STJORIS]);
        }
        if ($_SESSION['userObject']->company->terca == 0) {
          unset($brands[StoneBrands::BRAND_ID_WIENERBERGER]);
        }
      }

      $sizes = StoneSizes::getStonesizesByCategoryId($quotation->stoneCategoryId, $brandIds);

      $colors = StoneColors::getDisplayColors($brandIds, $quotationInfo->getType());
      foreach ($sizes as $size) {
        if (isset($colors[$size->stone->colorId]) && $size->stone->image != "" && !isset($colors[$size->stone->colorId]->image)) {
          $colors[$size->stone->colorId]->image = "//www.raamdorpel.nl/images/thresholds/" . $size->stone->image;
        }
      }

      //verwijder merken welke geen kleuren en geen maten hebben.
      foreach ($brands as $k => $brand) {
        //zoeken of er een kleur is
        $found = false;
        foreach ($colors as $color) {
          if ($color->brandId == $brand->brandId) {
            $found = true;
            break;
          }
        }
        if (!$found) {
          unset($brands[$k]);
          continue;
        }

        //zoeken of er een maat is
        $found = false;
        foreach ($sizes as $size) {
          if ($size->brandId == $brand->brandId) {
            $found = true;
            break;
          }
        }
        if (!$found) {
          unset($brands[$k]);
        }

      }

      if (count($brands) == 0) {
        ResponseHelper::responseError("De door u gemaakte keuze bevat geen producten. Selecteer een andere optie, of neem contact op met ons.", $this->wizardurl . "?action=wizard");
      }

      $data = new stdClass();
      $data->brands = array_values($brands);
      $data->colors = array_values($colors);
      $data->sizes = $sizes;
      $data->step = 1;
      $data->quotation = $quotation;
      $data->showNaturalStoneWallCopingFlat = $quotationInfo->isNatuursteenMuurafdekkerPlat();
      $data->showDiepteDikte = $quotationInfo->isNatuursteen() && $quotationInfo->isRaamdorpel();
      $data->showModel = !$quotationInfo->isBalkje() && !$quotationInfo->isNatuursteenMuurafdekkerPlat();
      $data->showEndstone = ($quotationInfo->isKeramiek() && $quotationInfo->isRaamdorpel()) || $quotationInfo->isIsosill();
      $data->material = $stone ? $stone->getMaterial() : '';
      $data->variant = $stone ? $stone->getType() : '';
      $data->isIsosill = $quotationInfo->isIsosill();

      ResponseHelper::responseSuccess($data);
    }

      /**
       * @throws GsdException
       */
      public function step1Post() {

      $response = [];
      $response["errors"] = [];

      $quotation = $this->initWizard();

      $quotation->projectName = trim($_POST["projectName"]);
      $quotation->projectReference = trim($_POST["projectReference"]);

      if (isset($_POST["prev"])) {
        $_SESSION["wizard"]['quotation'] = $quotation;
        ResponseHelper::responseSuccess($response, reconstructQuery(["step", "json"]) . "step=0");
      }

      $quotation->brandId = trim($_POST["brandId"]);
      $quotation->colorId = trim($_POST["colorId"]);

      $ralColor = trim($_POST["ralColor"]);
      $quotation->ralColor = !empty($ralColor) ? $ralColor : null;

      $quotationInfo = new GetStoneCategoryInfo($quotation->stoneCategoryId);
      if ($quotationInfo->isBalkje()) {

        //bij balkje altijd 1ste size
        $brandIds = (new GetBrandIdsByStone($quotationInfo->getType(), $quotationInfo->getMaterial()))->getBrandids();
        $sizes = StoneSizes::getStonesizesByCategoryId($quotation->stoneCategoryId, $brandIds);
        $size = $sizes[0];
        $quotation->sizeId = $size->sizeId;
        $stone = Stones::find_by(["sizeId" => $size->sizeId, "colorId" => $quotation->colorId, "brandId"=>$quotation->brandId]);
      } else if ($quotationInfo->isIsosill()) {
        if (isset($_POST["sizeId"])) $quotation->sizeId = trim($_POST["sizeId"]);
        if (isset($_POST["endstone"])) $quotation->endstone = trim($_POST["endstone"]);
        $stone = Stones::find_by(["sizeId" => $quotation->sizeId, "colorId" => $quotation->colorId, "brandId" => $quotation->brandId, "endstone" => $quotation->endstone]);
      } else {
        if (isset($_POST["sizeId"])) $quotation->sizeId = trim($_POST["sizeId"]);
        if (isset($_POST["endstone"])) $quotation->endstone = trim($_POST["endstone"]);
        $stone = Stones::find_by(["stoneId" => trim($_POST["stoneId"])]);
      }

      if (!$stone) {
        $response["errors"][] = "Steen niet gevonden. (Steen: " . $_POST["stoneId"] . ")";
      }
      else {
        $quotation->stoneId = $stone->stoneId;
        $quotation->stoneCategoryId = $stone->category_id;
      }

      if (isset($_POST["custom_depth"])) {
        //dit is een raamdorpel op maat
        $quotation->custom_stone = new QuotationsCustomStone();
        $quotation->custom_stone->depth = $_POST["custom_depth"];
        $quotation->custom_stone->thickness = $_POST["custom_thickness"];
        $quotation->custom_stone->height = $_POST["custom_height"];
        $quotation->custom_stone->width_click = $_POST["custom_width_click"];
        $quotation->custom_stone->height_click = (int)$_POST["custom_height_click"];

        if (empty($quotation->custom_stone->depth) || empty($quotation->custom_stone->thickness) || empty($quotation->custom_stone->height) || empty($quotation->custom_stone->width_click)) {
          $response["errors"][] = "U heeft niet alle waardes juist ingvuld. Alle waardes moeten groter dan 0 zijn.";
        }

      }
      elseif (isset($quotation->custom_stone)) {
        unset($quotation->custom_stone);
      }

      if (count($response["errors"]) > 0) {
        ResponseHelper::responseSuccess($response);
      }

      $_SESSION["wizard"]['quotation'] = $quotation;
      ResponseHelper::responseSuccess($response, reconstructQuery(["step", "json"]) . "step=2");

    }


  }