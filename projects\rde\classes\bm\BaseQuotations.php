<?php
class BaseQuotations extends AppModel {

  const DB_NAME = 'rde_b1mo';
  const TABLE_NAME = 'quotations';
  const OM_CLASS_NAME = 'Quotations';
  const columns = ['quotationId', 'companyId', 'userId', 'codeId', 'statusId', 'brandId', 'stoneId', 'stoneCategoryId', 'invoiceId', 'invoiceNumber', 'invoiceDate', 'quotationDate', 'priceDate', 'orderDate', 'dueDate', 'dueDateWeek', 'produceDate', 'productionDate', 'packingDate', 'nextRouteDate', 'quotationNumber', 'quotationVersion', 'quotationPart', 'projectName', 'projectReference', 'projectType', 'projectValue', 'freightCosts', 'meters', 'metersMuch', 'largeWorkList', 'largeworklistinfo', 'weight', 'weight_webshop', 'stoneAmount', 'endstone', 'shorter', 'maxElementSize', 'pickup', 'street', 'nr', 'ext', 'zipcode', 'domestic', 'country', 'customerNotes', 'internNotes', 'productionNotes', 'deliveryNotes', 'flaggedForDeletion', 'specialFreightCost', 'specialFreightCostPrice', 'mountingFlag', 'mountingPrice', 'mountingPriceXMeters', 'dispatchAppointment', 'paymentMethod', 'mollie_id', 'cashPayment', 'cashPaymentPrice', 'toNumberQuotations', 'sendMailToClient', 'callOrEmailNotes', 'noRackQuotations', 'noContainerQuotations', 'noRackNoContainerQuotations', 'palletQuotations', 'afhalenQuotations', 'urgencyFlag', 'onHoldFlag', 'calledFlag', 'executor', 'executorMobile', 'executorMail', 'executorPersonId', 'mattingOnlyGlue', 'mattingOnlyGlueFlag', 'mattingRemovalDiscount', 'mattingRemovalDiscountFlag', 'offerteType', 'offerteVariant', 'createdVia', 'payedFlag', 'proformaFlag', 'ralColor'];
  const field_structure = [
    'quotationId'                 => ['type' => 'int', 'length' => '10', 'null' => false],
    'companyId'                   => ['type' => 'int', 'length' => '7', 'null' => true],
    'userId'                      => ['type' => 'int', 'length' => '8', 'null' => false],
    'codeId'                      => ['type' => 'int', 'length' => '3', 'null' => false],
    'statusId'                    => ['type' => 'int', 'length' => '2', 'null' => false],
    'brandId'                     => ['type' => 'smallint', 'length' => '2', 'null' => false],
    'stoneId'                     => ['type' => 'int', 'length' => '4', 'null' => false],
    'stoneCategoryId'             => ['type' => 'mediumint', 'length' => '8', 'null' => true],
    'invoiceId'                   => ['type' => 'int', 'length' => '10', 'null' => true],
    'invoiceNumber'               => ['type' => 'varchar', 'length' => '255', 'null' => true],
    'invoiceDate'                 => ['type' => 'date', 'length' => '', 'null' => true],
    'quotationDate'               => ['type' => 'date', 'length' => '', 'null' => false],
    'priceDate'                   => ['type' => 'date', 'length' => '', 'null' => false],
    'orderDate'                   => ['type' => 'date', 'length' => '', 'null' => true],
    'dueDate'                     => ['type' => 'date', 'length' => '', 'null' => true],
    'dueDateWeek'                 => ['type' => 'varchar', 'length' => '3', 'null' => true],
    'produceDate'                 => ['type' => 'datetime', 'length' => '', 'null' => true],
    'productionDate'              => ['type' => 'date', 'length' => '', 'null' => true],
    'packingDate'                 => ['type' => 'date', 'length' => '', 'null' => true],
    'nextRouteDate'               => ['type' => 'date', 'length' => '', 'null' => true],
    'quotationNumber'             => ['type' => 'varchar', 'length' => '9', 'null' => true],
    'quotationVersion'            => ['type' => 'tinyint', 'length' => '3', 'null' => false],
    'quotationPart'               => ['type' => 'tinyint', 'length' => '3', 'null' => true],
    'projectName'                 => ['type' => 'varchar', 'length' => '30', 'null' => true],
    'projectReference'            => ['type' => 'varchar', 'length' => '20', 'null' => true],
    'projectType'                 => ['type' => 'enum', 'length' => '2', 'null' => true, 'enums' => ['newbuilt','renovation']],
    'projectValue'                => ['type' => 'decimal', 'length' => '8,2', 'null' => false],
    'freightCosts'                => ['type' => 'decimal', 'length' => '8,2', 'null' => true],
    'meters'                      => ['type' => 'decimal', 'length' => '7,2', 'null' => false],
    'metersMuch'                  => ['type' => 'enum', 'length' => '2', 'null' => false, 'enums' => ['false','true']],
    'largeWorkList'               => ['type' => 'enum', 'length' => '11', 'null' => false, 'enums' => ['overzicht','gekregen','onderaannemer','via handel','niet gekregen','concurent','te duur','handel','ander product','geannuleerd','anders']],
    'largeworklistinfo'           => ['type' => 'text', 'length' => '', 'null' => true],
    'weight'                      => ['type' => 'decimal', 'length' => '9,3', 'null' => false],
    'weight_webshop'              => ['type' => 'mediumint', 'length' => '6', 'null' => false],
    'stoneAmount'                 => ['type' => 'int', 'length' => '5', 'null' => false],
    'endstone'                    => ['type' => 'enum', 'length' => '10', 'null' => false, 'enums' => ['false','true','true_grooves','left','right','leftg','rightg','flat','standingside','stuc']],
    'shorter'                     => ['type' => 'tinyint', 'length' => '2', 'null' => true],
    'maxElementSize'              => ['type' => 'decimal', 'length' => '3,2', 'null' => true],
    'pickup'                      => ['type' => 'enum', 'length' => '2', 'null' => false, 'enums' => ['false','true']],
    'street'                      => ['type' => 'varchar', 'length' => '150', 'null' => true],
    'nr'                          => ['type' => 'int', 'length' => '5', 'null' => true],
    'ext'                         => ['type' => 'varchar', 'length' => '10', 'null' => true],
    'zipcode'                     => ['type' => 'varchar', 'length' => '10', 'null' => true],
    'domestic'                    => ['type' => 'varchar', 'length' => '150', 'null' => true],
    'country'                     => ['type' => 'varchar', 'length' => '2', 'null' => true],
    'customerNotes'               => ['type' => 'text', 'length' => '', 'null' => true],
    'internNotes'                 => ['type' => 'text', 'length' => '', 'null' => true],
    'productionNotes'             => ['type' => 'text', 'length' => '', 'null' => true],
    'deliveryNotes'               => ['type' => 'text', 'length' => '', 'null' => true],
    'flaggedForDeletion'          => ['type' => 'boolean', 'length' => '1', 'null' => false],
    'specialFreightCost'          => ['type' => 'boolean', 'length' => '1', 'null' => false],
    'specialFreightCostPrice'     => ['type' => 'decimal', 'length' => '8,2', 'null' => false],
    'mountingFlag'                => ['type' => 'boolean', 'length' => '1', 'null' => false],
    'mountingPrice'               => ['type' => 'decimal', 'length' => '8,2', 'null' => false],
    'mountingPriceXMeters'        => ['type' => 'varchar', 'length' => '255', 'null' => false],
    'dispatchAppointment'         => ['type' => 'text', 'length' => '', 'null' => true],
    'paymentMethod'               => ['type' => 'varchar', 'length' => '20', 'null' => false],
    'mollie_id'                   => ['type' => 'mediumint', 'length' => '8', 'null' => true],
    'cashPayment'                 => ['type' => 'boolean', 'length' => '1', 'null' => false],
    'cashPaymentPrice'            => ['type' => 'decimal', 'length' => '8,2', 'null' => false],
    'toNumberQuotations'          => ['type' => 'boolean', 'length' => '1', 'null' => false],
    'sendMailToClient'            => ['type' => 'boolean', 'length' => '1', 'null' => false],
    'callOrEmailNotes'            => ['type' => 'text', 'length' => '', 'null' => true],
    'noRackQuotations'            => ['type' => 'boolean', 'length' => '1', 'null' => false],
    'noContainerQuotations'       => ['type' => 'boolean', 'length' => '1', 'null' => false],
    'noRackNoContainerQuotations' => ['type' => 'boolean', 'length' => '1', 'null' => false],
    'palletQuotations'            => ['type' => 'boolean', 'length' => '1', 'null' => false],
    'afhalenQuotations'           => ['type' => 'boolean', 'length' => '1', 'null' => false],
    'urgencyFlag'                 => ['type' => 'boolean', 'length' => '1', 'null' => false],
    'onHoldFlag'                  => ['type' => 'boolean', 'length' => '1', 'null' => false],
    'calledFlag'                  => ['type' => 'boolean', 'length' => '1', 'null' => false],
    'executor'                    => ['type' => 'varchar', 'length' => '255', 'null' => true],
    'executorMobile'              => ['type' => 'varchar', 'length' => '255', 'null' => true],
    'executorMail'                => ['type' => 'varchar', 'length' => '255', 'null' => true],
    'executorPersonId'            => ['type' => 'int', 'length' => '8', 'null' => true],
    'mattingOnlyGlue'             => ['type' => 'decimal', 'length' => '8,2', 'null' => true],
    'mattingOnlyGlueFlag'         => ['type' => 'boolean', 'length' => '1', 'null' => false],
    'mattingRemovalDiscount'      => ['type' => 'decimal', 'length' => '8,2', 'null' => true],
    'mattingRemovalDiscountFlag'  => ['type' => 'boolean', 'length' => '1', 'null' => false],
    'offerteType'                 => ['type' => 'varchar', 'length' => '255', 'null' => true],
    'offerteVariant'              => ['type' => 'varchar', 'length' => '50', 'null' => true],
    'createdVia'                  => ['type' => 'varchar', 'length' => '20', 'null' => false],
    'payedFlag'                   => ['type' => 'boolean', 'length' => '1', 'null' => false],
    'proformaFlag'                => ['type' => 'boolean', 'length' => '1', 'null' => false],
    'ralColor'                    => ['type' => 'char', 'length' => '4', 'null' => true],
  ];

  protected static array $primary_key = ['quotationId'];
  protected string $auto_increment = 'quotationId';

  public $quotationId, $companyId, $userId, $codeId, $statusId, $brandId, $stoneId, $stoneCategoryId, $invoiceId, $invoiceNumber, $invoiceDate, $quotationDate, $priceDate, $orderDate, $dueDate, $dueDateWeek, $produceDate, $productionDate, $packingDate, $nextRouteDate, $quotationNumber, $quotationVersion, $quotationPart, $projectName, $projectReference, $projectType, $projectValue, $freightCosts, $meters, $metersMuch, $largeWorkList, $largeworklistinfo, $weight, $weight_webshop, $stoneAmount, $endstone, $shorter, $maxElementSize, $pickup, $street, $nr, $ext, $zipcode, $domestic, $country, $customerNotes, $internNotes, $productionNotes, $deliveryNotes, $flaggedForDeletion, $specialFreightCost, $specialFreightCostPrice, $mountingFlag, $mountingPrice, $mountingPriceXMeters, $dispatchAppointment, $paymentMethod, $mollie_id, $cashPayment, $cashPaymentPrice, $toNumberQuotations, $sendMailToClient, $callOrEmailNotes, $noRackQuotations, $noContainerQuotations, $noRackNoContainerQuotations, $palletQuotations, $afhalenQuotations, $urgencyFlag, $onHoldFlag, $calledFlag, $executor, $executorMobile, $executorMail, $executorPersonId, $mattingOnlyGlue, $mattingOnlyGlueFlag, $mattingRemovalDiscount, $mattingRemovalDiscountFlag, $offerteType, $offerteVariant, $createdVia, $payedFlag, $proformaFlag, $ralColor;

  function __construct($obj_arr = [], &$error_codes = []) {
    parent::__construct();
    $this->setDefaults();
    if (!empty($obj_arr)) {
      $this->from_array($obj_arr);
      $this->valid_required_fields($error_codes);
    }
  }

  /**
  * Set default object values
  */
  public function setDefaults() {
    $this->brandId = 0;
    $this->stoneId = 0;
    $this->quotationVersion = 0;
    $this->projectType = 'renovation';
    $this->metersMuch = 'false';
    $this->largeWorkList = 'overzicht';
    $this->weight = 0.000;
    $this->weight_webshop = 0;
    $this->pickup = 'false';
    $this->flaggedForDeletion = 0;
    $this->specialFreightCost = 0;
    $this->specialFreightCostPrice = 0.00;
    $this->mountingFlag = 0;
    $this->mountingPrice = 0.00;
    $this->mountingPriceXMeters = 0;
    $this->paymentMethod = 'overmaken';
    $this->cashPayment = 0;
    $this->cashPaymentPrice = 0.00;
    $this->toNumberQuotations = 0;
    $this->sendMailToClient = 0;
    $this->noRackQuotations = 0;
    $this->noContainerQuotations = 0;
    $this->noRackNoContainerQuotations = 0;
    $this->palletQuotations = 0;
    $this->afhalenQuotations = 0;
    $this->urgencyFlag = 0;
    $this->onHoldFlag = 0;
    $this->calledFlag = 0;
    $this->mattingOnlyGlue = 0.00;
    $this->mattingOnlyGlueFlag = 0;
    $this->mattingRemovalDiscountFlag = 0;
    $this->offerteVariant = 'keramische_raamdorpel';
    $this->createdVia = 'other';
    $this->payedFlag = 0;
    $this->proformaFlag = 0;
  }

  /**
   * @param array $conditions (required)
   * @param string $raw_sql (optional)
   * @return Quotations[]
   */
  public static function find_all_like(?array $conditions, string $raw_sql = ''): array {
    return parent::generic_find_all_like($conditions, static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param array|null $conditions (required)
   * @param string $raw_sql (optional)
   * @return Quotations[]
   */
  public static function find_all_by(?array $conditions, string $raw_sql = ''): array {
    return parent::generic_find_all_by($conditions, static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param string $raw_sql (optional)
   * @return Quotations[]
   */
  public static function find_all(string $raw_sql = ''): array {
    return parent::generic_find_all(static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param array|null $conditions (required)
   * @param string $raw_sql (optional)
   * @return Quotations|false
   */
  public static function find_by(?array $conditions, string $raw_sql = ''): Quotations|false {
    return parent::generic_find_by($conditions, static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param int|string|null $id (required)
   * @param string $raw_sql (optional)
   * @return Quotations|false
   */
  public static function find_by_id($id, string $raw_sql = ''): Quotations|false {
    return parent::generic_find_by_id($id, static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param array|null $conditions (required)
   * @param string $raw_sql (optional)
   * @return int
   */
  public static function count_all_by(?array $conditions, string $raw_sql = '') {
    return parent::generic_count_all_by($conditions, static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param array|null $conditions (required)
   * @param string $raw_sql (optional)
   * @return bool
   */
  public static function delete_by(?array $conditions, string $raw_sql = ''): bool {
    return parent::generic_delete_by($conditions, static::OM_CLASS_NAME, $raw_sql);
  }

}