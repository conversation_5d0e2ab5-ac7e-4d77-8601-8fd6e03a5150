<?php

  AppModel::loadBaseClass('BaseContainerImg');

  class ContainerImgModel extends BaseContainerImg {

    public function getFilepath() {
      return DIR_ROOT_HTTPDOCS . "/filesystem/raamdorpel/clients/container/" . $this->containerId . '/' . $this->filename;
    }

    /**
     * Get insertTS
     * @param string $format
     * @return string
     */
    public function getInsertTS(string $format = 'd-m-Y H:i:s'): string {
      return DateTimeHelper::formatDbDate($this->insertDate, $format);
    }

    public function save(&$errors = []) {
      if ($this->from_db == false || $this->insertDate == "0000-00-00 00:00:00") {
        $this->insertDate = date('Y-m-d H:i:s');
      }
      return parent::save($errors);
    }


  }