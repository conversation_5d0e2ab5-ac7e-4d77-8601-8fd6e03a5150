<?php

  AppModel::loadBaseClass('BaseContainersQuotations');

  class ContainersQuotationsModel extends BaseContainersQuotations {

    public static function getContainersByQuotation($quotationId) {

      $sQuery1 = "SELECT * FROM " . ContainersQuotations::getTablename() . " ";
      $sQuery1 .= "JOIN " . Containers::getTablename() . " ON containers.containerId = containers_quotations.containerId ";
      $sQuery1 .= "WHERE containers_quotations.quotationId=" . $quotationId . " ";
      $sQuery1 .= "AND returnDate IS NULL ";

      $result = DBConn::db_link()->query($sQuery1);

      $cqs = [];
      while ($row = $result->fetch_row()) {
        $cq = new ContainersQuotations();
        $cq->hydrate($row);
        $cq->from_db = true;

        $c = new Containers();
        $c->hydrate($row, count(ContainersQuotations::columns));
        $c->from_db = true;
        $cq->container = $c;

        $cqs[] = $cq;
      }

      return $cqs;
    }

    /**
     * Bak<PERSON> van deze klant die niet retour zijn.
     *
     * @return int
     */
    public static function getContainersAway() {

      $cqsAway = ContainersQuotations::find_all("WHERE returnDate IS NULL AND deliverDate<'" . date('Y-m-d', strtotime("-45 DAYS")) . "' ");

      $filt = "AND ( ";
      $filt .= "quotations.userId=" . $_SESSION['userObject']->userId . " ";
      if ($_SESSION['userObject']->companyId != "" && $_SESSION['userObject']->companyId != "0") {
        $filt .= " OR ";
        $filt .= " quotations.companyId=" . $_SESSION['userObject']->companyId . " ";
      }
      $filt .= ") ";
      $filt .= " AND quotations.statusId>=55 ";
      $filt .= "ORDER BY quotationDate ASC, quotationNumber ASC, quotationVersion ASC ";
      $quotations = AppModel::mapObjectIds(Quotations::find_all_by(["flaggedForDeletion" => 0], $filt), "quotationId");

      $counter = 0;
      $containers = [];
      foreach ($cqsAway as $cq) {
        if (isset($quotations[$cq->quotationId]) && !isset($containers[$cq->containerId])) {
          $containers[$cq->containerId] = $cq->containerId;
          $counter++;
        }
      }
      return $counter;
    }

    public function getDeliverDate($format = 'd-m-Y') {
      if ($this->deliverDate == "0000-00-00" || $this->deliverDate == "") {
        return "";
      }
      return date($format, strtotime($this->deliverDate));
    }

    public function getReturnDate($format = 'd-m-Y') {
      if ($this->returnDate == "0000-00-00" || $this->returnDate == "") {
        return "";
      }
      return date($format, strtotime($this->returnDate));
    }

    public function save(&$errors = []) {
      if ($this->deliverDate == "0000-00-00") {
        $this->deliverDate = null;
      }
      if ($this->returnDate == "0000-00-00") {
        $this->returnDate = null;
      }
      parent::save($errors);
    }


  }