<?php

  require_once DIR_INCLUDES . "barcodegen/Barcode.php";

  /**
   * <PERSON><PERSON> steen label pdf.
   *
   * Class LabelPdf
   */
  class LabelPdf extends GSDPDF {

    public $show = true;

    public function __construct($show = true) {
      parent::__construct();
      $this->AddFont('Nunito', '', 'Nunito-Regular.ttf', true);
      $this->AddFont('Nunito', 'B', 'Nunito-Bold.ttf', true);
      $this->AddFont('Nunito', 'BI', 'Nunito-BoldItalic.ttf', true);

      $this->show = $show;
      $this->SetAutoPageBreak(false);

    }

    public function Header() {
    }

    // Footer
    // - Public
    // Adds HeBlad Raamdorpelelementen page footer
    public function Footer() {
    }

    /**
     * @param int $stoneId
     * @return string filename
     * @throws BCGArgumentException
     * @throws BCGDrawException
     */
    public function generatePdf($stoneId) {

      $stone = Stones::find_by(["stoneId" => $stoneId]);
      $brand = StoneBrands::find_by(["brandId" => $stone->brandId]);
      $size = StoneSizes::find_by(["sizeId" => $stone->sizeId]);
      $color = StoneColors::find_by(["colorId" => $stone->colorId]);

      //generate datamatrix
      $barcodetxt = "stoneid;" . $stone->stoneId;
      $barcodefilepath = DIR_TEMP . 'barcode.png';
      $barcode = new Barcode($barcodefilepath);
      $barcode->generateDatamatrix($barcodetxt);

      $this->AddPage("L", [100, 50]);
      $this->SetFont('Nunito', 'B', 13);

      //image
      $this->SetLeftMargin(0);
      $this->Image($barcodefilepath, 5, 5, 40, 40, 'PNG');

      //text
      $this->SetLeftMargin(49);
      $this->SetY(8);

      $this->Cell(50, 7, $brand->name);
      $this->Ln(7);

      $this->Cell(50, 7, $color->name);
      $this->Ln(7);

      $sizename1 = number_format($size->length, 1, ",", "") . " x ";
      $this->Cell($this->GetStringWidth($sizename1), 7, $sizename1);

      $sizename2 = number_format($size->width, 1, ",", "");
      if ($size->width > 11) {
        $this->SetFont('Nunito', 'BI');
        $this->underline = true;
      }
      $this->Cell($this->GetStringWidth($sizename2), 7, $sizename2);
      $this->underline = false;
      $this->SetFont('Nunito', 'B');

      $sizename3 = " x ";
      $this->Cell($this->GetStringWidth($sizename3), 7, $sizename3);

      if ($size->height < 3 || $size->height > 3.3) {
        $this->SetFont('Nunito', 'BI');
        $this->underline = true;
      }
      $sizename4 = number_format($size->height, 1, ",", "");
      $this->Cell($this->GetStringWidth($sizename4), 7, $sizename4);
      $this->underline = false;
      $this->SetFont('Nunito', 'B');

      $this->Cell($this->GetStringWidth(" cm"), 7, " cm");
      $this->Ln(7);


      if ($size->click != 0) {
        $hoogte = intval($size->click) . " cm";
        if ($size->click > 2) {
          $hoogte .= " hoog";
        }
        $this->Cell(50, 7, $hoogte);
        $this->Ln(7);
      }

      $endstone = "Standaard";
      if ($stone->endstone == Stones::ENDSTONE_RIGHT) {
        $endstone = "Rechts";
      }
      elseif ($stone->endstone == Stones::ENDSTONE_LEFT) {
        $endstone = "Links";
      }
      elseif ($stone->endstone == Stones::ENDSTONE_RIGHTG || $stone->endstone == Stones::ENDSTONE_LEFTG) {
        $endstone = "Groef";
      }
      $this->Cell(50, 7, $endstone);
      $this->Ln(8);


      $this->SetDisplayMode('real');

      $filename = 'Label_' . StringHelper::slugify($stone->name) . '.pdf';
      if ($this->show) {
        $this->Output("I", $filename);
      }
      else {
        $this->Output("F", DIR_TEMP . $filename);
      }
      return $filename;

    }

  }