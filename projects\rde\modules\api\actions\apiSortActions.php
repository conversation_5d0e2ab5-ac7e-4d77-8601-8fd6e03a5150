<?php


  /**
   * Trait apiCmsActions
   * Sort actions
   */
  trait apiSortActions {

    public static function sortOrders($a, $b) {
      if ($a->addressSplit != "" && $b->addressSplit != "") {
        $val = strcmp($a->addressSplit, $b->addressSplit);
        if ($val == 0) { //gelijk. Dan op quotationnr
          return strcmp($a->quotationNumber, $b->quotationNumber);
        }

        return $val;
      }
      elseif ($a->addressSplit != "" && $b->addressSplit == "") {
        return -1;
      }
      elseif ($a->addressSplit == "" && $b->addressSplit != "") {
        return 1;
      }

      //beide leeg, sorteren op containerNumber/containerSort
      if (is_numeric($a->containerSort) && is_numeric($b->containerSort)) {
        if ($a->containerSort == $b->containerSort) {
          //gelijk. Dan op quotationnr
          return strcmp($a->quotationNumber, $b->quotationNumber);
        }

        return ($a->containerSort < $b->containerSort) ? -1 : 1;
      }
      elseif (is_numeric($a->containerSort) && !is_numeric($b->containerSort)) {
        return -1;
      }
      elseif (!is_numeric($a->containerSort) && is_numeric($b->containerSort)) {
        return 1;
      }
      return strcmp($a->containerSort, $b->containerSort);

    }

    public static function sortContainers($a, $b) {
      return $a["containerNumber"] - $b["containerNumber"];
    }

    public static function sortContainersObj($a, $b) {
      return $a->containerNumber - $b->containerNumber;
    }

    public static function sortPersons($a, $b) {
      if ($a['type'] == "executor" && $b['type'] == "contact") {
        return -1;
      }
      if ($a['type'] == "contact" && $b['type'] == "executor") {
        return 1;
      }
      if ($a['type'] == "executor" && $b['type'] == "executor") {
        if (isset($a['defaultexecutor']) && !isset($b['defaultexecutor'])) {
          return -1;
        }
        elseif (!isset($a['defaultexecutor']) && isset($b['defaultexecutor'])) {
          return 1;
        }
      }
      //      return strcmp($a['name'],$b['name']);
      if ($a['type'] == $b['type']) {
        return strcmp($a['name'], $b['name']);
      }

      return strcmp($a['type'], $b['type']);
    }

  }