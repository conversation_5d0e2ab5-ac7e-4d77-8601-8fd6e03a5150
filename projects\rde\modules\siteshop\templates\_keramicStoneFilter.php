<form method="post" id="searchform">
  <div class="row stone_filter">
    <div>
      <div class="select">
        <select name="f_brand_id" id="f_brand_id">
          <option value=""><?php echo __('Filter op merk') ?>...</option>
          <?php foreach($brands as $brand): ?>
            <option value="<?php echo $brand->id ?>" <?php writeIfSelectedVal($_SESSION["f_brand_id"],$brand->id) ?>><?php echo $brand->getName() ?></option>
          <?php endforeach; ?>
        </select>
      </div>
    </div>

    <div>
      <div class="select">
        <select name="f_glaced" id="f_glaced">
          <option value=""><?php echo __('Filter op glazuur') ?>...</option>
          <option value="true" <?php writeIfSelectedVal($_SESSION["f_glaced"],'true') ?>><?php echo __('Geglazuurd') ?>...</option>
          <option value="false" <?php writeIfSelectedVal($_SESSION["f_glaced"],'false') ?>><?php echo __('Ongeglazuurd') ?>...</option>
        </select>
      </div>
    </div>

    <div>
      <div class="select">
        <select name="f_color_id" id="f_color_id">
          <option value=""><?php echo __('Filter op kleur') ?>...</option>
          <?php foreach($colors as $name=>$group): ?>
            <optgroup label="<?php echo $name ?>">
              <?php foreach($group as $color): ?>
                <option value="<?php echo $color->colorId ?>" <?php writeIfSelectedVal($_SESSION["f_color_id"],$color->colorId) ?>><?php echo $color->name ?></option>
              <?php endforeach; ?>
            </optgroup>
          <?php endforeach; ?>
        </select>
      </div>
    </div>

    <div>
      <div class="select">
        <select name="f_size_id" id="f_size_id">
          <option value=""><?php echo __('Filter op maat') ?>...</option>
          <?php foreach($sizes as $name=>$group): ?>
            <optgroup label="<?php echo $name ?>">
              <?php foreach($group as $size): ?>
                <option value="<?php echo $size->sizeId ?>" <?php writeIfSelectedVal($_SESSION["f_size_id"],$size->sizeId) ?>><?php echo $size->name ?></option>
              <?php endforeach; ?>
            </optgroup>
          <?php endforeach; ?>
        </select>
      </div>
    </div>

    <button type="submit" name="filter" id="filter" class="btn" style="background-color: grey">ZOEKEN</button>

  </div>
</form>

<style>

  .stone_filter {
    display: flex;
    justify-content: space-between;
  }

  .stone_filter > div {
    padding: 0;
    width: 20%;
  }

  @media (max-width: 767px) {
    .stone_filter {
      display: block;
      padding: 0 10px;
    }

    .stone_filter > div {
      width: 100%;
      padding: 0 0 15px 0;
    }

    .stone_filter .btn {
      width: 100%;
    }
  }

</style>


<script type="text/javascript">

  $(document).ready(function(){
    $("#searchform select").change(function() {
      if($(this).attr("id")=="f_brand_id" && $(this).val()!="") {
        $("#f_color_id").val("");
        $("#f_size_id").val("");
      }
      if($(this).attr("id")=="f_glaced" && $(this).val()!="") {
        $("#f_color_id").val("");
        $("#f_size_id").val("");
      }
      if($(this).attr("id")=="f_color_id" && $(this).val()!="") {
        $("#f_size_id").val("");
      }
      $("#filter").click();
    });
  });

</script>