<?php

  AppModel::loadBaseClass('BaseStonePrices');

  class StonePricesModel extends BaseStonePrices {

    public static function getLatestPrices() {
      return AppModel::mapObjectIds(StonePrices::find_all("WHERE validFrom<NOW() AND validTo>NOW()"), "stoneId");
    }

    public static function getPricesNextYear() {
      return AppModel::mapObjectIds(StonePrices::find_all("WHERE YEAR(validFrom) = YEAR(CURDATE())+1 "), "stoneId");
    }

    public function getValidFrom($format = 'd-m-Y') {
      if ($this->validFrom == "0000-00-00" || $this->validFrom == "") {
        return "";
      }
      return date($format, strtotime($this->validFrom));
    }

    public function getValidTo($format = 'd-m-Y') {
      if ($this->validTo == "0000-00-00" || $this->validTo == "") {
        return "";
      }
      return date($format, strtotime($this->validTo));
    }

  }