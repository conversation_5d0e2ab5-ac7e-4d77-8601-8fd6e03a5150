<?php

  class invoicesRdeActions extends gsdActions {

    public function preExecute() {

      $this->homeurl = PageMap::getUrl(208);

      if (!isset($_SESSION["userObject"])) { //uitgelogd
        ResponseHelper::redirect($this->homeurl);
      }

      $this->site = $_SESSION['site'];
      if (is_numeric($this->pageId)) {
        $page = Page::getPageAndContent($this->pageId, $_SESSION['lang']);
        $this->seo_title = $page->content->getSeoTitle();
        $this->seo_description = $page->content->getSeoDescription();
        $this->page = $page;
      }
      $this->cats = Category::getShopCats();
    }

    public function executeList() {

      $userIds = [];
      $userIds[$_SESSION["userObject"]->userId] = $_SESSION["userObject"]->userId;
      if ($_SESSION["userObject"]->companyId != '') {
        $companyUsers = SandboxUsers::find_all_by(["companyId" => $_SESSION["userObject"]->companyId]);
        foreach ($companyUsers as $u) {
          $userIds[$u->userId] = $u->userId;
        }
      }
      $invoices = Invoices::find_all_by(["userId" => $userIds], "ORDER BY dateInvoice DESC");

      $this->invoices = $invoices;
      $this->paymentTerm = 14;
      if (isset($_SESSION['userObject']->company) && !empty($_SESSION['userObject']->company->paymentTerm)) {
        $this->paymentTerm = $_SESSION['userObject']->company->paymentTerm;
      }

    }

    public function executePdf() {
      if (!isset($_SESSION["userObject"])) { //uitgelogd
        ResponseHelper::redirect($this->homeurl);
      }
      $invoice = Invoices::find_by(["invoiceId" => $_GET["id"]]);

      $userIds = [];
      $userIds[$_SESSION["userObject"]->userId] = $_SESSION["userObject"]->userId;
      if ($_SESSION["userObject"]->companyId != '') {
        $companyUsers = SandboxUsers::find_all_by(["companyId" => $_SESSION["userObject"]->companyId]);
        foreach ($companyUsers as $u) {
          $userIds[$u->userId] = $u->userId;
        }
      }
      if ($invoice && $invoice->mayView($userIds)) {

        $codeCheck = sha1(date('Ymd') . $invoice->invoiceId . 'Tf2S@gA3sDj*eE#ypOJKJ!heeuh');
        $url = "https://www.raamdorpel.nl/cms/crm/pdfInvoiceMergeAll.php?type=printinvoicebyid&invoiceid=" . $invoice->invoiceId . "&code=" . $codeCheck;

        header('Content-Type: application/pdf');
        header('Content-Disposition: inline; filename="' . $invoice->invoiceNumber . '.pdf"');
        header('Cache-Control: private, max-age=0, must-revalidate');
        header('Pragma: public');

        echo file_get_contents($url);
        ResponseHelper::exit();
      }

      $_SESSION['flash_message_red'] = __("Factuur niet geopend. Factuur is niet gevonden, of u heeft geen rechten om deze te bekijken.");
      ResponseHelper::redirect(reconstructQueryAdd());
    }

    public function executePrepaid() {
      $quotations = [];
      $this->quotations = $quotations;
    }

  }