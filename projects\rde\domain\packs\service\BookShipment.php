<?php

  namespace domain\packs\service;

  use CrmCompanies;
  use CrmPersons;
  use gsdfw\plugins\packs\domain\exception\PacksException;
  use gsdfw\plugins\packs\domain\service\PacksApi;
  use Psr\Http\Message\ResponseInterface;
  use Quotations;
  use SandboxUsers;

  /**
   * Class BookShipment
   *
   * @package domain\packs\service
   */
  class BookShipment {

    var $packs_api;

    /**
     * BookShipment constructor.
     * @param PacksApi $packs_api
     */
    public function __construct($packs_api) {
      $this->packs_api = $packs_api;
    }

    /**
     * @param Quotations $order
     * @param        $zegel
     * @param        $gewicht
     * @param int $collo
     * @return bool|false|mixed|ResponseInterface|string
     * @throws PacksException
     */
    public function bookshipment(Quotations $quotation, $zegel, $gewicht, $collo = 1) {

      if (DEVELOPMENT) {
        return file_get_contents(DIR_PROJECT_FOLDER . 'domain/packs/service/packs_result_example.xml');
      }

      $company = CrmCompanies::find_by(["companyId" => $quotation->companyId]);
      $sandbox_user = SandboxUsers::find_by(["userId" => $quotation->userId]);
      $crm_person = CrmPersons::find_by(["personId" => $sandbox_user->personId, "flagForDeletion" => 0]);

      $send_companyname = '';
      if ($company) {
        $send_companyname = $company->name;
      }
      $send_contactname = '';
      if ($crm_person) {
        $send_contactname = $crm_person->getNaam();
      }
      if ($send_companyname == "") {
        $send_companyname = $send_contactname;
      }
      if ($send_companyname == "") {
        $send_companyname = "??";
      }
      $send_companyname = str_replace("‘", "'", $send_companyname);

      $send_address = trim($quotation->street);
      $send_number = trim($quotation->nr . " " . $quotation->ext);
      $send_zip = $quotation->zipcode;
      $send_city = $quotation->domestic;
      $send_country = "Nederland";

      //https://orders.packs.nl/Public/BookShipment.aspx?
      //username=XXXX&password=XXXXX&pdf=true&def=false&xml=false&layout=label10x15&
      //afz_naam=Packsladen&afz_straat=Inbrengstraat&afz_huisnr=1&afz_postcode=1745AS&afz_plaats=Packsinbreng&afz_contact=Sander&afz_Land=NL&
      //ont_naam=Packsbezorgen&ont_straat=Uitrijdstraat&ont_huisnr=2&ont_postcode=1745SA&ont_plaats=Packsuitrijd&ont_tav=TESTZENDING_6&ont_land=NL&
      //zegel=P&gewicht=5&aantal=1&laaddatum=16-10-2014&bezorgdatum=17-10-2014&referentie=TESTZENDING_6

      $parameters = [
        "username"     => $this->packs_api->getUsername(),
        "password"     => $this->packs_api->getPassword(),
        "xml"          => "true",
        //          "pdf"=>"true",
        "def"          => "true",
        //          "layout"=>"Label10X15",
        "ont_naam"     => DEVELOPMENT ? "PACKS TEST" : $send_companyname,
        "ont_straat"   => $send_address,
        "ont_huisnr"   => $send_number == "" ? '?' : $send_number,
        "ont_postcode" => $send_zip,
        "ont_plaats"   => $send_city,
        "ont_tav"      => $send_contactname,
        "ont_land"     => strtoupper($send_country),
        "zegel"        => $zegel,
        "gewicht"      => $gewicht,
        "aantal"       => $collo,
        "laaddatum"    => date("d-m-Y"),
        "bezorgdatum"  => date("d-m-Y", strtotime("+1 DAY")),
        "referentie"   => $quotation->getQuotationNumberFull(),
        "memo"         => "",
      ];

      if ($sandbox_user->email != "") {
        $parameters["email"] = $sandbox_user->email;
      }

      $action = '/Public/BookShipment.aspx';
      //        $action = '/Boeking/Shipment.aspx'; //handmatig
//      pd($parameters);ResponseHelper::exit();

      return $this->packs_api->request($parameters, $action);

    }

  }