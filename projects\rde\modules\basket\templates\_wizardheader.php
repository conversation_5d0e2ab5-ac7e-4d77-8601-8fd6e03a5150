<div class="steps" style="margin-top: 10px;">
  <div class="step_on">
    <?php if(1<$step): ?>
      <a href="<?php echo reconstructQueryAdd(['tender' => $tender ? 1 : 0, 'action' => 'pay1']) ?>">
    <?php endif; ?>

    <?php echo __('Verzendkosten') ?>

    <?php if(1<$step): ?>
      </a>
    <?php endif; ?>
  </div>

  <?php if ((count($paymethods) > 1 && !isset($tender)) || !$tender): ?>
    <div class="step_<?php echo 2 <= $step ? 'on' : 'off' ?>">
      <?php if (2 < $step): ?>
        <a href="<?php echo reconstructQueryAdd(['tender' => $tender ? 1 : 0, 'action' => 'pay2']) ?>">
      <?php endif; ?>

      <?php echo __('Betaalmethode') ?>

      <?php if (2 < $step): ?>
        </a>
      <?php endif; ?>
    </div>
  <?php endif; ?>

  <div class="step_<?php echo 3 <= $step ? 'on' : 'off' ?>">
    <?php if (3 < $step): ?>
      <a href="<?php echo reconstructQueryAdd(['tender' => $tender ? 1 : 0, 'action' => 'pay3']) ?>">
    <?php endif; ?>

    <?php echo __('Bevestigen') ?>

    <?php if (3 < $step): ?>
      </a>
    <?php endif; ?>
  </div>
  <div class="step_<?php echo 6<=$step?'on':'off'?>"><?php echo __('Gereed')?></div>
</div>