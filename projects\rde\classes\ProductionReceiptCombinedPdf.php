<?php
  /**
   * Class for combining production receipt for both stone elements and webshop products
   */
  class ProductionReceiptCombinedPdf extends GSDPDF {
    private $quotationId;

    public function __construct($quotationId) {
      parent::__construct();
      $this->quotationId = $quotationId;
    }

    private function includePdfs() {
      // Generate the main production PDF and include its pages
      $productionPdf = new ProductionReceiptPdf($this->quotationId);
      $productionPdf->setMultiple(true);
      $productionFilename = $productionPdf->generate();
      $productionFilepath = DIR_TEMP . $productionFilename;

      // Include all pages from the production PDF
      $pagecount = $this->setSourceFile($productionFilepath);
      for ($i = 1; $i <= $pagecount; $i++) {
        $tplidx = $this->importPage($i);
        $this->addPage();
        $this->useTemplate($tplidx, 0, 0);
      }

      // Generate the webshop PDF and include its pages
      $webshopPdf = new ProductionReceiptWebshopPdf($this->quotationId);
      $webshopPdf->setMultiple(true);
      $webshopFilename = $webshopPdf->generate();
      $webshopFilepath = DIR_TEMP . $webshopFilename;

      // Include all pages from the webshop PDF
      $pagecount = $this->setSourceFile($webshopFilepath);
      for ($i = 1; $i <= $pagecount; $i++) {
        $tplidx = $this->importPage($i);
        $this->addPage();
        $this->useTemplate($tplidx, 0, 0);
      }

      // Clean up temporary files
      if (file_exists($productionFilepath)) {
        unlink($productionFilepath);
      }
      if (file_exists($webshopFilepath)) {
        unlink($webshopFilepath);
      }
    }

    public function generate() {
      $this->includePdfs();

      $filename = 'production_receipt_' . $this->quotationId . '.pdf';
      $this->Output("I", $filename);

      return $filename;
    }
  }