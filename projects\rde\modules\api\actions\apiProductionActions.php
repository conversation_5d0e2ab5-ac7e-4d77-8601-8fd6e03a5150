<?php


  /**
   * Trait apiStoneActions
   * Used for calls from other rde cms
   */
  trait apiProductionActions {

    /**
     * Get production employee by nfcId
     */
    public function executeStartProductionEmployeeByNfcId() {
      $request_vars = $this->data->getData();

      if (!isset($request_vars["nfcid"])) {
        RestUtils::sendResponseError("Geen NFC id gezet.");
      }

      $wp = WorkerProfile::find_by(["code" => ["production_glue", "production_wash"], "value" => $request_vars["nfcid"]]);
      if (!$wp) {
        RestUtils::sendResponseError("Production employee met nfc-id " . $request_vars["nfcid"] . " niet gevonden.");
      }

      $worker = Worker::find_by_id($wp->worker_id);
      if (!$worker) {
        RestUtils::sendResponseError("Worker met externalId " . $wp->id . " niet gevonden.");
      }

      $employee = ProductionEmployees::find_by(["employeeId" => $worker->external_id]);
      if (!$employee) {
        RestUtils::sendResponseError("Production employee met id " . $wp->worker_id . " niet gevonden.");
      }

      if ($wp->code == "production_glue") {
        //lijmer
        //gaan kijken of er een record is zonder quotation, zo ja, update start tijd.
        $eq = EmployeeQuotation::find_by(["employeeId" => $employee->employeeId, "quotationId" => null]);
        if (!$eq) { //aanmaken
          $eq = new EmployeeQuotation();
          $eq->employeeId = $employee->employeeId;
        }
        $eq->startdate = date("Y-m-d H:i:s");
        $eq->save();

      }
      else {
        //wasser
        //gaan kijken of er een record is zonder quotation, zo ja, update start tijd.
        $eq = EmployeeWasherQuotation::find_by(["employeeId" => $employee->employeeId, "quotationId" => null]);
        if (!$eq) { //aanmaken
          $eq = new EmployeeWasherQuotation();
          $eq->employeeId = $employee->employeeId;
        }
        $eq->startdate = date("Y-m-d H:i:s");
        $eq->save();
      }


      $result = new stdClass();
      $result->employeeId = $employee->employeeId;
      $result->name = $worker->getNaam();
      $result->type = $wp->code == "production_glue" ? "glue" : "wash";
      $result->nfcid = $request_vars["nfcid"];
      RestUtils::sendResponseOK("EMPLOYEE GEVONDEN", $result);

    }

    public function executeStopProductionEmployeeByNfcId() {
      $request_vars = $this->data->getData();

      if (!isset($request_vars["nfcid"])) {
        RestUtils::sendResponseError("Geen NFC id gezet.");
      }

      $wp = WorkerProfile::find_by(["code" => ["production_glue", "production_wash"], "value" => $request_vars["nfcid"]]);
      if (!$wp) {
        RestUtils::sendResponseError("Production employee met nfc-id " . $request_vars["nfcid"] . " niet gevonden.");
      }

      $worker = Worker::find_by_id($wp->worker_id);
      if (!$worker) {
        RestUtils::sendResponseError("Worker met externalId " . $wp->id . " niet gevonden.");
      }

      $employee = ProductionEmployees::find_by(["employeeId" => $worker->external_id]);
      if (!$employee) {
        RestUtils::sendResponseError("Production employee met id " . $wp->worker_id . " niet gevonden.");
      }

      //bij het stoppen van een lijmer of wasser doen we niks.
      //we weten niet om welke quotation het gaat, deze is immers nog niet gescannt.

      $result = new stdClass();
      $result->employeeId = $employee->employeeId;
      $result->name = $worker->getNaam();
      $result->type = $wp->code == "production_glue" ? "glue" : "wash";
      $result->nfcid = $request_vars["nfcid"];

      RestUtils::sendResponseOK("EMPLOYEE GESTOPT", $result);

    }


    /**
     * Get rack by rackcode
     */
    public function executeGetRackByCode() {
      $request_vars = $this->data->getData();

      if (!isset($request_vars["code"])) {
        RestUtils::sendResponseError("Geen rek code gezet.");
      }
      $rackCode = substr($request_vars["code"], 4);
      $rack = GeneratedRackIds::find_by(["rackCode" => $rackCode]);
      if (!$rack) {
        RestUtils::sendResponseError("Rek niet gevonden met code " . $rackCode);
      }

      $result = new stdClass();
      $result->rackId = $rack->rackId;
      $result->rackCode = $rack->rackCode;
      RestUtils::sendResponseOK("REK GEVONDEN", $result);

    }

    /**
     * Start glue when
     *
     * http://beheer.raamdorpel.nl.rde.localhost/nl/api?action=ProcessQuotation&builder=6&washer=22&rackId=5350&quotationNr=22.157591-1
     *
     * @throws GsdException
     */
    public function executeProcessQuotation() {
      $request_vars = $this->data->getData();

      if (!isset($request_vars["quotationNr"])) {
        RestUtils::sendResponseError("Geen offertenummer gezet.");
      }
      if (!isset($request_vars["washer"])) {
        RestUtils::sendResponseError("Geen wasser gezet.");
      }
      if (!isset($request_vars["builder"])) {
        RestUtils::sendResponseError("Geen lijmer gezet.");
      }
      if (!isset($request_vars["rackId"])) {
        RestUtils::sendResponseError("Geen rek gezet.");
      }

      $quotationNr = $request_vars["quotationNr"];
      $washerEmployeeId = $request_vars["washer"];
      $builderEmployeeId = $request_vars["builder"];
      $rackId = $request_vars["rackId"];

      $quotationNrFull = explode("-", $quotationNr);
      $filt = [];
      $filt['quotationNumber'] = $quotationNrFull[0];
      $filt['quotationVersion'] = $quotationNrFull[1];
      if (!empty(trim($quotationNrFull[2]))) {
        $quotationPart = RdeHelper::AlphaToAsciiInt(trim($quotationNrFull[2]));
        $filt['quotationPart'] = $quotationPart;
      }
      $quotation = Quotations::find_by($filt);
      if (!$quotation) {
        RestUtils::sendResponseError("Bestelling niet gevonden met offertenummer " . $quotationNr);
      }

      //rack should be available
      $rack = GeneratedRackIds::find_by(["rackId" => $rackId]);
      if (!$rack) {
        RestUtils::sendResponseError("Rek niet gevonden met rackId " . $rackId);
      }

      //washer should be available
      $washer = ProductionEmployees::find_by(["employeeId" => $washerEmployeeId]);
      if (!$washer) {
        RestUtils::sendResponseError("Production employee wasser met id " . $washerEmployeeId . " niet gevonden.");
      }

      //builder/gluer should be available
      $builder = ProductionEmployees::find_by(["employeeId" => $builderEmployeeId]);
      if (!$builder) {
        RestUtils::sendResponseError("Production employee lijmer met id " . $builderEmployeeId . " niet gevonden.");
      }

      //is de laatste rack scan van deze rack+employee+washeremployee aanwezig, dan deze gebruiken.
      //deze tabel laat zien welke rekken zijn gebruikt door welke lijmer
      $rackEmployee = GeneratedRackIdsEmployees::find_by(["rackId" => $rack->rackId, "employeeId" => $builder->employeeId, "washerEmployeeId" => $washer->employeeId], "ORDER BY datetime DESC LIMIT 1");
      if ($rackEmployee && $rackEmployee->getDatetime("Y-m-d") != date("Y-m-d")) {
        //deze laatste rack scan is wel goede rack/washer/builder echter niet van vandaag. nieuwe toevoegen.
        $rackEmployee = false;
      }
      if (!$rackEmployee) { //nee, aanmaken maar
        $rackEmployee = new GeneratedRackIdsEmployees();
        $rackEmployee->employeeId = $builder->employeeId;
        $rackEmployee->washerEmployeeId = $washer->employeeId;
        $rackEmployee->rackId = $rack->rackId;
        $rackEmployee->save();
      }

      //dit is een koppeltabel tussen het rek en welke quotations erin liggen.
      //ook is zichtbaar wanneer ze in het rek zijn gelegd.
      $rackQuot = GeneratedRackidQuotationids::find_by(["rackId" => $rack->rackId, "quotationId" => $quotation->quotationId]);
      if (!$rackQuot) {
        //hoef maar 1 keer aangemaakt te worden
        $rackQuot = new GeneratedRackidQuotationids();
        $rackQuot->rackId = $rack->rackId;
        $rackQuot->quotationId = $quotation->quotationId;
        $rackQuot->datetime = date("Y-m-d H:i:s");
        $rackQuot->save();
      }

      //deze employeeQuotation updateDate bepaald uiteindelijk de verlijmsnelheid.
      //hier koppel ik de medewerker aan de quotation, weet ik de start en eind tijd van de productie

      //lijmer
      //1. is er een employee met deze quotation?
      $eq = EmployeeQuotation::find_by(["employeeId" => $builder->employeeId, "quotationId" => $quotation->quotationId]);
      if (!$eq) {
        //2. is er een lege quotation?
        $eq = EmployeeQuotation::find_by(["employeeId" => $builder->employeeId, "quotationId" => null]);
        if (!$eq) {
          //3. is er geen gevonden, nieuwe aanmaken, er is geen starttijd bekend.
          $eq = new EmployeeQuotation();
          $eq->employeeId = $builder->employeeId;
        }
      }
      $eq->quotationId = $quotation->quotationId;
      $eq->enddate = date("Y-m-d H:i:s");
      $eq->save();

      //wasser
      //1. is er een employee met deze quotation?
      $eq = EmployeeWasherQuotation::find_by(["employeeId" => $washer->employeeId, "quotationId" => $quotation->quotationId]);
      if (!$eq) {
        //2. is er een lege quotation?
        $eq = EmployeeWasherQuotation::find_by(["employeeId" => $washer->employeeId, "quotationId" => null]);
        if (!$eq) {
          //3. is er geen gevonden, nieuwe aanmaken, er is geen starttijd bekend.
          $eq = new EmployeeWasherQuotation();
          $eq->employeeId = $washer->employeeId;
        }
      }
      $eq->quotationId = $quotation->quotationId;
      $eq->enddate = date("Y-m-d H:i:s");
      $eq->save();

      //aangezien de eind datum is gezet, meteen een nieuwe start aanmaken.
      $eq = EmployeeQuotation::find_by(["employeeId" => $builder->employeeId, "quotationId" => null]);
      if (!$eq) { //aanmaken
        $eq = new EmployeeQuotation();
        $eq->employeeId = $builder->employeeId;
      }
      $eq->startdate = date("Y-m-d H:i:s");
      $eq->save();

      //wasser
      //gaan kijken of er een record is zonder quotation, zo ja, update start tijd.
      $eq = EmployeeWasherQuotation::find_by(["employeeId" => $washer->employeeId, "quotationId" => null]);
      if (!$eq) { //aanmaken
        $eq = new EmployeeWasherQuotation();
        $eq->employeeId = $washer->employeeId;
      }
      $eq->startdate = date("Y-m-d H:i:s");
      $eq->save();


      if (DEVELOPMENT) $quotation->statusId = Status::STATUS_PREPARED;

      if ($quotation->statusId == Status::STATUS_PREPARED) {

        $this->updateStock($quotation);

        $quotation->statusId = Status::STATUS_PRODUCED;
        $quotation->produceDate = date("Y-m-d H:i:s");
        $quotation->save();

        QuoteStatus35::delete_by(["quotationId" => $quotation->quotationId]);

      }
      elseif ($quotation->statusId != Status::STATUS_PRODUCED) { //niet 35 en niet 40 (dubbele scan)
        $this->sendErrorMail("Bestelling " . $quotation->getQuotationNumberFull() . " gescant echter deze heeft niet status 35/40 maar status " . $quotation->statusId);
      }


      $result = new stdClass();
      $result->quotationId = $quotation->quotationId;
      $result->quotationNumberFull = $quotation->getQuotationNumberFull();

      RestUtils::sendResponseOK("QUOTATION GEVONDEN", $result);

    }

    /**
     * Update stock and send change stock email.
     * @param Quotations $quotation
     * @return void
     * @throws GsdException
     */
    private function updateStock(Quotations $quotation): void {

      $quotationExtra = QuotationsExtra::find_by(["quotationId" => $quotation->quotationId]);
      $stone = Stones::find_by(["stoneId" => $quotation->stoneId]);
      $brand = StoneBrands::find_by(["brandId" => $stone->brandId]);
      $stoneColor = StoneColors::find_by(["colorId" => $stone->colorId]);
      $stoneSize = StoneSizes::find_by(["sizeId" => $stone->sizeId]);

      $stoneDataMail = '<p>Offerte: ' . $quotation->getQuotationNumberFull() . '</p>';
      $stoneDataMail .= '<p>Merk: ' . $brand->name . '</p>';
      if($stoneColor) $stoneDataMail .= '<p>Kleur: ' . $stoneColor->getFullname() . '</p>';
      $stoneDataMail .= '<p>Model: ' . ($stoneSize && !$stone->isBalkje() ? $stoneSize->name : 'Divers') . '</p>';

      if (in_array($stone->brandId, [1, 2])) {
        //alleen van wienerberger en stjoris beheren we voorraad
        $stoneEnd = "?";
        if ($stone->endstone == 'true_grooves') {
          $stoneEnd = 'Ja, groeven';
        }
        elseif ($stone->endstone == 'true') {
          $stoneEnd = 'Ja, opstaande zijkanten';
        }
        elseif ($stone->endstone == 'false') {
          $stoneEnd = 'Nee';
        }

        $stoneDataMail .= '<p>Steen einde: ' . $stoneEnd . '</p>';
        $stoneDataMail .= '<hr />';

        $middelStone = Stones::find_by(["brandId" => $stone->brandId, "colorId" => $stone->colorId, "sizeId" => $stone->sizeId, "endstone" => "false"]);

        if ($middelStone->stock >= $quotationExtra->totalMiddlesStones) {

          $oldStock = $middelStone->stock;
          $middelStone->stock = $middelStone->stock - $quotationExtra->totalMiddlesStones;
          $middelStone->save();

          $stoneDataMail .= '<p>Midden stock aantal: ' . $oldStock . '</p>';
          $stoneDataMail .= '<p>Midden stenen aantal: ' . $quotationExtra->totalMiddlesStones . '</p>';
          $stoneDataMail .= '<p>Midden new stock aantal: ' . $middelStone->stock . '</p>';
        }
        else {
          $stoneDataMail .= '<p>Er zijn te weinig stenen. Aantal is niet verwijderd van voorraad.</p>';
          $stoneDataMail .= '<p>Midden stock aantal: ' . $middelStone->stock . '</p>';
          $stoneDataMail .= '<p>Midden stenen aantal: ' . $quotationExtra->totalMiddlesStones . '</p>';
        }
        $stoneDataMail .= '<hr />';

        if ($quotation->endstone === 'true') {

          $leftStone = Stones::find_by(["brandId" => $stone->brandId, "colorId" => $stone->colorId, "sizeId" => $stone->sizeId, "endstone" => "left"]);

          if ($leftStone->stock >= $quotationExtra->totalLeftEndStones) {

            $oldStock = $leftStone->stock;
            $leftStone->stock = $leftStone->stock - $quotationExtra->totalLeftEndStones;
            $leftStone->save();

            $stoneDataMail .= '<p>Left stock aantal: ' . $oldStock . '</p>';
            $stoneDataMail .= '<p>Left stenen aantal: ' . $quotationExtra->totalLeftEndStones . '</p>';
            $stoneDataMail .= '<p>Left new stock aantal: ' . $leftStone->stock . '</p>';
          }
          else {
            $stoneDataMail .= '<p>Er zijn te weinig stenen. Aantal is niet verwijderd van voorraad.</p>';
            $stoneDataMail .= '<p>Left stock aantal: ' . $leftStone->stock . '</p>';
            $stoneDataMail .= '<p>Left stenen aantal: ' . $quotationExtra->totalLeftEndStones . '</p>';
          }
          $stoneDataMail .= '<hr />';

          $rightStone = Stones::find_by(["brandId" => $stone->brandId, "colorId" => $stone->colorId, "sizeId" => $stone->sizeId, "endstone" => "right"]);

          if ($rightStone->stock >= $quotationExtra->totalRightEndStones) {

            $oldStock = $rightStone->stock;
            $rightStone->stock = $rightStone->stock - $quotationExtra->totalRightEndStones;
            $rightStone->save();

            $stoneDataMail .= '<p>Right stock aantal: ' . $oldStock . '</p>';
            $stoneDataMail .= '<p>Right stenen aantal: ' . $quotationExtra->totalRightEndStones . '</p>';
            $stoneDataMail .= '<p>Right new stock aantal: ' . $rightStone->stock . '</p>';

          }
          else {

            $stoneDataMail .= '<p>Er zijn te weinig stenen. Aantal is niet verwijderd van voorraad.</p>';
            $stoneDataMail .= '<p>Right stock aantal: ' . $rightStone->stock . '</p>';
            $stoneDataMail .= '<p>Right stenen aantal: ' . $quotationExtra->totalRightEndStones . '</p>';

          }
          $stoneDataMail .= '<hr />';


        }
        elseif ($quotation->endstone === 'true_grooves') {

          $leftStoneGrooves = Stones::find_by(["brandId" => $stone->brandId, "colorId" => $stone->colorId, "sizeId" => $stone->sizeId, "endstone" => "leftg"]);

          if ($leftStoneGrooves->stock >= $quotationExtra->totalLeftEndStonesGrooves) {

            $oldStock = $leftStoneGrooves->stock;
            $leftStoneGrooves->stock = $leftStoneGrooves->stock - $quotationExtra->totalLeftEndStonesGrooves;
            $leftStoneGrooves->save();

            $stoneDataMail .= '<p>Leftgrooves stock aantal: ' . $oldStock . '</p>';
            $stoneDataMail .= '<p>Leftgrooves stenen aantal: ' . $quotationExtra->totalLeftEndStonesGrooves . '</p>';
            $stoneDataMail .= '<p>Leftgrooves new stock aantal: ' . $leftStoneGrooves->stock . '</p>';
          }
          else {
            $stoneDataMail .= '<p>Er zijn te weinig stenen. Aantal is niet verwijderd van voorraad.</p>';
            $stoneDataMail .= '<p>leftgrooves stock aantal: ' . $leftStoneGrooves->stock . '</p>';
            $stoneDataMail .= '<p>leftgrooves stenen aantal: ' . $quotationExtra->totalLeftEndStonesGrooves . '</p>';
          }


          $stoneDataMail .= '<hr />';

          $rightStoneGrooves = Stones::find_by(["brandId" => $stone->brandId, "colorId" => $stone->colorId, "sizeId" => $stone->sizeId, "endstone" => "rightg"]);
          if ($rightStoneGrooves->stock >= $quotationExtra->totalRightEndStonesGrooves) {

            $oldStock = $rightStoneGrooves->stock;
            $rightStoneGrooves->stock = $rightStoneGrooves->stock - $quotationExtra->totalRightEndStonesGrooves;
            $rightStoneGrooves->save();

            $stoneDataMail .= '<p>Rightgrooves stock aantal: ' . $oldStock . '</p>';
            $stoneDataMail .= '<p>Rightgrooves stenen aantal: ' . $quotationExtra->totalRightEndStonesGrooves . '</p>';
            $stoneDataMail .= '<p>Rightgrooves new stock aantal: ' . $rightStoneGrooves->stock . '</p>';
          }
          else {
            $stoneDataMail .= '<p>Er zijn te weinig stenen. Aantal is niet verwijderd van voorraad.</p>';
            $stoneDataMail .= '<p>Rightgrooves stock aantal: ' . $rightStoneGrooves->stock . '</p>';
            $stoneDataMail .= '<p>Rightgrooves stenen aantal: ' . $quotationExtra->totalRightEndStonesGrooves . '</p>';
          }

          $stoneDataMail .= '<hr />';

        }
      }

      GsdMailer::build(MAIL_BART, "Productiestraat: bestelling geproduceerd - " . $quotation->getQuotationNumberFull(), $stoneDataMail)->send();
    }

    private function sendErrorMail($message) {
      GsdMailer::build(MAIL_BART, "Foutmelding productiestraat", $message)->send();
    }


  }