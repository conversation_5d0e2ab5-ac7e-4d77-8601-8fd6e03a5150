<?php

  namespace domain\multivers\service;

  use gsdfw\domain\multivers\service\CustomerInvoiceInfo;
  use gsdfw\domain\multivers\service\MultiversApi;
  use Invoices;

  /**
   * Class InvoiceSyncStatus
   * Set paid status from multivers to local system
   *
   * @package domain\multivers\service
   */
  class InvoiceSyncStatus {

    private $multivers_api;
    private $paidInvoiceNrs = [];
    private $open = 0;
    private $checked = 0;

    public function __construct(MultiversApi $multivers_api) {
      $this->multivers_api = $multivers_api;
    }

    /**
     * Get paid invoice count
     * @return int
     */
    public function getPaid(): int {
      return count($this->paidInvoiceNrs);
    }


    /**
     * Get array with invoicenumber that are paid
     * @return array
     */
    public function getPaidInvoiceNrs(): array {
      return $this->paidInvoiceNrs;
    }

    /**
     * @param array $paidInvoiceNrs
     */
    public function setPaidInvoiceNrs(array $paidInvoiceNrs): void {
      $this->paidInvoiceNrs = $paidInvoiceNrs;
    }

    /*
     * Add paidinvoicenr
     */
    public function addPaidInvoiceNr(string $paidInvoiceNr): void {
      $this->paidInvoiceNrs[] = $paidInvoiceNr;
    }


    /**
     * Get open invoice count
     * @return int
     */
    public function getOpen(): int {
      return $this->open;
    }

    /**
     * @param int $open
     */
    public function setOpen(int $open): void {
      $this->open = $open;
    }

    /**
     * Get checked invoices (nr of invoices that is not paid and checked with multivers)
     * @return int
     */
    public function getChecked(): int {
      return $this->checked;
    }

    /**
     * @param int $checked
     */
    public function setChecked(int $checked): void {
      $this->checked = $checked;
    }


    /**
     * Synchronize paid
     */
    public function sync(): bool {

      $customer_invoice_info = new CustomerInvoiceInfo($this->multivers_api);

      $open_invoices = Invoices::getOpenInvoices();
      foreach ($open_invoices as $invoice) {

        //LogToFile("robert",'Usage: ' . (memory_get_usage(true) / 1024 / 1024) . ' MB | Peak: ' . (memory_get_peak_usage(true) / 1024 / 1024) . ' MB');

//        echo $invoice->dateInvoice."\n";
//        continue;
        $ciim = $customer_invoice_info->retrieveById($invoice->invoiceNumber);
//        uitgezet: er zijn hier geen facturen ouder dan 1 jaar.
//        if (!$ciim && $invoice->getISent("Y") <= 2014) {
//          //try zero padded invoiceId for old invoices
//          $ciim = $customer_invoice_info->retrieveById(zerofill($invoice->invoiceNumber, 8));
//        }
        if (!$ciim) {
          //factuur niet gevonden in multivers, overslaan
          $this->checked++;
          continue;
        }
        if ($ciim->isPaid()) {

          $invoice->setPaid($ciim->getPaymentDate("Y-m-d"));

          $this->addPaidInvoiceNr($invoice->invoiceNumber);
        }
        else {
          $this->open++;
        }
        $this->checked++;
      }
      return true;
    }


  }