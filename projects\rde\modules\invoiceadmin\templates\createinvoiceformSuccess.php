<?php TemplateHelper::includePartial("_tabs.php", 'invoiceadmin') ?>

<h2>Factuur maken</h2>

<?php //writeErrors($form->getErrors(), true); ?>

<form method="post">
  <table class="default_table" style="width: 100%;">

    <tr class="dataTableRow trhover">
      <td class="head">
        <?php echo __('Factuur nummer') ?>
      </td>
      <td>
        <input type="text" disabled name="title" placeholder="Factuur nummer..." value="<?php echo $invoice_number ?>">
      </td>
    </tr>
    <tr class="dataTableRow trhover">
      <td class="head">
        <?php echo __('Aantal meters') ?>
      </td>
      <td>
        <input type="text" disabled name="title" placeholder="Aantal meters..." value="<?php echo  $meters_total ?>">
      </td>
    </tr>
    <tr class="dataTableRow trhover">
      <td class="head">
        <?php echo __('Vrachtkosten') ?>
      </td>
      <td>
        <input type="text" name="freight_cost" placeholder="Vrachtkosten..." value="<?php echo  $freight_costs ?>">
      </td>
    </tr>
    <tr class="dataTableRow trhover">
      <td class="head">
        <?php echo __('Factuur notities') ?>
      </td>
      <td>
        <textarea id="invoice_notes" name="invoice_notes" rows="5"> </textarea>
      </td>
    </tr>

  </table>

  <br/>
  <input type="submit" name="go" value="Factuur maken"/>

</form>

<table style="margin-top: 15px" class="default_table default_table_center quotation_table visible">
  <tr class="dataTableHeadingRow row_head">
    <th>ProjectNr.</th>
    <th>Project naam</th>
    <th>Prijs ex. BTW</th>
    <th>Aantal</th>
    <th>Totaal prijs</th>
  </tr>
  <?php foreach ($quotations as $quotation): ?>
  <tr class="trhover dataTableRow">
    <td><?php echo $quotation->quotationNumber ?></td>
    <td><?php echo $quotation->projectName ?></td>
    <td><?php echo StringHelper::asMoney($quotation->projectValue) ?></td>
    <td>1</td>
    <td><?php echo StringHelper::asMoney($quotation->projectValue) ?></td>
  </tr>
  <?php endforeach; ?>
  <?php foreach ($projects as $project): ?>
    <tr class="trhover dataTableRow">
      <td><?php echo $project->projectId ?></td>
      <td><?php echo $project->name ?></td>
      <td><?php echo StringHelper::asMoney($project->pieceprice) ?></td>
      <td><?php echo $project->size ?></td>
      <td><?php echo StringHelper::asMoney($project->euro) ?></td>
    </tr>
  <?php endforeach; ?>
  <?php if ($intro_discount !== 0): ?>
    <tr class="trhover dataTableRow">
      <td><?php ?></td>
      <td>Introductie korting</td>
      <td><?php echo StringHelper::asMoney($intro_discount) ?></td>
      <td>1</td>
      <td><?php echo StringHelper::asMoney($intro_discount) ?></td>
    </tr>
  <?php endif; ?>
  <tr class="trhover dataTableRow">
    <td colspan="3"></td>
    <td class="totals">Vrachtkosten: </td>
    <td class="totals"><?php echo $freight_costs ?></td>
  </tr>
  <tr class="trhover dataTableRow">
    <td colspan="3"></td>
    <td class="totals">Subtotaal: </td>
    <td class="totals"><?php echo $subtotal ?></td>
  </tr>
  <tr class="trhover dataTableRow">
    <td colspan="3"></td>
    <td class="totals">BTW: </td>
    <td class="totals"><?php echo $total_btw ?></td>
  </tr>
  <tr class="trhover dataTableRow">
    <td colspan="3"></td>
    <td class="totals">Totaal bedrag: </td>
    <td class="totals"><?php echo $total_project_value ?></td>
  </tr>
</table>

<style>

  .row_head {
    text-align: left;
    background-color: #F5F8FD;
    height: 30px;
  }

  .totals {
    font-size: 12px;
    font-weight: bold;
  }
</style>