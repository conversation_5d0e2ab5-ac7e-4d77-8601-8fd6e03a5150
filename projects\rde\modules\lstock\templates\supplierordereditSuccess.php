<?php TemplateHelper::includePartial('_tabs.php','lstock'); ?>
<script type="text/javascript">
  $(document).ready(function() {
    $(".size").change(function (event) {
      var val = 0;
      if($(this).val()!="") {
        var val = parseInt($(this).val());
        if(isNaN(val)) {
          val = 0;
        }
      }
      $(this).val(val);

      if($(this).attr("data-palletsize")) {
        var palletizer = $(this).parent().parent().find(".palletizer");
        if(isInt(val/$(this).attr("data-palletsize"))) {
          palletizer.val(val/$(this).attr("data-palletsize"));
        }
        else {
          palletizer.val("");
        }
      }

    });

    $(".palletizer").change(function() {
      if($(this).val()==""){
        $(this).parent().parent().find(".size").val("");
      }
      else {
        $(this).parent().parent().find(".size").val(parseInt($(this).val()) * parseInt($(this).attr("data-size")));
      }
    });

  });
</script>
<?php writeErrors($errors, true); ?>
<h3>Bewerk bestellijst</h3>

<form method="post">

  <?php	if(count($stoneorder->items)==0): ?>
    <br/>Er zijn geen stenen gevonden
  <?php else: ?>
    <table class="default_table" style="width: auto;">
      <tr class="dataTableHeadingRow">
        <td>Product</td>
        <td>Kleur</td>
        <td>
          Naam <?php echo showInfoButton("Als naam leeg is worden de bestelnummers als referentie gebruikt.") ?>
        </td>
        <td>Bestellingen</td>
        <td>Per pallet</td>
        <td>
          Aantal <?php echo showInfoButton("Het aantal kan 0 zijn. Dit heeft te maken met de voorraad marge regeling. Bij het verzenden van de email worden deze regels overgeslagen.") ?>
        </td>
        <?php if($stoneorder->status=='new'): ?>
          <td style="width: 50px;">Verwijder</td>
        <?php else: ?>
          <td style="width: 50px;">Geleverd</td>
          <td>Verwachte leverdatum</td>
        <?php endif; ?>
      </tr>
      <?php
        $stoneprev = false;
        foreach($stoneorder->items as $item):
        $stone = $item->stone;
        $color = StoneColors::find_by(["colorId"=>$stone->colorId]);
        ?>
        <tr class="dataTableRow trhover <?php if($stoneprev && !Stones::areSiblings($stone, $stoneprev)): ?>topborder<?php endif; ?>">
          <td><?php echo $stone->name;?></td>
          <td><?php if($color) echo $color->getFullname();?></td>
          <td><input type="text" name="name[<?php echo $item->id ?>]" value="<?php echo $item->name ?>" <?php if($stoneorder->status!='new'): ?>readonly<?php endif; ?>/></td>
          <td><?php echo $item->quotationNames ?></td>
          <td>
            <?php if($stone->amountPerPallet>0): ?>
              <select class="palletizer" data-size="<?php echo $stone->amountPerPallet ?>">
                <option value="">#pallets</option>
                <?php for($tel=1;$tel<100;$tel++): ?>
                  <option value="<?php echo $tel ?>" <?php if($item->size/$stone->amountPerPallet==$tel) echo 'selected'; ?>><?php echo $tel ?></option>
                <?php endfor; ?>
              </select>
              x <?php echo $stone->amountPerPallet ?>
            <?php endif; ?>
          </td>
          <td><input style="width: 70px;text-align:right;" type="text" name="size[<?php echo $item->id ?>]" class="size" value="<?php echo $item->size ?>" data-palletsize="<?php echo $stone->amountPerPallet ?>" <?php if($stoneorder->status!='new'): ?>readonly<?php endif; ?>/></td>
          <?php if($stoneorder->status=='new'): ?>
            <td>
              <?php echo BtnHelper::getRemove('?action=supplierorderdeletepr&delid='.$item->id) ?>
            </td>
          <?php else: ?>
            <td style="text-align: right"><?php echo $item->receivedsize ?></td>
            <td><?php echo $item->getSupplierreadydate() ?></td>
          <?php endif; ?>
        </tr>
      <?php
          $stoneprev = $stone;
        endforeach;
      ?>
    </table>
    <br/>
    <?php if($stoneorder->status=='new'): ?>
      <input type="submit" name="go" value="Opslaan" class="gsd-btn gsd-btn-primary"/>
      <input type="submit" name="go_list" value="Opslaan en naar lijst" class="gsd-btn gsd-btn-secondary"/>
      <input type="submit" name="send" value="Opslaan en verzenden"/>
      <input type="submit" name="not_send" value="Opslaan en niet verzenden"/>
    <?php else: ?>
      <a href="<?php echo PageMap::getUrl($pageId) ?>" class="gsd-btn gsd-btn-primary">Terug naar overzicht</a>
    <?php endif; ?>
  <?php endif; ?>

</form>
