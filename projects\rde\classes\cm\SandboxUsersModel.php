<?php

  AppModel::loadBaseClass('BaseSandboxUsers');

  class SandboxUsersModel extends BaseSandboxUsers {

    /**
     * @var CrmCompanies
     */
    public $company;

    public static function getById($userId) {
      return self::find_by(["userId" => $userId]);
    }

    public static function getSalt() {
      return substr(md5(uniqid(rand(), true)), 5, 20);
    }

    /**
     * @param $password
     * @param $salt
     * @return string
     */
    public static function encryptPassword($password, $salt) {
      return sha1($salt . $password . Config::get('RDE_PEPPER'));
    }

    public function getLoginLink() {
      $userhash = urlencode(EncryptionHelper::encrypt("RDEUSER#824yy" . date("Ydm")));
      $domain = SiteHost::getPrimary(1)->getDomainSmart(true);
      return $domain . "/offerte?email=" . $this->email . "&userhash=" . $userhash;
    }

    public function getNaam() {
      return trim($this->firstName . ' ' . $this->lastName);
    }

    function getAanhef($persoonlijk = false) {
      if ($persoonlijk) {
        return $this->firstName;
      }
      $string = "";
      if ($this->gender == "male") $string .= __('heer');
      if ($this->gender == "female") $string .= __('mevrouw');
      $string .= " ";
      if ($this->lastName != "") $string .= $this->lastName;

      return $string;
    }

    public function getAddress() {
      $str = "";
      if ($this->street != "") {
        $str .= $this->street;
      }
      if ($this->nr != "") {
        $str .= " " . $this->nr;
      }
      if ($this->extension != "") {
        $str .= " " . $this->extension;
      }
      return trim($str);
    }

    /**
     * @param string $nl : break
     * @param bool $showcountry : toon land
     * @return mixed
     */
    public function getAddressFormatted($nl = '<br/>', $showcountry = true) {
      //for display only, so escaping single quotes to html codes
      $string = "";
      if ($this->street != null) {
        if ($this->street . $this->nr . $this->extension != "") {
          $string .= trim($this->street . " " . $this->nr . " " . $this->extension);
        }
        if ($this->zipcode . $this->domestic != "") {
          $string .= $nl;
          $string .= $this->zipcode . " " . $this->domestic;
        }
        if ($showcountry && $this->country != "") {
          $string .= $nl;
          $string .= Country::getCountryByCode(strtolower($this->country))->name_nl;
        }
      }
      return displayAsHtml($string);
    }

    /**
     * Start sessie
     * @param SandboxUsers $user
     */
    public static function startSession($user) {
      if ($user->companyId != "" && $user->companyId != "0") {
        $user->company = CrmCompanies::find_by(["companyId" => $user->companyId]);
      }

      $user->lastLogin = date("Y-m-d H:i:s");
      if ($user->lastName == "") $user->lastName = "-?-";
      if (isset($_SERVER['SERVER_ADDR'])) $user->ip = $_SERVER['SERVER_ADDR'];
      $user->save();

      $_SESSION['userObject'] = $user;
      $_SESSION['loggedIn'] = true;
      $_SESSION['userId'] = $user->userId;

      unset($_SESSION['priceincreases']);

    }

    /**
     * @param $userId
     * @return bool|SandboxUsers
     */
    public static function getUserAndCompany($userId) {
      $user = SandboxUsers::find_by(["userId" => $userId]);
      if (!$user) return false;
      if ($user->companyId != "" && $user->companyId != "0") {
        $user->company = CrmCompanies::find_by(["companyId" => $user->companyId]);
      }
      return $user;
    }

    /**
     * Get users of a company by companyId
     * @param int $companyId
     * @return SandboxUsers[]
     */
    public static function getUsersOfCompany($companyId) {
      return SandboxUsers::find_all_by(["companyId" => $companyId]);
    }

    /**
     * Stop sessie
     */
    public static function stopSession() {
      unset($_SESSION['userObject']);
      unset($_SESSION['loggedIn']);
      unset($_SESSION['userId']);
      unset($_SESSION['basket']); //opmruimen winkelmandje
      unset($_SESSION['wizard']); //opruimen offerte wizard
    }

    public static function isEmailUnique($email, $notcheckthisuserid = '') {
      $props = ['email' => $email];
//  $props['void'] = "0";
      $filt = "";
      if ($notcheckthisuserid != '') {
        $filt = " AND userId !='" . escapeForDB($notcheckthisuserid) . "'";
      }

      $result = SandboxUsers::find_all_by($props, $filt);
      if (count($result) == 0) {
        return true;
      }
      return false;
    }

    public static function isAdmin() {
      return isset($_SESSION["userAdmin"]);
    }

    public function save(&$errors = []) {
      if ($this->from_db == false) {
        $this->created = date('Y-m-d H:i:s');
      }
      if ($this->created == "0000-00-00 00:00:00") {
        $this->created = null;
      }
      if ($this->lastLogin == "0000-00-00 00:00:00") {
        $this->lastLogin = null;
      }
      $this->lastUpdate = date('Y-m-d H:i:s');
      if ($this->gender == "") $this->gender = null;
      return parent::save($errors);
    }

    /**
     *
     * M.b.t. payterm_increase: iemand met een langere betalingstermijn, betaald een hogere prijs.
     * @param $userId : leeg dan particulier prijs
     * @param $date : leeg dan vandaag
     * @return array|bool
     */
    public static function getPriceIncreases($userId = '', $date = false) {
      //    $userId = 60;
      if ($date === false) {
        $date = date("Y-m-d");
      }

      $row = false;
      if ($userId != '') {
        $sQuery1 = "SELECT CC.codeId, SU.userId, COMP.companyId, CC.codeId,  ";
        $sQuery1 .= "CI.*, ";
        $sQuery1 .= "PI.increase as payterm_increase ";
        $sQuery1 .= "FROM " . SandboxUsers::getTablename() . " SU ";
        $sQuery1 .= "JOIN " . CrmCompanies::getTablename() . " COMP ON COMP.companyId = SU.companyId ";
        $sQuery1 .= "JOIN " . CustomerProductincreases::getTablename() . " CI ON CI.groupId = COMP.customerGroupId ";
        $sQuery1 .= "JOIN " . CustomerCodes::getTablename() . " CC ON CC.payterm = COMP.paymentTerm AND CC.groupId = COMP.customerGroupId ";
        $sQuery1 .= "JOIN " . CustomerPaytermincreases::getTablename() . " PI ON PI.codeId = CC.codeId  ";
        $sQuery1 .= "WHERE (CI.validFrom <= '" . $date . "' AND CI.validTo >= '" . $date . "') ";
        $sQuery1 .= "AND (PI.validFrom <= '" . $date . "' AND PI.validTo >= '" . $date . "') ";
        $sQuery1 .= "AND SU.userId = '" . $userId . "' ";
        $oResult = DBConn::db_link()->query($sQuery1);
        $row = $oResult->fetch_assoc();
      }
      if (!$row) { //nog een particuliere prijzen (is nog geen Company aangemaakt (oftewel nog niet gekoppeld), het kan wel een bedrijf zijn natuurlijk)
        $codeId = 31; // codeId is Customer Code 7.1 (Private - 14 days payterm)
        $sQuery2 = "SELECT CC.codeId, ";
        $sQuery2 .= "CI.*, ";
        $sQuery2 .= "PI.increase as payterm_increase ";
        $sQuery2 .= "FROM " . CustomerCodes::getTablename() . " CC ";
        $sQuery2 .= "JOIN " . CustomerProductincreases::getTablename() . " CI ON CC.groupId = CI.groupId ";
        $sQuery2 .= "JOIN " . CustomerPaytermincreases::getTablename() . " PI ON CC.codeId = PI.codeId ";
        $sQuery2 .= "WHERE (CI.validFrom <= '" . $date . "' AND CI.validTo >= '" . $date . "') ";
        $sQuery2 .= "AND (PI.validFrom <= '" . $date . "' AND PI.validTo >= '" . $date . "') ";
        $sQuery2 .= "AND CC.codeId = '" . $codeId . "' ";
        $oResult = DBConn::db_link()->query($sQuery2);
        $row = $oResult->fetch_assoc();
      }

      if ($row) {
        $row["stoneIncrease"] = (1 + ($row["stoneIncrease"] / 100)) * (1 + ($row["payterm_increase"] / 100));
        $row["glueIncrease"] = (1 + ($row["glueIncrease"] / 100)) * (1 + ($row["payterm_increase"] / 100));
        $row["joint"] = (1 + ($row["joint"] / 100)) * (1 + ($row["payterm_increase"] / 100));
        $row["coldGlaze"] = (1 + ($row["coldGlaze"] / 100)) * (1 + ($row["payterm_increase"] / 100));
        $row["spacers"] = (1 + ($row["spacers"] / 100)) * (1 + ($row["payterm_increase"] / 100));
        $row["stoneIncreaseGroupA"] = (1 + ($row["stoneIncreaseGroupA"] / 100)) * (1 + ($row["payterm_increase"] / 100));
        $row["stoneIncreaseGroupB"] = (1 + ($row["stoneIncreaseGroupB"] / 100)) * (1 + ($row["payterm_increase"] / 100));
        $row["stoneIncreaseGroupC"] = (1 + ($row["stoneIncreaseGroupC"] / 100)) * (1 + ($row["payterm_increase"] / 100));
        $row["stoneIncreaseGroupD"] = (1 + ($row["stoneIncreaseGroupD"] / 100)) * (1 + ($row["payterm_increase"] / 100));
        $row["glazeside"] = (1 + ($row["glazeside"] / 100)); //geen betaal verhoging, dat is al verrekend in voorgaande
        $row["naturalstoneIncrease"] = (1 + ($row["naturalstoneIncrease"] / 100)) * (1 + ($row["payterm_increase"] / 100));
        $row["naturalstoneChinaIncrease"] = (1 + ($row["naturalstoneChinaIncrease"] / 100)) * (1 + ($row["payterm_increase"] / 100));
        $row["concreteIncrease"] = (1 + ($row["concreteIncrease"] / 100)) * (1 + ($row["payterm_increase"] / 100));
        $row["isosillIncrease"] = (1 + ($row["isosillIncrease"] / 100)) * (1 + ($row["payterm_increase"] / 100));
        return $row;
      }

      return false;
    }

    public static function getByPhone($phone) {
      $phone = escapeForDB($phone);

      $query = " WHERE ( ";
      $query .= " phone = '" . $phone . "' ";
      $query .= " OR mobile = '" . $phone . "' ";
      $query .= " ) ";
      $users = SandboxUsers::find_all($query);

      if (count($users) == 0) {
        $phone_s = substr($phone, 2);
        $query = " WHERE ( ";
        $query .= " phone LIKE '%" . $phone_s . "' ";
        $query .= " OR mobile LIKE '%" . $phone_s . "' ";
        $query .= " ) ";
        $users = SandboxUsers::find_all($query);
      }

      $companyids = [];
      foreach ($users as $user) {
        if ($user->companyId != "") {
          $companyids[$user->companyId] = $user->companyId;
        }
      }
      $companies = AppModel::mapObjectIds(CrmCompanies::find_all_by(["companyId" => $companyids]), "companyId");
      foreach ($users as $user) {
        if ($user->companyId != "") {
          $user->company = $companies[$user->companyId];
        }
      }

      return $users;

    }

    public function getCreated($format = 'd-m-Y H:i') {
      if ($this->created == "0000-00-00 00:00:00" || $this->created == "") {
        return "";
      }
      return date($format, strtotime($this->created));
    }

    /**
     * Copy sandbox data to person, or create if no person given
     * @param bool $person
     * @return bool|CrmPersons
     */
    public function copyToPerson($person = false) {
      if (!$person) {
        $person = new CrmPersons();
        $person->companyId = $this->companyId;
      }
      if ($person) {
        $person->gender = $this->gender;
        $person->firstName = $this->firstName;
        $person->lastName = $this->lastName;
        $person->email = $this->email;
        $person->phone = $this->phone;
        $person->mobile = $this->mobile;
        $person->fax = $this->fax;
        $person->country = $this->country;
        $person->save();
      }
      return $person;
    }

    /**
     * Copy person data to sandbox
     * @param CrmPersons $person
     * @param bool $ignoremail
     */
    public function copyFromPerson($person, $ignoremail = false) {
      $this->gender = $person->gender;
      $this->firstName = $person->firstName;
      $this->lastName = $person->lastName;
      $this->phone = $person->phone;
      $this->mobile = $person->mobile;
      $this->fax = $person->fax;
      $this->country = $person->country;
      if (!$ignoremail && $person->email != "") $this->email = $person->email;
    }

    /**
     * Copy company data to sandbox
     * @param CrmCompanies $company
     */
    public function copyFromCompany($company) {
      $this->companyName = $company->name;
      $this->tradeRegNo = $company->tradeRegNo;
      $this->payterm = $company->paymentTerm;
      $this->phone = $company->phone;
      //emailadres passen we niet aan in de sandbox
//    $this->email = $company->email ;
    }

    /**
     * @param CrmAddresses $address
     */
    public function copyFromAddress($address) {
      $this->street = $address->street;
      $this->nr = $address->nr;
      $this->extension = $address->extension;
      $this->zipcode = $address->zipcode;
      $this->domestic = $address->domestic;
      $this->country = $address->country;
    }

    /**
     * @param CrmInvoiceparties $invoiceparty
     */
    public function copyFromInvoiceparty($invoiceparty) {
      $this->invoice_email = $invoiceparty->email;
      $this->invoice_email_confirmed = $invoiceparty->emailConfirmed;
    }

    /**
     * Heeft deze user een bedrijf gekoppeld.
     * Let op: een particulier heeft ook een bedrijf als deze is gefactureerd.
     * Of een gebruiker een partifculier is kun je zien aan de private property
     * Een bedrijf hoeft geen company te hebben. Pas na het koppelen door bart krijg deze een company.
     * Voordat het bedrijf gekoppeld is, ziet deze particuliere prijzen.
     * @return bool
     */
    public function hasCompany(): bool {
      return !empty($this->companyId);
    }

    /**
     * Is deze gebruiker een particulier.
     * @return bool
     */
    public function isPrivate() {
      return $this->private == "true";
    }

    public function getLastUpdate($format = 'd-m-Y H:i') {
      if ($this->lastUpdate == "0000-00-00 00:00:00" || $this->lastUpdate == "") {
        return "";
      }
      return date($format, strtotime($this->lastUpdate));
    }

    public function getLastLogin($format = 'd-m-Y H:i') {
      if ($this->lastLogin == "0000-00-00 00:00:00" || $this->lastLogin == "") {
        return "";
      }
      return date($format, strtotime($this->lastLogin));
    }


  }