<?php

  /**
   * Draws an Arrow from x1,y1 to x2,y2 using the gd-library.
   * The Arrowheads always point to x2,y2
   *
   * @param image (The referenced canvas)
   * @param color (GD color of the arrow)
   * @param x1, y1, x2, y2 (Starting and endpoint of the arrow)
   * @param angle (Arm angle of the arrowhead)
   * @param radius (Length of the arrowhead)
   *
   * Usage:
   *
   * $myArrow = new GDArrow()
   *
   * $myArrow -> image = foo
   * $myArrow -> color = bar
   * $myArrow -> x1 = foo
   * $myArrow -> y1 = bar
   * $myArrow -> x2 = foo
   * $myArrow -> y2 = bar
   * $myArrow -> angle = foo
   * $myArrow -> radius = bar
   *
   * $myArrow -> drawGDArrow()
   *
   *
   * Some mathematics:
   *
   * The gradient of a segment A(x1, y1) B(x2, y2) can be calculated:
   * m = (y2 - y1) / (x2 - x1) for x1 != x2
   *
   * Taking the endpoint of the arrow as an endpoint of a segment, this
   * point can be seen as the center of a unit circle. Then the arm angles
   * of the arrowhead are:
   * gradient + 180 + arm angle for one arrowhead segment and
   * gradient + 180 - arm angle for the other arrowhead segment
   * Abscissa and ordinate of the starting point of these segments can
   * be calculated trigonometrically.
   *
   * The gradient can be:
   *
   * I) gradient = 0
   * Ia) x2 > x1 and Ib) x2 < x1
   *
   * II) gradient = oo
   * IIa) y2 > y1 and IIb) y2 < y1
   *
   * III) gradient > 0
   *
   * IV) gradient < 0
   *
   * @since         2006 Dec 02 (Reworked 2009 Aug 27)
   *
   * @copyright (C) 2006 Code-Kobold (Ron Metten) (www.code-kobold.de)
   *
   * This program is free software; you can redistribute it and/or
   * modify it under the terms of the GNU General Public License as
   * published by the Free Software Foundation; either version 3 of
   * the License, or (at your option) any later version.
   *
   * This program is distributed in the hope that it will be useful,
   * but WITHOUT ANY WARRANTY; without even the implied warranty of
   * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
   * GNU General Public License for more details.
   *
   * You should have received a copy of the GNU General Public
   * License along with this program; if not, see
   * <http://www.gnu.org/licenses/>.
   *
   * <AUTHOR> (Ron Metten) (www.code-kobold.de)
   */
  class GDArrow {

    /**
     * The referenced canvas
     */
    public $image;

    /**
     * Arrow color
     */
    public $color;

    /**
     * X-Coordinate of arrow's starting point
     */
    public $x1;

    /**
     * Y-Coordinate of arrow's starting point
     */
    public $y1;

    /**
     * X-Coordinate of arrow's endpoint
     */
    public $x2;

    /**
     * Y-Coordinate of arrow's starting point
     */
    public $y2;

    /**
     * Arm angle of the arrowhead
     */
    public $angle;

    /**
     * Length of the arrowhead
     */
    public $radius;


    /**
     * The constructor
     */
    function __construct() {
    }


    /**
     * Draws the arrow according the given parameters
     */
    function drawGDArrow() {

      $l_m = null;
      $l_x1 = null;
      $l_y1 = null;
      $l_x2 = null;
      $l_y2 = null;
      $l_angle1 = null;
      $l_angle2 = null;
      $l_cos1 = null;
      $l_sin1 = null;

      // Draws the arrow's line
      Imageline($this->image, $this->x1, $this->y1, $this->x2, $this->y2, $this->color);

      // Gradient infinite?
      if ($this->x2 == $this->x1) {

        $l_m = false;

        if ($this->y1 < $this->y2) {

          $l_x1 = $this->x2 - $this->radius * sin(deg2rad($this->angle));
          $l_y1 = $this->y2 - $this->radius * cos(deg2rad($this->angle));
          $l_x2 = $this->x2 + $this->radius * sin(deg2rad($this->angle));
          $l_y2 = $this->y2 - $this->radius * cos(deg2rad($this->angle));

        }
        else {

          $l_x1 = $this->x2 - $this->radius * sin(deg2rad($this->angle));
          $l_y1 = $this->y2 + $this->radius * cos(deg2rad($this->angle));
          $l_x2 = $this->x2 + $this->radius * sin(deg2rad($this->angle));
          $l_y2 = $this->y2 + $this->radius * cos(deg2rad($this->angle));

        } // endelse

      } // endif $this -> x2 == $this -> x1

      // Gradient = 0
      elseif ($this->y2 == $this->y1) {

        $l_m = 0;

        if ($this->x1 < $this->x2) {

          $l_x1 = $this->x2 - $this->radius * cos(deg2rad($this->angle));
          $l_y1 = $this->y2 - $this->radius * sin(deg2rad($this->angle));
          $l_x2 = $this->x2 - $this->radius * cos(deg2rad($this->angle));
          $l_y2 = $this->y2 + $this->radius * sin(deg2rad($this->angle));

        }
        else {

          $l_x1 = $this->x2 + $this->radius * cos(deg2rad($this->angle));
          $l_y1 = $this->y2 + $this->radius * sin(deg2rad($this->angle));
          $l_x2 = $this->x2 + $this->radius * cos(deg2rad($this->angle));
          $l_y2 = $this->y2 - $this->radius * sin(deg2rad($this->angle));

        }

      } // endif $this -> y2 == $this -> y1

      // Gradient positive?
      elseif ($this->x2 > $this->x1) {

        // Calculate gradient
        $l_m = (($this->y2 - $this->y1) / ($this->x2 - $this->x1));

        // Convert gradient (= Arc tangent(m)) from radian to degree
        $l_alpha = rad2deg(atan($l_m));

        // Right arm angle = gradient + 180 + arm angle
        $l_angle1 = $l_alpha + $this->angle + 180;
        // Left arm angle = gradient + 180 - arm angle
        $l_angle2 = $l_alpha - $this->angle + 180;

        // Right arm angle of arrowhead
        // Abscissa = cos(gradient + 180 + arm angle) * radius
        $l_cos1 = $this->radius * cos(deg2rad($l_angle1));
        $l_x1 = $this->x2 + $l_cos1;

        // Ordinate = sin(gradient + 180 + arm angle) * radius
        $l_sin1 = $this->radius * sin(deg2rad($l_angle1));
        $l_y1 = $this->y2 + $l_sin1;

        // Left arm angle of arrowhead
        $RCos2 = $this->radius * cos(deg2rad($l_angle2));
        $RSin2 = $this->radius * sin(deg2rad($l_angle2));

        $l_x2 = $this->x2 + $RCos2;
        $l_y2 = $this->y2 + $RSin2;

      }  // endif $this -> x2 > $this -> x1

      // Gradient negative?
      elseif ($this->x2 < $this->x1) {

        $this->angle = 90 - $this->angle;

        // Calculate gradient
        $l_m = (($this->y2 - $this->y1) / ($this->x2 - $this->x1));

        // Convert gradient (= Arc tangent(m)) from radian to degree
        $l_alpha = rad2deg(atan($l_m));

        // Right arm angle = gradient + 180 + arm angle
        $l_angle1 = $l_alpha + $this->angle + 180;
        // Left arm angle = gradient + 180 - arm angle
        $l_angle2 = $l_alpha - $this->angle + 180;

        // Right arm angle of arrowhead
        // Abscissa = cos(gradient + 180 + arm angle) * radius
        $l_cos1 = $this->radius * cos(deg2rad($l_angle1));

        // Ordinate = sin(gradient + 180 + arm angle) * radius
        $l_sin1 = $this->radius * sin(deg2rad($l_angle1));

        // Left arm angle of arrowhead
        $RCos2 = $this->radius * cos(deg2rad($l_angle2));
        $RSin2 = $this->radius * sin(deg2rad($l_angle2));

        $l_x1 = $this->x2 - $l_sin1;
        $l_y1 = $this->y2 + $l_cos1;

        $l_x2 = $this->x2 + $RSin2;
        $l_y2 = $this->y2 - $RCos2;

      }  // endif $this -> x2 < $this -> x1

      Imageline($this->image, (int)$l_x1, (int)$l_y1, $this->x2, $this->y2, $this->color);
      Imageline($this->image, (int)$l_x2, (int)$l_y2, $this->x2, $this->y2, $this->color);

    } // drawGDArrow()

  } // class GDArrow

  /*
    // The following code drwas a sample image:

    $dimension = 550;

    $image = imagecreate($dimension, $dimension);
    $green = Imagecolorallocate($image, 0, 255, 0);
    $white = Imagecolorallocate($image, 255, 255, 255);
    $black = Imagecolorallocate($image, 0, 0, 0);
    $red = Imagecolorallocate($image, 255, 0, 0);

    $myArrow = new GDArrow();
    $myArrow->image = $image;
    $myArrow->color = $black;

    $beta = 25;
    $r = 12;
    $radius = ($dimension / 3);

    $d = ($dimension / 2);
    $x1 = $y1 = $d;


    for ($angle = 0; $angle < 360; $angle += 10) {

      $x2 = $d + $radius * cos(deg2rad($angle));
      $y2 = $d + $radius * sin(deg2rad($angle));

      $myArrow->x1 = $x1;
      $myArrow->y1 = $y1;
      $myArrow->x2 = $x2;
      $myArrow->y2 = $y2;
      $myArrow->angle = $beta;
      $myArrow->radius = $r;

      $myArrow->drawGDArrow();

    } // next

    $beta = 45;
    $radius = ($dimension * 2 / 4.1);

    $myArrow->color = $red;

    for ($angle = 5; $angle < 360; $angle += 10) {

      $x2 = $d + $radius * cos(deg2rad($angle));
      $y2 = $d + $radius * sin(deg2rad($angle));

      $myArrow->x1 = $x1;
      $myArrow->y1 = $y1;
      $myArrow->x2 = $x2;
      $myArrow->y2 = $y2;
      $myArrow->angle = $beta;
      $myArrow->radius = $r;

      $myArrow->drawGDArrow();

    } // next

    // Save image
    imagepng($image, "gdarrow.png");

  */

