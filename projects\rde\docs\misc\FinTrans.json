{"messages": [], "canChange": true, "cannotChangeReason": "", "currencyId": "", "description": "Bankmutaties d.d. 25-10-2019", "document": "", "exchangeRate": 1.0, "finTransEntries": [{"$type": "UNIT4.Multivers.API.BL.Financial.Edit.BasicEntryProxy, UNIT4.Multivers.API.Web.WebApi.Model", "accountId": "1250", "messages": [], "canChange": true, "cannotChangeReason": "", "costCentreId": "", "costCentreIdRequired": false, "costUnitId": "", "costUnitIdRequired": false, "creditAmount": 498.45, "debitAmount": 0.0, "description": "Bankmutaties d.d. 25-10-2019", "document": "", "entryType": 6, "invoiceType": 3, "isSubAdminSpecificationRequired": false, "journalSection": 2, "lineNumbers": [5844], "nondeductibleVatPercentage": 0.0, "quantity": 0.0, "subAdminSpecifications": [], "subTransaction": "", "teleBankGuid": "", "transactionDate": "25-10-2019", "vatAmount": 0.0, "vatChanged": false, "vatCodeId": null, "vatIncluded": true, "vatOnInvoice": false, "vatScenarioId": null, "vatType": 5}, {"$type": "UNIT4.Multivers.API.BL.Financial.Edit.BasicEntryProxy, UNIT4.Multivers.API.Web.WebApi.Model", "accountId": "1690", "messages": [], "canChange": true, "cannotChangeReason": "", "costCentreId": "", "costCentreIdRequired": false, "costUnitId": "", "costUnitIdRequired": false, "creditAmount": 0.0, "debitAmount": 2887.0, "description": "VPB 2019", "document": "", "entryType": 0, "invoiceType": 1, "isSubAdminSpecificationRequired": false, "journalSection": 3, "lineNumbers": [5845], "nondeductibleVatPercentage": 0.0, "quantity": 0.0, "subAdminSpecifications": [], "subTransaction": "", "teleBankGuid": "19BE11AE4F5F48A09979FB435C3D0D9F", "transactionDate": "25-10-2019", "vatAmount": 0.0, "vatChanged": false, "vatCodeId": null, "vatIncluded": true, "vatOnInvoice": true, "vatScenarioId": 1, "vatType": 5}, {"$type": "UNIT4.Multivers.API.BL.Financial.Edit.SupplierEntryProxy, UNIT4.Multivers.API.Web.WebApi.Model", "accountId": "1600", "messages": [], "canChange": true, "cannotChangeReason": "", "creditAmount": 0.0, "creditAmountCur": 0.0, "currencyId": "", "debitAmount": 151.01, "debitAmountCur": 151.01, "description": "Donders Gas Special.", "entryType": 2, "journalSection": 0, "lineNumbers": [], "subTransaction": "2", "supplierEntryPayments": [{"acceptCreditSqueezeAmount": false, "acceptPaymentDifference": false, "acceptRebateAmount": false, "messages": [], "amountPaid": 151.01, "amountPaidCur": 151.01, "canChange": true, "cannotChangeReason": "", "creditSqueezeApplicable": false, "creditSqueezeRemainingCur": 0.0, "description": "", "exchangeRateDifference": 0.0, "invoiceAmountCur": 151.01, "invoiceBalance": 0.0, "invoiceBalanceCur": 0.0, "invoiceCurrencyId": "", "invoiceDate": "30-9-2019", "invoiceExchangeRate": 1.0, "invoiceExpirationDate": "30-9-2019", "invoiceId": "********", "isAdvance": false, "paymentDifference": 0.0, "paymentDifferenceAcceptable": false, "paymentDifferenceCur": 0.0, "paymentReference": "********", "rebateApplicable": false, "rebateExpirationDate": "30-9-2019", "rebateRemainingCur": 0.0, "transactionDate": "25-10-2019"}], "supplierId": "91", "supplierName": "Donders Gas Special.", "teleBankGuid": "5AA68321EA254A8C92975DD1141349F6", "transactionDate": "25-10-2019"}, {"$type": "UNIT4.Multivers.API.BL.Financial.Edit.CustomerEntryProxy, UNIT4.Multivers.API.Web.WebApi.Model", "accountId": "", "messages": [], "canChange": true, "cannotChangeReason": "", "creditAmount": 341.3, "creditAmountCur": 341.3, "currencyId": "", "customerEntryPayments": [{"acceptCreditSqueezeAmount": false, "acceptPaymentDifference": false, "acceptRebateAmount": false, "messages": [], "amountPaid": 341.3, "amountPaidCur": 341.3, "canChange": true, "cannotChangeReason": "", "creditSqueezeApplicable": false, "creditSqueezeRemainingCur": 0.0, "description": "", "exchangeRateDifference": 0.0, "invoiceAmountCur": 341.3, "invoiceBalance": 0.0, "invoiceBalanceCur": 0.0, "invoiceCurrencyId": "", "invoiceDate": "8-10-2019", "invoiceExchangeRate": 1.0, "invoiceExpirationDate": "23-10-2019", "invoiceId": "30689", "isAdvance": false, "paymentDifference": 0.0, "paymentDifferenceAcceptable": false, "paymentDifferenceCur": 0.0, "paymentReference": "****************", "rebateApplicable": false, "rebateExpirationDate": "23-10-2019", "rebateRemainingCur": 0.0, "transactionDate": "25-10-2019"}], "customerId": "114292", "customerName": "<PERSON><PERSON><PERSON>", "debitAmount": 0.0, "debitAmountCur": 0.0, "description": "<PERSON><PERSON><PERSON>", "entryType": 1, "journalSection": 0, "lineNumbers": [], "subTransaction": "2", "teleBankGuid": "423E7FB1BCE84F6294C9353323B30717", "transactionDate": "25-10-2019"}, {"$type": "UNIT4.Multivers.API.BL.Financial.Edit.CustomerEntryProxy, UNIT4.Multivers.API.Web.WebApi.Model", "accountId": "", "messages": [], "canChange": true, "cannotChangeReason": "", "creditAmount": 93.56, "creditAmountCur": 93.56, "currencyId": "", "customerEntryPayments": [{"acceptCreditSqueezeAmount": false, "acceptPaymentDifference": false, "acceptRebateAmount": false, "messages": [], "amountPaid": 93.56, "amountPaidCur": 93.56, "canChange": true, "cannotChangeReason": "", "creditSqueezeApplicable": false, "creditSqueezeRemainingCur": 0.0, "description": "", "exchangeRateDifference": 0.0, "invoiceAmountCur": 93.56, "invoiceBalance": 0.0, "invoiceBalanceCur": 0.0, "invoiceCurrencyId": "", "invoiceDate": "25-9-2019", "invoiceExchangeRate": 1.0, "invoiceExpirationDate": "10-10-2019", "invoiceId": "30576", "isAdvance": false, "paymentDifference": 0.0, "paymentDifferenceAcceptable": false, "paymentDifferenceCur": 0.0, "paymentReference": "****************", "rebateApplicable": false, "rebateExpirationDate": "10-10-2019", "rebateRemainingCur": 0.0, "transactionDate": "25-10-2019"}], "customerId": "20392", "customerName": "Cesar <PERSON> B.V.", "debitAmount": 0.0, "debitAmountCur": 0.0, "description": "Cesar <PERSON> B.V.", "entryType": 1, "journalSection": 0, "lineNumbers": [], "subTransaction": "2", "teleBankGuid": "45279AE60CC4412C8A8FE5C7611CFA8C", "transactionDate": "25-10-2019"}, {"$type": "UNIT4.Multivers.API.BL.Financial.Edit.CustomerEntryProxy, UNIT4.Multivers.API.Web.WebApi.Model", "accountId": "", "messages": [], "canChange": true, "cannotChangeReason": "", "creditAmount": 3677.04, "creditAmountCur": 3677.04, "currencyId": "", "customerEntryPayments": [{"acceptCreditSqueezeAmount": false, "acceptPaymentDifference": false, "acceptRebateAmount": false, "messages": [], "amountPaid": 3677.04, "amountPaidCur": 3677.04, "canChange": true, "cannotChangeReason": "", "creditSqueezeApplicable": false, "creditSqueezeRemainingCur": 0.0, "description": "", "exchangeRateDifference": 0.0, "invoiceAmountCur": 3677.04, "invoiceBalance": 0.0, "invoiceBalanceCur": 0.0, "invoiceCurrencyId": "", "invoiceDate": "8-10-2019", "invoiceExchangeRate": 1.0, "invoiceExpirationDate": "23-10-2019", "invoiceId": "30698", "isAdvance": false, "paymentDifference": 0.0, "paymentDifferenceAcceptable": false, "paymentDifferenceCur": 0.0, "paymentReference": "****************", "rebateApplicable": false, "rebateExpirationDate": "23-10-2019", "rebateRemainingCur": 0.0, "transactionDate": "25-10-2019"}], "customerId": "2906", "customerName": "Aannemingsbedrijf <PERSON> & Vink B.V.", "debitAmount": 0.0, "debitAmountCur": 0.0, "description": "Aannemingsbedrijf <PERSON> & Vink B.V.", "entryType": 1, "journalSection": 0, "lineNumbers": [], "subTransaction": "2", "teleBankGuid": "E09B803D2A934467A2CFFA657020848A", "transactionDate": "25-10-2019"}, {"$type": "UNIT4.Multivers.API.BL.Financial.Edit.CustomerEntryProxy, UNIT4.Multivers.API.Web.WebApi.Model", "accountId": "", "messages": [], "canChange": true, "cannotChangeReason": "", "creditAmount": 4780.77, "creditAmountCur": 4780.77, "currencyId": "", "customerEntryPayments": [{"acceptCreditSqueezeAmount": false, "acceptPaymentDifference": false, "acceptRebateAmount": false, "messages": [], "amountPaid": 4780.77, "amountPaidCur": 4780.77, "canChange": true, "cannotChangeReason": "", "creditSqueezeApplicable": false, "creditSqueezeRemainingCur": 0.0, "description": "", "exchangeRateDifference": 0.0, "invoiceAmountCur": 4780.77, "invoiceBalance": 0.0, "invoiceBalanceCur": 0.0, "invoiceCurrencyId": "", "invoiceDate": "23-9-2019", "invoiceExchangeRate": 1.0, "invoiceExpirationDate": "8-10-2019", "invoiceId": "30547", "isAdvance": false, "paymentDifference": 0.0, "paymentDifferenceAcceptable": false, "paymentDifferenceCur": 0.0, "paymentReference": "****************", "rebateApplicable": false, "rebateExpirationDate": "8-10-2019", "rebateRemainingCur": 0.0, "transactionDate": "25-10-2019"}], "customerId": "19332", "customerName": "Hemubo Bouw B.V.", "debitAmount": 0.0, "debitAmountCur": 0.0, "description": "Hemubo Bouw B.V.", "entryType": 1, "journalSection": 0, "lineNumbers": [], "subTransaction": "2", "teleBankGuid": "7269F81307AF4175A4F001D2B573266A", "transactionDate": "25-10-2019"}, {"$type": "UNIT4.Multivers.API.BL.Financial.Edit.CustomerEntryProxy, UNIT4.Multivers.API.Web.WebApi.Model", "accountId": "", "messages": [], "canChange": true, "cannotChangeReason": "", "creditAmount": 400.45, "creditAmountCur": 400.45, "currencyId": "", "customerEntryPayments": [{"acceptCreditSqueezeAmount": false, "acceptPaymentDifference": false, "acceptRebateAmount": false, "messages": [], "amountPaid": 400.45, "amountPaidCur": 400.45, "canChange": true, "cannotChangeReason": "", "creditSqueezeApplicable": false, "creditSqueezeRemainingCur": 0.0, "description": "", "exchangeRateDifference": 0.0, "invoiceAmountCur": 400.45, "invoiceBalance": 0.0, "invoiceBalanceCur": 0.0, "invoiceCurrencyId": "", "invoiceDate": "16-10-2019", "invoiceExchangeRate": 1.0, "invoiceExpirationDate": "31-10-2019", "invoiceId": "30769", "isAdvance": false, "paymentDifference": 0.0, "paymentDifferenceAcceptable": false, "paymentDifferenceCur": 0.0, "paymentReference": "****************", "rebateApplicable": false, "rebateExpirationDate": "31-10-2019", "rebateRemainingCur": 0.0, "transactionDate": "25-10-2019"}], "customerId": "13966", "customerName": "Veenstra en De Ruiter B.V.", "debitAmount": 0.0, "debitAmountCur": 0.0, "description": "Veenstra en De Ruiter B.V.", "entryType": 1, "journalSection": 0, "lineNumbers": [], "subTransaction": "2", "teleBankGuid": "1862E169EB8B4F20892AF522113EAA80", "transactionDate": "25-10-2019"}, {"$type": "UNIT4.Multivers.API.BL.Financial.Edit.CustomerEntryProxy, UNIT4.Multivers.API.Web.WebApi.Model", "accountId": "", "messages": [], "canChange": true, "cannotChangeReason": "", "creditAmount": 1646.56, "creditAmountCur": 1646.56, "currencyId": "", "customerEntryPayments": [{"acceptCreditSqueezeAmount": false, "acceptPaymentDifference": false, "acceptRebateAmount": false, "messages": [], "amountPaid": 1646.56, "amountPaidCur": 1646.56, "canChange": true, "cannotChangeReason": "", "creditSqueezeApplicable": false, "creditSqueezeRemainingCur": 0.0, "description": "", "exchangeRateDifference": 0.0, "invoiceAmountCur": 1646.56, "invoiceBalance": 0.0, "invoiceBalanceCur": 0.0, "invoiceCurrencyId": "", "invoiceDate": "22-10-2019", "invoiceExchangeRate": 1.0, "invoiceExpirationDate": "6-11-2019", "invoiceId": "30857", "isAdvance": false, "paymentDifference": 0.0, "paymentDifferenceAcceptable": false, "paymentDifferenceCur": 0.0, "paymentReference": "****************", "rebateApplicable": false, "rebateExpirationDate": "6-11-2019", "rebateRemainingCur": 0.0, "transactionDate": "25-10-2019"}], "customerId": "19880", "customerName": "Kozion B.V.", "debitAmount": 0.0, "debitAmountCur": 0.0, "description": "Kozion B.V.", "entryType": 1, "journalSection": 0, "lineNumbers": [], "subTransaction": "2", "teleBankGuid": "4FB05EC27FF8466D998D2F7901A90875", "transactionDate": "25-10-2019"}, {"$type": "UNIT4.Multivers.API.BL.Financial.Edit.CustomerEntryProxy, UNIT4.Multivers.API.Web.WebApi.Model", "accountId": "", "messages": [], "canChange": true, "cannotChangeReason": "", "creditAmount": 257.38, "creditAmountCur": 257.38, "currencyId": "", "customerEntryPayments": [{"acceptCreditSqueezeAmount": false, "acceptPaymentDifference": false, "acceptRebateAmount": false, "messages": [], "amountPaid": 257.38, "amountPaidCur": 257.38, "canChange": true, "cannotChangeReason": "", "creditSqueezeApplicable": false, "creditSqueezeRemainingCur": 0.0, "description": "", "exchangeRateDifference": 0.0, "invoiceAmountCur": 257.38, "invoiceBalance": 0.0, "invoiceBalanceCur": 0.0, "invoiceCurrencyId": "", "invoiceDate": "23-9-2019", "invoiceExchangeRate": 1.0, "invoiceExpirationDate": "8-10-2019", "invoiceId": "30540", "isAdvance": false, "paymentDifference": 0.0, "paymentDifferenceAcceptable": false, "paymentDifferenceCur": 0.0, "paymentReference": "****************", "rebateApplicable": false, "rebateExpirationDate": "8-10-2019", "rebateRemainingCur": 0.0, "transactionDate": "25-10-2019"}], "customerId": "21766", "customerName": "<PERSON><PERSON><PERSON> Ko<PERSON>jnen B.V.", "debitAmount": 0.0, "debitAmountCur": 0.0, "description": "<PERSON><PERSON><PERSON> Ko<PERSON>jnen B.V.", "entryType": 1, "journalSection": 0, "lineNumbers": [], "subTransaction": "2", "teleBankGuid": "E6D4B99045F54122A014F848C5AA869F", "transactionDate": "25-10-2019"}, {"$type": "UNIT4.Multivers.API.BL.Financial.Edit.CustomerEntryProxy, UNIT4.Multivers.API.Web.WebApi.Model", "accountId": "", "messages": [], "canChange": true, "cannotChangeReason": "", "creditAmount": 3853.47, "creditAmountCur": 3853.47, "currencyId": "", "customerEntryPayments": [{"acceptCreditSqueezeAmount": false, "acceptPaymentDifference": false, "acceptRebateAmount": false, "messages": [], "amountPaid": 256.07, "amountPaidCur": 256.07, "canChange": true, "cannotChangeReason": "", "creditSqueezeApplicable": false, "creditSqueezeRemainingCur": 0.0, "description": "", "exchangeRateDifference": 0.0, "invoiceAmountCur": 256.07, "invoiceBalance": 0.0, "invoiceBalanceCur": 0.0, "invoiceCurrencyId": "", "invoiceDate": "25-9-2019", "invoiceExchangeRate": 1.0, "invoiceExpirationDate": "10-10-2019", "invoiceId": "30577", "isAdvance": false, "paymentDifference": 0.0, "paymentDifferenceAcceptable": false, "paymentDifferenceCur": 0.0, "paymentReference": "****************", "rebateApplicable": false, "rebateExpirationDate": "10-10-2019", "rebateRemainingCur": 0.0, "transactionDate": "25-10-2019"}, {"acceptCreditSqueezeAmount": false, "acceptPaymentDifference": false, "acceptRebateAmount": false, "messages": [], "amountPaid": 3597.4, "amountPaidCur": 3597.4, "canChange": true, "cannotChangeReason": "", "creditSqueezeApplicable": false, "creditSqueezeRemainingCur": 0.0, "description": "", "exchangeRateDifference": 0.0, "invoiceAmountCur": 3597.4, "invoiceBalance": 0.0, "invoiceBalanceCur": 0.0, "invoiceCurrencyId": "", "invoiceDate": "25-9-2019", "invoiceExchangeRate": 1.0, "invoiceExpirationDate": "10-10-2019", "invoiceId": "30578", "isAdvance": false, "paymentDifference": 0.0, "paymentDifferenceAcceptable": false, "paymentDifferenceCur": 0.0, "paymentReference": "****************", "rebateApplicable": false, "rebateExpirationDate": "10-10-2019", "rebateRemainingCur": 0.0, "transactionDate": "25-10-2019"}], "customerId": "8454", "customerName": "Maas-Jacobs Ku<PERSON>stof Ko<PERSON>jnen B.V.", "debitAmount": 0.0, "debitAmountCur": 0.0, "description": "Maas-Jacobs Ku<PERSON>stof Ko<PERSON>jnen B.V.", "entryType": 1, "journalSection": 0, "lineNumbers": [], "subTransaction": "2", "teleBankGuid": "A5C064E69FAF4B438B361335C0BD1540", "transactionDate": "25-10-2019"}, {"$type": "UNIT4.Multivers.API.BL.Financial.Edit.CustomerEntryProxy, UNIT4.Multivers.API.Web.WebApi.Model", "accountId": "", "messages": [], "canChange": true, "cannotChangeReason": "", "creditAmount": 161.34, "creditAmountCur": 161.34, "currencyId": "", "customerEntryPayments": [{"acceptCreditSqueezeAmount": false, "acceptPaymentDifference": false, "acceptRebateAmount": false, "messages": [], "amountPaid": 161.34, "amountPaidCur": 161.34, "canChange": true, "cannotChangeReason": "", "creditSqueezeApplicable": false, "creditSqueezeRemainingCur": 0.0, "description": "", "exchangeRateDifference": 0.0, "invoiceAmountCur": 161.34, "invoiceBalance": 0.0, "invoiceBalanceCur": 0.0, "invoiceCurrencyId": "", "invoiceDate": "22-10-2019", "invoiceExchangeRate": 1.0, "invoiceExpirationDate": "6-11-2019", "invoiceId": "30865", "isAdvance": false, "paymentDifference": 0.0, "paymentDifferenceAcceptable": false, "paymentDifferenceCur": 0.0, "paymentReference": "****************", "rebateApplicable": false, "rebateExpirationDate": "6-11-2019", "rebateRemainingCur": 0.0, "transactionDate": "25-10-2019"}], "customerId": "56679", "customerName": "JR Bouw & Kozijnen", "debitAmount": 0.0, "debitAmountCur": 0.0, "description": "JR Bouw & Kozijnen", "entryType": 1, "journalSection": 0, "lineNumbers": [], "subTransaction": "2", "teleBankGuid": "C4B20BC65FAA4176B06C6907BC082AAC", "transactionDate": "25-10-2019"}, {"$type": "UNIT4.Multivers.API.BL.Financial.Edit.CustomerEntryProxy, UNIT4.Multivers.API.Web.WebApi.Model", "accountId": "", "messages": [], "canChange": true, "cannotChangeReason": "", "creditAmount": 191.76, "creditAmountCur": 191.76, "currencyId": "", "customerEntryPayments": [{"acceptCreditSqueezeAmount": false, "acceptPaymentDifference": false, "acceptRebateAmount": false, "messages": [], "amountPaid": 191.76, "amountPaidCur": 191.76, "canChange": true, "cannotChangeReason": "", "creditSqueezeApplicable": false, "creditSqueezeRemainingCur": 0.0, "description": "", "exchangeRateDifference": 0.0, "invoiceAmountCur": 191.76, "invoiceBalance": 0.0, "invoiceBalanceCur": 0.0, "invoiceCurrencyId": "", "invoiceDate": "15-10-2019", "invoiceExchangeRate": 1.0, "invoiceExpirationDate": "30-10-2019", "invoiceId": "30748", "isAdvance": false, "paymentDifference": 0.0, "paymentDifferenceAcceptable": false, "paymentDifferenceCur": 0.0, "paymentReference": "****************", "rebateApplicable": false, "rebateExpirationDate": "30-10-2019", "rebateRemainingCur": 0.0, "transactionDate": "25-10-2019"}], "customerId": "22449", "customerName": "BeliDrechtsteden B.V.", "debitAmount": 0.0, "debitAmountCur": 0.0, "description": "BeliDrechtsteden B.V.", "entryType": 1, "journalSection": 0, "lineNumbers": [], "subTransaction": "2", "teleBankGuid": "4F68163E92424D9CBEAE713899ACEA6A", "transactionDate": "25-10-2019"}, {"$type": "UNIT4.Multivers.API.BL.Financial.Edit.CustomerEntryProxy, UNIT4.Multivers.API.Web.WebApi.Model", "accountId": "", "messages": [], "canChange": true, "cannotChangeReason": "", "creditAmount": 727.49, "creditAmountCur": 727.49, "currencyId": "", "customerEntryPayments": [{"acceptCreditSqueezeAmount": false, "acceptPaymentDifference": false, "acceptRebateAmount": false, "messages": [], "amountPaid": 727.49, "amountPaidCur": 727.49, "canChange": true, "cannotChangeReason": "", "creditSqueezeApplicable": false, "creditSqueezeRemainingCur": 0.0, "description": "", "exchangeRateDifference": 0.0, "invoiceAmountCur": 727.49, "invoiceBalance": 0.0, "invoiceBalanceCur": 0.0, "invoiceCurrencyId": "", "invoiceDate": "15-10-2019", "invoiceExchangeRate": 1.0, "invoiceExpirationDate": "30-10-2019", "invoiceId": "30741", "isAdvance": false, "paymentDifference": 0.0, "paymentDifferenceAcceptable": false, "paymentDifferenceCur": 0.0, "paymentReference": "****************", "rebateApplicable": false, "rebateExpirationDate": "30-10-2019", "rebateRemainingCur": 0.0, "transactionDate": "25-10-2019"}], "customerId": "114246", "customerName": "Nos3 B.V. Hodn I-kozijn", "debitAmount": 0.0, "debitAmountCur": 0.0, "description": "Nos3 B.V. Hodn I-kozijn", "entryType": 1, "journalSection": 0, "lineNumbers": [], "subTransaction": "2", "teleBankGuid": "17BB53081B14411E87BA0B39100B0E17", "transactionDate": "25-10-2019"}, {"$type": "UNIT4.Multivers.API.BL.Financial.Edit.CustomerEntryProxy, UNIT4.Multivers.API.Web.WebApi.Model", "accountId": "", "messages": [], "canChange": true, "cannotChangeReason": "", "creditAmount": 952.87, "creditAmountCur": 952.87, "currencyId": "", "customerEntryPayments": [{"acceptCreditSqueezeAmount": false, "acceptPaymentDifference": false, "acceptRebateAmount": false, "messages": [], "amountPaid": -63.53, "amountPaidCur": -63.53, "canChange": true, "cannotChangeReason": "", "creditSqueezeApplicable": false, "creditSqueezeRemainingCur": 0.0, "description": "", "exchangeRateDifference": 0.0, "invoiceAmountCur": -63.53, "invoiceBalance": 0.0, "invoiceBalanceCur": 0.0, "invoiceCurrencyId": "", "invoiceDate": "24-10-2019", "invoiceExchangeRate": 1.0, "invoiceExpirationDate": "8-11-2019", "invoiceId": "30876", "isAdvance": false, "paymentDifference": 0.0, "paymentDifferenceAcceptable": false, "paymentDifferenceCur": 0.0, "paymentReference": "****************", "rebateApplicable": false, "rebateExpirationDate": "8-11-2019", "rebateRemainingCur": 0.0, "transactionDate": "25-10-2019"}, {"acceptCreditSqueezeAmount": false, "acceptPaymentDifference": false, "acceptRebateAmount": false, "messages": [], "amountPaid": 1016.4, "amountPaidCur": 1016.4, "canChange": true, "cannotChangeReason": "", "creditSqueezeApplicable": false, "creditSqueezeRemainingCur": 0.0, "description": "", "exchangeRateDifference": 0.0, "invoiceAmountCur": 1016.4, "invoiceBalance": 0.0, "invoiceBalanceCur": 0.0, "invoiceCurrencyId": "", "invoiceDate": "18-10-2019", "invoiceExchangeRate": 1.0, "invoiceExpirationDate": "2-11-2019", "invoiceId": "30814", "isAdvance": false, "paymentDifference": 0.0, "paymentDifferenceAcceptable": false, "paymentDifferenceCur": 0.0, "paymentReference": "****************", "rebateApplicable": false, "rebateExpirationDate": "2-11-2019", "rebateRemainingCur": 0.0, "transactionDate": "25-10-2019"}], "customerId": "20336", "customerName": "Burghouwt Bouwbeslag B.V.", "debitAmount": 0.0, "debitAmountCur": 0.0, "description": "Burghouwt Bouwbeslag B.V.", "entryType": 1, "journalSection": 0, "lineNumbers": [], "subTransaction": "2", "teleBankGuid": "C11F36A1BCE6451FBC90E56A25705BB4", "transactionDate": "25-10-2019"}, {"$type": "UNIT4.Multivers.API.BL.Financial.Edit.CustomerEntryProxy, UNIT4.Multivers.API.Web.WebApi.Model", "accountId": "", "messages": [], "canChange": true, "cannotChangeReason": "", "creditAmount": 485.65, "creditAmountCur": 485.65, "currencyId": "", "customerEntryPayments": [{"acceptCreditSqueezeAmount": false, "acceptPaymentDifference": false, "acceptRebateAmount": false, "messages": [], "amountPaid": 485.65, "amountPaidCur": 485.65, "canChange": true, "cannotChangeReason": "", "creditSqueezeApplicable": false, "creditSqueezeRemainingCur": 0.0, "description": "", "exchangeRateDifference": 0.0, "invoiceAmountCur": 485.65, "invoiceBalance": 0.0, "invoiceBalanceCur": 0.0, "invoiceCurrencyId": "", "invoiceDate": "22-10-2019", "invoiceExchangeRate": 1.0, "invoiceExpirationDate": "6-11-2019", "invoiceId": "30859", "isAdvance": false, "paymentDifference": 0.0, "paymentDifferenceAcceptable": false, "paymentDifferenceCur": 0.0, "paymentReference": "****************", "rebateApplicable": false, "rebateExpirationDate": "6-11-2019", "rebateRemainingCur": 0.0, "transactionDate": "25-10-2019"}], "customerId": "20339", "customerName": "AA Kozijnen", "debitAmount": 0.0, "debitAmountCur": 0.0, "description": "AA Kozijnen", "entryType": 1, "journalSection": 0, "lineNumbers": [], "subTransaction": "2", "teleBankGuid": "0538BD43BD60430A9B24FDA6C74CBEDD", "transactionDate": "25-10-2019"}], "fiscalYear": 2019, "journalId": "20", "journalTransaction": 203, "openingBalanceTransaction": false, "periodNumber": 10, "previousBalance": 64419.15, "totalCreditAmount": 18068.09, "totalDebitAmount": 3038.01, "transactionDate": "25-10-2019"}