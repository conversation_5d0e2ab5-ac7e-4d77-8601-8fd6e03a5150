<section>

  <div class="row">

    <?php TemplateHelper::includePartial("_topmessage.php","siteshop") ?>

    <div class="col12 contenttxt">
      <?php echo process_text($page->content->content1); ?>
    </div>
  </div>
  <div class="row">
    <?php foreach($cats as $cat): ?>
      <div class="col6 col12-xs">
        <div class="product-col">
          <a href="<?php echo $cat->getShopUrl() ?>" class="product-col-a">
            <?php if($cat->getPhoto(false)): ?>
              <img src="<?php echo $cat->getPhoto(false) ?>" alt="<?php echo escapeForInput($cat->getName($_SESSION['lang'])) ?>" title="<?php echo escapeForInput($cat->getName($_SESSION['lang'])) ?>" class="img-responsive" />
            <?php else: ?>
              <img class="img-responsive" src="<?php echo $_SESSION['site']->getTemplateUrl() ?>images/noimage.jpg" alt="<?php echo escapeForInput($cat->getName($_SESSION['lang'])) ?>"/>
            <?php endif; ?>
          </a>
          <div class="prcaption">
            <h4 class="ft-green" style="height: auto;">
              <a href="<?php echo $cat->getShopUrl() ?>"><?php echo $cat->getName($_SESSION['lang']) ?></a>
            </h4>
            <div class="cart-button">
              <a class="btn btn-cart bg-moreinfo" href="<?php echo $cat->getShopUrl() ?>" title="<?php echo escapeForInput($cat->getName($_SESSION['lang'])) ?>">
                MEER INFO
                <i class="fa fa-chevron-circle-right ft-yellow bg-grey"></i>
              </a>
            </div>
          </div>
        </div>
      </div>
    <?php endforeach; ?>
  </div>

</section>

