<?php

  AppModel::loadBaseClass('BaseWorkerPlanhour');

  class WorkerPlanhourModel extends BaseWorkerPlanhour {

    public function getDate($format = "d-m-Y") {
      if ($this->date != "") {
        return date($format, strtotime($this->date));
      }
      return '';
    }

    /**
     * @param int $worker_id
     * @param $from
     * @param $to
     * @return WorkerPlanhour[]
     */
    public static function getFromTo($worker_id, $from, $to) {
      return AppModel::mapObjectIds(WorkerPlanhour::find_all_by(["worker_id" => $worker_id], "AND date>='" . $from . "' AND date<='" . $to . "'"), 'date');
    }


    /**
     * Planning ophalen van 1 week vanaf maandag
     * @param $starttime
     * @return array
     */
    public static function getPlanningForWeek($starttime, $workerids = false) {

      $values = [];
      $filt = "WHERE DATE(date) BETWEEN '" . date("Y-m-d", $starttime) . "' AND '" . date("Y-m-d", strtotime("+6 DAYS", $starttime)) . "' ";
      if ($workerids !== false) {
        if (count($workerids) == 0) {
          $filt .= "AND 0 ";
        }
        else {
          $filt .= "AND worker_id IN (" . implode(",", $workerids) . ") ";
        }
      }
      $filt .= "ORDER BY date ";
      $daysplanned = WorkerPlanhour::find_all($filt);
      foreach ($daysplanned as $day) {
        $values[$day->worker_id][$day->getDate("U")] = $day->hours;
      }

      return $values;

    }

    public static function getPlanningForDay($date) {
      return WorkerPlanhour::find_all_by(["date" => $date]);
    }

    public static function checkPlannedAreCheckedIn() {
      $phs = WorkerPlanhour::getPlanningForDay(date("Y-m-d"));
      $workerids_planned = [];
      foreach ($phs as $ph) {
        $workerids_planned[] = $ph->worker_id;
      }
      $workers_checked_in = AppModel::mapObjectIds(WorkerHours::find_all("WHERE worker_hours.in>'" . date("Y-m-d 00:00:00") . "' GROUP BY worker_id"), 'worker_id');

      $message = "Beste,<br/><br/>";
      $message .= "Niet alle medewerkers welke zijn ingeplanned hebben ingeklokt.<br/><br/>";
      $message .= "De volgende medewerkers zijn niet aanwezig:<br/>";
      $send = false;
      foreach ($workerids_planned as $id) {
        if (!isset($workers_checked_in[$id])) {
          $worker = Worker::find_by_id($id);
          $message .= "- " . $worker->getNaam() . "<br/>";
          $send = true;
        }
      }
      $message .= "<br/>Met groet,<br/><br/>De Prikklok App";

      if ($send) {
        $to = MAIL_FROM;
        GsdMailer::build($to, "Medewerkers niet aanwezig", $message)->send();
      }

    }

  }