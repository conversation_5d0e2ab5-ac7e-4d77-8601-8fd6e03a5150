<?php

  AppModel::loadModelClass('MessageModel');

  class Message extends MessageModel {

    /**
     * @param $worker
     */
    public static function addMessageVGM($worker) {

      foreach (User::find_all_by(['usergroup' => User::USERGROUP_ADMIN, 'void' => 0]) as $user) {
        $mes = new Message();
        $mes->type = Message::MESSAGE_TYPE_PROJECT;
        $mes->code = "WORKER_VGM";
        $mes->user_id = $user->id;
        $mes->object_id = $worker->id;

        $mes->message = sprintf(__("Nieuwe medwerker %s. De VGM is nog niet geupload."), $worker->getNaam());
        $mes->url = PageMap::getUrl("M_WORKER_FILE", $user->locale) . 'id=' . $worker->id;
        $mes->url_title = __("VGM uploaden");

        $mes->save();

        MessageCoordinator::clearTS();
      }

    }

    /**
     * @param $workerid
     */
    public static function remMessageVGM($workerid) {
      if (Message::delete_by(['code' => "WORKER_VGM", 'object_id' => $workerid])) {
        MessageCoordinator::clearTS();
      }
    }

  }