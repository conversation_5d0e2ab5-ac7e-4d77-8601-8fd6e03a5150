<?php TemplateHelper::includePartial('_tabs.php', 'orders', compact(["quotation", "quotation_extra", "company", "sandboxuser"])); ?>

<h2>Plan route</h2>

<?php foreach ($groupedQuotations as $quotations): ?>
  <section class="content-block">
    <h2 style="margin-left: 1em;">
      <?php echo reset($quotations)->getAddress(); ?>
    </h2>
    <table class="default-data-table compact dataTable no-footer">
      <thead>
      <tr>
        <th>
          <input type="checkbox" class="select-all-quotations"/>
        </th>
        <th>Status</th>
        <th>Route ID</th>
        <th>Leverdatum</th>
        <th>Verwijderen</th>
        <th>Route</th>
        <th>Transport</th>
        <th>Offerte nummer</th>
        <th>Projectnaam</th>
        <th>Kenmerk</th>
        <th>Plaats</th>
        <th>Uiterlijke leverdatum</th>
        <th>Leverweek</th>
        <th>Opmerking</th>
        <th>Meters</th>
        <th>Bak nummers</th>
        <th>Adres kenmerk</th>
      </tr>
      </thead>
      <tbody>
      <?php foreach ($quotations as $quotation): ?>
        <tr id="<?php echo $quotation->quotationId ?>">
          <td>
            <?php if (!$quotation->gpsbuddy_rde->routeId): ?>
              <input type="checkbox" value="<?php echo $quotation->quotationId ?>" class="select-quotation"/>
            <?php endif; ?>
          </td>
          <td><?php echo Status::getIconHTML($quotation->statusId) ?></td>
          <td><?php echo $quotation->gpsbuddy_rde->routeId ?></td>
          <td><?php echo $quotation->gpsbuddy_route->date ?></td>
          <td>
            <?php if ($quotation->gpsbuddy_rde->routeId): ?>
              <input type="checkbox" value="<?php echo $quotation->gpsbuddy_rde->logId ?>" class="select-rde"/>
            <?php endif; ?>
          </td>
          <td><?php echo $quotation->route_title->title ?></td>
          <td><?php echo $quotation->gpsbuddy_truck ? $quotation->gpsbuddy_truck->name . $quotation->gpsbuddy_truck->licence : '' ?></td>
          <td>
            <a href="<?php echo PageMap::getUrl("M_RDE_ORDERS_GENERAL", ['id' => $quotation->quotationId]) ?>">
              <?php echo $quotation->getQuotationNumberFull() ?>
            </a>
          </td>
          <td><?php echo $quotation->projectName ?></td>
          <td><?php echo $quotation->projectReference ?></td>
          <td><?php echo $quotation->domestic ?></td>
          <td><?php echo $quotation->dueDate ?></td>
          <td><?php echo $quotation->dueDateWeek ?></td>
          <td>
            <?php if ($quotation->internNotes): ?>
              <span style="color: #982e1a;" class="qtipa fa_info fa fa-info-circle" title="<?php echo $quotation->internNotes ?>"></span>
            <?php endif; ?>
          </td>
          <td><?php echo $quotation->meters ?></td>
          <td><?php if ($quotation->gpsbuddy_rde->bakId) echo $quotation->gpsbuddy_rde->bakId ?></td>
          <td><?php echo $quotation->quotation_extra->addressSplit ?></td>
        </tr>
      <?php endforeach; ?>
      </tbody>
    </table>
  </section>
<?php endforeach; ?>

<?php writeErrors($errors); ?>

<form method="post" class="edit-form">
  <input type="hidden" name="quotationIds" value="">
  <input type="hidden" name="rdeIds" value="">
  <table class="default_table">
    <tr class="dataTableHeadingRow">
      <td>Item</td>
      <td>Setting</td>
    </tr>
    <tr class="dataTableRow">
      <td>Selecteer datum</td>
      <td><input class="datepicker datepicker_weeks" placeholder="datum" name="selectDate"></td>
    </tr>
    <?php foreach ($trucks as $truck): ?>
      <tr class="dataTableRow">
        <td>
          <label>
            <input class="radio-truck" type="radio" name="truck" value="<?php echo $truck->truckId ?>">
            <?php echo $truck->name . ' ' . $truck->licence ?>
          </label>
        </td>
        <td>
          <input type="text" placeholder="opmerking..." name="truckRemark_<?php echo $truck->truckId ?>">
        </td>
      </tr>
    <?php endforeach; ?>
  </table>
  <input type="submit" name="save_route" id="save_route" value="Opslaan" class="gsd-btn gsd-btn-primary"/>
  <input type="submit" name="save_to_list" value="Opslaan naar lijst" class="gsd-btn gsd-btn-primary"/>
</form>

<script type="text/javascript">
  $(document).ready(function () {
    // Initialiseer alle tabellen
    $('table').each(function () {
      const $table = $(this);

      // 'Selecteer alles' binnen deze tabel
      $table.find('.select-all-quotations').on('click', function () {
        const isChecked = $(this).prop('checked');

        // Deselecteer alles in andere tabellen
        $('table').not($table).each(function () {
          $(this).find('input.select-quotation').prop('checked', false);
          $(this).find('.select-all-quotations').prop('checked', false);
        });

        // Selecteer of deselecteer in huidige tabel
        $table.find('input.select-quotation').prop('checked', isChecked);
      });

      // Individuele checkbox selectie
      $table.find('input.select-quotation').on('click', function () {
        if (this.checked) {
          // Deselecteer alle andere tabellen
          $('table').not($table).each(function () {
            $(this).find('input.select-quotation').prop('checked', false);
            $(this).find('.select-all-quotations').prop('checked', false);
          });
        }

        // Update de 'select all' checkbox van deze tabel
        const allChecked = $table.find('input.select-quotation').length === $table.find('input.select-quotation:checked').length;
        $table.find('.select-all-quotations').prop('checked', allChecked);
      });
    });

    // Verzamel IDs bij verzenden
    $('#save_route, #save_to_list').click(function () {
      const selectedQuotations = [];
      $('input.select-quotation:checked').each(function () {
        selectedQuotations.push($(this).val());
      });
      $('input[name="quotationIds"]').val(selectedQuotations.join(','));

      const selectedRdes = [];
      $('input.select-rde:checked').each(function () {
        selectedRdes.push($(this).val());
      });
      $('input[name="rdeIds"]').val(selectedRdes.join(','));
    });
  });
</script>