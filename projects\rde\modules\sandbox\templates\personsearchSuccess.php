<?php
  TemplateHelper::includePartial('_tabs.php', 'sandbox');
  include("_header.php")
?>
<h3>3. Persoon zoeken of aanmaken</h3>

<div>

</div>

  <div class="list-filter-form">
    <a href="?action=personcreate&id=<?php echo $sandboxuser->userId ?>&companyid=<?php echo $company->companyId ?>" class="gsd-btn gsd-btn-primary" title="Aanmaken nieuw persoon">Aanmaken nieuw persoon</a>
  </div>
  <Br/>

  <?php if(count($persons)==0): ?>
    <br/><br/>
    Er zijn geen personen gevonden binnen dit bedrijf.
  <?php else: ?>
      Personenlijst binnen geselecteerde bedrijf.<br/><br/>
    <table class="default_table">
      <tr class="dataTableHeadingRow">
        <td>Naam</td>
        <td>Telefoonnummer</td>
        <td>Mobiel</td>
        <td>Email</td>
        <td>Verwijderen</td>
        <td><PERSON><PERSON>en</td>
      </tr>
      <?php
        /** @var CrmPersons $company */
        foreach($persons as $person): ?>
        <tr class="dataTableRow trhover">
          <td><?php echo $person->getNaam() ?></td>
          <td><?php echo $person->phone ?></td>
          <td><?php echo $person->mobile ?></td>
          <td><?php echo $person->email ?></td>
          <td>
            <?php echo BtnHelper::getRemove(reconstructQueryAdd(['pageId']) . 'action=persondelete&id=' . $sandboxuser->userId . '&relatedpersonid=' . $person->personId . '&companyid=' . $company->companyId,"Weet u zeker dat u deze persoon wilt verwijderen?") ?>
          </td>
          <td>
            <a href="<?php echo reconstructQueryAdd(['pageId'])?>action=personcompare&id=<?php echo $sandboxuser->userId ?>&relatedpersonid=<?php echo $person->personId ?>&companyid=<?php echo $company->companyId ?>" class="gsd-btn gsd-btn-secondary">
              Vergelijken
            </a>
          </td>
        </tr>
      <?php endforeach; ?>
    </table>
  <?php endif; ?>

  <br/>
  <a href="<?php echo reconstructQueryAdd(['pageId'])?>action=companycompare&id=<?php echo $sandboxuser->userId ?>&relatedid=<?php echo $company->companyId ?>" class="gsd-btn gsd-btn-secondary">Vorige stap</a>
