<?php

  /**
   * <PERSON><PERSON> voorraad overzicht pdf.
   *
   * Class StockPdf
   */
  class StockPdf extends GSDPDF {

    var $show = true;

    public function __construct($show = true) {
      parent::__construct();
      $this->AddFont('Nunito', '', 'Nunito-Regular.ttf', true);
      $this->AddFont('Nunito', 'B', 'Nunito-Bold.ttf', true);

      $this->SetAutoPageBreak(true, 30);
      $this->show = $show;
    }

    public function Header() {
      //$this->Image('C:\Work\www\HTTPDOCS\html\rde\filesystem\raamdorpel\clients\quotation\elementparts\105883\159915.png',null,null,0,0,'png');
      $this->Image(DIR_PROJECT_FOLDER . "templates/backend2/images/logopdf.png", null, null, 90);
      $this->SetFont('Nunito', '', 10);
      $this->Cell(25);
      $this->Cell(0, 5, '', 0, 1, 'L', false);

      $this->SetLeftMargin(104);
      $this->SetY(10.5);
      $this->SetFont('', 'B');
      $this->Cell(91, 4, "Raamdorpelelementen BV", 0, 1, "R");
      $this->SetFont('', '');
      $this->Cell(91, 4, "Raambrug 9", 0, 1, "R");
      $this->Cell(91, 4, "5531 AG Bladel", 0, 1, "R");
      $this->Cell(91, 4, "0497 36 07 91", 0, 1, "R");
      $this->Cell(91, 4, "<EMAIL>", 0, 1, "R");
      $this->Cell(91, 4, "www.raamdorpel.nl", 0, 1, "R");
      $this->Ln();
      $this->Ln(3.5);

      $this->SetX(10);
      $this->SetLeftMargin(10);
    }

    // Footer
    // - Public
    // Adds HeBlad Raamdorpelelementen page footer
    public function Footer() {

      $this->setY(-25);
      $this->SetFont('Nunito', 'B', 10);
      $this->Cell(0, 7, 'Raamdorpel.nl - Raamdorpelelementen BV', 'T', 1, 'L', false);

      $this->SetFont('Nunito', '', 10);
      $this->SetTextColor(128, 128, 128);
      $this->Cell(15, 5, 'Adres:');
      $this->SetTextColor(0, 0, 0);
      $this->Cell(50, 5, 'Raambrug 9', 0, 0, 'L', false);
      $this->SetTextColor(128, 128, 128);
      $this->Cell(25, 5, 'Telefoon:');
      $this->SetTextColor(0, 0, 0);
      $this->Cell(35, 5, '0497 - 36.07.91', 0, 0, 'L', false);
      $this->SetTextColor(128, 128, 128);
      $this->Cell(25, 5, 'E-mail:');
      $this->SetTextColor(0, 0, 0);
      $this->Cell(35, 5, '<EMAIL>', 0, 1, 'L', false);

      $this->Cell(15);
      $this->Cell(50, 5, '5531 AG Bladel', 0, 0, 'L', false);
      $this->SetTextColor(128, 128, 128);
      $this->Cell(25, 5, 'Fax:');
      $this->SetTextColor(0, 0, 0);
      $this->Cell(35, 5, '0497 - 38.09.71', 0, 0, 'L', false);
      $this->SetTextColor(128, 128, 128);
      $this->Cell(25, 5, 'Website:');
      $this->SetTextColor(0, 0, 0);
      $this->Cell(35, 5, 'www.raamdorpel.nl', 0, 1, 'L', false);

    }

    /**
     * @param bool $onlyunknown
     * @return string filename
     */
    public function generatePdf($onlyunknown = false) {

      $stones = Stones::find_all("WHERE stock>0 ORDER BY name");
      $colors = AppModel::mapObjectIds(StoneColors::find_all(), 'colorId');
      $prices = StonePrices::getLatestPrices();

      $this->AddPage();

      $this->SetCreator('raamdorpel.nl');
      $this->SetAuthor('Raamdorpelelementen BV');
      $this->SetTitle("Voorraad inzicht", true);
      $this->SetKeywords('Raamdorpel voorraadinzicht', true);

      $this->AliasNbPages();

      //-----------start rijen---------------
      $this->SetLeftMargin(10);
      $aWidth = [105, 20, 20, 20, 25];

      //Headers

      $this->SetFont('Nunito', 'B', 10);
      $this->Cell($aWidth[0], 5, 'Product', 'B', 0, 'L', false);
      $this->Cell($aWidth[1], 5, 'Voorraad', 'B', 0, 'r', false);
      $this->Cell($aWidth[2], 5, 'Bruto', 'B', 0, 'R', false);
      $this->Cell($aWidth[3], 5, 'Netto', 'B', 0, 'R', false);
      $this->Cell($aWidth[4], 5, 'Netto som', 'B', 0, 'R', false);
      $this->Ln();

      $this->SetFont('Nunito', '', 9);

      $total = 0;
      foreach ($stones as $stone) {
        $color = $colors[$stone->colorId];
        $price = $prices[$stone->stoneId];
        if ($onlyunknown && $price->price <= 100) continue;

        $pricenetto = $price->price * (1 - ($color->discount / 100));
        $rowsom = $pricenetto * $stone->stock;
        if ($price->price <= 100) {
          $total += $rowsom;
        }

        $this->Cell($aWidth[0], 5, substr($stone->name, 0, 78), 0, 0, 'L', false);
        $this->Cell($aWidth[1], 5, $stone->stock, 0, 0, 'R', false);
        $this->Cell($aWidth[2], 5, ($price->price > 100 ? '?' : '€ ' . getLocalePrice($price->price)), 0, 0, 'R', false);
        $this->Cell($aWidth[3], 5, ($price->price > 100 ? '?' : '€ ' . getLocalePrice($pricenetto)), 0, 0, 'R', false);
        $this->Cell($aWidth[4], 5, ($price->price > 100 ? '?' : '€ ' . getLocalePrice($rowsom, ",", ".")), 0, 0, 'R', false);
        $this->Ln();
      }
      if (!$onlyunknown) {
        $this->Cell($aWidth[0] + $aWidth[1] + $aWidth[2] + $aWidth[3] + $aWidth[4], 0, '', 'T', true);

        $this->SetFont('Nunito', 'B');
        $this->Cell($aWidth[0] + $aWidth[1] + $aWidth[2] + $aWidth[3], 5, "Totaal", 0, 0, 'R');
        $this->Cell($aWidth[4], 5, '€ ' . getLocalePrice($total, ',', '.', 0), 0, 0, 'R');

      }


      $this->SetDisplayMode('real');

      $filename = 'Voorraad_stenen_balans_' . date("dmY") . ($onlyunknown ? '_ongeldig' : '') . '.pdf';
      if ($this->show) {
        $this->Output("I", $filename);
      }
      else {
        $this->Output("F", DIR_TEMP . $filename);
      }
      return $filename;

    }

  }