<?php
class BaseQuoteStatus35 extends AppModel
{
  const DB_NAME = 'rde_b1mo';
  const TABLE_NAME = 'quote_status_35';
  const OM_CLASS_NAME = 'QuoteStatus35';
  const columns = ['quoteStatus35Id', 'quotationId', 'elementId', 'quotationNumber', 'companyName', 'projectName', 'referenceName', 'elementLength', 'splitReferenceName', 'elementAmount'];
  const field_structure = [
    'quoteStatus35Id'             => ['type' => 'int', 'length' => '11', 'null' => false],
    'quotationId'                 => ['type' => 'int', 'length' => '11', 'null' => false],
    'elementId'                   => ['type' => 'int', 'length' => '11', 'null' => false],
    'quotationNumber'             => ['type' => 'varchar', 'length' => '30', 'null' => false],
    'companyName'                 => ['type' => 'varchar', 'length' => '200', 'null' => false],
    'projectName'                 => ['type' => 'varchar', 'length' => '255', 'null' => false],
    'referenceName'               => ['type' => 'varchar', 'length' => '30', 'null' => false],
    'elementLength'               => ['type' => 'varchar', 'length' => '50', 'null' => false],
    'splitReferenceName'          => ['type' => 'varchar', 'length' => '30', 'null' => false],
    'elementAmount'               => ['type' => 'varchar', 'length' => '50', 'null' => false],
  ];

  protected static $primary_key = ['quoteStatus35Id'];
  protected $auto_increment = 'quoteStatus35Id';

  public $quoteStatus35Id, $quotationId, $elementId, $quotationNumber, $companyName, $projectName, $referenceName, $elementLength, $splitReferenceName, $elementAmount;

  function __construct($obj_arr = [], &$error_codes = []) {
    parent::__construct();
    $this->setDefaults();
    if (!empty($obj_arr)) {
      $this->from_array($obj_arr);
      $this->valid_required_fields($error_codes);
    }
  }

  /**
  * Set default object values
  **/
  public function setDefaults() {
  }

  /**
   * @param array $conditions (required)
   * @param string $raw_sql (optional)
   * @return QuoteStatus35[]
   **/
  public static function find_all_like($conditions, $raw_sql = '') {
    return parent::generic_find_all_like($conditions, static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param array $conditions (required)
   * @param string $raw_sql (optional)
   * @return QuoteStatus35[]
   **/
  public static function find_all_by($conditions, $raw_sql = '') {
    return parent::generic_find_all_by($conditions, static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param string $raw_sql (optional)
   * @return QuoteStatus35[]
   **/
  public static function find_all($raw_sql = '') {
    return parent::generic_find_all(static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param array $conditions (required)
   * @param string $raw_sql (optional)
   * @return QuoteStatus35
   **/
  public static function find_by($conditions, $raw_sql = '') {
    return parent::generic_find_by($conditions, static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param string $id (required)
   * @param string $raw_sql (optional)
   * @return QuoteStatus35
   **/
  public static function find_by_id($id, $raw_sql = '') {
    return parent::generic_find_by_id($id, static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param array $conditions (required)
   * @param string $raw_sql (optional)
   * @return int
   **/
  public static function count_all_by($conditions, $raw_sql = '') {
    return parent::generic_count_all_by($conditions, static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param array $conditions (required)
   * @param string $raw_sql (optional)
   * @return bool
   **/
  public static function delete_by($conditions, $raw_sql = '') {
    return parent::generic_delete_by($conditions, static::OM_CLASS_NAME, $raw_sql);
  }

}