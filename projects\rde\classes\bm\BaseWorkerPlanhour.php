<?php
class BaseWorkerPlanhour extends AppModel
{
  const DB_NAME = '';
  const TABLE_NAME = 'worker_planhour';
  const OM_CLASS_NAME = 'WorkerPlanhour';

  const columns = ['id', 'worker_id', 'date', 'hours'];

  protected static $primary_key = ['id'];

  public $column_names = ['id', 'worker_id', 'date', 'hours'];

  public $id, $worker_id, $date, $hours;

  protected $required_fields = ['worker_id', 'date', 'hours'];

  protected $auto_increment = 'id';

  function __construct($obj_arr = [], &$error_codes = [], &$error_msgs = [])
  {
    $this->setDefaults();

    if (!empty($obj_arr))
    {
      $this->from_array($obj_arr, $error_codes);
      $this->valid_required_fields($error_codes, $error_msgs);
    }
  }

  public function valid(&$error_codes = [], &$error_msgs = [])
  {
    $errors = [];

    foreach(self::columns as $column)
    {
      if ($column != $this->auto_increment)
      {
        call_user_func_array([$this, 'v_' . $column], [&$errors]);
      }
    }

    if(empty($errors))
    {
      return true;
    }

    $error_codes = array_merge($error_codes, $errors);
    return false;
  }

  public function valid_required_fields(&$error_codes = [], &$error_msgs = [])
  { 
    $errors = [];

    foreach (self::columns as $column)
    { 
      $function_name = "v_$column";

      if (in_array($column, $this->required_fields))
      {
        call_user_func_array([$this, $function_name], [&$errors]);
      }
      else
      {
        if (strlen($this->{$column}) > 0)
        {
          call_user_func_array([$this, $function_name], [&$errors]);
        }
      }
    }

    if (empty($errors))
    {
      return true;
    }

    $error_codes = array_merge($error_codes, $errors);
    return false;
  }
  public function setDefaults()
  {
  }

  public function v_id(&$error_codes = [])
  {
    if (!is_null($this->id) && strlen($this->id) > 0 && self::valid_mediumint($this->id, '8'))
    {
      return true;
    }

    $error_codes[] = 'id';
    return false;
  }

  public function v_worker_id(&$error_codes = [])
  {
    if (!is_null($this->worker_id) && strlen($this->worker_id) > 0 && self::valid_mediumint($this->worker_id, '8'))
    {
      return true;
    }

    $error_codes[] = 'worker_id';
    return false;
  }

  public function v_date(&$error_codes = [])
  {
    if (!is_null($this->date) && strlen($this->date) > 0 && self::valid_date($this->date))
    {
      return true;
    }

    $error_codes[] = 'date';
    return false;
  }

  public function v_hours(&$error_codes = [])
  {
    if (!is_null($this->hours) && strlen($this->hours) > 0 && self::valid_decimal($this->hours, '8,2'))
    {
      return true;
    }

    $error_codes[] = 'hours';
    return false;
  }

  /**
  *@param array $conditions (required)
  *@param String $raw_sql (optional)
  *@return WorkerPlanhour[]
  **/
  public static function find_all_like($conditions, $raw_sql = '') { return parent::generic_find_all_like($conditions, static::OM_CLASS_NAME, $raw_sql); }
  /**
  *@param array $conditions (required)
  *@param String $raw_sql (optional)
  *@return WorkerPlanhour[]
  **/
  public static function find_all_by($conditions, $raw_sql = '') { return parent::generic_find_all_by($conditions, static::OM_CLASS_NAME, $raw_sql); }
  /**
  *@param String $raw_sql (optional)
  *@return WorkerPlanhour[]
  **/
  public static function find_all($raw_sql = '') { return parent::generic_find_all(static::OM_CLASS_NAME, $raw_sql); }
  /**
  *@param array $conditions (required)
  *@param String $raw_sql (optional)
  *@return WorkerPlanhour
  **/
  public static function find_by($conditions, $raw_sql = '') { return parent::generic_find_by($conditions, static::OM_CLASS_NAME, $raw_sql); }
  /**
  *@param String $id (required)
  *@param String $raw_sql (optional)
  *@return WorkerPlanhour
  **/
  public static function find_by_id($id, $raw_sql = '') { return parent::generic_find_by_id($id, static::OM_CLASS_NAME, $raw_sql); }
  /**
  *@param array $conditions (required)
  *@param String $raw_sql (optional)
  *@return int
  **/
  public static function count_all_by($conditions, $raw_sql = '') { return parent::generic_count_all_by($conditions, static::OM_CLASS_NAME, $raw_sql); }
  /**
  *@param array $conditions (required)
  *@param String $raw_sql (optional)
  *@return bool
  **/
  public static function delete_by($conditions, $raw_sql = '') { return parent::generic_delete_by($conditions, static::OM_CLASS_NAME, $raw_sql); }
}