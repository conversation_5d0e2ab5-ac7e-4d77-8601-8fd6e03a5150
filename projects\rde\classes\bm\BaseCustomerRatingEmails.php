<?php
class BaseCustomerRatingEmails extends AppModel
{
  const DB_NAME = 'rde_b1mo';
  const TABLE_NAME = 'customer_rating_emails';
  const OM_CLASS_NAME = 'CustomerRatingEmails';
  const columns = ['id', 'email', 'userId', 'companyName', 'firstname', 'lastname', 'dateStatusChange', 'inActive', 'sent', 'randomCode'];
  const field_structure = [
    'id'                          => ['type' => 'int', 'length' => '11', 'null' => false],
    'email'                       => ['type' => 'varchar', 'length' => '255', 'null' => false],
    'userId'                      => ['type' => 'int', 'length' => '11', 'null' => false],
    'companyName'                 => ['type' => 'varchar', 'length' => '255', 'null' => false],
    'firstname'                   => ['type' => 'varchar', 'length' => '255', 'null' => false],
    'lastname'                    => ['type' => 'varchar', 'length' => '255', 'null' => false],
    'dateStatusChange'            => ['type' => 'datetime', 'length' => '', 'null' => false],
    'inActive'                    => ['type' => 'boolean', 'length' => '1', 'null' => false],
    'sent'                        => ['type' => 'boolean', 'length' => '1', 'null' => false],
    'randomCode'                  => ['type' => 'varchar', 'length' => '255', 'null' => false],
  ];

  protected static $primary_key = ['id'];
  protected $auto_increment = 'id';

  public $id, $email, $userId, $companyName, $firstname, $lastname, $dateStatusChange, $inActive, $sent, $randomCode;

  function __construct($obj_arr = [], &$error_codes = []) {
    parent::__construct();
    $this->setDefaults();
    if (!empty($obj_arr)) {
      $this->from_array($obj_arr);
      $this->valid_required_fields($error_codes);
    }
  }

  /**
  * Set default object values
  **/
  public function setDefaults() {
    $this->inActive = 0;
    $this->sent = 0;
  }

  /**
   * @param array $conditions (required)
   * @param string $raw_sql (optional)
   * @return CustomerRatingEmails[]
   **/
  public static function find_all_like($conditions, $raw_sql = '') {
    return parent::generic_find_all_like($conditions, static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param array $conditions (required)
   * @param string $raw_sql (optional)
   * @return CustomerRatingEmails[]
   **/
  public static function find_all_by($conditions, $raw_sql = '') {
    return parent::generic_find_all_by($conditions, static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param string $raw_sql (optional)
   * @return CustomerRatingEmails[]
   **/
  public static function find_all($raw_sql = '') {
    return parent::generic_find_all(static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param array $conditions (required)
   * @param string $raw_sql (optional)
   * @return CustomerRatingEmails
   **/
  public static function find_by($conditions, $raw_sql = '') {
    return parent::generic_find_by($conditions, static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param string $id (required)
   * @param string $raw_sql (optional)
   * @return CustomerRatingEmails
   **/
  public static function find_by_id($id, $raw_sql = '') {
    return parent::generic_find_by_id($id, static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param array $conditions (required)
   * @param string $raw_sql (optional)
   * @return int
   **/
  public static function count_all_by($conditions, $raw_sql = '') {
    return parent::generic_count_all_by($conditions, static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param array $conditions (required)
   * @param string $raw_sql (optional)
   * @return bool
   **/
  public static function delete_by($conditions, $raw_sql = '') {
    return parent::generic_delete_by($conditions, static::OM_CLASS_NAME, $raw_sql);
  }

}