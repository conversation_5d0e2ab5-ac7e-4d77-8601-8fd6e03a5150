<?php
class BaseStoneBrands extends AppModel
{
  const DB_NAME = 'rde_b1mo';
  const TABLE_NAME = 'stone_brands';
  const OM_CLASS_NAME = 'StoneBrands';
  const columns = ['brandId', 'short', 'name', 'display', 'displayOrder', 'default', 'orderemail', 'stoneTypeId'];
  const field_structure = [
    'brandId'                     => ['type' => 'tinyint', 'length' => '2', 'null' => false],
    'short'                       => ['type' => 'varchar', 'length' => '8', 'null' => false],
    'name'                        => ['type' => 'varchar', 'length' => '150', 'null' => false],
    'display'                     => ['type' => 'enum', 'length' => '2', 'null' => false, 'enums' => ['true','false']],
    'displayOrder'                => ['type' => 'int', 'length' => '11', 'null' => false],
    'default'                     => ['type' => 'enum', 'length' => '2', 'null' => false, 'enums' => ['true','false']],
    'orderemail'                  => ['type' => 'varchar', 'length' => '255', 'null' => true],
    'stoneTypeId'                 => ['type' => 'int', 'length' => '11', 'null' => false],
  ];

  protected static $primary_key = ['brandId'];
  protected $auto_increment = 'brandId';

  public $brandId, $short, $name, $display, $displayOrder, $default, $orderemail, $stoneTypeId;

  function __construct($obj_arr = [], &$error_codes = []) {
    parent::__construct();
    $this->setDefaults();
    if (!empty($obj_arr)) {
      $this->from_array($obj_arr);
      $this->valid_required_fields($error_codes);
    }
  }

  /**
  * Set default object values
  **/
  public function setDefaults() {
    $this->display = 'false';
    $this->default = 'false';
  }

  /**
   * @param array $conditions (required)
   * @param string $raw_sql (optional)
   * @return StoneBrands[]
   **/
  public static function find_all_like($conditions, $raw_sql = '') {
    return parent::generic_find_all_like($conditions, static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param array $conditions (required)
   * @param string $raw_sql (optional)
   * @return StoneBrands[]
   **/
  public static function find_all_by($conditions, $raw_sql = '') {
    return parent::generic_find_all_by($conditions, static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param string $raw_sql (optional)
   * @return StoneBrands[]
   **/
  public static function find_all($raw_sql = '') {
    return parent::generic_find_all(static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param array $conditions (required)
   * @param string $raw_sql (optional)
   * @return StoneBrands
   **/
  public static function find_by($conditions, $raw_sql = '') {
    return parent::generic_find_by($conditions, static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param string $id (required)
   * @param string $raw_sql (optional)
   * @return StoneBrands
   **/
  public static function find_by_id($id, $raw_sql = '') {
    return parent::generic_find_by_id($id, static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param array $conditions (required)
   * @param string $raw_sql (optional)
   * @return int
   **/
  public static function count_all_by($conditions, $raw_sql = '') {
    return parent::generic_count_all_by($conditions, static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param array $conditions (required)
   * @param string $raw_sql (optional)
   * @return bool
   **/
  public static function delete_by($conditions, $raw_sql = '') {
    return parent::generic_delete_by($conditions, static::OM_CLASS_NAME, $raw_sql);
  }

}