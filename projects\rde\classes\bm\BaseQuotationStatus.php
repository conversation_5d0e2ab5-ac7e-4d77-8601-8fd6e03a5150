<?php
class BaseQuotationStatus extends AppModel
{
  const DB_NAME = 'rde_b1mo';
  const TABLE_NAME = 'quotation_status';
  const OM_CLASS_NAME = 'QuotationStatus';
  const columns = ['id', 'quotationId', 'statusId', 'insertUserId', 'insertTS'];
  const field_structure = [
    'id'                          => ['type' => 'int', 'length' => '11', 'null' => false],
    'quotationId'                 => ['type' => 'int', 'length' => '11', 'null' => false],
    'statusId'                    => ['type' => 'int', 'length' => '2', 'null' => false],
    'insertUserId'                => ['type' => 'int', 'length' => '3', 'null' => false],
    'insertTS'                    => ['type' => 'datetime', 'length' => '', 'null' => false],
  ];

  protected static $primary_key = ['id'];
  protected $auto_increment = 'id';

  public $id, $quotationId, $statusId, $insertUserId, $insertTS;

  function __construct($obj_arr = [], &$error_codes = []) {
    parent::__construct();
    $this->setDefaults();
    if (!empty($obj_arr)) {
      $this->from_array($obj_arr);
      $this->valid_required_fields($error_codes);
    }
  }

  public function valid(&$error_codes = []) {
    $errors = [];
    foreach(self::columns as $column) {
      if ($column != $this->auto_increment)
      {
        call_user_func_array([$this, 'v_' . $column], [&$errors]);
      }
    }
    if(empty($errors)) {
      return true;
    }
    $error_codes = array_merge($error_codes, $errors);
    return false;
  }

  public function valid_required_fields(&$error_codes = []) {
    $errors = [];
    foreach (self::columns as $column) {
      $function_name = "v_$column";
      if ($column!=$this->auto_increment) {
        call_user_func_array([$this, $function_name], [&$errors]);
      }
    }
    if (empty($errors)) {
      return true;
    }
    $error_codes = array_merge($error_codes, $errors);
    return false;
  }

  public function setDefaults() {
  }



  public function v_id(&$error_codes = []) {
    if (!is_null($this->id) && strlen($this->id) > 0 && self::valid_int($this->id, '11')) {
      return true;
    }
    $error_codes[] = 'id';
    return false;
  }

  public function v_quotationId(&$error_codes = []) {
    if (!is_null($this->quotationId) && strlen($this->quotationId) > 0 && self::valid_int($this->quotationId, '11')) {
      return true;
    }
    $error_codes[] = 'quotationId';
    return false;
  }

  public function v_statusId(&$error_codes = []) {
    if (!is_null($this->statusId) && strlen($this->statusId) > 0 && self::valid_int($this->statusId, '2')) {
      return true;
    }
    $error_codes[] = 'statusId';
    return false;
  }

  public function v_insertUserId(&$error_codes = []) {
    if (!is_null($this->insertUserId) && strlen($this->insertUserId) > 0 && self::valid_int($this->insertUserId, '3')) {
      return true;
    }
    $error_codes[] = 'insertUserId';
    return false;
  }

  public function v_insertTS(&$error_codes = []) {
    if (!is_null($this->insertTS) && strlen($this->insertTS) > 0 && self::valid_datetime($this->insertTS)) {
      return true;
    }
    $error_codes[] = 'insertTS';
    return false;
  }

  /**
  *@param array $conditions (required)
  *@param String $raw_sql (optional)
  *@return QuotationStatus[]
  **/
  public static function find_all_like($conditions, $raw_sql = '') { return parent::generic_find_all_like($conditions, static::OM_CLASS_NAME, $raw_sql); }
  /**
  *@param array $conditions (required)
  *@param String $raw_sql (optional)
  *@return QuotationStatus[]
  **/
  public static function find_all_by($conditions, $raw_sql = '') { return parent::generic_find_all_by($conditions, static::OM_CLASS_NAME, $raw_sql); }
  /**
  *@param String $raw_sql (optional)
  *@return QuotationStatus[]
  **/
  public static function find_all($raw_sql = '') { return parent::generic_find_all(static::OM_CLASS_NAME, $raw_sql); }
  /**
  *@param array $conditions (required)
  *@param String $raw_sql (optional)
  *@return QuotationStatus
  **/
  public static function find_by($conditions, $raw_sql = '') { return parent::generic_find_by($conditions, static::OM_CLASS_NAME, $raw_sql); }
  /**
  *@param String $id (required)
  *@param String $raw_sql (optional)
  *@return QuotationStatus
  **/
  public static function find_by_id($id, $raw_sql = '') { return parent::generic_find_by_id($id, static::OM_CLASS_NAME, $raw_sql); }
  /**
  *@param array $conditions (required)
  *@param String $raw_sql (optional)
  *@return int
  **/
  public static function count_all_by($conditions, $raw_sql = '') { return parent::generic_count_all_by($conditions, static::OM_CLASS_NAME, $raw_sql); }
  /**
  *@param array $conditions (required)
  *@param String $raw_sql (optional)
  *@return bool
  **/
  public static function delete_by($conditions, $raw_sql = '') { return parent::generic_delete_by($conditions, static::OM_CLASS_NAME, $raw_sql); }
}