<section class="title-bar">
  <h1>
    <?php echo Navigation::getItem(Navigation::getActivePageId())->getName() ?>
  </h1>
  <?php TemplateHelper::includePartial("_tabs.php","other") ?>
</section>

<div class="box">

  <form method="post">

    <select name="stat_employee">
      <option value="">Selecteer medewerker...</option>
      <?php foreach($employees as $pe): ?>
        <option value="<?php echo $pe->employeeId ?>" <?php if($pe->employeeId==$_SESSION["stat_employee"]) echo 'selected'; ?>><?php echo $pe->name ?></option>
      <?php endforeach ?>
    </select>

    Melddatum van
    <?php echo getDateSelector("stat_from",$_SESSION["stat_from"]) ?>
    tot
    <?php echo getDateSelector("stat_to",$_SESSION["stat_to"]) ?>

    <input type="submit" value="Zoeken" name="search"/>
  </form>

</div>

<?php if(count($damage_stats)==0): ?>
  <br/>
  Geen items gevonden.
<?php else: ?>
  <table class="default_table">
    <tr class="dataTableHeadingRow">
      <td>Medewerker</td>
      <td>Beschadiging</td>
      <td>Bestelling</td>
      <td>Datum</td>
      <td>Rek</td>
      <td>Verwijder</td>
    </tr>
    <?php foreach($damage_stats as $damage_quot):
      if(!isset($employees[$damage_quot->employeeId])) {
        $employees[$damage_quot->employeeId] = ProductionEmployees::find_by(["employeeId"=>$damage_quot->employeeId]);
      }
      ?>
      <tr class="dataTableRow trhover">
        <td><?php echo $employees[$damage_quot->employeeId]->name ?></td>
        <td><?php echo $damages[$damage_quot->damageId]->name_nl ?></td>
        <td><?php echo $damage_quot->quotation->quotationNumber ?></td>
        <td><?php echo $damage_quot->getInsertTS("Y-m-d H:i") ?></td>
        <td><?php
            $rack = GeneratedRackIds::find_by(["rackId"=>$damage_quot->rackId]);
            echo $rack->rackCode;
          ?></td>
        <td>
          <?php echo BtnHelper::getRemove(reconstructQuery(['action', 'id']) . 'action=damagequotationdelete&qid=' . $damage_quot->id) ?>
        </td>
      </tr>
    <?php endforeach; ?>

  </table>
<?php endif; ?>
