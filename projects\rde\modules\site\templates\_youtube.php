<?php
  $vids = Youtube::getYoutubesAndContentByPageid($page->id, $_SESSION['lang']);
  if (count($vids)>0):
    Youtube::writeStructuredDataScript($vids);
    ?>
    <div id="youtube">
      <?php foreach ($vids as $video): ?>
        <div class="row">
          <div class="col6 col6-s col12-xs" style="padding-bottom: 15px;">
            <div class="vi-lazyload shadow" data-id="<?php echo $video->youtube_vid ?>" data-type="<?php echo $video->type ?>" data-thumb=""></div>
          </div>
          <div class="col6 col6-s col12-xs">
            <h2><?php echo $video->content->title ?></h2>
            <?php echo nl2br($video->content->description); ?>
          </div>
        </div>
      <?php endforeach; ?>
    </div>
    <script>
      videoSeofriendly();
    </script>

  <?php endif;
