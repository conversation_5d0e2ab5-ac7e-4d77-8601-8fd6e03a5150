<?php

  AppModel::loadBaseClass('BaseStoneOrderItemQuotation');

  class StoneOrderItemQuotationModel extends BaseStoneOrderItemQuotation {

    /**
     * @param $itemId
     * @return Quotations[]
     */
    public static function getQuotationsByItem($itemId) {
      $ids = [];
      foreach (StoneOrderItemQuotation::find_all_by(["stone_order_item_id" => $itemId]) as $soid) {
        $ids[] = $soid->quotation_id;
      }
      if (count($ids) == 0) return [];
      return Quotations::find_all_by(["quotationId" => $ids]);
    }

  }