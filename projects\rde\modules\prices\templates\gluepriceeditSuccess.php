<?php TemplateHelper::includePartial('_tabs.php', 'prices'); ?>

<h3>Bewerk lijm prijzen</h3>

<?php writeErrors($form->getErrors(), true); ?>

<form method="post">
  <table class="default_table" style="width: auto;">
    <tr class="dataTableHeadingRow">
      <td>Item</td>
      <td>Instelling</td>
    </tr>
    <tr class="dataTableRow trhover">
      <td>G<PERSON>ig vanaf</td>
      <td>
        01-01-<?php echo $_GET["yearfrom"] ?>
      </td>
    </tr>
  </table>
  <br/>

  <table class="default_table" style="width: auto;">
    <tr class="dataTableHeadingRow">
      <td>Naam</td>
      <td style="text-align: center">Merk</td>
      <td style="text-align: center">Online</td>
      <td>Nieuwe prijs <?php echo showHelpButton("Als dit veld een waarde bevat, betekent dit dat er al een prijs bekend is welke geldig is vanaf deze datum. Je kunt deze prijs hier wijzigen.","Nieuwe prijs") ?></td>
      <td>Huidige prijs <?php echo showHelpButton("Dit is prijs welk op dit moment gebruikt word voor nieuwe offertes.","Huidige prijs") ?></td>
    </tr>
    <?php
      /** @var StoneSizes $item */
      foreach($sizes as $item): ?>
        <tr class="dataTableRow trhover">
          <td><?php echo $item->name ?></td>
          <td><?php echo $brands[$item->brandId]->name ?></td>
          <td style="text-align: center"><?php echo $item->display=="true"?"Ja":"Nee" ?></td>
          <td>€ <?php $form->getElement("price_".$item->sizeId)->render() ?></td>
          <td style="text-align: right"><?php echo StringHelper::asMoney($item->price->price) ?></td>
        </tr>
      <?php endforeach; ?>
  </table>

  <br/>
  <input type="submit" name="go" value="Opslaan" />
  <input type="submit" name="go_list" value="Opslaan en naar lijst" />

</form>
<script type="text/javascript">
  $(document).ready(function () {
    $(".price").focus(function() {
      $(this).select();
    });


    $(".price").change( function() {
      if($(this).val()!="") {
        var val = $(this).val();
        //console.log(val);
        val = decimalNL(val,2);
        $(this).val(val);
      }
    });

  });

</script>
<style>
  input.price,input.buyprice, input.factor, input.currentprice {
    width: 80px;
    text-align: right;
  }
</style>