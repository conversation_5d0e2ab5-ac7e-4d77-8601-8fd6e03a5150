DEZE WORD NOG NIET GEBRUIKT.
AFSCHOTEN DOOR BART.
10-03-2022

<template>
  <div>
    <gsd-select
        name="natuursteenMuurafdekkerModel"
        v-bind:options="models"
        v-bind:value="model"
        v-bind:disabled="disabled"
        no_selection_text="Selecteer uw model..."
        @changeValue="modelChange"
    ></gsd-select>
  </div>
</template>
<script type="text/javascript">

import GsdSelect from "./GsdSelect";

export default {
  components: {
    'gsd-select' : GsdSelect,
  },

  props: ['name', 'sizes', 'sizeId', 'disabled'],
  data() {
    return {
      models_possbile : [
        {
          name: "Ezelsbrug",
          value: "ezelsbrug",
          image: "",
        },
        {
          name: "1 zijdig",
          value: "eenzijdig",
          image: "",
        },
        {
          name: "Plat",
          value: "plat",
          images: "",
        },
      ],
      models : [],
      model : "",
      modelsizes : {},
    }
  },
  mounted() {
    this.fillModelsizes();
  },
  methods :  {
    fillModelsizes() {

      this.modelsizes = {
        "ezelsrug": [],
        "eenzijdig": [],
        "plat": [],
      }

      for (var key in this.sizes) {
        let group = this.sizes[key];
        for (let t = 0; t < group.length ; t++) {
          let size = group[t];
          let option = {
            name: size.name,
            value: size.sizeId,
          };
          if(size.name.indexOf("ezelsrug")!==-1) {
            this.modelsizes["ezelsrug"].push(option);
          }
          else if(size.name.indexOf("eenzijdig")!==-1) {
            this.modelsizes["eenzijdig"].push(option);
          }
          else {
            this.modelsizes["plat"].push(option);
          }
        }
      }

      this.models = [];
      for (let t = 0; t < this.models_possbile.length ; t++) {
        let model = this.models_possbile[t];
        if (this.modelsizes[model.value]) {
          this.models.push(model);
        }
      }

    },
    modelChange(value) {
      this.model = value;
    },
    clickSelection() {
      // event.preventDefault(); //prevent scroll to top
      // this.$emit("changeValue", option.value);
      // this.hide();
    },
  }
}
</script>
