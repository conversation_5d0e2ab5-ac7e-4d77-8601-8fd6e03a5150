<?php

  namespace domain\reviews\service;

  use CustomerRatingEmails;
  use Invoices;
  use Quotations;
  use SandboxUsers;
  use ValidationHelper;

  class PrepareReviews {

    private Invoices $invoice;

    public function __construct(Invoices $invoice) {
      $this->invoice = $invoice;
    }

    /**
     * Set customers that can revieve a review
     * @return int number of added emails
     */
    public function execute(): int {
      $quotations = Quotations::find_all_by(['invoiceId' => $this->invoice->invoiceId]);
      $userIds = [];
      foreach ($quotations as $quotation) {
        $userIds[$quotation->userId] = $quotation->userId;
      }

      $usersToReview = SandboxUsers::find_all_by(["userId" => $userIds]);
      $addedEmails = 0;
      foreach ($usersToReview as $userToReview) {

        //bestaat het emailadres al? Dan overslaan.
        $inDatabase = CustomerRatingEmails::find_all_by(["email" => $userToReview->email]);
        $skip = false;
        foreach ($inDatabase as $cre) {
          if ($cre->inActive == 1) {
            //klant heeft aangegeven geen review emails te willen ontvangen.
            $skip = true;
            break;
          }
          if ($cre->getDateStatusChange("U") > strtotime("-1 YEAR")) {
            //er is al een klant review klaargezet de laatste 12 maanden (even los van of ie verzonden is)
            $skip = true;
            break;
          }
        }

        if ($skip) {
          continue;
        }
        if (!ValidationHelper::isEmail($userToReview->email)) continue;

        $cre = new CustomerRatingEmails();
        $cre->email = $userToReview->email;
        $cre->userId = $userToReview->userId;
        $cre->companyName = $userToReview->companyName;
        $cre->firstname = $userToReview->firstName;
        $cre->lastname = $userToReview->lastName;
        $cre->dateStatusChange = date("Y-m-d H:i:s");
        //$cre->dateStatusChange = $this->invoice->dateInvoice." 00:00:00";
        $cre->randomCode = $this->createRandomCode();
        $cre->save();

        $addedEmails++;
      }

      return $addedEmails;

    }

    /**
     * Copy from legacy rde
     * @return string
     */
    private function createRandomCode(): string {
      $chars = "abcdefghijkmnopqrstuvwxyz023456789";
      srand((int)(microtime(true) * 1000000));
      $i = 0;
      $pass = '';
      while ($i <= 50) {
        $num = rand() % 33;
        $tmp = substr($chars, $num, 1);
        $pass = $pass . $tmp;
        $i++;
      }
      return $pass;
    }

  }