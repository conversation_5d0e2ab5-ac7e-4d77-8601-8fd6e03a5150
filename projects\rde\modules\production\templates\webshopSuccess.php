<?php TemplateHelper::includePartial("_tabs.php",'production') ?>
<h2>Webshop</h2>

<table class="default_table">
  <tr class="dataTableHeadingRow row_head td-borders">
    <td>Positie</td>
    <td>Naam</td>
    <td>Productiedatum</td>
<!--    <td>quotationId</td>-->
    <td>Status</td>
    <td>Offerte nummer</td>
    <td>Acties</td>
  </tr>

  <?php
    /** @var Quotations $quotation */
    foreach ($quotations as $key => $quotation): ?>
  <?php if ($quotation->statusId != Status::STATUS_CHECKED) continue; ?>
  <tr class="dataTableRow trhover">
    <td><?php echo $key ?></td>
    <td><?php echo $quotation->name ?></td>
    <td><?php echo $quotation->getProductionDate() ?></td>
<!--    <td>--><?php //echo $quotation->quotationId ?><!--</td>-->
    <td><?php echo $quotation->status ?></td>
    <td><?php echo $quotation->quotation_link ?></td>
    <td><?php echo BtnHelper::getOpenPage('?action=webshopOptions&quotationId=' . $quotation->quotationId, '', '') ?></td>
  </tr>
  <?php endforeach; ?>
</table>
<br/>
<table class="default_table">
  <tr class="dataTableHeadingRow row_head td-borders">
    <td>Positie</td>
    <td>Naam</td>
    <td>Productiedatum</td>
<!--    <td>quotationId</td>-->
    <td>Status</td>
    <td>Offerte nummer</td>
    <td>Acties</td>
  </tr>

  <?php
    /** @var Quotations $quotation */
    foreach ($quotations as $key => $quotation): ?>
  <?php if ($quotation->statusId == Status::STATUS_CHECKED) continue; ?>
      <tr class="dataTableRow trhover">
        <td><?php echo $key ?></td>
        <td><?php echo $quotation->name ?></td>
        <td><?php echo $quotation->getProductionDate() ?></td>
<!--        <td>--><?php //echo $quotation->quotationId ?><!--</td>-->
        <td><?php echo $quotation->status ?></td>
        <td><?php echo $quotation->quotation_link ?></td>
        <td><?php echo BtnHelper::getOpenPage('?action=webshopOptions&quotationId=' . $quotation->quotationId, '', '') ?></td>
      </tr>
    <?php endforeach; ?>
</table>
