<?php

  class quotationComponents extends gsdComponents {

    public function executeQuotationlist() {

      if (!isset($this->limit)) {
        $this->pager = new Pager();
        $this->pager->rowsPerPage = 50;
        $this->pager->maxPageLink = 18;
        $this->pager->handle();
      }

      $filt = "AND ( ";
      $filt .= "quotations.userId=" . $_SESSION['userObject']->userId . " ";
      if ($_SESSION['userObject']->companyId != "") {
        $filt .= " OR ";
        $filt .= " quotations.companyId=" . $_SESSION['userObject']->companyId . " ";
      }
      $filt .= ") ";
      if (isset($_SESSION["tender_search"]) && $_SESSION["tender_search"] != '') {
        $search = escapeForDB($_SESSION["tender_search"]);
        $filt .= " AND ( ";
        $filt .= " quotations.quotationNumber LIKE '%" . $search . "%' ";
        $filt .= " OR (CONCAT(quotations.quotationNumber, '-', quotations.quotationVersion) LIKE '%" . $search . "%') ";
        $filt .= " OR quotations.projectName LIKE '%" . $search . "%' ";
        $filt .= " OR quotations.projectReference LIKE '%" . $search . "%' ";
        $filt .= ") ";
      }

      if (isset($this->pager)) {
        $this->pager->count = Quotations::count_all_by(["flaggedForDeletion" => 0], $filt);
      }

      $filt .= "ORDER BY quotationDate DESC, quotationNumber DESC, quotationVersion DESC, quotationId DESC ";
      if (isset($this->limit)) {
        $filt .= "LIMIT " . $this->limit;
      }
      else {
        $filt .= $this->pager->getLimitQuery();
      }

      $quotations = AppModel::mapObjectIds(Quotations::find_all_by(["flaggedForDeletion" => 0], $filt), "quotationId");

      $users = [];
      $users[$_SESSION["userObject"]->userId] = $_SESSION["userObject"];
      $quotids = [];
      foreach ($quotations as $quotation) {
        if (!isset($users[$quotation->userId])) {
          $users[$quotation->userId] = SandboxUsers::find_by(["userId" => $quotation->userId]);
        }
        if ($quotation->statusId >= 20) {
          $quotids[] = $quotation->quotationId;
        }
      }

      $routeids = [];
      if (count($quotids) > 0) {
        foreach (GpsbuddyRde::find_all_by(["quotationId" => $quotids]) as $gps_rde) {
          $routeids[$gps_rde->routeId] = $gps_rde->routeId;
          $quotations[$gps_rde->quotationId]->routeId = $gps_rde->routeId;
        }

        if (count($routeids) > 0) {
          $routes = AppModel::mapObjectIds(GpsbuddyRoutes::find_all_by(["routeId" => $routeids]), "routeId");
          foreach ($quotations as $quotation) {
            if (isset($quotation->routeId)) {
              $quotation->route = $routes[$quotation->routeId];
            }
          }
        }
      }

      $this->quotations = $quotations;
      $this->users = $users;
      $this->site = Context::getSite();
    }

  }
