<section class="title-bar">
  <h1>
    <?php echo Navigation::getItem(Navigation::getActivePageId())->getName() ?>
  </h1>
  <?php TemplateHelper::includePartial('_tabs.php', 'other'); ?>
  <?php TemplateHelper::includePartial('_tabs.php','planning'); ?>
</section>

  <form name="workerlist" action="<?php echo reconstructQueryAdd(['pageId']) ?>" method="post">
    <div class="box">
      Filters:
      <input type="text" name="p_search" value="<?php echo $_SESSION['p_search']?>" style="width: 130px;" placeholder="Zoeken..."/>
      <select name="p_searchuser" id="p_searchuser">
        <option value="">Alle werknemers...</option>
        <?php foreach($selectable_workers as $worker): ?>
          <option value="<?php echo $worker->id ?>" <?php if($worker->id==$_SESSION['p_searchuser']) echo 'selected'?>><?php echo $worker->getNaam() ?></option>
        <?php endforeach ?>
      </select>
      <select name="p_searchgroup" id="p_searchgroup">
        <option value="">Alle groepen...</option>
        <?php foreach(WorkerGroup::getAll() as $workergr): ?>
          <option value="<?php echo $workergr->id ?>" <?php if($workergr->id==$_SESSION['p_searchgroup']) echo 'selected'?>><?php echo $workergr->name ?></option>
        <?php endforeach ?>
      </select>
      Van: <?php echo getDateSelector('p_searchin', $_SESSION['p_searchin'], false); ?>
      t/m: <?php echo getDateSelector('p_searchout', $_SESSION['p_searchout'], false); ?>

      <input type="submit" name="go" id="go" value="Zoeken" />
    </div>
    <div class="box">
      Acties:
      <a href="?action=edit" class="gsd-btn gsd-btn-primary">Toevoegen nieuw planuur</a>
      <a href="?action=batch" class="gsd-btn gsd-btn-primary">Toevoegen nieuwe planuren batch</a>
      <input type="button" value="Selecteer alles" name="selectall" id="selectall" />
      <input type="submit" value="Verwijder geselecteerde planuren" title="Verwijder selectie" data-gsd-text="Weet u zeker dat u de geselecteerde items wilt verwijderen?" name="delall" id="delall" class="gsd-confirm" />
    </div>

    <?php $pager->writePreviousNext(); ?>

    <?php if(count($hours)==0): ?>
      <br/>Er zijn geen planuren gevonden.
    <?php else: ?>
      <table class="default_table">
        <tr class="dataTableHeadingRow">
          <td style="width: 15px;"></td>
          <td>Naam</td>
          <td style="width: 100px;">Datum</td>
          <td style="width: 100px;text-align: right">Uren</td>
          <td style="width: 100px;text-align: right">Verlof</td>
          <td style="width: 50px;">Bewerk</td>
          <td style="width: 50px;">Verwijder</td>
        </tr>
        <?php
        $sum = 0;
        foreach($hours as $item):
          $sum += $item->hours; ?>
          <tr class="dataTableRow trhover">
            <td><input type="checkbox" value="<?php echo $item->id ?>" name="selected[]" class="hour_sel" <?php if(isset($_POST['selected']) && in_array($item->id,$_POST['selected'])) echo 'checked' ?> /></td>
            <td><a href="?action=edit&id=<?php echo $item->id ?>"><?php echo $workers[$item->worker_id]->getNaam() ?></a></td>
            <td><?php echo $item->getDate() ?></td>
            <td style="text-align: right"><?php echo $item->hours ?></td>
            <td style="color: red;text-align: right"><?php if(isset($item->verlof)) echo number_format(-1*$item->verlof,2);?></td>
            <td>
              <?php echo BtnHelper::getEdit('?action=edit&id='.$item->id) ?>
            </td>
            <td>
              <?php echo BtnHelper::getRemove('?action=delete&delid=' . $item->id, $workers[$item->worker_id]->getNaam().' - '. $item->getDate()) ?>
            </td>
          </tr>
        <?php endforeach; ?>
        <tr class="dataTableHeadingRow topborder nobottomborder">
          <td></td>
          <td colspan="2" style="text-align: right"></td>
          <td style="text-align: right"><?php echo number_format($sum,2) ?></td>
          <td></td>
          <td></td>
          <td></td>
        </tr>
      </table>
    <?php endif; ?>
  </form>

  <script type="text/javascript">
    $(document).ready(function() {

      $("#selectall").click(function() {
        $(".hour_sel").each(function() {
          $(this).prop("checked",!$(this).is(":checked"));
        });
      });
      $("#sp_search").focus();

      $("#p_searchuser,#p_searchgroup").change(function() {
        $("#go").click();
      });

    });
  </script>