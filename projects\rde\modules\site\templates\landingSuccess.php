<script src='https://www.google.com/recaptcha/api.js' async defer></script>

<div class="contenttxt">
  <?php echo process_text($page->content->content1); ?>
</div>  
<?php TemplateHelper::includePartial("_pagefiles.php",'site',array('site'=>$site,'pagefiles'=>$pagefiles,'page'=>$page)) ?>
<?php TemplateHelper::includePartial("_pageimages.php",'site',array('site'=>$site,'images'=>$images,'page'=>$page)) ?>
<?php TemplateHelper::includePartial("_youtube.php",'site',array('page'=>$page)) ?>
<?php echo Tag::getTagsString($page, $_SESSION['lang']) ?>

<div class="link-structuur">
  <h3>Anderen bekeken ook:</h3>
  <ul>
    <?php
      $itemsF = [];
      foreach (Navigation::getItem(278)->getChildren() as $item):
        if(!($item->pageId >= 278 && $item->pageId <= 285) || $item->pageId == $pageId)
          continue;
        $itemsF[] = $item;
      endforeach;
      shuffle($itemsF);
      $itemsF = array_slice($itemsF, 0, 5);
      foreach ($itemsF as $item): ?>
        <li><a href="<?php echo $item->page->getUrl() ?>" title="<?php echo escapeForInput($item->getName()) ?>"><?php echo $item->getName() ?></a></li>
      <?php endforeach; ?>
  </ul>
</div>

<div id="contact">
  <h2>Neem nu contact op</h2>
  <br/>
  <form id="form1" method="post">
    <?php echo writeErrors($errors); ?>
    <div id="form" class="full">
      <div id="details">
        <div class="box-field half left half-left">
          <input class="required full" name="naam" value="<?php writeIfSet("naam") ?>" placeholder="Naam *" type="text"/>
        </div>
        <div class="box-field half half-right">
          <input class="required full" name="bedrijfsnaam" value="<?php writeIfSet("bedrijfsnaam") ?>" placeholder="Bedrijfsnaam *" type="text"/>
        </div>
        <div class="box-field half left half-left">
          <input class="required full" name="email" value="<?php writeIfSet("email") ?>" placeholder="E-mail *" type="email"/>
        </div>
        <div class="box-field half half-right">
          <input class="required full" name="telefoonnummer" value="<?php writeIfSet("telefoonnummer") ?>" placeholder="Telefoonnummer *" type="text"/>
        </div>
      </div>
      <div id="message">
        <div class="box-field">
          <textarea name="vraag" class="required full" placeholder="Bericht *"><?php writeIfSet("vraag") ?></textarea>
        </div>
        <div class="box-field">
          <div class="g-recaptcha" data-sitekey="<?php echo Config::get("GOOGLE_RECAPTCHA")["public_key"] ?>" data-callback="onSubmit"></div>
        </div>
        <div class="box-field">
          <button class="send-submit" name="go" type="submit">Verzenden</button>
        </div>
        <input name="keyid" value="200012747A" type="hidden"/>
        <p class="foot-note">Velden met * zijn verplicht</p>
      </div>
    </div>
  </form>
</div>
<script type="text/javascript">

  jQuery.validator.messages.required = "";

  (function (jQuery) {
    jQuery("form#form1").validate();
  })(jQuery);

</script>

<script>
  //<!-- TK 3.5.2019 -->
  campagneId1 = '200012747A';
  key = '4181C22196DB5237170835984E12827262354242';
  folder = 'https://website2.skl1142601.interip.nl/';
  ip = '';
  bt = navigator.appName;
  bv = navigator.appVersion;
  bc = navigator.appCodeName;
  ua = navigator.userAgent;
  ref = escape(document.referrer);
  pa = escape(window.location);
  pp = '';
  pl = navigator.platform;
  jt = navigator.javaEnabled();
  sr = screen.width + 'x' + screen.height;
  sc = screen.colorDepth;
  url = folder + 'teller.aspx?campagneId=' + campagneId1 + '&pa=' + pa + '&bt=' + bt + '&bv=' + bv + '&ip=' + ip + '&ua=' + ua + '&ref=' + ref;
  ifrm = document.createElement('IFRAME');
  ifrm.setAttribute('src', url);
  ifrm.setAttribute('id', 'ifrm');
  ifrm.setAttribute('frameBorder', '0');
  ifrm.style.width = 0+'px';
  ifrm.style.height = 0+'px';
  ifrm.style.display = 'none';
  document.body.appendChild(ifrm);
</script>

