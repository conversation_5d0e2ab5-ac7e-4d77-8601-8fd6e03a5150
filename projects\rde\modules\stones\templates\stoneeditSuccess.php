<?php TemplateHelper::includePartial('_tabs.php', 'stones'); ?>

<h3>Bewerk steen</h3>

<?php writeErrors($form->getErrors(), true); ?>

<?php $form->renderAsTable(true,true) ?>

<script type="text/javascript">
  $(document).ready(function() {

    new SimpleLightbox("a.image_viewbtn", {
      fileExt: false,
    });

    $("#alert_txt").click(function(e) {
      e.preventDefault();
      $("#alert").val("Indien dit model niet voorradig is, houd dan rekening met een langere levertijd en instelkosten.\nNeem voor meer informatie contact op met HeBlad.");
    });

    $("#alert_empty").click(function(e) {
      e.preventDefault();
      $("#alert").val("");
    });

    var colorOptions = $($("#colorId").html());
    var sizeOptions = $($("#sizeId").html());

    $("#brandId").change(function() {

      var brandId = $(this).val();

      //color
      var found = false;
      var colorEl = $("#colorId");
      var selected  = colorEl.val();
      colorEl.html("");
      colorOptions.each(function(){
        var option = $(this);
        var optionvalue = option.attr("value");
        if(optionvalue=="" || option.attr("data-brandId")==brandId) {
          colorEl.append(option);
          if(optionvalue==selected) {
            found = true;
          }
        }
      })
      if(!found) {
        colorEl.val("");
      }

      found = false;
      var sizeEl = $("#sizeId");
      selected  = sizeEl.val();
      sizeEl.html("");
      sizeOptions.each(function(){
        var option = $(this);
        var optionvalue = option.attr("value");
        if(optionvalue=="" || option.attr("data-brandId")==brandId) {
          sizeEl.append(option);
          if(optionvalue==selected) {
            found = true;
          }
        }
      })
      if(!found) {
        sizeEl.val("");
      }

    });

    $("#brandId").change();


  });
</script>