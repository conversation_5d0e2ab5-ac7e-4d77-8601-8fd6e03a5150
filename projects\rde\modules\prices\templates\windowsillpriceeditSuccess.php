<?php TemplateHelper::includePartial('_tabs.php', 'prices'); ?>

<h3>Bewerk ventsterbank prijzen</h3>

Het leegmaken van de meterprijs zal de prijs verwijderen uit de database.<br/><br/>

<?php writeErrors($form->getErrors(), true); ?>

<form method="post">
  <table class="default_table" style="width: auto;">
    <tr class="dataTableHeadingRow">
      <td>Item</td>
      <td>Instelling</td>
    </tr>
    <tr class="dataTableRow trhover">
      <td>Merk</td>
      <td><?php echo $brands[$_SESSION['sp_brand']]->name ?></td>
    </tr>
    <?php if(count($colors)>0): ?>
    <tr class="dataTableRow trhover">
      <td><PERSON><PERSON>uren</td>
      <td>
        <?php
          $colornames = [];
          foreach($colors as $color):
            $colornames[] = $color->name;
          endforeach;
          echo implode(", ", $colornames);
        ?>
      </td>
    </tr>
    <?php endif; ?>
    <tr class="dataTableRow trhover">
      <td>Geldig vanaf</td>
      <td>
        01-01-<?php echo $_GET["yearfrom"] ?>
      </td>
    </tr>
  </table>
  <br/>

  <table class="default_table" style="width: auto;">
    <tr class="dataTableHeadingRow">
      <td>Naam</td>
      <td style="text-align: center">Online</td>
      <td>Huidige<br/>meter prijs</td>
      <td style="width: 100px">Meter prijs <?php echo showHelpButton("Als dit veld een waarde bevat, betekent dit dat er al een prijs bekend is welke geldig is vanaf deze datum. Je kunt deze prijs hier wijzigen.") ?></td>
      <td>Huidige<br/>m2 prijs</td>
      <td style="width: 100px">m2<br/>prijs <?php echo showHelpButton("Als dit veld een waarde bevat, betekent dit dat er al een prijs bekend is welke geldig is vanaf deze datum. Je kunt deze prijs hier wijzigen.") ?></td>
      <td>Huidige<br/>per stuk</td>
      <td style="width: 100px">Meerprijs<br/>per stuk <?php echo showHelpButton("Als dit veld een waarde bevat, betekent dit dat er al een prijs bekend is welke geldig is vanaf deze datum. Je kunt deze prijs hier wijzigen.") ?></td>
      <td>Huidige<br/>zaagdeel<br/>opslag</td>
      <td style="width: 100px">Meerprijs<br/>per zaagdeel <?php echo showHelpButton("Meerprijs voor 1 zaagdeel. Als dit veld een waarde bevat, betekent dit dat er al een prijs bekend is welke geldig is vanaf deze datum. Je kunt deze prijs hier wijzigen.") ?></td>
      <td style="width: 100px">Kopie <?php echo showHelpButton("Kopieër huidige prijs naar deze prijs.") ?></td>
    </tr>
    <?php
      /** @var Stones $item */
      foreach($stones as $item): ?>
        <tr class="dataTableRow trhover">
          <td><?php echo $item->name ?></td>
          <td style="text-align: center"><?php echo $item->display=="true"?"Ja":"Nee" ?></td>
          <td style="text-align: right"><?php echo StringHelper::asMoney($item->price->price) ?></td>
          <td>€ <?php $form->getElement("price_".$item->stoneId)->addAtribute("data-prev-value",$item->price->price)->render() ?></td>
          <td style="text-align: right"><?php echo StringHelper::asMoney($item->price->price_m2) ?></td>
          <td>€ <?php $form->getElement("price_m2_".$item->stoneId)->addAtribute("data-prev-value",$item->price->price_m2)->render() ?></td>
          <td style="text-align: right"><?php echo StringHelper::asMoney($item->price->price_piece) ?></td>
          <td>€ <?php $form->getElement("price_piece_".$item->stoneId)->addAtribute("data-prev-value",$item->price->price_piece)->render() ?></td>
          <td style="text-align: right"><?php echo StringHelper::asMoney($item->price->price_mitre) ?></td>
          <td>€ <?php $form->getElement("price_mitre_".$item->stoneId)->addAtribute("data-prev-value",$item->price->price_mitre)->render() ?></td>
          <td><?php echo BtnHelper::getCopy("#")->addClass("copyprice")->removeClass("gsd-delete") ?></td>
        </tr>
      <?php endforeach; ?>
  </table>

  <br/>
  <input type="submit" name="go" value="Opslaan" />
  <input type="submit" name="go_list" value="Opslaan en naar lijst" />

</form>
<script type="text/javascript">
  $(document).ready(function () {
    $(".price").change( function() {
      if($(this).val()!="") {
        var val = $(this).val();
        //console.log(val);
        val = decimalNL(val,2);
        $(this).val(val);
      }
    }).focus(function() {
      $(this).select();
    });

    $(".copyprice").on("click", function() {
      let inputs = $(this).parent().parent().find("input");
      $.each(inputs, function(){
        $(this).val($(this).attr("data-prev-value"));
      });
    });

  });

</script>
<style>
  input.price,input.buyprice, input.factor, input.currentprice {
    width: 80px;
    text-align: right;
  }
</style>