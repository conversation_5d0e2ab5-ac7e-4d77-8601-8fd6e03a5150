  <section>

    <?php if(isset($browserversion_message) && $browserversion_message) TemplateHelper::includePartial("_browserversion.php",'quotation'); ?>
    <?php TemplateHelper::includePartial("_prepaid.php",'quotation'); ?>

    <?php foreach($imessageshome as $imessagehome): ?>
      <div class="contenttxt" id="mainnews">
        <?php echo $imessagehome->message; $imessagehome->setRead($_SESSION["userObject"]->userId)?>
      </div>
    <?php endforeach; ?>

    <div class="contenttxt">
      <h1>Welkom <?php echo $_SESSION['userObject']->getNaam(); ?></h1>
      Met onze product configurator kunt u in een mum van tijd een offerte voor uw elementen samenstellen, en een bestelling plaatsen. Deze vrijblijvende offerte wordt u vervolgens per e-mail toegezonden.
      <br/>
      <br/>
      <a href="?action=wizard" class="btn btn-primary" id="new_offer"><i class="icon-calculator"></i> START NIEUWE CONFIGURATIE</a>
      <?php if(!$_SESSION['userObject']->hasCompany()): ?>
        <br/><br/>
        Vooralsnog bent u niet geregistreerd als bedrijf. Wilt u in aanmerking komen voor onze speciale kortingen, mail dan uw gegevens met KVK <NAME_EMAIL>.
      <?php endif; ?>
      <br/><br/>
      <?php
        $_SESSION["tender_search"] = '';
        TemplateHelper::includeComponent("quotationlist","quotation",["limit"=>10,"showhistorylink"=>true])
      ?>
    </div>
  </section>


  <script type="text/javascript">

    $(document).ready( function() {
      <?php if(isset($_SESSION["wizard"]['quotation'])): ?>

        $("#new_offer").click (function(e) {
          e.preventDefault();
          swal({
            title: 'Open offerte',
            html: "U heeft een offerte open die nog niet is opgeslagen.<Br/>Wilt u verder gaan met de open offerte, of een nieuwe offerte aanmaken?",
            type: 'question',
            showCancelButton: true,
            confirmButtonColor: '#3085d6',
            cancelButtonColor: '#d33',
            confirmButtonText: 'Verder met open offerte',
            cancelButtonText: 'Nieuwe offerte starten'
          }).then(function(result){
            if (result.value) {
              location.href=$("#new_offer").attr("href");
            }
            else if (result.dismiss) {
              location.href=$("#new_offer").attr("href")+"&clear=true";
            }
          }).catch(swal.noop);
        });
      <?php endif; ?>

    });

  </script>
