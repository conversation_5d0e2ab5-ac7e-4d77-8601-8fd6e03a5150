<?php
class BaseQuotationsExtra extends AppModel
{
  const DB_NAME = 'rde_b1mo';
  const TABLE_NAME = 'quotations_extra';
  const OM_CLASS_NAME = 'QuotationsExtra';
  const columns = ['id', 'quotationId', 'maxMeasureElement', 'seamColorId', 'rackContainerMark', 'stoneOrdered', 'stoneOrderedDate', 'stoneDeliveryDate', 'callToDelivery', 'executorTicket', 'rackContainerReturn', 'totalLeftEndStones', 'totalRightEndStones', 'totalLeftEndStonesGrooves', 'totalRightEndStonesGrooves', 'totalMiddlesStones', 'addressDeliveryId', 'supplier', 'addressSplit', 'addressSplitInfo', 'carrierId', 'quoteInvoiceAlertFlag', 'quoteInvoiceAlertInfo', 'quotationAltPriceYear', 'stoneSizeWidth', 'wall_thickness', 'prod_cm_per_hour', 'prod_employee_id', 'customerSplitRights', 'supplier_show', 'sms', 'sms_delivered', 'smsnumber'];
  const field_structure = [
    'id'                          => ['type' => 'int', 'length' => '11', 'null' => false],
    'quotationId'                 => ['type' => 'int', 'length' => '11', 'null' => false],
    'maxMeasureElement'           => ['type' => 'int', 'length' => '11', 'null' => false],
    'seamColorId'                 => ['type' => 'int', 'length' => '11', 'null' => false],
    'rackContainerMark'           => ['type' => 'varchar', 'length' => '10', 'null' => true],
    'stoneOrdered'                => ['type' => 'boolean', 'length' => '1', 'null' => false],
    'stoneOrderedDate'            => ['type' => 'date', 'length' => '', 'null' => true],
    'stoneDeliveryDate'           => ['type' => 'date', 'length' => '', 'null' => true],
    'callToDelivery'              => ['type' => 'boolean', 'length' => '1', 'null' => false],
    'executorTicket'              => ['type' => 'boolean', 'length' => '1', 'null' => false],
    'rackContainerReturn'         => ['type' => 'boolean', 'length' => '1', 'null' => false],
    'totalLeftEndStones'          => ['type' => 'int', 'length' => '11', 'null' => false],
    'totalRightEndStones'         => ['type' => 'int', 'length' => '11', 'null' => false],
    'totalLeftEndStonesGrooves'   => ['type' => 'int', 'length' => '11', 'null' => false],
    'totalRightEndStonesGrooves'  => ['type' => 'int', 'length' => '11', 'null' => false],
    'totalMiddlesStones'          => ['type' => 'int', 'length' => '11', 'null' => false],
    'addressDeliveryId'           => ['type' => 'int', 'length' => '11', 'null' => true],
    'supplier'                    => ['type' => 'varchar', 'length' => '255', 'null' => true],
    'addressSplit'                => ['type' => 'varchar', 'length' => '10', 'null' => true],
    'addressSplitInfo'            => ['type' => 'varchar', 'length' => '70', 'null' => true],
    'carrierId'                   => ['type' => 'int', 'length' => '11', 'null' => false],
    'quoteInvoiceAlertFlag'       => ['type' => 'boolean', 'length' => '1', 'null' => false],
    'quoteInvoiceAlertInfo'       => ['type' => 'varchar', 'length' => '255', 'null' => true],
    'quotationAltPriceYear'       => ['type' => 'date', 'length' => '', 'null' => true],
    'stoneSizeWidth'              => ['type' => 'decimal', 'length' => '4,2', 'null' => true],
    'wall_thickness'              => ['type' => 'smallint', 'length' => '3', 'null' => true],
    'prod_cm_per_hour'            => ['type' => 'mediumint', 'length' => '8', 'null' => true],
    'prod_employee_id'            => ['type' => 'int', 'length' => '11', 'null' => true],
    'customerSplitRights'         => ['type' => 'boolean', 'length' => '1', 'null' => false],
    'supplier_show'               => ['type' => 'datetime', 'length' => '', 'null' => true],
    'sms'                         => ['type' => 'boolean', 'length' => '1', 'null' => false],
    'sms_delivered'               => ['type' => 'boolean', 'length' => '1', 'null' => false],
    'smsnumber'                   => ['type' => 'varchar', 'length' => '30', 'null' => true],
  ];

  protected static $primary_key = ['id'];
  protected $auto_increment = 'id';

  public $id, $quotationId, $maxMeasureElement, $seamColorId, $rackContainerMark, $stoneOrdered, $stoneOrderedDate, $stoneDeliveryDate, $callToDelivery, $executorTicket, $rackContainerReturn, $totalLeftEndStones, $totalRightEndStones, $totalLeftEndStonesGrooves, $totalRightEndStonesGrooves, $totalMiddlesStones, $addressDeliveryId, $supplier, $addressSplit, $addressSplitInfo, $carrierId, $quoteInvoiceAlertFlag, $quoteInvoiceAlertInfo, $quotationAltPriceYear, $stoneSizeWidth, $wall_thickness, $prod_cm_per_hour, $prod_employee_id, $customerSplitRights, $supplier_show, $sms, $sms_delivered, $smsnumber;

  function __construct($obj_arr = [], &$error_codes = []) {
    parent::__construct();
    $this->setDefaults();
    if (!empty($obj_arr)) {
      $this->from_array($obj_arr);
      $this->valid_required_fields($error_codes);
    }
  }

  /**
  * Set default object values
  **/
  public function setDefaults() {
    $this->stoneOrdered = 0;
    $this->callToDelivery = 0;
    $this->executorTicket = 0;
    $this->rackContainerReturn = 0;
    $this->totalLeftEndStones = 0;
    $this->totalRightEndStones = 0;
    $this->totalLeftEndStonesGrooves = 0;
    $this->totalRightEndStonesGrooves = 0;
    $this->totalMiddlesStones = 0;
    $this->carrierId = 0;
    $this->quoteInvoiceAlertFlag = 0;
    $this->customerSplitRights = 0;
    $this->sms = 0;
    $this->sms_delivered = 0;
  }

  /**
   * @param array $conditions (required)
   * @param string $raw_sql (optional)
   * @return QuotationsExtra[]
   **/
  public static function find_all_like($conditions, $raw_sql = '') {
    return parent::generic_find_all_like($conditions, static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param array $conditions (required)
   * @param string $raw_sql (optional)
   * @return QuotationsExtra[]
   **/
  public static function find_all_by($conditions, $raw_sql = '') {
    return parent::generic_find_all_by($conditions, static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param string $raw_sql (optional)
   * @return QuotationsExtra[]
   **/
  public static function find_all($raw_sql = '') {
    return parent::generic_find_all(static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param array $conditions (required)
   * @param string $raw_sql (optional)
   * @return QuotationsExtra
   **/
  public static function find_by($conditions, $raw_sql = '') {
    return parent::generic_find_by($conditions, static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param string $id (required)
   * @param string $raw_sql (optional)
   * @return QuotationsExtra
   **/
  public static function find_by_id($id, $raw_sql = '') {
    return parent::generic_find_by_id($id, static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param array $conditions (required)
   * @param string $raw_sql (optional)
   * @return int
   **/
  public static function count_all_by($conditions, $raw_sql = '') {
    return parent::generic_count_all_by($conditions, static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param array $conditions (required)
   * @param string $raw_sql (optional)
   * @return bool
   **/
  public static function delete_by($conditions, $raw_sql = '') {
    return parent::generic_delete_by($conditions, static::OM_CLASS_NAME, $raw_sql);
  }

}