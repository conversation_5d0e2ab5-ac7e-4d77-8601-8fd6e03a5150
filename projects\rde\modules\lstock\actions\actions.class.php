<?php

  class lstockRdeActions extends gsdActions {

    public function executeList() {
      //pager properties
      $this->pager = new Pager();
      $this->pager->setWriteCount(true);
      $this->pager->handle();
      //einde pager props

      if (!isset($_SESSION['st_search'])) $_SESSION['st_search'] = '';
      if (!isset($_SESSION['st_brand'])) $_SESSION['st_brand'] = '';
      if (isset($_POST['st_search'])) $_SESSION['st_search'] = trim($_POST['st_search']);
      if (isset($_POST['st_brand'])) $_SESSION['st_brand'] = trim($_POST['st_brand']);

      $query = "SELECT * FROM " . Stones::getTablename() . " ";
      $filt = " JOIN " . StoneColors::getTablename() . " ON stones.colorId=stone_colors.colorId ";
      $filt .= " WHERE stones.endstone!='rightg' "; //rightg word niet gebruikt voor voorraad
      if (!empty($_SESSION['st_search'])) {
        $searchStr = DbHelper::escape($_SESSION['st_search']);
        $filt .= " AND (";
        $filt .= "stone_colors.short LIKE '%" . $searchStr . "%' OR ";
        $filt .= "stone_colors.name LIKE '%" . $searchStr . "%' OR ";
        $filt .= "stones.short LIKE '%" . $searchStr . "%' OR ";
        $filt .= "stones.name LIKE '%" . $searchStr . "%' ";
        $filt .= ")";
      }
      if ($_SESSION['st_brand'] != "") {
        $filt .= " AND stone_colors.brandId = " . $_SESSION['st_brand'] . " ";
      }

      $count = "SELECT COUNT(stones.stoneId) AS count FROM " . Stones::getTablename() . " " . $filt;
      $result = DBConn::db_link()->query($count);
      $row = $result->fetch_assoc();
      if ($row) {
        $this->pager->count = $row['count'];
      }
      $filt .= 'ORDER BY stone_colors.name';
      $filt .= $this->pager->getLimitQuery();

      $result = DBConn::db_link()->query($query . $filt);
      $stones = [];
      $stoneIds = [];
      while ($row = $result->fetch_row()) {
        $stone = new Stones();
        $stone->hydrate($row);
        $stone->from_db = true;
        $color = new StoneColors();
        $color->hydrate($row, count(Stones::columns));
        $color->from_db = true;
        $stone->color = $color;
        $stones[] = $stone;
        $stoneIds[] = $stone->stoneId;
      }

      if (isset($_POST["go"]) && isset($_POST["stock"])) {
        $stones = AppModel::mapObjectIds($stones, "stoneId");
        foreach ($_POST["stock"] as $id => $value) {
          if (isset($stones[$id]) && $stones[$id]->stock != $value) {
            $stones[$id]->changeStock($value, false);
            $stones[$id]->save();
          }
        }
        $_SESSION['flash_message'] = "Voorraad opgeslagen.";
        ResponseHelper::redirect(reconstructQuery());
      }

      $this->alreadyOrdered = StoneOrderItem::getOrderedNotDelivered();
      $this->stonebrands = StoneBrands::getBrands();
      $this->stones = $stones;

    }

    public function executeOrderlist() {
      //pager properties
      $this->pager = new Pager();
      $this->pager->setWriteCount(true);
      $this->pager->handle();
      //einde pager props

      if (!isset($_SESSION['st_search'])) $_SESSION['st_search'] = '';
      if (!isset($_SESSION['st_brand'])) $_SESSION['st_brand'] = '';
      if (!isset($_SESSION['st_shortage'])) $_SESSION['st_shortage'] = 1;
      if (!isset($_SESSION['st_hasorder'])) $_SESSION['st_hasorder'] = 0;

      if (isset($_POST['st_search'])) {
        $_SESSION['st_search'] = trim($_POST['st_search']);
        $_SESSION['st_brand'] = trim($_POST['st_brand']);
        $_SESSION['st_shortage'] = isset($_POST['st_shortage']) ? 1 : 0;
        $_SESSION['st_hasorder'] = isset($_POST['st_hasorder']) ? 1 : 0;
        ResponseHelper::redirect(reconstructQuery());
      }

      if (isset($_POST['order'])) { //op bestellijst

        $stoneorder = StoneOrder::find_by(["brand_id" => $_POST['brandId'], "status" => "new"]);
        if (!$stoneorder) {
          $stoneorder = new StoneOrder();
          $stoneorder->brand_id = $_POST['brandId'];
          $stoneorder->save();
        }

        foreach ($_POST["stock"] as $stoneId => $toOrder) {
          $stone = Stones::find_by(["stoneId" => $stoneId]);
          if ($toOrder > 0 || ($stone->endstone == 'false' && isset($_POST['quotations']))) {
            $soi = StoneOrderItem::find_by(["stone_id" => $stoneId], " AND senddate IS NULL ");
            if (!$soi) {
              $soi = new StoneOrderItem();
              $soi->stone_order_id = $stoneorder->id;
              $soi->stone_id = $stoneId;
              $soi->size = 0;
            }
            $soi->size += $toOrder;
            $soi->save();

            if (isset($_POST['quotations'])) {
              foreach ($_POST['quotations'] as $quotId) {
                $soiq = StoneOrderItemQuotation::find_by(["stone_order_item_id" => $soi->id, "quotation_id" => $quotId]);
                if (!$soiq) { //nog niet gekoppeld, aanmaken maar
                  $soiq = new StoneOrderItemQuotation();
                  $soiq->stone_order_item_id = $soi->id;
                  $soiq->quotation_id = $quotId;
                  $soiq->save();
                }
              }
            }
          }
        }

        $_SESSION['flash_message'] = "Op bestellijst geplaatst.";
        ResponseHelper::redirect(reconstructQuery());

      }

      if ($_SESSION['st_shortage'] == 1) {
        $this->pager->setRowsPerPage(1000);
      }

      $result = Stones::getStonesToOrder($this->pager, $_SESSION['st_search'], $_SESSION['st_brand'], $_SESSION['st_shortage'], $_SESSION['st_hasorder']);
      $stones = $result["stones"];
      $this->order_item_quotations = $result["order_item_quotations"];

      if ($_SESSION['st_shortage'] == 1) {
        $this->pager->setCount(count($stones));
      }

      $this->stonebrands = StoneBrands::getBrands();
      $this->stones = $stones;
      $this->groups = WorkerGroup::getAll();
    }

    public function executeSupplierordersOrdered() {

      if ($this->pageId == 'M_SUPPLIER_ORDERS_ORDERED') {
        $_SESSION['st_status'] = 'ordered';
      }
      else {
        $_SESSION['st_status'] = 'new';
      }


      $filt = " WHERE 1 ";
      if ($_SESSION['st_status'] != "") {
        $filt .= " AND status = '" . $_SESSION['st_status'] . "' ";
      }

      $stoneorders = [];
      foreach (StoneOrder::find_all($filt . " ORDER BY senddate DESC, id DESC") as $stoneorder) {
        $stoneorder->items = StoneOrderItem::getWithStones($stoneorder->id);
        //toevoegen quotations
        foreach ($stoneorder->items as $i) {
          $i->quotations = StoneOrderItemQuotation::getQuotationsByItem($i->id);
          $i->quotationNames = Quotations::getNames($i->quotations);
        }
        $stoneorders[$stoneorder->brand_id][] = $stoneorder;
      }

      $this->stonebrands = AppModel::mapObjectIds(StoneBrands::getBrands(), "brandId");
      $this->stoneorders = $stoneorders;

    }

    public function executeSupplierordersDelivered() {

      if ($this->pageId == 'M_SUPPLIER_ORDERS_DELIVERED') {
        $_SESSION['st_status'] = 'delivered';
      }
      else {
        $_SESSION['st_status'] = 'new';
      }

      //pager properties
      $this->pager = new Pager();
      $this->pager->setWriteCount(true);
      $this->pager->handle();
      //einde pager props

      if (!isset($_SESSION['st_brand'])) $_SESSION['st_brand'] = '';

      if (isset($_POST['st_brand'])) $_SESSION['st_brand'] = trim($_POST['st_brand']);

      $filt = " WHERE 1 ";
      if ($_SESSION['st_brand'] != "") {
        $filt .= " AND brand_id = " . $_SESSION['st_brand'] . " ";
      }
      if ($_SESSION['st_status'] != "") {
        $filt .= " AND status = '" . $_SESSION['st_status'] . "' ";
      }
      $stoneorders = StoneOrder::find_all($filt . " ORDER BY senddate DESC, id DESC" . $this->pager->getLimitQuery());
      $this->pager->count = StoneOrder::count_all_by([], $filt);

      $this->stonebrands = AppModel::mapObjectIds(StoneBrands::getBrands(), "brandId");
      $this->stoneorders = $stoneorders;

    }

    public function executeSupplierorderedit() {

      if (isset($_POST['cancel'])) {
        ResponseHelper::redirect(PageMap::getUrl($this->pageId));
      }

      $errors = [];

      $stoneorder = StoneOrder::find_by_id($_GET['id']);
      $stoneorder->items = StoneOrderItem::getWithStones($stoneorder->id);
      //toevoegen quotations
      foreach ($stoneorder->items as $i) {
        $i->quotations = StoneOrderItemQuotation::getQuotationsByItem($i->id);
        $i->quotationNames = Quotations::getNames($i->quotations);
      }

      if (isset($_POST['go']) || isset($_POST['go_list']) || isset($_POST['send']) || isset($_POST['not_send'])) {
        foreach ($_POST["size"] as $key => $size) {
          if (!isset($stoneorder->items[$key])) {
            ResponseHelper::redirectAlertMessage("Er gaat iets mis. Een verwijderd product of product regel? Open deze levering opnieuw.");
          }
          if ($stoneorder->items[$key]->size != $size) {
            $stoneorder->items[$key]->size = $size;
          }
          $stoneorder->items[$key]->name = $_POST["name"][$key];
        }

        foreach ($stoneorder->items as $k => $item) {
          if ($item->size == 0) {
            $item->destroy();
          }
          else {
            $item->save();
          }
        }

        if (isset($_POST['go_list'])) {
          $_SESSION['flash_message'] = "Bestelling is opgeslagen";
          ResponseHelper::redirect(PageMap::getUrl($this->pageId));
        }
        elseif (isset($_POST['go'])) {
          $_SESSION['flash_message'] = "Bestelling is opgeslagen";
          ResponseHelper::redirect(PageMap::getUrl($this->pageId, ['action' => 'supplierorderedit', 'id' => $stoneorder->id]));
        }
        elseif (isset($_POST['send'])) {

          foreach ($stoneorder->items as $item) {
            $item->name = $item->getRefname(); //zet naam in naam veld
            $item->senddate = date("Y-m-d H:i:s");
            $item->save();

            //de quotations bij deze bestellingen krijgen ook deze waarde
            foreach ($item->quotations as $item) {
              QuotationsExtra::manualOrdereddate($item->quotationId);
            }

          }

          $stoneorder->senddate = date("Y-m-d H:i:s");
          $stoneorder->status = 'ordered';
          $stoneorder->save();

          MailsFactory::sendOrder($stoneorder);

          $_SESSION['flash_message'] = "Email is verzonden en besteldatums zijn gezet.";
          ResponseHelper::redirect(PageMap::getUrl($this->pageId));
        }
        elseif (isset($_POST['not_send'])) {

          foreach ($stoneorder->items as $item) {
            $item->name = $item->getRefname(); //zet naam in naam veld
            $item->senddate = date("Y-m-d H:i:s");
            $item->save();

            //de quotations bij deze bestellingen krijgen ook deze waarde
            foreach ($item->quotations as $item) {
              QuotationsExtra::manualOrdereddate($item->quotationId);
            }

          }

          $stoneorder->senddate = date("Y-m-d H:i:s");
          $stoneorder->status = 'ordered';
          $stoneorder->save();

          MailsFactory::sendOrder($stoneorder, false);

          $_SESSION['flash_message_red'] = "Bestelling opgeslagen, maar er is nog geen mail gestuurd naar de leverancier!";
          ResponseHelper::redirect(PageMap::getUrl($this->pageId));
        }

      }

      $this->errors = $errors;
      $this->stoneorder = $stoneorder;

    }

    public function executeSupplierorderdelete() {
      if (isset($_GET['delid'])) {
        $ph = StoneOrder::find_by_id($_GET['delid']);

        if ($ph) {
          $ph->destroy();
          $_SESSION['flash_message'] = "Bestelling is verwijderd.";
          ResponseHelper::redirect(reconstructQueryAdd(['pageId']));
        }
      }

      $_SESSION['flash_message_red'] = "Bestelling niet gevonden.";
      ResponseHelper::redirect(reconstructQueryAdd(['pageId']));
    }

    public function executeSupplierorderdeletepr() {
      if (isset($_GET['delid'])) {
        $ph = StoneOrderItem::find_by_id($_GET['delid']);

        if ($ph) {
          $ph->destroy();
          $_SESSION['flash_message'] = "Product is verwijderd.";
          ResponseHelper::redirect(PageMap::getUrl($this->pageId) . "?action=supplierorderedit&id=" . $ph->stone_order_id);
        }
      }

      $_SESSION['flash_message_red'] = "Product niet gevonden.";
      ResponseHelper::redirect(PageMap::getUrl($this->pageId) . "?action=supplierorderedit&id=" . $ph->stone_order_id);
    }


    public function executeSupplierorderproducts() {

      if (isset($_POST['cancel'])) {
        ResponseHelper::redirect(PageMap::getUrl($this->pageId));
      }

      if (!isset($_SESSION['st_brand'])) $_SESSION['st_brand'] = '';
      if (isset($_POST['go'])) {
        if (isset($_POST['st_brand'])) $_SESSION['st_brand'] = trim($_POST['st_brand']);
        ResponseHelper::redirect(reconstructQuery());
      }


      $errors = [];

      $stoneorderitems = StoneOrderItem::getStonesToRecieve($_SESSION['st_brand']);
      $stoneorderitems_delivered = [];

      if (isset($_POST['go_updatestock'])) {

        $quotationsReceived = [];
        foreach ($_POST["received"] as $id => $size) {
          $checked = isset($_POST["checked"][$id]);
          $nodelivery = isset($_POST["nodelivery"][$id]);
          $item = $stoneorderitems[$id];
          $stoneorderitems_delivered[$id] = $item;

          $save = false;

          if (getTSFromStr($_POST["supplierreadydate"][$id]) != $item->supplierreadydate) {
            if ($_POST["supplierreadydate"][$id] == "") {
              $item->supplierreadydate = null;
            }
            else {
              $item->supplierreadydate = getTSFromStr($_POST["supplierreadydate"][$id]);
            }
            $save = true;
          }

          if (($checked && $size > 0) || $nodelivery) {
            if ($item->receiveddate == "") {
              if ($item->receivedsize == "") $item->receivedsize = 0;
              $item->receivedsize += $size;
              if ($nodelivery || $item->receivedsize >= $item->size) {
                //word niet meer geleverd, of het aantal is behaald
                $item->receiveddate = date("Y-m-d H:i:s");
              }

              //voorraad ophogen
              $item->stone->addStock($size);

              //de quotations bij deze bestellingen krijgen ook deze waarde
              foreach ($item->quotations as $quot) {
                $quotationsReceived[$quot->quotationId] = $quot->quotationId;
              }

              $save = true;
            }
          }

          if ($item->loadreference != $_POST["loadreference"][$id]) {
            $item->loadreference = $_POST["loadreference"][$id];
            $save = true;
          }

          if ($save) {
            $item->save();
          }
        }

        StoneOrder::checkStatus();

        StoneOrderItem::checkReceivedQuotations($quotationsReceived);

        MailsFactory::sendDeliveredExpectedproducts($stoneorderitems_delivered);

        $_SESSION['flash_message'] = "Voorraad geupdate";
        ResponseHelper::redirect(PageMap::getUrl($this->pageId));
      }

      $this->errors = $errors;
      $this->stoneorderitems = $stoneorderitems;
      $this->stoneorders = AppModel::mapObjectIds(StoneOrder::find_all_by(["status" => 'ordered']));
      $this->stonebrands = StoneBrands::getBrands();
    }

    public function executeStockpdf() {
      $pdf = new StockPdf(true);
      $pdf->generatePdf();
      ResponseHelper::exit();
    }

    public function executeStockpdfinvalid() {
      $pdf = new StockPdf(true);
      $pdf->generatePdf(true);
      ResponseHelper::exit();
    }

    public function executeGeneratelabel() {
      $pdf = new LabelPdf(true);
      $pdf->generatePdf($_GET["id"]);
      ResponseHelper::exit();
    }

    public function executeStonesneeded() {
      if (!isset($_SESSION['st_search'])) $_SESSION['st_search'] = '';
      if (!isset($_SESSION['st_brand'])) $_SESSION['st_brand'] = '';
      if (isset($_POST['st_search'])) $_SESSION['st_search'] = trim($_POST['st_search']);
      if (isset($_POST['st_brand'])) $_SESSION['st_brand'] = trim($_POST['st_brand']);

      $result = Stones::getStonesToOrder(false, $_SESSION['st_search'], $_SESSION['st_brand'], false, true);

      $stones = $result["stones"];

      $this->stones = $stones;
      $this->stonebrands = StoneBrands::getBrands();
    }


  }
