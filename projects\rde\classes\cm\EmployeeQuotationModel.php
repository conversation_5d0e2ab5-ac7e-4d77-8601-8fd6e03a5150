<?php

  AppModel::loadBaseClass('BaseEmployeeQuotation');

  class EmployeeQuotationModel extends BaseEmployeeQuotation {

    public function getStartdateFormatted($format = 'd-m-Y H:i:s') {
      if ($this->startdate != "" && $this->startdate != "0000-00-00 :00:00:00") {
        return date($format, strtotime($this->startdate));
      }
      return "";
    }

    public function getEnddateFormatted($format = 'd-m-Y H:i:s') {
      if ($this->enddate != "" && $this->enddate != "0000-00-00 :00:00:00") {
        return date($format, strtotime($this->enddate));
      }
      return "";
    }

    public function save(&$errors = []) {
      return parent::save($errors);
    }


  }