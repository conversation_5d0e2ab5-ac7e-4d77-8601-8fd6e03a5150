<?php TemplateHelper::includePartial('_tabs.php','lstock'); ?>
<script type="text/javascript">
$(document).ready(function() {
  $(".size").change(function (event) {
    if($(this).val()!="") {
      var val = parseInt($(this).val());
      if(!isNaN(val)) {
        $(this).val(val);
      }
      else {
        $(this).val(0);
      }
    }
    else {
      $(this).val(0);
    }
  });
  $("#st_brand").change(function() {
    $("#go").click();
  });
});
</script>	

<form method="post">
  <div class="box">
    <select name="st_brand" id="st_brand">
      <option value="">Selecteer leverancier...</option>
      <?php foreach($stonebrands as $sb): ?>
        <option value="<?php echo $sb->brandId ?>" <?php if($_SESSION['st_brand']==$sb->brandId) echo 'selected'; ?>><?php echo $sb->name ?></option>
      <?php endforeach; ?>
    </select>
    <input type="submit" name="go" id="go" value="Zoeken" />
  </div>

  <?php $pager->writePreviousNext(); ?>

	<?php	if(count($stoneorders)==0): ?>
		<br/>Er zijn geen bestellingen gevonden
  <?php else: ?>
		<table class="default_table" style="width: auto;">
		<tr class="dataTableHeadingRow">
      <td>Leverancier</td>
      <td>Status</td>
      <?php if($pageId=="M_SUPPLIER_ORDERS_DELIVERED"): ?>
        <td>Besteld op</td>
        <td>Geleverd op</td>
      <?php endif; ?>
      <td style="width: 50px;">Bewerk</td>
      <?php if($pageId!="M_SUPPLIER_ORDERS_DELIVERED"): ?>
        <td style="width: 50px;">Verwijder</td>
      <?php endif; ?>
		</tr>
		<?php foreach($stoneorders as $item): ?>
			<tr class="dataTableRow trhover">
        <td><?php echo $stonebrands[$item->brand_id]->name ?></td>
        <td><?php echo StoneOrder::$stati[$item->status] ?></td>
        <?php if($pageId=="M_SUPPLIER_ORDERS_DELIVERED"): ?>
          <td><?php echo $item->getSenddate("d-m-Y H:i") ?></td>
          <td><?php echo $item->getRecieveddate("d-m-Y H:i") ?></td>
        <?php endif; ?>
        <td>
          <?php echo BtnHelper::getEdit('?action=supplierorderedit&id='.$item->id) ?>
        </td>
        <?php if($pageId!="M_SUPPLIER_ORDERS_DELIVERED"): ?>
          <td>
            <?php echo BtnHelper::getRemove('?action=supplierorderdelete&delid='.$item->id, "Verwijderen bestelling ".($item->getSenddate()==""?"nieuw":"van ".$item->getSenddate())) ?>
          </td>
        <?php endif;  ?>
			</tr>
    <?php endforeach; ?>
		</table><br/>
	<?php endif; ?>

</form>