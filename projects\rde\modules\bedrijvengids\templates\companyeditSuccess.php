<?php TemplateHelper::includePartial('_tabs.php', 'sandbox'); ?>
<?php //dumpe($form_post,$form_visit); ?>
<?php if (!empty($form_company->getModelobject()->companyId)): ?>
  <ul id="tabnav" class="nav nav-tabs">
    <?php echo Navigation::writeNavigationNodes(Navigation::getItem('M_BEDRIJVENGIDS_LIST')->getChildren()); ?>
  </ul>
<?php endif; ?>

<?php writeErrors(array_merge($form_company->getErrors(), $form_visit->getErrors(), $form_post->getErrors()), true); ?>

<form method="post" name="form" id="form" class="edit-form">

  <table class="default_table">
    <tr class="dataTableHeadingRow">
      <td colspan="2">Bedrijfsgegevens</td>
    </tr>
    <?php foreach ($form_company->getElements() as $element): ?>
      <?php $element->renderRow() ?>
    <?php endforeach; ?>
    <tr class="dataTableHeadingRow">
      <td colspan="2">Bezoekadres</td>
    </tr>
    <?php foreach ($form_visit->getElements() as $element): ?>
      <?php $element->renderRow() ?>
    <?php endforeach; ?>
    <tr>
      <td></td>
      <td style="height: 20px">
        <input type="checkbox" name="noDelivery" <?php echo $no_delivery?"checked":"" ?> > Geen levering op dit adres
      </td>
    </tr>
    <tr class="dataTableHeadingRow">
      <td colspan="2">Postadres</td>
    </tr>
    <tr>
      <td></td>
      <td><input type="checkbox" name="compare_address" <?php echo $is_double_address?"checked":"" ?> > Postadres is hetzelfde als bezoekadres</td>
    </tr>
    <?php foreach ($form_post->getElements() as $element): ?>
      <?php $element->renderRow() ?>
    <?php endforeach; ?>

  </table>

  <input type="submit" name="go" value="Opslaan" class="gsd-btn gsd-btn-primary"/>
  <input type="submit" name="go_list" value="Opslaan en naar lijst" class="gsd-btn gsd-btn-secondary"/>

</form>
