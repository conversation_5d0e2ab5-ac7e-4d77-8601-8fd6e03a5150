<template>
  <div id="map-filter">
    <v-container>
      <v-card class="mb-4">
        <v-card-text>
          <template v-if="Object.keys(statuses).length">
            <v-row>
              <v-col
                v-for="(status, index) in statuses"
                :key="`status-${index}`"
                cols="4"
                class="pa-0"
              >
                <v-checkbox
                  v-model="checkedStatus"
                  :value="index"
                  @change="refreshLocations"
                  :color="getStatusColor(index)"
                  hide-details
                >
                  <template v-slot:label>
                    <v-icon
                      :color="getStatusColor(index)"
                      icon="mdi-circle"
                      :title="status"
                    />
                  </template>
                </v-checkbox>
              </v-col>
            </v-row>
          </template>
          <v-progress-circular
            v-else
            indeterminate
            color="primary"
          />
        </v-card-text>
      </v-card>

      <v-card v-if="selectedCompany" class="mb-4">
        <v-card-title>Geselecteerd Bedrijf</v-card-title>
        <v-card-text>
          <p class="text-subtitle-1">Bedrijf ID: {{ selectedCompany }}</p>
          <v-divider class="my-2" />
          <template v-if="companyQuotations.length">
            <template v-for="quotation in companyQuotations" :key="`selected-quotation-${quotation.quotationId}`">
              <v-card variant="outlined" class="mb-2">
                <v-card-text>
                  <h3 class="text-h6 mb-2">{{ quotation.quotationNumber }} - {{ quotation.projectName }}</h3>
                  <p><strong>Besteldatum:</strong> {{ quotation.productionDate }}</p>
                  <p><strong>Leverdatum:</strong> {{ quotation.dueDate }} (week {{ quotation.dueDateWeek }})</p>
                  <p>{{ quotation.deliveryNotes }}</p>
                  <p><strong>Meters:</strong> {{ quotation.meters }}</p>
                  <v-btn
                    :href="'http://beheer.raamdorpel.nl.rde.localhost/nl/bestellingen-algemeen?id='+ quotation.quotationId"
                    target="_blank"
                    variant="text"
                    color="primary"
                    class="mt-2"
                  >
                    Bekijk {{ quotation.quotationNumber }}
                  </v-btn>
                </v-card-text>
              </v-card>
            </template>
          </template>
          <v-alert
            v-else
            type="info"
            text="Geen offertes gevonden voor dit bedrijf."
          />
        </v-card-text>
      </v-card>

      <v-expansion-panels>
        <v-expansion-panel>
          <v-expansion-panel-title>Alle Bestellingen</v-expansion-panel-title>
          <v-expansion-panel-text>
            <div v-for="(quotation, key) in quotationsByComp" :key="`company-${key}`">
              <p class="text-subtitle-1">Bedrijf ID: {{ key }}</p>
              <v-list>
                <v-list-item v-for="q in quotation" :key="`quotation-${q.quotationId}`">
                  <v-list-item-title>Offerte ID: {{ q.quotationId }}</v-list-item-title>
                </v-list-item>
              </v-list>
              <v-divider class="my-2" />
            </div>
          </v-expansion-panel-text>
        </v-expansion-panel>
      </v-expansion-panels>
    </v-container>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { getStatusColor } from '../helpers/ColorHelper'

const props = defineProps({
  quotationsByComp: {
    type: Object,
    required: true,
    default: () => ({})
  },
  statuses: {
    type: Object,
    required: true,
    default: () => ({})
  },
  selectedCompany: {
    type: [String, Number],
    default: null
  },
  companyQuotations: {
    type: Array,
    default: () => []
  }
})

const emit = defineEmits(['selectedQuotations'])

const checkedStatus = ref([])

const refreshLocations = () => {
  emit('selectedQuotations', checkedStatus.value)
}
</script>

<style>
#map-filter {
  border-right: 1px solid grey;
  height: 100%;
  overflow: auto;
  padding: 15px;
}
</style>
