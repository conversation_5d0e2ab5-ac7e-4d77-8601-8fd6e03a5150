<div class="box">
  <form method="post" action="<?php echo reconstructQueryAdd() ?>">
    <input type="text" name="size_search" value="<?php echo $_SESSION['size_search'] ?>" placeholder="Zoeken..."/>
    <select name="size_brand" id="size_brand">
      <option value="">Selecteer merk...</option>
      <?php foreach($brands as $brand): ?>
        <option value="<?php echo $brand->brandId ?>" <?php if($_SESSION['size_brand']==$brand->brandId) echo 'selected'; ?>><?php echo $brand->name ?></option>
      <?php endforeach; ?>
    </select>
    <input type="submit" name="go" id="go" value="Zoeken" />

  </form>
</div>

<form method="post" id="invoice_form">
  <?php if ($isInvoices): ?>
    <input type="hidden" id="quotation_ids" name="quotation_ids" value="">
    <input type="submit" id="invoices_selected" class="gsd-btn gsd-btn-primary"  value="<?php echo __('Factureren') ?>" name="invoice" style="margin-bottom: 15px">
  <?php endif; ?>

<?php if(count($quotationsGrouped)==0): ?>
  <section class="empty-list-state">
    <p><?php echo __('Er zijn geen items gevonden.') ?></p>
  </section>
<?php else: ?>
  <table class="default_table" style="width: auto;">
    <tr class="dataTableHeadingRow">
      <?php if ($isInvoices): ?>
      <td>Factureren</td>
      <?php endif; ?>
      <td>Leveringsnummer
        <?php echo showHelpButton("Het leveringsnummer komt overeen met het unieke id van het afleveradres. Immers producten met hetzelfde afleveradres mogen in dezelfde bak.") ?>
      </td>
      <td>Status</td>
      <td>Leverweek</td>
      <td style="width: 70px;">Afhalen</td>
      <td>Opdrachtdatum</td>
      <td>Offertenummer</td>
      <td>Merk/Materiaal</td>
      <td>Model</td>
      <td>Opmerking</td>
      <td>M</td>
      <td>M2</td>
      <td>PDF</td>
      <?php if(isset($showProducedate) && $showProducedate): ?>
        <td>Geleverd op</td>
      <?php else: ?>
        <?php if (!$isInvoices): ?>
          <td style="width: 200px;">Actie</td>
        <?php endif; ?>
      <?php endif; ?>
    </tr>
    <?php
      $lastAfleveradresId = 0;
      foreach($quotationsGrouped as $addressId=>$quotationGroup):
        /** @var Quotations $quotation */
        $tel = 0;
        foreach($quotationGroup["items"] as $k=>$quotation):
//          if($tel==0):
            $supplier_time = strtotime("-1 WEEKS",intval($quotation->getDueDate("U")));
//          endif;
          $stone = Stones::find_by(["stoneId" => $quotation->stoneId]);
          $tel++;
          ?>
          <tr class="dataTableRow trhover <?php if($lastAfleveradresId!=$addressId) echo 'bordertopper' ?>">
            <?php if ($isInvoices): ?>
            <td><input type="checkbox" name="check_for_invoice_<?php echo $quotation->quotationId ?>" value="<?php echo $quotation->quotationId ?>"></td>
            <?php endif; ?>
            <td><?php echo $quotationGroup["name"] ?></td>
            <td><?php echo $quotation->getIconHtml() ?></td>
            <td><?php echo date("Y-W",$supplier_time) ?></td>
            <td style="color: #f44336;font-weight: bold;">
              <?php if(isset($quotationRoutes[$quotation->quotationId])):
                $minDate = "-2 DAY";
                if($quotationRoutes[$quotation->quotationId]->getDate("N")==1) { //maandag
                  $minDate = "-3 DAY";
                }
                $pickup = date("d-m-Y", strtotime($minDate, intval($quotationRoutes[$quotation->quotationId]->getDate("U"))));
                ?>
                <?php echo $pickup; ?>
              <?php endif; ?>
            </td>
            <td><?php echo $quotation->getProductionDate() ?></td>
            <td><?php echo $quotation->getQuotationNumberFull() ?></td>
            <td><?php echo $brands[$quotation->brandId]->name ?></td>
            <td><?php
                if($stone):
                  if($stone->type==Stones::TYPE_BALKJES):
                    echo 'Balkje';
                  else:
                    $size = StoneSizes::find_by(["sizeId" => $stone->sizeId]);
                    if($size):
                      echo $size->name;
                    endif;
                  endif;
                endif;
              ?></td>
            <td><?php echo nl2br((string)$quotation->productionNotes) ?></td>
            <td style="text-align: right;"><?php echo $quotation->meters ?></td>
            <td style="text-align: right;"><?php $surf = $quotation->getSurface($stone); echo $surf!=0?$surf:''; ?></td>
            <td>
              <?php echo BtnHelper::getPrintPDF("?action=downloadpdf&id=".$quotation->quotationId) ?>
            </td>
            <td style="text-align: center">
              <?php if($quotation->statusId == Status::STATUS_CHECKED): ?>
                <a href="?action=setinproduction&id=<?php echo $quotation->quotationId ?>" class="gsd-btn gsd-delete" data-gsd-text="Heeft u de PDF geprint en is de bestelling in productie?" title="In productie">In productie</a>
              <?php elseif($quotation->statusId == Status::STATUS_PREPARED): ?>
                <a href="?action=setpickup&id=<?php echo $quotation->quotationId ?>" class="gsd-btn gsd-btn-primary">RDE mag afhalen</a>
              <?php elseif($quotation->statusId == Status::STATUS_IN_PRODUCTION && !$isInvoices): ?>
                <a href="?action=resetpickup&id=<?php echo $quotation->quotationId ?>" class="gsd-btn gsd-delete" title="Terugzetten" data-gsd-text="Wil je deze bestellingen terugzetten naar 'In productie'?">Terugzetten naar 'In productie'</a>
              <?php endif; ?>
            </td>
          </tr>
        <?php
          $lastAfleveradresId = $addressId;
        endforeach; ?>
      <?php endforeach; ?>
  </table>
<?php endif; ?>
</form>
<script>
  $(document).ready(function() {
    $("#size_brand,#size_display").change(function() {
      $("#go").click();
    });

  });

</script>
<style>
  .bordertopper td {
    border-top: 2px solid #9526175e;
  }
</style>