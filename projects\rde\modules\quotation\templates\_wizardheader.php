<h1>
  <?php if($step > 0): ?>
    <a href="#" style="color: #808080;" title="Vorige" id="prev_top"><i class="fa fa-chevron-left"></i></a>
  <?php endif; ?>

  <?php if($quotation->quotationId == ""): ?>
    <?php echo __("Nieuwe offerte"); ?>
  <?php else: ?>
    <?php echo __("Offerte bewerken"); ?>
  <?php endif; ?>

  <?php if($step != 6): ?>
    <a href="#" style="color: #808080;float: right;" title="Volgende" id="next_top"><i class="fa fa-chevron-right"></i></a>
  <?php endif; ?>

  <?php if($step == 0 && isset($_SESSION["wizard"]['quotation'])): ?>
    <a id="startnewquotation" href="?action=wizard&step=1&clear=true" class="qtipa" title="De huidig offerte word leeggemaakt en u start met een nieuwe offerte.">START NIEUWE OFFERTE <i class="fa fa-undo"></i></a>
  <?php endif; ?>
</h1>

<div class="steps ">
  <div class="step_on">
    <?php if(0<$step): ?><a href="?action=wizard&step=0"><?php endif; ?>
      <?php echo __('Product') ?>
      <?php if(0<$step): ?></a><?php endif; ?>
  </div>
  <div class="step_<?php echo 1<=$step?'on':'off'?>">
    <?php if(1<$step): ?><a href="?action=wizard&step=1"><?php endif; ?>
      <?php echo __('Steen') ?>
      <?php if(1<$step): ?></a><?php endif; ?>
  </div>
  <div class="step_<?php echo 2<=$step?'on':'off'?>">
    <?php if(2<$step): ?><a href="?action=wizard&step=2"><?php endif; ?>
      <?php echo __('Elementen') ?>
      <?php if(2<$step): ?></a><?php endif; ?>
  </div>
  <?php if($hasVerstek): ?>
    <div class="step_<?php echo 3<=$step?'on':'off'?>">
      <?php if(3<$step): ?><a href="?action=wizard&step=3"><?php endif; ?>
        <?php echo __('Verstek') ?>
        <?php if(3<$step): ?></a><?php endif; ?>
    </div>
  <?php endif; ?>
  <div class="step_<?php echo 4<=$step?'on':'off'?>">
    <?php if(4<$step): ?><a href="?action=wizard&step=4"><?php endif; ?>
      <?php echo __('Extra producten') ?>
      <?php if(4<$step): ?></a><?php endif; ?>
  </div>
  <div class="step_<?php echo 5<=$step?'on':'off'?>">
    <?php if(5<$step): ?><a href="?action=wizard&step=5"><?php endif; ?>
      <?php echo __('Afleveradres') ?>
    <?php if(5<$step): ?></a><?php endif; ?>
  </div>
  <div class="step_<?php echo 6<=$step?'on':'off'?>"><?php echo __('Aanvragen')?></div>
</div>
<script type="text/javascript">
  $(document).ready( function() {

    $("#prev_top").click(function(e) {
      e.preventDefault();
      $("#prev").click();
    });

    $("#next_top").on("click",function(e) {
      e.preventDefault();
      $("#next").click();
    });

  });
</script>