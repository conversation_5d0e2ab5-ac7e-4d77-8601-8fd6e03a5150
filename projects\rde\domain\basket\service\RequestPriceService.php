<?php

  namespace domain\basket\service;

  class RequestPriceService {
    public static function basketHasRequestPrices($basket): bool {
      if (!$basket || !isset($basket['products'])) return false;
      $price_on_request = false;
      foreach ($basket['products'] as $productItem) {
        $product = $productItem['product'];
        if ($product->price_on_request) {
          $price_on_request = true;
          break;
        }
      }
      return $price_on_request;
    }
  }