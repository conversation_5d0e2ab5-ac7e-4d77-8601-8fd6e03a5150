<section class="title-bar">
  <h1>
    <?php echo Navigation::getItem(Navigation::getActivePageId())->getName() ?>
  </h1>
  <?php TemplateHelper::includePartial("_tabs.php","other") ?>
</section>

<?php if(count($ratings)==0): ?>
  <br/>
  Er zijn geen review e-email welke verzonden moeten worden.
<?php else: ?>
  <table class="default_table" style="width: auto;">
    <tr class="dataTableHeadingRow">
      <td>Bedrijfsnaam</td>
      <td>Datum</td>
      <td>Naam</td>
      <td>E-mailadres</td>
      <td>Verwijden</td>
      <td>Verzenden</td>
    </tr>
    <?php
      /** @var CustomerRatingEmails $rating */
      foreach($ratings as $rating): ?>
        <tr class="dataTableRow trhover">
          <td><?php echo $rating->companyName ?></td>
          <td><?php echo date("d-m-Y",strtotime($rating->dateStatusChange)) ?></td>
          <td><?php echo $rating->firstname ?> <?php echo $rating->lastname ?></td>
          <td><?php echo $rating->email ?></td>
          <td>
            <?php echo BtnHelper::getRemove(reconstructQuery(array('action','id')).'action=delete&id=' . $rating->id) ?>
          </td>
          <td>
            <a href="?action=send&id=<?php echo $rating->id ?>" class="gsd-btn gsd-btn-primary">Verzenden</a>
          </td>
        </tr>
      <?php endforeach; ?>
  </table>
<?php endif; ?>

<script>
  $(document).ready(function() {
    $("#size_brand,#size_display").change(function() {
      $("#go").click();
    })
  });
</script>