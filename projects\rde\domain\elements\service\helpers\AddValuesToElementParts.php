<?php

  namespace domain\elements\service\helpers;

  class AddValuesToElementParts
  {
    public function roundDoubleToOneDecimal($length): float|int {
      return round($length * 10) / 10;
    }

    public function numberFormatToOneDecimal($length): string {
      return number_format($this->roundDoubleToOneDecimal($length), 1, ".", "");
    }

    public function addValuesToAElementParts($partNr, $aMitreElementParts, $stoneAmountB, $aElementParts): array {
      $aElementParts['aAmount1'] = 1;
      $aElementParts['aLength2'] = $aMitreElementParts['firstLength2'];

      return array_merge($aElementParts, $this->getCommonParts($aMitreElementParts, $partNr, $stoneAmountB));
    }

    private function getCommonParts($parts, $partNr, $stoneAmountB): array {
      $result = [];

      if ($partNr === 2) {
        $result['bAmount1'] = 1;
        $result['aLength1'] = $this->fmt($parts['firstLength1']);
        $result['bLength1'] = $this->fmt($parts['lastLength1']);
        $result['bLength2'] = $parts['lastLength2'];

      } elseif ($partNr === 3) {
        $result['bAmount1'] = 1;
        $result['cAmount1'] = 1;
        $result['aLength1'] = $this->fmt($parts['firstLength1']);
        $result['bLength1'] = $this->fmt($parts['bLength1']);
        $result['cLength1'] = $this->fmt($parts['lastLength1']);
        $result['cLength2'] = $parts['lastLength2'];

      } elseif ($partNr >= 4) {
        $isWholeNumber = floor($stoneAmountB) == $stoneAmountB;
        $result['aLength1'] = $this->fmt($parts['firstLength1']);

        if ($isWholeNumber) {
          $result['bAmount1'] = $partNr - 2;
          $result['cAmount1'] = 1;
          $result['bLength1'] = $this->fmt($parts['bLength1']);
          $result['cLength1'] = $this->fmt($parts['lastLength1']);
          $result['cLength2'] = $parts['lastLength2'];
        } else {
          $result['bAmount1'] = $partNr - 3;
          $result['cAmount1'] = 1;
          $result['dAmount1'] = 1;
          $result['bLength1'] = $this->fmt($parts['bLength1']);
          $result['cLength1'] = $this->fmt($parts['cLength1']);
          $result['dLength1'] = $this->fmt($parts['lastLength1']);
          $result['dLength2'] = $parts['lastLength2'];
        }
      }

      return $result;
    }

    private function fmt($val): string {
      return $this->numberFormatToOneDecimal($val);
    }
  }