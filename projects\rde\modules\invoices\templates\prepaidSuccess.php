<section>

  <div class="contenttxt">
    <h1>Vooruitbetalen</h1>

    Op deze pagina ziet u de bestellingen welke u nog niet betaald zijn.
    Wanneer u het bedrag heeft overgemaakt, gaan we deze bestellingen in behandeling nemen. <br/><br/>

    <?php if(count($quotations)==0): ?>
      Er zijn geen te betalen bestellingen gevonden.
    <?php else: ?>
      <table class="download" id="offertetable">
        <tbody>
        <tr>
          <th style="width: 30px;"></th>
          <th style="min-width: 110px;">Factuurnr.</th>
          <th>Factuurdatum</th>
          <th>Vervaldatum</th>
          <th style="text-align: right;">Bedrag</th>
          <th style="text-align: right;">Bekijk</th>
          <th style="text-align: right;">Afrekenen</th>
        </tr>
        <?php
          /** @var Invoices $invoice */
          foreach($quotations as $quotation):
            $dayslate = 0;
            if($invoice->paid=='') $dayslate = $invoice->getExpirationdays($paymentTerm);
            ?>
            <tr class="trhover <?php if($dayslate>0) echo 'invoicelate'; ?>">
              <td>
                <?php if($invoice->paid!=''): ?>
                  <img src="<?php echo $site->getTemplateUrl() ?>images/check_green.svg" alt="" width="20" class="qtipa" title="Factuur betaald op <?php echo $invoice->getPaid(); ?>" />
                <?php else: ?>
                  <?php if($dayslate>0): ?>
                    <a href="javascript:void(0);" tabindex="-1" title="Factuur <?php echo $dayslate ?> dagen te laat betaald." class="fa fa-exclamation-circle qtipa"></a>
                  <?php endif; ?>
                <?php endif; ?>
              </td>
              <td><?php echo $invoice->invoiceNumber ?></td>
              <td><?php echo $invoice->getDateInvoice() ?></td>
              <td><?php echo $invoice->getExpirationdate($paymentTerm) ?></td>
              <td style="text-align: right;">€ <?php echo getLocalePrice($invoice->totalProjectValue,',','.') ?></td>
              <td style="text-align: right;">
                <a target="_blank" href="?action=pdf&id=<?php echo $invoice->invoiceId ?>" title="Bekijk de PDF van uw factuur" ><i class="fa fa-file-pdf-o"></i></a>
              </td>
              <td style="text-align: right;">
                <?php if($invoice->paid==''): ?>
                  <a href="<?php echo $invoice->getPayUrl() ?>" title="Online afrekenen">Afrekenen <i class="fa fa-chevron-right"></i></a>
                <?php endif; ?>
              </td>
          <?php endforeach; ?>
        </tbody>
      </table>


    <?php endif; ?>

    <script type="text/javascript">


    </script>

  </div>

</section>
