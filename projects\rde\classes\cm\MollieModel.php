<?php

  AppModel::loadBaseClass('BaseMollie');

  class MollieModel extends BaseMollie {

    /**
     * @param $paymentId
     * @param $amount
     * @return Mollie
     */
    public static function add($paymentId, $amount) {
      $m = new Mollie();
      $m->paymentId = $paymentId;
      $m->amount = $amount;
      $m->save();
      return $m;
    }


    /**
     * Get insertTS
     * @param string $format
     * @return string
     */
    public function getInsertTS(string $format = 'd-m-Y H:i'): string {
      return DateTimeHelper::formatDbDate($this->insertTS, $format);
    }


    public function save(&$errors = []) {
      if ($this->from_db == false) {
        $this->insertTS = date('Y-m-d H:i:s');
      }
      return parent::save($errors);
    }

  }