<?php

  use domain\firebase\service\PushNotification;
  use Gsd\Form\ModelForm;

  class otherRdeActions extends otherActions {

    public function executeImagelist() {

      if (!isset($_SESSION["img_search"])) $_SESSION["img_search"] = "";
      if (!isset($_SESSION["img_from"])) $_SESSION["img_from"] = "";
      if (!isset($_SESSION["img_to"])) $_SESSION["img_to"] = "";

      if (isset($_POST["img_search"])) {
        $_SESSION["img_search"] = trim($_POST["img_search"]);
        $_SESSION["img_from"] = trim($_POST["img_from"]);
        $_SESSION["img_to"] = trim($_POST["img_to"]);
        ResponseHelper::redirect(reconstructQuery());
      }

      if (isset($_POST['delete_go'])) {
        foreach ($_POST['delete'] as $imageid) {
          $bi = ContainerImg::find_by_id($imageid);
          if ($bi) {
            $bi->destroy();
          }
        }
        $_SESSION['flash_message'] = "Beelden verwijderd.";
        ResponseHelper::redirect(reconstructQuery());
      }

      Context::addJavascript(URL_INCLUDES . 'jsscripts/imagesloaded.pkgd.min.js');
      Context::addJavascript(URL_INCLUDES . 'jsscripts/jquery/waypoint/waypoints.min.js');
      Context::addJavascript(URL_INCLUDES . 'jsscripts/jquery/waypoint/shortcuts/infinite-scroll/waypoints-infinite.min.js');

      //pager properties
      $this->pager = new Pager();
      $this->pager->setRowsPerPage(50);
      $this->pager->handle();
      //einde pager props

      $filter = 'WHERE 1 ';
      if ($_SESSION["img_search"] != "") {
        $quot = Quotations::find_by(["quotationNumber" => $_SESSION["img_search"]]);
        if ($quot) {
          $filter .= ' AND quotationId=' . $quot->quotationId . " ";
        }
        else {
          $filter .= ' AND 0 ';
        }
      }

      if ($_SESSION['img_from'] && $_SESSION['img_to']) {
        $filter .= " AND insertDate >= '" . escapeForDB(getTSFromStr($_SESSION['img_from'])) . "' AND insertDate <= '" . getTSFromStr(escapeForDB($_SESSION['img_to'])) . "' ";
      }

      $filter .= 'GROUP BY filename ';


      $this->pager->count = ContainerImg::count_all_by([], $filter);
      $images = ContainerImg::find_all($filter . ' ORDER BY id DESC' . $this->pager->getLimitQuery()); //nieuwste bovenaan

      $filenames = [];
      foreach ($images as $i) {
        $filenames[] = $i->filename;
      }
      $bestids = [];
      $allimages = ContainerImg::find_all_by(["filename" => $filenames]);
      foreach ($allimages as $i) {
        if ($i->quotationId != "") {
          $bestids[$i->quotationId] = $i->quotationId;
        }
      }
      $bestellingen = [];
      if (count($bestids) > 0) {
        $bestellingen = AppModel::mapObjectIds(Quotations::find_all_by(['quotationId' => $bestids]), 'quotationId');
      }

      $groupedimages = [];
      foreach ($allimages as $image) {
        if (!isset($groupedimages[$image->filename])) {
          $image->offerteNummers = [];
          $groupedimages[$image->filename] = $image;
        }
        if ($image->quotationId != "") {
          $groupedimages[$image->filename]->offerteNummers[] = $bestellingen[$image->quotationId]->quotationNumber;
        }
      }

      $this->images = $groupedimages;

      if ($this->pager->getPageNum() > 1) {
        $this->template = "_images.php";
        $this->template_wrapper_clear = true;
      }
    }

    public function executeImage() {
      $img = ContainerImg::find_by_id($_GET["id"]);
      if ($img && file_exists($img->getFilepath())) {
        header('Content-Type: image/jpeg');
        header("Content-Length: " . filesize($img->getFilepath()));
        echo file_get_contents($img->getFilepath());
      }
      ResponseHelper::exit();
    }

    public function executeImageinfo() {
      $images = ContainerImg::find_all_by(["filename" => $_GET["filename"]]);
      if (count($images) == 0) {
        ResponseHelper::redirectAlertMessage("Foto niet gevonden");
      }

      $containerIds = [];
      $quotationIds = [];
      foreach ($images as $image) {
        if ($image->containerId != "") $containerIds[] = $image->containerId;
        if ($image->quotationId != "") $quotationIds[] = $image->quotationId;
      }

      $this->img = $images[0];
      $this->containers = Containers::find_all_by(["containerId" => $containerIds]);
      $this->containerNumbers = [];
      foreach ($this->containers as $c) {
        $this->containerNumbers[$c->containerNumber] = $c->containerNumber;
      }
      $this->quotations = Quotations::find_all_by(["quotationId" => $quotationIds]);
      $this->quotationNumbers = [];
      foreach ($this->quotations as $q) {
        $this->quotationNumbers[$q->quotationNumber] = $q->quotationNumber;
        $q->quotationuser = SandboxUsers::getUserAndCompany($q->userId);
      }


      $this->template_wrapper_clear = true;
    }

    public function executeDamage() {
      $this->damages = Damage::find_all("ORDER BY name_nl");
    }

    public function executeDamageedit() {

      $damage = new Damage();
      if (isset($_GET["id"])) {
        $damage = Damage::find_by_id($_GET["id"]);
      }

      $form = new ModelForm();
      $form->addClass("edit-form");
      $form->buildElementsFromModel($damage);
      $form->setElementsLabel([
        "name_nl" => "Naam NL",
        "name_pl" => "Naam PL",
      ])->getElement("name_nl")->setRequired(true);

      if (isset($_POST['go']) || isset($_POST['go_list'])) {

        $form->setElementsAndObjectValue($_POST);
        if ($form->isValid()) {
          $form->getModelobject()->save();
          $_SESSION['flash_message'] = "Gegevens opgeslagen";
          if (isset($_POST['go_list'])) {
            ResponseHelper::redirect(reconstructQueryAdd());
          }
          ResponseHelper::redirect(reconstructQuery());
        }

      }

      $this->form = $form;
    }

    public function executeDamagedelete() {
      $damage = Damage::find_by_id($_GET["id"]);
      if ($damage) {
        $damage->destroy();
        MessageFlashCoordinator::addMessage("Item verwijderd");
      }
      ResponseHelper::redirect(reconstructQueryAdd());
    }

    public function executeDamagestats() {

      if (!isset($_SESSION["stat_from"])) $_SESSION["stat_from"] = '';
      if (!isset($_SESSION["stat_to"])) $_SESSION["stat_to"] = '';
      if (!isset($_SESSION["stat_employee"])) $_SESSION["stat_employee"] = '';

      if (isset($_POST["stat_from"])) {
        $_SESSION["stat_from"] = $_POST["stat_from"];
        $_SESSION["stat_to"] = $_POST["stat_to"];
        $_SESSION["stat_employee"] = $_POST["stat_employee"];
      }

      $query = "WHERE 1 ";
      if ($_SESSION["stat_from"] != "" && $_SESSION["stat_to"] != "") {
        $query .= "AND insertTS>='" . getTSFromStr($_SESSION["stat_from"]) . "' AND insertTS<='" . getTSFromStr($_SESSION["stat_to"]) . "' ";
      }
      if ($_SESSION["stat_employee"] != "") {
        $query .= "AND employeeId=" . $_SESSION["stat_employee"] . " ";
      }
      $query .= "ORDER BY insertTS DESC";
      $damage_quots = DamageQuotation::find_all($query);

      $quotIds = [];
      foreach ($damage_quots as $dq) {
        $quotIds[$dq->quotationId] = $dq->quotationId;
      }
      $quotations = AppModel::mapObjectIds(Quotations::find_all_by(["quotationId" => $quotIds]), "quotationId");
      foreach ($damage_quots as $dq) {
        $dq->quotation = $quotations[$dq->quotationId];
      }

      $damages = AppModel::mapObjectIds(Damage::find_all("ORDER BY name_nl"));
      $employees = AppModel::mapObjectIds(ProductionEmployees::find_all_by(["working" => 1], "ORDER BY name"), "employeeId");

      $this->damages = $damages;
      $this->damage_stats = $damage_quots;
      $this->employees = $employees;
    }

    public function executeDamagequotationdelete() {
      $damage = DamageQuotation::find_by_id($_GET["qid"]);
      if ($damage) {
        $damage->destroy();
        MessageFlashCoordinator::addMessage("Item verwijderd");
      }
      ResponseHelper::redirect(reconstructQueryAdd());
    }


    public function executeSettings() {
      $settings = [];
      $descriptions['deliveryweeks'] = 'Standaard afleverweken';

      $settings = AppModel::mapObjectIds(Setting::find_all_by(["code" => "deliveryweeks"]), "code");

      if (!isset($settings['deliveryweeks'])) {
        $setting = new Setting();
        $setting->code = 'deliveryweeks';
        $setting->value = 4;
        $setting->save();
        $settings[$setting->code] = $setting;
      }

      $settings_b1mo = Settings::find_all();

      if (isset($_POST['go'])) {
        foreach ($settings_b1mo as $setting) {
          $setting->setting = $_POST[$setting->name];
          $setting->save();
        }
      }

      $this->settings = $settings;
      $this->descriptions = $descriptions;
      $this->settings_b1mo = $settings_b1mo;
      parent::executeSettings();
    }

    public function executeStatusoverview() {
      $this->stati = Status::find_all("ORDER BY statusId");
    }

    public function executePushnotifications(): void {
      $this->deviceTokens = AppModel::mapObjectIds(PushNotification::getAllTokensWithUsernames(), 'user_id');
    }

    public function executeSaveToken(): void {
      if (empty($_POST['token'])) ResponseHelper::redirectAlertMessage("Geen token opgegeven.", reconstructQueryAdd());
      PushNotification::saveToken($_POST['token'], $_SESSION['userObject']->id);

      ResponseHelper::redirectMessage("Nieuwe token is gekoppeld aan huidige gebruiker.", reconstructQueryAdd());
    }

    public function executeDeletePushnotificationToken(): void {
      $tokenId = DbHelper::escape($_GET['tokenId']);
      if (empty($tokenId)) ResponseHelper::redirectAlertMessage(reconstructQueryAdd());

      $deleted = PushNotification::deleteToken($tokenId);
      if ($deleted) ResponseHelper::redirectMessage("Token is succesvol ontkoppeld.", reconstructQueryAdd());

      ResponseHelper::redirectAlertMessage("Er is iets misgegaan. Token is reeds al verwijderd of bestaat niet.", reconstructQueryAdd());
    }
  }

