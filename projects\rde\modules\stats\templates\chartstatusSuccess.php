<h3>Meters per status</h3>
Hieronder vind u een overzicht van het aantal meters per status. Het aantal meters bij status offerte is ontdubbeld.
<Br/>
<div class="filter">
  Filter:
  <form>
    <select name="type" id="chartstatus_type">
      <option value="">Selecteer type...</option>
      <?php foreach(Stones::TYPES as $mk=>$mname): ?>
        <option value="<?php echo $mk ?>" <?php writeIfSelectedVal($type,$mk); ?>><?php echo $mname ?></option>
      <?php endforeach; ?>
    </select>
    <select name="material" id="chartstatus_material">
      <option  value="">Selecteer materiaal...</option>
      <?php foreach(Stones::MATERIALS as $mk=>$mname): ?>
        <option value="<?php echo $mk ?>" <?php writeIfSelectedVal($material,$mk); ?>><?php echo $mname ?></option>
      <?php endforeach; ?>
    </select>
    <select name="year" id="chartstatus_year">
<!--      <option value="">Selecteer jaar...</option>-->
      <?php for($tel=date("Y");$tel>=2014;$tel--): ?>
        <option value="<?php echo $tel ?>" <?php writeIfSelectedVal($year,$tel); ?>><?php echo $tel ?></option>
      <?php endfor; ?>
    </select>
  </form>
</div>
<div class="rdechart" id="chartstatus_chart"></div>
<?php
  $bystatus = [
    "offerte"=>0,
    "productie"=>0,
    "gefactureerd"=>0,
    "totaal"=>0,
  ];
  $inproductie = 0;
  foreach($chartdata["data"] as $value) {
    $bystatusid[$value["statusId"]] = $value;
    if($value["statusId"]==10) {
      $bystatus["offerte"] += $value["value"];
    }
    elseif($value["statusId"]>10 && $value["statusId"]<60) {
      $bystatus["productie"] += $value["value"];
    }
    else {
      $bystatus["gefactureerd"] += $value["value"];
    }
    $bystatus["totaal"] += $value["value"];
  }
?>
<table>
  <tr>
    <td style="font-weight: bold;">Offerte</td>
    <td style="text-align: right;"><?php echo number_format($bystatus["offerte"],0,"",".") ?></td>
  </tr>
  <tr>
    <td style="font-weight: bold;">In productie</td>
    <td style="text-align: right;"><?php echo number_format($bystatus["productie"],0,"",".") ?></td>
  </tr>
  <tr>
    <td style="font-weight: bold;">Gefactureerd/betaald</td>
    <td style="text-align: right;"><?php echo number_format($bystatus["gefactureerd"],0,"",".") ?></td>
  </tr>
  <tr>
    <td style="font-weight: bold;">Totaal</td>
    <td style="text-align: right;"><?php echo number_format($bystatus["totaal"],0,"",".") ?></td>
  </tr>
</table>

<script>
  $(document).ready(function() {
    createBarchart("chartstatus_chart", <?php echo json_encode($chartdata) ?>);

    $("#chartstatus_material,#chartstatus_year,#chartstatus_type").change(function() {
      getChart("chartstatus");
    })

  });
</script>