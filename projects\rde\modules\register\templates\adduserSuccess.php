<section>

  <div class="contenttxt">
    <h1>Nieuwe gebruiker aanmaken</h1>
    Op deze pagina kunt u een nieuwe collega gebruiker aanmaken.<br/><br/>
    De nieuwe gebruiker:
    <ul>
      <li>Logt in onder en voor rekening van hetzelfde bedrijf als u.</li>
      <li><PERSON><PERSON><PERSON><PERSON> dezelfde korting als welke u nu hebt.</li>
      <li>Kan van u en uw collega's de offertes inzien en bewerken.</li>
    </ul>
  </div>

  <form method="post" id="basketform">

    <?php writeErrors($errors, true) ?>

    <div class="form-row">
      <label for="gender" class="col-4 col-form-label"><?php echo __("Aanhef") ?> <span class="form-arterisk">*</span> </label>
      <div class="col-4-3">
        <label style="float:left;">
          <input type="radio" class="form-radio form-check-inline" name="gender" value="male" required <?php writeIfCheckedVal($user->gender, 'male') ?>/>
          <?php echo __("Dhr.") ?>
        </label>
        <label style="float: left;padding-left: 15px;">
          <input type="radio" class="form-radio form-check-inline" name="gender" value="female" required <?php writeIfCheckedVal($user->gender, 'female') ?>/>
          <?php echo __("Mevr.") ?>
        </label>
      </div>
    </div>

    <div class="form-row">
      <label for="firstName" class="col-4 col-form-label"><?php echo __("Voornaam") ?> <span class="form-arterisk">*</span> </label>

      <div class="col-4-3">
        <input type="text" class="form-input" name="firstName" placeholder="<?php echo __("Voornaam") ?>" value="<?php echo displayAsHtml($user->firstName) ?>" required maxlength="50"/>
      </div>
    </div>

    <div class="form-row">
      <label for="lastName" class="col-4 col-form-label"><?php echo __("Achternaam") ?> <span class="form-arterisk">*</span> </label>

      <div class="col-4-3">
        <input type="text" class="form-input" name="lastName" placeholder="<?php echo __("Achternaam") ?>" value="<?php echo displayAsHtml($user->lastName )?>" required/>
      </div>
    </div>

    <div class="form-row">
      <label for="phone" class="col-4 col-form-label"><?php echo __("Telefoonnummer") ?> <span class="form-arterisk">*</span> </label>
      <div class="col-4-3">
        <input type="text" class="form-input" name="phone" value="<?php echo displayAsHtml($user->phone) ?>" size="25" maxlength="15" placeholder="<?php echo __("Telefoonnummer") ?>" required/>
      </div>
    </div>

    <div class="form-row">
      <label for="mobile" class="col-4 col-form-label"><?php echo __("Mobiel nummer") ?></label>
      <div class="col-4-3">
        <input type="text" class="form-input" name="mobile" value="<?php echo displayAsHtml($user->mobile) ?>" size="25" maxlength="15" placeholder="<?php echo __("Mobiel nummer") ?>"/>
      </div>
    </div>

    <div class="form-row">
      <label for="email" class="col-4 col-form-label">
        <?php echo __("E-mail") ?>
        <span class="form-arterisk">*</span>
      </label>
      <div class="col-4-3">
        <input type="email" class="form-input" name="email" value="<?php echo escapeForInput($user->email) ?>" size="40" maxlength="150" placeholder="<?php echo __("E-mail") ?>" required/>
      </div>
    </div>

    <div class="form-row">
      <label for="password1" class="col-4 col-form-label"><?php echo __("Wachtwoord") ?> <span class="form-arterisk">*</span></label>
      <div class="col-4-3">
        <input type="password" class="form-input" name="password1" id="password1" value="<?php echo escapeForInput($user->password) ?>" size="30" maxlength="50" placeholder="<?php echo __("Wachtwoord") ?>" required/>
      </div>
    </div>

    <div class="form-row">
      <label for="password2" class="col-4 col-form-label"><?php echo __("Wachtwoord bevestig") ?> <span class="form-arterisk">*</span></label>
      <div class="col-4-3">
        <input type="password" class="form-input" name="password2" id="password2" value="<?php echo escapeForInput($user->password) ?>" size="30" maxlength="50" placeholder="<?php echo __("Wachtwoord bevestig") ?>" required/>
      </div>
    </div>



    <div class="form-row">
      <div class="col-4 responsive-hide">&nbsp;</div>
      <div class="col-4-3">
        <input type="submit" value="<?php echo __("Aanmaken") ?>" class="btn btn-primary" name="go" id="registeren"/>
      </div>
    </div>

  </form>

</section>

<script type="text/javascript">
  $(document).ready(function () {

    jQuery.extend(jQuery.validator.messages, {
      required: "<?php echo __("Dit veld is verplicht") ?>",
      email: "<?php echo __("Voer een geldig emailadres in.") ?>",
      equalTo: "<?php echo __("Voer 2 keer dezelfde waarde in.") ?>",
      minlength: "<?php echo __("Voer minimaal {0} karakters in, met minimaal 1 hoofdletter, 1 kleine letter en 1 cijfer" ) ?>"
    });

    var basketform = $('#basketform').validate( {
      rules: {
        password1: {
          minlength: 8
        },
        password2: {
          equalTo: "#password1"
        }
      }
    });

  });
</script>