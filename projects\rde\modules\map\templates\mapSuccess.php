<html>
<head>
  <title><PERSON><PERSON><PERSON><PERSON></title>

  <link href="//fonts.googleapis.com/css?family=Open+Sans:300,400,600,700" rel="stylesheet" type="text/css"/>
  <link href="//fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet"/>

  <script type="text/javascript" src="//cdn.jsdelivr.net/es6-promise/latest/es6-promise.auto.min.js"></script>

  <?php if (file_exists($site->getTemplateDir() . "dist/")): //project specific css/js dist folder   ?>
    <?php echo TemplateHelper::includeJavascript($site->getTemplateUrl() . 'dist/libraries', true); ?>
    <?php echo TemplateHelper::includeStylesheet($site->getTemplateUrl() . 'dist/libraries', true); ?>
    <?php echo TemplateHelper::includeJavascript($site->getTemplateUrl() . '/dist/main', true); ?>
    <?php echo TemplateHelper::includeStylesheet($site->getTemplateUrl() . '/dist/main', true); ?>
  <?php else: ?>
    <?php echo TemplateHelper::includeJavascript('/gsdfw/projects/default/templates/backend2/dist/libraries', true); ?>
    <?php echo TemplateHelper::includeStylesheet('/gsdfw//projects/default/templates/backend2/dist/libraries', true); ?>
    <?php echo TemplateHelper::includeJavascript('/gsdfw//projects/default/templates/backend2/dist/main', true); ?>
    <?php echo TemplateHelper::includeStylesheet('/gsdfw//projects/default/templates/backend2/dist/main', true); ?>
  <?php endif; ?>

  <?php echo TemplateHelper::includeJavascript('/gsdfw/includes/jsscripts/popper', true) ?>

  <?php echo TemplateHelper::includeJavascript('/gsdfw/includes/jsscripts/general', true) ?>

  <?php if (file_exists($site->getTemplateDir() . 'js/project.js')): ?>
    <?php echo TemplateHelper::includeJavascript($site->getTemplateUrl() . 'js/project', false) ?>
  <?php endif; ?>

  <?php if (file_exists($site->getTemplateDir() . 'style/style.css')): ?>
    <?php echo TemplateHelper::includeStylesheet($site->getTemplateUrl() . 'style/style', false) ?>
  <?php endif; ?>

</head>
<body>
    <div id="app"></div>

    <?php if (DEVELOPMENT): ?>
      <script type="module" src="http://localhost:5173/projects/rde/templates/map/src/main.js"></script>
    <?php else: ?>
      <?php echo TemplateHelper::includeJavascript('/projects/rde/templates/map/assets/index.min'); ?>
      <?php echo TemplateHelper::includeStylesheet('/projects/rde/templates/map/assets/index.min'); ?>
    <?php endif; ?>

    <script type="text/javascript">

      <?php
      $mapConfig = [
      ];
      ?>

      window.gsdMapConfig = <?php echo json_encode($mapConfig) ?>;

    </script>

    <style>
      body, html {
        height: 100%;
        padding: 0;
        margin: 0;
      }

    </style>
</body>
</html>