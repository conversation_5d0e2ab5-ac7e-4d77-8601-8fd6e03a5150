<?php

  namespace domain\elements\service\helpers;

  use OrderElements;
  use QuotationsExtra;

  class ElementStoneAmount {
    public static function getStoneAmount(OrderElements $element, QuotationsExtra $quotationsExtra) {
      //-- soms wijkt deze maat af van de standaard, omdat ze deze kunnen wijzigen bij productie
      $stoneSizeWidthQuoteExtra = $quotationsExtra->stoneSizeWidth;
      $stoneSizeWidthQuoteExtraPlus10 = $stoneSizeWidthQuoteExtra * 10;
      $elementDB = $element->elementLength;

      $verdeling = $stoneSizeWidthQuoteExtraPlus10 + 3.5;
      $verdelingMin = $stoneSizeWidthQuoteExtraPlus10 + 3;
      $verdelingMax = $stoneSizeWidthQuoteExtraPlus10 + 4.5; // LET OP! Voeg erbij!

      $iElementVoeg = $elementDB + 3.5;

      // LET OP! Voeg erbij!
      $iStenenMin = ceil($iElementVoeg / $verdelingMin);
      $iStenenStd = ceil($iElementVoeg / $verdeling);
      $iStenenMax = ceil($iElementVoeg / $verdelingMax);

      $aAantalStenen = [];
      $aAantalStenen[] = $iStenenMin;
      $aAantalStenen[] = $iStenenStd;
      $aAantalStenen[] = $iStenenMax;

      return min($aAantalStenen);
    }
  }