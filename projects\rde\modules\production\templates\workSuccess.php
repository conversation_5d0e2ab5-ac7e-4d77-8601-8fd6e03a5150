<?php TemplateHelper::includePartial("_tabs.php",'production') ?>

<form method="post">
  <label>Selecteer:</label>
  <input type="submit" name="filter_has_date" id="filter_has_date" value="Toon alleen met gps route">
  <input type="submit" name="filter_both" id="filter_both" value="Toon beide">
</form>

<?php
  /** @var Quotations $quotation */
  foreach ($quotations_35 as $prod_date => $quotation_35): ?>
    <table class="default_table">
      <tr class="dataTableHeadingRow td-borders">
        <td class="production-date" colspan="10"><?php echo $prod_date ?> <?php if (!empty($quotation_35['quotationIds'])) echo ' - ' . BtnHelper::getPrintPDF('?action=printpdfmultiple&quotation_ids=' . $quotation_35['quotationIds'], __('Bekijk pdf'), '') ?></td>
      </tr>
      <tr class="dataTableHeadingRow row_head td-borders">
        <td>Offertenummer</td>
        <td>Naam</td>
        <td>Meters K</td>
        <td>Meters N</td>
        <td>Meters B</td>
        <td>Meters I</td>
        <td>Merk</td>
        <td>Kleur</td>
        <td>Afmeting</td>
        <td>Eindstenen</td>
        <td>Verstek</td>
        <td>Kleur voeg</td>
      </tr>
      <?php foreach ($quotation_35 as $item): ?>
        <?php if (is_float($item) || is_string($item)) continue; ?>
        <tr style="<?php if ($item->statusId == Status::STATUS_CHECKED) echo "background-color: red; color: white;" ?>" class="trhover dataTableRow td-borders">
          <td><a href="<?php echo PageMap::getUrl("M_RDE_ORDERS_GENERAL").'?id=' . $item->quotationId ?>"><?php echo $item->getQuotationNumberFull() ?></a></td>
          <td><a href="<?php echo PageMap::getUrl("M_BEDRIJVENGIDS_EDIT").'?companyid='.$item->companyId ?>"><?php echo $item->company_name ?></a></td>
          <td><?php echo $item->meters_k ?: '' ?></td>
          <td><?php echo $item->meters_n ?: '' ?></td>
          <td><?php echo $item->meters_b ?: '' ?></td>
          <td><?php echo $item->meters_i ?: '' ?></td>
          <td><?php echo $item->stone_brand ?></td>
          <td><?php echo $item->stone_color ?></td>
          <td><?php echo $item->stone_size ?></td>
          <td><?php echo $item->has_endstone ?></td>
          <td><?php echo $item->has_verstek == "true" ? "Ja" : "" ?></td>
          <td><?php echo $item->seam_color ?></td>
        </tr>
      <?php endforeach; ?>
      <tr class="trhover dataTableRow td-borders">
        <td class="bold">Totaal aantal keramische meters:</td>
        <td></td>
        <td></td>
        <td class="bold"><?php echo $quotation_35['meters_total_k'] ?></td>
      </tr>
    </table>
  <?php endforeach; ?>

<?php if ($quotations_30): ?>
<table class="default_table">
    <tr class="dataTableHeadingRow td-borders">
      <td class="production-date" colspan="10">Geen datum aanwezig in gpsbuddy_routes.date</td>
    </tr>
    <tr class="dataTableHeadingRow row_head td-borders">
      <td>Offertenummer</td>
      <td>Naam</td>
      <td>Meters K</td>
      <td>Meters N</td>
      <td>Meters B</td>
      <td>Meters I</td>
      <td>Merk</td>
      <td>Kleur</td>
      <td>Afmeting</td>
      <td>Eindstenen</td>
      <td>Verstek</td>
      <td>Kleur voeg</td>
    </tr>
  <?php foreach ($quotations_30 as $quotation_30): ?>
    <?php if (is_float($quotation_30)) continue; ?>
      <tr class="trhover dataTableRow td-borders">
        <td><?php echo $quotation_30->quotationNumber ?></td>
        <td><?php echo $quotation_30->company_name ?></td>
        <td><?php echo $quotation_30->meters_k ?: '' ?></td>
        <td><?php echo $quotation_30->meters_n ?: '' ?></td>
        <td><?php echo $quotation_30->meters_b ?: '' ?></td>
        <td><?php echo $quotation_30->meters_i ?: '' ?></td>
        <td><?php echo $quotation_30->stone_brand ?></td>
        <td><?php echo $quotation_30->stone_color ?></td>
        <td><?php echo $quotation_30->stone_size ?></td>
        <td><?php echo $quotation_30->has_endstone ?></td>
        <td><?php echo $quotation_30->has_verstek == "true" ? "Ja" : "" ?></td>
        <td><?php echo $quotation_30->seam_color ?></td>
      </tr>
  <?php endforeach; ?>
    <tr class="trhover dataTableRow td-borders">
      <td class="bold">Totaal aantal keramische meters:</td>
      <td></td>
      <td></td>
      <td class="bold"><?php echo $quotations_30['meters_total_k'] ?></td>
    </tr>
</table>
<?php endif; ?>

<style>
  .td-borders > td {
    border: 1px solid var(--stone-color-100);
  }

  .production-date {
    height: fit-content;
  }

  .default_table {
    margin-bottom: 20px;
  }

  .row_head > td {
    background-color: white !important;
  }

  .bold {
    font-weight: bold;
  }
</style>