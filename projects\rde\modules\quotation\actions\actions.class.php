<?php

  use domain\quotations\service\GetStoneCategoryInfo;

  require_once 'quotationSplitActions.php';
  require_once 'quotationStep0Actions.php';
  require_once 'quotationStep1Actions.php';
  require_once 'quotationStep2Actions.php';
  require_once 'quotationStep3Actions.php';
  require_once 'quotationStep4Actions.php';
  require_once 'quotationStep5Actions.php';
  require_once 'quotationStep6Actions.php';
  require_once 'quotationStep7Actions.php';

  class quotationRdeActions extends gsdActions {

    use quotationSplitActions;
    use quotationStep0Actions;
    use quotationStep1Actions;
    use quotationStep2Actions;
    use quotationStep3Actions;
    use quotationStep4Actions;
    use quotationStep5Actions;
    use quotationStep6Actions;
    use quotationStep7Actions;

    public string $wizardurl = "";

    public function preExecute() {
      $this->site = $_SESSION['site'];
      $this->wizardurl = PageMap::getUrl(208);
      if (is_numeric($this->pageId)) {
        $page = Page::getPageAndContent($this->pageId, $_SESSION['lang']);
        $this->seo_title = $page->content->getSeoTitle();
        $this->seo_description = $page->content->getSeoDescription();
        $this->page = $page;
      }

      if (!isset($_SESSION["browser"])) {
        $browser = new Wolfcast\BrowserDetection();
        $_SESSION["browser"]["name"] = $browser->getName();
        $_SESSION["browser"]["version"] = $browser->getVersion();
      }
      if (isset($_SESSION["browser"]) && $_SESSION["browser"]["name"] == "Internet Explorer" && $_SESSION["browser"]["version"] <= 10) {
        $this->browserversion_message = true;
      }
      $this->cats = Category::getShopCats();

    }


    private function checkAuthentication() {
      if (!isset($_SESSION["userObject"])) { //uitgelogd
        ResponseHelper::redirect301($this->wizardurl);
      }
    }

    public function executeStart() {
      if (isset($_GET["adminhash"])) {
        $decrypt = EncryptionHelper::decrypt($_GET["adminhash"], "#34tkdfQQss");
        if ($decrypt == 'ADMINUSER#15xx' . date('Ymd')) {
          $_SESSION["userAdmin"] = true;
          $_SESSION['flash_message'] = __("Ingelogd als ADMIN.");
        }
        ResponseHelper::redirect($this->wizardurl);
      }
      elseif (isset($_GET["adminclear"])) {
        unset($_SESSION["userAdmin"]);
        $_SESSION['flash_message'] = __("Uitgelogd als ADMIN.");
        ResponseHelper::redirect($this->wizardurl);
      }

      //    $userhash = urlencode(User::encrypt("RDEUSER#824yy".date("Ydm")));
      //    $email = MAIL_BART;

      if (isset($_GET['userhash']) && isset($_GET['email'])) { //word alleen aangeroepen vanaf beheer.
        if (User::decrypt($_GET['userhash']) == 'RDEUSER#824yy' . date("Ydm")) {

          if (isset($_SESSION["userObject"])) {
            SandboxUsers::stopSession();
          }

          $user = SandboxUsers::find_by(["email" => $_GET['email'], "blocked" => "false"]);
          if ($user == null) {
            MessageFlashCoordinator::addMessageAlert("Het account van " . $_GET['email'] . " is niet gevonden.");
            ResponseHelper::redirect(reconstructQueryAdd());
          }
          if ($user->blockedbyadmin == "true") {
            MessageFlashCoordinator::addMessageAlert("Het account van " . $_GET['email'] . " is geblokkeerd.");
            ResponseHelper::redirect(reconstructQueryAdd());
          }

          SandboxUsers::startSession($user);
          $_SESSION["userAdmin"] = true; //direct ook als admin
          if (isset($_GET["redirect"])) {
            $_SESSION['flash_message'] = __("Ingelogd als klant " . $_SESSION['userObject']->getNaam() . " en doorgestuurd.");
            ResponseHelper::redirect($_GET["redirect"]);
          }
          else {
            $_SESSION['flash_message'] = __("U bent ingelogd als " . $_SESSION['userObject']->getNaam());
            if (isset($_GET["offerte"]) && is_numeric($_GET["offerte"])) {
              if (isset($_GET["extraproducts"]) && $_GET["extraproducts"] == 1) {
                $_SESSION['flash_message'] .= " en direct doorgestuurd naar het extraproducten tabblad.";
                ResponseHelper::redirect($this->wizardurl . '?action=extraproductsedit&id=' . $_GET["offerte"]);
              }
              elseif (isset($_GET["orderconfirm"]) && $_GET["orderconfirm"] == 1) {
                $_SESSION['flash_message'] .= " en direct doorgestuurd naar pagina bestellen.";
                ResponseHelper::redirect($this->wizardurl . '?action=confirm&id=' . $_GET["offerte"]);
              }
              else {
                $_SESSION['flash_message'] .= " en direct doorgestuurd naar de offerte.";
                ResponseHelper::redirect($this->wizardurl . '?action=edit&id=' . $_GET["offerte"]);
              }
            }
            else {
              ResponseHelper::redirect(reconstructQueryAdd());
            }
          }

        }
        else {
          MessageFlashCoordinator::addMessageAlert("Ongeldige url");
          ResponseHelper::redirect(reconstructQueryAdd());
        }
      }

      if (isset($_SESSION['userObject'])) {
        ResponseHelper::redirect(PageMap::getUrl(350)); //main
      }
      else {
        ResponseHelper::redirect($this->wizardurl . "?action=wizard" . (isset($_GET["variant"]) ? "&variant=" . $_GET["variant"] : '')); //inloggen
      }

    }

    public function executeMain() {
      $this->checkAuthentication();
      $this->imessageshome = Imessage::getMyMessages($_SESSION['userObject'], 1);
      $this->template = "mainSuccess.php";
    }

    public function executeLogoff() {
      if (isset($_SESSION["userObject"])) {
        SandboxUsers::stopSession();
      }
      if (isset($_SESSION["userAdmin"])) { // ook admin uitloggen
        unset($_SESSION["userAdmin"]);
      }
      $_SESSION['flash_message'] = "U bent uitgelogd.";
      ResponseHelper::redirect($this->wizardurl);
    }

    public function executeWizard() {

      if (isset($_GET["clear"])) {
        unset($_SESSION["wizard"]);
        ResponseHelper::redirect($this->wizardurl . '?action=wizard');
      }
      if (isset($_GET["brandId"])) {
        $this->openByBrandidColoridVariant($_GET["brandId"], $_GET["colorId"], $_GET["variant"], $_GET["stoneId"] ?? false);
      }
      elseif (isset($_GET["variant"])) {
        $this->openByVariant();
      }
      elseif (isset($_GET["stoneId"])) {
        $this->openByStoneId($_GET["stoneId"]);
      }

      $this->hasVerstek = isset($_SESSION["wizard"]['elements']) ? $this->hasVerstek($_SESSION["wizard"]['quotation'], $_SESSION["wizard"]['elements']) : false;

      if (!isset($_GET['step']) || $_GET['step'] == 0) {
        $this->executeWizardstep0();
        $this->stepname = "step0";
        $this->template = "vuespaSuccess.php";
      }
      elseif ($_GET['step'] == 1) {
        $this->executeWizardstep1();
        $this->stepname = "step1";
        $this->template = "vuespaSuccess.php";
      }
      elseif ($_GET['step'] == 2) {
        $this->executeWizardstep2();
        $this->template = "vuespaSuccess.php";
      }
      elseif ($_GET['step'] == 3) {
        $this->executeWizardstep3();
        $this->stepname = "step3";
        $this->template = "vuespaSuccess.php";
      }
      elseif ($_GET['step'] == 4) {
        $this->executeWizardstep4();
        $this->template = "wizardstep" . $this->step . "Success.php";
      }
      elseif ($_GET['step'] == 5) {
        $this->executeWizardstep5();
      }
      elseif ($_GET['step'] == 6) {
        $this->executeWizardstep6();
        $this->template = "wizardstep" . $this->step . "Success.php";
      }
      elseif ($_GET['step'] == 7) {
        $this->executeWizardstep7();
        $this->template = "wizardstep" . $this->step . "Success.php";
      }

    }

    private function openByVariant() {
      $variants = [
        'keramische_raamdorpel'    => 4,
        'keramische_spekbanden'    => 5,
        'keramische_muurafdekker'  => 6,
        'beton_raamdorpel'         => 7,
        'beton_spekbanden'         => 8,
        'beton_muurafdekker'       => 9,
        'natuursteen_raamdorpel'   => 10,
        'natuursteen_spekbanden'   => 11,
        'natuursteen_muurafdekker' => 12,
        'natuursteen_vensterbank'  => 13,
      ];

      if (!isset($variants[$_GET["variant"]])) {
        MessageFlashCoordinator::addMessageAlert(__("Onbekende offerte variant: " . $_GET["variant"]));
        ResponseHelper::redirect(reconstructQueryAdd());
      }

      if (isset($_SESSION["wizard"]['quotation']) && $_SESSION["wizard"]['quotation']->projectName != "") {
        //er worden een andere variant geopend terwijl er nog een offerte openstaat.
        $this->confirmopenofferte();
        return;
      }
      //er is geen offerte open, start nieuwe configuratie met variant geselecteerd
      $quotation = $this->initWizard();
      $quotation->stoneCategoryId = $variants[$_GET["variant"]];
      $_SESSION["wizard"]['quotation'] = $quotation;
      ResponseHelper::redirect(reconstructQuery(["step", "variant"]) . "step=0");
    }

    /**
     * @param $brandId
     * @param $colorId
     * @param $variant
     * @param $stoneId : dit is niet de geselecteerde steen, maar dit is een steen van deze dikte,
     * zodat we de juiste dikte kunnen selecteren.
     * @return void
     */
    private function openByBrandidColoridVariant($brandId, $colorId, $variant, $stoneId) {
      $brand = StoneBrands::find_by(["brandId" => $brandId]);
      if (!$brand) ResponseHelper::redirectNotFound("Merk niet gevonden.");
      $color = StoneColors::find_by(["colorId" => $colorId]);
      if (!$color) ResponseHelper::redirectNotFound("Kleur niet gevonden.");


      //start nieuwe configuratie
      $quotation = $this->initWizard();
      $quotation->projectName = "Vul hier uw projectnaam in...";
      //die("hier verder. bepalen categorie");

      if ($stoneId) {
        $stone = Stones::find_by(["stoneId" => $stoneId]);
        if ($stone) {
          $quotation->sizeId = $stone->sizeId;
        }
      }
      $quotation->stoneCategoryId = $variant;
      $quotation->brandId = $brand->brandId;
      $quotation->colorId = $color->colorId;
      $_SESSION["wizard"]['quotation'] = $quotation;
      ResponseHelper::redirect(reconstructQueryAdd() . "action=wizard&step=1");
    }

    private function openByStoneId($stoneId) {
      $stone = Stones::find_by(["stoneId" => $stoneId]);
      if (!$stone) ResponseHelper::redirectNotFound("Steen niet gevonden.");
      $size = StoneSizes::find_by(["sizeId" => $stone->sizeId]);
      if (!$size) ResponseHelper::redirectNotFound("Formaat niet gevonden.");


      //start nieuwe configuratie
      $quotation = $this->initWizard();
      $quotation->projectName = "Vul hier uw projectnaam in...";
      $quotation->stoneCategoryId = $stone->category_id;
      $quotation->brandId = $stone->brandId;
      $quotation->sizeId = $stone->sizeId;
      $quotation->colorId = $stone->colorId;
      $_SESSION["wizard"]['quotation'] = $quotation;
      ResponseHelper::redirect(reconstructQueryAdd() . "action=wizard&step=1");
    }

    public function confirmopenofferte() {
      $this->template = "confirmopenofferteSuccess.php";
    }

    /**
     * @return Quotations
     */
    private function initWizard() {
      if (isset($_SESSION["wizard"]['quotation'])) {
        /** @var Quotations $quotation */
        $quotation = $_SESSION["wizard"]['quotation'];
      }
      else {
        $quotation = new Quotations();
        if (isset($_SESSION['userObject'])) {
          $quotation->userId = $_SESSION['userObject']->userId;
          $quotation->companyId = $_SESSION['userObject']->companyId;

//          $quotation_extra = new QuotationsExtra();
//          $quotation_extra->quotationAltPriceYear = date("Y").'-01-01';
//          if($_SESSION['userObject']->sms==1) {
//            $quotation_extra->sms = 1;
//            $quotation_extra->smsnumber = $_SESSION['userObject']->mobile;
//          }
//
//
//          $_SESSION["wizard"]['quotation_extra'] = $quotation_extra;

        }
        $quotation->projectName = "";
        $quotation->brandId = "";
        //tijdelijke properties
        $quotation->colorId = "";
        $quotation->sizeId = "";
        $quotation->endstone = "";
        $quotation->shorter = "";
      }
      return $quotation;
    }

    public function executeHistory() {
      $this->checkAuthentication();
      if (!isset($_SESSION["tender_search"])) {
        $_SESSION["tender_search"] = '';
      }
      if (isset($_POST["tender_search"])) {
        $_SESSION["tender_search"] = trim($_POST["tender_search"]);
      }
    }

    /**
     * Bij het bewerken van een offerte kopieren we altijd de gehele offerte
     * @return void
     * @throws Exception
     */
    public function executeEdit(): void {
      $this->checkAuthentication();
      unset($_SESSION["wizard"]);

      $quotationIn = Quotations::getById($_GET["id"]);
      if (!$quotationIn) {
        ResponseHelper::redirectAlertMessage(__("Offerte niet geopend. Offerte is niet gevonden."), reconstructQueryAdd());
      }
      if ($quotationIn->createdVia == Quotations::CREATED_VIA_WEBSHOP) {
        ResponseHelper::redirectAlertMessage(__("Dit is een webshop offerte. Deze offerte kunt u niet bewerken met de offerte wizard."), reconstructQueryAdd());
      }
      if (!$quotationIn->mayEdit()) {
        ResponseHelper::redirectAlertMessage(__("Offerte niet geopend. U heeft geen rechten om deze offerte te bewerken."), reconstructQueryAdd());
      }
      if ($quotationIn->projectType == "") {
        ResponseHelper::redirectAlertMessage(__("Deze offerte is erg oud of is maatwerk, en kunt u hierdoor niet bewerken. De PDF van deze offerte kunt u nog inzien, dus u kunt een nieuwe passende offerte aanmaken via de 'Nieuwe offerte aanmaken' knop."), reconstructQueryAdd());
      }

      if($quotationIn->quotationVersion >= 255) {
        ResponseHelper::redirectAlertMessage(__("U kunt maximaal 255 versies maken van 1 offerte. Het is niet mogelijk om deze offerte te bewerken, start met een nieuwe offerte."), reconstructQueryAdd());
      }

      //er zijn een aantal properties die ik aanmaak voor het gemak
      $stone = Stones::find_by(["stoneId" => $quotationIn->stoneId]);
      $quotation = new Quotations();
      $quotation->createdVia = $quotationIn->createdVia;
      $quotation->copyQuotationId = $quotationIn->quotationId;
      $quotation->userId = $quotationIn->userId;
      $quotation->companyId = $_SESSION["userObject"]->companyId; // altijd kijken of hij nu is gekoppeld.
      $quotation->codeId = $quotationIn->codeId;
      $quotation->statusId = Status::STATUS_NEW;
      $quotation->brandId = $quotationIn->brandId;
      $quotation->stoneId = $quotationIn->stoneId;
      $quotation->stoneCategoryId = $quotationIn->stoneCategoryId;
      //in sommige gevallen is de brandId niet gezet in de database. Fixen.
      if ($quotation->brandId == 0) {
        $quotation->brandId = $stone->brandId;
      }
      $quotation->projectName = $quotationIn->projectName;
      $quotation->projectReference = $quotationIn->projectReference;
      $quotation->endstone = $quotationIn->endstone;
      $quotation->shorter = $quotationIn->shorter;
      $quotation->street = $quotationIn->street;
      $quotation->nr = $quotationIn->nr;
      $quotation->ext = $quotationIn->ext;
      $quotation->zipcode = $quotationIn->zipcode;
      $quotation->domestic = $quotationIn->domestic;
      $quotation->country = $quotationIn->country;
      $quotation->customerNotes = $quotationIn->customerNotes;
      $quotation->deliveryNotes = $quotationIn->deliveryNotes;
      $quotation->offerteType = $quotationIn->offerteType;

      $quotation->colorId = $stone->colorId;
      $quotation->sizeId = $stone->sizeId;

      $quotation_extra_in = QuotationsExtra::find_by(["quotationId" => $quotationIn->quotationId]);
      $quotation_extra = new QuotationsExtra();
      $quotation_extra->addressDeliveryId = $quotation_extra_in->addressDeliveryId;
      if ($quotation_extra->addressDeliveryId == "0") {
        $quotation_extra->addressDeliveryId = null;
      }
      $quotation_extra->wall_thickness = $quotation_extra_in->wall_thickness;
      $quotation_extra->sms = $quotation_extra_in->sms;
      $quotation_extra->sms_delivered = $quotation_extra_in->sms_delivered;
      $quotation_extra->smsnumber = $quotation_extra_in->smsnumber;

      $custom_stone_in = QuotationsCustomStone::find_by(["quotationId" => $quotationIn->quotationId]);
      if ($custom_stone_in) {
        $custom_stone_in->from_db = false;
        $custom_stone_in->quotationId = null;
        $quotation->custom_stone = $custom_stone_in;
      }

      if ($quotation_extra->addressDeliveryId == null && $quotation->companyId != "") {
        //kijk eens of in de addressen van dit bedrijf een exact gelijk afleveradres is. Dan dit adres koppelen.
        $types = ["delivery"];
        if ($_SESSION['userObject']->company->noDelivery != 1) {
          $types[] = "visit";
        }
        $addresses = CrmAddresses::find_all_by(["companyId" => $quotation->companyId, 'type' => $types], "ORDER BY domestic, street");
        foreach ($addresses as $addres) {
          if (strcasecmp($addres->street, trim($quotation->street) == 0)
            && strcasecmp($addres->nr, trim($quotation->nr) == 0)
            && strcasecmp($addres->extension, trim($quotation->ext) == 0)
            && strcasecmp($addres->zipcode, trim($quotation->zipcode) == 0)
            && strcasecmp($addres->domestic, trim($quotation->domestic) == 0)
          ) {
            $quotation_extra->addressDeliveryId = $addres->addressId;
            break;
          }
        }
      }

      $elements = OrderElements::find_all_by(["quotationId" => $quotationIn->quotationId]);
      foreach ($elements as $element) {

        if ($stone->isNatuursteen() && $stone->isVensterbank()) {
          $element->windowsill = OrderElementWindowsill::find_by(["element_id" => $element->elementId]);
          $element->windowsill->from_db = false;
          $element->windowsill->element_id = null;
        }
        elseif ($stone->isNatuursteen() && $stone->isBalkje()) {

          $element->order_element_sizes = OrderElementSize::getByElementId($element->elementId);
          foreach ($element->order_element_sizes as $k => $oes) {
            //maak kopie formaat
            $element->order_element_sizes[$k]->id = null;
            $element->order_element_sizes[$k]->from_db = false;
            $element->order_element_sizes[$k]->element_id = null;
          }
        }

        $element->from_db = false;
        $element->elementId = null;
        $element->quotationId = null;
        $element->mitre = $element->getMitre();

        //hearClickSize overnemen van eerste element, zijn allemaal hetzelfde
        if (!isset($quotation->heartClickSize)) {
          $quotation->heartClickSize = $element->heartClickSize;
        }
      }

      $extra_products = [];
      foreach (Projects::find_all_by(["quotationId" => $quotationIn->quotationId], "AND NOT product_id IS NULL ORDER BY orderNr") as $project) {
        $key = $project->product_id;
        if ($project->glaced_left == 1) {
          $key .= "_glacedleft";
        }
        elseif ($project->glaced_right == 1) {
          $key .= "_glacedright";
        }
        $project->from_db = false;
        $project->projectId = null;
        $project->quotationId = null;
        $project->invoiceId = null;
        $extra_products[$key] = $project;
      }

      $_SESSION["wizard"]['quotation'] = $quotation;
      $_SESSION["wizard"]['quotation_extra'] = $quotation_extra;
      $_SESSION["wizard"]['elements'] = $elements;
      $_SESSION["wizard"]['extra_products'] = $extra_products;

      $_SESSION['flash_message'] = __("Offerte " . $quotationIn->getQuotationNumberFull() . " is geopend.");
      ResponseHelper::redirect($this->wizardurl . '?action=wizard');


    }

    public function executeDelete() {
      $this->checkAuthentication();
      $quot = Quotations::getById($_GET["id"]);
      if ($quot && $quot->mayDelete()) {
        $quot->flaggedForDeletion = 1;
        $quot->statusId = Status::STATUS_REMOVED;
        $quot->save();
        $_SESSION['flash_message'] = __("De offerte " . $quot->getQuotationNumberFull() . " is verwijderd.");
        ResponseHelper::redirect(reconstructQueryAdd());
      }
      $_SESSION['flash_message_red'] = __("Offerte niet verwijderd. Offerte is niet gevonden, of u heeft geen rechten om deze te verwijderen.");
      ResponseHelper::redirect(reconstructQueryAdd());
    }

    function executePdf() {

      $this->checkAuthentication();

      $quotation = Quotations::getById($_GET["id"]);
      if ($quotation && $quotation->mayView()) {
        $quotations_extra = QuotationsExtra::find_by(["quotationId" => $quotation->quotationId]);
        if (!$quotations_extra) {
          $_SESSION['flash_message_red'] = __("Offerte niet geopend. Offerte is niet gevonden, of u heeft geen rechten om deze te bekijken. [code 2]");
          ResponseHelper::redirect(reconstructQueryAdd());
        }
        $pdf = new QuotationPdf($quotation->quotationId);
        $pdf->setShow(true);
        $pdf->generatePdf();
      }
      else {
        $_SESSION['flash_message_red'] = __("Offerte niet geopend. Offerte is niet gevonden, of u heeft geen rechten om deze te bekijken.");
        ResponseHelper::redirect(reconstructQueryAdd());
      }
      ResponseHelper::exit();

    }

    function executePdfcargo() {

      $this->checkAuthentication();

      $quotation = Quotations::getById($_GET["id"]);
      if (!$quotation || !$quotation->mayView()) {
        $_SESSION['flash_message_red'] = __("Vrachtbon niet geopend. Vrachtbon is niet gevonden, of u heeft geen rechten om deze te bewerken.");
        ResponseHelper::redirect(reconstructQueryAdd());
      }

      $pr_route = GpsbuddyRde::getDeliverRoute($quotation->quotationId);
      if (!$pr_route) {
        $_SESSION['flash_message_red'] = __("Vrachtbon niet geopend. Vrachtbon is niet gevonden, of u heeft geen rechten om deze te bewerken.");
        ResponseHelper::redirect(reconstructQueryAdd());
      }

      $cr = CargoReceipt::find_by(["cargoReceiptId" => $pr_route->cargoReceiptId]);
      if (!$cr) {
        $_SESSION['flash_message_red'] = __("Vrachtbon niet geopend. Vrachtbon is niet gevonden, of u heeft geen rechten om deze te bewerken.");
        ResponseHelper::redirect(reconstructQueryAdd());
      }

      $filepath_gen = CargoReceipt::getPdf($cr->cargoReceiptId);
      $filename_new = $quotation->getQuotationNumberFull() . '.pdf';

      header('Content-Type: application/pdf');
      header('Content-Disposition: inline; filename="' . $filename_new . '"');
      header('Cache-Control: private, max-age=0, must-revalidate');
      header('Pragma: public');
      echo file_get_contents($filepath_gen);

      ResponseHelper::exit();
    }


    function executePdfproductionstate() {

      $this->checkAuthentication();

      if (DEVELOPMENT) {
        //nieuwe productiestaat PDF
        $pdf = new ProductionReceiptPdf(DbHelper::escape($_GET['id']));
        $pdf->generate();
      }
      else {
        //oude productiestaat PDF
        $quotation = Quotations::getById($_GET["id"]);
        if (!$quotation || !$quotation->mayView()) {
          $_SESSION['flash_message_red'] = __("Productiestaat PDF niet geopend. Productiestaat niet gevonden, of u heeft geen rechten om deze te bewerken.");
          ResponseHelper::redirect(reconstructQueryAdd());
        }

        $pr_rde = GpsbuddyRde::find_by(["quotationId" => $quotation->quotationId]);
        if (!$pr_rde) {
          $_SESSION['flash_message_red'] = __("Productiestaat PDF niet geopend. Productiestaat is niet gevonden, of u heeft geen rechten om deze te bewerken.");
          ResponseHelper::redirect(reconstructQueryAdd());
        }

        $pr_route = GpsbuddyRoutes::find_by(["routeId" => $pr_rde->routeId]);
        if (!$pr_route) {
          $_SESSION['flash_message_red'] = __("Productiestaat PDF niet geopend. Productiestaat is niet gevonden, of u heeft geen rechten om deze te bewerken.");
          ResponseHelper::redirect(reconstructQueryAdd());
        }

        $cr = CargoReceipt::find_by(["cargoReceiptId" => $pr_route->cargoReceiptId]);
        if (!$cr) {
          $_SESSION['flash_message_red'] = __("Productiestaat PDF niet geopend. Productiestaat is niet gevonden, of u heeft geen rechten om deze te bewerken.");
          ResponseHelper::redirect(reconstructQueryAdd());
        }

        if ($cr->cargoReceiptType != 'standard') {
          $_SESSION['flash_message_red'] = __("Productiestaat PDF niet geopend. Productiestaat is niet gevonden, of u heeft geen rechten om deze te bewerken.");
          ResponseHelper::redirect(reconstructQueryAdd());
        }

        $filepath_gen = CargoReceipt::getProductionstaatPdf([$quotation->quotationId]);
        $filename_new = $quotation->getQuotationNumberFull() . '_productiestaat.pdf';

        header('Content-Type: application/pdf');
        header('Content-Disposition: inline; filename="' . $filename_new . '"');
        header('Cache-Control: private, max-age=0, must-revalidate');
        header('Pragma: public');
        echo file_get_contents($filepath_gen);
        ResponseHelper::exit();
      }

    }


    public function executeOpen() {
      $quotation = false;
      if (isset($_GET['hash'])) {
        $hash_decode = Quotations::decrypt($_GET['hash']);
        $parts = explode("_", $hash_decode);
        if (count($parts) == 2) {
          $quotationId = $parts[0];
          $userId = $parts[1];
          $quotation = Quotations::find_by(["quotationId" => $quotationId, "userId" => $userId, "flaggedForDeletion" => 0]);
        }
      }

      if (!$quotation) {
        $_SESSION['flash_message_red'] = __("Offerte niet gevonden. Deze is reeds besteld, bestaat niet meer of u bent geen eigenaar.");
        ResponseHelper::redirect($this->wizardurl);
      }

      //gevonden. Inloggen en openen
      $user = SandboxUsers::find_by(['userId' => $quotation->userId]);
      if (isset($_SESSION['userObject']) && $_SESSION['userObject']->userId == $user->userId) {
        //reeds ingelogd als deze user, meteen naar confirm scherm
        ResponseHelper::redirect($this->wizardurl . '?action=confirm&id=' . $quotation->quotationId);
      }
      else {
        SandboxUsers::stopSession();
        if ($user->blockedbyadmin == "true") {
          $_SESSION['flash_message_red'] = __("Uw account is geblokkeerd. Neem contact op met Raamdorpelelementen BV als dit onjuist is.");
          ResponseHelper::redirect('/');
        }
        //doorsturen naar login scherm
        if (SandboxUsers::isAdmin()) { //admin, direct inloggen als klant
          SandboxUsers::startSession($user);
          ResponseHelper::redirect($this->wizardurl . '?action=confirm&id=' . $quotation->quotationId);
        }
        else {
          ResponseHelper::redirect($this->wizardurl . '?email=' . urlencode($user->email) . '&redirect=' . urlencode('/' . $this->wizardurl . '?action=confirm&id=' . $quotation->quotationId));
        }
      }
      ResponseHelper::exit();
    }

    public function executeConfirm() {

      $this->checkAuthentication();

      $errors = [];
      $returnurl = $this->wizardurl;
      if (isset($_GET["history"])) {
        $returnurl = PageMap::getUrl(211);
      }

      $quotation = Quotations::getById($_GET["id"]);
      if (!$quotation) {
        MessageFlashCoordinator::addMessageAlert("Offerte bestaat niet meer of u bent geen eigenaar.");
        ResponseHelper::redirect($returnurl);
      }
      if (!$quotation->mayConfirm()) {
        if ($quotation->statusId != 10) {
          MessageFlashCoordinator::addMessageAlert("De offerte " . $quotation->getQuotationNumberFull() . " is reeds omgezet in een bestelling.");
        }
        else {
          MessageFlashCoordinator::addMessageAlert("Offerte bestaat niet meer of u bent geen eigenaar.");
        }
        ResponseHelper::redirect($returnurl);
      }

      if ($quotation->createdVia == Quotations::CREATED_VIA_WEBSHOP) {
        $this->returnurl = $returnurl;
        $this->quotation = $quotation;
        $this->executeConfirmwebshop();
        return;
      }

      Context::addJavascript('/gsdfw/includes/jsscripts/vuejs/vue.3.1.5' . (DEVELOPMENT ? '' : '.min') . '.js');

      $quotation_extra = QuotationsExtra::find_by(["quotationId" => $quotation->quotationId]);
      if (!$quotation_extra) {
        $_SESSION['flash_message_red'] = __("Offerte extra gegevens is niet gevonden?");
        ResponseHelper::redirect($returnurl);
      }
      $stone = Stones::find_by(["stoneId" => $quotation->stoneId]);
      $elements = OrderElements::find_all_by(["quotationId" => $quotation->quotationId]);

      if ($quotation_extra->addressDeliveryId == null && $quotation->street != "") { //geen id, en staat ingevuld, dan nieuw selecteren.
        $quotation_extra->addressDeliveryId = "NEW";
      }
      elseif ($quotation_extra->addressDeliveryId == null) {
        $quotation_extra->addressDeliveryId = "";
      }

      $addresses = [];
      //    $addresses[] = new CrmAddresses(); //nieuwe afleveradres
      //    $addresses += CrmAddresses::find_all_by(array("companyId"=>$quotation->companyId,'type'=>["delivery","visit"]), "ORDER BY domestic, street");
      $newAdres = new CrmAddresses();
      $newAdres->addressId = "NEW";
      $addresses[] = $newAdres; //nieuwe afleveradres
      if ($quotation->companyId != "") {
        $types = ["delivery"];
        if ($_SESSION['userObject']->company->noDelivery != 1) {
          $types[] = "visit";
        }
        $addresses = array_merge($addresses, CrmAddresses::find_all_by(["companyId" => $quotation->companyId, 'type' => $types], "ORDER BY domestic, street"));
      }
      else { //particulier, dan account adres standaard invullen.
        $quotation->street = $_SESSION['userObject']->street;
        $quotation->nr = (int)$_SESSION['userObject']->nr;
        $quotation->ext = substr($_SESSION['userObject']->extension, 0, 10);
        $quotation->zipcode = $_SESSION['userObject']->zipcode;
        $quotation->domestic = $_SESSION['userObject']->domestic;
      }

      if ($quotation_extra->addressDeliveryId != "" && $quotation_extra->addressDeliveryId != 0) {
        $isfound = false;
        foreach ($addresses as $addr) {
          if ($addr->addressId == $quotation_extra->addressDeliveryId) {
            $isfound = true;
            break;
          }
        }
        if ($isfound != true) {
          //gekoppelde addres niet gevonden in array, kijk of je hem kunt vinden en voeg hem dan toe
          $addr_search = CrmAddresses::find_by(["addressId" => $quotation_extra->addressDeliveryId]);
          if ($addr_search) {
            $addresses[] = $addr_search;
          }
          else { //?? address id niet gevonden....leeg maken.
            $quotation_extra->addressDeliveryId = null;
          }
        }
      }
      if ($quotation_extra->addressDeliveryId != 20357) $addresses[] = CrmAddresses::find_by(["addressId" => 20357]); //rde pickup address

      $showOptions = false;
      if ($quotation->offerteType == "") {
        $customer_code = CustomerCodes::find_by(["codeId" => $quotation->codeId]);
        if (in_array($customer_code->groupId, CustomerGroups::getIdsByTypeid(2))) {
          $showOptions = true;

          $prices = Quotations::getPrices($quotation_extra->getQuotationAltPriceYear("Y-m-d"), $quotation->userId, $stone);
          $spareparts = Spareparts::getValues();

          $stonecount = 0;
          $iEndstoneL = 0;
          $iEndstoneR = 0;
          $iEndstoneLG = 0;
          $iEndstoneRG = 0;
          foreach ($elements as $element) {
            $stonecount += ($element->amount * $element->stoneAmount);
            $iEndstoneL += $element->leftEndstone * $element->amount;
            $iEndstoneR += $element->rightEndstone * $element->amount;
            $iEndstoneLG += $element->leftEndstoneGrooves * $element->amount;
            $iEndstoneRG += $element->rightEndstoneGrooves * $element->amount;
          }

          $option2price = 0;
          $option3price = $quotation->projectValue - ($quotation->meters * $spareparts['CMFREIGHTDISCOUNT']);

          $sOption1 = 'De offerte zoals gespecificeerd. De elementen worden door ons geleverd. Voor (deel)leveringen onder de 40 meter geldt een toeslag van € ' . number_format($spareparts['FREIGHTCOST'], 2, ',', '.') . ' transportkosten.<br/>Totaalprijs: € ' . number_format($quotation->projectValue, 2, ',', '.');
          $sOption2 = '';
          $sOption3 = 'De offerte zoals gespecificeerd. U haalt de elementen zelf op. De lege bakken en rekken halen wij gratis op uw werf op. U betaalt geen transportkosten en ontvangt bovendien een additionele korting van € ' . number_format($quotation->meters * $spareparts['CMFREIGHTDISCOUNT'], 2, ',', '.') . ' (€ ' . number_format($spareparts['CMFREIGHTDISCOUNT'], 2, ',', '.') . '/meter).';
          $sOption3 .= '<br/>Totaalprijs: € ' . number_format($option3price, 2, ',', '.');

          if ($quotation->endstone == 'true_grooves') {
            $option2price = $quotation->projectValue - ($stonecount * $prices['stonePrice']) - (($iEndstoneLG + $iEndstoneRG) * ($prices['stonePriceGroove'] - $prices['stonePrice'])) - ($quotation->meters * $spareparts['CMGLUEDISCOUNT']);
            $sOption2 = 'U levert zelf ' . ($stonecount - $iEndstoneLG - $iEndstoneRG) . ' stenen';
            $sOption2 .= ', ' . $iEndstoneLG . ' linkse en ' . $iEndstoneRG . ' rechste eindstenen met groeven';
            $sOption2 .= ' bij ons af en haalt later de elementen weer op. De lege bakken en rekken worden gratis op uw werf opgehaald.<br/>';
            $sOption2 .= 'Totaalprijs: € ' . number_format($option2price, 2, ',', '.');
          }
          elseif ($quotation->endstone == 'true') {
            $option2price = $quotation->projectValue - ($stonecount * $prices['stonePrice']) - (($iEndstoneL + $iEndstoneR) * ($prices['stonePriceEnd'] - $prices['stonePrice'])) - ($quotation->meters * $spareparts['CMGLUEDISCOUNT']);
            $sOption2 = 'U levert zelf ' . ($stonecount - $iEndstoneL - $iEndstoneR) . ' stenen';
            $sOption2 .= ', ' . $iEndstoneL . ' linkse en ' . $iEndstoneR . ' rechste eindstenen met opstaande zijkant ';
            $sOption2 .= ' bij ons af en haalt later de elementen weer op. De lege bakken en rekken worden gratis op uw werf opgehaald.<br/>';
            $sOption2 .= 'Totaalprijs: € ' . number_format($option2price, 2, ',', '.');
          }
          elseif ($quotation->endstone == 'false') {
            $option2price = $quotation->projectValue - ($stonecount * $prices['stonePrice']) - ($quotation->meters * $spareparts['CMGLUEDISCOUNT']);
            $sOption2 = 'U levert zelf ' . ($stonecount) . ' stenen';
            $sOption2 .= ' bij ons af en haalt later de elementen weer op. De lege bakken en rekken worden gratis op uw werf opgehaald.<br/>';
            $sOption2 .= 'Totaalprijs: € ' . number_format($option2price, 2, ',', '.');
          }

          $this->sOption1 = $sOption1;
          $this->sOption2 = $sOption2;
          $this->sOption3 = $sOption3;
          $this->option2price = $option2price; //use in save
          $this->mattingDiscount = $quotation->meters * $spareparts['CMFREIGHTDISCOUNT']; //use in save
          $this->optionsExtra = 'Wanneer u voor optie 2 of 3 kiest, betekent dit dat u de elementen zelf vervoert. Elementen worden door ons verpakt in kratten en/of rekken. ' .
            'Deze kunt u 60 dagen gratis gebruiken, vanaf het moment dat de elementen klaar staan op onze werf in Bladel. Binnen die periode dient u ons te contacteren voor het ophalen van de lege bakken en/of rekken. ' .
            'Indien u de bakken of rekken langer nodig heeft, betaalt u € ' . number_format($spareparts['CMBENCHRENTAL'], 2, ',', '.') . ' per rek per dag en € ' . number_format($spareparts['CMCRATERENTAL'], 2, ',', '.') . " per bak per dag aan huur.\n" .
            'Vier werkweken na opdrachtdatum staan de elementen klaar, tenzij u bij de opdrachtbevestiging uitdrukkelijk anders aangeeft middels een leverschema.';
        }
      }

      [$dueDate, $dueDateWeek, $weeks, $dueweek] = $this->determineDeliveryWeeks();
      $quotation->dueDate = $dueDate;
      $quotation->dueDateWeek = $dueDateWeek;

      $quotation->pickup = $quotation_extra->isPickup() ? 'true' : 'false';

      $payonline = false;
      if ($_SESSION['userObject']->isPrivate()) {
        if ($quotation->pickup == "true") {
          $quotation->freightCosts = 0;
        }
        else {
          $quotation->freightCosts = Config::get("QUOTATION_DEFAULT_FREIGHTCOSTS");
        }
        $total_price_excl = $quotation->getTotalPrice(false);
        if ($total_price_excl < 200) {
          //direct afrekenen met mollie
          $payonline = true;
        }
      }

      if (isset($_POST["order"])) {

        $quotation->projectName = trim($_POST["projectName"]);
        $quotation->projectReference = trim($_POST["projectReference"]);
        $quotation->customerNotes = trim($_POST["customerNotes"]);
        if (isset($_POST["deliveryNotes"])) $quotation->deliveryNotes = trim($_POST["deliveryNotes"]);

        $quotation_extra->sms = isset($_POST["sms"]) ? 1 : 0;
        $quotation_extra->sms_delivered = isset($_POST["sms_delivered"]) ? 1 : 0;
        if ($quotation_extra->sms == 1 || $quotation_extra->sms_delivered == 1) {
          $quotation_extra->smsnumber = trim($_POST["smsnumber"]);
          if (!ValidationHelper::isMobile($quotation_extra->smsnumber)) {
            $errors["smser"] = 'SMS number ontbreekt of is ongeldig. Uw mobielenummer is verplicht als u een SMS/Whatsapp wilt ontvangen.';
          }
        }
        else {
          $quotation_extra->smsnumber = null;
        }

        $quotation_extra->addressDeliveryId = $_POST['addressDeliveryId'];

        if ($quotation_extra->addressDeliveryId == "NEW") { //nieuwe adres
          $quotation->street = trim($_POST["street"]);
          $quotation->nr = (int)substr(trim($_POST["nr"]), 0, 5);
          $quotation->ext = substr(trim($_POST["ext"]), 0, 10);
          $quotation->zipcode = trim($_POST["zipcode"]);
          $quotation->domestic = trim($_POST["domestic"]);
          $quotation->country = trim($_POST["country"]);
          if ($quotation->street == "") {
            $errors['street'] = "Straat mag niet leeg zijn.";
          }
          if ($quotation->domestic == "") {
            $errors['domestic'] = "Plaats mag niet leeg zijn.";
          }

        }
        else {
          //hij is niet nieuw, kopier gegevens van geselecteerd adres
          /** @var CrmAddresses $addr */
          foreach ($addresses as $addr) {
            if ($addr->addressId == $quotation_extra->addressDeliveryId) {
              $quotation->street = $addr->street;
              $quotation->nr = $addr->nr;
              $quotation->ext = $addr->extension;
              $quotation->zipcode = $addr->zipcode;
              $quotation->domestic = $addr->domestic;
              break;
            }
          }
        }


        if ($quotation->projectName == "") {
          $errors[] = 'Project naam is verplicht';
        }
        if (isset($_POST["showoptions"]) && !isset($_POST["option"])) {
          $errors[] = 'U dient 1 van de 3 opties te selecteren.';
        }
        if (!isset($_POST["delivery_reach"])) {
          if ($quotation->deliveryNotes == "") {
            $errors["deliveryNotes"] = 'U heeft aangeven dat er bijzonderheden zijn bij de leverlocatie. U bent verplicht een toelichting in te vullen.';
          }
        }
        else {
          $quotation->deliveryNotes = "";
        }
        if (count($errors) == 0) {

          if ($quotation_extra->addressDeliveryId == "NEW") { //nieuwe adres
            $quotation_extra->addressDeliveryId = null;
          }

          $weeknr = intval($_POST["week"]);
          $deliveryFast = false;
          if ($weeknr < $dueweek) { //spoedlevering, niet leverweek aanpassen
            $weeknrstr = substr($weeknr, 4) . ' - ' . substr($weeknr, 0, 4);
            $quotation->customerNotes = "Let op: er is gekozen voor een levering in week " . $weeknrstr . ".\nNa onze controle, kunt u in uw account bij offertes de definitieve leverweek inzien.\n" . $quotation->customerNotes;
            $deliveryFast = true;
          }
          elseif ($weeknr > $dueweek) { //levering later als standaard, dan leverweek aanpassen aan geselecteerde week
            $extraweeks = $weeknr - $dueweek;
            $year = substr($weeknr, 0, 4);
            if ($year != $quotation->getDueDate("Y")) {
              $extraweeks -= 100 - DateTimeHelper::getTotalWeeksInYear($quotation->getDueDate("Y")); //in het vorige jaar. 52 / 53 weken in een jaar, deze extra verwijderen.
            }
            $newtime = strtotime('+' . $extraweeks . ' WEEKS', intval($quotation->getDueDate("U")));
            $quotation->dueDate = date('Y-m-d', $newtime);
            $quotation->dueDateWeek = date('W', $newtime);
          }

          $quotation->internNotes = $quotation->customerNotes; //kopie naar intern
          if (trim($quotation->deliveryNotes) != "") {
            $quotation->dispatchAppointment = $quotation->deliveryNotes;
          }

          if (isset($_POST["showoptions"])) {

            if ($_POST["option"] == "mattingOnlyGlueFlag") {
              $quotation->mattingOnlyGlue = round($this->option2price, 2);
              $quotation->mattingOnlyGlueFlag = 1;
            }
            elseif ($_POST["option"] == "mattingRemovalDiscountFlag") {
              $quotation->mattingRemovalDiscount = round(-1 * $this->mattingDiscount, 2);
              $quotation->mattingRemovalDiscountFlag = 1;
            }
          }

          $quotation->zipcodeMap = str_replace(" ", "", $quotation->zipcode);
          $quotation->userId = $_SESSION["userObject"]->userId;

          if ($payonline) { //online afrekenen: particulieren onder de 200 euro excl.

            $quotation->paymentMethod = Payment::PAYMENT_MOLLIE;
            $quotation->save();

            $quotation_extra->save();

            $paymentobj = new PaymentMollie();
            $paymentobj->handlePayment($quotation, $quotation->getTotalPrice());

          }
          else {
            if (!$_SESSION['userObject']->hasCompany() ||
              (isset($_SESSION['userObject']->company) && $_SESSION['userObject']->company->payInAdvance == 1) ||
              trim($stone->alert) != "" ||
              trim($quotation->customerNotes) != "" ||
              $deliveryFast ||
              $quotation->hasVerstek($elements, $stone)) {
              //bij stone.alert OF opmerking OF weeknummer snel OF verstek
              $quotation->statusId = Status::STATUS_ORDER; //opdracht
            }
            else {
              $quotation->statusId = Status::STATUS_CHECKED; //akkoord
            }
            $quotation->orderDate = date('Y-m-d');
            $quotation->productionDate = date('Y-m-d');

            $quotation->save();

            $quotation_extra->save();

            ProductionOrder::create($quotation);
            MailsFactory::sendOrderMail($quotation);
            //          MailsFactory::sendMissingDataEmail($quotation);
          }

          $message = 'Bedankt voor het plaatsen van uw bestelling. U heeft ook een bevestiging per email gekregen.';
          MessageFlashCoordinator::addMessage($message);

          ResponseHelper::redirect($returnurl);

        }

      }
      elseif ($quotation->deliveryNotes == "") {
        $_POST["delivery_reach"] = 1;
      }

      $this->payonline = $payonline;
      $this->dueweek = $dueweek;
      $this->weeks = $weeks;
      $this->quotation = $quotation;
      $this->quotation_extra = $quotation_extra;
      $this->stone = $stone;
      $this->addresses = $addresses;
      $this->errors = $errors;
      $this->showOptions = $showOptions;
      $this->returnurl = $returnurl;
    }

    public function executeConfirmwebshop() {
      if (isset($_POST["order"])) {
        ResponseHelper::redirect(PageMap::getUrl("M_BASKET") . "?action=openorder&quotationId=" . $this->quotation->quotationId);
      }
      $this->template = "confirmwebshopSuccess.php";
    }

    /**
     * Deze pagina is ook bereikbaar als je niet bent ingelogd, echter dan moet er een hashcode aanwezig zijn.
     */
    public function executeContainers() {

      $sanboxuser = false;
      $company = false;
      if (isset($_SESSION['userObject'])) {
        //ingelogd, dus bakken van deze klant tonen
        $sanboxuser = $_SESSION['userObject'];
        if ($sanboxuser->hasCompany()) {
          $company = $sanboxuser->company;
        }
      }
      elseif (isset($_GET["hash"])) {
        //extern geopend, is er een hash, zo ja, juiste containers tonen.
        $compOrUserid = User::decrypt($_GET['hash']);
        if (substr($compOrUserid, 0, 1) == "U" && is_numeric(substr($compOrUserid, 1))) {
          //dit is een userId
          $sanboxuser = SandboxUsers::getUserAndCompany(substr($compOrUserid, 1));
          if ($sanboxuser->hasCompany()) {
            $company = $sanboxuser->company;
          }
        }
        elseif (is_numeric($compOrUserid)) {
          //company id
          $company = CrmCompanies::find_by(["companyId" => $compOrUserid]);
        }
      }

      if (!$sanboxuser && !$company) {
        MessageFlashCoordinator::addMessageAlert("Er zijn geen bakken of rekken gevonden.");
        ResponseHelper::redirect("/");
      }

      $cqsAway = ContainersQuotations::find_all("WHERE returnDate IS NULL");

      $filt = "AND ( ";
      if ($sanboxuser) {
        $filt .= "quotations.userId=" . $sanboxuser->userId . " ";
      }
      if ($company) {
        if ($sanboxuser) $filt .= " OR ";
        $filt .= " quotations.companyId=" . $company->companyId . " ";
      }
      $filt .= ") ";
      $filt .= " AND quotations.statusId>=55 ";
      $filt .= "ORDER BY quotationDate ASC, quotationNumber ASC, quotationVersion ASC ";
      $quotations = AppModel::mapObjectIds(Quotations::find_all_by(["flaggedForDeletion" => 0], $filt), "quotationId");

      $containers = [];
      foreach ($cqsAway as $cq) {
        if (isset($quotations[$cq->quotationId])) {
          if (isset($containers[$cq->containerId])) {
            $container = $containers[$cq->containerId];
          }
          else {
            $container = Containers::find_by(["containerId" => $cq->containerId]);
            $container->deliverdate = $cq->getDeliverDate();
            $container->deliverdatetime = intval($cq->getDeliverDate("U"));
          }
          if ($container->inStock != 'A') { //afgeschreven containers niet tonen
            $cq->quotations[] = Quotations::getById($cq->quotationId);
            $container->container_quotations[] = $cq;
            $containers[$container->containerId] = $container;
          }
        }
      }

      usort($containers, function ($a, $b) {
        return $a->deliverdatetime - $b->deliverdatetime;
      });

      if (isset($_GET["pickup"]) && isset($_GET["id"])) {
        foreach ($containers as $container) {
          foreach ($container->container_quotations as $cq) {
            if ($cq->containerId == $_GET["id"] && $cq->nextroute != "true") {
              $cq->nextroute = "true";
              $cq->save();
            }
          }
        }
        MessageFlashCoordinator::addMessage("Bak/Rek word z.s.m. opgehaald. Bedankt voor het retour melden.");
        ResponseHelper::redirect(reconstructQueryAdd(["hash"]));
      }

      $this->containers = $containers;

    }

    public function executeExtraproductsedit() {

      $this->checkAuthentication();

      if (isset($_GET["id"])) {
        unset($_SESSION["wizard"]);
        $quotation = Quotations::getById($_GET["id"]);
        if ($quotation && $quotation->mayEdit()) {
          $stone = Stones::find_by(["stoneId" => $quotation->stoneId]);
          $quotation->colorId = $stone->colorId;
          $quotation->sizeId = $stone->sizeId;

          $quotation_extra = QuotationsExtra::find_by(["quotationId" => $quotation->quotationId]);
          $elements = OrderElements::find_all_by(["quotationId" => $quotation->quotationId]);
          foreach ($elements as $key1 => $element) {
            $element->from_db = false;
            $element->elementId = null;
            $element->quotationId = null;
            $element->mitre = $element->getMitre();
            //hearClickSize overnemen van eerste element, zijn allemaal hetzelfde
            if (!isset($quotation->heartClickSize)) {
              $quotation->heartClickSize = $element->heartClickSize;
            }
          }

          $_SESSION["wizard"]['quotation'] = $quotation;
          $_SESSION["wizard"]['quotation_extra'] = $quotation_extra;
          $_SESSION["wizard"]['elements'] = $elements;
          $_SESSION["wizard"]['extra_products'] = [];
          foreach (Projects::find_all_by(["quotationId" => $quotation->quotationId], "AND NOT product_id IS NULL ORDER BY orderNr") as $project) {
            $key = $project->product_id;
            if ($project->glaced_left == 1) {
              $key .= "_glacedleft";
            }
            elseif ($project->glaced_right == 1) {
              $key .= "_glacedright";
            }
            $_SESSION["wizard"]['extra_products'][$key] = $project;
          }

          $_SESSION['flash_message'] = __("Extra producten " . $quotation->getQuotationNumberFull() . " is geopend.");
          ResponseHelper::redirect($this->wizardurl . '?action=wizard&step=4&singlepage=1');
        }
      }
      elseif (isset($_GET["save"])) {
        $extra_products = $_SESSION["wizard"]['extra_products'];
        $quotation = $_SESSION["wizard"]['quotation'];
        foreach ($extra_products as $extra_project) {
          if ($extra_project->size > 0) {
            $extra_project->quotationId = $quotation->quotationId;
            $extra_project->save();
          }
          elseif ($extra_project->projectId != "" && $extra_project->size == 0) {
            $extra_project->destroy();
          }
        }
        $_SESSION['flash_message'] = __("Extra producten " . $quotation->getQuotationNumberFull() . " aangepast.");
        ResponseHelper::redirect($this->wizardurl);
      }
      $_SESSION['flash_message_red'] = __("Onjuiste link");
      ResponseHelper::redirect($this->wizardurl);
    }

    public function executePrepaid() {

      if (!(isset($_SESSION["userObject"]->company) && $_SESSION["userObject"]->company->payInAdvance == 1)) {
        //mag niet vooruit betalen
        ResponseHelper::redirect301($this->wizardurl);
      }

      $this->checkAuthentication();
      $hasvat = true;
      if ($_SESSION["userObject"]->companyId != "" && $_SESSION["userObject"]->companyId != 0) {
        $hasvat = $_SESSION["userObject"]->company->hasVat();
      }
      $quotations = Quotations::getQuotationsPrepaid($_SESSION["userObject"]);

      $quotationsGrouped = [];

      foreach ($quotations as $quotation) {
        $quotation_extra = QuotationsExtra::find_by(["quotationId" => $quotation->quotationId]);
        $quotation->extra = $quotation_extra;
        if (!isset($quotationsGrouped[$quotation_extra->addressDeliveryId])) {
          $val["address"] = CrmAddresses::find_by(["addressId" => $quotation_extra->addressDeliveryId]);
          $val["quotations"] = [];
          $val["freightcosts_payed"] = 0;
          $val["freightcosts"] = 0;
          $val["total"] = 0;
          $val["meters"] = 0;
          $val["pickup"] = $quotation_extra->isPickup();
          $quotationsGrouped[$quotation_extra->addressDeliveryId] = $val;
        }
        $quotationsGrouped[$quotation_extra->addressDeliveryId]["quotations"][] = $quotation;
      }

      //bepaal de vrachtkosten binnen een afleveradres. Er zijn nooit facturen wanneer een quotation geel/groen is.
      //Er kan natuurlijk al wel een deel betaald zijn.

      $totals = [
        'excl'  => 0,
        'vat'   => 0,
        'total' => 0,
      ];
      $spareparts = Spareparts::getValues();
      $allUnpaidQuotations = [];
      foreach ($quotationsGrouped as $k => $quotationsGroup) {
        $paidQuotations = [];
        $unpaidQuotations = [];
        $freightCostsPayed = 0;
        $freightCostsNow = 0;
        foreach ($quotationsGroup["quotations"] as $quotation) {
          if ($quotation->payedFlag == 1) {
            //deze is betaald
            $paidQuotations[] = $quotation;
          }
          else {
            $quotationsGrouped[$k]["total"] += $quotation->projectValue;
            $unpaidQuotations[] = $quotation;
            $allUnpaidQuotations[] = $quotation;
          }
          $quotationsGrouped[$k]["meters"] += $quotation->meters;
        }

        if (count($unpaidQuotations) == 0) {
          //geen enkele onbetaalde quotations...unset
          unset($quotationsGrouped[$k]);
          continue;
        }

        if (!$quotationsGroup["pickup"]) {
          if (count($paidQuotations) > 0) { //als pickup dan geen vrachtkosten
            $metersPayed = 0;
            foreach ($paidQuotations as $pq) {
              $metersPayed += $pq->meters;
            }
            if ($metersPayed < 40) { //de betaalde orders zijn minder dan 40 meter, dus er zijn vrachtkosten berekend
              $freightCostsPayed = $spareparts['FREIGHTCOST'];
            }
          }

          if ($quotationsGrouped[$k]["meters"] < 40) { //totaal aantal meters is minder dan 40, dus vrachtkosten.
            $freightCostsNow = $spareparts['FREIGHTCOST'];
          }

          if ($freightCostsPayed == $freightCostsNow) {
            //niks gewijzigd aan vrachtkosten, geen vrachtkosten berekenen
          }
          else {
            //er zijn te veel of te weinig vrachtkosten betaald
            $quotationsGrouped[$k]["freightcosts"] = $freightCostsNow - $freightCostsPayed;
          }
        }
        $quotationsGrouped[$k]["total"] += $quotationsGrouped[$k]["freightcosts"];
        $quotationsGrouped[$k]["freightcosts_payed"] = $freightCostsPayed;


        $totals['excl'] += $quotationsGrouped[$k]["total"];

      }

      if ($totals['excl'] < 0) {
        $totals['excl'] = 0;
      }

      if ($hasvat) {
        $totals['vat'] += 0.21 * $totals['excl'];
      }
      $totals['total'] += $totals['excl'] + $totals['vat'];


      if (isset($_POST["go"])) {
        if ($totals['excl'] == 0) {
          //totaal bedrag te betalen is 0, dus direct op betaald zetten.
          $sendMail = false;
          foreach ($quotations as $quotation) {
            if ($quotation->payedFlag == 0) {
              $sendMail = true; //prevent double mail
            }
            $quotation->setPaid();
          }
          if ($sendMail) {
            MailsFactory::sendQuotationsPayedMail($quotations);
          }
          MessageFlashCoordinator::addMessage("Bedankt voor uw betaling, we gaan uw bestellingen zo spoedig mogelijk uitleveren.");
          ResponseHelper::redirect(reconstructQuery());
        }
        else {
          $paymentobj = new PaymentMollie();
          $paymentobj->handlePaymentQuotations($allUnpaidQuotations, $totals['total']);
        }
      }

      $this->quotationsGrouped = $quotationsGrouped;
      $this->totals = $totals;
    }

    public function executePrepaiddone() {

      if (!isset($_GET["ids"])) {
        $this->error = __("We hebben uw bestellingen kunnen vinden.");
        return;
      }
      $quotationIds = explode(",", $_GET["ids"]);
      $quotations = Quotations::find_all_by(["quotationId" => $quotationIds]);

      if (count($quotations) == 0) {
        $this->error = __("We hebben geen bestellingen kunnen vinden.");
        return;
      }
      $isPaid = false;
      $mollie_id = false;
      foreach ($quotations as $quotation) {
        $isPaid = $quotation->payedFlag == 1;
        $mollie_id = $quotation->mollie_id;
        break;
      }
      $message = "";
      if (!$isPaid) {
        // (nog)niet betaald via postback. Even ophalen van mollie, om betere melding te geven.
        $message = __("Er is iets mis gegaan met uw betaling.<Br/>Probeert u het op een later stadium nogmaals.");
        if ($mollie_id !== false) {
          try {
            $m = Mollie::find_by_id($mollie_id);
            $mollie = PaymentMollie::getMollie();
            $payment = $mollie->payments->get($m->paymentId);
            //echo $payment->status;
            if ($payment->isPaid()) {
              //toch betaald!
            }
            elseif ($payment->isPending()) {
              $message = __("Uw betaling is in verwerking, maar nog niet bevestigd.<br/>Zodra deze bevestigd is ontvangt u van ons bericht.");
            }
            elseif ($payment->isCanceled()) {
              $message = __("U heeft uw betaling geannuleerd.");
            }
          }
          catch (Mollie\Api\Exceptions\ApiException $e) {
            logToFile('order', 'prepaid Mollie_API_Exception:' . $e->getMessage());
          }
        }
      }

      $this->message = $message;
      $this->quotations = $quotations;

    }


    public function executePostcodeapi() {
      header('Content-Type: application/json');
      $postcodeapi = new PostcodeApi();
      echo $postcodeapi->getAllAsJson($_GET["zipcode"], $_GET["nr"]);
      $this->template = null;
    }

    /**
     * @param Quotations $quotation
     * @param OrderElements[] $elements
     * @param Stones $stone
     * @return boolean
     */
    private function hasVerstek($quotation, $elements, $stone = null) {
      if ($stone == null && $quotation->stoneId != "" && $quotation->stoneId != 0) {
        $stone = Stones::find_by(["stoneId" => $quotation->stoneId]);
      }
      $this->hasVerstek = $quotation->hasVerstek($elements, $stone);
      return $this->hasVerstek;
    }

    public function executeNotavailable() {
      $this->quotationInfo = new GetStoneCategoryInfo($_GET["stoneCategoryId"]);
    }

    /**
     * @return array[$dueDate, $dueDateWeek, $weeks, $dueweek]
     */
    private function determineDeliveryWeeks() {

      $neededdays = Quotations::determineNeededProductiondays();
      $duetime = strtotime("+" . $neededdays . " DAYS");
      $dueDate = date('Y-m-d', $duetime);
      $dueDateWeek = date('W', $duetime);

      $weeks = [];

      //als de hele week dicht, dan deze week disabelen.
      $leaves = AppModel::mapObjectIds(WorkerBaseleave::getOffDaysPeriod(date("Y-m-d"), date("Y-m-d", strtotime("+1 YEAR"))), "date");
      for ($time = time(); $time < strtotime("+1 YEAR"); $time = strtotime("+1 WEEK", $time)) {
        $closed = true;
        $time_monday = DateTimeHelper::getFirstOfWeekDate(date("Y-m-d", $time), "U");
        $lastleave = false;
        for ($ltime = $time_monday; $ltime < strtotime("+7 DAYS", $time_monday); $ltime = strtotime("+1 DAY", $ltime)) {
          if (!isset($leaves[date("Y-m-d", $ltime)])) {
            $closed = false;
            break;
          }
          else {
            $lastleave = $leaves[date("Y-m-d", $ltime)];
          }
        }
        $weeknr = intval(date("YW", $time_monday)); //$time_monday gebruiken, anders krijg je verkeerde jaar bij jaarwissel
        $name = "Week " . date("W - Y", $time_monday);
        if ($closed) {
          $name .= " - " . strtolower($lastleave->description);
        }
        $weeks[$weeknr] = [
          'name'   => $name,
          'closed' => $closed,
        ];
      }

      //bovenin word al rekening gehouden met eventuele vaste snipperdagen.
      //de eerst volgende werkweek is een geldige week
      $dueweekTime = strtotime($dueDate);
      $dueweek = date("YW", $dueweekTime);
      if ($weeks[$dueweek]["closed"]) {
        $go = false;
        foreach ($weeks as $weeknr => $name) {
          if ($weeknr == $dueweek) {
            $go = true;
            continue;
          }
          if ($go) {
            if (!$weeks[$weeknr]["closed"]) {
              $dueweek = $weeknr;
              $week_start = new DateTime();
              $week_start->setISODate(substr($weeknr, 0, 4), substr($weeknr, 4, 2));
              $dueweekTime = $week_start->format('U');
              break;
            }
          }
        }
      }
      $weeks[$dueweek]["name"] .= " - standaard";

      if ($dueweek != date("Ym", $duetime)) {
        //de berekende duedate is later als ivm vakantie, pas dueDate en duedateWeek aan
        $dueDate = date('Y-m-d', $dueweekTime);
        $dueDateWeek = date('W', $dueweekTime);
      }

      return [$dueDate, $dueDateWeek, $weeks, $dueweek];

    }

  }