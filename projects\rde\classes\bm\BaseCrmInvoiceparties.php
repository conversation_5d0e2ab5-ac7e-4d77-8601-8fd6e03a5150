<?php
class BaseCrmInvoiceparties extends AppModel
{
  const DB_NAME = 'rde_cms';
  const TABLE_NAME = 'crm_invoiceparties';
  const OM_CLASS_NAME = 'CrmInvoiceparties';
  const columns = ['invoicePartyId', 'companyId', 'attendant', 'addressId', 'differentaddressId', 'vatRegNo', 'invoiceCopies', 'invoiceTerm', 'eInvoice', 'email', 'orders_separate', 'emailConfirmed', 'emailReminder', 'invoicePartyNotes'];
  const field_structure = [
    'invoicePartyId'              => ['type' => 'int', 'length' => '10', 'null' => false],
    'companyId'                   => ['type' => 'int', 'length' => '10', 'null' => false],
    'attendant'                   => ['type' => 'varchar', 'length' => '150', 'null' => true],
    'addressId'                   => ['type' => 'int', 'length' => '10', 'null' => false],
    'differentaddressId'          => ['type' => 'int', 'length' => '10', 'null' => true],
    'vatRegNo'                    => ['type' => 'varchar', 'length' => '30', 'null' => true],
    'invoiceCopies'               => ['type' => 'boolean', 'length' => '1', 'null' => false],
    'invoiceTerm'                 => ['type' => 'int', 'length' => '11', 'null' => false],
    'eInvoice'                    => ['type' => 'boolean', 'length' => '1', 'null' => false],
    'email'                       => ['type' => 'varchar', 'length' => '150', 'null' => true],
    'orders_separate'             => ['type' => 'boolean', 'length' => '1', 'null' => true],
    'emailConfirmed'              => ['type' => 'boolean', 'length' => '1', 'null' => false],
    'emailReminder'               => ['type' => 'varchar', 'length' => '150', 'null' => true],
    'invoicePartyNotes'           => ['type' => 'text', 'length' => '', 'null' => true],
  ];

  protected static $primary_key = ['invoicePartyId'];
  protected $auto_increment = 'invoicePartyId';

  public $invoicePartyId, $companyId, $attendant, $addressId, $differentaddressId, $vatRegNo, $invoiceCopies, $invoiceTerm, $eInvoice, $email, $orders_separate, $emailConfirmed, $emailReminder, $invoicePartyNotes;

  function __construct($obj_arr = [], &$error_codes = []) {
    parent::__construct();
    $this->setDefaults();
    if (!empty($obj_arr)) {
      $this->from_array($obj_arr);
      $this->valid_required_fields($error_codes);
    }
  }

  /**
  * Set default object values
  **/
  public function setDefaults() {
    $this->invoiceCopies = 1;
    $this->eInvoice = 0;
    $this->orders_separate = 0;
    $this->emailConfirmed = 0;
  }

  /**
   * @param array $conditions (required)
   * @param string $raw_sql (optional)
   * @return CrmInvoiceparties[]
   **/
  public static function find_all_like($conditions, $raw_sql = '') {
    return parent::generic_find_all_like($conditions, static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param array $conditions (required)
   * @param string $raw_sql (optional)
   * @return CrmInvoiceparties[]
   **/
  public static function find_all_by($conditions, $raw_sql = '') {
    return parent::generic_find_all_by($conditions, static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param string $raw_sql (optional)
   * @return CrmInvoiceparties[]
   **/
  public static function find_all($raw_sql = '') {
    return parent::generic_find_all(static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param array $conditions (required)
   * @param string $raw_sql (optional)
   * @return CrmInvoiceparties
   **/
  public static function find_by($conditions, $raw_sql = '') {
    return parent::generic_find_by($conditions, static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param string $id (required)
   * @param string $raw_sql (optional)
   * @return CrmInvoiceparties
   **/
  public static function find_by_id($id, $raw_sql = '') {
    return parent::generic_find_by_id($id, static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param array $conditions (required)
   * @param string $raw_sql (optional)
   * @return int
   **/
  public static function count_all_by($conditions, $raw_sql = '') {
    return parent::generic_count_all_by($conditions, static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param array $conditions (required)
   * @param string $raw_sql (optional)
   * @return bool
   **/
  public static function delete_by($conditions, $raw_sql = '') {
    return parent::generic_delete_by($conditions, static::OM_CLASS_NAME, $raw_sql);
  }

}