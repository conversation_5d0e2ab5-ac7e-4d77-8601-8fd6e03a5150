<?php

  class siteshopRdeActions extends siteshopActions {

    public function preExecute() {

      parent::preExecute();

      $this->cats = Category::getShopCats();
      $this->page = Page::getPageAndContent(216, $_SESSION['lang']);

    }

    public function executeHome() {
      $shoppage = Page::find_by(['site_id' => $_SESSION['site']->id, 'module' => 'siteshop', 'action' => 'home']);
      $shoppage->content = PageContent::getByPageIdAndLang($shoppage->id);
      $this->page = $shoppage;
    }

    public function executeCategory() {

      if (!(isset($_GET['var2']) && StringHelper::isInt($_GET['var2']))) {
        MessageFlashCoordinator::addMessageAlert('Helaas... Deze categorie is niet meer beschikbaar. U bent doorgestuurd naar de homepage.');
        ResponseHelper::redirect301('/');
      }

      $this->category = Category::find_by_id($_GET['var2']);

      $filt_subcats = '';
      if (SandboxUsers::isAdmin()) {
        $filt_subcats = " AND (category.online_custshop=1 OR category.online_admin=1)";
      }
      else {
        $filt_subcats = " AND category.online_custshop=1";
      }
      $filt_subcats .= ' AND category.void=0 ORDER BY category.sort ASC';

      $this->subcategories = Category::find_all_by(['parent_id' => $this->category->id], $filt_subcats);

      $_SESSION["shop_continue_url"] = $this->category->getShopUrl();

      $show = ($this->category->online_custshop == 1 || (SandboxUsers::isAdmin() && $this->category->online_admin == 1));
      if ($this->category == null || $this->category->void == 1 || !$show) {
        $_SESSION['flash_message_red'] = 'Helaas, deze categorie is niet meer beschikbaar. U bent doorgestuurd naar de homepage.';
        ResponseHelper::redirect301("/");
      }

      $this->seo_title = $this->category->getSeoTitle($_SESSION['lang']);
      $this->seo_description = $this->category->getSeoDesc($_SESSION['lang']);
      if ($this->category->foto_orig != "") {
        $this->seo_image = $this->site->site_host->getDomain(true) . $this->category->getPhoto(false);
      }

      BreadCrumbs::getInstance()->removeLastItem(); //category
      $catparents = Category::getParents($this->category);
      $catparents_active = [];
      foreach ($catparents as $parcat) {
        if ($parcat->id != $this->category->id) {
          BreadCrumbs::getInstance()->addItem($parcat->getName($_SESSION['lang']), $parcat->getShopUrl());
        }
        $catparents_active[$parcat->id] = $parcat;
      }
      BreadCrumbs::getInstance()->addItem($this->category->getName($_SESSION['lang']));

      $this->catparents = $catparents;
      $this->catparents_active = $catparents_active;


      //pager properties
      $this->pager = new Pager();
      $this->pager->rowsPerPage = 21;
      if (Config::isdefined('FRONTEND_PAGER_SIZE')) {
        $this->pager->rowsPerPage = Config::get('FRONTEND_PAGER_SIZE');
      }
      $this->pager->base_url = $this->category->getShopUrl();
      $this->pager->handle();
      //einde pager props

      //de volgende en vorige pagina's dienen wel als canonical te worden opgenomen
      if ($this->pager->getPageNum() == 1) { //pagina 1 heeft niet pageNum request param
        Context::addCanonical($this->site->site_host->getDomain(true) . $this->category->getShopUrl());
      }
      else { //pagina 2 en verder heeft de pageNum request param
        Context::addCanonical($this->site->site_host->getDomain(true) . $_SERVER["REQUEST_URI"]);
      }

      if ($this->category->id == 1) { //stenen pas informatie aan
        $this->categoryKeramicStones();
        return;
      }
      elseif ($this->category->id == 19) { //stenen pas informatie aan
        $this->categoryKeramicStandardlengths();
        return;
      }
      elseif ($this->category->id == 20) { //stenen pas informatie aan
        $this->categoryNaturalStandardlengths();
        return;
      }

      //artikelen ophalen
      $filtquery = "";
      $filtquery .= "JOIN category_product ON category_product.product_id = product.id AND category_product.online = 1 AND category_product.void = 0 ";
      $filtquery .= "WHERE product.void = 0 AND category_product.category_id=" . $this->category->id . " ";
      if (SandboxUsers::isAdmin()) {
        $filtquery .= "AND (product.online_custshop=1 OR product.online_admin=1) ";
      }
      else {
        $filtquery .= "AND product.online_custshop=1 ";
      }
      $filtquery .= " ORDER BY category_product.sort ";

      //count
      $result = DBConn::db_link()->query("SELECT count(product.id) as som FROM product " . $filtquery);
      $row = $result->fetch_assoc();
      $this->pager->count = $row["som"];
      if (!$this->pager->count) $this->pager->count = 0;

      $filtquery = "SELECT * FROM product " . $filtquery;
      $filtquery .= $this->pager->getLimitQuery();

      $products = [];
      $result = DBConn::db_link()->query($filtquery);
      while ($row = $result->fetch_row()) {
        $product = new Product();
        $product->hydrate($row);
        $product->content = ProductContent::getByProductIdAndLang($product->id);

        $catprod = new CategoryProduct();
        $catprod->hydrate($row, count(Product::columns));
        $product->category_product = $catprod;
        $products[] = $product;
      }

      $this->products = $products;
      $this->brands = Brand::find_all();

    }

    public function categoryKeramicStones() {

      if (!isset($_SESSION['f_brand_id'])) $_SESSION['f_brand_id'] = '';
      if (!isset($_SESSION['f_glaced'])) $_SESSION['f_glaced'] = '';
      if (!isset($_SESSION['f_color_id'])) $_SESSION['f_color_id'] = '';
      if (!isset($_SESSION['f_size_id'])) $_SESSION['f_size_id'] = '';

      if (isset($_POST["filter"])) {
        $_SESSION['f_brand_id'] = $_POST['f_brand_id'];
        $_SESSION['f_glaced'] = $_POST['f_glaced'];
        $_SESSION['f_color_id'] = $_POST['f_color_id'];
        $_SESSION['f_size_id'] = $_POST['f_size_id'];
        ResponseHelper::redirect($this->category->getShopUrl());
      }

      $glaced_filtered_colors = [];
      $filterOnProductIds = false;
      $productIds = [];

      $allcolors = StoneColors::getDisplayColors([1, 2]);
      $stoneId_productId = [];
      $filtextra = "";
      if (SandboxUsers::isAdmin()) {
        $filtextra = "AND (product.online_custshop=1 OR product.online_admin=1)";
      }
      else {
        $filtextra = "AND product.online_custshop=1";
      }

      foreach (Product::find_all_by(["discountgroup_id" => 1, "void" => 0], $filtextra) as $pr) {
        $stoneId = str_replace("STONE_", "", $pr->supplier_code);
        if ($stoneId != "") {
          $stoneId_productId[$stoneId] = $pr->id;
        }
      }

      $stones = AppModel::mapObjectIds(Stones::getDisplayStones(), "stoneId");

      if ($_SESSION['f_glaced'] != "" || $_SESSION['f_color_id'] != "" || $_SESSION['f_size_id'] != "") {
        //er is een filter, alle productentoevoegen, en verwijden wat niet voldoet.
        $filterOnProductIds = true;
        $productIds = $stoneId_productId;
      }

      if ($_SESSION['f_glaced'] != "") {
        foreach ($allcolors as $color) {
          if ($color->glaced == $_SESSION['f_glaced']) {
            $glaced_filtered_colors[$color->colorId] = $color->colorId;
          }
        }

        foreach ($stones as $stone) {
          if (!isset($glaced_filtered_colors[$stone->colorId]) && isset($stoneId_productId[$stone->stoneId])) {
            unset($productIds[$stone->stoneId]);
          }
        }
      }

      if ($_SESSION['f_color_id'] != "") {
        foreach ($stones as $stone) {
          if ($stone->colorId != $_SESSION['f_color_id'] && isset($stoneId_productId[$stone->stoneId])) {
            unset($productIds[$stone->stoneId]);
          }
        }
      }

      if ($_SESSION['f_size_id'] != "") {
        foreach ($stones as $stone) {
          if ($stone->sizeId != $_SESSION['f_size_id'] && isset($stoneId_productId[$stone->stoneId])) {
            unset($productIds[$stone->stoneId]);
          }
        }
      }

      //artikelen ophalen
      $filtquery = "";
      $filtquery .= "JOIN category_product ON category_product.product_id = product.id AND category_product.online = 1 AND category_product.void = 0 ";
      $filtquery .= "WHERE product.void = 0 AND category_product.category_id=" . $this->category->id . " ";
      if (SandboxUsers::isAdmin()) {
        $filtquery .= "AND (product.online_custshop=1 OR product.online_admin=1) ";
      }
      else {
        $filtquery .= "AND product.online_custshop=1 ";
      }

      //stenen
      $filtquery .= " AND discountgroup_id=1 ";
      $filtquery .= " AND weight!=0 ";
      if ($_SESSION['f_brand_id'] != "") {
        $filtquery .= " AND brand_id=" . $_SESSION['f_brand_id'] . " ";
      }
      if ($filterOnProductIds) {
        $filtquery .= " AND " . DbHelper::getSqlIn("product.id", $productIds);
      }
      $filtquery .= " ORDER BY category_product.sort ";

      //count
      $result = DBConn::db_link()->query("SELECT count(product.id) as som FROM product " . $filtquery);
      $row = $result->fetch_assoc();
      $this->pager->count = $row["som"];
      if (!$this->pager->count) $this->pager->count = 0;

      $filtquery = "SELECT * FROM product " . $filtquery;
      $filtquery .= $this->pager->getLimitQuery();

      $products = [];
      $result = DBConn::db_link()->query($filtquery);
      while ($row = $result->fetch_row()) {
        $product = new Product();
        $product->hydrate($row);
        $product->content = ProductContent::getByProductIdAndLang($product->id);

        $catprod = new CategoryProduct();
        $catprod->hydrate($row, count(Product::columns));
        $product->category_product = $catprod;
        $products[] = $product;
      }

      //colors
      $colors = [
        "Ongeglazuurd"           => [],
        "Geglazuurd - standaard" => [],
        "Geglazuurd - speciaal"  => [],
      ];

      foreach ($allcolors as $k => $color) {
        if ($_SESSION['f_brand_id'] != "") {
          if ($color->brandId != $_SESSION['f_brand_id']) {
            unset($allcolors[$k]);
            continue;
          }
        }
        if ($_SESSION['f_glaced'] != "") {
          if ($color->glaced != $_SESSION['f_glaced']) {
            unset($allcolors[$k]);
            continue;
          }
        }
        if ($color->glaced == "true") {
          if ($color->common == "true") {
            $colors["Geglazuurd - standaard"][] = $color;
          }
          else {
            $colors["Geglazuurd - speciaal"][] = $color;
          }
        }
        else {
          $colors["Ongeglazuurd"][] = $color;
        }
      }

      if (count($colors["Geglazuurd - standaard"]) == 0) unset($colors["Geglazuurd - standaard"]);
      if (count($colors["Geglazuurd - speciaal"]) == 0) unset($colors["Geglazuurd - speciaal"]);
      if (count($colors["Ongeglazuurd"]) == 0) unset($colors["Ongeglazuurd"]);

      //sizes ----------
      $allsizes = StoneSizes::getDisplayStonesizes();

      if ($_SESSION['f_brand_id'] != "") {
        foreach ($allsizes as $k => $size) {
          if ($size->brandId != $_SESSION['f_brand_id']) {
            unset($allsizes[$k]);
          }
        }
      }
      if ($_SESSION['f_glaced'] != "") {
        //all producten ophalen welke glaced zijn.
//            pd($glaced_filtered_colors);
        //houd geen rekening met vinkje online...nice to have
        $result = DBConn::db_link()->query("SELECT stoneId, sizeId FROM " . Stones::getTablename() . " WHERE display='true' AND (stones.type='raamdorpel' OR stones.type='spekband') AND " . DbHelper::getSqlIn("colorId", $glaced_filtered_colors, false));
        $glacesStoneIds = [];
        while ($row = $result->fetch_assoc()) {
          $glacesStoneIds[$row["stoneId"]] = $row["sizeId"];
        }
//            pd($glacesStoneIds);
        foreach ($allsizes as $k => $size) {
          if (!in_array($size->sizeId, $glacesStoneIds)) {
            unset($allsizes[$k]);
          }
        }
      }

      if ($_SESSION['f_color_id'] != "") {
        //all producten ophalen welke deze kleur hebben.
        //houd geen rekening met vinkje online...nice to have
        $result = DBConn::db_link()->query("SELECT stoneId, sizeId FROM " . Stones::getTablename() . " WHERE display='true' AND (stones.type='raamdorpel' OR stones.type='spekband') AND colorId=" . $_SESSION['f_color_id']);
        $coloredStoneIds = [];
        while ($row = $result->fetch_assoc()) {
          $coloredStoneIds[$row["stoneId"]] = $row["sizeId"];
        }
        //            pd($glacesStoneIds);
        foreach ($allsizes as $k => $size) {
          if (!in_array($size->sizeId, $coloredStoneIds)) {
            unset($allsizes[$k]);
          }
        }
      }

      $sizes = [
        "Standaard maten" => [],
        "Speciale maten"  => [],
      ];
      foreach ($allsizes as $size) {
        if ($size->common == "true") {
          $sizes["Standaard maten"][] = $size;
        }
        else {
          $sizes["Speciale maten"][] = $size;
        }
      }
      if (count($sizes["Standaard maten"]) == 0) unset($sizes["Standaard maten"]);
      if (count($sizes["Speciale maten"]) == 0) unset($sizes["Speciale maten"]);

      $this->products = $products;
      $this->brands = Brand::find_all();
      $this->colors = $colors;
      $this->sizes = $sizes;

    }

    public function categoryKeramicStandardlengths() {

      if (!isset($_SESSION['sl_depth'])) $_SESSION['sl_depth'] = '';

      if (isset($_POST["filter"])) {
        $_SESSION['sl_depth'] = $_POST['sl_depth'];
        ResponseHelper::redirect($this->category->getShopUrl());
      }

      //artikelen ophalen
      $filtquery = "";
      $filtquery .= "JOIN category_product ON category_product.product_id = product.id AND category_product.online = 1 AND category_product.void = 0 ";
      $filtquery .= "WHERE product.void = 0 AND category_product.category_id=" . $this->category->id . " ";
      if (SandboxUsers::isAdmin()) {
        $filtquery .= "AND (product.online_custshop=1 OR product.online_admin=1) ";
      }
      else {
        $filtquery .= "AND product.online_custshop=1 ";
      }
      $filtquery .= " ORDER BY category_product.sort ";

      //count
      $result = DBConn::db_link()->query("SELECT count(product.id) as som FROM product " . $filtquery);
      $row = $result->fetch_assoc();
      $this->pager->count = $row["som"];
      if (!$this->pager->count) $this->pager->count = 0;

      $filtquery = "SELECT * FROM product " . $filtquery;
      $filtquery .= $this->pager->getLimitQuery();

      $products = [];
      $result = DBConn::db_link()->query($filtquery);
      while ($row = $result->fetch_row()) {
        $product = new Product();
        $product->hydrate($row);
        $product->content = ProductContent::getByProductIdAndLang($product->id);

        $catprod = new CategoryProduct();
        $catprod->hydrate($row, count(Product::columns));
        $product->category_product = $catprod;
        $products[] = $product;
      }

      if (!empty($_SESSION['sl_depth'])) {
        foreach ($products as $k => $product) {
          $stoneId = str_replace("STONE_", "", $product->supplier_code);
          $stone = Stones::find_by(["stoneId" => $stoneId]);
          $show = false;
          if ($stone) {
            $stone_size = StoneSizes::find_by(["sizeId" => $stone->sizeId]);
            if ($stone_size && $stone_size->length * 10 == $_SESSION['sl_depth']) {
              $show = true;
            }
          }

          if (!$show) {
            unset($products[$k]);
          }
        }
      }


      $this->products = $products;
      $this->depths = [
        105 => "105 mm diep",
        160 => "160 mm diep",
        215 => "215 mm diep",
      ];
    }

    public function categoryNaturalStandardlengths() {

      if (!isset($_SESSION['sln_depth'])) $_SESSION['sln_depth'] = '';
      if (!isset($_SESSION['sln_thickness'])) $_SESSION['sln_thickness'] = '';

      if (isset($_POST["filter"])) {
        $_SESSION['sln_depth'] = $_POST['sln_depth'];
        $_SESSION['sln_thickness'] = $_POST['sln_thickness'];
        ResponseHelper::redirect($this->category->getShopUrl());
      }

      //artikelen ophalen
      $filtquery = "";
      $filtquery .= "JOIN category_product ON category_product.product_id = product.id AND category_product.online = 1 AND category_product.void = 0 ";
      $filtquery .= "WHERE product.void = 0 AND category_product.category_id=" . $this->category->id . " ";
      if (SandboxUsers::isAdmin()) {
        $filtquery .= "AND (product.online_custshop=1 OR product.online_admin=1) ";
      }
      else {
        $filtquery .= "AND product.online_custshop=1 ";
      }
      $filtquery .= " ORDER BY category_product.sort ";

      //count
      $result = DBConn::db_link()->query("SELECT count(product.id) as som FROM product " . $filtquery);
      $row = $result->fetch_assoc();
      $this->pager->count = $row["som"];
      if (!$this->pager->count) $this->pager->count = 0;

      $filtquery = "SELECT * FROM product " . $filtquery;
      $filtquery .= $this->pager->getLimitQuery();

      $products = [];
      $result = DBConn::db_link()->query($filtquery);
      while ($row = $result->fetch_row()) {
        $product = new Product();
        $product->hydrate($row);
        $product->content = ProductContent::getByProductIdAndLang($product->id);

        $catprod = new CategoryProduct();
        $catprod->hydrate($row, count(Product::columns));
        $product->category_product = $catprod;
        $products[] = $product;
      }

      if (!empty($_SESSION['sln_depth']) || !empty($_SESSION['sln_thickness'])) {
        foreach ($products as $k => $product) {
          $stoneId = str_replace("STONE_", "", $product->supplier_code);
          $stone = Stones::find_by(["stoneId" => $stoneId]);
          $show = true;
          if ($stone) {
            $stone_size = StoneSizes::find_by(["sizeId" => $stone->sizeId]);
            if (!empty($_SESSION['sln_depth']) && (!$stone_size || $stone_size->length * 10 != $_SESSION['sln_depth'])) {
              $show = false;
            }
            if (!empty($_SESSION['sln_thickness']) && (!$stone_size || $stone_size->height * 10 != $_SESSION['sln_thickness'])) {
              $show = false;
            }
          }
          if (!$show) {
            unset($products[$k]);
          }
        }
      }


      $this->products = $products;
      $this->depths = [
        120 => "120 mm diep",
        160 => "160 mm diep",
        210 => "210 mm diep",
      ];
      $this->thickness = [
        30 => "Dun (30/60 mm)",
        50 => "Dik (50/80 mm)",
      ];

    }


    public function executeProduct() {

      if (!isset($_GET['var2'])) {
        MessageFlashCoordinator::addMessageAlert('Helaas... dit product is uit de collectie of niet meer leverbaar. U bent doorgestuurd naar de homepage.');
        ResponseHelper::redirect301('/');
      }


      $product = Product::find_by_id($_GET['var2']);
      $show = ($product->online_custshop == 1 || (SandboxUsers::isAdmin() && $product->online_admin == 1));
      if (!$product || $product->void == 1 || !$show) {
        $_SESSION['flash_message_red'] = 'Helaas... dit product is uit de collectie of niet meer leverbaar. U bent doorgestuurd naar de homepage.';
        ResponseHelper::redirect301('/');
      }
      if ($_SERVER['REQUEST_URI'] != $product->getShopUrl()) { //elk product heeft maar 1 url! Voorkomen van duplicate content!
        ResponseHelper::redirect301($product->getShopUrl());
      }
      $product_images = ProductImage::getImagesByProductId($product->id);
      $brand = Brand::find_by_id($product->brand_id);
      $vals = explode('/', $_GET['rest']);
      $catproduct_id = $vals[0];
      $cat = null;
      $category = null;
      if (is_numeric($catproduct_id)) { //is er een categoryproduct id gedefinieerd, dan juiste catogorie ophalen
        $cat = CategoryProduct::find_by_id($catproduct_id); //dit haalt DE CategoryProduct op
        if (isset($cat->category_id)) {
          $filtextra = '';
          if (SandboxUsers::isAdmin()) {
            $filtextra = " AND (category.online_custshop=1 OR category.online_admin=1) ";
          }
          else {
            $filtextra = " AND category.online_custshop=1 ";
          }
          $category = Category::find_by_id($cat->category_id, $filtextra);
        }
      }
      if ($category == null) {
        //dit haalt EEN valide CategoryProduct op
        foreach (CategoryProduct::find_all_by(['product_id' => $product->id, 'void' => 0]) as $cat1) {
          $category = Category::find_by_id($cat1->category_id);
          $show = ($category->online_custshop == 1 || (SandboxUsers::isAdmin() && $category->online_admin == 1));
          if ($category && $category->void == 0 && $show) {
            $cat = $cat1;
            break;
          }
          $category = false;
        }

      }
      if (!$cat || $cat->void == 1 || !$category) {
        $_SESSION['flash_message_red'] = 'Helaas... deze categorie is uit de collectie of niet meer leverbaar. U bent doorgestuurd naar de homepage.';
        ResponseHelper::redirect301('/');
      }

      BreadCrumbs::getInstance()->removeLastItem(); //product

      $catparents = Category::getParents($category);
      $catparents_active = [];
      foreach ($catparents as $parcat) {
        BreadCrumbs::getInstance()->addItem($parcat->getName($_SESSION['lang']), $parcat->getShopUrl());
        $catparents_active[$parcat->id] = $parcat;
      }
      BreadCrumbs::getInstance()->addItem($category->getName($_SESSION['lang']), $category->getShopUrl());


      $seodesc = $product->getSeoDesc($_SESSION['lang']);
      if ($seodesc == "") {
        $seodesc .= $product->getSeoTitle($_SESSION['lang']);
      }
      $seodesc .= ' - ';
      $seotitle = $product->getSeoTitle($_SESSION['lang']) . ' - ';
      if ($brand) {
        $seotitle .= $brand->getName($_SESSION['lang']) . ' | ';
        $seodesc .= $brand->getName($_SESSION['lang']) . ' | ';
      }
      $seotitle .= $category->getName($_SESSION['lang']);
      $seodesc .= $category->getName($_SESSION['lang']);

      $this->seo_title = $seotitle;
      $this->seo_description = $seodesc;


      BreadCrumbs::getInstance()->removeLastItem(); //product
      $name = $product->getName($_SESSION['lang']);
      if ($brand) {
        //merk toevoegen aan breadcrumb
//          BreadCrumbs::getInstance()->addItem($brand->getName($_SESSION['lang']),$brand->getShopUrl());
      }
      BreadCrumbs::getInstance()->addItem($name);

      $this->product = $product;
      $this->product_images = $product_images;
      $this->category = $category;
      $this->brand = $brand;
      $this->catparents = $catparents;
      $this->catparents_active = $catparents_active;

      if (defined('CATALOG_BRAND') && CATALOG_BRAND) {
        $this->brand = Brand::find_by_id($product->brand_id);
      }

      if (defined('TAGS_PRODUCT') && TAGS_PRODUCT) {
        $this->producttagstr = Tag::getProducttagsString($product);
      }

      if (Config::isTrue('CATALOG_PRODUCT_RELATEDPRODUCTS')) {
        $this->productsrelated = ProductRelated::getProducts($product->id);
      }
      $isStoneproduct = false;
      $isStoneproductStandardSizes = false;
      if (substr((string)$product->supplier_code, 0, 6) == "STONE_") {
        $isStoneproduct = true;
        //deze categorieen bevatten standaard lengtes.
        $isStoneproductStandardSizes = in_array($category->id, [19, 20, 21]);

        $userId = '';
        if (isset($_SESSION["userObject"])) {
          $userId = $_SESSION["userObject"]->userId;
        }


        $stoneId = str_replace("STONE_", "", $product->supplier_code);
        $stone = Stones::find_by(["stoneId" => $stoneId]);
        $stone_size = StoneSizes::find_by(["sizeId" => $stone->sizeId]);
        $prices = [];
        $prices[$stone->stoneId] = Quotations::getPrices(date("Y-m-d"), $userId, $stone);

        //meter prijs berekenen.
        $meterprice = $stone->getMeterprice($prices[$stone->stoneId], $stone_size, false);

        if ($isStoneproductStandardSizes) {
          $stoneLengths = [];
          $lengths = ProductOption::getOptionValue($product->id, "lengths");
          if ($lengths !== false && $lengths !== "") {
            foreach (explode(";", $lengths) as $length) {
              $sc["name"] = $stone->name;
              $sc["length"] = $length;
//                $sc["help"] = "St";
              $sc["price"] = round($meterprice / 1000 * $length, 2);
              $stoneLengths[] = $sc;
            }
          }
          $this->stoneLengths = $stoneLengths;
        }
        else {
          $stoneproducts = [];
          $stoneproducts['standard'] = ["stone" => $stone];
          foreach ($stone->getSibelings() as $s) {
            if ($s->endstone == "rightg")
              continue; //negeren rechtste groef, is gelijk aan links groef
            $stoneproducts[$s->endstone] = ["stone" => $s];
          }
          $stoneproducts["leftglaced"] = ["stone" => $stone];
          $stoneproducts["rightglaced"] = ["stone" => $stone];
          $stoneproducts["leftmitre"] = ["stone" => $stone];
          $stoneproducts["rightmitre"] = ["stone" => $stone];

          foreach ($stoneproducts as $k => $sc) {
            if (!isset($prices[$sc["stone"]->stoneId])) {
              $prices[$sc["stone"]->stoneId] = Quotations::getPrices(date("Y-m-d"), $userId, $sc["stone"], true);
            }
            $price = $prices[$sc["stone"]->stoneId]; //prijs van deze steen

            $sc["price"] = 1000000;
            $sc["name"] = "";
            $sc["help"] = "";
            if ($k == "standard") {
              $sc["name"] = "Standaard steen";
              $sc["help"] = "Dit is de standaard raamdorpel steen.";
              $sc["price"] = $price["stonePrice"];
            }
            elseif ($k == "left") {
              $sc["name"] = "Eindsteen links";
              $sc["help"] = "Deze raamdorpel steen als eindsteen links.";
              $sc["price"] = $price["stonePriceEnd"];
            }
            elseif ($k == "right") {
              $sc["name"] = "Eindsteen rechts";
              $sc["help"] = "Deze raamdorpel steen als eindsteen rechts.";
              $sc["price"] = $price["stonePriceEnd"];
            }
            elseif ($k == "leftg") {
              $sc["name"] = "Eindsteen groef";
              $sc["help"] = "Deze raamdorpel steen als eindsteen met groef. De eindsteen groef zijn hetzelfde voor links en rechts.";
              $sc["price"] = $price["stonePriceGroove"];
            }
            elseif ($k == "leftglaced") {
              $sc["name"] = "Geglazuurd links";
              $sc["help"] = "Deze raamdorpel steen met de linker zijkant geglazuurd.";
              $sc["price"] = $price["glazesidePrice"];
            }
            elseif ($k == "rightglaced") {
              $sc["name"] = "Geglazuurd rechts";
              $sc["help"] = "Deze raamdorpel steen met de rechter zijkant geglazuurd.";
              $sc["price"] = $price["glazesidePrice"];
            }
            elseif ($k == "leftmitre") {
              $sc["name"] = "Verstek links";
              $sc["help"] = "Deze raamdorpel steen met verstekhoek aan de linkerzijde.";
            }
            elseif ($k == "rightmitre") {
              $sc["name"] = "Verstek rechts";
              $sc["help"] = "Deze raamdorpel steen met verstekhoek aand de rechterzijde.";
            }
            $stoneproducts[$k] = $sc;
          }

          $mitres = AppModel::mapObjectIds(Mitres::getMitresByStoneId($stone->stoneId), "mitreId");
          $mitrePrices = [];

          $mitresSelect = [
            '90° hoek'  => [],
            '135° hoek' => [],
            'Overige'   => [],
          ];
          foreach ($mitres as $mitre) {
            if ($mitre->angle == 45) {
              $mitresSelect['90° hoek'][] = $mitre;
            }
            elseif ($mitre->angle == 67.5) {
              $mitresSelect['135° hoek'][] = $mitre;
            }
            else {
              $mitresSelect['Overige'][] = $mitre;
            }

            //bepaal prijs mitres
            $mitrePrices[$mitre->mitreId] = $mitre->getMitrePrice($stone, $stone_size, $price);

          }

          $this->mitresSelect = $mitresSelect;
          $this->mitrePrices = $mitrePrices;
          $this->stoneproducts = $stoneproducts;

        }


        $this->size = StoneSizes::find_by(["sizeId" => $stone->sizeId]);
        $this->color = StoneColors::find_by(["colorId" => $stone->colorId]);
        $this->stoneId = $stone->stoneId;
        $this->meterprice = ceil($meterprice);
      }

      //facebook opengraph tags
      Context::addMetatag('og:type', 'product');
      if ($product->getPhoto(true)) {
        $this->seo_image = $this->site->site_host->getDomain(true) . $product->getPhoto(true);
      }


      $this->isStoneproduct = $isStoneproduct;
      $this->isStoneproductStandardSizes = $isStoneproductStandardSizes;

    }


  }