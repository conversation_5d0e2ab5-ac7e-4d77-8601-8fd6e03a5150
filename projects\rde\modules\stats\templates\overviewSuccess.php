<?php
  TemplateHelper::includePartial('_tabs.php',"stats");
?>

<?php echo TemplateHelper::includeJavascript(URL_INCLUDES . 'jsscripts/amcharts4/core'); ?>
<?php echo TemplateHelper::includeJavascript(URL_INCLUDES . 'jsscripts/amcharts4/charts'); ?>
<?php echo TemplateHelper::includeJavascript(URL_INCLUDES . 'jsscripts/amcharts4/themes/animated'); ?>

<div class="inzicht" id="chartyearlyorder"></div>
<div class="inzicht" id="chartstatus"></div>
<div class="inzicht" id="charttender"></div>
<div class="inzicht" id="chartorder"></div>
<div class="inzicht" id="charttendercount"></div>
<div class="inzicht" id="chartordercount"></div>
<div class="inzicht" id="chartordertodeliver"></div>

<script>
  charts = [];
  am4core.ready(function() {

    am4core.useTheme(am4themes_animated);
    getChart("chartyearlyorder");
    getChart("chartstatus");
    getChart("charttender");
    getChart("chartorder");
    getChart("charttendercount");
    getChart("chartordercount");
    getChart("chartordertodeliver");

  });

  function getChart(id) {
    var container = $("#"+id);
    container.show();
    var form = container.find("form");
    var get = null;
    if(form.length>0) {
      get = form.serialize();
    }
    var minheight = container.height();
    container.css("min-height",minheight+"px");
    container.html('<img src="/images/loading1.gif" class="rdeloading"/>');
    $.get("?action="+id, get).done(function (html) {
      container.html(html);
    });
  }


  function createBarchart(id, chartdata) {
    if(charts[id]) {
      charts[id].dispose();
    }
    // Create chart instance
    var chart = am4core.create(id, am4charts.XYChart);
    chart.data = chartdata.data
    chart.language.locale["_decimalSeparator"] = ",";
    chart.language.locale["_thousandSeparator"] = ".";

    /* Create axes */
    var categoryAxis = chart.xAxes.push(new am4charts.CategoryAxis());
    categoryAxis.dataFields.category = "category";
    categoryAxis.renderer.minGridDistance = 30;
    // categoryAxis.numberFormatter = new am4core.NumberFormatter();
    // categoryAxis.numberFormatter.numberFormat = "###.";

    if(chartdata.series.length>1) {
      categoryAxis.renderer.grid.template.location = 0;
      categoryAxis.renderer.cellStartLocation = 0.05;
      categoryAxis.renderer.cellEndLocation = 0.95;
    }

    /* Create value axis */
    var valueAxis = chart.yAxes.push(new am4charts.ValueAxis());
    valueAxis.min = 0;
    valueAxis.extraMax = 0.1;
    // valueAxis.numberFormatter.numberFormat =  '€ #,###.';
    //

    var clustered = chartdata.series.length>1;
    for (var seriek in chartdata.series) {
      var serie = chartdata.series[seriek];
      createSerie(chart,serie, clustered);
    }

    chart.legend = new am4charts.Legend();

    charts[id] = chart;

  }

  function createSerie(chart, value, clustered) {


    var columnSeries = chart.series.push(new am4charts.ColumnSeries());
    columnSeries.dataFields.categoryX = "category";
    columnSeries.dataFields.valueY = value.valuecolumn;
    columnSeries.name = value.name;
    columnSeries.tooltip.label.textAlign = "middle"
    if(clustered) columnSeries.columns.template.width = am4core.percent(100);

    var labelBullet2 = columnSeries.bullets.push(new am4charts.LabelBullet());
    labelBullet2.label.verticalCenter = "bottom";
    labelBullet2.label.dy = -10;
    labelBullet2.label.text = "{valueY}";

    var columnTemplate2 = columnSeries.columns.template;
    // columnTemplate.tooltipText = "[#fff font-size: 15px]{name} in {categoryX}:\n[/][#fff font-size: 20px]{valueY.formatNumber('€ #,###.')}[/] [#fff]{additional}[/]"
    columnTemplate2.tooltipText = "[#fff font-size: 15px]{name} in {categoryX}:\n[/][#fff font-size: 20px]{valueY}[/] [#fff]{additional}[/]"
    columnTemplate2.propertyFields.fillOpacity = "fillOpacity";
    columnTemplate2.propertyFields.stroke = "stroke";
    columnTemplate2.propertyFields.strokeWidth = "strokeWidth";
    columnTemplate2.propertyFields.strokeDasharray = "columnDash";
  }

</script>

<style>
  .inzicht {
    border: 1px solid #F0F0F0;
    padding: 15px;
    min-height: 350px;
    margin-bottom: 15px;
    display: none;
  }
  .inzicht h3 {
    margin-top: 0;
  }

  .rdechart {
    width: 100%;
    height: 350px;
  }
  .rdeloading {
    margin: 0 auto;
    width: 150px;
    display: block;
  }
  .filter {
    padding: 10px 0;
    background-color: #f2f2f229;
  }
</style>