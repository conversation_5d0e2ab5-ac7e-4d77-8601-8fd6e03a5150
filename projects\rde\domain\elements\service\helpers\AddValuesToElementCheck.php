<?php

  namespace domain\elements\service\helpers;

  class AddValuesToElementCheck {
    public function addValuesToAElementCheck($partNr, $aMitreElementParts, $stoneAmountB): array {
      $arrayElementCheck = [];
      if($partNr === 2) {
        $arrayElementCheck['aElementCheck'] = $aMitreElementParts['firstLengthCheck'];
        $arrayElementCheck['bElementCheck'] = $aMitreElementParts['lastLengthCheck'];
      }
      elseif($partNr === 3) {
        $arrayElementCheck['aElementCheck'] = $aMitreElementParts['firstLengthCheck'];
        $arrayElementCheck['bElementCheck'] = $aMitreElementParts['bLength1Check'];
        $arrayElementCheck['cElementCheck'] = $aMitreElementParts['lastLengthCheck'];
      }
      elseif($partNr >= 4) {
        $arrayElementCheck['aElementCheck'] = $aMitreElementParts['firstLengthCheck'];
        $arrayElementCheck['bElementCheck'] = $aMitreElementParts['bLength1Check'];
        if(floor($stoneAmountB) == $stoneAmountB) {
          // whole number
          $arrayElementCheck['cElementCheck'] = $aMitreElementParts['lastLengthCheck'];
        }
        else {
          // not a whole number
          $arrayElementCheck['cElementCheck'] = $aMitreElementParts['cLength1Check'];
          $arrayElementCheck['dElementCheck'] = $aMitreElementParts['lastLengthCheck'];
        }
      }
      return $arrayElementCheck;
    }
  }