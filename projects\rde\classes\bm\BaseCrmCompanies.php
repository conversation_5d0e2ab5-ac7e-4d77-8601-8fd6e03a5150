<?php
class BaseCrmCompanies extends AppModel
{
  const DB_NAME = 'rde_cms';
  const TABLE_NAME = 'crm_companies';
  const OM_CLASS_NAME = 'CrmCompanies';
  const columns = ['companyId', 'name', 'visitAddressId', 'postAddressId', 'invoicePartyId', 'receiveDM', 'receiveDMA', 'tradeRegNo', 'establishmentNumber', 'country', 'phone', 'fax', 'email', 'url', 'customer', 'customerGroupId', 'paymentTerm', 'categoryId', 'noRack', 'noRackNoContainer', 'noContainer', 'pallet', 'afhalen', 'stJoris', 'terca', 'introDiscount', 'blocked', 'containerDoNotReturn', 'toNumber', 'callToDelivery', 'sendMailToClient', 'executorTicket', 'sendByPost', 'rackContainerReturn', 'documentFlag', 'notes', 'dateLiquidated', 'dateCreated', 'lastUpdate', 'flagForDeletion', 'noDelivery', 'projectInfo', 'customerNotes', 'callOrEmailNotes', 'employees', 'businessForm', 'tradeGroup', 'tradeRegistrationDate', 'carrierCode', 'pay_online', 'pay_after', 'payInAdvance', 'cargo_receipt_mail_1', 'cargo_receipt_mail_2', 'cargo_receipt_mail_3'];
  const field_structure = [
    'companyId'                   => ['type' => 'int', 'length' => '7', 'null' => false],
    'name'                        => ['type' => 'varchar', 'length' => '200', 'null' => false],
    'visitAddressId'              => ['type' => 'int', 'length' => '8', 'null' => true],
    'postAddressId'               => ['type' => 'int', 'length' => '8', 'null' => true],
    'invoicePartyId'              => ['type' => 'int', 'length' => '7', 'null' => true],
    'receiveDM'                   => ['type' => 'boolean', 'length' => '1', 'null' => false],
    'receiveDMA'                  => ['type' => 'boolean', 'length' => '1', 'null' => false],
    'tradeRegNo'                  => ['type' => 'varchar', 'length' => '12', 'null' => true],
    'establishmentNumber'         => ['type' => 'varchar', 'length' => '12', 'null' => true],
    'country'                     => ['type' => 'varchar', 'length' => '2', 'null' => false],
    'phone'                       => ['type' => 'varchar', 'length' => '15', 'null' => true],
    'fax'                         => ['type' => 'varchar', 'length' => '15', 'null' => true],
    'email'                       => ['type' => 'varchar', 'length' => '150', 'null' => true],
    'url'                         => ['type' => 'varchar', 'length' => '120', 'null' => true],
    'customer'                    => ['type' => 'boolean', 'length' => '1', 'null' => false],
    'customerGroupId'             => ['type' => 'tinyint', 'length' => '2', 'null' => true],
    'paymentTerm'                 => ['type' => 'tinyint', 'length' => '3', 'null' => true],
    'categoryId'                  => ['type' => 'tinyint', 'length' => '3', 'null' => true],
    'noRack'                      => ['type' => 'boolean', 'length' => '1', 'null' => false],
    'noRackNoContainer'           => ['type' => 'boolean', 'length' => '1', 'null' => false],
    'noContainer'                 => ['type' => 'boolean', 'length' => '1', 'null' => false],
    'pallet'                      => ['type' => 'boolean', 'length' => '1', 'null' => false],
    'afhalen'                     => ['type' => 'boolean', 'length' => '1', 'null' => false],
    'stJoris'                     => ['type' => 'boolean', 'length' => '1', 'null' => false],
    'terca'                       => ['type' => 'boolean', 'length' => '1', 'null' => false],
    'introDiscount'               => ['type' => 'boolean', 'length' => '1', 'null' => false],
    'blocked'                     => ['type' => 'boolean', 'length' => '1', 'null' => false],
    'containerDoNotReturn'        => ['type' => 'boolean', 'length' => '1', 'null' => false],
    'toNumber'                    => ['type' => 'boolean', 'length' => '1', 'null' => false],
    'callToDelivery'              => ['type' => 'boolean', 'length' => '1', 'null' => false],
    'sendMailToClient'            => ['type' => 'boolean', 'length' => '1', 'null' => false],
    'executorTicket'              => ['type' => 'boolean', 'length' => '1', 'null' => false],
    'sendByPost'                  => ['type' => 'boolean', 'length' => '1', 'null' => false],
    'rackContainerReturn'         => ['type' => 'boolean', 'length' => '1', 'null' => false],
    'documentFlag'                => ['type' => 'boolean', 'length' => '1', 'null' => false],
    'notes'                       => ['type' => 'text', 'length' => '', 'null' => true],
    'dateLiquidated'              => ['type' => 'date', 'length' => '', 'null' => true],
    'dateCreated'                 => ['type' => 'datetime', 'length' => '', 'null' => false],
    'lastUpdate'                  => ['type' => 'timestamp', 'length' => '', 'null' => false],
    'flagForDeletion'             => ['type' => 'boolean', 'length' => '1', 'null' => false],
    'noDelivery'                  => ['type' => 'boolean', 'length' => '1', 'null' => false],
    'projectInfo'                 => ['type' => 'text', 'length' => '', 'null' => true],
    'customerNotes'               => ['type' => 'text', 'length' => '', 'null' => true],
    'callOrEmailNotes'            => ['type' => 'text', 'length' => '', 'null' => true],
    'employees'                   => ['type' => 'int', 'length' => '11', 'null' => false],
    'businessForm'                => ['type' => 'varchar', 'length' => '255', 'null' => true],
    'tradeGroup'                  => ['type' => 'varchar', 'length' => '255', 'null' => true],
    'tradeRegistrationDate'       => ['type' => 'date', 'length' => '', 'null' => true],
    'carrierCode'                 => ['type' => 'varchar', 'length' => '255', 'null' => true],
    'pay_online'                  => ['type' => 'boolean', 'length' => '1', 'null' => false],
    'pay_after'                   => ['type' => 'boolean', 'length' => '1', 'null' => false],
    'payInAdvance'                => ['type' => 'boolean', 'length' => '1', 'null' => false],
    'cargo_receipt_mail_1'        => ['type' => 'varchar', 'length' => '150', 'null' => true],
    'cargo_receipt_mail_2'        => ['type' => 'varchar', 'length' => '150', 'null' => true],
    'cargo_receipt_mail_3'        => ['type' => 'varchar', 'length' => '150', 'null' => true],
  ];

  protected static $primary_key = ['companyId'];
  protected $auto_increment = 'companyId';

  public $companyId, $name, $visitAddressId, $postAddressId, $invoicePartyId, $receiveDM, $receiveDMA, $tradeRegNo, $establishmentNumber, $country, $phone, $fax, $email, $url, $customer, $customerGroupId, $paymentTerm, $categoryId, $noRack, $noRackNoContainer, $noContainer, $pallet, $afhalen, $stJoris, $terca, $introDiscount, $blocked, $containerDoNotReturn, $toNumber, $callToDelivery, $sendMailToClient, $executorTicket, $sendByPost, $rackContainerReturn, $documentFlag, $notes, $dateLiquidated, $dateCreated, $lastUpdate, $flagForDeletion, $noDelivery, $projectInfo, $customerNotes, $callOrEmailNotes, $employees, $businessForm, $tradeGroup, $tradeRegistrationDate, $carrierCode, $pay_online, $pay_after, $payInAdvance, $cargo_receipt_mail_1, $cargo_receipt_mail_2, $cargo_receipt_mail_3;

  function __construct($obj_arr = [], &$error_codes = []) {
    parent::__construct();
    $this->setDefaults();
    if (!empty($obj_arr)) {
      $this->from_array($obj_arr);
      $this->valid_required_fields($error_codes);
    }
  }

  /**
  * Set default object values
  **/
  public function setDefaults() {
    $this->receiveDM = 1;
    $this->receiveDMA = 1;
    $this->country = 'NL';
    $this->customer = 0;
    $this->noRack = 0;
    $this->noRackNoContainer = 0;
    $this->noContainer = 0;
    $this->pallet = 0;
    $this->afhalen = 0;
    $this->stJoris = 1;
    $this->terca = 1;
    $this->introDiscount = 0;
    $this->blocked = 0;
    $this->containerDoNotReturn = 0;
    $this->toNumber = 0;
    $this->callToDelivery = 0;
    $this->sendMailToClient = 0;
    $this->executorTicket = 0;
    $this->sendByPost = 0;
    $this->rackContainerReturn = 0;
    $this->documentFlag = 0;
    $this->lastUpdate = 'current_timestamp()';
    $this->flagForDeletion = 0;
    $this->noDelivery = 0;
    $this->employees = 0;
    $this->pay_online = 1;
    $this->pay_after = 1;
    $this->payInAdvance = 0;
  }

  /**
   * @param array $conditions (required)
   * @param string $raw_sql (optional)
   * @return CrmCompanies[]
   **/
  public static function find_all_like($conditions, $raw_sql = '') {
    return parent::generic_find_all_like($conditions, static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param array $conditions (required)
   * @param string $raw_sql (optional)
   * @return CrmCompanies[]
   **/
  public static function find_all_by($conditions, $raw_sql = '') {
    return parent::generic_find_all_by($conditions, static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param string $raw_sql (optional)
   * @return CrmCompanies[]
   **/
  public static function find_all($raw_sql = '') {
    return parent::generic_find_all(static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param array $conditions (required)
   * @param string $raw_sql (optional)
   * @return CrmCompanies
   **/
  public static function find_by($conditions, $raw_sql = '') {
    return parent::generic_find_by($conditions, static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param string $id (required)
   * @param string $raw_sql (optional)
   * @return CrmCompanies
   **/
  public static function find_by_id($id, $raw_sql = '') {
    return parent::generic_find_by_id($id, static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param array $conditions (required)
   * @param string $raw_sql (optional)
   * @return int
   **/
  public static function count_all_by($conditions, $raw_sql = '') {
    return parent::generic_count_all_by($conditions, static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param array $conditions (required)
   * @param string $raw_sql (optional)
   * @return bool
   **/
  public static function delete_by($conditions, $raw_sql = '') {
    return parent::generic_delete_by($conditions, static::OM_CLASS_NAME, $raw_sql);
  }

}