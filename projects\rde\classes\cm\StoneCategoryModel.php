<?php

  AppModel::loadBaseClass('BaseStoneCategory');

  class StoneCategoryModel extends BaseStoneCategory {

    public function getChildren() {
      return StoneCategory::find_all_by(['parent_id' => $this->id], "ORDER BY sort");
    }

    /**
     * Get the category tree sorted.
     * @param bool $onlyOnline
     * @return StoneCategory[]
     */
    public static function getTree(bool $onlyOnline = false) {

      $filt = [];
      if ($onlyOnline) {
        $filt = ["online" => 1];
      }
      $allCategories = StoneCategory::find_all_by($filt);
      foreach ($allCategories as $key => $cat) {
        foreach ($allCategories as $pkey => $searchparent) {
          if ($searchparent->id == $cat->parent_id) {
            $allCategories[$pkey]->children[$cat->id] = $cat;
          }
        }
      }

      //opruimen niet root cats
      foreach ($allCategories as $key => $cat) {
        if ($cat->parent_id != null) {
          unset($allCategories[$key]);
        }
      }

      self::sortTree($allCategories);

      return $allCategories;
    }


    /**
     * Flattens a nested category tree. A tree is flattend as an array withhout children. This is handy for select boxes.
     * @param array $category_tree
     * @return StoneCategory[]
     */
    public static function flattenCategoryTree($category_tree) {
      $flatten_tree = [];

      foreach ($category_tree as $cat) {
        $children = [];
        if (isset($cat->children) && count($cat->children) > 0) {
          $children = $cat->children;
        }

        //same cat, so just create new clean object
        $ncat = new StoneCategory($cat->asArray());
        $ncat->from_db = 1;
        if (isset($cat->content)) $ncat->content = $cat->content;

        if (isset($cat->product_ids)) $ncat->product_ids = $cat->product_ids;
        $flatten_tree[$ncat->id] = $ncat;
        unset($flatten_tree[$ncat->id]->children);

        if (count($children) > 0) {
          $flatten_tree = $flatten_tree + self::flattenCategoryTree($children);
        }
      }
      return $flatten_tree;
    }

    /**
     * Sort a tree on property sort
     * @param StoneCategory[] $allCategories
     * @return void
     */
    private static function sortTree(&$allCategories) {
      usort($allCategories, function ($a, $b) {
        return $a->sort - $b->sort;
      });
      foreach ($allCategories as $cat) {
        if (isset($cat->children) && count($cat->children) > 0) {
          self::sortTree($cat->children);
        }
      }
    }

    /**
     * @param StoneCategory $category
     * @param $top
     * @return StoneCategory[]|null
     */
    public static function getParents($category, $top = null) {
      if ($category == null) return null;
      $items = [];
      $items[0] = $category;
      while ($items[0]->parent_id != $top) {
        $items = ArrayHelper::arrayInsert($items, 0, StoneCategory::find_by_id($items[0]->parent_id));
      }
      return $items;
    }


    public function removeImageFile() {
      if ($this->imagefilename != "") {
        if (file_exists(DIR_UPLOADS . 'stonecategoryimages/' . $this->imagefilename)) {
          unlink(DIR_UPLOADS . 'stonecategoryimages/' . $this->imagefilename);
        }
        $this->imagefilename = null;
      }
    }


    public function destroy() {
      //verwijder ook alle kinderen
      foreach ($this->getChildren() as $child) {
        $child->destroy();
      }

      $this->removeImageFile();
      parent::destroy();

    }

  }