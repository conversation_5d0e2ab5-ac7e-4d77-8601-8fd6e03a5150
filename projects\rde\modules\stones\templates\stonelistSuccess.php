<?php TemplateHelper::includePartial('_tabs.php', 'stones'); ?>

<div class="box">
    <form method="post" action="<?php echo reconstructQueryAdd() ?>">
      <input type="text" name="stone_search" value="<?php echo $_SESSION['stone_search'] ?>" placeholder="Zoeken..."/>
      <select name="stone_brand" id="stone_brand">
        <option value="">Selecteer merk...</option>
        <?php foreach($brands as $brand): ?>
          <option value="<?php echo $brand->brandId ?>" <?php if($_SESSION['stone_brand']==$brand->brandId) echo 'selected'; ?>><?php echo $brand->name ?></option>
        <?php endforeach; ?>
      </select>
      <select name="stone_type" id="stone_type">
        <option value="">Selecteer type...</option>
        <?php foreach(Stones::TYPES as $k=>$name): ?>
          <option value="<?php echo $k ?>" <?php if($_SESSION['stone_type']==$k) echo 'selected'; ?>><?php echo $name ?></option>
        <?php endforeach; ?>
      </select>
      <select name="stone_material" id="stone_material">
        <option value="">Selecteer materiaal...</option>
        <?php foreach(Stones::MATERIALS as $k=>$name): ?>
          <option value="<?php echo $k ?>" <?php if($_SESSION['stone_material']==$k) echo 'selected'; ?>><?php echo $name ?></option>
        <?php endforeach; ?>
      </select>
      <select name="stone_color" id="stone_color">
        <option value="">Selecteer kleur...</option>
        <?php foreach($colors as $color): ?>
          <option value="<?php echo $color->colorId ?>" <?php if($_SESSION['stone_color']==$color->colorId) echo 'selected'; ?>><?php echo $brands[$color->brandId]->name ?> - <?php echo $color->name ?> <?php echo $color->short ?></option>
        <?php endforeach; ?>
      </select>
      <select name="stone_display"  id="stone_display">
        <option value="">Selecteer zichtbaar...</option>
        <option value="true" <?php if($_SESSION['stone_display']=="true") echo 'selected'; ?>>Ja</option>
        <option value="false" <?php if($_SESSION['stone_display']=="false") echo 'selected'; ?>>Nee</option>
      </select>
      <input type="submit" name="go" id="go" value="Zoeken" />

      <a href="?action=stoneedit" class="gsd-btn gsd-btn-primary">Toevoegen nieuwe steen</a>
    </form>
  </div>

  <?php $pager->writePreviousNext(); ?>

  <?php if(count($items)==0): ?>
    <br/>
    Er zijn nog geen items gevonden.
  <?php else: ?>
    <table class="default_table" style="width: auto;">
      <tr class="dataTableHeadingRow">
        <td>Naam</td>
        <td>Merk</td>
        <td>Type</td>
        <td>Materiaal</td>
        <td>Categorie</td>
        <td>Zichtbaar</td>
        <td>Te bestellen</td>
        <td style="width: 70px;">Actie</td>
      </tr>
      <?php
        /** @var Stones $item */
        foreach($items as $item): ?>
        <tr class="dataTableRow trhover">
          <td><?php echo $item->name ?></td>
          <td><?php echo $brands[$item->brandId]->name ?></td>
          <td><?php echo Stones::TYPES[$item->type] ?></td>
          <td><?php echo Stones::MATERIALS[$item->material] ?></td>
          <td><?php
              $path = [];
              $currentCat = $categories[$item->category_id];
              $path[] = $currentCat->name;
              while($currentCat->parent_id!="") {
                $currentCat = $categories[$currentCat->parent_id];
                $path[] = $currentCat->name;
              }
              echo implode(" > ",array_reverse($path));

            ?></td>
          <td><?php echo $item->display=="true"?"Ja":"Nee" ?></td>
          <td><?php echo $item->may_order==1?"Ja":"Nee" ?></td>
          <td>
            <?php echo BtnHelper::getEdit(reconstructQueryAdd(['pageId']).'action=stoneedit&id='.$item->stoneId) ?>
          </td>
        </tr>
      <?php endforeach; ?>
    </table>
  <?php endif; ?>
<script>
  $(document).ready(function() {
    $("#stone_brand,#stone_display,#stone_color,#stone_type,#stone_material").change(function() {
      $("#go").click();
    })
  });
</script>