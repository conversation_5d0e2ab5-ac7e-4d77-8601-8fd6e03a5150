<?php
  $price = StringHelper::getPriceDot($product->getPriceByUser());
  $product->writeStructuredDataScript($site);
?>
<section id="productpage" class="row">

  <?php TemplateHelper::includePartial("_topmessage.php","siteshop") ?>

  <div class="col12">
    <h1>
      <?php echo escapeSafe($product->getName($_SESSION['lang'])) ?>
    </h1>
  </div>

  <div class="col6 col12-xs" style="padding-top: 15px;">
    <?php if($product->getPhoto(false)) : ?>
        <div class="productimage"><img class="img-responsive zoom" src="<?php echo $product->getPhoto(true) ?>" title="<?php echo escapeForInput($product->getName($_SESSION['lang'])) ?>" alt="<?php echo escapeForInput($product->getName($_SESSION['lang'])) ?>"/></div>
      <?php else: ?>
        <div class="productimage"><img class="img-responsive" src="<?php echo $_SESSION['site']->getTemplateUrl() ?>images/noimage.jpg" alt="<?php echo escapeForInput($product->getName($_SESSION['lang'])) ?>"/></div>
      <?php endif; ?>
    <?php foreach($product_images as $key=>$pimage):
      if($pimage->getUrlThumb(true, $product->content->name) == $product->getPhoto(false)) continue; //stel dat eerste foto niet vanaf product komt dan pimage niet nogmaals tonen
      ?>
      <div class="productimage"><img class="img-responsive" src="<?php echo $pimage->getUrlThumb(true, $product->getName($_SESSION['lang']))?>" title="<?php echo escapeForInput($product->getName($_SESSION['lang'])) ?>" alt="<?php echo escapeForInput($product->getName($_SESSION['lang'])) ?>"/></div>
    <?php endforeach; ?>
  </div>

  <div class="col6 col12-xs contenttxt"  style="padding-top: 15px;padding-bottom: 15px;">

    <?php echo $product->getDescription($_SESSION['lang']) ?>

    <table id="productprops">
      <?php if(isset($brand) && $brand!=null): ?>
        <?php if($product->pdf_filename!=""):?>
          <tr>
            <td>Document</td>
            <td>
              <?php echo '<a href="'.URL_UPLOAD_PDF.$product->pdf_filename.'" target="_blank" style="text-decoration: none;" title="Download PDF '.$product->getName($_SESSION['lang']).'"> Download PDF '.$product->getName($_SESSION['lang']).'</a>' ?>
            </td>
          </tr>
        <?php endif; ?>
      <?php endif; ?>
    </table>
  </div>

  <div class="col12">
    <div>
      <span class="hide"><?php echo $price ?></span>

      <form method="post" action="<?php echo PageMap::getUrl('M_BASKET') ?>">
        <?php if($isStoneproduct && !$isStoneproductStandardSizes): ?>
          <?php include("_product_orderform.php") ?>
        <?php elseif($isStoneproduct && $isStoneproductStandardSizes): ?>
          <?php include("_product_orderform_standardsizes.php") ?>
        <?php else: ?>
          <div class="product-addtocart-div">
            <?php if($product->getStaffel() != ""): ?>
              <div>
                <h3>Staffelprijs</h3><br/>
                <table class="table">
                  <tr>
                    <td class="head">Aantal</td>
                    <?php foreach ($product->getStaffel() as $key => $sprice): ?>
                      <td><?php echo number_format($key,0,",",".") ?>+</td>
                    <?php endforeach; ?>
                  </tr>
                  <tr>
                    <td class="head">Stukprijs</td>
                    <?php foreach ($product->getStaffel() as $key => $sprice): ?>
                      <td style="text-align: right;"><?php echo $product->price_on_request ?
                            "Prijs op aanvraag" :
                            "€" . StringHelper::getPriceDot($product->getPriceByUser($_SESSION['userObject'] ?? null, false, $key))
                        ?></td>
                    <?php endforeach; ?>
                  </tr>
                </table>
              </div>
              <div style="padding-right: 15px;margin-left: auto;">
                <?php if($product->discountgroup_id==4): ?>
                  <div class="select">
                    <select name="size[<?php echo $product->id ?>]" id="prod_<?php echo $product->id ?>" data-id="<?php echo $product->id ?>" class="sizeselect">
                      <?php
                        $start = 100;
                        $step = 100;
                        if($product->id==676 || $product->id==682) { //ventiklik
                          $start = 500;
                          $step = 500;
                        }
                      ?>
                      <option value="">Selecteer...</option>
                      <?php for($tel=$start;$tel<=100000;$tel+=$step): ?>
                        <option value="<?php echo $tel ?>" <?php if($tel==$start) echo 'selected' ?>><?php echo $tel ?></option>
                        <?php
                        if($tel>=5000) $step=1000;
                        if($tel>=50000) $step=10000;
                        ?>
                      <?php endfor; ?>
                    </select>
                  </div>
                <?php else: ?>
                  Aantal:
                  <input title="Aantal producten" class="form-input productsize" type="text" value="" name="size[<?php echo $product->id ?>]" style="width: 70px;"/>
                  <div style="font-size: 16px; display:inline-block;padding: 4px;">
                    <a href="#" class="mininput fa fa-minus-circle" title="Aantal -1"></a>
                    <a href="#" class="plusinput fa fa-plus-circle" title="Aantal +1"></a>
                  </div>
                <?php endif; ?>
              </div>
            <?php else: ?>
              <div>
                <b>
                  <span class="ft-green bold">
                    <?php echo $product->price_on_request ?
                      "Prijs op aanvraag" :
                      "€" . getLocalePrice($price) . " per stuk"
                    ?>
                  </span>
                </b>
              </div>
              <div style="padding-right: 15px;margin-left: auto;">
                Aantal:
                <input title="Aantal producten" class="form-input productsize" type="text" value="" name="size[<?php echo $product->id ?>]" style="width: 70px;"/>
                <div style="font-size: 16px; display:inline-block;padding: 4px;">
                  <a href="#" class="mininput fa fa-minus-circle" title="Aantal -1"></a>
                  <a href="#" class="plusinput fa fa-plus-circle" title="Aantal +1"></a>
                </div>
              </div>
            <?php endif; ?>
            <div class="cart-button">
              <button class="btn btn-cart bg-red" type="submit" name="add" id="add" title="<?php echo escapeForInput($product->getName($_SESSION['lang'])) ?>">
                IN WINKELMANDJE
                <i class="fa fa-shopping-cart ft-yellow bg-grey"></i>
              </button>
            </div>
          </div>
        <?php endif; ?>
      </form>

    </div>
  </div>

  <?php if($product->youtube_vid!=""): ?>
    <div class="col12">
      <br/>
      <div class="video-container">
        <?php echo "<br/>" . $product->getYoutubeEmbedcode(350, 223) . "<br/>"; ?>
      </div>
    </div>
  <?php endif; ?>

</section>

<script type="text/javascript">
  $(document).ready(function(){
    $(".productsize").val('<?php echo $isStoneproduct?"":1 ?>');
    $(".productsize").keydown(function(event) {
      // Allow: backspace, delete, tab and escape
      if ( event.keyCode == 46 || event.keyCode == 8 || event.keyCode == 9 || event.keyCode == 27 ||
        // Allow: Ctrl+A
        (event.keyCode == 65 && event.ctrlKey === true) ||
        // Allow: home, end, left, right
        (event.keyCode >= 35 && event.keyCode <= 39)) {
        // let it happen, don't do anything
        return;
      }
      else {
        // Ensure that it is a number and stop the keypress
        if ((event.keyCode < 48 || event.keyCode > 57) && (event.keyCode < 96 || event.keyCode > 105 )) {
          event.preventDefault();
        }
      }
    });

    $(".plusinput").click(function (event) {
      event.preventDefault();
      var size = $(this).parent().parent().find(".productsize");
      if(size.val()!="") {
        size.val(parseInt(size.val())+1);
      }
      else {
        size.val(1);
      }
    });

    $(".mininput").click(function (event) {
      event.preventDefault();
      var size = $(this).parent().parent().find(".productsize");
      if(size.val()!="") {
        var newval = parseInt(size.val())-1;
        if(newval<=0) {
          size.val("");
        }
        else {
          size.val(newval);
        }
      }
      else {
        size.val("");
      }
    });

    $("#add").click(function(e) {
      if($(".sizeselect").length==1) {
        if($(".sizeselect").val()=="") {
          e.preventDefault();
          swal("Aantal","Selecteer minimaal 1 product","error").catch(swal.noop);
        }
      }
    });

  });
</script>