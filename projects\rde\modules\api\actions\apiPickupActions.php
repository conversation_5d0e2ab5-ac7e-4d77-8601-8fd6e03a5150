<?php


  /**
   * Trait apiPickupActions
   * Used for calls for pickup actions
   */
  trait apiPickupActions {

    /**
     * Get picking companies
     * Orders of same company are folded into 1 item
     */
    public function executeGetPickingCompanies() {

      //ophalen alle geproduceerde orders, gegroeppeerd per klant
      $query = "SELECT Q.companyId, GROUP_CONCAT(Q.quotationNumber,'-',Q.quotationVersion SEPARATOR ', ') as quotations, Q.userId, if(C.name IS NULL,sandbox_users.companyName,C.name) as name  ";
      $query .= "FROM " . Quotations::getTablename() . " Q ";
      $query .= "LEFT JOIN " . CrmCompanies::getTablename() . " C ON C.companyId = Q.companyId ";
      $query .= "LEFT JOIN " . SandboxUsers::getTablename() . " ON sandbox_users.userId = Q.userId ";
      $query .= "WHERE Q.statusId IN (40, 50) ";
      $query .= "GROUP BY Q.userId "; //companyId is zero with particulier
      $query .= "ORDER BY name ";

//      $oResult = DBConn::db_link()->query($query);
//      $dataArr = [];
//      while ($oData = $oResult->fetch_object()) {
//        if($oData->companyId == "") {
//          $oData->companyId = -1;
//          //          $oData->name = "";
//        }
//
//        $oData->name .= " [".$oData->quotations."]";
//        unset($oData->quotations);
//
//        $dataArr[] = $oData;
//      }
//
//      RestUtils::sendResponseOK("PICKING COMPANIES SUCCESFULLY RETRIEVED",$dataArr);

      //start groepering per bedrijf.

      $companies = [];
      $privates = [];
      $oResult = DBConn::db_link()->query($query);
      while ($company = $oResult->fetch_object()) {
        if ($company->companyId == "" || $company->companyId == 0) { //particulier
          $company->companyId = -1;
          $privates[] = $company;
        }
        else {
          $companies[$company->companyId][] = $company;
        }
      }

      $allCustomers = [];
      foreach ($privates as $private) {
        $private->name .= " [" . $private->quotations . "]";
        unset($private->quotations);
        $allCustomers[] = $private;
      }

      foreach ($companies as $companyId => $items) {
        $mergedCompany = new stdClass();
        $mergedCompany->companyId = $companyId;
        $mergedCompany->userId = $items[0]->userId;
        $mergedCompany->name = $items[0]->name;
        //$mergedCompany->robert = count($items);
        $mergedCompany->name .= " [";
        foreach ($items as $company) {
          $mergedCompany->name .= $company->quotations . ", ";
        }
        $mergedCompany->name = substr($mergedCompany->name, 0, -2) . "]";
        $allCustomers[] = $mergedCompany;
      }

      RestUtils::sendResponseOK("PICKING COMPANIES SUCCESFULLY RETRIEVED", $allCustomers);

    }


    /**
     * Bedrijf en offertes van dit bedrijf.
     * Alle offertes van een bedrijf kun je verwerken, oftewel ik snor de companyId op van de user, en haal daarbij de offertes op
     *
     * @param bool $userId
     * @param bool $return
     * @return stdClass
     */
    public function executeGetPickingCompanyData($userId = false, $return = false) {
      $request_vars = $this->data->getData();
      if ($userId === false) {
        $userId = $request_vars['user_id'];
      }

      $sUser = SandboxUsers::getById($userId);
      $quotationsObjs = [];
      if ($sUser && $sUser->companyId != "") {
        $quotationsObjs = Quotations::find_all_by(["companyId" => $sUser->companyId, "statusId" => [Status::STATUS_PRODUCED, Status::STATUS_PACKED]], "ORDER BY quotationNumber");
      }
      else {
        $quotationsObjs = Quotations::find_all_by(["userId" => $userId, "statusId" => [Status::STATUS_PRODUCED, Status::STATUS_PACKED]], "ORDER BY quotationNumber");
      }

      $data = new stdClass();
      $data->userId = $userId;

      //ophalen quotations per route
      $quotations = [];
      $containers = [];
      $notes = [];
      $allpaid = true;
      foreach ($quotationsObjs as $quotation) {

        $query2 = "SELECT containerId, deliverDate FROM " . ContainersQuotations::getTablename() . " WHERE quotationId = '" . $quotation->quotationId . "' GROUP BY containerId";
        $oResult2 = DBConn::db_link()->query($query2);
        while ($row_c = $oResult2->fetch_object()) {
          //ophalen containers van quotation
          $query3 = "SELECT containers.* FROM " . Containers::getTablename() . " ";
          $query3 .= "WHERE containers.containerId = '" . $row_c->containerId . "' ";
          $oResult3 = DBConn::db_link()->query($query3);
          $container_row = $oResult3->fetch_object();

          if (!isset($containers[$container_row->containerId])) {
            //ophalen tagid van container
            foreach (ContainerTag::find_all_by(["containerId" => $row_c->containerId]) as $to) {
              $container_row->tagIds[] = $to->tagId;
            }

            $container_row->quotations = [];
            $containers[$container_row->containerId] = $container_row;
          }
          $containers[$container_row->containerId]->quotations[] = $quotation->quotationId;

        }


        $data->street = $quotation->street;
        $data->nr = $quotation->nr;
        $data->ext = $quotation->ext;
        $data->zipcode = $quotation->zipcode;
        $data->domestic = $quotation->domestic;
        $data->country = $quotation->country;
        if ($quotation->companyId != "") {
          $data->companyId = $quotation->companyId;
        }
        if (!isset($data->executorPersonId)) {
          $data->executorPersonId = "";
        }
        if ($quotation->executorPersonId != "") {
          $data->executorPersonId = $quotation->executorPersonId;
          $data->executor = "";
          $data->executorMobile = "";
          $data->executorMail = "";
        }
        else {
          $data->executor = $quotation->executor;
          $data->executorMobile = $quotation->executorMobile;
          $data->executorMail = $quotation->executorMail;
        }

        if (trim($quotation->dispatchAppointment) != "" && !in_array(trim($quotation->dispatchAppointment), $notes)) {
          $notes[] = trim($quotation->dispatchAppointment);
        }

        $l_quotation_short = new stdClass();
        $l_quotation_short->quotationId = $quotation->quotationId;
        $l_quotation_short->statusId = $quotation->statusId;
        $l_quotation_short->userId = $quotation->userId;
        $l_quotation_short->quotationNumber = $quotation->quotationNumber;
        $l_quotation_short->quotationPart = $quotation->quotationPart == "" ? "" : RdeHelper::asciiIntToAlpha($quotation->quotationPart);
        $l_quotation_short->quotationVersion = $quotation->quotationVersion;
        $quotations[] = $l_quotation_short;

        if ($quotation->payedFlag == 0) {
          $allpaid = false;
        }

      }

      $data->quotations = $quotations;
      $data->containers = array_values($containers);
      $data->notes = "";
      if (count($notes) > 0) {
        $data->notes = implode(", ", $notes);
      }
      $data->allpaid = $allpaid;

      //ook overige containers zetten
      $all_container_nrs = [];
      foreach ($data->containers as $c) {
        $all_container_nrs[] = $c->containerNumber;
      }

      if (!isset($data->street)) {
        //er is geen enkele NIET BEZORGDE quotation gekoppeld aan deze gebruiker....haal de laatste bezorgde op en haal daar gegevens uit
        $querylast = "SELECT Q.quotationId, Q.statusId,Q.quotationNumber,Q.quotationPart,Q.quotationVersion, street, nr, ext, zipcode, domestic, country, userId, companyId, executor, executorMobile, executorMail, executorPersonId, dispatchAppointment, CQ.containerId FROM " . Quotations::getTablename() . " Q ";
        $querylast .= "JOIN " . ContainersQuotations::getTablename() . " CQ ON CQ.quotationId=Q.quotationId ";
        $querylast .= "WHERE userId=" . $userId . " ";
        $querylast .= "AND NOT deliverDate IS NULL AND returnDate IS NULL ";
        $querylast .= "ORDER BY Q.quotationId DESC LIMIT 1 ";
        $oResult_last = DBConn::db_link()->query($querylast);
        if ($l_quotation = $oResult_last->fetch_object()) {
          //          pd($l_quotation);

          $data->street = $l_quotation->street;
          $data->nr = $l_quotation->nr;
          $data->ext = $l_quotation->ext;
          $data->zipcode = $l_quotation->zipcode;
          $data->domestic = $l_quotation->domestic;
          $data->country = $l_quotation->country;
          if ($l_quotation->companyId != "") {
            $data->companyId = $l_quotation->companyId;
          }
          if (!isset($data->executorPersonId)) {
            $data->executorPersonId = "";
          }
          if ($l_quotation->executorPersonId != "") {
            $data->executorPersonId = $l_quotation->executorPersonId;
            $data->executor = "";
            $data->executorMobile = "";
            $data->executorMail = "";
          }
          else {
            $data->executor = $l_quotation->executor;
            $data->executorMobile = $l_quotation->executorMobile;
            $data->executorMail = $l_quotation->executorMail;
          }

          if (trim($l_quotation->dispatchAppointment) != "" && !in_array(trim($l_quotation->dispatchAppointment), $notes)) {
            $notes[] = trim($l_quotation->dispatchAppointment);
          }

          $l_quotation_short = new stdClass();
          $l_quotation_short->quotationId = $l_quotation->quotationId;
          $l_quotation_short->statusId = $l_quotation->statusId;
          $l_quotation_short->userId = $l_quotation->userId;
          $l_quotation_short->quotationNumber = $l_quotation->quotationNumber;
          $l_quotation_short->quotationPart = $l_quotation->quotationPart == "" ? "" : RdeHelper::asciiIntToAlpha($l_quotation->quotationPart);

          $l_quotation_short->quotationVersion = $l_quotation->quotationVersion;
          $quotations[] = $l_quotation_short;

        }
      }


      $data->name = "";
      $data->persons = [];
      $userIds = [];
      if (isset($data->companyId)) {

        $query2 = "SELECT * FROM " . CrmCompanies::getTablename() . " WHERE companyId = " . $data->companyId;
        $oResult2 = DBConn::db_link()->query($query2);
        $company = $oResult2->fetch_object();
        $data->name = ($company->name == null ? "" : $company->name);

        //alle personen gekoppeld bij dit bedrijf

        $query3 = "SELECT * FROM " . CrmPersons::getTablename() . " WHERE flagForDeletion=0 AND companyId = " . $data->companyId;
        $oResult3 = DBConn::db_link()->query($query3);
        while ($person_o = $oResult3->fetch_object()) {
          $person = [];
          $person['id'] = ($person_o->personId + 1000000);
          $person['personid'] = $person_o->personId;
          $person['userid'] = 0;
          $person['name'] = $person_o->firstName . ' ' . $person_o->lastName;
          $person['phone'] = $person_o->phone;
          $person['mobile'] = $person_o->mobile;
          $person['email'] = $person_o->email;
          $person['type'] = 'other';
          $data->persons[] = $person;
        }

        //sandboxuser gekoppeld aan bestelling
        if ($sUser) {
          $found_main_user = false;
          foreach ($data->persons as $k => $p) {
            if ($p['personid'] != "" && $p['personid'] == $sUser->personId) {
              $data->persons[$k]['type'] = 'contact';
              $data->persons[$k]['userid'] = $sUser->userId;
              $found_main_user = true;
              break;
            }
          }
          if (!$found_main_user) {
            $person = [];
            $person['id'] = $sUser->userId;
            $person['personid'] = ($sUser->personId == "" ? 0 : $sUser->personId);
            $person['userid'] = $sUser->userId;
            $person['name'] = $sUser->firstName . ' ' . $sUser->lastName;
            $person['phone'] = $sUser->phone;
            $person['mobile'] = $sUser->mobile;
            $person['email'] = $sUser->email;
            $person['type'] = 'contact';
            $data->persons[] = $person;
            if ($sUser->userId != "")
              $userIds[$sUser->userId] = $sUser->userId;
          }
        }

        if ($data->executorPersonId != "") {
          $person_o = CrmPersons::find_by(["personId" => $data->executorPersonId]);

          if ($person_o) {
            $found_executor = false;
            foreach ($data->persons as $k => $p) {
              if ($p) {
                $data->persons[$k]['type'] = 'executor';
                $found_executor = true;
                break;
              }
            }
            if (!$found_executor) {
              $person = [];
              $person['id'] = ($person_o->personId + 1000000);
              $person['personid'] = $person_o->personId;
              $person['userid'] = 0;
              $person['name'] = $person_o->firstName . ' ' . $person_o->lastName;
              $person['phone'] = $person_o->phone;
              $person['mobile'] = $person_o->mobile;
              $person['email'] = $person_o->email;
              $person['type'] = 'executor';
              $data->persons[] = $person;
            }
          }


        }

      }
      else { //particulier

        $person = [];
        $person['id'] = $sUser->userId;
        $person['personid'] = ($sUser->personId == "" ? 0 : $sUser->personId);
        $person['userid'] = $sUser->userId;
        $person['name'] = $sUser->firstName . ' ' . $sUser->lastName;
        $person['phone'] = $sUser->phone;
        $person['mobile'] = $sUser->mobile;
        $person['email'] = $sUser->email;
        $person['type'] = 'contact';
        if ($sUser->userId != "")
          $userIds[$sUser->userId] = $sUser->userId;
        $data->persons[] = $person;
      }

      usort($data->persons, "apiRdeActions::sortPersons");

      $all_container_nrs = [];
      foreach ($data->containers as $c) {
        $all_container_nrs[] = $c->containerNumber;
      }
      $data->containers_other = apiRdeActions::getContainersNearFar(isset($data->companyId) ? $data->companyId : 0, $userIds, isset($data->zipcode) ? $data->zipcode : '', date('Y-m-d'), $all_container_nrs);

      //tagids toevoegen
      foreach ($data->containers_other as $ca) {
        foreach (ContainerTag::find_all_by(["containerId" => $ca->containerId]) as $to) {
          $ca->tagIds[] = $to->tagId;
        }
      }

      if ($return) {
        return $data;
      }

      RestUtils::sendResponseOK("PICKING COMPANIES SUCCESFULLY RETRIEVED", [$data]);

    }


    /**
     * Vewerk Ophalen App
     */
    public function executeSendPickup() {
      $request_vars = $this->data->getData();

      $go = true;

      logToFile("sendpickup", print_r($_FILES, true));
      //      logToFile("sendpickup", print_r($request_vars, true));
      $json = json_decode($request_vars["json"]);
      $json = $json[0];
      logToFile("sendpickup", "JSON ONTVANGEN: " . print_r($json, true));

      $base_path = DIR_ROOT_HTTPDOCS . "filesystem/raamdorpel/clients/signatures/";
      $success = true;
      $cargo_receipt_ids = [];
      foreach ($json as $transportticket) {

        $container_quotations = [];
        $userId = $transportticket->routeId / 100000;
        $pickupdata = $this->executeGetPickingCompanyData($userId, true);
        logToFile("sendpickup", "executeGetPickingCompanyData " . $userId . " " . $this->version . " " . print_r($pickupdata, true));

        if (count($pickupdata->quotations) == 0 && count($pickupdata->containers) == 0) {
          if (!isset($transportticket->pickupitems_scanned) || count($transportticket->pickupitems_scanned) == 0) {
            logToFile("sendpickup_error", "Geen containers en quotations. Overgeslagen.");
            continue;
          }
          else {
            //loop eens over deze items. allemaal pickup...kan niet, een pickup moet quotations hebben.
            $onlypickup = true;
            foreach ($transportticket->pickupitems_scanned as $scanned) {
              if ($scanned->action != "pickup") {
                $onlypickup = false;
                break;
              }
            }
            if ($onlypickup) {
              logToFile("sendpickup_error", "Alleen maar pickup items maar GEEN quotations. Overslaan. " . print_r($pickupdata, true));
              continue;
            }
          }

        }

        $quotationIds = [];
        $containernrs_picked_up = [];
        $containernrs_retour = [];
        $containers_retour_ids = [];
        $quot_cont = [];

        foreach ($transportticket->pickupitems_scanned as $scanned) {

          if (isset($scanned->containerId) && $scanned->containerId != "") { // een container scan. manual heeft een dummyid

            $container = Containers::find_by(["containerNumber" => $scanned->containerNumber]);

            if ($container) { //container gevonden (het kan zijn dat je een ongeldig nummer hebt ingevoerd)
              $all_cqs_done = true;
              foreach (ContainersQuotations::find_all_by(["containerId" => $container->containerId, "returnDate" => null]) as $container_quotation) {
                //alleen containerquotions pakken die bij deze gebruiker horen.
                $cq_found = false;
                foreach ($pickupdata->quotations as $quot) {
                  if ($quot->quotationId == $container_quotation->quotationId) {
                    $cq_found = true;
                    break;
                  }
                }
                if ($cq_found) { //deze containerquotion hoort bij deze klant
                  if ($scanned->action == 'pickup') {
                    if ($container_quotation->deliverDate == "") {
                      $container_quotation->deliverDate = date("Y-m-d", strtotime($transportticket->signedDate));
                      $container_quotation->deliverUserId = gsdApiSession::getUserId();
                      $container_quotation->save();
                    }

                    $quotationIds[$container_quotation->quotationId] = $container_quotation->quotationId;
                    $containernrs_picked_up[$container->containerNumber] = $container->containerNumber;
                  }

                  if (!isset($quot_cont[$container_quotation->quotationId])) {
                    $quot_cont[$container_quotation->quotationId] = [];
                  }
                  $quot_cont[$container_quotation->quotationId][] = $container->containerNumber;
                  $container_quotations[$container_quotation->containerId][$container_quotation->quotationId] = $container_quotation->quotationId;
                }
                elseif ($container_quotation->deliverDate == "") {
                  // Deze container_quotation hoort NIET bij deze klant, en zijn leverdatum is leeg.
                  // Dan is de container dus niet meegenomen, en nog steeds in voorraad.
                  $all_cqs_done = false;
                }
              }

              if ($scanned->action == 'pickup' && $all_cqs_done && $container->inStock != 'N' && $container->containerNumber != "0000") {
                $container->inStock = 'N';
                $container->save();
              }

              if ($scanned->action == 'retour') {

                //de container_quotations van deze klant in deze container op retour zetten
                $all_cqs_done = true;
                foreach (ContainersQuotations::find_all_by(["containerId" => $container->containerId, "returnDate" => null], "AND NOT deliverDate IS NULL AND deliverDate<='" . date("Y-m-d", strtotime($transportticket->signedDate)) . "' ") as $cq) {
                  $cq_found = false;
                  foreach ($pickupdata->quotations as $quot) {
                    if ($quot->quotationId == $cq->quotationId) {
                      $cq_found = true;
                      break;
                    }
                  }
                  if ($cq_found) { //deze container_quotation hoort bij deze klant, returndate zetten
                    $cq->returnUserId = gsdApiSession::getUserId();
                    $cq->returnDate = date("Y-m-d", strtotime($transportticket->signedDate));
                    $cq->save();
                  }
                  else { //deze container_quotation hoort niet bij deze klant en is niet retour. Bak is niet leeg.
                    $all_cqs_done = false;
                  }
                }

                if ($all_cqs_done && $container->inStock != 'Y') {
                  //zijn er geen container_quotations gekoppeld aan deze container die niet retour zijn, dan ook container retour zetten
                  $container->inStock = 'Y';
                  $container->save();
                }

                //altijd mailen naar klant.
                $containernrs_retour[$container->containerNumber] = $container->containerNumber; //nrs op vrachtbon
                $containers_retour_ids[$container->containerId] = $container->containerId; //gebruikt voor email

              }

            }

          }
          elseif ($scanned->quotationId != "" && isset($pickupdata->quotations) && count($pickupdata->quotations) > 0) {
            // een quotation meegenomen zonder container
            foreach ($pickupdata->quotations as $quot) {
              if ($quot->quotationId == $scanned->quotationId) {
                $quotationIds[$scanned->quotationId] = $scanned->quotationId;
                break;
              }

            }
          }
        }

        //--------------------------VRACHTBON AANMAKEN--------------------------

        $offertetxt = "";
        $quotationIds_4050 = [];
        if (count($quotationIds) > 0) {

          $quotations = Quotations::find_all_by(["quotationId" => $quotationIds]);

          foreach ($quotations as $l_quotation) {

            //echo $l_quotation->quotationId." ".$l_quotation->quotationNumber." | ";

            if ($l_quotation->statusId == Status::STATUS_PRODUCED || $l_quotation->statusId == Status::STATUS_PACKED) {
              $quotationIds_4050[$l_quotation->quotationId] = $l_quotation->quotationId;
            }

            $name = $l_quotation->getQuotationNumberFull();

            if (isset($quot_cont[$l_quotation->quotationId])) {
              sort($quot_cont[$l_quotation->quotationId]);
              if ($l_quotation->statusId >= Status::STATUS_DELIVERED) {
              }
              else {
                $offertetxt .= $name . ' - ' . implode(", ", $quot_cont[$l_quotation->quotationId]) . "\n";
              }
            }
            else {
              $offertetxt .= $name . "\n";
            }
          }
        }

        $userIds = [];
        foreach ($pickupdata->persons as $u) {
          if ($u['userid'] != 0) {
            $userIds[$u['userid']] = $u['userid'];
          }
        }
        logToFile("sendpickup", print_r($userIds, true));


        $containers_nearfar = apiRdeActions::getContainersNearFar(isset($pickupdata->companyId) ? $pickupdata->companyId : 0, $userIds, $pickupdata->zipcode, date("Y-m-d"), $containernrs_picked_up);

        $containers_far = '';
        $containers_near_ar = [];
        if (count($containernrs_picked_up) > 0 || count($containers_nearfar) > 0) {
          foreach ($containernrs_picked_up as $contnr) {
            $containers_near_ar[] = $contnr;
          }
          foreach ($containers_nearfar as $cont) {
            if ($cont->distance == 'near') {
              $containers_near_ar[] = $cont->containerNumber;
            }
            else { //far
              $containers_far .= $cont->containerNumber . "\n";
            }
          }
          trim($containers_far);
        }

        $containers_near = '';
        if (count($containers_near_ar) > 0) {
          $containers_near_ar = array_merge($containers_near_ar, $containernrs_retour);//welke retour zijn genomen ook bij near zetten.
          $containers_near_ar = array_unique($containers_near_ar);
          sort($containers_near_ar);
          $containers_near .= implode("\n", $containers_near_ar);
        }

        $containers_retour = ''; //word gezet vanuit app
        if (count($containernrs_retour) > 0) {
          $retour_op_offerte_ar = array_unique($containernrs_retour);
          sort($retour_op_offerte_ar);
          $containers_retour .= implode("\n", $retour_op_offerte_ar);
        }

        //-----------------EINDE alle bakken opzoeken------------------
        //        if(!$go) {
        //          //pd($route);
        //          if($offertetxt != "")
        //            pd("Offerte: " . $offertetxt);
        //          if($containers_retour != "")
        //            pd("Retour: " . $containers_retour);
        //          if($containers_near != "")
        //            pd("Dichtbij: " . $containers_near);
        //          if($containers_far != "")
        //            pd("Verweg: " . $containers_far);
        //          pd('-');
        //        }

        $filename = "";
        if (isset($_FILES['signatures'])) {
          $filename = $userId . '_' . date("Ymdhis") . ".png";
          move_uploaded_file($_FILES['signatures']['tmp_name'][$userId], $base_path . $filename);
        }

        $person = SandboxUsers::find_by(["userId" => $pickupdata->userId]);

        $senderInstructions = 'Let op: U dient de raamdorpelelementen rechtop te dragen, zoals ze ook verpakt zijn in rek of bak. Langere lengtes dienen door 2 personen gedragen en gelegd te worden.';
        $fields = [
          'companyId'               => (!isset($pickupdata->companyId) || $pickupdata->companyId == "" || $pickupdata->companyId == -1) ? null : $pickupdata->companyId,
          'cargoReceiptType'        => 'factorypickup',
          'shippingAgentName'       => 'Raamdorpelelementen BV',
          'shippingAgentStreet'     => 'Raambrug',
          'shippingAgentNumber'     => '9',
          'shippingAgentExt'        => '',
          'shippingAgentZip'        => '5531AG',
          'shippingAgentCity'       => 'Bladel',
          'shippingAgentPhone'      => '0497360791',
          'destinationName'         => $pickupdata->name,
          'destinationStreet'       => $pickupdata->street,
          'destinationNumber'       => $pickupdata->nr,
          'destinationExt'          => $pickupdata->ext,
          'destinationZip'          => $pickupdata->zipcode,
          'destinationCity'         => $pickupdata->domestic,
          'city3'                   => $pickupdata->domestic,
          'country3'                => 'Nederland',
          'city4'                   => $pickupdata->domestic,
          'country4'                => 'Nederland',
          'dateOfToday'             => date('Y-m-d'),
          'documentsAttached'       => 'Specificatielijst raamdorpelelementen',
          'marksAndNums'            => trim($offertetxt), //de te bezorgen offertes / de op te halen offertes
          //              'numberOfPackages'           => '',
          //              'methodOfPacking'            => '',
          //              'natureOfTheGoods'           => '',
          'staticNumberNear'        => $containers_near, //bakken in de buurt van klant
          'staticNumberFar'         => $containers_far, //bakken ver weg van klant
          'staticNumberRetour'      => $containers_retour, //bakken retour
          //              'gross'                      => '',
          //              'volume'                     => '',
          'destinationContact'      => $person ? $person->firstName . ' ' . $person->lastName : '',
          'destinationPhone'        => $person ? $person->phone : '',
          'destinationMobile'       => $person ? $person->mobile : '',
          'consigneeExecutor'       => $pickupdata->executor,
          'consigneeExecutorMobile' => $pickupdata->executorMobile,
          'consigneeExecutorMail'   => $pickupdata->executorMail,
          'senderInstructions'      => $senderInstructions,
          //              'carriagePaid'               => '',
          //              'carriageForward'            => '',
          //              'cashOnDelivery'             => '',
          //              'carrierName'                => '',
          //              'carrierStreet'              => '',
          //              'carrierStreetNumber'        => null,
          //              'carrierExt'                 => '',
          //              'carrierZip'                 => '',
          //              'carrierCity'                => '',
          //              'carrierNumberplate'         => '',
          //              'successiveCarriers'         => '',
          //          'carrierDispatchAppointment' => $quotation->dispatchAppointment,
          //              'specialAgreements'          => '',
          //              'toBePaidBy'                 => '',
          'establishedCity'         => 'Bladel',
          'establishedDate'         => date('Y-m-d H:i:s'),
          //              'signatureSenderName'        => '',
          //                        'signatureSenderFilename'    => 'ludo.png',
          //              'signatureSenderPlace'       => '',
          //              'signatureSenderDate'        => '',
          //              'signatureCarrierName'       => '',
          //              'signatureCarrierFilename'   => '',
          //              'signatureCarrierPlace'      => '',
          //              'signatureCarrierDate'       => '',
          //              'signatureCarrierMail'       => '',
          //              'cmr'                        => '',
          //              'avc'                        => '',
          //              'carrierCode'                => '',
        ];

        $signer = null;
        if ($transportticket->driverSignee == 1) { //transporteur tekent

          $tcompanies = $this->executeGetCompanies(true);
          $tcompany = false;
          if (isset($tcompanies[$transportticket->signeeCompanyId])) {
            $tcompany = $tcompanies[$transportticket->signeeCompanyId];

            if ($transportticket->signeeCarriercode != '' && $transportticket->signeeCarriercode != $tcompany->carrierCode) {
              //even carriercode opslaan bij bedrijf
              $query = "UPDATE " . CrmCompanies::getTablename() . " ";
              $query .= "SET carrierCode='" . $transportticket->signeeCarriercode . "' ";
              $query .= "WHERE companyId=" . $tcompany->companyId;
              //logToFile("mysql", $query);
              if ($go) {
                DBConn::db_link()->query($query);
              }
            }

            if ($transportticket->receiverPersonId == -1) { //nieuw contactpersoon
              $names = explode(" ", $transportticket->receiverName);
              $firstName = trim($names[0]);
              unset($names[0]);
              $lastName = '';
              if (count($names) > 0) {
                $lastName = implode(" ", $names);
              }

              $crm_person = new CrmPersons();
              $crm_person->companyId = $transportticket->signeeCompanyId;
              $crm_person->firstName = $firstName;
              $crm_person->lastName = $lastName;
              $crm_person->mobile = $transportticket->receiverPhone;
              $crm_person->email = $transportticket->receiverEmail;
              $crm_person->flagForExecutor = 0;
              if ($go) $crm_person->save();

            }
            else {
              $cpersons = [];
              foreach ($tcompany->persons as $cp) {
                $cpersons[$cp["personid"]] = $cp;
              }

              if (isset($cpersons[$transportticket->receiverPersonId])) {

                $crm_person = CrmPersons::find_by(["personId" => $transportticket->receiverPersonId]);
                if ($crm_person) {
                  $crm_person->email = $transportticket->receiverEmail;
                  if ($transportticket->receiverPhone != "") {
                    $crm_person->mobile = $transportticket->receiverPhone;
                  }
                  if ($go) $crm_person->save();
                }

              }
            }


          }

          $signer = [
            'signatureCarrierName'       => $transportticket->receiverName ?? '',
            'signatureCarrierFilename'   => $filename,
            'signatureCarrierPlace'      => 'Bladel', //getekend op factory
            'signatureCarrierDate'       => date('Y-m-d H:i:s', strtotime($transportticket->signedDate)),
            'signatureCarrierMail'       => $transportticket->receiverEmail ?? '',
            'carrierId'                  => $tcompany ? $tcompany->companyId : 0,
            'carrierName'                => $tcompany ? $tcompany->name : null,
            'carrierStreet'              => $tcompany ? $tcompany->visitaddress->street : null,
            'carrierstreetNumber'        => $tcompany ? $tcompany->visitaddress->nr : null,
            'carrierExt'                 => $tcompany ? $tcompany->visitaddress->extension : null,
            'carrierZip'                 => $tcompany ? $tcompany->visitaddress->zipcode : null,
            'carrierCity'                => $tcompany ? $tcompany->visitaddress->domestic : null,
            'carrierCode'                => $transportticket->signeeCarriercode,
            'cmr'                        => isset($transportticket->signeeCmrAvc) && $transportticket->signeeCmrAvc == "cmr" ? 1 : 0,
            'avc'                        => isset($transportticket->signeeCmrAvc) && $transportticket->signeeCmrAvc == "avc" ? 1 : 0,
            'carrierNumberplate'         => $transportticket->signeeNumberplate ?? '',
            'signatureConsigneeDateTime' => date('Y-m-d H:i:s', strtotime($transportticket->signedDate)),
            'signatureConsigneePlace'    => 'Bladel', //getekend op factory
          ];
        }
        else {
          $signer = [
            'signatureConsigneeName'     => $transportticket->receiverName ?? '',
            'signatureConsigneeFilename' => $filename,
            'signatureConsigneePlace'    => 'Bladel', //getekend op factory
            'signatureConsigneeDateTime' => date('Y-m-d H:i:s', strtotime($transportticket->signedDate)),
            'signatureConsigneeMail'     => $transportticket->receiverEmail ?? '',
            'carrierNumberplate'         => $transportticket->receiverNumberplate ?? '',
          ];
        }

        $fields = array_merge($fields, $signer);

        //          pd($fields);

        $cr = new CargoReceipt();
        foreach ($fields as $key => $val) {
          $cr->{$key} = $val;
        }
        $cr->save();

        $cargo_receipt_ids[$cr->cargoReceiptId] = $cr->cargoReceiptId;

        if (count($quotationIds) > 0) {

          //update stati 53 naar 55
          if (count($quotationIds_4050) > 0) {
            $query = "UPDATE " . Quotations::getTablename() . " ";
            $query .= "SET statusId=55 ";
            $query .= "WHERE statusId IN(40,50) AND quotationId IN (" . implode(",", $quotationIds_4050) . ") ";
            //logToFile("mysql", $query);
            if ($go) {
              DBConn::db_link()->query($query);
              QuotationStatus::registerBatch($quotationIds_4050, Status::STATUS_DELIVERED, gsdApiSession::getUserId());
            }
          }

          if ($transportticket->driverRemarks != "null" && trim($transportticket->driverRemarks) != "") {
            //opslaan opmerkingen chauffeur
            $query = "UPDATE " . QuotationsExtra::getTablename() . " QE ";
            $query .= "SET QE.quoteInvoiceAlertFlag=1, QE.quoteInvoiceAlertInfo='" . escapeForDB($transportticket->driverRemarks) . "' ";
            $query .= "WHERE quotationId IN (" . implode(",", $quotationIds) . ") ";
            //logToFile("mysql", $query);
            if ($go)
              DBConn::db_link()->query($query);
          }
          if ($transportticket->custRemarks != "null" && trim($transportticket->custRemarks) != "") {
            //opslaan opmerkingen klant bij alle facturen met tekst in dispatchAppointment
            $query = "UPDATE " . Quotations::getTablename() . " Q ";
            $query .= "SET Q.dispatchAppointment='" . escapeForDB($transportticket->custRemarks) . "' ";
            $query .= "WHERE Q.quotationId IN (" . implode(",", array_merge($quotationIds)) . ") AND Q.dispatchAppointment!='' ";
            //logToFile("mysql", $query);
            if ($go)
              DBConn::db_link()->query($query);
          }

          CargoReceiptQuotation::updateForCargoReceipt($cr->cargoReceiptId, $quotationIds);

        }

        $tos = [];
        if (isset($transportticket->receiverEmail) && ValidationHelper::isEmail($transportticket->receiverEmail)) {
          $tos[$transportticket->receiverEmail] = $transportticket->receiverEmail;
        }


        //mail gaat naar contactpersonen en persoon welke heeft getekend
        $signeefound = false;
        foreach ($pickupdata->persons as $person) {
          if ($person["type"] == "contact" && ValidationHelper::isEmail($person["email"])) {
            $tos[$person["email"]] = $person["email"];
          }
          if ($transportticket->driverSignee != 1) {
            if ($transportticket->receiverPersonId == $person["personid"] && $person["personid"] != 0) { //crm_person heeft getekend
              $crm_person = CrmPersons::find_by(["personId" => $person["personid"]]);
              if ($crm_person) {
                $crm_person->email = $transportticket->receiverEmail;
                $crm_person->mobile = $transportticket->receiverPhone;
                if ($go) $crm_person->save();
              }
              $signeefound = true;
            }
            if ($transportticket->receiverUserId == $person["userid"] && $person["userid"] != 0) { //sandboxuser heeft getekend
              $crm_person = SandboxUsers::find_by(["userId" => $person["userid"]]);
              if ($crm_person) {
                $crm_person->email = $transportticket->receiverEmail;
                $crm_person->mobile = $transportticket->receiverPhone;
                if ($go) $crm_person->save();
              }
              $signeefound = true;
            }
          }
        }

        if (!$signeefound && $transportticket->driverSignee != 1 && $pickupdata->companyId != 0 && $transportticket->receiverPersonId == -2) {
          //Nieuwe uitvoerder ingevoerd. Voeg deze toe aan crm_persons met flagForExecutor=1
          $names = explode(" ", $transportticket->receiverName);
          $firstName = trim($names[0]);
          unset($names[0]);
          $lastName = '';
          if (count($names) > 0) {
            $lastName = implode(" ", $names);
          }

          $crm_person = new CrmPersons();
          $crm_person->companyId = $pickupdata->companyId;
          $crm_person->firstName = $firstName;
          $crm_person->lastName = $lastName;
          $crm_person->mobile = $transportticket->receiverPhone;
          $crm_person->email = $transportticket->receiverEmail;
          $crm_person->flagForExecutor = 1;
          if ($go) $crm_person->save();

        }

        MailsFactory::sendContainerretourEmail($containers_retour_ids);
        MailsFactory::sendPickupEmail($tos, $cr->cargoReceiptId, $quotationIds, $containers_retour_ids);

        //alle verzonden afbeeldingen opslaan
        $base_path_cont = DIR_ROOT_HTTPDOCS . "filesystem/raamdorpel/clients/container/";
        $base_path_quot = DIR_ROOT_HTTPDOCS . "filesystem/raamdorpel/clients/quotation/photo/";

        if (isset($_FILES['pickupImage'])) {
          foreach ($_FILES['pickupImage']['name'] as $id => $items) {

            $remark = '';
            if (isset($request_vars["pickupImageRemark"][$id])) {
              $remark = $request_vars["pickupImageRemark"][$id];
            }

            if (substr($id, 0, 1) == 'C') {

              $containerId = substr($id, 2);
              foreach ($items as $nr => $filename) {

                $tmp_filename = $_FILES['pickupImage']['tmp_name'][$id][$nr];
                if (!file_exists($base_path_cont . $containerId . '/')) { //map aanmaken
                  mkdir($base_path_cont . $containerId . '/');
                }
                $containerFilePath = $base_path_cont . $containerId . '/' . $filename;
                if (!file_exists($containerFilePath)) {
                  move_uploaded_file($tmp_filename, $containerFilePath);

                  ImageHelper::compress($containerFilePath); //verklein afbeelding

                  if (isset($container_quotations[$containerId])) {
                    foreach ($container_quotations[$containerId] as $quotationId) {
                      $cim = new ContainerImg();
                      $cim->containerId = $containerId;
                      $cim->quotationId = $quotationId;
                      $cim->filename = $filename;
                      $cim->statusPhotoId = 2;
                      $cim->remark = $remark;
                      if ($go) $cim->save();
                    }
                  }
                  else { //geen quotation, dan maar zonder....
                    $cim = new ContainerImg();
                    $cim->containerId = $containerId;
                    $cim->filename = $filename;
                    $cim->statusPhotoId = 2;
                    $cim->remark = $remark;
                    if ($go) $cim->save();
                  }

                  logToFile("sendpickup", "Container image succesvol gekoppeld");
                }
              }
            }
            else {

              $quotationId = substr($id, 2);
              foreach ($items as $nr => $filename) {

                $tmp_filename = $_FILES['pickupImage']['tmp_name'][$id][$nr];
                if (!file_exists($base_path_quot . $quotationId . '/')) { //map aanmaken
                  mkdir($base_path_quot . $quotationId . '/');
                }
                if (!file_exists($base_path_quot . $quotationId . '/' . $filename)) {
                  move_uploaded_file($tmp_filename, $base_path_quot . $quotationId . '/' . $filename);

                  $cim = new ContainerImg();
                  $cim->quotationId = $quotationId;
                  $cim->filename = $filename;
                  $cim->statusPhotoId = 2;
                  $cim->remark = $remark;
                  if ($go) $cim->save();

                  logToFile("sendpickup", "Quotation image succesvol gekoppeld");
                }
              }
            }
          }
        }


      }

      if ($success) {
        RestUtils::sendResponseOK("Ophalen succesvol verzonden", array_values($cargo_receipt_ids));
      }
      else {
        RestUtils::sendResponseError("Er is een fout opgetreden tijdens het verzenden van de routes", []);
      }
    }

    public function executeGetCargoReceiptPdf() {
      $request_vars = $this->data->getData();
      $cargo_receipt_id = $request_vars['cargo_receipt_id'];

      if (!CargoReceipt::find_by(["cargoReceiptId" => $cargo_receipt_id])) {
        RestUtils::sendResponseError("Niet gevonden");
      }

      $filepath = CargoReceipt::getPdf($cargo_receipt_id);
      $filename_new = $cargo_receipt_id . '.pdf';

      header('Content-Type: application/pdf');
      header('Content-Disposition: inline; filename="' . $filename_new . '"');
      header('Cache-Control: private, max-age=0, must-revalidate');
      header('Pragma: public');
      echo file_get_contents($filepath);
      ResponseHelper::exit();
    }

  }