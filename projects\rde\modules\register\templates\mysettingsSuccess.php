<section>

  <div class="contenttxt">
    <h1>Mijn instellingen</h1>
    Op deze pagina kun u uw notificatie instellingen aanpassen.
    <br/><br/>
  </div>

  <form method="post" id="basketform">

    <?php writeErrors($errors, true) ?>

    <div class="form-row">
      <label for="noti_order_mail" class="col-4 col-form-label">Offerte e-mail</label>
      <div class="col-4-3 label-line-height">
        <label>
          <input type="checkbox" class="form-checkbox" name="noti_order_mail" id="noti_order_mail" value="1" <?php writeIfCheckedVal($user->noti_order_mail, 1) ?>/>
          Ik ontvang een offerte e-mail met de gegevens en PDF offerte na het opslaan van mijn offerte.
        </label>
      </div>
    </div>

    <div class="form-row">
      <label for="noti_confirm_mail" class="col-4 col-form-label">Bestelling e-mail</label>
      <div class="col-4-3 label-line-height">
        <label>
          <input type="checkbox" class="form-checkbox" name="noti_confirm_mail" id="noti_confirm_mail" value="1" <?php writeIfCheckedVal($user->noti_confirm_mail, 1) ?>/>
          Ik ontvang een bestelbevestiging per e-mail met de gegevens en PDF bestelling na het plaatsen van mijn bestelling.
        </label>
      </div>
    </div>

    <div class="form-row">
      <label for="noti_delivery_mail" class="col-4 col-form-label">Geproduceerd e-mail</label>
      <div class="col-4-3 label-line-height">
        <label>
          <input type="checkbox" class="form-checkbox" name="noti_produced_mail" id="noti_produced_mail" value="1" <?php writeIfCheckedVal($user->noti_produced_mail, 1) ?>/>
          Ik ontvang een email wanneer mijn bestelling is geproduceerd, zodat ik weet dat deze binnenkort word geleverd.
        </label>
      </div>
    </div>

    <div class="form-row">
      <label for="noti_delivery_mail" class="col-4 col-form-label">Levering e-mail</label>
      <div class="col-4-3 label-line-height">
        <label>
          <input type="checkbox" class="form-checkbox" name="noti_delivery_mail" id="noti_delivery_mail" value="1" <?php writeIfCheckedVal($user->noti_delivery_mail, 1) ?>/>
          Ik ontvang voor levering van mijn bestelling een e-mail met daarin de leverdatum.
        </label>
      </div>
    </div>

    <div class="form-row">
      <label for="sms" class="col-4 col-form-label">Levering SMS/Whatsapp</label>
      <div class="col-4-3 label-line-height">
        <label>
          <input type="checkbox" class="form-checkbox" name="sms" id="noti_delivery_mail" value="1" <?php writeIfCheckedVal($user->sms, 1) ?>/>
          Wanneer de chauffeur onderweg is naar het afleveradres van de bestelling, ontvang ik een SMS/Whatsapp op mijn GSM.<br/>
          Het mobiele nummer kunt u aanpassen bij <a href="/mijn-gegevens">Mijn gegevens</a>
        </label>
      </div>
    </div>

    <div class="form-row">
      <label for="sms_delivered" class="col-4 col-form-label">Geleverd SMS/Whatsapp</label>
      <div class="col-4-3 label-line-height">
        <label>
          <input type="checkbox" class="form-checkbox" name="sms_delivered" id="noti_delivery_mail" value="1" <?php writeIfCheckedVal($user->sms_delivered, 1) ?>/>
          Wanneer de bestelling geleverd is op het afleveradres van de bestelling, ontvang ik een SMS/Whatsapp op mijn GSM.<br/>
          Het mobiele nummer kunt u aanpassen bij <a href="/mijn-gegevens">Mijn gegevens</a>
        </label>
      </div>
    </div>

    <div class="form-row">
      <div class="col-4 responsive-hide">&nbsp;</div>
      <div class="col-4-3">
        <input type="submit" value="<?php echo __("Opslaan") ?>" class="btn btn-primary" name="go" id="registeren"/>
      </div>
    </div>

  </form>

</section>
