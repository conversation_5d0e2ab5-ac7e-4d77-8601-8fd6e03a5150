<?php
class BaseMitres extends AppModel
{
  const DB_NAME = 'rde_b1mo';
  const TABLE_NAME = 'mitres';
  const OM_CLASS_NAME = 'Mitres';
  const columns = ['mitreId', 'stoneLength', 'angle', 'shortLength', 'heartLength', 'longLength', 'stoneCount', 'display'];
  const field_structure = [
    'mitreId'                     => ['type' => 'int', 'length' => '5', 'null' => false],
    'stoneLength'                 => ['type' => 'decimal', 'length' => '4,1', 'null' => false],
    'angle'                       => ['type' => 'decimal', 'length' => '4,1', 'null' => false],
    'shortLength'                 => ['type' => 'decimal', 'length' => '4,1', 'null' => false],
    'heartLength'                 => ['type' => 'decimal', 'length' => '4,1', 'null' => false],
    'longLength'                  => ['type' => 'decimal', 'length' => '4,1', 'null' => false],
    'stoneCount'                  => ['type' => 'tinyint', 'length' => '2', 'null' => false],
    'display'                     => ['type' => 'enum', 'length' => '2', 'null' => false, 'enums' => ['true','false']],
  ];

  protected static $primary_key = ['mitreId'];
  protected $auto_increment = 'mitreId';

  public $mitreId, $stoneLength, $angle, $shortLength, $heartLength, $longLength, $stoneCount, $display;

  function __construct($obj_arr = [], &$error_codes = []) {
    parent::__construct();
    $this->setDefaults();
    if (!empty($obj_arr)) {
      $this->from_array($obj_arr);
      $this->valid_required_fields($error_codes);
    }
  }

  public function valid(&$error_codes = []) {
    $errors = [];
    foreach(self::columns as $column) {
      if ($column != $this->auto_increment)
      {
        call_user_func_array([$this, 'v_' . $column], [&$errors]);
      }
    }
    if(empty($errors)) {
      return true;
    }
    $error_codes = array_merge($error_codes, $errors);
    return false;
  }

  public function valid_required_fields(&$error_codes = []) {
    $errors = [];
    foreach (self::columns as $column) {
      $function_name = "v_$column";
      if ($column!=$this->auto_increment) {
        call_user_func_array([$this, $function_name], [&$errors]);
      }
    }
    if (empty($errors)) {
      return true;
    }
    $error_codes = array_merge($error_codes, $errors);
    return false;
  }

  public function setDefaults() {
    $this->display = 'false';
  }



  public function v_mitreId(&$error_codes = []) {
    if (!is_null($this->mitreId) && strlen($this->mitreId) > 0 && self::valid_int($this->mitreId, '5')) {
      return true;
    }
    $error_codes[] = 'mitreId';
    return false;
  }

  public function v_stoneLength(&$error_codes = []) {
    if (!is_null($this->stoneLength) && strlen($this->stoneLength) > 0 && self::valid_decimal($this->stoneLength, '4,1')) {
      return true;
    }
    $error_codes[] = 'stoneLength';
    return false;
  }

  public function v_angle(&$error_codes = []) {
    if (!is_null($this->angle) && strlen($this->angle) > 0 && self::valid_decimal($this->angle, '4,1')) {
      return true;
    }
    $error_codes[] = 'angle';
    return false;
  }

  public function v_shortLength(&$error_codes = []) {
    if (!is_null($this->shortLength) && strlen($this->shortLength) > 0 && self::valid_decimal($this->shortLength, '4,1')) {
      return true;
    }
    $error_codes[] = 'shortLength';
    return false;
  }

  public function v_heartLength(&$error_codes = []) {
    if (!is_null($this->heartLength) && strlen($this->heartLength) > 0 && self::valid_decimal($this->heartLength, '4,1')) {
      return true;
    }
    $error_codes[] = 'heartLength';
    return false;
  }

  public function v_longLength(&$error_codes = []) {
    if (!is_null($this->longLength) && strlen($this->longLength) > 0 && self::valid_decimal($this->longLength, '4,1')) {
      return true;
    }
    $error_codes[] = 'longLength';
    return false;
  }

  public function v_stoneCount(&$error_codes = []) {
    if (!is_null($this->stoneCount) && strlen($this->stoneCount) > 0 && self::valid_tinyint($this->stoneCount, '2')) {
      return true;
    }
    $error_codes[] = 'stoneCount';
    return false;
  }

  public function v_display(&$error_codes = []) {
    if ($this->display == 'true') { return true; }
    if ($this->display == 'false') { return true; }
    $error_codes[] = 'display';
    return false;
  }

  /**
  *@param array $conditions (required)
  *@param String $raw_sql (optional)
  *@return Mitres[]
  **/
  public static function find_all_like($conditions, $raw_sql = '') { return parent::generic_find_all_like($conditions, static::OM_CLASS_NAME, $raw_sql); }
  /**
  *@param array $conditions (required)
  *@param String $raw_sql (optional)
  *@return Mitres[]
  **/
  public static function find_all_by($conditions, $raw_sql = '') { return parent::generic_find_all_by($conditions, static::OM_CLASS_NAME, $raw_sql); }
  /**
  *@param String $raw_sql (optional)
  *@return Mitres[]
  **/
  public static function find_all($raw_sql = '') { return parent::generic_find_all(static::OM_CLASS_NAME, $raw_sql); }
  /**
  *@param array $conditions (required)
  *@param String $raw_sql (optional)
  *@return Mitres
  **/
  public static function find_by($conditions, $raw_sql = '') { return parent::generic_find_by($conditions, static::OM_CLASS_NAME, $raw_sql); }
  /**
  *@param String $id (required)
  *@param String $raw_sql (optional)
  *@return Mitres
  **/
  public static function find_by_id($id, $raw_sql = '') { return parent::generic_find_by_id($id, static::OM_CLASS_NAME, $raw_sql); }
  /**
  *@param array $conditions (required)
  *@param String $raw_sql (optional)
  *@return int
  **/
  public static function count_all_by($conditions, $raw_sql = '') { return parent::generic_count_all_by($conditions, static::OM_CLASS_NAME, $raw_sql); }
  /**
  *@param array $conditions (required)
  *@param String $raw_sql (optional)
  *@return bool
  **/
  public static function delete_by($conditions, $raw_sql = '') { return parent::generic_delete_by($conditions, static::OM_CLASS_NAME, $raw_sql); }
}