<?php

  namespace domain\updater;

  use CustomerRatingEmails;
  use GpsbuddyRoutes;
  use Gsd\Updater\GsdUpdater;
  use Invoices;
  use <PERSON>llie;
  use OrderElementWindowsill;
  use Quotations;
  use Stones;
  use BretiInvoice;
  use BretiQuotation;
  use CrmInvoiceparties;
  use CrmCompanies;

  /**
   * Class RdeUpdater
   * Updaten van specifieke changes.
   * @package domain\updater
   */
  class RdeUpdater extends GsdUpdater {
    /**
     * 05-06-2025 - Justin - <PERSON>llie Payment ID meer dan 20 tekens
     */
    protected function execute19() {
      $mollie = Mollie::getTablename();
      $this->executeQuery("ALTER TABLE $mollie MODIFY `paymentId` VARCHAR(50) NOT NULL;");
      return true;
    }

    /**
     * 13-05-2025 - <PERSON> - <PERSON><PERSON><PERSON> opties voor endstone ('flat', 'standingside', 'stuc') in offerte tabel
     */
    protected function execute18() {
      $table = Quotations::getTablename();
      $this->executeQuery(<<<SQL
            ALTER TABLE $table
            MODIFY COLUMN endstone 
            ENUM('false', 'true','true_grooves', 'left', 'right', 'leftg', 'rightg', 'flat', 'standingside', 'stuc') 
            NOT NULL;
      SQL);
      return true;
    }

    /*
     * 11-04-2025 - Justin - optionele ral kleur kolom toegevoegd aan de quotation tabel
     */
    protected function execute17() {
      $this->executeQuery("ALTER TABLE " . Quotations::getTablename() . " ADD `ralColor` CHAR(4) NULL;");
      return true;
    }

    /**
     * 10-04-2025 - Justin - tabel om recente bellers bij te houden, is de bedoeling dat deze periodiek opgeschoond wordt.
     */
    protected function execute16() {
      $this->executeQuery(<<<SQL
        CREATE TABLE rde_b1mo.recent_callers (
          id INT NOT NULL AUTO_INCREMENT,
          callerId VARCHAR(20) NOT NULL,
          callerAt DATETIME NOT NULL,
          PRIMARY KEY (id)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8;
      SQL);
      return true;
    }

    /**
     * 08-04-2025 - Robert - container_quotations welke verwijzen naar een niet bestaande quotation verwijderen. Dit waren alleen wat oude.
     */
    protected function execute15() {
      foreach(\ContainersQuotations::find_all_by([], "WHERE containerQuotationId<700 ORDER BY containerQuotationId ASC ") as $containerQuotation) {
        $quot = Quotations::find_by(["quotationId"=>$containerQuotation->quotationId]);
        if(!$quot) {
          $containerQuotation->destroy();
        }
      }
      return true;
    }

    /**
     * 08-04-2025 - Robert - fix migration to innodb
     */
    protected function execute14() {
      $this->executeQuery("ALTER TABLE " . \GluePrices::getTablename() . " ROW_FORMAT=DYNAMIC;");
      $this->executeQuery("ALTER TABLE " . \CustomerPaytermincreases::getTablename() . " ROW_FORMAT=DYNAMIC;");
      $this->executeQuery("ALTER TABLE " . \CustomerProductincreases::getTablename() . " ROW_FORMAT=DYNAMIC;");
      return true;
    }

    /**
     * 06-03-2025 - Justin - Nieuwe opties voor endstone ('flat', 'standingside', 'stuc')
     */
    protected function execute13() {
      $table = Stones::getTablename();
      $this->executeQuery(<<<SQL
            ALTER TABLE $table
            MODIFY COLUMN endstone 
            ENUM('false', 'left', 'right', 'leftg', 'rightg', 'flat', 'standingside', 'stuc') 
            NOT NULL;
      SQL);
      return true;
    }

    /**
     * 23-04-2024 - Robert - nr mag leeg zijn
     */
    protected function execute12() {
      $this->executeQuery("ALTER TABLE " . \CrmAddresses::getTablename() . " CHANGE `nr` `nr` INT(5) UNSIGNED NULL DEFAULT NULL;");
      return true;
    }

    /**
     * 23-04-2024 - Max - Maten vensterbank decimal maken ipv smallint
     */
    protected function execute11() {
      $this->executeQuery("ALTER TABLE " . OrderElementWindowsill::getTablename() . " CHANGE `x1` `x1` DECIMAL(12,2) NULL DEFAULT NULL;");
      $this->executeQuery("ALTER TABLE " . OrderElementWindowsill::getTablename() . " CHANGE `x2` `x2` DECIMAL(12,2) NULL DEFAULT NULL;");
      $this->executeQuery("ALTER TABLE " . OrderElementWindowsill::getTablename() . " CHANGE `x3` `x3` DECIMAL(12,2) NULL DEFAULT NULL;");
      $this->executeQuery("ALTER TABLE " . OrderElementWindowsill::getTablename() . " CHANGE `x4` `x4` DECIMAL(12,2) NULL DEFAULT NULL;");
      $this->executeQuery("ALTER TABLE " . OrderElementWindowsill::getTablename() . " CHANGE `x5` `x5` DECIMAL(12,2) NULL DEFAULT NULL;");
      $this->executeQuery("ALTER TABLE " . OrderElementWindowsill::getTablename() . " CHANGE `x6` `x6` DECIMAL(12,2) NULL DEFAULT NULL;");
      return true;
    }

    /**
     * 07-11-2023 - Robert - reviews tabel goed zetten
     */
    protected function execute10() {
      $this->executeQuery("ALTER TABLE " . CustomerRatingEmails::getTablename() . " CHANGE `inActive` `inActive` BOOLEAN NOT NULL DEFAULT FALSE;");
      $this->executeQuery("ALTER TABLE " . CustomerRatingEmails::getTablename() . " CHANGE `sent` `sent` BOOLEAN NOT NULL DEFAULT FALSE;");
      return true;
    }

    /**
     * 29-08-2023 - Robert - veld mag null zijn
     */
    protected function execute9() {
      $this->executeQuery("ALTER TABLE " . GpsbuddyRoutes::getTablename() . " CHANGE `domestic` `domestic` VARCHAR(255) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL;");
      return true;
    }

    /**
     * 22-06-2023 - Max - veld toegevoegd aan CrmInvoiceparties om 1 order per keer te factureren ipv allemaal tegelijk + meerdere vrachtbon emails toegevoegd aan CrmCompanies
     */
    protected function execute8() {
      $this->executeQuery("ALTER TABLE " . CrmInvoiceparties::getTablename() . " ADD `orders_separate` BOOLEAN NULL DEFAULT FALSE AFTER `email`;");

      $this->executeQuery("ALTER TABLE " . CrmCompanies::getTablename() . " ADD `cargo_receipt_mail_1` VARCHAR(150) NULL;");
      $this->executeQuery("ALTER TABLE " . CrmCompanies::getTablename() . " ADD `cargo_receipt_mail_2` VARCHAR(150) NULL;");
      $this->executeQuery("ALTER TABLE " . CrmCompanies::getTablename() . " ADD `cargo_receipt_mail_3` VARCHAR(150) NULL;");

      return true;
    }

    /**
     * 09-05-2023 - Max - breti_invoice en breti_quotation table toegevoegd voor leverancier portaal
     */
    protected function execute7() {
      $this->executeQuery("CREATE TABLE " . BretiInvoice::getTablename() . " (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `breti_invoice_number` int(11) NOT NULL,
        `breti_invoice_date` datetime DEFAULT NULL,
        `total_project_value` DECIMAL(8,2) DEFAULT NULL,
        `insertTS` datetime DEFAULT NULL,
        `updateTS` datetime DEFAULT NULL,
        PRIMARY KEY (`id`)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8;");

      $this->executeQuery("CREATE TABLE " . BretiQuotation::getTablename() . " (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `breti_invoice_id` int(11) NOT NULL,
        `quotation_id` int(11) NOT NULL,
        PRIMARY KEY (`id`)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8;");

      return true;
    }

    /**
     * 10-03-2023 - Robert - niet bestelbaar bericht en
     */
    protected function execute6() {
      $this->executeQuery("ALTER TABLE " . Stones::getTablename() . " ADD `may_order` BOOLEAN NOT NULL DEFAULT TRUE AFTER `color_model_code`, ADD INDEX (`may_order`);");
      $this->executeQuery("ALTER TABLE " . Stones::getTablename() . " ADD `may_not_order_message` TEXT DEFAULT NULL AFTER `may_order`;");
      $this->executeQuery("ALTER TABLE " . Stones::getTablename() . " ADD `endstones_required` BOOLEAN NOT NULL DEFAULT FALSE AFTER `may_not_order_message`, ADD INDEX (`endstones_required`);");
      return true;
    }

    /**
     * 10-03-2023 - Max - Gedeeltelijk betalen kolommen toegevoegd
     */
    protected function execute5() {
      $this->executeQuery("ALTER TABLE " . Invoices::getTablename() . " ADD `payment_1` DECIMAL(8,2) NULL;");
      $this->executeQuery("ALTER TABLE " . Invoices::getTablename() . " ADD `payment_2` DECIMAL(8,2) NULL;");
      $this->executeQuery("ALTER TABLE " . Invoices::getTablename() . " ADD `payment_3` DECIMAL(8,2) NULL;");
      $this->executeQuery("ALTER TABLE " . Invoices::getTablename() . " ADD `payment_4` DECIMAL(8,2) NULL;");
      $this->executeQuery("ALTER TABLE " . Invoices::getTablename() . " ADD `payment_1_date` DATETIME NULL;");
      $this->executeQuery("ALTER TABLE " . Invoices::getTablename() . " ADD `payment_2_date` DATETIME NULL;");
      $this->executeQuery("ALTER TABLE " . Invoices::getTablename() . " ADD `payment_3_date` DATETIME NULL;");
      $this->executeQuery("ALTER TABLE " . Invoices::getTablename() . " ADD `payment_4_date` DATETIME NULL;");
      $this->executeQuery("ALTER TABLE " . Invoices::getTablename() . " CHANGE `vatRegShifted` `vatRegShifted` INT(2) NOT NULL;");
      return true;
    }

    /**
     * 07-02-2023 - Robert - employee_quotation cleanup
     */
    protected function execute4() {
      $this->executeQuery("ALTER TABLE rde_b1mo.`employee_quotation` DROP `washerEmployeeId`;");
      $this->executeQuery("ALTER TABLE rde_b1mo.`employee_quotation` CHANGE `updateDate` `enddate` DATETIME NULL;");
      $this->executeQuery("ALTER TABLE rde_b1mo.`employee_quotation` ADD `startdate` DATETIME NULL AFTER employeeId;");
      $this->executeQuery("ALTER TABLE rde_b1mo.`employee_quotation` ADD INDEX(`employeeId`)");
      $this->executeQuery("ALTER TABLE rde_b1mo.`employee_quotation` ADD INDEX(`startdate`)");
      $this->executeQuery("ALTER TABLE rde_b1mo.`employee_quotation` ADD INDEX(`enddate`)");
      $this->executeQuery("UPDATE rde_b1mo.`employee_quotation` SET startdate = NULL");
      $this->executeQuery("ALTER TABLE rde_b1mo.`employee_quotation` CHANGE `quotationId` `quotationId` INT(11) NULL;");

      $this->executeQuery("CREATE TABLE rde_b1mo.`employee_washer_quotation` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `quotationId` int(11) NULL,
        `employeeId` int(11) NOT NULL,
        `startdate` datetime DEFAULT NULL,
        `enddate` datetime DEFAULT NULL,
        PRIMARY KEY (`id`),
        KEY (`quotationId`),
        KEY (`employeeId`),
        KEY (`startdate`),
        KEY (`enddate`)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8;");
      return true;

    }

    /**
     * 31-01-2023 - Robert - employee_quotation cleanup
     */
    protected function execute3() {
      $this->executeQuery("ALTER TABLE rde_b1mo.`employee_quotation` DROP `scanData`;");
      $this->executeQuery("ALTER TABLE rde_b1mo.`employee_quotation` DROP `statusId`;");
      return true;

    }

    protected function execute2() {
      $this->executeQuery("ALTER TABLE rde_b1mo.`generated_rack_ids_employees` DROP `rackCode`, DROP `rackScanCode`;");
      $this->executeQuery("ALTER TABLE rde_b1mo.`generated_rack_ids_employees` ADD `washerEmployeeId` INT(11) NULL DEFAULT NULL AFTER `employeeId`;");
      $this->executeQuery("UPDATE rde_b1mo.`generated_rack_ids_employees` SET washerEmployeeId = employeeId;");
      $this->executeQuery("ALTER TABLE rde_b1mo.`employee_quotation` ADD `washerEmployeeId` INT(11) NULL DEFAULT NULL AFTER `employeeId`;");
      $this->executeQuery("UPDATE rde_b1mo.`employee_quotation` SET washerEmployeeId = employeeId;");
      return true;
    }

    protected function execute1() {
      $this->executeQuery("ALTER TABLE rde_b1mo.`quotations_custom_stone` ADD `width_click` SMALLINT(4) NOT NULL DEFAULT '20' AFTER `height`;");
      $this->executeQuery("ALTER TABLE rde_b1mo.`quotations_custom_stone` ADD `height_click` SMALLINT(4) NOT NULL DEFAULT '20' AFTER `width_click`;");
      return true;
    }

    public function __construct() {
      $this->setVersionCode(PROJECT . "-version");
    }

  }