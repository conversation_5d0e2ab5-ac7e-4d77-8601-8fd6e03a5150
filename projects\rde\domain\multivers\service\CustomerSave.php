<?php

  namespace domain\multivers\service;

  use CrmAddresses;
  use CrmInvoiceparties;
  use gsdfw\domain\multivers\exception\MultiversException;
  use gsdfw\domain\multivers\model\CustomerModel;
  use gsdfw\domain\multivers\service\Customer;
  use gsdfw\domain\multivers\service\MultiversApi;
  use SandboxUsers;
  use StringHelper;
  use ValidationHelper;

  /**
   * Class CustomerSave
   * Save SandboxUser to multivers Customer
   * @package domain\multivers\service
   */
  class CustomerSave {

    private $multivers_api;
    private $user;

    public function __construct(MultiversApi $multivers_api, SandboxUsers $user) {
      $this->multivers_api = $multivers_api;
      $this->user = $user;
    }

    /**
     * Save customer to multivers
     * @return bool|CustomerModel
     * @throws MultiversException
     */
    public function save() {

      $crm_invoiceparty = CrmInvoiceparties::find_by(["companyId" => $this->user->companyId]);

      //aanmaken nieuwe klant of updaten bestaande klant
      $customer = new Customer($this->multivers_api);

//      //rde: 0051 is een belg
//      pd($customer->retrieveById("0001"));
//      ResponseHelper::exit();

      if ($this->user->company->invoicePartyId == "") {
        throw new MultiversException("U kunt geen factuur sturen naar een klant zonder invoiceParty. Klant: " . $this->user->getNaam() . ", id: " . $this->user->userId);
      }

      $ip = CrmInvoiceparties::find_by(["invoicePartyId" => $this->user->company->invoicePartyId]);
      $invoice_address = CrmAddresses::find_by(["addressId" => $ip->addressId]);

      $cm = $customer->exists($this->user->companyId); //bestaat klant?

      if (!$cm) {
        //bestaat niet, aanmaken nieuwe
        $cm = new CustomerModel();
        $cm->setNew(true);
        $cm->set("customerId", $this->user->companyId);
        $cm->set("customerStateId", "A");
        $cm->set("paymentConditionId", 1);
        $cm->set("revenueAccountId", 8030);
        $cm->set("usesUBLInvoice", 1);
      }

      $cm->set("shortName", DEVELOPMENT ? 'TEST' : substr($this->user->company->name, 0, 8));
      $cm->set("name", substr($this->user->company->name . (DEVELOPMENT ? ' TEST' : ''), 0, 40));
      $cm->set("city", $invoice_address->domestic);
      $cm->set("street1", substr($invoice_address->getAddress(), 0, 40));
      $cm->set("zipCode", $invoice_address->zipcode);
      $cm->set("fullAddress", $this->user->company->name . " " . $invoice_address->getAddressFormatted());
      $cm->set("countryId", strtoupper($invoice_address->country));

      if ($crm_invoiceparty) {
        $crm_invoiceparty->vatRegNo = StringHelper::cleanVatnumber($crm_invoiceparty->vatRegNo);
        if (!empty($crm_invoiceparty->vatRegNo) && ValidationHelper::isVatnumber($crm_invoiceparty->vatRegNo)) {
          $cm->set("vatNumber", $crm_invoiceparty->vatRegNo);
        }
      }
      if ($this->user->company->tradeRegNo != "") {
        $cm->set("cocRegistration", substr($crm_invoiceparty->tradeRegNo, 0, 12));
      }

      //opslaan klantgegevens
      $cm = $cm->isNew() ? $customer->add($cm) : $customer->put($cm);

      if ($cm === false) {
        return false;
      }
      return $cm;
    }


  }