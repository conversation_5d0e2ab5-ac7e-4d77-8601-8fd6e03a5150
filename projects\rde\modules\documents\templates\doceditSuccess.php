<?php
  TemplateHelper::includePartial('_tabs.php', 'documents');
?>
<?php
  /** @var Invoices $invoice */
?>

<h3>Bewerk document <?php echo $doc->title ?></h3>

<?php writeErrors($form->getErrors(), true); ?>

<form method="post" class="edit-form">
  <table class="default_table">
    <tr class="dataTableHeadingRow">
      <td>Item</td>
      <td>Instelling</td>
    </tr>
    <tr class="dataTableRow trhover">
      <td>Bedrijfsnaam</td>
      <td><?php echo $company->name ?></td>
    </tr>
    <tr class="dataTableRow trhover">
      <td>Omschrijving</td>
      <td><?php echo $doc->title ?></td>
    </tr>
    <tr class="dataTableRow trhover">
      <td>Datum</td>
      <td><?php echo $doc->getDocumentdate() ?></td>
    </tr>
    <tr class="dataTableRow trhover">
      <td>Bestand</td>
      <td>
        <?php echo BtnHelper::getPrintPDF("?action=docopen&id=".$doc->fileId) ?>
    </tr>
    <tr class="dataTableRow trhover">
      <td><?php echo $form->getElement("uploadAlert")->getLabel(); ?></td>
      <td>
        <?php $form->getElement("uploadAlert")->render(); ?>
      </td>
    </tr>
    <tr class="dataTableRow trhover">
      <td><?php echo $form->getElement("notes")->getLabel(); ?></td>
      <td><?php $form->getElement("notes")->render(); ?></td>
    </tr>
    <tr class="dataTableRow trhover">
      <td>Opmerking alert</td>
      <td>
        <?php $form->getElement("notesAlert")->render(); ?>
      </td>
    </tr>

  </table>

  <input type="submit" name="go" value="Opslaan" class="gsd-btn gsd-btn-primary"/>
  <input type="submit" name="go_list" value="Opslaan en naar lijst" class="gsd-btn gsd-btn-secondary"/>

</form>