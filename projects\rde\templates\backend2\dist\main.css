/*!**************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/css-loader/dist/cjs.js??ruleSet[1].rules[0].use[1]!./node_modules/sass-loader/dist/cjs.js!./node_modules/postcss-loader/dist/cjs.js??ruleSet[1].rules[0].use[3]!../../projects/rde/resources/backend2/style/imports.css ***!
  \**************************************************************************************************************************************************************************************************************************************************/
@import url(https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap);
/*!******************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/css-loader/dist/cjs.js??ruleSet[1].rules[0].use[1]!./node_modules/sass-loader/dist/cjs.js!./node_modules/postcss-loader/dist/cjs.js??ruleSet[1].rules[0].use[3]!../../projects/rde/resources/backend2/style/imports.css (1) ***!
  \******************************************************************************************************************************************************************************************************************************************************/
@charset "UTF-8";
/* import the default backend css */
@CHARSET "UTF-8";
:root {
  /** use CSS variables instead of SASS variables, as these can easily be overwritten on project level **/
  /* standaard kleuren */
  --gsd-primary-color: #A8BA2A;
  --gsd-primary-color-100: #f3f5e7;
  /** button colors **/
  /* standaard button kleuren */
  --gsd-btn-bg-color: hsl(60, 5%, 90%);
  --gsd-btn-text-color: hsl(24, 10%, 10%);
  --gsd-btn-bg-color-hover: hsl(20, 6%, 86%);
  --gsd-btn-text-color-hover: hsl(207, 60%, 45%);
  --gsd-btn-primary-bg-color: var(--gsd-primary-color);
  --gsd-btn-primary-text-color: white;
  --gsd-btn-primary-bg-color-hover: #BAC954;
  --gsd-btn-primary-text-color-hover: white;
  --gsd-btn-secondary-border-color: var(--gsd-primary-color);
  --gsd-btn-secondary-text-color: var(--gsd-primary-color);
  --gsd-btn-secondary-border-color-hover: var(--gsd-primary-color);
  --gsd-btn-secondary-text-color-hover: white;
  --gsd-btn-secondary-bg-color-hover: var(--gsd-primary-color);
  --gsd-btn-tertiary-bg-color: white;
  --gsd-btn-tertiary-text-color: var(--gray-400);
  --gsd-btn-tertiary-border-color: var(--gray-400);
  --gsd-btn-tertiary-bg-color-hover: var(--gray-400);
  --gsd-btn-tertiary-text-color-hover: white;
  /* sidebar */
  --sidebar-color: hsl(240, 6%, 91%);
  --sidebar-active-color: var(--gsd-primary-color);
  --sidebar-logo-border-color: hsl(240, 13%, 15%);
  --sidebar-background-color: hsl(240, 18%, 10%);
  --sidebar-submenu-background-color: hsl(240, 15%, 18%);
  --minimized-sidebar-width: 65px;
  --main-background-color: hsl(240, 20%, 97%);
  --primary-active-color: var(--gsd-primary-color);
  --primary-active-color-100: #f3f5e7;
  --gray-100: hsl(240, 20%, 97%); /* F6F6F9 */
  --gray-200: hsl(240, 6%, 91%); /* E6E6E9 */
  --gray-400: hsl(240, 3%, 66%); /* A5A5AB */
  --gray-600: hsl(240, 3%, 46%); /* A5A5AB */
  --gray-800: hsl(240, 15%, 18%); /* 272735 */
  --gray-900: hsl(240, 18%, 10%); /* 15151E */
  /* lists */
  --default-table-hover: #F3F5E7;
  --default-table-border-color: var(--gray-200);
}

/***********
  BASE is for all default html elements (no classes/ids)
************/
/* Google Fonts Import Link */
/*
  This is used as base, by setting the font-size this way, 1 rem becomes 10 px, so 1.6rem = 16px
  see: https://stackoverflow.com/a/43131958
 */
:root {
  font-size: 62.5%; /* 10/16 (16px is default browser font-size) which is 0.625 = 62.5% */
}

html, body {
  font-size: 1.4rem;
  font-family: "Poppins", sans-serif;
  width: 100%;
  height: 100%;
  background: var(--main-background-color);
}

h1 {
  font-size: 3.2rem;
  font-weight: 600;
  margin: 0;
  color: var(--gray-900);
}

@media (max-width: 600px) {
  h1 {
    font-size: 2.5rem;
  }
}
h2 {
  font-size: 2.2rem;
  font-weight: 500;
  color: var(--gray-900);
}

h3 {
  font-size: 1.8rem;
  font-weight: 500;
  color: var(--gray-900);
}

h4 {
  font-size: 1.6rem;
  font-weight: 500;
  color: var(--gray-900);
}

td {
  vertical-align: top;
  text-align: left;
}

img {
  border: 0;
}

form {
  display: inline;
}

a {
  color: var(--gray-900);
  text-decoration: none;
}

a:hover {
  color: var(--primary-active-color);
}

/* move position slightly to make the click visual **/
a:active {
  position: relative;
  top: 1px;
  left: 1px;
}

.main-grid {
  display: grid;
  width: 100%;
  min-height: 100%;
  grid-template-areas: "head head" "nav userbar" "nav horizontal-nav" "nav main" "nav foot";
  grid-template-rows: auto auto auto 1fr auto;
  grid-template-columns: auto 1fr;
}
.main-grid.minimal-layout {
  /* only header and main content area */
  grid-template-areas: "head" "main";
  grid-template-rows: auto 1fr;
  grid-template-columns: 1fr;
}
.main-grid header {
  grid-area: head;
}
.main-grid aside {
  grid-area: nav;
  background-color: var(--sidebar-background-color);
}
.main-grid .horizontal-menu-bar {
  grid-area: horizontal-nav;
}
.main-grid main {
  grid-area: main;
  position: relative;
  transition: all 0.5s ease;
  padding: 0 10px;
}
.main-grid footer {
  grid-area: foot;
  display: flex;
  justify-content: space-around;
  padding: 20px;
}
.main-grid footer * {
  color: hsl(217, 15%, 70%);
}
.main-grid .userbar {
  grid-area: userbar;
}
.main-grid.minimized-sidebar main, .main-grid.minimized-sidebar header, .main-grid.minimized-sidebar footer, .main-grid.minimized-sidebar .userbar {
  margin-left: var(--minimized-sidebar-width);
}

.has_leftmenu {
  display: flex;
}

.has_leftmenu .div_leftmenu {
  width: 200px;
  margin-right: 15px;
  padding: 10px 0;
  background-color: rgba(240, 240, 240, 0.5019607843);
}

div.content.has_leftmenu {
  padding-top: 5px;
}

.sidebar {
  width: 276px;
  z-index: 100;
  transition: all 0.3s;
  /** Minimized NAVBAR **/
}
.sidebar .logo {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 60px;
  padding: 8px;
  overflow: hidden;
  border-bottom: 1px solid var(--sidebar-logo-border-color);
  background-size: 125px auto;
  background-position: center;
  background-repeat: no-repeat;
  transition: background-size 0.5s;
}
.sidebar .toggle-menu {
  display: flex;
  justify-content: flex-end;
  align-items: flex-end;
  color: var(--sidebar-color);
  padding: 20px 10px 0 0;
  font-size: 1.5em;
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
  cursor: pointer;
}
.sidebar .toggle-menu .toggle-sidebar:before {
  content: "\e9e0";
}
.sidebar .toggle-menu:hover {
  color: var(--sidebar-active-color);
}
.sidebar .toggle-menu.open {
  justify-content: center;
  padding-right: 0;
  transition: all 0.4s ease;
}
.sidebar .toggle-menu.open .toggle-sidebar:before {
  content: "\e9e2";
}
.sidebar nav {
  padding: 10px 0 150px 0;
  /** overflow: auto;  uitgezet, gaf scrollbar op firefox bij openklappen menu**/
}
.sidebar nav ul {
  padding-left: 0;
  margin-top: 0;
}
.sidebar nav li {
  position: relative;
  list-style: none;
  transition: all 0.4s ease;
  margin: 6px 12px;
  /** SUBMENU **/
  /* de 2de en 3de selectors zorgen ervoor dat het submenu openklapt en blijft als je hovert op het links icoontje */
}
.sidebar nav li.sub-items-header {
  display: none;
}
.sidebar nav li .nav-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.sidebar nav li .nav-item.active * {
  color: var(--sidebar-active-color);
}
.sidebar nav li .nav-item a {
  display: flex;
  flex-grow: 1;
  align-items: center;
  text-decoration: none;
  font-size: 1.6rem;
  font-weight: 500;
  color: var(--sidebar-color);
}
.sidebar nav li .nav-item a .link-name {
  transition: all 0.4s ease;
}
.sidebar nav li .nav-item i {
  height: 44px;
  min-width: 41px;
  text-align: center;
  line-height: 44px;
  color: var(--sidebar-color);
  cursor: pointer;
  transition: transform 0.3s ease;
}
.sidebar nav li .nav-item i.nav-item-icon {
  font-size: 1.8rem;
}
.sidebar nav li .nav-item i.arrow {
  font-size: 0.7em;
}
.sidebar nav li:hover {
  background: var(--sidebar-submenu-background-color);
  border-radius: 6px;
}
.sidebar nav li:hover > .nav-item a * {
  color: var(--sidebar-active-color);
}
.sidebar nav li:hover > .nav-item a .link-name {
  color: var(--sidebar-active-color);
}
.sidebar nav li:hover > .nav-item i {
  color: var(--sidebar-active-color);
}
.sidebar nav li ul {
  padding: 0 0 12px 29px;
  display: none;
  /** no items on sub levels **/
  /** SUB SUB MENU **/
}
.sidebar nav li ul a {
  font-weight: 500;
  color: var(--gray-400);
  font-size: 1.5rem;
  padding: 4px 0;
  transition: all 0.3s ease;
}
.sidebar nav li ul i.ki-solid {
  display: none;
}
.sidebar nav li ul ul {
  padding: 0 0 12px 7px;
}
.sidebar nav li.sub-open {
  background: var(--sidebar-submenu-background-color);
  border-radius: 6px;
}
.sidebar nav li.sub-open > li i.arrow, .sidebar nav li.sub-open > div i.arrow, .sidebar nav li:has(i.nav-item-icon:hover) > li i.arrow, .sidebar nav li:has(i.nav-item-icon:hover) > div i.arrow, .sidebar nav li:has(ul:hover) > li i.arrow, .sidebar nav li:has(ul:hover) > div i.arrow {
  transform: rotate(-180deg);
}
.sidebar nav li.sub-open > ul, .sidebar nav li:has(i.nav-item-icon:hover) > ul, .sidebar nav li:has(ul:hover) > ul {
  /* dit moet een inline-block zijn, anders werkt hover op linker icoontje niet goed */
  display: inline-block;
}
.sidebar nav li.sub-open a:hover, .sidebar nav li:has(i.nav-item-icon:hover) a:hover, .sidebar nav li:has(ul:hover) a:hover {
  opacity: 1;
}
.sidebar nav li.active:before,
.sidebar nav li:hover:before {
  opacity: 1;
}
.sidebar nav > li.active:before,
.sidebar nav > li:before {
  position: absolute;
  left: 0;
  top: 0;
  content: "";
  width: 4px;
  height: 100%;
  background: #5B5B5B;
  opacity: 0;
  transition: all 0.25s ease-in-out;
  border-top-right-radius: 5px;
  border-top-right-radius: 5px;
}
.sidebar nav::-webkit-scrollbar {
  display: none;
}
.sidebar.minimized {
  position: fixed;
  height: 100vh;
  width: var(--minimized-sidebar-width);
  overflow: hidden;
}
.sidebar.minimized .logo {
  background-size: 55px auto;
}
.sidebar.minimized .logo .logo_name {
  transition-delay: 0s;
  opacity: 0;
  pointer-events: none;
}
.sidebar.minimized nav {
  overflow: visible;
}
.sidebar.minimized nav .link-name {
  margin-left: 20px;
}
.sidebar.minimized nav li.sub-open > ul {
  display: none;
}
.sidebar.minimized.minimized-hover {
  /* Hover on minimized sidebar opens the full sidebar */
}
.sidebar.minimized.minimized-hover:hover {
  width: 276px;
}
.sidebar.minimized.minimized-hover:hover .toggle-menu.open {
  justify-content: flex-end;
  padding-right: 10px;
}
.sidebar.minimized.minimized-hover:hover .link-name {
  margin-left: 0px;
}
.sidebar.minimized.minimized-hover:hover li.sub-open > ul {
  display: block;
}
.sidebar.minimized.minimized-hover:hover li.sub-open > ul li a {
  /* this will make the animation look nicer, because the text wont change height */
  text-wrap: nowrap;
}

@media (max-width: 420px) {
  .sidebar.minimized nav li ul {
    display: none;
  }
}
/* ICONS SIDEBAR */
.sidebar .nav-item-icon:before {
  content: "\e906";
}

.sidebar .nav-item-icon.M_HOME:before {
  content: "\ea41" !important;
}

.sidebar .nav-item-icon.M_CATALOG:before {
  /*content: "\f00b" !important;*/
  content: "\e91b" !important;
}

.sidebar .nav-item-icon.M_WEBSITE:before {
  content: "\e9d4" !important;
}

.sidebar .nav-item-icon.M_ORGANISATIONS:before,
.sidebar .nav-item-icon.M_USERSIMPLE:before {
  content: "\e936" !important;
}

.sidebar .nav-item-icon.M_INVOICES:before {
  content: "\e9b2" !important;
}

.sidebar .nav-item-icon.M_ORDER_OV:before {
  content: "\e95c" !important;
}

.sidebar .nav-item-icon.M_WEBSITE_MESSAGES:before {
  content: "\ea71" !important;
}

.sidebar .nav-item-icon.M_HOURS:before {
  content: "\eb0f" !important;
}

.sidebar .nav-item-icon.M_OTHER:before {
  content: "\e906" !important;
}

.sidebar .nav-item-icon.M_SETTINGS:before {
  content: "\eac6" !important;
}

.sidebar .nav-item-icon.M_SMOELEN:before {
  content: "\ea09" !important;
}

.sidebar .nav-item-icon.M_STATS:before {
  content: "\e992" !important;
}

.horizontal-menu-bar {
  display: flex;
  justify-content: center;
  align-items: center;
  background: var(--sidebar-background-color);
  color: var(--sidebar-color);
}
.horizontal-menu-bar nav {
  /* Reset */
  /* ROOT MENU */
}
.horizontal-menu-bar nav ul, .horizontal-menu-bar nav li {
  margin: 0;
  padding: 0;
}
.horizontal-menu-bar nav > ul {
  display: flex;
  align-items: center;
  flex-wrap: wrap; /* wrap when the screen size is smaller as the root menu */
  margin: 0;
  padding: 0;
  list-style: none;
  position: relative;
  z-index: 1000;
  width: 100%;
  /* SUBMENUS */
  /* END SUBMENUS */
  /* 2nd Menu */
  /* 2nd Menu Hover Persistence */
  /* 3rd Menu */
  /* 3rd Menu Hover Persistence */
  /* 4th Menu */
  /* 4th Menu Hover */
  /* Hover Function - Do Not Move */
}
.horizontal-menu-bar nav > ul ul {
  width: 250px; /* Sub Menu Width */
  margin: 0;
  list-style: none;
  display: none;
  position: absolute;
  top: 100%;
  left: 0;
  z-index: 1000;
}
.horizontal-menu-bar nav > ul ul li {
  width: 100%;
  display: block;
}
.horizontal-menu-bar nav > ul ul ul, .horizontal-menu-bar nav > ul ul ul ul {
  top: 0;
  left: 100%;
}
.horizontal-menu-bar nav > ul ul .has-sub-items.iehover > a {
  background-color: #f8f8f8;
}
.horizontal-menu-bar nav > ul li {
  position: relative;
  transition: 0.15s ease-in-out;
}
.horizontal-menu-bar nav > ul li:hover {
  background: var(--sidebar-submenu-background-color);
}
.horizontal-menu-bar nav > ul a {
  display: flex;
  align-items: center;
  gap: 8px;
  border-top: 0;
  padding: 20px 15px 20px 15px;
  color: var(--sidebar-color);
  font-size: 1.6rem;
  text-decoration: none;
  background: none;
}
.horizontal-menu-bar nav > ul a.active {
  color: var(--sidebar-active-color);
}
.horizontal-menu-bar nav > ul a:hover {
  color: var(--sidebar-active-color);
}
.horizontal-menu-bar nav > ul > li.has-sub-items > a::after {
  content: "\f0d7";
  font-family: "FontAwesome";
  margin-top: 3px;
  font-size: 13px;
  color: #d4d4d4;
}
.horizontal-menu-bar nav > ul li:hover li a, .horizontal-menu-bar nav > ul li.iehover li a {
  padding: 10px 20px;
  border-right: 0;
  background: var(--sidebar-submenu-background-color);
  color: var(--sidebar-color);
  font-size: 1.5rem;
  transition: all 0.3s ease;
}
.horizontal-menu-bar nav > ul li:hover li:last-child a {
  border-bottom-left-radius: 6px;
  border-bottom-right-radius: 6px;
}
.horizontal-menu-bar nav > ul li:hover li a:hover, .horizontal-menu-bar nav > ul li:hover li:hover a, .horizontal-menu-bar nav > ul li.iehover li a:hover, .horizontal-menu-bar nav > ul li.iehover li.iehover a {
  color: var(--sidebar-active-color);
}
.horizontal-menu-bar nav > ul li:hover li:hover li a, .horizontal-menu-bar nav > ul li.iehover li.iehover li a {
  color: var(--sidebar-active-color);
}
.horizontal-menu-bar nav > ul li:hover li:hover li a:hover, .horizontal-menu-bar nav > ul li:hover li:hover li:hover a, .horizontal-menu-bar nav > ul li.iehover li.iehover li a:hover, .horizontal-menu-bar nav > ul li.iehover li.iehover li.iehover a {
  color: var(--sidebar-active-color);
}
.horizontal-menu-bar nav > ul li:hover li:hover li:hover li a, .horizontal-menu-bar nav > ul li.iehover li.iehover li.iehover li a {
  color: var(--sidebar-active-color);
}
.horizontal-menu-bar nav > ul li:hover li:hover li:hover li a:hover, .horizontal-menu-bar nav > ul li.iehover li.iehover li.iehover li a:hover {
  color: var(--sidebar-active-color);
}
.horizontal-menu-bar nav > ul li:hover ul ul, .horizontal-menu-bar nav > ul li:hover ul ul ul, .horizontal-menu-bar nav > ul li.iehover ul ul, .horizontal-menu-bar nav > ul li.iehover ul ul ul {
  display: none;
}
.horizontal-menu-bar nav > ul li:hover ul, .horizontal-menu-bar nav > ul ul li:hover ul, .horizontal-menu-bar nav > ul ul ul li:hover ul, .horizontal-menu-bar nav > ul li.iehover ul, .horizontal-menu-bar nav > ul ul li.iehover ul, .horizontal-menu-bar nav > ul ul ul li.iehover ul {
  display: block;
}

@media (max-width: 768px) {
  .horizontal-menu-bar {
    padding: 8px 0;
  }
  .horizontal-menu-bar nav > ul a {
    padding: 8px 12px;
    font-size: 1.5rem;
  }
}
.userbar {
  display: flex;
  justify-content: space-between;
  padding: 10px 30px 10px 30px;
  height: 60px;
  background: var(--main-background-color);
  border: 1px solid var(--gray-200);
}
.userbar .logo {
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
}
.userbar .logo .logo-link {
  width: 150px;
  height: 100%;
  background-size: contain;
  background-repeat: no-repeat;
}
.userbar .user-actions {
  display: flex;
  color: var(--gray-900);
  text-align: right;
  align-items: center;
  justify-content: flex-end;
  font-size: 1.4rem;
}
.userbar .user-actions > * {
  margin-left: 18px;
}
.userbar .user-actions .popout-box summary {
  font-weight: 600;
  display: flex;
  align-items: center;
}
.userbar .user-actions .popout-box summary:hover {
  color: var(--primary-active-color);
}
.userbar .user-actions .popout-box summary i.user-icon {
  margin-right: 8px;
  font-size: 2rem;
}
.userbar .user-actions .popout-box summary .user-icon:before {
  content: "\eaa5";
}
.userbar .user-actions .popout-box summary i.caret {
  margin-left: 6px;
}
.userbar .user-actions .popout-box .popout-content {
  padding: 10px 8px;
  text-align: left;
}
.userbar .user-actions .popout-box .popout-content a {
  display: block;
  padding: 4px 12px;
}
.userbar .user-actions .popout-box .popout-content a:hover {
  background-color: var(--gray-100);
}
.userbar .user-actions .companyname {
  font-style: italic;
}
.userbar .user-actions a#return2admin {
  color: var(--primary-active-color);
}
.userbar .user-actions .notification {
  position: relative;
}
.userbar .user-actions .notification summary {
  padding-right: 6px;
}
.userbar .user-actions .notification summary .notifications-amount {
  position: absolute;
  background-color: #ed1c24;
  z-index: 1101;
  right: -8px;
  top: -10px;
  color: white;
  text-align: center;
  padding: 2px 6px;
  font-weight: normal;
  border-radius: 100%;
  font-size: 1.2rem;
}
.userbar .user-actions .notification a.notification-item {
  display: block;
  padding: 6px;
}
.userbar .user-actions .notification a.notification-item span {
  text-decoration: underline;
}

.logo-image {
  background-image: url(/projects/rde/templates/backend2/dist/71bead68c701e75b873e.svg);
}

@media (min-width: 1600px) {
  /* only change when horizontal menu is active */
  .horizontal-menu-active .userbar {
    width: 1600px;
    margin: 0 auto;
    border: 0;
  }
}
/** structure elements for all template pages (...Success.php) */
.content-container {
  margin: 5px auto 15px;
  max-width: 1600px;
  padding: 0 15px;
}

@media (max-width: 600px) {
  .content-container {
    padding: 0;
  }
}
.content {
  padding: 0 0 25px 0;
  vertical-align: top;
  min-height: 400px;
}

.title-bar {
  display: flex;
  flex-wrap: wrap; /* put tabs below page title if there are too many tabs */
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  margin-top: 16px;
  /** ik heb deze tijdelijk uit gezet. Gaf veel geneuzel. Ook met submenu welke meerdere items bevatten **/
}
.title-bar .title {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 16px;
}
.module-description {
  display: block;
  width: 100%;
  font-size: 1.5rem;
  /* for better readability of long lines */
  max-width: 1000px;
  line-height: 2rem;
  padding: 8px 0px;
}
.module-description h1, .module-description h2, .module-description h3 {
  margin: 16px 0 24px 0;
}
.module-description p {
  color: var(--gray-600);
}

.edit-form, table.nextprev, .default_table, .content-block {
  background: white;
  border-radius: 12px;
  border: 1px solid hsl(60, 9%, 93%);
}

.content-block {
  padding-top: 12px;
  padding-bottom: 12px;
  margin-bottom: 16px;
  font-size: 1.5rem;
  line-height: 2rem;
  /* content block met white space links/rechts */
}
.content-block.with-padding {
  padding-left: 16px;
  padding-right: 16px;
}
.content-block.home {
  margin: 36px auto;
  max-width: 600px;
  padding: 24px;
}
.content-block.home img {
  margin: 24px 0;
}

@media (max-width: 600px) {
  section.main-grid.vertical-menu-active main {
    max-width: calc(100vw - var(--minimized-sidebar-width));
  }
  .content-block {
    overflow: scroll;
  }
}
.default_table {
  width: 100%;
  border-spacing: 0;
  font-size: 1.4rem;
  margin-top: 10px;
}
.default_table a {
  text-decoration: none;
}
.default_table .no-border-radius td, .default_table .no-border-radius th {
  border-radius: 0 !important;
}
.default_table tr.dataTableRow {
  /* als de tr een td.sort-td (onzichtbare sorteer kolom) heeft, zet dan padding op de 2de td */
  /* border-radius op de onderste linker en rechter cellen van de tabel */
}
.default_table tr.dataTableRow:has(.sort-td) td:nth-child(2) {
  padding-left: 15px !important;
}
.default_table tr.dataTableRow:last-child td:first-child {
  border-bottom-left-radius: 12px;
  padding-left: 15px;
}
.default_table tr.dataTableRow:last-child td:last-child {
  border-bottom-right-radius: 12px;
  padding-right: 15px;
}
.default_table tr.dataTableRow td {
  padding: 8px 4px 8px 8px;
  border-bottom: 1px solid var(--default-table-border-color);
  vertical-align: middle;
  /* gsd-btn in de tabel td niet zo groot
  a.gsd-btn {
    padding: 6px 12px;
  }*/
}
.default_table tr.dataTableRow td:first-child {
  padding-left: 15px;
}
.default_table tr.dataTableRow td:last-child {
  padding-right: 15px;
}
.default_table tr.dataTableRow td a:not(.gsd-btn) {
  font-weight: 500;
  color: var(--gray-900);
  text-decoration: none;
}
.default_table tr.dataTableRow td a:not(.gsd-btn):hover {
  text-decoration: underline;
}
.default_table tr.dataTableRow td.actions {
  text-align: right;
  width: 60px;
  /* has 2 children */
  /* has 3 children */
  /* has 4 children */
  /* At least 6 (6 or more) children */
}
.default_table tr.dataTableRow td.actions:has(> :nth-child(2):last-child) {
  width: 80px;
}
.default_table tr.dataTableRow td.actions:has(> :nth-child(3):last-child) {
  width: 100px;
}
.default_table tr.dataTableRow td.actions:has(> :nth-child(4):last-child) {
  width: 120px;
}
.default_table tr.dataTableRow td.actions:has(> :nth-child(6)) {
  width: 150px;
}
.default_table tr.dataTableRow td.actions .gsd-svg-icon-a {
  height: 24px;
  margin: 0 1px;
}
.default_table tr.dataTableRow.trhover:hover {
  background-color: var(--default-table-hover);
}
.default_table tr.rowhighlight {
  background-color: #ffc107;
}
.default_table tr.rowhighlightalert td {
  background-color: #f44336;
  color: white;
}
.default_table tr.rowhighlightalert td a {
  color: white;
}
.default_table .dataTableHeadingRow {
  /* als de tr een td.sort-td (onzichtbare sorteer kolom) heeft, zet dan padding op de 2de td */
}
.default_table .dataTableHeadingRow:has(.sort-td) td:nth-child(2) {
  padding-left: 15px !important;
}
.default_table .dataTableHeadingRow.nobottomborder td {
  border-bottom: 0;
}
.default_table .dataTableHeadingRow.notopborder td {
  border-top: 0;
  border-radius: 0 !important;
}
.default_table .dataTableHeadingRow td {
  padding: 15px 7px;
  font-weight: 600;
  vertical-align: top;
  background-color: #fafafc;
  color: var(--gray-900);
  border-top: none;
  border-top: 1px solid var(--default-table-border-color);
  border-bottom: 1px solid var(--default-table-border-color);
  font-size: 1.3rem;
}
.default_table .dataTableHeadingRow td:first-child {
  border-top-left-radius: 12px;
  padding-left: 15px !important;
}
.default_table .dataTableHeadingRow td:last-child {
  border-top-right-radius: 12px;
  padding-right: 15px;
}
.default_table .dataTableHeadingRow td a {
  text-decoration: none;
}
.default_table .dataTableHeadingRow td a.order:first-child {
  margin-left: 4px;
}
.default_table .dataTableHeadingRow td a.order .gsd-svg-icon svg {
  width: auto;
  height: auto;
  color: var(--gray-400);
}
.default_table .dataTableHeadingRow td a.order .gsd-svg-icon svg:hover {
  color: var(--gray-700);
}
.default_table .dataTableHeadingRow td a.order.orderactive .gsd-svg-icon svg {
  color: var(--gray-800);
}
.default_table .dataTableHeadingRow td.actions {
  text-align: right;
}
.default_table .dataTableHeadingRow td.actions.buttons-3 {
  min-width: 75px;
}
.default_table .dataTableHeadingRow.dataTableHeadingRowLow td {
  padding-top: 5px;
  padding-bottom: 5px;
}
.default_table .dataTableHeadingRow .qtipa.fa_info.fa.fa-info-circle,
.default_table .dataTableHeadingRow .qtipa.fa_alert.fa.fa-exclamation-circle,
.default_table .dataTableHeadingRow .fa.fa_help {
  font-size: 14px;
}
.default_table.notopborder {
  border-top: 0;
  border-radius: 0 !important;
}
.default_table.nobottomborder {
  border-bottom: 0;
}
.default_table label {
  /* voornamelijk styling voor labels met radio/checkbox erin */
  display: inline-flex;
  align-items: center;
  gap: 4px;
}
.default_table a.fa.fa-sort-down, .default_table a.fa.fa-sort-up {
  text-decoration: none;
}

.default_table_scroller {
  overflow-x: auto;
}

.default_table .material-icons, .default-data-table .material-icons {
  font-size: 17px;
}

.default_table_div {
  border-left: 1px solid rgba(230, 230, 230, 0.4);
  border-right: 1px solid rgba(230, 230, 230, 0.4);
  background-color: #F8F8F8;
}

.dataTableRow {
  vertical-align: top;
}

.dataTableHeadingTd {
  font-weight: bold;
}

.dataTableRowOver, .trhover:hover {
  background-color: #F5F8FD;
  height: 20px;
}

.dataTableHeadingRow td a.popuplink,
.dataTableHeadingRow td a.popuplink:hover,
.dataTableHeadingRow td a.popuplink:visited,
.dataTableHeadingRow td a.popuplink:active {
  text-decoration: none;
  color: white;
  font-weight: bold;
}

/* button to open child rows */
table.default-data-table.dataTable td.dt-control:before {
  font-weight: 500;
  line-height: 1.5;
  color: #181C32;
  text-align: center;
  vertical-align: middle;
  cursor: pointer;
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
  border: 1px solid transparent;
  font-size: 1.1rem;
  border-radius: 0.475rem;
  transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  background-color: #F5F8FD;
  height: calc(1.5em + 0.2rem + 2px);
  width: calc(1.5em + 0.2rem + 2px);
  /* resets from original styling */
  margin-top: 0;
  box-shadow: none;
  font-family: inherit;
}

/* button to close child rows */
table.default-data-table.dataTable tr.dt-hasChild td.dt-control:before {
  background-color: hsl(218, 67%, 90%);
}

/** Formulier gerelateerde styling **/
/* zodat tekst achter input veldjes mooi in het midden staat */
.table_td_middle tr.dataTableRow td {
  line-height: 20px;
}

.head {
  height: 23px;
  font-weight: 500;
}

.tablehead {
  font-weight: 500;
}

.edit-form .default_table {
  margin-top: 0;
}
.edit-form .default_table .dataTableHeadingRow {
  /** used for a headerrow not in top of the table **/
}
.edit-form .default_table .dataTableHeadingRow td, .edit-form .default_table .dataTableHeadingRow th {
  font-size: 1.6rem;
  font-weight: 500;
  background: var(--gray-100);
  border-top: 1px solid var(--default-table-border-color);
  border-bottom: 1px solid var(--default-table-border-color);
}
.edit-form .default_table .dataTableHeadingRow.topborder td, .edit-form .default_table .dataTableHeadingRow.topborder th {
  border-radius: 0;
}
.edit-form .default_table .dataTableHeadingRow.nobottomborder td, .edit-form .default_table .dataTableHeadingRow.nobottomborder th {
  border-bottom: 0;
}
.edit-form .default_table .dataTableHeadingRow.notopborder td, .edit-form .default_table .dataTableHeadingRow.notopborder th {
  border-top: 0;
  border-radius: 0 !important;
}
.edit-form .default_table .dataTableRow td {
  border: none;
}

/* overzichten waar geen items getoond worden */
.empty-list-state {
  padding: 8px 12px;
}

/* soms staan er 2 tabelen en een edit-form met bijvoorbeeld begeleiden tekst. deze tekst heeft standaard spacing */
.edit-form-text {
  padding: 8px 12px;
}

@media (max-width: 600px) {
  .default_table {
    max-width: calc(100vw - var(--minimized-sidebar-width));
    overflow-x: scroll;
  }
  .default_table select {
    /* forceer select breedte op mobiel */
    width: 100%;
  }
  .edit-form .default_table .dataTableRow {
    display: flex;
    height: auto;
    flex-wrap: wrap;
    margin-bottom: 12px;
    border-bottom: 1px solid var(--default-table-border-color);
  }
  .edit-form .default_table .dataTableRow td {
    display: flex;
    height: auto;
    flex-wrap: wrap;
    gap: 12px;
  }
  .edit-form .default_table .dataTableRow td.head {
    display: block;
    width: 100%;
    font-size: 1.6rem;
  }
}
table.dataTable.default-data-table {
  width: 100% !important;
  border-spacing: 0;
  border: 1px solid rgba(230, 230, 230, 0.4);
  border-radius: 3px 3px 0 0;
  margin: 8px 0;
  /* Grouped by rows */
}
table.dataTable.default-data-table a {
  text-decoration: none;
}
table.dataTable.default-data-table tr td {
  padding: 3px;
  border-bottom: 1px solid rgba(230, 230, 230, 0.4);
  vertical-align: middle;
  /* gsd-btn in de tabel td niet zo groot
  a.gsd-btn {
    padding: 6px 12px;
  }
  */
}
table.dataTable.default-data-table tr td:first-child {
  padding-left: 15px;
}
table.dataTable.default-data-table tr td:last-child {
  padding-right: 5px;
}
table.dataTable.default-data-table tr td.actions {
  text-align: right;
}
table.dataTable.default-data-table thead tr th {
  padding: 12px 7px;
  font-weight: 600;
  vertical-align: top;
  background-color: #fafafc;
  color: var(--gray-900);
  border-top: none;
  border-top: 1px solid var(--default-table-border-color);
  border-bottom: 1px solid var(--default-table-border-color);
  font-size: 1.3rem;
}
table.dataTable.default-data-table thead tr th:first-child {
  padding-left: 15px;
}
table.dataTable.default-data-table thead tr th:last-child {
  padding-right: 5px;
}
table.dataTable.default-data-table thead tr th.actions {
  text-align: right;
}
table.dataTable.default-data-table thead tr th.actions.buttons-3 {
  min-width: 75px;
}
table.dataTable.default-data-table tbody tr {
  /* standaard/minimale hoogte van 40px, zodat tabellen zonder Actions kolom dezelfde hoogte hebben als met de Actions kolom */
  height: 40px;
  vertical-align: top;
}
table.dataTable.default-data-table tbody tr:hover {
  background-color: var(--default-table-hover);
  /* standaard/minimale hoogte van 40px, zodat tabellen zonder Actions kolom dezelfde hoogte hebben als met de Actions kolom */
  height: 40px;
}
table.dataTable.default-data-table.no-footer {
  border: none;
}
table.dataTable.default-data-table tr.dtrg-group th {
  background-color: var(--gray-100);
  vertical-align: middle;
}
table.dataTable.default-data-table tr.dtrg-group.dtrg-level-0 th {
  font-weight: 500;
}

.dataTables_wrapper .dt-buttons {
  margin-left: 15px;
}

.dataTables_wrapper .dt-buttons button {
  font-family: "Open Sans", sans-serif;
  background-color: var(--gsd-btn-bg-color);
  color: var(--gsd-btn-text-color);
  border: 1px solid transparent;
  background-image: none;
  padding: 4px 10px;
  text-align: center;
  text-decoration: none;
  border-radius: 3px;
  font-size: inherit;
  line-height: inherit;
  transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.dataTables_wrapper .dt-buttons button:hover:not(:disabled) {
  background: var(--gsd-btn-bg-color-hover);
  text-decoration: none;
  border-color: transparent;
}

.dataTables_paginate {
  margin-right: 16px;
  margin-bottom: 8px;
}

.paginate_button.current {
  background: none !important;
  border: 1px solid var(--primary-active-color) !important;
  color: var(--primary-active-color) !important;
}

.dataTables_wrapper .dataTables_paginate .paginate_button {
  background-color: none;
  transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  font-weight: normal;
}

.dataTables_wrapper .dataTables_paginate .paginate_button:hover {
  background: var(--primary-active-color) !important;
  border-color: transparent !important;
  color: var(--gsd-btn-text-color) !important;
}

/* Select checkbox for row selection */
table.dataTable.compact > tbody > tr > td.select-checkbox:before, table.dataTable.compact > tbody > tr > th.select-checkbox:before {
  margin-top: 0 !important;
}

table.dataTable.compact > tbody > tr.selected > td.select-checkbox:after, table.dataTable.compact > tbody > tr.selected > th.select-checkbox:after {
  margin-top: -24px !important;
}

table.dataTable tbody tr.selected > * {
  box-shadow: inherit !important;
  background-color: #B0BED9;
}

div.dataTables_processing {
  background: white;
  box-shadow: 1px 4px 10px 0 rgba(0, 0, 0, 0.53), 0 3px 2px 0 rgba(0, 0, 0, 0.15);
  border-radius: 3px;
  padding: 10px 10px 0 10px !important;
  width: 250px;
  max-width: 100%;
  z-index: 10000;
}

/* content-block toevoegen anders kunnen we de styles van datatables niet overschrijven */
.content-block .dataTables_wrapper .dataTables_length {
  margin-left: 16px;
}
.content-block .dataTables_wrapper .dataTables_length select {
  padding: 4px 6px !important; /* smaller default padding */
}

.dataTables_info {
  margin-left: 16px;
}

div.box, .list-filter-form {
  display: flex;
  -moz-column-gap: 8px;
  column-gap: 8px;
  row-gap: 12px;
  align-items: center;
  flex-wrap: wrap;
  margin-bottom: 16px;
  background: white;
  border-radius: 12px;
  border: 1px solid var(--gray-200);
  padding: 10px 16px;
}
div.box input[type=search], div.box input[type=text], div.box input[type=number], div.box select, .list-filter-form input[type=search], .list-filter-form input[type=text], .list-filter-form input[type=number], .list-filter-form select {
  background: white;
  border: 1px solid var(--gray-200);
  padding: 8px 12px;
  line-height: normal; /* zodat ze dezelfde hoogte hebben */
}
div.box input[type=submit], .list-filter-form input[type=submit] {
  padding: 10px 24px;
}
div.box label, .list-filter-form label {
  display: inline-flex;
  align-items: center;
}
div.box input.datesel, div.box input.datepicker, div.box input.datepicker_week, .list-filter-form input.datesel, .list-filter-form input.datepicker, .list-filter-form input.datepicker_week {
  width: 11rem;
}

.select2.select2-container {
  height: 39px !important;
  max-width: 350px;
}

.select2-container .select2-selection--single,
.select2-container--default .select2-selection--single .select2-selection__rendered,
.select2-container--default.select2-container--focus .select2-selection--multiple {
  height: 39px !important;
  border-color: var(--gray-200) !important;
  border-radius: 6px !important;
}

.select2-container .select2-selection--multiple {
  min-height: 39px !important;
  border-color: var(--gray-200) !important;
  border-radius: 6px !important;
}

.select2-container .select2-search--inline .select2-search__field {
  margin-top: 8px !important;
}

.select2-container--default .select2-selection--single .select2-selection__rendered {
  line-height: 39px !important;
}

.select2-container--default .select2-selection--single .select2-selection__arrow {
  top: calc(50% - 13px) !important;
}

@media (max-width: 600px) {
  div.box select, .list-filter-form select {
    /* forceer select breedte op mobiel */
    width: 100%;
  }
}
table.nextprev {
  border-collapse: initial;
  width: 100%;
  border-spacing: 0;
  padding: 12px 12px;
  font-size: 1.5rem;
}
table.nextprev tr {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: space-between;
  gap: 12px;
}
table.nextprev td.nextprev {
  display: inline-flex;
  flex-wrap: wrap;
  align-items: center;
  /* pager met Bedrijfsnaam begint met .... */
}
table.nextprev td.nextprev a, table.nextprev td.nextprev .nextprevactive {
  font-weight: normal;
  vertical-align: middle;
  box-sizing: border-box;
  display: inline-block;
  min-width: 1.5em;
  padding: 0.5em 1em;
  margin-left: 2px;
  text-align: center;
  text-decoration: none;
  cursor: pointer;
  color: inherit !important;
  border: 1px solid transparent;
  border-radius: 2px;
  transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}
table.nextprev td.nextprev a:first-child .gsd-svg-icon svg, table.nextprev td.nextprev a:last-child .gsd-svg-icon svg, table.nextprev td.nextprev .nextprevactive:first-child .gsd-svg-icon svg, table.nextprev td.nextprev .nextprevactive:last-child .gsd-svg-icon svg {
  width: auto;
  height: auto;
  vertical-align: baseline;
}
table.nextprev td.nextprev a:hover, table.nextprev td.nextprev .nextprevactive:hover {
  background: var(--primary-active-color) !important;
  border-color: transparent !important;
  color: var(--gsd-btn-text-color) !important;
}
table.nextprev td.nextprev a:hover:not(:disabled), table.nextprev td.nextprev .nextprevactive:hover:not(:disabled) {
  background: var(--gsd-btn-bg-color-hover);
  text-decoration: none;
  border-color: transparent;
}
table.nextprev td.nextprev .nextprevactive {
  color: var(--primary-active-color);
  border: 1px solid var(--primary-active-color);
}
table.nextprev td.nextprev .pager_firstletter {
  font-size: 1.4rem;
  padding: 0.5em 0.5em;
}
table.nextprev td.nextprev a.pager_letterselected {
  color: var(--primary-active-color);
  border: 1px solid var(--primary-active-color);
  padding: 3px 7px;
  font-weight: 600;
}
table.nextprev i.fa.fa-chevron-right {
  display: none;
}

.gsd-svg-icon svg {
  color: var(--gray-400);
  height: 1.85rem;
  width: 1.85rem;
  display: inline-block;
  vertical-align: middle;
}

.icon-color-red .gsd-svg-icon svg {
  color: #f44336;
}

.icon-color-orange .gsd-svg-icon svg {
  color: #ff7811;
}

.icon-color-green .gsd-svg-icon svg {
  color: var(--gsd-primary-color);
}

.icon-color-off .gsd-svg-icon svg {
  color: #bbbbbb;
}

.gsd-svg-icon-a.icon-size-inline-form,
.icon-size-inline-form .gsd-svg-icon-a {
  height: 1.6em;
  width: 1.6em;
  vertical-align: top;
}

.icon-size-1 .gsd-svg-icon svg {
  height: 1.6rem;
  width: 1.6rem;
}

.icon-size-2 .gsd-svg-icon svg {
  height: 1.4rem;
  width: 1.4rem;
}

.gsd-svg-icon-a .gsd-svg-icon svg {
  display: block;
}

.gsd-svg-icon.gsd-phone svg {
  height: 1.7rem;
  width: 1.7rem;
}

.gsd-svg-icon.gsd-checkbox svg,
.gsd-svg-icon.gsd-checkbox-off svg,
.gsd-svg-icon.gsd-cross svg {
  height: 2.2rem;
  width: 2.2rem;
}

.gsd-svg-icon.gsd-checkbox-off svg {
  color: rgba(161, 165, 183, 0.3098039216);
}

.gsd-svg-icon.gsd-remind svg {
  height: 2.1rem;
  width: 2.1rem;
}

.gsd-svg-icon.gsd-add svg {
  height: 2.1rem;
  width: 2.1rem;
}

.gsd-svg-icon.gsd-plus svg,
.gsd-svg-icon.gsd-min svg {
  height: 2.1rem;
  width: 2.1rem;
}

.gsd-svg-icon.gsd-help svg,
.gsd-svg-icon.gsd-alert svg {
  height: 1.9rem;
  width: 1.9rem;
}

.gsd-svg-icon.gsd-mail svg {
  height: 2.2rem;
  width: 2.2rem;
}

.gsd-svg-icon.gsd-login svg,
.gsd-svg-icon.gsd-upload svg,
.gsd-svg-icon.gsd-lock svg {
  height: 2.4rem;
  width: 2.4rem;
}

.gsd-svg-icon-a {
  font-weight: 500;
  line-height: 1.5;
  color: var(--gray-400);
  text-align: center;
  cursor: pointer;
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
  border: 1px solid transparent;
  font-size: 1.7rem;
  transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  display: inline-flex;
}

.gsd-svg-icon-a:hover {
  color: var(--primary-active-color);
}
.gsd-svg-icon-a:hover .gsd-svg-icon svg {
  color: var(--primary-active-color);
}
.gsd-svg-icon-a:hover .gsd-remove svg {
  color: #c3321e;
}

.gsd-svg-icon.gsd-alert svg {
  color: #c3321e;
}

.icon-color-off.gsd-svg-icon-a:hover .gsd-svg-icon svg {
  color: var(--primary-active-color);
}

.gsd-svg-icon-width-2 {
  width: 77px;
}

.gsd-svg-icon-width-3 {
  width: 112px;
}

.gsd-svg-icon-width-4 {
  width: 146px;
}

/* Material Icons */
.material-icons.material-icon-green {
  color: #A8BA2A;
}

.material-icons.material-icon-orange {
  color: #ff7811;
}

.material-icons.material-icon-off {
  color: #bbbbbb;
}

@font-face {
  font-family: "keenicons-solid";
  src: url(/projects/rde/templates/backend2/dist/fonts/keenicons-solid.eot);
  src: url(/projects/rde/templates/backend2/dist/fonts/keenicons-solid.eot#iefix) format("embedded-opentype"), url(/projects/rde/templates/backend2/dist/fonts/keenicons-solid.ttf) format("truetype"), url(/projects/rde/templates/backend2/dist/fonts/keenicons-solid.woff) format("woff"), url(/projects/rde/templates/backend2/dist/bf01934481277240a34f.svg?812fv7#keenicons-solid) format("svg");
  font-weight: normal;
  font-style: normal;
  font-display: block;
}
.ki-solid {
  /* use !important to prevent issues with browser extensions that change fonts */
  font-family: "keenicons-solid" !important;
  speak: never;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;
  /* Better Font Rendering =========== */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.ki-abstract-1.ki-solid:before {
  content: "\e900";
}

.ki-abstract-2.ki-solid:before {
  content: "\e901";
}

.ki-abstract-3.ki-solid:before {
  content: "\e902";
}

.ki-abstract-4.ki-solid:before {
  content: "\e903";
}

.ki-abstract-5.ki-solid:before {
  content: "\e904";
}

.ki-abstract-6.ki-solid:before {
  content: "\e905";
}

.ki-abstract-7.ki-solid:before {
  content: "\e906";
}

.ki-abstract-8.ki-solid:before {
  content: "\e907";
}

.ki-abstract-9.ki-solid:before {
  content: "\e908";
}

.ki-abstract-10.ki-solid:before {
  content: "\e909";
}

.ki-abstract-11.ki-solid:before {
  content: "\e90a";
}

.ki-abstract-12.ki-solid:before {
  content: "\e90b";
}

.ki-abstract-13.ki-solid:before {
  content: "\e90c";
}

.ki-abstract-14.ki-solid:before {
  content: "\e90d";
}

.ki-abstract-15.ki-solid:before {
  content: "\e90e";
}

.ki-abstract-16.ki-solid:before {
  content: "\e90f";
}

.ki-abstract-17.ki-solid:before {
  content: "\e910";
}

.ki-abstract-18.ki-solid:before {
  content: "\e911";
}

.ki-abstract-19.ki-solid:before {
  content: "\e912";
}

.ki-abstract-20.ki-solid:before {
  content: "\e913";
}

.ki-abstract-21.ki-solid:before {
  content: "\e914";
}

.ki-abstract-22.ki-solid:before {
  content: "\e915";
}

.ki-abstract-23.ki-solid:before {
  content: "\e916";
}

.ki-abstract-24.ki-solid:before {
  content: "\e917";
}

.ki-abstract-25.ki-solid:before {
  content: "\e918";
}

.ki-abstract-26.ki-solid:before {
  content: "\e919";
}

.ki-abstract-27.ki-solid:before {
  content: "\e91a";
}

.ki-abstract-28.ki-solid:before {
  content: "\e91b";
}

.ki-abstract-29.ki-solid:before {
  content: "\e91c";
}

.ki-abstract-30.ki-solid:before {
  content: "\e91d";
}

.ki-abstract-31.ki-solid:before {
  content: "\e91e";
}

.ki-abstract-32.ki-solid:before {
  content: "\e91f";
}

.ki-abstract-33.ki-solid:before {
  content: "\e920";
}

.ki-abstract-34.ki-solid:before {
  content: "\e921";
}

.ki-abstract-35.ki-solid:before {
  content: "\e922";
}

.ki-abstract-36.ki-solid:before {
  content: "\e923";
}

.ki-abstract-37.ki-solid:before {
  content: "\e924";
}

.ki-abstract-38.ki-solid:before {
  content: "\e925";
}

.ki-abstract-39.ki-solid:before {
  content: "\e926";
}

.ki-abstract-40.ki-solid:before {
  content: "\e927";
}

.ki-abstract-41.ki-solid:before {
  content: "\e928";
}

.ki-abstract-42.ki-solid:before {
  content: "\e929";
}

.ki-abstract-43.ki-solid:before {
  content: "\e92a";
}

.ki-abstract-44.ki-solid:before {
  content: "\e92b";
}

.ki-abstract-45.ki-solid:before {
  content: "\e92c";
}

.ki-abstract-46.ki-solid:before {
  content: "\e92d";
}

.ki-abstract-47.ki-solid:before {
  content: "\e92e";
}

.ki-abstract-48.ki-solid:before {
  content: "\e92f";
}

.ki-abstract-49.ki-solid:before {
  content: "\e930";
}

.ki-abstract.ki-solid:before {
  content: "\e931";
}

.ki-add-files.ki-solid:before {
  content: "\e932";
}

.ki-add-folder.ki-solid:before {
  content: "\e933";
}

.ki-add-item.ki-solid:before {
  content: "\e934";
}

.ki-add-notepad.ki-solid:before {
  content: "\e935";
}

.ki-address-book.ki-solid:before {
  content: "\e936";
}

.ki-airplane-square.ki-solid:before {
  content: "\e937";
}

.ki-airplane.ki-solid:before {
  content: "\e938";
}

.ki-airpod.ki-solid:before {
  content: "\e939";
}

.ki-android.ki-solid:before {
  content: "\e93a";
}

.ki-angular.ki-solid:before {
  content: "\e93b";
}

.ki-apple.ki-solid:before {
  content: "\e93c";
}

.ki-archive-tick.ki-solid:before {
  content: "\e93d";
}

.ki-archive.ki-solid:before {
  content: "\e93e";
}

.ki-arrow-circle-left.ki-solid:before {
  content: "\e93f";
}

.ki-arrow-circle-right.ki-solid:before {
  content: "\e940";
}

.ki-arrow-diagonal.ki-solid:before {
  content: "\e941";
}

.ki-arrow-down-left.ki-solid:before {
  content: "\e942";
}

.ki-arrow-down-refraction.ki-solid:before {
  content: "\e943";
}

.ki-arrow-down-right.ki-solid:before {
  content: "\e944";
}

.ki-arrow-down.ki-solid:before {
  content: "\e945";
}

.ki-arrow-left.ki-solid:before {
  content: "\e946";
}

.ki-arrow-mix.ki-solid:before {
  content: "\e947";
}

.ki-arrow-right-left.ki-solid:before {
  content: "\e948";
}

.ki-arrow-right.ki-solid:before {
  content: "\e949";
}

.ki-arrow-two-diagonals.ki-solid:before {
  content: "\e94a";
}

.ki-arrow-up-down.ki-solid:before {
  content: "\e94b";
}

.ki-arrow-up-left.ki-solid:before {
  content: "\e94c";
}

.ki-arrow-up-refraction.ki-solid:before {
  content: "\e94d";
}

.ki-arrow-up-right.ki-solid:before {
  content: "\e94e";
}

.ki-arrow-up.ki-solid:before {
  content: "\e94f";
}

.ki-arrow-zigzag.ki-solid:before {
  content: "\e950";
}

.ki-arrows-circle.ki-solid:before {
  content: "\e951";
}

.ki-arrows-loop.ki-solid:before {
  content: "\e952";
}

.ki-artificial-intelligence.ki-solid:before {
  content: "\e953";
}

.ki-auto-brightness.ki-solid:before {
  content: "\e954";
}

.ki-avalanche.ki-solid:before {
  content: "\e955";
}

.ki-award.ki-solid:before {
  content: "\e956";
}

.ki-badge.ki-solid:before {
  content: "\e957";
}

.ki-bandage.ki-solid:before {
  content: "\e958";
}

.ki-bank.ki-solid:before {
  content: "\e959";
}

.ki-barcode.ki-solid:before {
  content: "\e95a";
}

.ki-basket-ok.ki-solid:before {
  content: "\e95b";
}

.ki-basket.ki-solid:before {
  content: "\e95c";
}

.ki-behance.ki-solid:before {
  content: "\e95d";
}

.ki-bill.ki-solid:before {
  content: "\e95e";
}

.ki-binance-usd.ki-solid:before {
  content: "\e95f";
}

.ki-binance.ki-solid:before {
  content: "\e960";
}

.ki-bitcoin.ki-solid:before {
  content: "\e961";
}

.ki-black-down.ki-solid:before {
  content: "\e962";
}

.ki-black-left-line.ki-solid:before {
  content: "\e963";
}

.ki-black-left.ki-solid:before {
  content: "\e964";
}

.ki-black-right-line.ki-solid:before {
  content: "\e965";
}

.ki-black-right.ki-solid:before {
  content: "\e966";
}

.ki-black-up.ki-solid:before {
  content: "\e967";
}

.ki-bluetooth.ki-solid:before {
  content: "\e968";
}

.ki-book-open.ki-solid:before {
  content: "\e969";
}

.ki-book-square.ki-solid:before {
  content: "\e96a";
}

.ki-book.ki-solid:before {
  content: "\e96b";
}

.ki-bookmark-2.ki-solid:before {
  content: "\e96c";
}

.ki-bookmark.ki-solid:before {
  content: "\e96d";
}

.ki-bootstrap.ki-solid:before {
  content: "\e96e";
}

.ki-briefcase.ki-solid:before {
  content: "\e96f";
}

.ki-brifecase-cros.ki-solid:before {
  content: "\e970";
}

.ki-brifecase-tick.ki-solid:before {
  content: "\e971";
}

.ki-brifecase-timer.ki-solid:before {
  content: "\e972";
}

.ki-brush.ki-solid:before {
  content: "\e973";
}

.ki-bucket-square.ki-solid:before {
  content: "\e974";
}

.ki-bucket.ki-solid:before {
  content: "\e975";
}

.ki-burger-menu-1.ki-solid:before {
  content: "\e976";
}

.ki-burger-menu-2.ki-solid:before {
  content: "\e977";
}

.ki-burger-menu-3.ki-solid:before {
  content: "\e978";
}

.ki-burger-menu-4.ki-solid:before {
  content: "\e979";
}

.ki-burger-menu-5.ki-solid:before {
  content: "\e97a";
}

.ki-burger-menu-6.ki-solid:before {
  content: "\e97b";
}

.ki-burger-menu.ki-solid:before {
  content: "\e97c";
}

.ki-bus.ki-solid:before {
  content: "\e97d";
}

.ki-calculator.ki-solid:before {
  content: "\e97e";
}

.ki-calendar-2.ki-solid:before {
  content: "\e97f";
}

.ki-calendar-8.ki-solid:before {
  content: "\e980";
}

.ki-calendar-add.ki-solid:before {
  content: "\e981";
}

.ki-calendar-edit.ki-solid:before {
  content: "\e982";
}

.ki-calendar-remove.ki-solid:before {
  content: "\e983";
}

.ki-calendar-search.ki-solid:before {
  content: "\e984";
}

.ki-calendar-tick.ki-solid:before {
  content: "\e985";
}

.ki-calendar.ki-solid:before {
  content: "\e986";
}

.ki-call.ki-solid:before {
  content: "\e987";
}

.ki-capsule.ki-solid:before {
  content: "\e988";
}

.ki-car-2.ki-solid:before {
  content: "\e989";
}

.ki-car-3.ki-solid:before {
  content: "\e98a";
}

.ki-car.ki-solid:before {
  content: "\e98b";
}

.ki-category.ki-solid:before {
  content: "\e98c";
}

.ki-cd.ki-solid:before {
  content: "\e98d";
}

.ki-celsius.ki-solid:before {
  content: "\e98e";
}

.ki-chart-line-down-2.ki-solid:before {
  content: "\e98f";
}

.ki-chart-line-down.ki-solid:before {
  content: "\e990";
}

.ki-chart-line-star.ki-solid:before {
  content: "\e991";
}

.ki-chart-line-up-2.ki-solid:before {
  content: "\e992";
}

.ki-chart-line-up.ki-solid:before {
  content: "\e993";
}

.ki-chart-line.ki-solid:before {
  content: "\e994";
}

.ki-chart-pie-3.ki-solid:before {
  content: "\e995";
}

.ki-chart-pie-4.ki-solid:before {
  content: "\e996";
}

.ki-chart-pie-simple.ki-solid:before {
  content: "\e997";
}

.ki-chart-pie-too.ki-solid:before {
  content: "\e998";
}

.ki-chart-simple-2.ki-solid:before {
  content: "\e999";
}

.ki-chart-simple-3.ki-solid:before {
  content: "\e99a";
}

.ki-chart-simple.ki-solid:before {
  content: "\e99b";
}

.ki-chart.ki-solid:before {
  content: "\e99c";
}

.ki-check-circle.ki-solid:before {
  content: "\e99d";
}

.ki-check-square.ki-solid:before {
  content: "\e99e";
}

.ki-check.ki-solid:before {
  content: "\e99f";
}

.ki-cheque.ki-solid:before {
  content: "\e9a0";
}

.ki-chrome.ki-solid:before {
  content: "\e9a1";
}

.ki-classmates.ki-solid:before {
  content: "\e9a2";
}

.ki-click.ki-solid:before {
  content: "\e9a3";
}

.ki-clipboard.ki-solid:before {
  content: "\e9a4";
}

.ki-cloud-add.ki-solid:before {
  content: "\e9a5";
}

.ki-cloud-change.ki-solid:before {
  content: "\e9a6";
}

.ki-cloud-download.ki-solid:before {
  content: "\e9a7";
}

.ki-cloud.ki-solid:before {
  content: "\e9a8";
}

.ki-code.ki-solid:before {
  content: "\e9a9";
}

.ki-coffee.ki-solid:before {
  content: "\e9aa";
}

.ki-color-swatch.ki-solid:before {
  content: "\e9ab";
}

.ki-colors-square.ki-solid:before {
  content: "\e9ac";
}

.ki-compass.ki-solid:before {
  content: "\e9ad";
}

.ki-copy-success.ki-solid:before {
  content: "\e9ae";
}

.ki-copy.ki-solid:before {
  content: "\e9af";
}

.ki-courier-express.ki-solid:before {
  content: "\e9b0";
}

.ki-courier.ki-solid:before {
  content: "\e9b1";
}

.ki-credit-cart.ki-solid:before {
  content: "\e9b2";
}

.ki-cross-circle.ki-solid:before {
  content: "\e9b3";
}

.ki-cross-square.ki-solid:before {
  content: "\e9b4";
}

.ki-cross.ki-solid:before {
  content: "\e9b5";
}

.ki-crown-2.ki-solid:before {
  content: "\e9b6";
}

.ki-crown.ki-solid:before {
  content: "\e9b7";
}

.ki-css.ki-solid:before {
  content: "\e9b8";
}

.ki-cube-2.ki-solid:before {
  content: "\e9b9";
}

.ki-cube-3.ki-solid:before {
  content: "\e9ba";
}

.ki-cup.ki-solid:before {
  content: "\e9bb";
}

.ki-dash.ki-solid:before {
  content: "\e9bc";
}

.ki-data.ki-solid:before {
  content: "\e9bd";
}

.ki-delete-files.ki-solid:before {
  content: "\e9be";
}

.ki-delete-folder.ki-solid:before {
  content: "\e9bf";
}

.ki-delivery-2.ki-solid:before {
  content: "\e9c0";
}

.ki-delivery-3.ki-solid:before {
  content: "\e9c1";
}

.ki-delivery-24.ki-solid:before {
  content: "\e9c2";
}

.ki-delivery-door.ki-solid:before {
  content: "\e9c3";
}

.ki-delivery-geolocation.ki-solid:before {
  content: "\e9c4";
}

.ki-delivery-time.ki-solid:before {
  content: "\e9c5";
}

.ki-delivery.ki-solid:before {
  content: "\e9c6";
}

.ki-design-2.ki-solid:before {
  content: "\e9c7";
}

.ki-design-frame.ki-solid:before {
  content: "\e9c8";
}

.ki-design-mask.ki-solid:before {
  content: "\e9c9";
}

.ki-design.ki-solid:before {
  content: "\e9ca";
}

.ki-devices-2.ki-solid:before {
  content: "\e9cb";
}

.ki-devices.ki-solid:before {
  content: "\e9cc";
}

.ki-diamonds.ki-solid:before {
  content: "\e9cd";
}

.ki-directbox-default.ki-solid:before {
  content: "\e9ce";
}

.ki-disconnect.ki-solid:before {
  content: "\e9cf";
}

.ki-discount.ki-solid:before {
  content: "\e9d0";
}

.ki-disk.ki-solid:before {
  content: "\e9d1";
}

.ki-dislike.ki-solid:before {
  content: "\e9d2";
}

.ki-dj.ki-solid:before {
  content: "\e9d3";
}

.ki-document.ki-solid:before {
  content: "\e9d4";
}

.ki-dollar.ki-solid:before {
  content: "\e9d5";
}

.ki-dots-circle-vertical.ki-solid:before {
  content: "\e9d6";
}

.ki-dots-circle.ki-solid:before {
  content: "\e9d7";
}

.ki-dots-horizontal.ki-solid:before {
  content: "\e9d8";
}

.ki-dots-square-vertical.ki-solid:before {
  content: "\e9d9";
}

.ki-dots-square.ki-solid:before {
  content: "\e9da";
}

.ki-dots-vertical.ki-solid:before {
  content: "\e9db";
}

.ki-double-check-circle.ki-solid:before {
  content: "\e9dc";
}

.ki-double-check.ki-solid:before {
  content: "\e9dd";
}

.ki-double-down.ki-solid:before {
  content: "\e9de";
}

.ki-double-left-arrow.ki-solid:before {
  content: "\e9df";
}

.ki-double-left.ki-solid:before {
  content: "\e9e0";
}

.ki-double-right-arrow.ki-solid:before {
  content: "\e9e1";
}

.ki-double-right.ki-solid:before {
  content: "\e9e2";
}

.ki-double-up.ki-solid:before {
  content: "\e9e3";
}

.ki-down-square.ki-solid:before {
  content: "\e9e4";
}

.ki-down.ki-solid:before {
  content: "\e9e5";
}

.ki-dribbble.ki-solid:before {
  content: "\e9e6";
}

.ki-drop.ki-solid:before {
  content: "\e9e7";
}

.ki-dropbox.ki-solid:before {
  content: "\e9e8";
}

.ki-educare.ki-solid:before {
  content: "\e9e9";
}

.ki-electricity.ki-solid:before {
  content: "\e9ea";
}

.ki-electronic-clock.ki-solid:before {
  content: "\e9eb";
}

.ki-element-1.ki-solid:before {
  content: "\e9ec";
}

.ki-element-2.ki-solid:before {
  content: "\e9ed";
}

.ki-element-3.ki-solid:before {
  content: "\e9ee";
}

.ki-element-4.ki-solid:before {
  content: "\e9ef";
}

.ki-element-5.ki-solid:before {
  content: "\e9f0";
}

.ki-element-6.ki-solid:before {
  content: "\e9f1";
}

.ki-element-7.ki-solid:before {
  content: "\e9f2";
}

.ki-element-8.ki-solid:before {
  content: "\e9f3";
}

.ki-element-9.ki-solid:before {
  content: "\e9f4";
}

.ki-element-10.ki-solid:before {
  content: "\e9f5";
}

.ki-element-11.ki-solid:before {
  content: "\e9f6";
}

.ki-element-12.ki-solid:before {
  content: "\e9f7";
}

.ki-element-equal.ki-solid:before {
  content: "\e9f8";
}

.ki-element-plus.ki-solid:before {
  content: "\e9f9";
}

.ki-emoji-happy.ki-solid:before {
  content: "\e9fa";
}

.ki-enjin-coin.ki-solid:before {
  content: "\e9fb";
}

.ki-entrance-left.ki-solid:before {
  content: "\e9fc";
}

.ki-entrance-right.ki-solid:before {
  content: "\e9fd";
}

.ki-eraser.ki-solid:before {
  content: "\e9fe";
}

.ki-euro.ki-solid:before {
  content: "\e9ff";
}

.ki-exit-down.ki-solid:before {
  content: "\ea00";
}

.ki-exit-left.ki-solid:before {
  content: "\ea01";
}

.ki-exit-right-corner.ki-solid:before {
  content: "\ea02";
}

.ki-exit-right.ki-solid:before {
  content: "\ea03";
}

.ki-exit-up.ki-solid:before {
  content: "\ea04";
}

.ki-external-drive.ki-solid:before {
  content: "\ea05";
}

.ki-eye-slash.ki-solid:before {
  content: "\ea06";
}

.ki-eye.ki-solid:before {
  content: "\ea07";
}

.ki-facebook.ki-solid:before {
  content: "\ea08";
}

.ki-faceid.ki-solid:before {
  content: "\ea09";
}

.ki-fasten.ki-solid:before {
  content: "\ea0a";
}

.ki-fat-rows.ki-solid:before {
  content: "\ea0b";
}

.ki-feather.ki-solid:before {
  content: "\ea0c";
}

.ki-figma.ki-solid:before {
  content: "\ea0d";
}

.ki-file-added.ki-solid:before {
  content: "\ea0e";
}

.ki-file-deleted.ki-solid:before {
  content: "\ea0f";
}

.ki-file-down.ki-solid:before {
  content: "\ea10";
}

.ki-file-left.ki-solid:before {
  content: "\ea11";
}

.ki-file-right.ki-solid:before {
  content: "\ea12";
}

.ki-file-sheet.ki-solid:before {
  content: "\ea13";
}

.ki-file-up.ki-solid:before {
  content: "\ea14";
}

.ki-file.ki-solid:before {
  content: "\ea15";
}

.ki-files-tablet.ki-solid:before {
  content: "\ea16";
}

.ki-filter-edit.ki-solid:before {
  content: "\ea17";
}

.ki-filter-search.ki-solid:before {
  content: "\ea18";
}

.ki-filter-square.ki-solid:before {
  content: "\ea19";
}

.ki-filter-tablet.ki-solid:before {
  content: "\ea1a";
}

.ki-filter-tick.ki-solid:before {
  content: "\ea1b";
}

.ki-filter.ki-solid:before {
  content: "\ea1c";
}

.ki-finance-calculator.ki-solid:before {
  content: "\ea1d";
}

.ki-financial-schedule.ki-solid:before {
  content: "\ea1e";
}

.ki-fingerprint-scanning.ki-solid:before {
  content: "\ea1f";
}

.ki-flag.ki-solid:before {
  content: "\ea20";
}

.ki-flash-circle.ki-solid:before {
  content: "\ea21";
}

.ki-flask.ki-solid:before {
  content: "\ea22";
}

.ki-focus.ki-solid:before {
  content: "\ea23";
}

.ki-folder-added.ki-solid:before {
  content: "\ea24";
}

.ki-folder-down.ki-solid:before {
  content: "\ea25";
}

.ki-folder-up.ki-solid:before {
  content: "\ea26";
}

.ki-folder.ki-solid:before {
  content: "\ea27";
}

.ki-frame.ki-solid:before {
  content: "\ea28";
}

.ki-gear.ki-solid:before {
  content: "\ea29";
}

.ki-general-mouse.ki-solid:before {
  content: "\ea2a";
}

.ki-geolocation-home.ki-solid:before {
  content: "\ea2b";
}

.ki-geolocation.ki-solid:before {
  content: "\ea2c";
}

.ki-ghost.ki-solid:before {
  content: "\ea2d";
}

.ki-gift.ki-solid:before {
  content: "\ea2e";
}

.ki-github.ki-solid:before {
  content: "\ea2f";
}

.ki-glass.ki-solid:before {
  content: "\ea30";
}

.ki-google-play.ki-solid:before {
  content: "\ea31";
}

.ki-google.ki-solid:before {
  content: "\ea32";
}

.ki-graph-2.ki-solid:before {
  content: "\ea33";
}

.ki-graph-3.ki-solid:before {
  content: "\ea34";
}

.ki-graph-4.ki-solid:before {
  content: "\ea35";
}

.ki-graph-up.ki-solid:before {
  content: "\ea36";
}

.ki-graph.ki-solid:before {
  content: "\ea37";
}

.ki-grid-2.ki-solid:before {
  content: "\ea38";
}

.ki-grid-frame.ki-solid:before {
  content: "\ea39";
}

.ki-grid.ki-solid:before {
  content: "\ea3a";
}

.ki-handcart.ki-solid:before {
  content: "\ea3b";
}

.ki-happy-emoji.ki-solid:before {
  content: "\ea3c";
}

.ki-heart-circle.ki-solid:before {
  content: "\ea3d";
}

.ki-heart.ki-solid:before {
  content: "\ea3e";
}

.ki-home-1.ki-solid:before {
  content: "\ea3f";
}

.ki-home-2.ki-solid:before {
  content: "\ea40";
}

.ki-home-3.ki-solid:before {
  content: "\ea41";
}

.ki-home.ki-solid:before {
  content: "\ea42";
}

.ki-html.ki-solid:before {
  content: "\ea43";
}

.ki-icon.ki-solid:before {
  content: "\ea44";
}

.ki-illustrator.ki-solid:before {
  content: "\ea45";
}

.ki-information-2.ki-solid:before {
  content: "\ea46";
}

.ki-information-3.ki-solid:before {
  content: "\ea47";
}

.ki-information-4.ki-solid:before {
  content: "\ea48";
}

.ki-information-5.ki-solid:before {
  content: "\ea49";
}

.ki-information.ki-solid:before {
  content: "\ea4a";
}

.ki-instagram.ki-solid:before {
  content: "\ea4b";
}

.ki-joystick.ki-solid:before {
  content: "\ea4c";
}

.ki-js-2.ki-solid:before {
  content: "\ea4d";
}

.ki-js.ki-solid:before {
  content: "\ea4e";
}

.ki-kanban.ki-solid:before {
  content: "\ea4f";
}

.ki-key-square.ki-solid:before {
  content: "\ea50";
}

.ki-key.ki-solid:before {
  content: "\ea51";
}

.ki-keyboard.ki-solid:before {
  content: "\ea52";
}

.ki-laptop.ki-solid:before {
  content: "\ea53";
}

.ki-laravel.ki-solid:before {
  content: "\ea54";
}

.ki-left-square.ki-solid:before {
  content: "\ea55";
}

.ki-left.ki-solid:before {
  content: "\ea56";
}

.ki-like-2.ki-solid:before {
  content: "\ea57";
}

.ki-like-folder.ki-solid:before {
  content: "\ea58";
}

.ki-like-shapes.ki-solid:before {
  content: "\ea59";
}

.ki-like-tag.ki-solid:before {
  content: "\ea5a";
}

.ki-like.ki-solid:before {
  content: "\ea5b";
}

.ki-loading.ki-solid:before {
  content: "\ea5c";
}

.ki-lock-2.ki-solid:before {
  content: "\ea5d";
}

.ki-lock-3.ki-solid:before {
  content: "\ea5e";
}

.ki-lock.ki-solid:before {
  content: "\ea5f";
}

.ki-logistic.ki-solid:before {
  content: "\ea60";
}

.ki-lots-shopping.ki-solid:before {
  content: "\ea61";
}

.ki-lovely.ki-solid:before {
  content: "\ea62";
}

.ki-lts.ki-solid:before {
  content: "\ea63";
}

.ki-magnifier.ki-solid:before {
  content: "\ea64";
}

.ki-map.ki-solid:before {
  content: "\ea65";
}

.ki-mask.ki-solid:before {
  content: "\ea66";
}

.ki-maximize.ki-solid:before {
  content: "\ea67";
}

.ki-medal-star.ki-solid:before {
  content: "\ea68";
}

.ki-menu.ki-solid:before {
  content: "\ea69";
}

.ki-message-add.ki-solid:before {
  content: "\ea6a";
}

.ki-message-edit.ki-solid:before {
  content: "\ea6b";
}

.ki-message-minus.ki-solid:before {
  content: "\ea6c";
}

.ki-message-notif.ki-solid:before {
  content: "\ea6d";
}

.ki-message-programming.ki-solid:before {
  content: "\ea6e";
}

.ki-message-question.ki-solid:before {
  content: "\ea6f";
}

.ki-message-text-2.ki-solid:before {
  content: "\ea70";
}

.ki-message-text.ki-solid:before {
  content: "\ea71";
}

.ki-messages.ki-solid:before {
  content: "\ea72";
}

.ki-microsoft.ki-solid:before {
  content: "\ea73";
}

.ki-milk.ki-solid:before {
  content: "\ea74";
}

.ki-minus-circle.ki-solid:before {
  content: "\ea75";
}

.ki-minus-folder.ki-solid:before {
  content: "\ea76";
}

.ki-minus-square.ki-solid:before {
  content: "\ea77";
}

.ki-minus.ki-solid:before {
  content: "\ea78";
}

.ki-monitor-mobile.ki-solid:before {
  content: "\ea79";
}

.ki-moon.ki-solid:before {
  content: "\ea7a";
}

.ki-more-2.ki-solid:before {
  content: "\ea7b";
}

.ki-mouse-circle.ki-solid:before {
  content: "\ea7c";
}

.ki-mouse-square.ki-solid:before {
  content: "\ea7d";
}

.ki-mouse.ki-solid:before {
  content: "\ea7e";
}

.ki-nexo.ki-solid:before {
  content: "\ea7f";
}

.ki-night-day.ki-solid:before {
  content: "\ea80";
}

.ki-note-2.ki-solid:before {
  content: "\ea81";
}

.ki-note.ki-solid:before {
  content: "\ea82";
}

.ki-notepad-bookmark.ki-solid:before {
  content: "\ea83";
}

.ki-notepad-edit.ki-solid:before {
  content: "\ea84";
}

.ki-notepad.ki-solid:before {
  content: "\ea85";
}

.ki-notification-2.ki-solid:before {
  content: "\ea86";
}

.ki-notification-bing.ki-solid:before {
  content: "\ea87";
}

.ki-notification-circle.ki-solid:before {
  content: "\ea88";
}

.ki-notification-favorite.ki-solid:before {
  content: "\ea89";
}

.ki-notification-on.ki-solid:before {
  content: "\ea8a";
}

.ki-notification-status.ki-solid:before {
  content: "\ea8b";
}

.ki-notification.ki-solid:before {
  content: "\ea8c";
}

.ki-ocean.ki-solid:before {
  content: "\ea8d";
}

.ki-office-bag.ki-solid:before {
  content: "\ea8e";
}

.ki-package.ki-solid:before {
  content: "\ea8f";
}

.ki-pails.ki-solid:before {
  content: "\ea90";
}

.ki-paintbucket.ki-solid:before {
  content: "\ea91";
}

.ki-paper-clip.ki-solid:before {
  content: "\ea92";
}

.ki-parcel-tracking.ki-solid:before {
  content: "\ea93";
}

.ki-parcel.ki-solid:before {
  content: "\ea94";
}

.ki-password-check.ki-solid:before {
  content: "\ea95";
}

.ki-paypal.ki-solid:before {
  content: "\ea96";
}

.ki-pencil.ki-solid:before {
  content: "\ea97";
}

.ki-people.ki-solid:before {
  content: "\ea98";
}

.ki-percentage.ki-solid:before {
  content: "\ea99";
}

.ki-phone.ki-solid:before {
  content: "\ea9a";
}

.ki-photoshop.ki-solid:before {
  content: "\ea9b";
}

.ki-picture.ki-solid:before {
  content: "\ea9c";
}

.ki-pill.ki-solid:before {
  content: "\ea9d";
}

.ki-pin.ki-solid:before {
  content: "\ea9e";
}

.ki-plus-circle.ki-solid:before {
  content: "\ea9f";
}

.ki-plus-square.ki-solid:before {
  content: "\eaa0";
}

.ki-plus.ki-solid:before {
  content: "\eaa1";
}

.ki-pointers.ki-solid:before {
  content: "\eaa2";
}

.ki-price-tag.ki-solid:before {
  content: "\eaa3";
}

.ki-printer.ki-solid:before {
  content: "\eaa4";
}

.ki-profile-circle.ki-solid:before {
  content: "\eaa5";
}

.ki-profile-user.ki-solid:before {
  content: "\eaa6";
}

.ki-pulse.ki-solid:before {
  content: "\eaa7";
}

.ki-purchase.ki-solid:before {
  content: "\eaa8";
}

.ki-python.ki-solid:before {
  content: "\eaa9";
}

.ki-question-2.ki-solid:before {
  content: "\eaaa";
}

.ki-question.ki-solid:before {
  content: "\eaab";
}

.ki-questionnaire-tablet.ki-solid:before {
  content: "\eaac";
}

.ki-ranking.ki-solid:before {
  content: "\eaad";
}

.ki-react.ki-solid:before {
  content: "\eaae";
}

.ki-receipt-square.ki-solid:before {
  content: "\eaaf";
}

.ki-rescue.ki-solid:before {
  content: "\eab0";
}

.ki-right-left.ki-solid:before {
  content: "\eab1";
}

.ki-right-square.ki-solid:before {
  content: "\eab2";
}

.ki-right.ki-solid:before {
  content: "\eab3";
}

.ki-rocket.ki-solid:before {
  content: "\eab4";
}

.ki-route.ki-solid:before {
  content: "\eab5";
}

.ki-router.ki-solid:before {
  content: "\eab6";
}

.ki-row-horizontal.ki-solid:before {
  content: "\eab7";
}

.ki-row-vertical.ki-solid:before {
  content: "\eab8";
}

.ki-safe-home.ki-solid:before {
  content: "\eab9";
}

.ki-satellite.ki-solid:before {
  content: "\eaba";
}

.ki-save-2.ki-solid:before {
  content: "\eabb";
}

.ki-save-deposit.ki-solid:before {
  content: "\eabc";
}

.ki-scan-barcode.ki-solid:before {
  content: "\eabd";
}

.ki-scooter-2.ki-solid:before {
  content: "\eabe";
}

.ki-scooter.ki-solid:before {
  content: "\eabf";
}

.ki-screen.ki-solid:before {
  content: "\eac0";
}

.ki-scroll.ki-solid:before {
  content: "\eac1";
}

.ki-search-list.ki-solid:before {
  content: "\eac2";
}

.ki-security-check.ki-solid:before {
  content: "\eac3";
}

.ki-security-user.ki-solid:before {
  content: "\eac4";
}

.ki-send.ki-solid:before {
  content: "\eac5";
}

.ki-setting-2.ki-solid:before {
  content: "\eac6";
}

.ki-setting-3.ki-solid:before {
  content: "\eac7";
}

.ki-setting-4.ki-solid:before {
  content: "\eac8";
}

.ki-setting.ki-solid:before {
  content: "\eac9";
}

.ki-share.ki-solid:before {
  content: "\eaca";
}

.ki-shield-cross.ki-solid:before {
  content: "\eacb";
}

.ki-shield-search.ki-solid:before {
  content: "\eacc";
}

.ki-shield-slash.ki-solid:before {
  content: "\eacd";
}

.ki-shield-tick.ki-solid:before {
  content: "\eace";
}

.ki-shield.ki-solid:before {
  content: "\eacf";
}

.ki-ship.ki-solid:before {
  content: "\ead0";
}

.ki-shop.ki-solid:before {
  content: "\ead1";
}

.ki-simcard-2.ki-solid:before {
  content: "\ead2";
}

.ki-simcard.ki-solid:before {
  content: "\ead3";
}

.ki-size.ki-solid:before {
  content: "\ead4";
}

.ki-slack.ki-solid:before {
  content: "\ead5";
}

.ki-slider-horizontal-2.ki-solid:before {
  content: "\ead6";
}

.ki-slider-horizontal.ki-solid:before {
  content: "\ead7";
}

.ki-slider-vertical-2.ki-solid:before {
  content: "\ead8";
}

.ki-slider-vertical.ki-solid:before {
  content: "\ead9";
}

.ki-slider.ki-solid:before {
  content: "\eada";
}

.ki-sms.ki-solid:before {
  content: "\eadb";
}

.ki-snapchat.ki-solid:before {
  content: "\eadc";
}

.ki-social-media.ki-solid:before {
  content: "\eadd";
}

.ki-soft-2.ki-solid:before {
  content: "\eade";
}

.ki-soft-3.ki-solid:before {
  content: "\eadf";
}

.ki-soft.ki-solid:before {
  content: "\eae0";
}

.ki-some-files.ki-solid:before {
  content: "\eae1";
}

.ki-sort.ki-solid:before {
  content: "\eae2";
}

.ki-speaker.ki-solid:before {
  content: "\eae3";
}

.ki-spotify.ki-solid:before {
  content: "\eae4";
}

.ki-spring-framework.ki-solid:before {
  content: "\eae5";
}

.ki-square-brackets.ki-solid:before {
  content: "\eae6";
}

.ki-star.ki-solid:before {
  content: "\eae7";
}

.ki-status.ki-solid:before {
  content: "\eae8";
}

.ki-subtitle.ki-solid:before {
  content: "\eae9";
}

.ki-sun.ki-solid:before {
  content: "\eaea";
}

.ki-support-24.ki-solid:before {
  content: "\eaeb";
}

.ki-switch.ki-solid:before {
  content: "\eaec";
}

.ki-syringe.ki-solid:before {
  content: "\eaed";
}

.ki-tablet-book.ki-solid:before {
  content: "\eaee";
}

.ki-tablet-delete.ki-solid:before {
  content: "\eaef";
}

.ki-tablet-down.ki-solid:before {
  content: "\eaf0";
}

.ki-tablet-ok.ki-solid:before {
  content: "\eaf1";
}

.ki-tablet-text-down.ki-solid:before {
  content: "\eaf2";
}

.ki-tablet-text-up.ki-solid:before {
  content: "\eaf3";
}

.ki-tablet-up.ki-solid:before {
  content: "\eaf4";
}

.ki-tablet.ki-solid:before {
  content: "\eaf5";
}

.ki-tag-cross.ki-solid:before {
  content: "\eaf6";
}

.ki-tag.ki-solid:before {
  content: "\eaf7";
}

.ki-teacher.ki-solid:before {
  content: "\eaf8";
}

.ki-tech-wifi.ki-solid:before {
  content: "\eaf9";
}

.ki-technology-2.ki-solid:before {
  content: "\eafa";
}

.ki-technology-3.ki-solid:before {
  content: "\eafb";
}

.ki-technology-4.ki-solid:before {
  content: "\eafc";
}

.ki-technology.ki-solid:before {
  content: "\eafd";
}

.ki-telephone-geolocation.ki-solid:before {
  content: "\eafe";
}

.ki-test-tubes.ki-solid:before {
  content: "\eaff";
}

.ki-text-align-center.ki-solid:before {
  content: "\eb00";
}

.ki-text-align-justify-center.ki-solid:before {
  content: "\eb01";
}

.ki-text-align-left.ki-solid:before {
  content: "\eb02";
}

.ki-text-align-right.ki-solid:before {
  content: "\eb03";
}

.ki-text-bold.ki-solid:before {
  content: "\eb04";
}

.ki-text-circle.ki-solid:before {
  content: "\eb05";
}

.ki-text-italic.ki-solid:before {
  content: "\eb06";
}

.ki-text-number.ki-solid:before {
  content: "\eb07";
}

.ki-text-strikethrough.ki-solid:before {
  content: "\eb08";
}

.ki-text-underline.ki-solid:before {
  content: "\eb09";
}

.ki-text.ki-solid:before {
  content: "\eb0a";
}

.ki-thermometer.ki-solid:before {
  content: "\eb0b";
}

.ki-theta.ki-solid:before {
  content: "\eb0c";
}

.ki-tiktok.ki-solid:before {
  content: "\eb0d";
}

.ki-time.ki-solid:before {
  content: "\eb0e";
}

.ki-timer.ki-solid:before {
  content: "\eb0f";
}

.ki-to-left.ki-solid:before {
  content: "\eb10";
}

.ki-to-right.ki-solid:before {
  content: "\eb11";
}

.ki-toggle-off-circle.ki-solid:before {
  content: "\eb12";
}

.ki-toggle-off.ki-solid:before {
  content: "\eb13";
}

.ki-toggle-on-circle.ki-solid:before {
  content: "\eb14";
}

.ki-toggle-on.ki-solid:before {
  content: "\eb15";
}

.ki-trailer.ki-solid:before {
  content: "\eb16";
}

.ki-trash-square.ki-solid:before {
  content: "\eb17";
}

.ki-trash.ki-solid:before {
  content: "\eb18";
}

.ki-tree.ki-solid:before {
  content: "\eb19";
}

.ki-trello.ki-solid:before {
  content: "\eb1a";
}

.ki-triangle.ki-solid:before {
  content: "\eb1b";
}

.ki-truck.ki-solid:before {
  content: "\eb1c";
}

.ki-ts.ki-solid:before {
  content: "\eb1d";
}

.ki-twitch.ki-solid:before {
  content: "\eb1e";
}

.ki-twitter.ki-solid:before {
  content: "\eb1f";
}

.ki-two-credit-cart.ki-solid:before {
  content: "\eb20";
}

.ki-underlining.ki-solid:before {
  content: "\eb21";
}

.ki-up-down.ki-solid:before {
  content: "\eb22";
}

.ki-up-square.ki-solid:before {
  content: "\eb23";
}

.ki-up.ki-solid:before {
  content: "\eb24";
}

.ki-update-file.ki-solid:before {
  content: "\eb25";
}

.ki-update-folder.ki-solid:before {
  content: "\eb26";
}

.ki-user-edit.ki-solid:before {
  content: "\eb27";
}

.ki-user-square.ki-solid:before {
  content: "\eb28";
}

.ki-user-tick.ki-solid:before {
  content: "\eb29";
}

.ki-user.ki-solid:before {
  content: "\eb2a";
}

.ki-verify.ki-solid:before {
  content: "\eb2b";
}

.ki-vibe.ki-solid:before {
  content: "\eb2c";
}

.ki-virus.ki-solid:before {
  content: "\eb2d";
}

.ki-vue.ki-solid:before {
  content: "\eb2e";
}

.ki-vuesax.ki-solid:before {
  content: "\eb2f";
}

.ki-wallet.ki-solid:before {
  content: "\eb30";
}

.ki-wanchain.ki-solid:before {
  content: "\eb31";
}

.ki-watch.ki-solid:before {
  content: "\eb32";
}

.ki-whatsapp.ki-solid:before {
  content: "\eb33";
}

.ki-wifi-home.ki-solid:before {
  content: "\eb34";
}

.ki-wifi-square.ki-solid:before {
  content: "\eb35";
}

.ki-wifi.ki-solid:before {
  content: "\eb36";
}

.ki-wrench.ki-solid:before {
  content: "\eb37";
}

.ki-xaomi.ki-solid:before {
  content: "\eb38";
}

.ki-xd.ki-solid:before {
  content: "\eb39";
}

.ki-xmr.ki-solid:before {
  content: "\eb3a";
}

.ki-yii.ki-solid:before {
  content: "\eb3b";
}

.ki-youtube.ki-solid:before {
  content: "\eb3c";
}

/**** ERRORS/ALERTS START *****/
.asterisk, .error {
  color: #B94A48;
}

tr.alert td {
  color: #B94A48;
}

.alert {
  padding: 16px;
  margin-top: 8px;
  margin-bottom: 8px;
  border-radius: 12px;
  border: 1px solid transparent;
}
.alert.alert-danger {
  border: 1px solid #db351e;
  background: #f2e2e4;
  color: #db351e;
}
.alert.alert-warning {
  color: #8a6d3b;
  background-color: #fcf8e3;
  border-color: #faebcc;
}
.alert.alert-success {
  color: var(--gsd-primary-color);
  background-color: var(--gsd-primary-color-100);
  border-color: var(--gsd-primary-color);
  font-weight: 500;
}
.alert .alert-link, .alert a {
  font-weight: 700;
}

.content-block .alert {
  margin-left: 12px;
  margin-right: 12px;
}

.alert-dismissable, .alert-dismissible {
  padding-right: 35px;
}

.alert-danger .alert-link {
  color: #843534;
}

.alert-warning .alert-link {
  color: #66512c;
}

.alert-success .alert-link {
  color: #245269;
}

.alert .fa {
  font-size: 1.6rem;
  vertical-align: top;
  padding-right: 5px;
}

div.errorbox ul,
div.warningbox ul,
div.alert ul {
  padding: 0 20px;
  margin: 5px 0 0 0;
  list-style-type: disc;
}

.messagered {
  padding: 16px;
  margin: 5px 0;
  background-color: #F2E2E4;
  border: 1px solid #DB351E;
  border-radius: 12px;
  color: #DB351E;
}

.message {
  padding: 16px;
  margin: 5px 0;
  background-color: #E6F1F6;
  border: 1px solid #BCE8F1;
  border-radius: 12px;
  color: #29235C;
}

.qtipa.fa_info.fa.fa-info-circle, .qtipa.fa_alert.fa.fa-exclamation-circle, .fa.fa_help {
  font-size: 17px;
  text-decoration: none;
}

.qtipa.fa_alert.fa.fa-exclamation-circle {
  color: #e43331;
}

.qtip-def {
  border: 1px solid #D2D2D2;
  color: #000;
  font-size: 12px;
  background-color: white;
  border-radius: 5px 5px;
}

.qtip-def .qtip-titlebar {
  background-color: var(--gsd-primary-color);
  color: white;
  border-radius: 5px 5px 0 0;
}

.qtip {
  line-height: inherit;
}

#tooltip {
  border-radius: 4px;
  background: hsl(240, 15%, 18%);
  color: white;
  display: none;
  z-index: 100000;
  width: auto;
  max-width: 600px;
}
#tooltip a {
  color: white;
}

#tooltip-title {
  color: white;
  padding: 10px 15px;
  font-size: 1.4rem;
  font-weight: bold;
  border-bottom: 1px solid hsl(240, 15%, 38%);
}

#tooltip-content {
  font-size: 1.4rem;
  padding: 10px 15px;
}

#tooltip-arrow,
#tooltip-arrow::before {
  position: absolute;
  width: 12px;
  height: 12px;
  background: var(--gray-800);
  z-index: -1;
}

#tooltip-arrow {
  visibility: hidden;
}

#tooltip-arrow::before {
  visibility: visible;
  content: "";
  transform: rotate(45deg);
}

#tooltip[data-popper-placement^=top] > #tooltip-arrow {
  bottom: -6px;
}

#tooltip[data-popper-placement^=bottom] > #tooltip-arrow {
  top: -6px;
}

#tooltip[data-popper-placement^=left] > #tooltip-arrow {
  right: -6px;
}

#tooltip[data-popper-placement^=right] > #tooltip-arrow {
  left: -6px;
}

/*** BASE ***/
input:focus, textarea:focus, select:focus, button:focus {
  outline: none;
}

input.disabled, input.readonly, input[disabled], input[readonly], select.disabled, select.readonly, select[disabled], select[readonly], textarea.disabled, textarea.readonly, textarea[disabled], textarea[readonly] {
  background-color: var(--gray-100);
  border: 1px solid var(--gray-400);
  color: var(--gray-900);
  opacity: 0.7;
}

::-moz-placeholder, [disabled]::-moz-placeholder, [readonly]::-moz-placeholder {
  color: rgb(170, 170, 170);
}

::placeholder, [disabled]::placeholder, [readonly]::placeholder {
  color: rgb(170, 170, 170);
}

input[type=button][disabled], input[type=submit][disabled], input[type=button].disabled, input[type=submit].disabled {
  color: var(--gray-900);
}

input, select, textarea, .content-block .dataTables_wrapper .dataTables_length select {
  font-family: inherit;
  font-size: 1.4rem;
  border-radius: 6px;
  border: 1px solid var(--gray-200);
  padding: 12px 16px;
}

textarea {
  width: 30rem;
}

input[type=text], input[type=password] {
  width: 30rem;
}

label input[type=checkbox] {
  vertical-align: middle;
}

input[type=checkbox], input[type=radio] {
  margin: 3px 3px 3px 4px;
  height: auto;
}

input[type=text]:focus, input[type=password]:focus, input[type=date]:focus, input[type=datetime]:focus, input[type=time]:focus, input[type=number]:focus, textarea:focus {
  border-color: var(--gray-400);
}

select {
  padding: 0.6rem 1rem;
}

option:disabled {
  color: #0091BD;
}

/**** SPECIFIC STYLING *****/
.inputerror {
  transition: all 0.3s ease-out;
  box-shadow: 0 2px 4px -2px #DE0000;
  border: 1px solid #DE0000;
}

.inputerror:focus {
  box-shadow: 0 4px 8px -4px #DE0000;
}

/** making sure all input/select/a.buttons are same height **/
input, select {
  box-sizing: border-box;
  /*height: calc(1.5em + .5rem + 1.5px);*/
}

input[type=submit][name=cancel] {
  color: var(--gray-400);
}
input[type=submit][name=cancel]:not([disabled]):hover {
  color: var(--primary-active-color);
  background: none;
  text-decoration: underline;
}

input.datesel, input.datepicker, input.datepicker_week {
  width: 13rem;
}

input.datetimesel, input.datetimepicker {
  width: 16rem;
}

/* used for price inputs */
input.size {
  width: 8rem;
}

input.price, input.percentage {
  text-align: right;
  width: 10rem;
}

/** used for address fields */
input.address_street {
  width: 28rem;
}

input.address_nr {
  width: 6rem;
}

input.address_zip {
  width: 10rem;
}

input.address_city {
  width: 24rem;
}

.form-btn-group {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  row-gap: 16px;
  -moz-column-gap: 8px;
  column-gap: 8px;
  padding: 24px 16px 8px 16px;
  margin: 12px 0;
}

.edit-form {
  display: block;
  /* content block met white space links/rechts */
}
.edit-form.with-padding {
  padding: 16px;
}
.edit-form .default_table {
  background: none;
  border: none;
  border-radius: 0;
  padding-bottom: 12px;
  border-bottom: 1px solid var(--gray-200);
}

form.edit-form > input[type=submit],
form.edit-form > button,
.default_table > input[type=submit],
.default_table > button {
  margin: 16px 0 16px 8px;
}

/** FILE UPLOAD VELDEN */
input[type=file] {
  border: 0;
}

#tabnav {
  margin: 5px 0 5px 8px;
  padding-left: 0;
  display: flex;
  flex-wrap: wrap;
  font-size: 1.5rem;
}
#tabnav li {
  position: relative;
  /** offset for border bottom line **/
  margin: 5px 15px 5px 10px;
  padding: 0;
  display: inline-flex;
  list-style-type: none;
  border-bottom: 2px solid transparent;
  flex-direction: column;
}
#tabnav a {
  font-weight: 500;
  padding: 8px 0;
  text-decoration: none;
  color: var(--gray-900);
  display: inline-block;
  flex: 1;
}
#tabnav a.here0,
#tabnav a.here1,
#tabnav a.here2,
#tabnav li.active,
#tabnav a.nu,
#tabnav a.active,
#tabnav a:hover {
  color: var(--primary-active-color);
}
#tabnav a.active {
  color: var(--primary-active-color);
}
#tabnav a:hover {
  color: var(--primary-active-color);
  transition: color 0.2s;
}
#tabnav li::after {
  content: "";
  display: block;
  width: 0;
  height: 2px;
  background: var(--primary-active-color);
  transition: width 0.2s;
}
#tabnav li.active::after, #tabnav li:hover::after {
  width: 100%;
}
#tabnav #tabnav a:visited.here {
  background: var(--gsd-primary-color);
}
#tabnav a:link.disabled, #tabnav a:visited.disabled {
  color: #dbdbdb;
}

#tabnav_wiz span.here0,
#tabnav_wiz span.here1,
#tabnav_wiz span.here2,
#tabnav_wiz span.active {
  color: #000;
  font-weight: bold;
}

#breadcrumbs {
  width: 100%;
  max-width: 1600px;
  padding: 14px 8px 10px 8px;
  border-bottom: 0;
  font-size: 1.4rem;
  margin-left: auto;
  margin-right: auto;
}
#breadcrumbs a {
  text-decoration: none;
  color: var(--gray-400);
}
#breadcrumbs ol {
  display: flex;
  align-items: center;
  margin: 0;
  padding: 0;
}
#breadcrumbs ol li {
  display: inline-block;
  margin: 0;
  color: var(--gray-400);
}
#breadcrumbs ol li i.fa {
  font-weight: 300;
  font-size: 1.1rem;
  margin: 0 8px;
}
#breadcrumbs ol li:first-child a:before {
  content: "\f015"; /* this is your text. You can also use UTF-8 character codes as I do here */
  font-family: FontAwesome;
  position: relative;
  margin: 0;
  font-weight: normal;
}
#breadcrumbs ol li:first-child span {
  display: none;
}
#breadcrumbs ol li:last-child {
  color: var(--gray-900);
}

@media (max-width: 600px) {
  #breadcrumbs {
    display: none;
  }
}
body.body_login {
  background: url(/projects/rde/templates/backend2/dist/c8f3634efcd5a3c4400f.jpg) center no-repeat;
  background-size: cover;
  width: 100%;
  height: 100%;
}
body.body_login .main-grid {
  display: grid;
  width: 100%;
  height: 100%;
  grid-template-areas: "head" "main";
  grid-template-columns: auto;
  grid-template-rows: auto 1fr;
  /* Login part */
}
body.body_login .main-grid main {
  display: flex;
  align-items: center;
  flex-direction: column;
  background: none;
  grid-area: main;
  padding: 0px;
}
body.body_login .main-grid main > div {
  margin: auto 0;
  border-radius: 12px;
  background: var(--gray-900);
  width: 580px;
  font-size: 1.6rem;
  padding: 70px 80px 80px 80px;
  z-index: 1;
}
body.body_login .main-grid main > div #loginscrn {
  color: var(--gray-400);
}
body.body_login .main-grid main > div #loginscrn a {
  color: var(--gray-400);
}
body.body_login .main-grid main > div #loginscrn > div {
  background: var(--gray-900);
  border-radius: 0 0 8px 8px;
}
body.body_login .main-grid main > div #loginscrn > h2 {
  display: block;
  margin: 0;
  margin-bottom: 40px;
  font-size: 3.2rem;
  color: white;
  text-align: center;
}
body.body_login .main-grid main > div #loginscrn label {
  display: block;
  margin-top: 24px;
  color: var(--gray-400);
}
body.body_login .main-grid main > div #loginscrn input[type=submit], body.body_login .main-grid main > div #loginscrn input[type=button] {
  display: block;
  width: 100%;
  margin-top: 8px;
  padding: 16px 12px;
  font-size: 1.6rem;
}
body.body_login .main-grid main > div #loginscrn input[type=text], body.body_login .main-grid main > div #loginscrn input[type=password] {
  display: block;
  width: 100%;
  margin-top: 8px;
  border-radius: 6px;
  background: var(--gray-800);
  padding: 16px 12px;
  border: none;
  color: white;
}
body.body_login .main-grid main > div #loginscrn .below-password {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  margin-top: 8px;
  margin-bottom: 40px;
  font-size: 1.4rem;
}
body.body_login .main-grid main > div #login_header {
  width: 100%;
  max-width: 100%;
  height: 64px;
  background-image: url(/projects/rde/templates/backend2/dist/71bead68c701e75b873e.svg);
  background-color: var(--gray-900);
  background-position: center center;
  background-size: 180px;
  background-repeat: no-repeat;
  margin-bottom: 40px;
}

@media (max-width: 600px) {
  body.body_login .main-grid main {
    max-width: 100vw;
  }
  body.body_login .main-grid main > div {
    margin: auto 15px;
    width: calc(100% - 30px);
    padding: 40px 25px;
  }
}
:root {
  --big-circle-right: -128%;
  --small-circle-left: -92%;
  --big-circle-position: -876px;
  --small-circle-position: -757px ;
}

@media (min-width: 480px) {
  :root {
    --big-circle-right: -229%;
    --small-circle-left: -82%;
    --big-circle-position: -2947px;
    --small-circle-position: -1173px ;
  }
}
@media (min-width: 728px) {
  :root {
    --big-circle-right: -203%;
    --small-circle-left: -102% ;
  }
}
@media (min-width: 992px) {
  :root {
    --big-circle-right: -143%;
    --small-circle-left: -68% ;
  }
}
@media (min-width: 1200px) {
  :root {
    --big-circle-right: -102%;
    --small-circle-left: -58% ;
  }
}
@media (min-width: 1600px) {
  :root {
    --big-circle-right: -41%;
    --small-circle-left: -15% ;
  }
}
@keyframes bigCircleOut {
  0% {
    right: var(--big-circle-right);
  }
  to {
    right: var(--big-circle-position);
  }
}
@keyframes bigCircleIn {
  0% {
    right: var(--big-circle-position);
  }
  to {
    right: var(--big-circle-right);
  }
}
@keyframes smallCircleOut {
  0% {
    left: var(--small-circle-left);
  }
  to {
    left: var(--small-circle-position);
  }
}
@keyframes smallCircleIn {
  0% {
    left: var(--small-circle-position);
  }
  to {
    left: var(--small-circle-left);
  }
}
body .circles.closing .bigCircle {
  animation: bigCircleIn 1s 0s;
  animation-iteration-count: 1;
  animation-fill-mode: forwards;
  animation-timing-function: cubic-bezier(0.86, 0, 0.07, 1);
}
body .circles.closing .smallCircle {
  animation: smallCircleIn 1s 0s;
  animation-iteration-count: 1;
  animation-fill-mode: forwards;
  animation-timing-function: cubic-bezier(0.86, 0, 0.07, 1);
}
body .circles.opened .bigCircle {
  animation: bigCircleOut 1s 0s;
  animation-iteration-count: 1;
  animation-fill-mode: forwards;
  animation-timing-function: cubic-bezier(0.86, 0, 0.07, 1);
}
body .circles.opened .smallCircle {
  animation: smallCircleOut 1s 0s;
  animation-iteration-count: 1;
  animation-fill-mode: forwards;
  animation-timing-function: cubic-bezier(0.86, 0, 0.07, 1);
}
body .circles .bigCircle, body .circles .smallCircle {
  position: fixed;
}
body .circles .bigCircle {
  top: -70px;
  width: 2947px;
  height: 2947px;
  border-radius: 2947px;
  text-align: center;
  background: linear-gradient(204deg, #fff 15.48%, hsla(0, 0%, 100%, 0) 62.97%);
  opacity: 0.12;
}
body .circles .smallCircle {
  top: 50%;
  width: 1173px;
  height: 1173px;
  border-radius: 1173px;
  opacity: 0.06;
  background: linear-gradient(204deg, #fff 15.48%, hsla(0, 0%, 100%, 0) 50.01%);
}

@media (max-width: 480px) {
  body .circles .bigCircle {
    width: 876px;
    height: 876px;
    top: -3px;
  }
}
@media (max-width: 480px) {
  body .circles .smallCircle {
    width: 757px;
    height: 757px;
    top: 40%;
  }
}
body.body_login {
  background: linear-gradient(107deg, var(--gsd-primary-color), var(--gsd-btn-primary-bg-color-hover) 139.5%);
}

@keyframes rotateSpinnerLoader {
  0% {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(1turn);
  }
}
@keyframes fadeOutLoader {
  0% {
    opacity: 1;
    visibility: visible;
  }
  to {
    opacity: 0;
    visibility: hidden;
  }
}
@keyframes fadeInLoader {
  0% {
    opacity: 0;
    visibility: hidden;
  }
  to {
    opacity: 1;
    visibility: visible;
  }
}
#loading-spinner {
  position: fixed;
  background-color: rgba(51, 51, 51, 0.66);
  height: 100%;
  width: 100%;
  z-index: 1300;
  opacity: 0;
  visibility: hidden;
}
#loading-spinner .spinner {
  position: absolute;
  left: 50%;
  top: 50%;
  height: 50px;
  width: 50px;
  transform: translateX(-50%);
}
#loading-spinner .spinner:after, #loading-spinner .spinner:before {
  content: "";
  position: absolute;
  border-radius: 100%;
  border: 2px solid #fff;
  border-top-color: transparent;
  width: 50px;
  height: 50px;
}
#loading-spinner .spinner:before {
  z-index: 100;
  animation: rotateSpinnerLoader 1s 0s;
  animation-iteration-count: infinite;
  animation-fill-mode: forwards;
  animation-timing-function: ease-in-out;
}
#loading-spinner .spinner:after {
  border: 2px solid transparent;
}
#loading-spinner.hide-spinner {
  animation: fadeOutLoader 0.4s 1;
  animation-iteration-count: 1;
  animation-fill-mode: forwards;
  animation-timing-function: ease-in-out;
}
#loading-spinner.show-spinner {
  animation: fadeInLoader 0.4s 1;
  animation-iteration-count: 1;
  animation-fill-mode: forwards;
  animation-timing-function: ease-in-out;
}

.gsd-modal {
  display: none;
  z-index: 10000;
  overflow-y: auto;
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
}
.gsd-modal.gsd-modal-show {
  display: block;
}
.gsd-modal > div {
  position: fixed;
  display: flex;
  justify-content: center;
  min-height: 100vh;
  min-width: 100vw;
}
.gsd-modal > div.gsd-modal-position-center {
  align-items: center;
}
.gsd-modal > div.gsd-modal-position-top {
  align-items: flex-start;
}
.gsd-modal > div.gsd-modal-position-bottom {
  align-items: flex-end;
}

.gsd-modal-bg-glass {
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  position: fixed;
}
.gsd-modal-bg-glass > div {
  opacity: 0.5;
  background-color: black;
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
}

.gsd-modal-container {
  width: 800px;
  max-width: 100%;
  max-height: 90vh;
  overflow: auto;
  display: inline-block;
  vertical-align: middle;
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
  --tw-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  box-shadow: 0 0 rgba(0, 0, 0, 0), 0 0 rgba(0, 0, 0, 0), var(--tw-shadow);
  text-align: left;
  border-radius: 0.5rem;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  transform: translateX(var(--tw-translate-x)) translateY(var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.gsd-modal-loader {
  max-width: 100%;
}

.gsd-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: var(--gsd-primary-color);
  color: #ffffff;
  padding: 1rem 1.5rem;
  font-size: 1.5rem;
}

.gsd-modal-content {
  min-height: 300px;
  padding: 1.5em;
  background-color: white;
}
.gsd-modal-content iframe {
  width: 100%;
  height: 100%;
  min-height: 300px;
  border: none;
  margin: 0;
  outline: none;
  padding: 0;
  vertical-align: top;
}

.gsd-modal-close svg {
  color: white;
  height: 1.5em;
  width: 1.5em;
}

.gsd-modal-close:hover svg {
  color: var(--gray-100);
  height: 1.5em;
  width: 1.5em;
}

.swal2-popup {
  /* overrule default 1rem font-size, which is too small **/
  font-size: inherit;
}

.swal2-popup .swal2-styled:focus {
  box-shadow: none;
}

#pac-input, .pac-input {
  margin-top: 10px;
  border: 1px solid transparent;
  border-radius: 2px 0 0 2px;
  box-sizing: border-box;
  height: 40px;
  outline: none;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
  background-color: #fff;
  padding: 0 6px 0 13px;
  width: 300px;
  font-family: Roboto, serif;
  font-size: 18px;
  font-weight: 300;
  text-overflow: ellipsis;
}

#pac-input:focus, .pac-input:focus {
  border-color: #4d90fe;
  margin-left: -1px;
  padding-left: 14px; /* Regular padding-left + 1. */
  width: 401px;
}

.pac-container {
  font-family: Roboto, Verdana, Arial, Helvetica, sans-serif;
}

.popout-box {
  position: relative;
}

.popout-box summary {
  display: inline-block;
  cursor: pointer;
}

.popout-box .popout-content {
  display: block;
  transform-origin: top left;
  position: absolute;
  left: 0px;
  margin-top: 1rem;
  padding: 5px 8px;
  background: white;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -4px rgba(0, 0, 0, 0.1);
  border: 1px solid var(--gray-200);
  transition: 10000ms ease all;
  z-index: 10000;
}

.popout-box .popout-content.right {
  left: initial;
  right: 0px;
}

/**********
  WIDTH UTILITIES, TAKE FROM https://tailwindcss.com/docs/width
 **********/
.w-1 {
  width: 4px;
}

.w-2 {
  width: 8px;
}

.w-3 {
  width: 12px;
}

.w-4 {
  width: 16px;
}

.w-5 {
  width: 20px;
}

.w-6 {
  width: 24px;
}

.w-7 {
  width: 28px;
}

.w-8 {
  width: 32px;
}

.w-9 {
  width: 36px;
}

.w-10 {
  width: 40px;
}

.w-11 {
  width: 44px;
}

.w-12 {
  width: 48px;
}

.w-14 {
  width: 56px;
}

.w-16 {
  width: 64px;
}

.w-20 {
  width: 80px;
}

.w-24 {
  width: 96px;
}

.w-28 {
  width: 112px;
}

.w-32 {
  width: 128px;
}

.w-36 {
  width: 144px;
}

.w-40 {
  width: 160px;
}

.w-44 {
  width: 176px;
}

.w-48 {
  width: 192px;
}

.w-52 {
  width: 208px;
}

.w-56 {
  width: 224px;
}

.w-60 {
  width: 240px;
}

.w-64 {
  width: 256px;
}

.w-72 {
  width: 288px;
}

.w-80 {
  width: 320px;
}

.w-96 {
  width: 384px;
}

.w-auto {
  width: auto;
}

.w-full {
  width: 100%;
}

.w-screen {
  width: 100vw;
}

.w-min {
  width: -moz-min-content;
  width: min-content;
}

.w-max {
  width: -moz-max-content;
  width: max-content;
}

.w-fit {
  width: -moz-fit-content;
  width: fit-content;
}

/*
 gsd-btn is the standard class for buttons.
 this class can be set on:
 - a elements
 - input submit/button elements
 Also some helper
  css for buttons with icons from the IconHelper
*/
.qq-upload-button, input[type=submit], input[type=button], .dataTables_wrapper .dt-buttons button, .gsd-btn, input[type=file]::file-selector-button, .swal2-popup .swal2-styled.swal2-confirm, .swal2-popup .swal2-styled.swal2-cancel {
  display: inline-flex;
  padding: 12px 24px;
  justify-content: center;
  align-items: center;
  gap: 8px;
  flex-shrink: 0;
  border: 1px solid transparent;
  cursor: pointer;
  border-radius: 50px;
  text-align: center;
  text-decoration: none;
  transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  background-color: var(--gsd-btn-bg-color);
  color: var(--gsd-btn-text-color);
  font-size: 1.4rem;
}

input[type=submit]:hover:not(:disabled), input[type=button]:hover:not(:disabled) {
  background-color: var(--gsd-btn-bg-color-hover);
  color: var(--gsd-btn-text-color-hover);
}

input[type=file]::file-selector-button, .qq-upload-button, .gsd-btn.gsd-btn-primary, .swal2-popup .swal2-styled.swal2-confirm {
  background-color: var(--gsd-btn-primary-bg-color);
  color: var(--gsd-btn-primary-text-color);
}

.gsd-btn.gsd-btn-primary:hover:not(:disabled) {
  background-color: var(--gsd-btn-primary-bg-color-hover);
  color: var(--gsd-btn-primary-text-color-hover);
}

.dataTables_wrapper .dt-buttons button, .gsd-btn.gsd-btn-secondary, div.box input[type=submit], .list-filter-form input[type=submit], input[type=file]::file-selector-button {
  border: 1.5px solid var(--gsd-btn-secondary-border-color);
  color: var(--gsd-btn-secondary-text-color);
  background: none;
}

input[type=file]:hover:not(:disabled)::file-selector-button, .qq-upload-button:hover:not(:disabled), .gsd-btn.gsd-btn-secondary:hover:not(:disabled) {
  background-color: var(--gsd-btn-secondary-bg-color-hover);
  color: var(--gsd-btn-secondary-text-color-hover);
}

.gsd-btn.gsd-btn-tertiary {
  border: 1.5px solid var(--gsd-btn-tertiary-border-color);
  color: var(--gsd-btn-tertiary-text-color);
  background: none;
}

.gsd-btn.gsd-btn-tertiary:hover:not(:disabled) {
  background-color: var(--gsd-btn-tertiary-bg-color-hover);
  color: var(--gsd-btn-tertiary-text-color-hover);
}

.gsd-btn[disabled], .gsd-btn[disabled]:hover, .gsd-btn.disabled, .gsd-btn.disabled:hover {
  background-color: var(--gray-100) !important;
  color: rgb(170, 170, 170) !important;
  opacity: 0.7 !important;
  box-shadow: none;
  border: 1px solid transparent !important;
}

.gsd-btn.gsd-btn-link, div.box input[name=reset], div.box input[type=submit].reset-btn, .list-filter-form input[name=reset], .list-filter-form input[type=submit].reset-btn, input[type=submit][name=cancel], .swal2-popup .swal2-styled.swal2-cancel {
  box-shadow: none;
  background: none;
  border: 1px solid transparent;
  padding: 12px;
}

div.box input[name=reset]:hover, div.box input[type=submit].reset-btn:hover, .list-filter-form input[name=reset]:hover, .list-filter-form input[type=submit].reset-btn:hover {
  box-shadow: none;
  background: none;
  border: 1px solid transparent;
}

.gsd-btn {
  /** PRIMARY **/
  /** SECONDARY **/
  /** TERTIARY **/
  /** LINK **/
  /** DISABLED **/
  /** WITH ICON **/
}
.gsd-btn.gsd-btn-primary.gsd-btn-icon .gsd-svg-icon svg {
  color: var(--gsd-btn-primary-text-color);
}
.gsd-btn.gsd-btn-secondary.gsd-btn-icon .gsd-svg-icon svg {
  color: var(--gsd-btn-secondary-text-color);
}
.gsd-btn.gsd-btn-link:hover:not(:disabled) {
  color: var(--primary-active-color);
}
.gsd-btn[readonly], .gsd-btn[readonly]:hover, .gsd-btn.readonly, .gsd-btn.readonly:hover {
  border: 1px solid transparent !important;
}
.gsd-btn.gsd-btn-icon {
  display: inline-flex;
  align-items: center;
  padding: 8px 24px;
}
.gsd-btn.gsd-btn-icon span {
  margin-left: -0.25rem;
  padding-right: 0.25rem;
}
.gsd-btn.gsd-btn-small {
  padding: 8px 12px;
}

/** overwriting button styling of plugins **/
.dataTables_wrapper .dt-buttons button {
  /** default button has a background linear, which we cant overrule with background-color */
  background: var(--gray-100);
  padding: 3px 10px;
  line-height: initial;
}

/** all inputsubmit/button default have the gsd-btn style. makes life a lot easier **/
.gsd-select {
  position: relative;
  display: inline-block;
  line-height: normal;
}

.gsd-select-placeholder {
  min-width: 160px;
  display: block;
  border: 1px solid var(--gray-200);
  padding: 0.8rem 1rem;
}

.gsd-select-placeholder, .gsd-select-options {
  flex-direction: column;
  font-family: inherit;
  border-radius: 6px;
  background-color: white;
  box-sizing: border-box;
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
}

.gsd-select-options {
  max-height: 500px;
  overflow: auto;
  cursor: default;
  position: absolute;
  z-index: 1;
  display: none;
  border: 1px solid rgba(187, 187, 187, 0.6509803922);
  padding: 4px 4px;
}
.gsd-select-options label {
  padding: 0.4rem;
}
.gsd-select-options label:hover {
  background-color: var(--gsd-btn-bg-color-hover);
}
.gsd-select-options label input {
  margin-right: 10px;
}

.gsd-select-option-selected {
  background-color: #f7f8fa;
}
.gsd-select-option-selected:hover {
  background-color: var(--gsd-btn-bg-color-hover);
}

.gsd-select-searchbox {
  width: auto !important;
  margin: 5px;
}

.gsd-select-flex {
  display: flex;
}

.gsd-select-empty {
  font-weight: bold;
}

input.sorton {
  background-color: #2196f3 !important;
  color: white !important;
}

tr.tDnD_whileDrag td {
  background-color: #2196f3;
  color: white !important;
}

.sort-td {
  width: 15px;
  display: none;
}

.sort-td span {
  pointer-events: none;
}

/*********************
 * Algemene classes die overal gebruikt kunnen worden
 * en niet in een specifiek bestand horen
 *********************/
hr.divider {
  display: block;
  height: 1px;
  border: none;
  background: var(--gray-200);
  width: 100%;
}

.clear {
  clear: both;
}

.lockonsubmit_spinner {
  margin-left: 5px;
  margin-right: 5px;
  color: #f44336;
}

div.scroll {
  height: 300px;
  overflow: auto;
  background-color: White;
  padding: 5px 10px 10px 10px;
}

.hidden {
  display: none;
}

a.imagegallery {
  padding: 10px 0;
  display: inline-block;
}

.multi-select {
  width: 100%;
  height: 400px;
}

div.st {
  padding-top: 10px;
  font-size: 12px;
  padding-left: 0;
  float: left;
}

input.productsize {
  width: 30px;
  text-align: right;
  margin: 8px 0 0 0;
}

.tabscontent {
  width: 100%;
  display: none;
}

.selectprod {
  padding: 3px 0;
  display: block;
}

.languageselect {
  vertical-align: middle;
  display: inline-block;
}

/* hide div */
#orderform .emailcust {
  display: none;
}

a.delproduct, a.minbasket, a.plusbasket {
  display: inline-block;
  text-decoration: none;
  font-size: 16px;
  padding: 3px;
}

a.delproduct img {
  vertical-align: bottom;
}

/* PRODUCT TABLE */
.producttable {
  float: left;
  border-top: 1px solid #E9E9E9;
  margin-top: 3px;
  width: 700px;
}

a.plusinput, a.mininput {
  text-decoration: none;
  font-size: 17px;
  padding: 8px 2px;
}

.not_in_backorder_info {
  font-style: italic;
  margin-top: 5px;
  display: inline-block;
}

.status_div {
  float: left;
  width: 125px;
  margin-right: 3px;
  padding: 8px;
  text-decoration: none;
  font-weight: normal;
  font-size: 12px;
  border-radius: 3px;
}

.orderstatusdiv {
  float: left;
  width: 154px;
  margin-right: 3px;
  padding: 8px;
  text-decoration: none;
  font-weight: normal;
  font-size: 12px;
  border-radius: 3px;
}

.orderstatusdiv:hover {
  text-decoration: none;
}

.info-box a {
  text-decoration: none;
}

.phonebutton {
  display: inline-block;
  vertical-align: middle;
}

/* CKEDITOR MODAL */
.fm-modal {
  z-index: 10011; /** Because CKEditor image dialog was at 10010 */
  width: 80%;
  height: 80%;
  top: 10%;
  left: 10%;
  border: 0;
  position: fixed;
  -o-box-shadow: 0px 1px 5px 0px #656565;
  box-shadow: 0px 1px 5px 0px #656565;
}

/* autocomplete */
.autocomplete-suggestions {
  border: 1px solid #999;
  background: #FFF;
  cursor: default;
  overflow: auto;
  box-shadow: 1px 4px 3px rgba(50, 50, 50, 0.64);
}

.autocomplete-suggestion {
  padding: 2px 5px;
  white-space: nowrap;
  overflow: hidden;
}

.autocomplete-no-suggestion {
  padding: 2px 5px;
}

.autocomplete-selected {
  background: #F0F0F0;
}

.autocomplete-suggestions strong {
  font-weight: bold;
  color: #FF9900;
}

.autocomplete-group {
  padding: 2px 5px;
}

.autocomplete-group strong {
  font-weight: bold;
  font-size: 16px;
  color: #FF9900;
  display: block;
  border-bottom: 1px solid #000;
}

/* colorpicker */
.ui-colorpicker, .ui-dialog.ui-colorpicker {
  width: 350px !important;
}

/* module specific style */
#message_development, #message_updater {
  background-color: #f44336;
  text-align: center;
  color: white;
  font-weight: bold;
  padding: 5px;
  display: block;
}

#message_updater ul {
  list-style: none;
  margin: 0;
  padding: 0;
  font-weight: normal;
}

#ordertable {
  font-size: 1.5rem;
  margin: 10px 16px;
}

#ordertable td {
  vertical-align: middle;
  padding-bottom: 5px;
}

.descriptiondate {
  display: inline-block;
}

.allimages {
  display: inline-block;
  border: 1px dashed lightgrey;
  position: relative;
}
.allimages img {
  max-width: 100%;
  max-height: 100%;
  vertical-align: middle;
}
.allimages .imagegallery {
  padding: 0;
  width: 150px;
  height: 120px;
  line-height: 100px;
}
.allimages .actionmenu {
  background-color: white;
  display: block;
  padding: 0 8px;
}
.allimages .actionmenu a {
  text-decoration: none;
  display: inline-block;
}
.allimages .actionmenu .material-icons {
  font-size: 22px;
  padding: 0 3px;
}
.allimages .btn_crop {
  bottom: 3px;
  text-decoration: none;
  display: block;
}

.product_outofstock {
  color: red;
  font-style: italic;
}

/* import the project backend css's */
@CHARSET "UTF-8";
:root {
  --gsd-primary-color: #982e1a;
  --primary-active-color: hsl(9, 70.79%, 34.9%);
  --sidebar-color: white;
  --sidebar-active-color: #adadad;
  --sidebar-background-color: hsl(9, 70.79%, 34.9%);
  --sidebar-submenu-background-color: hsl(9, 70.79%, 40%);
  --gsd-btn-primary-text-color: white;
  --gsd-btn-primary-bg-color-hover: hsl(9, 70.79%, 40%);
  --gsd-btn-primary-text-color-hover: white;
  --gsd-btn-text-color-hover: hsl(9, 70.79%, 34.9%);
  /* lists */
  --default-table-hover: hsl(5, 100%, 98%);
}

@CHARSET "UTF-8";
body.body_login .main-grid main > div #login_header {
  background: white url(/projects/rde/templates/backend2/dist/9825703f26a9e8ec2bff.svg) center no-repeat;
  background-size: 230px;
  height: 100px;
  border-radius: 5px;
}

.showdetail_highlight {
  color: red !important;
  border-color: red !important;
}

.copy, .copy_all {
  display: inline-block;
  font-size: 15px;
  padding: 0px 10px;
  color: #dadada;
}

.copy:hover, .copy_all:hover {
  text-decoration: none;
}

.copy_diff {
  color: red;
}

.referenceimage {
  display: inline-block;
  border: 1px dashed lightgrey;
  position: relative;
  vertical-align: top;
}

.referenceimage img {
  width: 100px;
}

.referenceimage .imagegallery {
  padding: 0;
}

.referenceimage .ch_delete {
  position: absolute;
  bottom: 0;
  left: 0;
  z-index: 1000;
  display: none;
}

.div_content {
  width: 100%;
}

.logo-image {
  background-image: url(/projects/rde/templates/backend2/dist/e7926534bce03b30affa.png);
}

.sidebar .logo {
  background-color: #fafaf9;
  background-size: 145px auto;
}

.sidebar .nav-item-icon.M_BEDRIJVENGIDS:before {
  content: "\eaa6" !important;
}

.sidebar .nav-item-icon.M_SUPPLIER_STOCK_OV:before {
  content: "\ea60" !important;
}

.sidebar .nav-item-icon.M_RDE_SETTINGS:before {
  content: "\eac6" !important;
}

.sidebar .nav-item-icon.M_DOCUMENTS:before {
  content: "\e9d4" !important;
}

.sidebar .nav-item-icon.M_RDE_PRICES:before {
  content: "\eaa3" !important;
}

.sidebar .nav-item-icon.M_RDE_ORDERS:before {
  content: "\e95c" !important;
}

.sidebar .nav-item-icon.M_PRODUCTION:before {
  content: "\eb37" !important;
}

.sidebar .nav-item-icon.M_RDE_MAP:before {
  content: "\ea65" !important;
}

.quotation_icon {
  width: 16px;
  height: 16px;
  border-radius: 8px;
  box-shadow: 1px 1px 4px #dddbdb;
  border: 1px solid #dddbdb;
  color: white;
  font-size: 12px !important;
  padding: 1px;
  position: relative;
}

.quotation_icon.fa.fa-bolt {
  padding: 1px 5px;
  color: black;
}

.quotation_icon_10, .quotation_icon_80 {
  background-color: white;
}

.quotation_icon_20, .quotation_icon_21 {
  background-color: #65CF0F;
}

.quotation_icon_30 {
  background-color: #DEC300;
}

.quotation_icon_31, .quotation_icon_35, .quotation_icon_38 {
  background-color: #de6d00;
}

.quotation_icon_40 {
  background-color: #CF0F1C;
}

.quotation_icon_50 {
  background-color: #9D0091;
}

.quotation_icon_53 {
  background-color: #69DBCF;
}

.quotation_icon_55 {
  background-color: #4E00DE;
}

.quotation_icon_60, .quotation_icon_61, .quotation_icon_62, .quotation_icon_63 {
  background-color: black;
}

.quotation_icon_70 {
  background-color: #D6D9BA;
}

.quotation_icon_80::after {
  content: "✖";
  color: red;
  font-weight: bold;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.quotation_icon {
  box-sizing: border-box;
}

.quotation_icon_38 {
  background-color: #de3f00;
}
