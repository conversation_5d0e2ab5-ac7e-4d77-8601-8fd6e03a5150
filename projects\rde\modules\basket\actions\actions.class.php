<?php

  use domain\basket\service\RequestPriceService;

  class basketRdeActions extends basketActions {

    public function preExecute() {
//    $_SESSION["userAdmin"] = true;

      Config::set("PRODUCTS_SHOW_PRICES_INC_VAT", false); //show prices including vat.
      $this->showleftmenu = true;
      $this->cats = Category::getShopCats();
      $this->cloudtags = Tag::getAllAndScore($_SESSION['site']->id);
      if (isset($_SESSION['userObject'])) $this->paymethods = $this->getPaymentMethods($_SESSION["userObject"]);

      //offerte of bestelling
      $this->tender = isset($_GET["tender"]) ? true : false;
      if ($this->tender) {
        $this->wizard_name = __("Offerte");
      }
      else {
        $this->wizard_name = __("Bestelling");
      }
      $this->hasRequestPrices = RequestPriceService::basketHasRequestPrices($_SESSION['basket'] ?? null);
    }

    /**
     * @param SandboxUsers $suser
     * @param null $shippingmethod
     * @return array
     */
    public function getPaymentMethods($suser, $shippingmethod = null) {
      $paymethods = [];
      if (!isset($suser->company) || (isset($suser->company) && $suser->company->pay_online == 1) && PaymentMollie::isAvailable()) {
        $paymethods[Payment::PAYMENT_MOLLIE] = new PaymentMollie();
      }
      if (isset($suser->company) && $suser->company->pay_after == 1 && $suser->company->payInAdvance == 0) {
        $paymethods[Payment::PAYMENT_OVERMAKEN] = new PaymentOvermaken();
      }
      return $paymethods;
    }

    protected function addToBasket($basket, $import = false) {
      $errors = [];

      if (isset($_POST['size'])) {
        if (isset($_POST['stoneId'])) {
          $sprices = [];
          foreach ($_POST['size'] as $name => $size) {
            if ($size <= 0) continue;

            $productId = $_POST['product_id'];
            $product = Product::getProductAndContent($productId, $_SESSION['lang']);
            $stoneId = $_POST['stoneId'];
            $stone = Stones::find_by(['stoneId' => $stoneId]);
            $sibelings = $stone->getSibelings();

            $item = [];

            if (!isset($sprices[$stoneId])) {
              $sprices[$stoneId] = Quotations::getPrices(date("Y-m-d"), $_SESSION["userObject"]->userId ?? '', $stone, true);
            }

            $rowname = $product->getName();

            if ($name == "left" || $name == "right" || $name == "leftg") {
              $item['stone_id'] = $sibelings[$name]->stoneId;
              $rowname = $sibelings[$name]->name;
            }
            else {
              $item['stone_id'] = $stoneId;
            }

            if ($name == "leftglaced") {
              $item['glaced_left'] = 1;
              $rowname .= ' - Geglazuurde zijkant links';
            }
            elseif ($name == "rightglaced") {
              $item['glaced_right'] = 1;
              $rowname .= ' - Geglazuurde zijkant rechts';
            }
            elseif ($name == "leftmitre") {
              $item['mitre_id'] = $_POST["mitre"]["leftmitreId"];
              $rowname .= ' - Verstek links';
              $mitre = Mitres::find_by(["mitreId" => $item['mitre_id']]);
              $rowname .= ' ' . $mitre->angle . '°';
            }
            elseif ($name == "rightmitre") {
              $item['mitre_id'] = $_POST["mitre"]["rightmitreId"];
              $rowname .= ' - Verstek rechts';
              $mitre = Mitres::find_by(["mitreId" => $item['mitre_id']]);
              $rowname .= ' ' . $mitre->angle . '°';
            }
            elseif (is_numeric($name)) { //dit is een standaard maat
              $rowname .= " - lengte " . $name . " mm";
              $item['elementlength'] = $name;
            }
            $item['name'] = $rowname;

            $found = false;
            foreach ($basket['products'] as $key => $litem) {
              if ($litem["name"] == $rowname) { //naam gelijk, dat hetzelfde product
                $found = $key;
                break;
              }
            }

            if ($found !== false) {
              $item = $basket['products'][$found];
              $item['size'] = $item['size'] + $size;
              $item['totalprice'] = $item['size'] * $item['pieceprice'];
              $item['totalprice_inc'] = $item['totalprice'] * 1.21;
              $basket['products'][$found] = $item;
            }
            else {
              if ($product && $product->void == 0) {

                $pieceprice = $sprices[$stoneId]["stonePrice"];
                if ($name == "left" || $name == "right") {
                  $pieceprice = $sprices[$stoneId]["stonePriceEnd"];
                }
                elseif ($name == "leftg" || $name == "rightg") {
                  $pieceprice = $sprices[$stoneId]["stonePriceGroove"];
                }
                elseif ($name == "leftglaced" || $name == "rightglaced") {
                  $pieceprice = $sprices[$stoneId]["glazesidePrice"];
                }
                elseif ($name == "leftmitre" || $name == "rightmitre") {
                  $stone_size = StoneSizes::find_by(["sizeId" => $stone->sizeId]);
                  $pieceprice = $mitre->getMitrePrice($stone, $stone_size, $sprices[$stoneId]);
                }
                elseif (is_numeric($name)) { //dit is een standaard maat
                  //standaard element heeft geen steen opslag.
                  $standard_element_price = Quotations::getPrices(date("Y-m-d"), $_SESSION["userObject"]->userId ?? '', $stone);
                  $pieceprice = round($stone->getMeterprice($standard_element_price) / 1000 * $name, 2);
                }

                $item['product'] = $product;
                $item['size'] = $size;
                $item['pieceprice'] = $pieceprice;
                $item['totalprice'] = $item['size'] * $pieceprice;
                $item['totalprice_inc'] = $item['size'] * $pieceprice * 1.21;

                if (count($errors) == 0) {
                  $basket['products'][] = $item;
                  $basket = $this->sortBasket($basket);
                }
              }
              else {
                $errors[] = "Dit product is helaas niet (meer) leverbaar en is verwijderd uit uw winkelmandje.";
              }
            }
          }

        }
        else {
          //standard
          foreach ($_POST['size'] as $productid => $size) {
            if ($size > 0) {
              $found = false;
              if (isset($basket['products'])) {
                foreach ($basket['products'] as $key => $litem) {
                  if ($litem['product']->id == $productid) {
                    $found = $key;
                    break;
                  }
                }
              }

              if ($found !== false) {
                $item = $basket['products'][$found];
                $product = $item['product'];
                $item['size'] = $item['size'] + $size;
                $item['pieceprice'] = $product->getPriceByUser(null, Config::get('PRODUCTS_SHOW_PRICES_INC_VAT', true), $item['size']);
                $item['totalprice'] = $item['size'] * $product->getPriceByUser(null, false, $size);
                $item['totalprice_inc'] = $item['size'] * $product->getPriceByUser(null, true, $size);
                $basket['products'][$found] = $item;
              }
              else {
                $product = Product::getProductAndContent($productid, $_SESSION['lang']);
                if ($product && $product->void == 0) {
                  $item['product'] = $product;
                  $item['name'] = $product->getName($_SESSION['lang']);
                  $item['size'] = $size;
                  $item['pieceprice'] = $product->getPriceByUser(null, Config::get('PRODUCTS_SHOW_PRICES_INC_VAT', true), $item['size']);
                  $item['totalprice'] = $item['size'] * $item['product']->getPriceByUser(null, false, $size);
                  $item['totalprice_inc'] = $item['size'] * $item['product']->getPriceByUser(null, true, $size);

                  $rowname = $product->getName();
                  if ($product->discountgroup_id == 3) {
                    $rowname = "Koudglazuur: " . $product->getName();
                  }
                  elseif ($product->discountgroup_id == 4) {
                    $rowname = "Afstandhouders: " . $product->getName();
                  }
                  $item['name'] = $rowname;

                  if (count($errors) == 0) {
                    $basket['products'][] = $item;
                    $basket = $this->sortBasket($basket);
                  }
                }
                else {
                  $errors[] = "Dit product is helaas niet (meer) leverbaar en is verwijderd uit uw winkelmandje.";
                }
              }
            }

          }
        }
        $basket['subtotal'] = 0;
        $basket['subtotal_inc'] = 0;
        foreach ($basket['products'] as $key => $litem) {
          $basket['subtotal'] += $litem['totalprice'];
          $basket['subtotal_inc'] += $litem['totalprice_inc'];
        }

        $this->updateBasketInSession($basket);

        if (count($errors) == 0) {

          if ($import == true) {
            return $basket;
          }
          else {
            $_SESSION['flash_message'] = __("Product(en) toegevoegd aan winkelmandje.");
            ResponseHelper::redirect(reconstructQuery(['add']));
          }
        }
      }
      $this->errors = array_merge($this->errors, $errors);
      return $basket;
    }

    /**
     * Aangeroepen bij het updaten/refreshen/aanpassen aantal van het winkelmandje
     * @param array $basket
     * @return array $basket
     */
    protected function updateBasket($basket, $checkproducts = false) {
      $errors = [];
      if (isset($_POST['size'])) {
        $basket['subtotal'] = 0;
        $basket['subtotal_inc'] = 0;
        $monsterstones = 0; //max 3 monsterstones
        foreach ($basket['products'] as $key => $litem) {
          $product = $litem['product'];
          if (isset($_POST['size'][$key])) {//$litem->size
            $size = (int)$_POST['size'][$key];
            if ($size == 0) {
              unset($basket['products'][$key]);
            }
            else {

              $minproductsize = 1;
              if ($product->getStaffel() != "") {
                foreach ($product->getStaffel() as $lsize => $lval) {
                  $minproductsize = $lsize;
                  break;
                }
              }
              if ($size < $minproductsize) {
                $errors[] = $product->getName($_SESSION['lang']) . ': het minimale bestel aantal is ' . $minproductsize . ' stuks.';
              }

//            $extra_options = array();
//            if(isset($litem['extra_options']) && count($litem['extra_options']) > 0) {
//              $extra_options = $litem['extra_options'];
//            }

              $pieceprice = 0;
              if (isset($litem['stone_id'])) {
                //steen heeft geen staffelkorting, dus gewoon pieceprice pakken.
                $pieceprice = $litem['pieceprice'];
              }
              else {
                $pieceprice = $product->getPriceByUser(null, Config::get('PRODUCTS_SHOW_PRICES_INC_VAT', true), $size);
              }

              $basket['products'][$key]['size'] = $size;
              $basket['products'][$key]['totalprice'] = $size * $pieceprice;
              $basket['products'][$key]['totalprice_inc'] = $size * $pieceprice * 1.21;
              $basket['subtotal'] += $basket['products'][$key]['totalprice'];
              $basket['subtotal_inc'] += $basket['products'][$key]['totalprice_inc'];

              if ($product->discountgroup_id == 10) {
                $monsterstones += $size;
              }

              if ($product->order_size_max != "" && $size > $product->order_size_max) {
                $errors[] = "U kunt maximaal " . $product->order_size_max . " '" . $product->content->name . "' per keer bestellen van " . $product->getName($_SESSION['lang']) . ".";
              }

            }
          }
        }

        if ($monsterstones > 3) {
          $errors[] = 'U kunt maximaal 3 monsterstenen per keer bestellen.';
        }

        if (count($errors) == 0) {
          $this->updateBasketInSession($basket);
        }

        $this->errors = array_merge($this->errors, $errors);
        return $basket;
      }


      $basket['subtotal'] = 0;
      $basket['subtotal_inc'] = 0;
      $monsterstones = 0; //max 3 monsterstones
      foreach ($basket['products'] as $key => $litem) {

        $size = 0;
        foreach ($_SESSION['basket']['products'] as $product) {
          if ($product['product']->id == $litem['product']->id) {
            $size = $product['size'];
            if ($product['product']->discountgroup_id == 10) {
              $monsterstones += $size;
            }
          }
        }

        $product = $litem['product'];

        $minproductsize = 1;
        if ($product->getStaffel() != "") {
          foreach ($product->getStaffel() as $lsize => $lval) {
            $minproductsize = $lsize;
            break;
          }
        }
        if ($size < $minproductsize) {
          $errors[] = $product->getName($_SESSION['lang']) . ': het minimale bestel aantal is ' . $minproductsize . ' stuks.';
        }

        if ($product->order_size_max != "" && $size > $product->order_size_max) {
          $errors[] = "U kunt maximaal " . $product->order_size_max . " '" . $product->content->name . "' per keer bestellen.";
        }

        $extra_options = [];
        if (isset($litem['extra_options']) && count($litem['extra_options']) > 0) {
          $extra_options = $litem['extra_options'];
        }
        $basket['products'][$key]['size'] = $size;
        $basket['products'][$key]['totalprice'] = $size * $product->getPriceByUser(null, false, $size, $extra_options);
        $basket['products'][$key]['totalprice_inc'] = $size * $product->getPriceByUser(null, true, $size, $extra_options);
        $basket['subtotal'] += $basket['products'][$key]['totalprice'];
        $basket['subtotal_inc'] += $basket['products'][$key]['totalprice_inc'];
      }

      if ($monsterstones > 3) {
        $errors[] = 'U kunt maximaal 3 monsterstenen per keer bestellen.';
      }

      if (count($errors) == 0) {
        $this->updateBasketInSession($basket);
      }
      $this->errors = array_merge($this->errors, $errors);
      $basket = $this->sortBasket($basket);

      return $basket;
    }

    public function executePay0() {
      $this->errors = [];
      $this->updateBasket($_SESSION['basket']);
      if (count($this->errors) > 0) {
        MessageFlashCoordinator::addMessageAlerts($this->errors);
        ResponseHelper::redirect(PageMap::getUrl("M_BASKET"));
      }
      $this->forward('register', 'login', ["fromwebshop" => true]);
    }

    /**
     * Verzendkosten
     */
    public function executePay1() {
      if (!$this->tender && $this->hasRequestPrices) {
        ResponseHelper::redirectAlertMessage(
          "U kunt geen bestelling plaatsen voor producten die op aanvraag zijn. Vraag een offerte aan via de knop 'Offerte'.",
          PageMap::getUrl('M_BASKET')
        );
      }

      Context::addJavascript('/gsdfw/includes/jsscripts/vuejs/vue.3.1.5' . (DEVELOPMENT ? '' : '.min') . '.js');

      $this->step = 1;
      if (!isset($_SESSION['userObject'])) {
        ResponseHelper::redirect(reconstructQuery(['action']) . 'action=pay0');
      }
      if (empty($_SESSION['basket']['products'])) {
        ResponseHelper::redirect(PageMap::getUrl('M_BASKET'));
      }

      //validate basket
      $valid = $this->validateBasket($_SESSION['basket']);
      if ($valid !== true) {
        $_SESSION['flash_message_red'] = "U kunt nog niet afrekenen. " . implode(", ", $valid);
        ResponseHelper::redirect(PageMap::getUrl('M_BASKET'));
      }

      $errors = [];

      //defaults
      $quotation = new Quotations();
      if (isset($_SESSION["basket"]['quotation'])) {
        $quotation = $_SESSION["basket"]['quotation'];
      }
      $quotation_extra = new QuotationsExtra();
      $quotation_extra->quotationAltPriceYear = date("Y") . '-01-01';

      if (isset($_SESSION["basket"]['source_quotation'])) {
        $quotation->street = $_SESSION["basket"]['source_quotation']->street;
        $quotation->nr = $_SESSION["basket"]['source_quotation']->nr;
        $quotation->ext = $_SESSION["basket"]['source_quotation']->ext;
        $quotation->zipcode = $_SESSION["basket"]['source_quotation']->zipcode;
        $quotation->domestic = $_SESSION["basket"]['source_quotation']->domestic;
        $quotation->quotationVersion = $_SESSION["basket"]['source_quotation']->quotationVersion + 1;
        $quotation->projectName = $_SESSION["basket"]['source_quotation']->projectName;
        $quotation->customerNotes = $_SESSION["basket"]['source_quotation']->customerNotes;
        //      $quotation->addressDeliveryId = $_SESSION["basket"]['source_quotation']->addressDeliveryId;
        unset($_SESSION["basket"]['source_quotation']);
        $_SESSION["basket"]['quotation'] = $quotation;
      }

      if (isset($_SESSION['basket']['quotation'])) {
        $quotation = $_SESSION['basket']['quotation'];
      }
      if (isset($_SESSION["basket"]['quotation_extra'])) {
        $quotation_extra = $_SESSION["basket"]['quotation_extra'];
      }

      if ($quotation_extra->quotationAltPriceYear == "" || $quotation_extra->quotationAltPriceYear == "0000-00-00") {
        $quotation_extra->quotationAltPriceYear = date("Y") . '-01-01';
      }
      if ($quotation_extra->addressDeliveryId == null && $quotation->street != "") { //geen id, en staat ingevuld, dan nieuw selecteren.
        $quotation_extra->addressDeliveryId = "NEW";
      }
      elseif ($quotation_extra->addressDeliveryId == null) {
        $quotation_extra->addressDeliveryId = "";
      }

      $addresses = [];
      $newAdres = new CrmAddresses();
      $newAdres->addressId = "NEW";
      $addresses[] = $newAdres; //nieuwe afleveradres
      if ($quotation->companyId != "" && $quotation->companyId != "0") {
        $types = ["delivery"];
        if ($_SESSION['userObject']->company->noDelivery != 1) {
          $types[] = "visit";
        }
        $addresses = array_merge($addresses, CrmAddresses::find_all_by(["companyId" => $quotation->companyId, 'type' => $types], "ORDER BY domestic, street"));
      }
      else { //particulier, dan account adres standaard invullen.
        if ($quotation->street == "") { //alleen vullen als straat leeg is.
          $quotation->street = $_SESSION['userObject']->street;
          $quotation->nr = $_SESSION['userObject']->nr;
          $quotation->ext = $_SESSION['userObject']->extension;
          $quotation->zipcode = $_SESSION['userObject']->zipcode;
          $quotation->domestic = $_SESSION['userObject']->domestic;
        }
        if ($quotation_extra->addressDeliveryId == null) { //particulier pak standaard de nieuwe
          $quotation_extra->addressDeliveryId = "NEW";
        }
      }
      if ($quotation_extra->addressDeliveryId != "" && $quotation_extra->addressDeliveryId != 0) {
        $isfound = false;
        foreach ($addresses as $addr) {
          if ($addr->addressId == $quotation_extra->addressDeliveryId) {
            $isfound = true;
            break;
          }
        }
        if ($isfound != true) {
          //gekoppelde addres niet gevonden in array, kijk of je hem kunt vinden en voeg hem dan toe
          $addr_search = CrmAddresses::find_by(["addressId" => $quotation_extra->addressDeliveryId]);
          if ($addr_search) {
            $addresses[] = $addr_search;
          }
          else { //?? address id niet gevonden....leeg maken.
            $quotation_extra->addressDeliveryId = null;
          }
        }
      }
      $vrachtkosten = ShippingFactory::calculate($_SESSION['basket']['products'], "nl");
      if ($quotation_extra->addressDeliveryId != 20357) {  //NIET rde pickup address
        $addresses[] = CrmAddresses::find_by(["addressId" => 20357]);
      }

      if (isset($_POST["next"]) || isset($_POST["prev"])) {

        $quotation_extra->addressDeliveryId = $_POST['addressDeliveryId'];

        if ($quotation_extra->addressDeliveryId == "NEW") { //nieuwe adres
          $quotation_extra->addressDeliveryId = null;
          $quotation->street = trim($_POST["street"]);
          $quotation->nr = trim($_POST["nr"]);
          $quotation->ext = trim($_POST["ext"]);
          $quotation->zipcode = StringHelper::cleanZip($_POST["zipcode"]);
          $quotation->domestic = trim($_POST["domestic"]);
        }

        //bij een volgende post, zonder validatie naar vorige stap, maar wel elementen opslaan.
        if (isset($_POST["prev"])) {
          $_SESSION["basket"]['quotation'] = $quotation;
          $_SESSION["basket"]['quotation_extra'] = $quotation_extra;

          ResponseHelper::redirect(PageMap::getUrl("M_BASKET"));

        }

        if (!$quotation_extra->isPickup()) {

          if (count($errors) == 0 && $quotation_extra->addressDeliveryId != "NEW" && $quotation_extra->addressDeliveryId != "" && $quotation_extra->addressDeliveryId != 0) {
            $rde_address = CrmAddresses::find_by(["addressId" => $quotation_extra->addressDeliveryId]);
            if ($rde_address) {
              $quotation->street = $rde_address->street;
              $quotation->nr = $rde_address->nr;
              $quotation->ext = $rde_address->extension;
              $quotation->zipcode = $rde_address->zipcode;
              $quotation->domestic = $rde_address->domestic;
            }
          }

          if ($quotation->domestic == "") {
            $errors['domestic'] = "Afleverplaats mag niet leeg zijn.";
          }
        }

        if ($quotation->nr != "" && !is_numeric($quotation->nr)) {
          $errors["nr"] = "Huisnummer moet een getal zijn.";
        }

        if (count($errors) == 0) {

          if ($quotation_extra->isPickup()) { //ophalen. Vul raamdorpel address in.
            $rde_address = CrmAddresses::find_by(["addressId" => 20357]);
            $quotation->street = $rde_address->street;
            $quotation->nr = $rde_address->nr;
            $quotation->ext = $rde_address->extension;
            $quotation->zipcode = $rde_address->zipcode;
            $quotation->domestic = $rde_address->domestic;
          }

          $_SESSION["basket"]['quotation'] = $quotation;
          $_SESSION["basket"]['quotation_extra'] = $quotation_extra;

          ResponseHelper::redirect(reconstructQuery(["action"]) . 'action=pay2');

        }
      }

      $this->vrachtkosten = $vrachtkosten;
      $this->quotation = $quotation;
      $this->quotation_extra = $quotation_extra;
      $this->addresses = $addresses;
      $this->errors = $errors;

      BreadCrumbs::getInstance()->addItem('Verzendkosten');
      $this->seo_title = "Verzendkosten";
      $this->template = 'pay1RespSuccess.php';
    }

    /**
     * Selecteer betaalmethode
     */
    public function executePay2() {

      if (!isset($_SESSION['userObject'])) {
        ResponseHelper::redirect(reconstructQuery(["action"]) . 'action=pay0');
      }
      if (empty($_SESSION['basket']['products'])) {
        ResponseHelper::redirect(PageMap::getUrl('M_BASKET'));
      }
      if ($this->tender) {
        ResponseHelper::redirect(reconstructQuery(["action"]) . 'action=pay3');
      }

      $errors = [];
      $this->step = 2;

      /** @var Quotations $quotation */
      $quotation = $_SESSION['basket']['quotation'];
      $paymenthods = $this->getPaymentMethods($_SESSION["userObject"]);

      if (count($paymenthods) == 1) {
        //er is maar 1 betaalmethode. Selecteren en door.
        $quotation->paymentMethod = array_shift($paymenthods)->key;
        $_SESSION["basket"]['quotation'] = $quotation;
        ResponseHelper::redirect(reconstructQuery(["action"]) . 'action=pay3');
      }

      if (isset($_POST["next"]) || isset($_POST["prev"])) {

        if (isset($_POST["prev"])) {
          $_SESSION["basket"]['quotation'] = $quotation;
          ResponseHelper::redirect(reconstructQuery(["action"]) . 'action=pay1');
        }

        if (isset($_POST["paymethod"])) {
          $quotation->paymentMethod = $_POST["paymethod"];
        }

        if ($quotation->paymentMethod == "") {
          $errors[] = "Selecteer uw betaalmethode";
        }

        if (count($errors) == 0) {

          $_SESSION["basket"]['quotation'] = $quotation;

          ResponseHelper::redirect(reconstructQuery(["action"]) . 'action=pay3');
        }

      }

      $this->quotation = $quotation;
      $this->paymenthods = $paymenthods;
      $this->errors = $errors;
      $this->template = 'pay2RespSuccess.php';
    }

    /**
     * Overzicht bestelling
     */
    public function executePay3() {
      if (!isset($_SESSION['userObject'])) {
        ResponseHelper::redirect(reconstructQuery(["action"]) . 'action=pay0');
      }
      if (empty($_SESSION['basket']['products'])) {
        ResponseHelper::redirect(PageMap::getUrl('M_BASKET'));
      }

      $errors = [];
      $this->step = 3;

      /** @var Quotations $quotation */
      $quotation = $_SESSION['basket']['quotation'];
      /** @var QuotationsExtra $quotation_extra */
      $quotation_extra = $_SESSION['basket']['quotation_extra'];
      $paymenthods = $this->getPaymentMethods($_SESSION["userObject"]);
      $user = $_SESSION["userObject"];
      $basket = $_SESSION['basket'];

      $quotation->verzendkosten = $this->getQuotationShippping(false, $quotation_extra);
      $quotation->freightCosts = $this->getQuotationShippping(true, $quotation_extra);

      if (isset($_POST["next"]) || isset($_POST["prev"]) || isset($_POST["refresh"])) {

        $quotation->projectName = trim($_POST["projectName"]);
        $quotation->customerNotes = trim($_POST["customerNotes"]);

        if (SandboxUsers::isAdmin()) {
          $_SESSION["basket"]['freeshipping'] = isset($_POST["freeshipping"]) ? 1 : 0;

          $quotation->verzendkosten = $this->getQuotationShippping(false, $quotation_extra);
          $quotation->freightCosts = $this->getQuotationShippping(true, $quotation_extra);


          if (trim($_POST["admin_extra_desc"]) != "" || !empty($_POST["admin_extra_amount"])) {
            $_SESSION["basket"]['admin_extra_desc'] = trim($_POST["admin_extra_desc"]);
            $_SESSION["basket"]['admin_extra_amount'] = $_POST["admin_extra_amount"];
            if ($_POST["admin_extra_desc"] == "") {
              $errors["admin_extra_desc"] = "Admin product omschrijving is verplicht.";
            }
          }

        }

        if (isset($_POST["prev"])) {
          $_SESSION["basket"]['quotation'] = $quotation;
          $url = reconstructQuery(["action"]) . 'action=pay2';
          if (count($this->getPaymentMethods($_SESSION["userObject"])) < 2 || $this->tender) {
            $url = reconstructQuery(["action"]) . 'action=pay1';
          }
          ResponseHelper::redirect($url);
        }

        if ($quotation->projectName == "") {
          $errors["projectName"] = "Uw referentie is verpicht.";
        }

        if (isset($_POST["refresh"])) {
          $_SESSION["basket"]['quotation'] = $quotation;
          ResponseHelper::redirect(reconstructQuery());
        }

        if (count($errors) == 0) {

          $save = true;

          $quotation->meters = 0;
          $quotation->weight = 0;

          $projects = [];
          foreach ($basket['products'] as $tel => $item) {
            $product = $item['product'];
            $project = new Projects();
            $project->name = $item['name'];
            $project->size = $item['size'];
            $project->pieceprice = $item['pieceprice'] == "" ? 0 : $item['pieceprice'];
            $project->euro = $project->size * $project->pieceprice;
            $project->product_id = $product->id;
            $project->orderNr = $tel + 1; //ordernummer start bij 1
            $project->showOnProductionPage = 1;
            $project->productFromWebshop = 1;
            $project->webshopOnly = 1;
            if (isset($item['stone_id'])) {
              $project->stone_id = $item['stone_id'];
            }
            if (isset($item['mitre_id'])) {
              $project->mitre_id = $item['mitre_id'];
            }
            if (isset($item['glaced_left'])) {
              $project->glaced_left = 1;
            }
            if (isset($item['glaced_right'])) {
              $project->glaced_right = 1;
            }
            $projects[] = $project;

            if (isset($item["elementlength"])) {
              $quotation->meters += round(($project->size * $item["elementlength"]) / 1000, 2);
            }
            $quotation->weight += $product->weight;

          }

          if (isset($_SESSION["basket"]['admin_extra_desc']) || isset($_SESSION["basket"]['admin_extra_amount'])) {
            $project = new Projects();
            $project->name = $_SESSION["basket"]['admin_extra_desc'];
            $project->size = 1;
            $project->pieceprice = $_SESSION["basket"]['admin_extra_amount'] == "" ? 0 : $_SESSION["basket"]['admin_extra_amount'];
            $project->euro = $project->size * $project->pieceprice;
            $project->orderNr = count($projects) + 1;
            $project->showOnProductionPage = 0;
            $project->productFromWebshop = 1;
            $project->webshopOnly = 1;
            $projects[] = $project;
          }

          $quotation->codeId = 31;
          if (isset($user->company) && $user->company) {
            $customer_code = CustomerCodes::getCustomerCodeByCompany($user->company);
            if ($customer_code && $customer_code->codeId != '') {
              $quotation->codeId = $customer_code->codeId;
            }
          }
          $quotation->userId = $_SESSION['userObject']->userId;
          $quotation->companyId = $_SESSION['userObject']->companyId;
          $quotation->projectReference = "";
          $quotation->statusId = Status::STATUS_NEW; //offerte
          $quotation->quotationDate = date('Y-m-d');
          $quotation->priceDate = date('Y-m-d');
          $quotation->projectValue = 0;
          $quotation->stoneAmount = 0;
          $quotation->stonesFacilitated = 'false';
          $quotation->pickup = $quotation_extra->isPickup() ? 'true' : 'false';
          $quotation->zipcodeMap = str_replace(" ", "", $quotation->zipcode); //kopie
          $quotation->country = 'NL';
          $quotation->createdVia = Quotations::CREATED_VIA_WEBSHOP;
          $quotation->internNotes = $quotation->customerNotes; //kopie
          $quotation->metersMuch = $quotation->meters >= 90 ? 'true' : 'false';

          $total = $_SESSION["basket"]["subtotal"] + $quotation->verzendkosten;
          if (!empty($_SESSION["basket"]["admin_extra_amount"])) {
            $total += (float)$_SESSION["basket"]["admin_extra_amount"];
          }
          if ($total == 0) {
            //er is een extra admin kortingregels, waardoor er een 0 euro bedrag kan ontstaat.
            //flag zodat dit opvalt in beheer.
            $quotation_extra->quoteInvoiceAlertFlag = 1;
            $quotation_extra->quoteInvoiceAlertInfo = "0 euro bedrag";
          }

          if ($save) $quotation->save();

          $quotation->quotationNumber = date("y") . '.' . sprintf("%06d", $quotation->quotationId);
//        $quotation->projectValue = $basket['subtotal'];
          $quotation->dueDate = date('Y-m-d', strtotime("+1 WEEK"));
          $quotation->dueDateWeek = date('W', strtotime("+1 WEEK"));


          //address stuff
          if ($quotation_extra->addressDeliveryId != "") {
            $crmAddress = CrmAddresses::find_by(["addressId" => $quotation_extra->addressDeliveryId]);
          }
          else {
            $crmAddress = CrmAddresses::getAddressObj($quotation);
            if ($crmAddress->addressId == "") $crmAddress->save(); //nieuwe, even opslaan
            $quotation_extra->addressDeliveryId = $crmAddress->addressId;
          }

          if ($crmAddress && $crmAddress->addressId != "" && $_SESSION['userObject']->lastDeliveryAddressId != $crmAddress->addressId) {
            $luser = SandboxUsers::getUserAndCompany($_SESSION['userObject']->userId);
            $luser->lastDeliveryAddressId = $crmAddress->addressId;
            if ($save) $luser->save();
            $_SESSION['userObject'] = $luser;
          }

          if ($_SESSION['userObject']->companyId != "") {
            $company = $_SESSION['userObject']->company;

            $quotation_extra->maxMeasureElement = 2400;

            $quotation_extra->callToDelivery = $company->callToDelivery;
            $quotation_extra->executorTicket = $company->executorTicket;
            $quotation_extra->rackContainerReturn = $company->rackContainerReturn;

            $quotation->noRackQuotations = $company->noRack;
            $quotation->sendMailToClient = $company->sendMailToClient;
            $quotation->callOrEmailNotes = $company->callOrEmailNotes;
            $quotation->noContainerQuotations = $company->noContainer;
            $quotation->noRackNoContainerQuotations = $company->noRackNoContainer;
            $quotation->palletQuotations = $company->pallet;
            $quotation->toNumberQuotations = $company->toNumber;
            $quotation->afhalenQuotations = $company->afhalen;
          }
          else {
            $quotation_extra->maxMeasureElement = 2400;
          }

//        $quotation_extra->stoneSizeWidth = $stone_size->width;
          $quotation_extra->seamColorId = 1;
          $quotation_extra->quotationId = $quotation->quotationId;

//        $quotation->setMatting($quotation_extra, $prices);


          if ($save) {
            if ($quotation->quotationId != "") {
              //deze is al opgeslagen...verwijdere oude projects.
              Projects::delete_by(["quotationId" => $quotation->quotationId]);
            }
            $quotation->save();
            $quotation_extra->save();
            foreach ($projects as $ep) {
              $ep->quotationId = $quotation->quotationId;
              $ep->save();
            }
          }

          $_SESSION['order_saved'] = $quotation;

          if ($this->tender) {

            MailsFactory::sendTenderEmailWebshop($quotation);

            $this->clearBasketSession();
            ResponseHelper::redirect(reconstructQuery(['action']) . 'action=payfinished&tender=1&quotationId=' . $quotation->quotationId);
          }
          elseif ($quotation->paymentMethod == Payment::PAYMENT_OVERMAKEN) {

            $quotation->statusId = Status::STATUS_ORDER; //besteld
            $quotation->productionDate = date("Y-m-d"); //besteldatum
            $quotation->save();
            MailsFactory::sendOrderMail($quotation);

            $this->clearBasketSession();
            ResponseHelper::redirect(reconstructQuery(['action']) . 'action=payfinished&quotationId=' . $quotation->quotationId);
          }
          elseif ($quotation->paymentMethod == Payment::PAYMENT_MOLLIE) {
            $paymentobj = $quotation->getPaymentObject();
            $paymentobj->handlePayment($quotation, $basket['subtotal_inc'] + (1.21 * $quotation->verzendkosten));
          }
          ResponseHelper::redirectError("Onbekende betaalmethode?? [" . $quotation->paymentMethod . "]");
        }

      }

      $this->quotation = $quotation;
      $this->quotation_extra = $quotation_extra;
      $this->paymenthods = $paymenthods;
      $this->errors = $errors;
      $this->template = 'pay3RespSuccess.php';
      $this->basket = $basket;
    }

    public function executePayfinished() {

      $succesfullresult = false;
      $this->succesfullresult = $succesfullresult;

      if (isset($_GET['quotationId'])) {
        $quotation = Quotations::getById($_GET['quotationId']);
        if (!$quotation) {
          $this->message = "Betaling niet geslaagd. Bestelling is niet gevonden!";
        }
        elseif ($quotation->paymentMethod == Payment::PAYMENT_MOLLIE) {
          $this->message = __("BEDANKT_OVERMAKEN_DEFAULT");

          if ($quotation->statusId == Status::STATUS_ORDER) {
            $this->clearBasketSession();
            $succesfullresult = true;
          }
          else {
            // (nog)niet betaald via postback. Even ophalen van mollie, om betere melding te geven.
            $this->message = __("Er is iets mis gegaan met uw betaling. Probeert u het op een later stadium nogmaals, of neem contact met ons op.");
            if ($quotation->mollie_id != "") {
              try {
                $mollie = PaymentMollie::getMollie();
                $m = Mollie::find_by_id($quotation->mollie_id);
                $payment = $mollie->payments->get($m->paymentId);
                if ($payment->isPaid()) {
                  //toch betaald!
                  $quotation->statusId = 20; //even simuleren betaald.
                  $this->clearBasketSession();
                  $succesfullresult = true;
                }
                elseif ($payment->isPending()) {
                  $this->message = __("Uw betaling is in verwerking, maar nog niet bevestigd.<br/>Zodra deze bevestigd is ontvangt u van ons bericht.<br/>");
                }
                elseif ($payment->isCanceled()) {
                  $this->message = __("U heeft uw betaling geannuleerd.<br/>Heeft u vragen over onze producten neem dan gerust contact op.");
                }
              }
              catch (Mollie\Api\Exceptions\ApiException $e) {
                logToFile('order', 'mollie Mollie_API_Exception:' . $e->getMessage());
              }
            }
            if (!$succesfullresult) {
              $this->executePayerror();
              return;
            }
          }
        }
        elseif ($quotation->paymentMethod == Payment::PAYMENT_OVERMAKEN) {
          if ($this->tender) {
            $this->message = "Bedankt voor uw offerte aanvraag.<br/>U heeft een email ontvangen met uw offerte, en uitleg hoe u deze kunt omzetten naar een bestelling.<br/>U kunt uw offerte ook terugvinden in uw offerte geschiedenis.";
          }
          else {
            $this->message = "Bedankt voor uw bestelling.<br/>U heeft een email ontvangen met uw bestelling, en uitleg hoe u het bedrag kunt overmaken naar onze rekening.<br/>U kunt uw bestelling ook terugvinden in uw offerte/bestelgeschiedenis.";
          }
          $succesfullresult = true;
        }
      }
      else {
        $this->message = "Onbekende bestelling.";
      }
      $this->seo_title = "Bestelling afgerond";
      $this->template = 'payfinishedRespSuccess.php';
      $this->succesfullresult = $succesfullresult;
      $this->step = 4;
    }

    public function sortBasket($l_basket) {
      if (!empty($l_basket['products'])) usort($l_basket['products'], "basketRdeActions::sortByName");
      return $l_basket;
    }

    public static function sortByName($a, $b) {
      return strcmp($a["name"], $b["name"]);
    }

    public function executeOpenorder() {
      $quotation = Quotations::getById($_GET["quotationId"]);
      $projects = Projects::find_all_by(["quotationId" => $quotation->quotationId]);
      $_SESSION['basket']['products'] = [];
      foreach ($projects as $project) {

        unset($_POST['size'], $_POST['stoneId'], $_POST['product_id'], $_POST["mitre"]);

        if ($project->stone_id != "") {
          $name = '';

          //pak altijd de standaard steen als stoneId
          $stone = Stones::find_by(["stoneId" => $project->stone_id]);
          $product = Product::find_by_id($project->product_id);
          if ($stone->endstone != "false") {
            $sibelings = $stone->getSibelings();
            $stone = $sibelings["false"];
            $product = Product::find_by(["supplier_code" => "STONE_" . $stone->stoneId]);
          }
          $_POST['stoneId'] = $stone->stoneId;
          $_POST['product_id'] = $product->id;
          if ($project->mitre_id != "") {
            if (strpos($project->name, "Verstek links") !== false) {
              $name = "leftmitre";
              $_POST["mitre"]["leftmitreId"] = $project->mitre_id;
            }
            else {
              $name = "rightmitre";
              $_POST["mitre"]["rightmitreId"] = $project->mitre_id;
            }
          }
          $stone = Stones::find_by(["stoneId" => $project->stone_id]);
          if ($stone->endstone == "left" || $stone->endstone == "right" || $stone->endstone == "leftg") {
            $name = $stone->endstone;
          }
          if ($project->glaced_left == 1) {
            $name = "leftglaced";
          }
          elseif ($project->glaced_right == 1) {
            $name = "rightglaced";
          }
          $_POST['size'][$name] = $project->size;
        }
        else {
          $_POST['size'][$project->product_id] = $project->size;
        }
        $this->addToBasket($_SESSION['basket'], true);
      }

      $_SESSION["basket"]['source_quotation'] = $quotation;

      $_SESSION['flash_message'] = __("Producten uit offerte toegevoegd aan winkelmandje.");
      ResponseHelper::redirect(PageMap::getUrl("M_BASKET"));
    }


    /**
     * Afrekenen via mollie betaalink
     */
    public function executePaylinkinvoice() {

      if (!isset($_GET["id"])) {
        $this->error = __("We hebben uw factuur niet kunnen vinden.<br/>Controleer of u de link in zijn geheel heeft gekopieërd naar de browser.");
        return;
      }

      $invoice = Invoices::find_by(["invoiceId" => $_GET["id"]]);
      if (!$invoice || $invoice->getHashCode() != $_GET["key"]) {
        $this->error = __("We hebben uw factuur niet kunnen vinden.<br/>Controleer of u de link in zijn geheel heeft gekopieërd naar de browser.");
        return;
      }

      if ($invoice->paid != "") {
        $this->error = __("De factuur %s is reeds afgerekend.", $invoice->invoiceNumber);
        return;
      }

      if ($invoice->totalProjectValue == 0) {
        $this->error = __("De factuur %s kunt u niet online afrekenen. Het totaal bedrag van de factuur is 0 euro.", $invoice->invoiceNumber);
        return;
      }

      if (isset($_GET["go"])) {
        $paymentobj = new PaymentMollie();
        $paymentobj->handlePaymentInvoiceLink($invoice, __("Factuur " . $invoice->invoiceNumber));
      }

      $this->invoice = $invoice;

    }

    /**
     * Terug vanuit mollie betaling.
     */
    public function executePaylinkinvoicedone() {

      if (!isset($_GET["id"])) {
        $this->error = __("We hebben uw factuur niet kunnen vinden.");
        return;
      }
      $invoice = Invoices::find_by(["invoiceId" => $_GET["id"]]);
      if (!$invoice) {
        $this->error = __("We hebben uw factuur niet kunnen vinden.");
        return;
      }
      $message = "";
      if ($invoice->paid == "") {
        //betaling succesvol
        // (nog)niet betaald via postback. Even ophalen van mollie, om betere melding te geven.
        $message = __("Er is iets mis gegaan met uw betaling.<Br/>Probeert u het op een later stadium nogmaals.");
        if ($invoice->mollie_id != "") {
          try {
            $m = Mollie::find_by_id($invoice->mollie_id);
            $mollie = PaymentMollie::getMollie();
            $payment = $mollie->payments->get($m->paymentId);
            //echo $payment->status;
            if ($payment->isPaid()) {
              //toch betaald!
            }
            elseif ($payment->isPending()) {
              $message = __("Uw betaling is in verwerking, maar nog niet bevestigd.<br/>Zodra deze bevestigd is ontvangt u van ons bericht.");
            }
            elseif ($payment->isCanceled()) {
              $message = __("U heeft uw betaling geannuleerd.");
            }
          }
          catch (Mollie\Api\Exceptions\ApiException $e) {
            logToFile('order', 'mollie Mollie_API_Exception:' . $e->getMessage());
          }
        }
      }

      $this->message = $message;
      $this->invoice = $invoice;

    }

    private function getQuotationShippping($forDb, $quotation_extra) {
      if ((!isset($_SESSION["basket"]['freeshipping']) || $_SESSION["basket"]['freeshipping'] == 0) && !$quotation_extra->isPickup()) {
        if ($forDb) {
          //we gaan de verzendkosten van 50 euro bij een standaard element niet in de database opslaan, wel op het scherm tonen.
          foreach ($_SESSION['basket']['products'] as $prodvault) {
            $product = $prodvault["product"];
            $lengths = ProductOption::getOptionValue($product->id, "lengths");
            if ($lengths != false && $lengths != "") {
              //er zijn standaard elment lengtes
              return 0;
            }
          }
        }
        return ShippingFactory::calculate($_SESSION['basket']['products'], "nl");
      }
      return 0;
    }

  }
