<?php
  TemplateHelper::includePartial('_tabs.php', 'sandbox');
?>

<style>
  #google_maps {
    display: block !important;
  }
</style>

<h1>
  <?php if(empty($address->addressId)): ?>
    Nieuw adres aan<PERSON>ken
  <?php else: ?>
    <?php echo CrmAddresses::TYPES[$address->type] . ' ' . $address->street . ' ' . $address->nr ?>
  <?php endif; ?>
</h1>

<?php writeErrors($form_address->getErrors(), true); ?>

<?php include("_header.php") ?>

<form method="post" name="form" id="form" class="edit-form">

  <div style="display: flex">
  <table class="default_table" style="width: auto; min-width: 600px;">
    <tr class="dataTableHeadingRow">
      <td colspan="2">Adresgegevens</td>
    </tr>
    <?php foreach ($form_address->getElements() as $element): ?>
      <?php $element->renderRow() ?>
    <?php endforeach; ?>

  </table>

  <table style="margin-top: 10px; margin-left: 25px;">
    <tr class="dataTableRow">
<!--      <td class="head">--><?php //echo __('Coördinaten (Lat, Long)'); ?><!--</td>-->
      <td>
        <input type="text" name="lat" id="lat" placeholder="latitude..." value="<?php echo $address->latitude; ?>" style="width: 150px;" /> <span class="asterisk">*</span>
        <input type="text" name="lng" id="lng" placeholder="longitude..." value="<?php echo $address->longitude; ?>" style="width: 150px;" /> <span class="asterisk">*</span>
        <input type="button" id="get_coordinates" value="<?php echo __('Coördinaten ophalen');?>" title="<?php echo __('Coördinaten ophalen a.d.h.v. adres gegevens'); ?>"/>
        <br/>
        <br/>
        <div id="google_maps" style="width: 500px; height: 300px; display: none;"></div>
        <input id="pac-input" type="text" placeholder="<?php echo __('Ga naar een locatie'); ?>" style="display: none;" />
      </td>
  </table>
  </div>

  <br/><br/>
  <input type="submit" name="go" value="Opslaan" class="gsd-btn gsd-btn-primary"/>
  <input type="submit" name="go_list" value="Opslaan en naar lijst" class="gsd-btn gsd-btn-secondary"/>

</form>

<script type="text/javascript">
  let map;
  let marker;
  const startup = true;

  async function initmap() {
    // Import the Advanced Marker library
    const { AdvancedMarkerElement } = await google.maps.importLibrary("marker");

    const mapOptions = {
      center: new google.maps.LatLng(52.2, 5.5),
      zoom: 15,
      scrollwheel: false,
      mapTypeId: google.maps.MapTypeId.ROADMAP,
      mapId: "route_map_id" // Required for Advanced Markers
    };

    map = new google.maps.Map(document.getElementById("google_maps"), mapOptions);

    <?php if($address->latitude != "" && $address->longitude != "") : ?>
    const latlng = new google.maps.LatLng(<?php echo $address->latitude; ?>, <?php echo $address->longitude; ?>);

    marker = new AdvancedMarkerElement({
      position: latlng,
      map: map,
      gmpDraggable: true
    });

    map.setCenter(latlng);

    marker.addListener('drag', function() {
      const position = marker.position;
      $("#lat").val(position.lat);
      $("#lng").val(position.lng);
    });
    <?php endif; ?>
  }

  $(document).ready(function(){
    initmap();

    $("#get_coordinates").click(function() {
      const url = '<?php echo reconstructQueryAdd(array('pageId')) ?>action=findlatlng';
      const street = trim($("#street").val());
      const nr = trim($("#nr").val());
      const city = $("#city").val();
      const country = $("#country").val();
      const zip = $("#zipcode").val().replace(/\s+/g, '');
      let requestUrl = url;

      if (street) requestUrl += '&street=' + encodeURIComponent(street);
      if (nr) requestUrl += '&nr=' + encodeURIComponent(nr);
      if (city) requestUrl += '&city=' + encodeURIComponent($("#domestic").val());
      if (country) requestUrl += '&country=' + encodeURIComponent(country);
      if (zip) requestUrl += '&zipcode=' + encodeURIComponent(zip);

      $.getJSON(requestUrl, async function(data) {
        const { AdvancedMarkerElement } = await google.maps.importLibrary("marker");

        if(!data) {
          swal("Kaart","We kunnen de locatie van dit adres op de kaart niet vinden. Controleer of u het adres wel kunt vinden met google maps, of versleep de marker handmatig op de juiste plaats.");

          // Show marker at owner location
          const latLng = new google.maps.LatLng(<?php echo $_SESSION['userObject']->organisation->lat ?>, <?php echo $_SESSION['userObject']->organisation->lng ?>);

          if(!marker) {
            marker = new AdvancedMarkerElement({
              position: latLng,
              map: map,
              gmpDraggable: true
            });

            marker.addListener('drag', function() {
              const position = marker.position;
              $("#lat").val(position.lat);
              $("#lng").val(position.lng);
            });
          }
          else {
            marker.position = latLng;
            marker.map = map;
          }

          map.setCenter(latLng);
          $("#lat").val(<?php echo $_SESSION['userObject']->organisation->lat ?>);
          $("#lng").val(<?php echo $_SESSION['userObject']->organisation->lng ?>);
        }
        else {
          const latLng = new google.maps.LatLng(data.latitude, data.longitude);

          if(typeof latLng !== 'undefined') {
            if(!marker) {
              marker = new AdvancedMarkerElement({
                position: latLng,
                map: map,
                gmpDraggable: true
              });

              marker.addListener('drag', function() {
                const position = marker.position;
                $("#lat").val(position.lat);
                $("#lng").val(position.lng);
              });
            }
            else {
              marker.position = latLng;
              marker.map = map;
            }

            map.setCenter(latLng);
            $("#lat").val(data.latitude);
            $("#lng").val(data.longitude);
          }
        }
      });
    });
  });
</script>