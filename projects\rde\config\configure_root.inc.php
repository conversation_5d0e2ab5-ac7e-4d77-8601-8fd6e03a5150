<?php

  const PROJECT = 'rde';

  const DIR_PROJECT_FOLDER = DIR_ROOT . 'projects/' . PROJECT . '/';
  const URL_PROJECT_FOLDER = '/projects/' . PROJECT . '/';
  const URL_UPLOADS = '/uploads/' . PROJECT . '/';
  const DIR_UPLOADS = DIR_ROOT . 'uploads/' . PROJECT . '/';
  const URL_TEMP = '/temp/';
  const DIR_TEMP = DIR_ROOT . 'temp/';
  const DIR_ROOT_GSDFW = DIR_ROOT . 'gsdfw/';
  const DIR_CLASSES = DIR_ROOT_GSDFW . 'includes/classes/';
  const URL_INCLUDES = '/gsdfw/includes/';
  const DIR_INCLUDES = DIR_ROOT_GSDFW . 'includes/';
  const DIR_MODULES = DIR_ROOT_GSDFW . 'modules/';
  const CONFIGURE_ADDPRODUCTS_IN_ROOTDIR = false; //Voeg producten toe aan de root.
  const CONFIGURE_MAX_DdEPTH_NESTED_CATS = 4; //Maximaal genestte niveau van categorieën.
  const CONFIGURE_ADD_SAME_PRODUCT_MULTIPLE_CATS = false; //Hetzelfde product onder meerdere categorieën hangen.
  Config::set('PRODUCT_RESELLING', false);
  //Config::set("PRODUCT_ONLINE_CATEGORY_ONLINE_LINKED", false);
  const DIR_LANGUAGES = DIR_ROOT_GSDFW . 'includes/languages/';
  const DIR_LANGUAGES_PROJECT = DIR_PROJECT_FOLDER . 'languages/';
  const LOG_FOLDER = DIR_ROOT . "logs/";
  const LOGLEVEL = "DEBUG";//DEBUG is only active string at this moment

  const HASH_STRING = '#34tkdf';
  const ADMIN_DEFAULT_ID = 2;

  const DIR_UPLOAD_CAT = DIR_UPLOADS . 'images/catalog/';
  const URL_UPLOAD_CAT = URL_UPLOADS . 'images/catalog/';
  const DIR_UPLOAD_BRAND = DIR_UPLOADS . 'images/brands/';
  const URL_UPLOAD_BRAND = URL_UPLOADS . 'images/brands/';
  const IMAGES_THUMB_WIDTH = 360; //product image
  const IMAGES_THUMB_HEIGHT = 207; //product image
  const IMAGES_ORIG_WIDTH = 1920; //product image
  const IMAGES_ORIG_HEIGHT = 1280; //product image
  const IMAGES_BRAND_THUMB_WIDTH = 170; //brand image
  const IMAGES_BRAND_THUMB_HEIGHT = 100; //brand image
  const IMAGES_BRAND_ORIG_WIDTH = 700; //brand image
  const IMAGES_BRAND_ORIG_HEIGHT = 600; //brand image
  const IMAGES_CAT_ORIG_RESIZE = false; //cat image
  const IMAGES_CAT_THUMB_WIDTH = 240; //cat image
  const IMAGES_CAT_THUMB_HEIGHT = 190; //cat image
  const IMAGES_CAT_ORIG_WIDTH = 690; //cat image
  const IMAGES_CAT_ORIG_HEIGHT = 280; //cat image
  const IMAGES_PAGE_THUMB_WIDTH = 360; //page image
  const IMAGES_PAGE_THUMB_HEIGHT = 600; //page image
  const IMAGES_PAGE_PREVIEW_WIDTH = 1920; //page image
  const IMAGES_PAGE_PREVIEW_HEIGHT = 1280; //page image
  const IMAGES_PAGE_THUMBPREVIEW_WIDTH = 130; //page image
  const IMAGES_PAGE_THUMBPREVIEW_HEIGHT = 130; //page image
  const IMAGES_CAR_THUMB_WIDTH = 250; //car image
  const IMAGES_CAR_THUMB_HEIGHT = 188; //car image
  const IMAGES_CAR_PREVIEW_WIDTH = 700; //car image
  const IMAGES_CAR_PREVIEW_HEIGHT = 600; //car image
  const DIR_UPLOAD_PDF = DIR_UPLOADS . 'pdf/';
  const URL_UPLOAD_PDF = 'uploads/pdf/';

  const DIR_SITE = DIR_PROJECT_FOLDER . 'sites/';
  const URL_SITE = URL_PROJECT_FOLDER . 'sites/';
  const DIR_TEMPLATE = DIR_PROJECT_FOLDER . 'templates/';
  const URL_TEMPLATE = URL_PROJECT_FOLDER . 'templates/';
  const DIR_UPLOADS_SITE = DIR_UPLOADS . 'sites/';
  const URL_UPLOADS_SITE = URL_UPLOADS . 'sites/';

  //pagina beheer

  const DISABLE_1STLEVEL_CREATENEW = false;
  const DISABLE_1STLEVEL_EDIT = false;
  const DISABLE_1STLEVEL_DELETE = false;
  const DISABLE_1STLEVEL_MOVE = false;

  Config::set('PAGE_GALLERY_ENABLED', true); //Er mogen foto gallerijen aangemaakt worden. (tab: Gallerij in pagina beheer)


  const SEOTITLE = true;
  const SEODESCRIPTION = true;
  const TAGS = true;
  const TAGS_PRODUCT = false;

  const NROFNAVIGATIONLEVELS = 5;
  const IMAGES_EXIF = false; //retrieve lat/long of image


  Config::set("catalog_languages", ['nl']); //languages for catalog/product descriptions

  Config::set("organisation_types_usg", [
    'ADMIN' => ['BEDRIJF'],
  ]); //welke organisatie.types mag een usergroup aanmaken [USER.USERGROUP=>[ORGANISATION.TYPE,ORGANISATION.TYPE,...]]
  Config::set("usergroups_for_usg", [
    'SUPERADMIN' => [
      'ADMIN',
      'BEDRIJF',
      'WEBSITE_MANAGER',
      'LEVERANCIER',
      'PLANNER',
      'PRODUCTION',
    ],
    'ADMIN'      => [
      'ADMIN',
      'BOEKHOUDER',
      'WEBSITE_MANAGER',
      'LEVERANCIER',
      'PLANNER',
      'PRODUCTION',
    ],
  ]); //welke gebruikersgroepen mag een bepaalde gebruikersgroep zien/bewerken [USER.USERGROUP=>[USER.USERGROUP,USER.USERGROUP,...]]
  Config::set("usergroups_for_organtype", [
    'OTHER'   => ['ADMIN', 'BOEKHOUDER', 'WEBSITE_MANAGER', 'LEVERANCIER', 'PLANNER','PRODUCTION'],
    'BEDRIJF' => ['WEBSITE_MANAGER'],
  ]); //welke gebruikersgroepen kan een bepaald bedrijfstype hebben [ORGANISATION.TYPE=>[USER.USERGROUP,USER.USERGROUP,...]]
  Config::set("LOGIN_BACKEND_USERGROUPS", [
    'SUPERADMIN',
    'ADMIN',
    'BOEKHOUDER',
    'WEBSITE_MANAGER',
    'LEVERANCIER',
    'PLANNER',
    'PRODUCTION'
  ]); //usergroups welke mogen inloggen op de backend [USER.USERGROUP,USER.USERGROUP,...]

  const CATALOG_BUY_DISCOUNT = false; //inkoopkorting aangeven
  const CATALOG_BRAND = true; //producten aan merken koppelen
  Config::set('CATALOG_PRODUCT_BTWGROUP', [
    'nl' => [1 => 9, 2 => 21],
//    'be' => array(1 => 21, 3 => 21, 2 => 21),
  ]); //producten aan btwgroep kunnen koppelen
  Config::set('CATALOG_BRAND_OBLIDGED', false); //merk verplicht invoeren bij product bewerken

  Config::set('PAGES_MAX_PAGE_COUNT', [
    'free'     => 0,
    'standard' => 100,
    'large'    => 100,
  ]); //maximale aantal pagina's aan te maken per abo
  Config::set('ORGANISATION_EMAIL_OBLIDGED', false); //organisatie is verplicht emailadres in te vullen



  Config::set("PAGES_FRONTEND_IMAGES_PER_ROW", 3); //aantal beelden per rij
  Config::set('FRONTEND_PAGER_SIZE', 24); //aantal producten per pagina

  Config::set('PAGE_CKEDITOR_TOOLBAR', 'Full'); //Simple/Full toolbar to load ckeditor
  Config::set('PAGE_CKEDITOR_FILEBROWSER', true); //use ckeditor filebrowser
  Config::set('PAGE_IMAGES_NOT_RESIZE_PAGEIDS', [8]); //paginaids van pagina's waarvan we de large formaat niet aanpassen. Bijv fotoslider pagina
  Config::set('PAGE_IMAGES_CKEDITOR_PAGEIDS', [8]); //paginaids van pagina's waarvan we de textarea vervangen door de ckeditor

  //CROPPING TOOL
  Config::set('PAGE_IMAGES_CROPPING_TOOL', false); //'ALL', paginaids van pagina's waarvan we de beelden kunnen croppen middels de croppingtool.
  Config::set('PAGE_IMAGES_CROPPING_TOOL_TO_RESIZE', ['ORIG' => 'ORIG']); //Welke formaten er met de tool gecropt mogen worden.
  Config::set('PAGE_IMAGES_CROPPING_TOOL_RESIZE_TO_ORIG', [8]); //'ALL' || pageids OR parent_id. Bij de opgegeven paginaids zal de large(orig) foto worden overschreven na cropping.
  Config::set('PAGE_IMAGES_CROPPING_TOOL_RESIZE_TO_THUMB', 'ALL'); //'ALL' || pageids OR parent_id. Bij de opgegeven paginaids zal de thumb foto worden overschreven na cropping.
  Config::set('PAGE_IMAGES_CROPPING_TOOL_RESIZE_TO_ORIG_SIZE', [
    8 => [
      1951,
      555,
    ],
  ]); //'ALL' || pageid || parent_id. Array(Breedte, Hoogte). Naar welke aspect-ratio en grootte de large foto gerescaled of gecropt dient te worden.
  Config::set('PAGE_IMAGES_CROPPING_TOOL_RESIZE_TO_THUMB_SIZE', [
    IMAGES_PAGE_THUMB_WIDTH,
    IMAGES_PAGE_THUMB_HEIGHT,
  ]); //Breedte, Hoogte. Naar welke aspect-ratio en grootte de thumb foto gerescaled of gecropt dient te worden.


  Config::set("GSD_API_AUTHENTICATION_KEY", '423432686d8d63d387d63A!@$fewifuwe78822d2guxg27g2dueguey'); //authentication key

  const PICKUP_ADDRESS_ID = 20357; //ophalen thuis default addressid (".CrmAddresses::getTablename().")

  const DIR_PLUGIN_FOLDER = DIR_ROOT_GSDFW . 'plugins/';
  const URL_PLUGIN_FOLDER = '/gsdfw/plugins/';
  Config::set("GSDFW_PLUGINS", ['timeregistration', 'faq', 'packs']);
  Config::set("GSDFW_PLUGIN_TIMEREGISTRATION_EXTERNAL_ID", true);//gebruikt external id
  Config::set("GSDFW_TIMEREGISTRATION_FUNCTIONS", [
    "voegen"               => "Voegen",
    "vrachtwagenchauffeur" => "Vrachtwagenchauffeur",
    "zagen"                => "Zagen",
  ]);
  Config::set("GSDFW_TIMEREGISTRATION_PDFLOGO", DIR_PROJECT_FOLDER . 'templates/backend2/images/logopdf.jpg');
  Config::set("GSDFW_TIMEREGISTRATION_PDF_WEEKLYROUND", "NONE");
  Config::set("GSDFW_TIMEREGISTRATION_PDF_HOURS_ADDYEAROV", true);
  Config::set("TIMEREGISTRATION_WORKER_PROFILE", [
    "password"        => [
      "name" => "Wachtwoord code App NFC",
      "type" => "text",
    ],
    "passwordnr"      => ["name" => "Wachtwoord nr App (kaart)", "type" => "text",],
    "production_glue" => ["name" => "NFC tag id - lijmer", "type" => "text",],
    "production_wash" => ["name" => "NFC tag id - wasser", "type" => "text",],
    "app1"            => ["name" => "NFC tags & containers koppelen"],
    "app2"            => ["name" => "Inpakken"],
    "app19"           => ["name" => "Inpakken webshop"],
    "app3"            => ["name" => "Laden"],
    "app4"            => ["name" => "Transport"],
    "app5"            => ["name" => "Ophalen"],
    "app6"            => ["name" => "Containers retour"],
    "app15"           => ["name" => "Containers retour vrachtbon"],
    "app7"            => ["name" => "Container onderhoud"],
    "app11"           => ["name" => "Informatie order/container"],
    "app8"            => ["name" => "Kaart"],
    "app9"            => ["name" => "VGM-werkinspectie"],
    "app10"           => ["name" => "Montage"],
    "app12"           => ["name" => "Prikklok vrachtwagenchauffeur"],
    "app13"           => ["name" => "Stenen ophalen"],
    "app14"           => ["name" => "Voorraad verlies"],
    "app16"           => ["name" => "Containers ophalen"],
    "app17"           => ["name" => "Beschadigingen"],
    "app18"           => ["name" => "Geproduceerd"],
    "app20"           => ["name" => "Kaart containers"],
    "lockapp"         => ["name" => "App blockeren"],
  ]);

  Config::set("GSDFW_BACKEND_PRIVILIGES_OPEN", ['M_API' => 'api', 'M_WORKER_APP' => 'workerapp', 'M_VOIP' => 'voip']); //dit zijn de pagina id's welke extern bereikbaar zijn zonder standaard authenticatie id=>name

  Config::set("RDE_MAIL_ACCOUNTS", [
    "MAIL_FILE_BOX_SERVER"          => "vmail.cyberfusion.nl",
    "MAIL_FILE_BOX_SERVER_PORT"     => "993",
    "MAIL_FILE_BOX_SERVER_SECURITY" => "SSL",
    "personeel"                     => [
      "MAIL_FILE_BOX_MAIL"     => "<EMAIL>",
      "MAIL_FILE_BOX_USERNAME" => "<EMAIL>",
      "MAIL_FILE_BOX_PASSWORD" => "4qHdZt13zh",
    ],
    "document"                     => [
      "MAIL_FILE_BOX_MAIL"     => "<EMAIL>",
      "MAIL_FILE_BOX_USERNAME" => "<EMAIL>",
      "MAIL_FILE_BOX_PASSWORD" => "4qHdZt13zh",
    ],

  ]);

  Config::set("GPSBUDDY", [
    "email"     => "<EMAIL>",
    "password"  => "BnYufSlN",
    "companyId" => "7970",
    "userId"    => "230085",
  ]);

  Config::set("MESSAGECOORDINATOR_ENABLED", true); //messagecoordinator enabled
  Config::set("MESSAGECOORDINATOR_TYPES", [
    "project",
  ]); //01-03-2017 - ROBERT - zet notifications (messages) aan in beheer, en welke.

  Config::set('PAGE_CUSTOM_URL', true);

  Config::set('RDE_PEPPER', 'a645dfd1c6c315692768');

  Config::set("PRODUCT_USE_BRUTO_PRICE", false); //Men kan adviesprijs (bruto prijs) invoeren bij product.
  Config::set('PRODUCT_DISCOUNT_PRICE', true); //13-03-2018 - ROBERT = Toon product aanbieding in beheer bij product bewerken. Is deze niet gedefinieerd dan staat is dit zichtbaar.
  Config::set('PRODUCT_USE_STAFFEL', true); //17-12-2015 - ROBERT - product staffel prijzen mogelijk
  Config::set("PRICE_PRODUCT_DECIMALS", 4); //aantal decimalen bij bewerken factuur. Zet op 4 wanneer er ronde prijzen dienen te komen.
  Config::set("PRODUCT_DISCOUNTGROUP", true); //true/false: maakt gebruik van discountgroepen

  Config::set('PRODUCT_PRICE_BUY_DISABLED', true);//inkoopprijs is hidden
  Config::set('PRODUCT_DISCOUNT_PRICE', false);  //product edit discount prijs tonen

  Config::set('NUMBER_QUOTATIONS_THRESHOLDS', [
    "ELEMENTAMOUNT"     => 50,
    "LENGTH_DIFFERENCE" => 100, //mm
  ]);  //toNumberQuotations

  const STOCK_ENABLED = false; //Gebruikt voorraad syteem + controles.
  Config::set('IS_RESPONSIVE', true);//De website is responsive (Bij bv basket zal voor de Resp bestanden gekozen worden)
  Config::set("PAYMENT_MOLLIE", [
    'enabled'        => true,
    'api-key'        => DEVELOPMENT ? 'test_qpqVNQ2gpCqFNNTqvBWNghQ5qFTnwJ' : 'live_pUPzHcTep9SrND7SGdd7cn6JsBuxtN',
    'title'          => 'iDeal via Mollie',
    'short_title'    => 'iDeal',
    'paymentmethods' => [
      ["id" => "ideal", "name" => "iDEAL", "image" => "https://www.mollie.com/images/payscreen/methods/ideal.png"],
//      ["id" => "mistercash", "name" => "Mistercash", "image" => "https://www.mollie.com/images/payscreen/methods/mistercash.png"],
//      ["id" => "kbc", "name" => "KBC/CBC", "image" => "https://www.mollie.com/images/payscreen/methods/kbc.png"],
      //      ["id"=>"sofort","name"=>"SOFORT Banking","image"=>"https://www.mollie.com/images/payscreen/methods/sofort.png"],
    ],
  ]);

  Config::set('PRODUCTS_SHOW_PRICES_INC_VAT', false);
  Config::set("PRODUCT_USE_ONLINE_UC", true);
  Config::set("PRODUCT_USE_ONLINE_ADMIN", true);
  Config::set("BREADCRUMBS_IGNORE_IDS", [219, 228, 291]);//Pagina's die niet zichtbaar zijn in de breadcrumbs
  Config::set("PAGE_TEASER_ENABLED", true); //page id || parent_id waarvoor teaser tekst + plaatje ingevoerd kunnen worden

  Config::set('PAGE_CKEDITOR_CONTENT_CSS', [
    '/projects/rde/templates/frontend/dist/main.css',
    'https://fonts.googleapis.com/css?family=Roboto:300,400,400i,700,900&display=swap',
  ]);

  Config::set('PAGES_IGNORE_IN_FEED', [211, 212, 213, 214, 215, 209, 210, 217, 291]);//06-09-2016 - ROBERT - pageid's negeren in feed
  Config::set('PRODUCT_CUSTOM_URL', true); //31-01-2019 - ROBERT - mogelijkheid om een zelf de product url in te voeren
  Config::set('PRODUCT_WEIGHT_ENABLED', true);//true if the product weight is available and mandatory
  Config::set('PRODUCT_SHIPPING_CAT_ENABLED', true);//true if the shipping cat is enabled.

  Config::set("CATEGORY_USE_ONLINE_UC", true); //07-03-2019 - ROBERT - gebruik van online_uc vlag
  Config::set("CATEGORY_USE_ONLINE_ADMIN", true); //07-03-2019 - ROBERT - gebruik van online_admin vlag

  Config::set("POSTCODEAPI_KEY", 'XFvk0eBGkD3aup33HeycS1xonjm0plvU8tfhKd4l'); //18-07-2019 - ROBERT - api key used for PostcodeApi

  Config::set("GOOGLE_RECAPTCHA", [
    "public_key" => "6LeuhK8UAAAAAKNtww48SLaWDGPySBHb_f2i2F1X",
    "secret_key" => "6LeuhK8UAAAAAAVGxf2IFVBGsTuUnWd2_IrHWvLu",
  ]); //23-06-2017 - ROBERT - vraag captcha bij 1e keer product in mandje plaatsen.

  const CONFIGURE_MAX_DEPTH_NESTED_CATS = 4; //Maximaal genestte niveau van categorieën.

  //Unit4 Multivers API
  Config::set("MULTIVERS", [
    'enabled'      => true,
    'api_url'      => 'https://api.multivers.nl/V221', //productie
    'callback_url' => DEVELOPMENT ? "http://beheer.raamdorpel.nl.rde.localhost/nl/multivers" : "https://api.raamdorpel.nl/nl/multivers",
    'vat_codes'    => [
      0  => 0,
      6  => 2,
      9  => 2,
      19 => 1,
      21 => 9,
    ], //btwtarief => multiverscode
    'ledger_codes' => [
      'nl'    => [0 => 8000, 6 => 8040, 9 => 8040, 19 => 8030, 21 => 8030],
      'eu'    => [0 => 8010, 6 => 8040, 9 => 8040, 19 => 8030, 21 => 8030],
      'other' => [0 => 8020, 6 => 8040, 9 => 8040, 19 => 8030, 21 => 8030],
    ], //country => btw => grootboeknummer
    'journalId'    => 20,
  ]); //29-08-2019 - ROBERT - config Unit4 multivers

  Config::set('CATEGORY_CUSTOM_URL', true); //31-01-2019 - ROBERT - mogelijkheid om een zelf de categorie url in te voeren

  Config::set("PACKS", [
    'api_url'  => 'https://orders.packs.nl', //productie
    'username' => 'Raamdorpel', //productie
    'password' => 'X7vFhtC8R', //productie
  ]); //13-09-2019 - ROBERT - config PACKS api

  Config::set('PRODUCT_FILES_MULTIPLE', true); //28-11-2019 - ROBERT - meerdere (pdf) bestanden kunnen uploaden bij een product
  Config::set('USER_MAY_SEE_OWN_ORGANISATION', true); //ingelogde gebruiker kan eigen organisatie in organisatielijst zien
  Config::set("USER_PASSWORD_ENCRYPT", true);

  Config::set("MITRE_FALLBACK_STONE_ID", 3); //deze steen id word gebruikt bij beton/natuursteen aangezien we deze dynamische berekenen.

  Config::set('PRODUCT_OPTIONS', ["lengths"]); //product options. Mbv product_option tabel. Benoem properties tbv product excel export

  Config::set('PRODUCT_ORDER_SIZE_MAX', true); // 07-05-20120 - ROBERT - Maximaal aantal producten in 1 keer te bestellen in mandje

  Config::set("RDE_VALID_IP_ADDRESSES", [
    "127.0.0.1",
    "**************", //raamdorpel server
    '2a0c:eb00:0:f3:185:233:172:60',  //raamdorpel server
    "**************",
    "*************",
    "************",
    "**************",
    "**************",
    "************", //gsd
    "***************",//gsd hapert
    '************', //raamdorpel nieuwe pand
    "**********", //robert local
    "**********", //max local
  ]);


  Config::set("RESTRICT_USER_SITE_ACCESS", [
    2 => [1, 2],
    4 => [1, 2],
  ]); // use this to limit the sites a user can access from the "select site" or "pages" module, user_id => [site ids which the user may access]

  Config::set("QUOTATION_DEFAULT_FREIGHTCOSTS", 50.00);

  Config::set("LOG_ALL_MAIL_TO_DB", true);

  Config::set("PRICE_FACTORS", [
    "raamdorpel"   => [
      "endstone" => 1.05,
    ],
    "muurafdekker" => [
      "default" => 1.02,
    ],
  ]);

  Config::set("RDE_PAYMENTTERMS", [
    14,
    21,
    30,
    45,
    60,
    75,
  ]);

  Config::set("ORGAN_EDIT_SHOW_SOCIAL_MEDIA", false); //true/false. Toont Social media inputvelden bij bewerken organisatie.

  Config::set("GOOGLE_AUTHENTICATOR", [
    "backend" => [
      "enabled"    => true,
      "usergroups" => ["SUPERADMIN", "ADMIN", "BOEKHOUDER"], //ALL: all usergroups must validate / array of usergroups to validate
      "remember"   => 14, //false: not enabled, integer: number of days to remember code on this browser
    ],
  ]);  //20-04-2019 - ROBERT - enable google authenticator login. only for backend implemented currently
  Config::set('CRYPT_SALT_KEY', 'rdedominator');
  Config::set('CRYPT_SALT_IV', 'bartisrde');

  Config::set("LOGIN_VALIDATE_IPADDRESS", [
    "backend" => [
      "enabled"    => true,
      "usergroups" => "ALL", //ALL: all usergroups must validate / array of usergroups to validate
    ],
  ]);//01-06-2021 - ROBERT - enable IP-addres check. If an unkwown IP-adress tries to login, they have to validate via e-mail. only for backend implemented currently. Don't forget to set CRYPT_SALT_KEY en CRYPT_SALT_IV if not available. There is also an overview screen of allowed IP-addresses available in otherActions

  Config::set("ERROR_HANDLER_JS", [
    "enabled"  => false, //enable js error logging
    "sendmail" => false, //send email to error mailbox
  ]);//21-04-2022 - ROBERT - enabe js error loging/mails

  Config::set("MESSAGEBIRD", [
    "API_KEY"                     => 'ATHB5n5CzWSpPz4KhSvGkmgCe', //messagebird SMS api key
    "API_DEV_KEY"                 => 'ATHB5n5CzWSpPz4KhSvGkmgCe',//messagebird SMS api key for development (test mode key in messagebird)
    "API_ORIGINATOR"              => '+31655392857', //messagebird SMS originator. This can be a phonenumber or 11 character length string
    //"API_ORIGINATOR" => '+31654650888', //messagebird SMS originator. This can be a phonenumber or 11 character length string
    "WHATSAPP_CHANNEL_ID" => "b787309e789b4e7494a7b8d7c38d84f6",
    "WHATSAPP_TEMPLATE_NAMESPACE" => "b787309e789b4e7494a7b8d7c38d84f6",
  ]);// 23-05-2017 - ROB - messagebird


  Config::set("CATEGORY_EDIT_CONTENT_2", true);

  Config::set("GSDEDITOR", [
    "active"                => true, //is blockeditor active
    "toggle"                => true, //may toggle between old and new editore
    "developer"             => false, //set true if you want to load in dev mode
    "tinymce_stylesheets"   => [
      '/projects/rde/templates/frontend/dist/main.css?version=1',
      '/projects/rde/templates/frontend/style/ckeditor_style.css?version=1',
    ], //stylesheets to use in tinymce
    "tinymce_content_style" => 'https://fonts.googleapis.com/css?family=Roboto:300,400,400i,700,900&display=swap', //extra style to use in tinymce
  ]);  //24-02-2022 - ROBERT - blockeditor

// firebase
  Config::set('FIREBASE_PV_KEY', [
    'type' => "service_account",
    'project_id' => "raamdorpel-push-notificaties",
    'private_key_id' => "84f83c06b24500d46e6cf2187125a7f40e8c7ac3",
****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
    'client_email' => "<EMAIL>",
    'client_id' => "118327158297777559955",
    'auth_uri' => "https://accounts.google.com/o/oauth2/auth",
    'token_uri' => "https://oauth2.googleapis.com/token",
    'auth_provider_x509_cert_url' => "https://www.googleapis.com/oauth2/v1/certs",
    'client_x509_cert_url' => "https://www.googleapis.com/robot/v1/metadata/x509/firebase-adminsdk-fbsvc%40raamdorpel-push-notificaties.iam.gserviceaccount.com",
    'universe_domain' => "googleapis.com"
  ]);
  Config::set('FIREBASE_CONFIG', [
    'apiKey'            => 'AIzaSyCNuU_cskHe5Qaf9FbXYy_qqU4ODmGhB7E',
    'authDomain'        => 'raamdorpel-push-notificaties.firebaseapp.com',
    'projectId'         => 'raamdorpel-push-notificaties',
    'storageBucket'     => 'raamdorpel-push-notificaties.firebasestorage.app',
    'messagingSenderId' => '************',
    'appId'             => '1:************:web:7954759606b9f57e141022',
  ]);
  Config::set('FIREBASE_VAPID_KEY', 'BLRMWH5CF1xzVSOw-fGYsdhkRgfXEIZWbN5Mjue6WiVPXoTfy7hpfIOOWRykJrEwipnelKQDUkC4wEqwk3CdVk8');

  Config::set('PRODUCT_PRICE_ON_REQUEST', true); //product price on request