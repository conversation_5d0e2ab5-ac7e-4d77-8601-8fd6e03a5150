<?php
class BaseMitrePrices extends AppModel
{
  const DB_NAME = 'rde_b1mo';
  const TABLE_NAME = 'mitre_prices';
  const OM_CLASS_NAME = 'MitrePrices';
  const columns = ['id', 'stoneLength', 'factor', 'validFrom', 'validTo'];
  const field_structure = [
    'id'                          => ['type' => 'int', 'length' => '5', 'null' => false],
    'stoneLength'                 => ['type' => 'decimal', 'length' => '4,1', 'null' => false],
    'factor'                      => ['type' => 'float', 'length' => '5,3', 'null' => false],
    'validFrom'                   => ['type' => 'date', 'length' => '', 'null' => false],
    'validTo'                     => ['type' => 'date', 'length' => '', 'null' => false],
  ];

  protected static $primary_key = ['id'];
  protected $auto_increment = 'id';

  public $id, $stoneLength, $factor, $validFrom, $validTo;

  function __construct($obj_arr = [], &$error_codes = []) {
    parent::__construct();
    $this->setDefaults();
    if (!empty($obj_arr)) {
      $this->from_array($obj_arr);
      $this->valid_required_fields($error_codes);
    }
  }

  public function valid(&$error_codes = []) {
    $errors = [];
    foreach(self::columns as $column) {
      if ($column != $this->auto_increment)
      {
        call_user_func_array([$this, 'v_' . $column], [&$errors]);
      }
    }
    if(empty($errors)) {
      return true;
    }
    $error_codes = array_merge($error_codes, $errors);
    return false;
  }

  public function valid_required_fields(&$error_codes = []) {
    $errors = [];
    foreach (self::columns as $column) {
      $function_name = "v_$column";
      if ($column!=$this->auto_increment) {
        call_user_func_array([$this, $function_name], [&$errors]);
      }
    }
    if (empty($errors)) {
      return true;
    }
    $error_codes = array_merge($error_codes, $errors);
    return false;
  }

  public function setDefaults() {
    $this->validTo = '9999-12-31';
  }



  public function v_id(&$error_codes = []) {
    if (!is_null($this->id) && strlen($this->id) > 0 && self::valid_int($this->id, '5')) {
      return true;
    }
    $error_codes[] = 'id';
    return false;
  }

  public function v_stoneLength(&$error_codes = []) {
    if (!is_null($this->stoneLength) && strlen($this->stoneLength) > 0 && self::valid_decimal($this->stoneLength, '4,1')) {
      return true;
    }
    $error_codes[] = 'stoneLength';
    return false;
  }

  public function v_factor(&$error_codes = []) {
    if (!is_null($this->factor) && strlen($this->factor) > 0 && self::valid_float($this->factor, '5,3')) {
      return true;
    }
    $error_codes[] = 'factor';
    return false;
  }

  public function v_validFrom(&$error_codes = []) {
    if (!is_null($this->validFrom) && strlen($this->validFrom) > 0 && self::valid_date($this->validFrom)) {
      return true;
    }
    $error_codes[] = 'validFrom';
    return false;
  }

  public function v_validTo(&$error_codes = []) {
    if (!is_null($this->validTo) && strlen($this->validTo) > 0 && self::valid_date($this->validTo)) {
      return true;
    }
    $error_codes[] = 'validTo';
    return false;
  }

  /**
  *@param array $conditions (required)
  *@param String $raw_sql (optional)
  *@return MitrePrices[]
  **/
  public static function find_all_like($conditions, $raw_sql = '') { return parent::generic_find_all_like($conditions, static::OM_CLASS_NAME, $raw_sql); }
  /**
  *@param array $conditions (required)
  *@param String $raw_sql (optional)
  *@return MitrePrices[]
  **/
  public static function find_all_by($conditions, $raw_sql = '') { return parent::generic_find_all_by($conditions, static::OM_CLASS_NAME, $raw_sql); }
  /**
  *@param String $raw_sql (optional)
  *@return MitrePrices[]
  **/
  public static function find_all($raw_sql = '') { return parent::generic_find_all(static::OM_CLASS_NAME, $raw_sql); }
  /**
  *@param array $conditions (required)
  *@param String $raw_sql (optional)
  *@return MitrePrices
  **/
  public static function find_by($conditions, $raw_sql = '') { return parent::generic_find_by($conditions, static::OM_CLASS_NAME, $raw_sql); }
  /**
  *@param String $id (required)
  *@param String $raw_sql (optional)
  *@return MitrePrices
  **/
  public static function find_by_id($id, $raw_sql = '') { return parent::generic_find_by_id($id, static::OM_CLASS_NAME, $raw_sql); }
  /**
  *@param array $conditions (required)
  *@param String $raw_sql (optional)
  *@return int
  **/
  public static function count_all_by($conditions, $raw_sql = '') { return parent::generic_count_all_by($conditions, static::OM_CLASS_NAME, $raw_sql); }
  /**
  *@param array $conditions (required)
  *@param String $raw_sql (optional)
  *@return bool
  **/
  public static function delete_by($conditions, $raw_sql = '') { return parent::generic_delete_by($conditions, static::OM_CLASS_NAME, $raw_sql); }
}