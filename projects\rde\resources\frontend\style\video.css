.vi-lazyload {
  width: 100%;
  position: relative !important;
  overflow: hidden;
  cursor: pointer;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.vi-lazyload::before {
  content: '';
  width: 100%;
  display: block;
  position: relative;
  padding-top: 56.25%; /*16:9 ratio*/
  background-color: #000;
}

.vi-lazyload-wrap {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  padding: inherit;
}

.vi-lazyload-content {
  width: 100%;
  height: 100%;
  position: relative;
  background-color: #000;
  /* --vi-lazyload-img wil be replaced with thumb */
  background-image: var(--vi-lazyload-img);
  background-size: cover;
  background-position: 50%;
  background-repeat: no-repeat;
}

.vi-lazyload-playbtn {
  display: block;
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 1;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 13 8'%3E%3Crect fill='rgba(0,0,0,.65)' width='13' height='8' rx='1' ry='1'/%3E%3Cpolygon fill='%23fff' points='5 6 9 4 5 2'/%3E%3C/svg%3E");
  background-position: 50%;
  background-size: calc(35px + 10%) auto;
  background-repeat: no-repeat;
}

.vi-lazyload-playbtn:hover {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 13 8'%3E%3Crect fill='%23B43929' width='13' height='8' rx='1' ry='1'/%3E%3Cpolygon fill='%23fff' points='5 6 9 4 5 2'/%3E%3C/svg%3E");
}

.vi-lazyload iframe {
  width: 100% !important;
  height: 100% !important;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 3;
  border: 0;
  background-color: #000;
}
