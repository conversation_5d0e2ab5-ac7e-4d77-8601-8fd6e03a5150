<?php TemplateHelper::includePartial('_tabs.php', 'prices'); ?>

<div class="box">
      <form method="post" action="<?php echo reconstructQueryAdd() ?>">
        <?php
          foreach($filter_form->getElements() as $element):
            echo $element->render(false). ' ';
          endforeach;
        ?>
        <input type="submit" name="go" id="go" value="Zoeken" />
        <input type="submit" name="reset" id="reset" value="Reset filter" />
      </form>
    </div>
  <?php if(count($items)!=0): ?>
    <div class="box">
      <form method="get">
        <input type="hidden" name="action" value="paymenttermedit"/>
        Opslag aanpassen welke ingaat vanaf 1 januari <select name="yearfrom" id="yearfrom">
          <?php echo getOptionVal(date("Y"), date("Y")+1, date("Y")+1) ?>
        </select>
        <input type="submit" name="edit" id="edit" value="Bewerk betaaltermijn opslag" class="gsd-btn gsd-btn-primary" />
        <?php echo showHelpButton("Met deze knop kun je de opslag aanpassen startent op een bepaald jaar.","Bewerk opslag") ?>
      </form>
    </div>
  <?php endif; ?>
  <br/>
  <?php if(count($items)==0): ?>
    <section class="empty-list-state">
      <p><?php echo __('Er zijn geen items gevonden.') ?></p>
    </section>
  <?php else: ?>
    <table class="default_table" style="width: auto;">
      <tr class="dataTableHeadingRow">
        <td style="width: 190px;">Naam</td>
        <?php foreach (Config::get("RDE_PAYMENTTERMS") as $term): ?>
          <td style="text-align: center;width: 50px;"><?php echo $term ?></td>
        <?php endforeach; ?>
        <td style="text-align: center;">Geldig van</td>
        <td style="text-align: center;">Geldig tot</td>
      </tr>
      <?php
        /** @var CustomerGroups $item */
        foreach($items as $item):
          $paytermfirst = false;
          ?>
        <tr class="dataTableRow trhover">
          <td><?php echo $item->name ?></td>
          <?php foreach (Config::get("RDE_PAYMENTTERMS") as $term): ?>
            <td style="text-align: center">
              <?php if(!isset($item->paymentterms[$term])): ?>
                x
              <?php elseif(isset($item->paymentterms[$term]->current)):
                if(!$paytermfirst) $paytermfirst = $item->paymentterms[$term]->current;
                ?>
                <?php if($item->paymentterms[$term]->current->increase!=0) echo $item->paymentterms[$term]->current->increase ?>
              <?php endif; ?>
            </td>
          <?php endforeach; ?>
          <td style="text-align: center"><?php echo $paytermfirst->getValidFrom() ?></td>
          <td style="text-align: center"><?php echo $paytermfirst->getValidTo()=="31-12-9999"?"-":$paytermfirst->getValidTo() ?></td>
        </tr>
      <?php endforeach; ?>
    </table>

    <Br/>
    Indien er een x staat, is er geen opslag code gedefineerd voor deze klant groep en betalingstermijn in tabel customer_codes.<Br/>
    <Br/>

  <?php endif; ?>

<script>
  $(document).ready(function() {
    $("#year").change(function() {
      $("#go").click();
    })
  });
</script>
<style>
  #colors_wrapper {
  }
  #colors_wrapper label {
    padding: 5px 5px 0 0;
    display: inline-block;
  }

</style>