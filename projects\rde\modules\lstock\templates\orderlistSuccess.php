<?php TemplateHelper::includePartial('_tabs.php','lstock'); ?>
<script type="text/javascript">
$(document).ready(function() {
  $(".size").change(function (event) {
    if($(this).val()!="") {
      var val = parseInt($(this).val());
      if(!isNaN(val)) {
        $(this).val(val);
      }
      else {
        $(this).val(0);
      }
    }
    else {
      $(this).val(0);
    }
  });

  $(".showdetail").click(function (e) {
    e.preventDefault();
    var id = $(this).attr("data-id");
    $("#detail_"+id).toggle();
  });

  $(".select_all").click(function() {
    const isChecked = $(this).is(":checked");
    $(this).closest("table").find("input[type='checkbox']").prop("checked", isChecked);
  });
});
</script>	

<form method="post">
  <div class="box">
    <input type="text" name="st_search" value="<?php echo $_SESSION['st_search'] ?>" placeholder="Zoeken..."/>
    <select name="st_brand">
      <option value="">Selecteer leverancier...</option>
      <?php foreach($stonebrands as $sb): ?>
        <option value="<?php echo $sb->brandId ?>" <?php if($_SESSION['st_brand']==$sb->brandId) echo 'selected'; ?>><?php echo $sb->name ?></option>
      <?php endforeach; ?>
    </select>
    <label><input type="checkbox" value="1" name="st_shortage" <?php if($_SESSION['st_shortage']==1) echo 'checked'; ?>/> toon alleen stenen tekort</label>
    <label><input type="checkbox" value="1" name="st_hasorder" <?php if($_SESSION['st_hasorder']==1) echo 'checked'; ?>/> toon alleen stenen met bestelling</label>
    <input type="submit" name="go" value="Zoeken" />
  </div>

</form>
<?php $pager->writePreviousNext(); ?>

<?php	if(count($stones)==0): ?>
  <br/>Er zijn geen stenen gevonden
<?php else: ?>
  <table class="default_table" style="width: auto;">
  <tr class="dataTableHeadingRow">
    <td>Code</td>
    <td>Kleur</td>
    <td style="text-align: right;">
      Stenen benodigd<br/>
      <?php echo showInfoButton("Dit zijn het aantal stenen benodigd voor de actuele bestellingen.") ?>
    </td>
    <td style="text-align: right;">
      Voorraad<br/>
      <?php echo showInfoButton("Dit is voorraad gesomeerd van alle varianten van dit type. M+EL+ER+GL+GR") ?>
    </td>
    <td style="text-align: right;">Besteld leverancier<br/><?php echo showInfoButton("Dit zijn het aantal stenen wat besteld is en nog niet geleverd.") ?></td>
    <td>Details</td>
  </tr>
  <?php foreach($stones as $item): ?>
    <tr class="dataTableRow trhover">
      <td><?php echo $item->name ?> <?php //echo $item->stoneId ?></td>
      <td><?php echo $item->color->getFullname() ?></td>
      <td style="text-align: right;"><?php echo $item->totals["sumorder"] ?></td>
      <td style="text-align: right;"><?php echo $item->totals["stock"] ?></td>
      <td style="text-align: right;"><?php echo $item->totals["alreadyOrdered"] ?></td>
      <td><a href="#" class="gsd-btn showdetail <?php if($item->hasShortage) echo "showdetail_highlight" ?>" data-id="<?php echo $item->stoneId ?>">Toon detail</a></td>
    </tr>
    <tr class="dataTableRow" style="display:none;" id="detail_<?php echo $item->stoneId ?>">
      <td class="detail" colspan="7">
        <form method="post">
          <table class="default_table" style="width: auto;">
            <tr class="dataTableHeadingRow">
              <td><input type="checkbox" class="select_all" /></td>
              <td>Bestelnummer</td>
              <td style="text-align: right;">M</td>
              <td style="text-align: right;">EL</td>
              <td style="text-align: right;">ER</td>
              <?php if($item->brandId!=1): ?>
              <td style="text-align: right;">G</td>
              <?php endif; ?>
              <td style="text-align: right;">Totaal</td>
            </tr>
            <?php foreach($item->orders as $order): ?>
              <tr class="dataTableRow trhover">
                <td><input type="checkbox" value="<?php echo $order["quotationId"] ?>" name="quotations[]" id="<?php echo $order["quotationId"] ?>" <?php if(!isset($order_item_quotations[$order["quotationId"]])): ?>checked<?php endif;  ?>/></td>
                <td><label for="<?php echo $order["quotationId"] ?>"><?php echo $order["name"] ?></label></td>
                <td style="text-align: right;"><?php echo $order["totalMiddlesStones"] ?></td>
                <td style="text-align: right;"><?php echo $order["totalLeftEndStones"] ?></td>
                <td style="text-align: right;"><?php echo $order["totalRightEndStones"] ?></td>
                <?php if($item->brandId!=1): ?>
                <td style="text-align: right;"><?php echo $order["totalEndStonesGrooves"] ?></td>
                <?php endif; ?>
                <td style="text-align: right;"><?php echo $order["sum"] ?></td>
              </tr>
            <?php endforeach; ?>
            <tr class="dataTableRow trhover topborder">
              <td></td>
              <td>Totalen:</td>
              <td style="text-align: right;"><?php echo $item->totals["totalMiddlesStones"] ?></td>
              <td style="text-align: right;"><?php echo $item->totals["totalLeftEndStones"] ?></td>
              <td style="text-align: right;"><?php echo $item->totals["totalRightEndStones"]  ?></td>
              <?php if($item->brandId!=1): ?>
              <td style="text-align: right;"><?php echo $item->totals["totalEndStonesGrooves"] ?></td>
              <?php endif; ?>
              <td style="text-align: right;"><?php echo $item->totals["sumorder"] ?></td>
            </tr>
            <tr class="dataTableRow trhover topborder">
              <td></td>
              <td>Voorraad:</td>
              <td style="text-align: right;"><?php echo $item->stock ?></td>
              <td style="text-align: right;"><?php if(isset($item->sibelings["left"])) echo $item->sibelings["left"]->stock ?></td>
              <td style="text-align: right;"><?php if(isset($item->sibelings["right"])) echo $item->sibelings["right"]->stock ?></td>
              <?php if($item->brandId!=1): ?>
              <td style="text-align: right;"><?php if(isset($item->sibelings["leftg"])) echo $item->sibelings["leftg"]->stock ?></td>
              <?php endif; ?>
              <td style="text-align: right;"><?php //echo $order["sum"] ?></td>
            </tr>
            <tr class="dataTableRow trhover">
              <td></td>
              <td>Voorraad marge:</td>
              <td style="text-align: right;"><?php echo $item->minAmountStones ?></td>
              <td style="text-align: right;"><?php if(isset($item->sibelings["left"])) echo $item->sibelings["left"]->minAmountStones ?></td>
              <td style="text-align: right;"><?php if(isset($item->sibelings["right"])) echo $item->sibelings["right"]->minAmountStones ?></td>
              <?php if($item->brandId!=1): ?>
              <td style="text-align: right;"><?php if(isset($item->sibelings["leftg"])) echo $item->sibelings["leftg"]->minAmountStones ?></td>
              <?php endif; ?>
              <td style="text-align: right;"><?php //echo $order["sum"] ?></td>
            </tr>
            <tr class="dataTableRow trhover">
              <td></td>
              <td>Op te bestellen lijst:</td>
              <td style="text-align: right;"><?php echo $item->onOrderlist ?></td>
              <td style="text-align: right;"><?php if(isset($item->sibelings["left"])) echo $item->sibelings["left"]->onOrderlist ?></td>
              <td style="text-align: right;"><?php if(isset($item->sibelings["right"])) echo $item->sibelings["right"]->onOrderlist ?></td>
              <?php if($item->brandId!=1): ?>
              <td style="text-align: right;"><?php if(isset($item->sibelings["leftg"])) echo $item->sibelings["leftg"]->onOrderlist ?></td>
              <?php endif; ?>
              <td style="text-align: right;"><?php //echo $order["sum"] ?></td>
            </tr>
            <tr class="dataTableRow trhover">
              <td></td>
              <td>Besteld, niet geleverd:</td>
              <td style="text-align: right;"><?php echo $item->alreadyOrdered ?></td>
              <td style="text-align: right;"><?php if(isset($item->sibelings["left"])) echo $item->sibelings["left"]->alreadyOrdered ?></td>
              <td style="text-align: right;"><?php if(isset($item->sibelings["right"])) echo $item->sibelings["right"]->alreadyOrdered ?></td>
              <?php if($item->brandId!=1): ?>
              <td style="text-align: right;"><?php if(isset($item->sibelings["leftg"])) echo $item->sibelings["leftg"]->alreadyOrdered ?></td>
              <?php endif; ?>
              <td style="text-align: right;"><?php //echo $order["sum"] ?></td>
            </tr>

            <tr class="dataTableRow trhover topborder">
              <td></td>
              <td>Te bestellen:</td>
              <td>
                <input style="text-align: right;" type="text" name="stock[<?php echo $item->stoneId ?>]" class="size" value="<?php echo $item->toOrder>0?$item->toOrder:0 ?>"/>
              </td>
              <td>
                <input style="text-align: right;" type="text" name="stock[<?php if(isset($item->sibelings["left"])) echo $item->sibelings["left"]->stoneId ?>]" class="size" value="<?php echo isset($item->sibelings["left"]) && $item->sibelings["left"]->toOrder>0?$item->sibelings["left"]->toOrder:0 ?>" <?php if(!isset($item->sibelings["left"])) echo 'readonly title="Steen type bestaat niet"'; ?>/>
              </td>
              <td>
                <input style="text-align: right;" type="text" name="stock[<?php if(isset($item->sibelings["right"])) echo $item->sibelings["right"]->stoneId ?>]" class="size" value="<?php echo isset($item->sibelings["right"]) && $item->sibelings["right"]->toOrder>0?$item->sibelings["right"]->toOrder:0 ?>" <?php if(!isset($item->sibelings["right"])) echo 'readonly title="Steen type bestaat niet"'; ?>/>
              </td>
              <?php if($item->brandId!=1): ?>
                <td>
                  <input style="text-align: right;" type="text" name="stock[<?php if(isset($item->sibelings["leftg"])) echo $item->sibelings["leftg"]->stoneId ?>]" class="size" value="<?php echo isset($item->sibelings["leftg"]) && $item->sibelings["leftg"]->toOrder>0?$item->sibelings["leftg"]->toOrder:0 ?>" <?php if(!isset($item->sibelings["leftg"])) echo 'readonly title="Steen type bestaat niet"'; ?>/>
                </td>
              <?php endif; ?>
              <td style="text-align: right;">
                <input type="hidden" name="brandId" value="<?php echo $item->brandId ?>"/>
                <input type="submit" name="order" value="Bestellen"/>
              </td>
            </tr>
          </table>
        </form>
      </td>
    </tr>

  <?php endforeach; ?>
  </table><br/>
<?php endif; ?>
