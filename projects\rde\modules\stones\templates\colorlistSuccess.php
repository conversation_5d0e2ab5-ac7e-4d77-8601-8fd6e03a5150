<?php TemplateHelper::includePartial('_tabs.php', 'stones'); ?>

<div class="box">
    <form method="post" action="<?php echo reconstructQueryAdd() ?>">
      <input type="text" name="color_search" value="<?php echo $_SESSION['color_search'] ?>" placeholder="Zoeken..."/>
      <select name="color_brand" id="color_brand">
        <option value="">Selecteer merk...</option>
        <?php foreach($brands as $brand): ?>
          <option value="<?php echo $brand->brandId ?>" <?php if($_SESSION['color_brand']==$brand->brandId) echo 'selected'; ?>><?php echo $brand->name ?></option>
        <?php endforeach; ?>
      </select>
      <select name="color_display"  id="color_display">
        <option value="">Selecteer zichtbaar...</option>
        <option value="true" <?php if($_SESSION['color_display']=="true") echo 'selected'; ?>>Ja</option>
        <option value="false" <?php if($_SESSION['color_display']=="false") echo 'selected'; ?>>Nee</option>
      </select>
      <input type="submit" name="go" id="go" value="Zoeken" />

      <a href="?action=coloredit" class="gsd-btn gsd-btn-primary">Toevoegen nieuwe kleur</a>
    </form>
  </div>

  <?php $pager->writePreviousNext(); ?>

  <?php if(count($items)==0): ?>
    <section class="empty-list-state">
      <p><?php echo __('Er zijn geen items gevonden.') ?></p>
    </section>
  <?php else: ?>
    <table class="default_table" style="width: auto;">
      <tr class="dataTableHeadingRow">
        <td>Merk</td>
        <td>Kleurcode</td>
        <td>Kleur</td>
        <td>Geglazuurd</td>
        <td>Zichtbaar</td>
        <td style="width: 70px;">Actie</td>
      </tr>
      <?php
        /** @var StoneColors $item */
        foreach($items as $item): ?>
        <tr class="dataTableRow trhover">
          <td><?php echo $brands[$item->brandId]->name ?></td>
          <td><?php echo $item->short ?></td>
          <td><?php echo $item->name ?></td>
          <td><?php echo $item->glaced=="true"?"Ja":"Nee" ?></td>
          <td><?php echo $item->display=="true"?"Ja":"Nee" ?></td>
          <td>
            <?php echo BtnHelper::getEdit(reconstructQueryAdd(['pageId']).'action=coloredit&id='.$item->colorId) ?>
          </td>
        </tr>
      <?php endforeach; ?>
    </table>
  <?php endif; ?>
<script>
  $(document).ready(function() {
    $("#color_brand,#color_display").change(function() {
      $("#go").click();
    })
  });
</script>