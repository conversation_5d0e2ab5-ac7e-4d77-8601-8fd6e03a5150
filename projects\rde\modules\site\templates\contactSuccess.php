<script src='https://www.google.com/recaptcha/api.js' async defer></script>

<div class="contenttxt" style="min-height: 0;">
  <?php echo $page->content->content1 ?>
</div>
<Br/>

<?php writeErrors($errors); ?>

<div class="requiredMessage"></div>

<form method="post" class="custom_form required-form">
  <div class="form" id="form-1">
    <div class="form-row">
      <div class="col-4">Naam<span class="form-arterisk">*</span></div>
      <div class="col-4-3"><input class="required form-input" value="<?php writeIfSet('naam') ?>" type="text" name="naam"></div>
    </div>
    <div class="form-row">
      <div class="col-4">Bedrijfsnaam<span class="form-arterisk">*</span></div>
      <div class="col-4-3"><input class="required form-input" value="<?php writeIfSet('bedrijfsnaam') ?>" type="text" name="bedrijfsnaam"></div>
    </div>
    <div class="form-row">
      <div class="col-4">Telefoonnummer</div>
      <div class="col-4-3"><input class="form-input" value="<?php writeIfSet('telefoonnummer') ?>" type="text" name="telefoonnummer"></div>
    </div>
    <div class="form-row">
      <div class="col-4">E-mailadres<span class="form-arterisk">*</span></div>
      <div class="col-4-3"><input id="email_field" class="required form-input" value="<?php writeIfSet('email') ?>" type="email" name="email"></div>
    </div>
    <div class="form-row">
      <div class="col-4">Vraag/opmerking<span class="form-arterisk">*</span></div>
      <div class="col-4-3">
        <textarea class="required form-input" name="vraagopmerking"><?php writeIfSet('vraagopmerking') ?></textarea>
        <div class="g-recaptcha" data-sitekey="<?php echo Config::get("GOOGLE_RECAPTCHA")["public_key"] ?>" data-callback="onSubmit"></div>
      </div>
    </div>
    <div class="form-row">
      <div class="col-4 responsive-hide">&nbsp;</div>
      <div class="col-4-3">
        <button class="btn" name="send" id="send" type="submit">Verstuur</button>
      </div>
    </div>
  </div>
</form>

<br/>
<form class="form-inline">
  <div id="gmap" title="<?php echo $site->organisation->name ?>"></div>
  <style>

  </style>
  <script type="text/javascript">
    var gmap_image = '/images/markers/marker.png';
    var gmap_location = Array(<?php echo $site->organisation->getLatlng()?>);
    var gmap_location_name = "<?php echo $site->organisation->address . ' ' . $site->organisation->number . ', ' . $site->organisation->zip . ', ' . $site->organisation->city . ', ' . $site->organisation->getCountry() ?>";
    var gmap_location_title = "<?php echo $site->organisation->name; ?>";
    var gmap_grayed = true;

    blockEnterSubmit();

  </script>
  <script src="//maps.google.com/maps/api/js?key=<?php echo LocationHelper::getGoogleMapsKey() ?>"></script>
  <script src="/gsdfw/includes/jsscripts/jquery/jquery.directions.js" type="text/javascript"></script>
  <br/>
  <h2><?php echo __('Plan uw route') ?></h2>
  <br/>
  <div class="form-row">
    <div class="col8 col12-xs" style="padding-bottom: 15px;">
      <label>Uw vertrekpunt &nbsp;&nbsp;&nbsp;</label>
      <input type="text" class="form-input" name="routefrom" id="routefrom" style="width: 180px;" placeholder="Postcode,plaats..."/>&nbsp;&nbsp;&nbsp;
    </div>
    <div class="col4 col12-xs">
      <input type="button" class="btn btn-default btn-round" value="<?php echo __('Toon route') ?>"
             id="calcroute"/>
    </div>
  </div>
  <div id="gdirections"></div>
  <br/>
</form>

