<?php

  namespace domain\stones\service;

  use AppModel;
  use domain\quotations\service\GetBrandIdsByStone;
  use Quotations;
  use StoneBrands;
  use StoneCategory;
  use StoneColors;
  use Stones;
  use StoneSizes;

  class StoneSiteOverview {

    private string $material = Stones::MATERIAL_NATUURSTEEN;

    public function __construct($material) {
      $this->material = $material;
    }

    public function getValues(): string {
      $file = DIR_TEMP . "stonesearch_" . $this->material . ".json";
      //caching
      if (false && file_exists($file) && filemtime($file) > strtotime("-1 DAY")) {
        return file_get_contents($file);
      }

      if ($this->material == Stones::MATERIAL_KERAMIEK) {
        $stoneType = Stones::TYPE_RAAMDORPEL;
        $stoneMaterial = Stones::MATERIAL_KERAMIEK;
        $categoryIds = [4]; //keramisch heeft geen subcategorie
      }
      else {
        $stoneType = Stones::TYPE_RAAMDORPEL;
        $stoneMaterial = Stones::MATERIAL_NATUURSTEEN;
        $categoryIds = [19, 21, 20]; // 1stuk / dam / vlakke plaat
      }

      //merken
      $brandIds = (new GetBrandIdsByStone($stoneType, $stoneMaterial))->getBrandids();
      $brands = StoneBrands::find_all_by(["brandId" => $brandIds], "ORDER BY displayOrder ASC");

      $cats = [];
      $catSizesAr = [];
      foreach (StoneCategory::find_all_by(["id" => $categoryIds], "ORDER BY sort") as $cat) {
        $catSizesAr[$cat->id] = StoneSizes::getStonesizesByCategoryId($cat->id, $brandIds);
        $cats[] = $cat;
      }

      //mogelijke kleuren
      $colors = StoneColors::getDisplayColors($brandIds, $stoneType);

      $productArray = [];

      foreach ($brands as $brand) {
        $brandAr = [];
        $brandAr["brandId"] = $brand->brandId;
        $brandAr["name"] = $brand->name;
        $brandAr["colors"] = [];
        foreach ($colors as $color) {

          if ($color->brandId != $brand->brandId) continue;

          $colorGroup = [];
          $colorGroup["colorId"] = $color->colorId;
          $colorGroup["name"] = $color->getFullname();
          $colorGroup["categories"] = [];

          foreach ($cats as $cat) {

            //chinees natuursteen: alleen uit 1 stuk
            if ($brand->brandId == 4 && ($cat->id == 20 || $cat->id == 21)) {
              continue;
            }

            $catArray = [];
            $catArray["id"] = $cat->id;
            $catArray["name"] = $cat->name;
            $catArray["shortname"] = $cat->shortname;
            $catArray["groups"] = [];
            if ($this->material == Stones::MATERIAL_KERAMIEK) {
              $catArray["groups"]["thin"] = [
                "name"  => "thin",
                "items" => [
                  "120 x 30 / 60" => false,
                  "160 x 30 / 60" => false,
                  "210 x 30 / 60" => false,
                  "Overige maten" => false,
                ],
              ];
              $catArray["groups"]["thick"] = [
                "name"  => "thick",
                "items" => [
                  "120 x 50 / 80" => false,
                  "160 x 50 / 80" => false,
                  "210 x 50 / 80" => false,
                  "Overige maten" => false,
                ],
              ];
            }
            else {
              $catArray["groups"]["thin"] = [
                "name"  => "thin",
                "items" => [
                  "120 x 30 / 60" => false,
                  "160 x 30 / 60" => false,
                  "210 x 30 / 60" => false,
                  "Overige maten" => false,
                ],
              ];
              $catArray["groups"]["thick"] = [
                "name"  => "thick",
                "items" => [
                  "120 x 50 / 80" => false,
                  "160 x 50 / 80" => false,
                  "210 x 50 / 80" => false,
                  "Overige maten" => false,
                ],
              ];
            }
            $catSizes = $catSizesAr[$cat->id];

            //weg gaan opzoek naar de standaard maten binnen dit product.

            foreach ($catSizes as $size) {

              $price = 0;
              //we houden geen rekening met een ingelogde gebruiker op dit moment.
              $stone_price = Quotations::getPrices(date("Y-m-d"), '', $size->stone);
              if ($stone_price === false) {
                $price = 1000002;
              }
              else {
                $price = round($size->stone->getMeterprice($stone_price), 2);
              }
              $size->pricePerMeter = $price;


              if ($size->brandId == $brand->brandId && $size->stone->colorId == $color->colorId) {
                if (in_array($size->name, ["120 x 30 / 60", "160 x 30 / 60", "210 x 30 / 60"])) {
                  $catArray["groups"]["thin"]["items"][$size->name] = $size;
                }
                elseif (in_array($size->name, ["120 x 50 / 80", "160 x 50 / 80", "210 x 50 / 80"])) {
                  $catArray["groups"]["thick"]["items"][$size->name] = $size;
                }
              }
            }
            $colorGroup["categories"][] = $catArray;
          }
          $brandAr["colors"][] = $colorGroup;
        }
        $productArray[] = $brandAr;
      }


//dumpe($productArray);

      $colorsFlatten = [];
      foreach ($colors as $color) {
        $colorName = $color->name;
        if ($colorName == "Asian Black" || $colorName == "Donker") {
          $colorName = "Donker gezoet";
        }
        elseif ($colorName == "Chinees") {
          $colorName = "Licht gezoet - geschuurd";
        }
        $val = [];
        $val["colorGroupId"] = count($colorsFlatten);
        $val["name"] = $colorName;
        $val["ids"] = [];
        if (isset($colorsFlatten[$colorName])) {
          $val = $colorsFlatten[$colorName];
        }
        $val["ids"][] = $color->colorId;
        $colorsFlatten[$colorName] = $val;
      }

      ksort($colorsFlatten);
      $colorsFlatten = array_values($colorsFlatten);


      $result = [
        "brands"        => AppModel::plainObjects($brands),
        "colors"        => AppModel::plainObjects($colors),
        "colorsFlatten" => $colorsFlatten,
        "categories"    => AppModel::plainObjects($cats),
        "list"          => $productArray,
      ];

      $result = json_encode($result);
      file_put_contents($file, $result);

      return $result;

    }


  }