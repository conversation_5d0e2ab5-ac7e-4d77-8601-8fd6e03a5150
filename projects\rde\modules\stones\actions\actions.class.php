<?php

  use Gsd\Form\Elements\Checkbox;
  use Gsd\Form\Elements\Number;
  use Gsd\Form\Elements\Option;
  use Gsd\Form\Elements\Select;
  use Gsd\Form\ModelForm;

  class stonesRdeActions extends gsdActions {

    public function preExecute() {
      parent::preExecute();
      $this->showleftmenu = true;
    }

    public function executeSizelist() {
      if (!isset($_SESSION['size_search']))
        $_SESSION['size_search'] = '';
      if (!isset($_SESSION['size_brand']))
        $_SESSION['size_brand'] = '';
      if (!isset($_SESSION['size_display']))
        $_SESSION['size_display'] = '';
      if (isset($_POST['go'])) {
        $_SESSION['size_search'] = trim($_POST['size_search']);
        $_SESSION['size_brand'] = trim($_POST['size_brand']);
        $_SESSION['size_display'] = trim($_POST['size_display']);
        ResponseHelper::redirect(reconstructQuery());
      }

      $this->pager = new Pager();
      $this->pager->setWriteCount(true);
      $this->pager->handle();

      $filt = " WHERE 1 ";
      if ($_SESSION['size_brand'] != "") {
        $filt .= " AND brandId=" . $_SESSION['size_brand'] . " ";
      }
      if ($_SESSION['size_display'] != "") {
        $filt .= " AND display='" . $_SESSION['size_display'] . "' ";
      }
      if ($_SESSION['size_search'] != "") {
        $searchval = escapeForDB($_SESSION['size_search']);
        $filt .= " AND (";
        $filt .= " name LIKE '%" . $searchval . "%' ";
        $filt .= ")";
      }

      $this->pager->count = StoneSizes::count_all_by([], $filt);
      if (!$this->pager->count)
        $this->pager->count = 0;

      $filt .= "ORDER BY brandId, name";
      $filt .= $this->pager->getLimitQuery();
      $sizes = StoneSizes::find_all($filt);
      $this->items = $sizes;
      $this->brands = AppModel::mapObjectIds(StoneBrands::getBrands(), "brandId");
    }


    public function executeSizeedit() {

      $size = new StoneSizes();
      if (isset($_GET["id"])) {
        $size = StoneSizes::find_by(["sizeId" => $_GET["id"]]);
      }
      $brands = StoneBrands::getBrands();

      $form = new ModelForm();
      $form->addClass("edit-form");
      $form->buildElementsFromModel($size);
      $form->setElementsLabel([
        "brandId"  => "Merk",
        "name"     => "Naam",
        "short"    => "Naam kort",
        "length"   => "Lengte",
        "width"    => "Breedte",
        "height"   => "Hoogte",
        "click"    => "Klikhoogte",
        "wall_min" => "Muurafdekker minimale dikte",
        "wall_max" => "Muurafdekker maximale dikte",
        "display"  => "Online",
        "common"   => "Gebruikelijk",
      ], true);
      $select = new Select("Merk", "brandId", $size->brandId);
      $select->addOption(new Option("", "Selecteer merk"));
      foreach ($brands as $brand) {
        $select->addOptionHelper($brand->brandId, $brand->name);
      }
      $form->addElement($select);
      $form->setElementsRequired([
        "brandId",
        "name",
        "short",
        "length",
        "width",
        "height",
        "click",
      ]);

      if (isset($_POST["form"]["save"]) || isset($_POST["form"]["save_to_list"])) {

        $form->setElementsAndObjectValue($_POST);

        if ($form->isValid()) { //normally like this
          if ($size->wall_min == 0)
            $size->wall_min = null;
          if ($size->wall_max == 0)
            $size->wall_max = null;
          $size->save();

          MessageFlashCoordinator::addMessage("Maat opgeslagen");
          if (isset($_POST["form"]["save_to_list"])) {
            ResponseHelper::redirect(PageMap::getUrl('M_STONES_SIZES'));
          }
          else {
            ResponseHelper::redirect(PageMap::getUrl('M_STONES_SIZES', ['action' => 'sizeedit', 'id' => $size->sizeId]));
          }
        }

      }

      $this->form = $form;

    }

    public function executeColorlist() {
      if (!isset($_SESSION['color_search']))
        $_SESSION['color_search'] = '';
      if (!isset($_SESSION['color_brand']))
        $_SESSION['color_brand'] = '';
      if (!isset($_SESSION['color_display']))
        $_SESSION['color_display'] = '';
      if (isset($_POST['go'])) {
        $_SESSION['color_search'] = trim($_POST['color_search']);
        $_SESSION['color_brand'] = trim($_POST['color_brand']);
        $_SESSION['color_display'] = trim($_POST['color_display']);
        ResponseHelper::redirect(reconstructQuery());
      }

      $this->pager = new Pager();
      $this->pager->setWriteCount(true);
      $this->pager->handle();

      $filt = " WHERE 1 ";
      if ($_SESSION['color_brand'] != "") {
        $filt .= " AND brandId=" . $_SESSION['color_brand'] . " ";
      }
      if ($_SESSION['color_display'] != "") {
        $filt .= " AND display='" . $_SESSION['color_display'] . "' ";
      }
      if ($_SESSION['color_search'] != "") {
        $searchval = escapeForDB($_SESSION['color_search']);
        $filt .= " AND (";
        $filt .= " name LIKE '%" . $searchval . "%' ";
        $filt .= ")";
      }

      $this->pager->count = StoneColors::count_all_by([], $filt);
      if (!$this->pager->count)
        $this->pager->count = 0;

      $filt .= "ORDER BY brandId, name";
      $filt .= $this->pager->getLimitQuery();
      $colors = StoneColors::find_all($filt);
      $this->items = $colors;
      $this->brands = AppModel::mapObjectIds(StoneBrands::getBrands(), "brandId");
    }


    public function executeColoredit() {

      $color = new StoneColors();
      if (isset($_GET["id"])) {
        $color = StoneColors::find_by(["colorId" => $_GET["id"]]);
      }
      $brands = StoneBrands::getBrands();

      $form = new ModelForm();
      $form->addClass("edit-form");
      $form->setEnctype("multipart/form-data");
      $form->buildElementsFromModel($color);
      $form->setElementsLabel([
        "brandId"  => "Merk",
        "name"     => "Naam",
        "short"    => "Naam kort",
        "discount" => "Korting",
        "glaced"   => "Geglazuurd",
        "display"  => "Online",
        "common"   => "Gebruikelijk",
        "hexcolor" => "Kleur (bijv #982e1a)",
        "filename" => "Kleur afbeelding",
      ], true);

      $select = new Select("Merk", "brandId", $color->brandId);
      $select->addOption(new Option("", "Selecteer merk"));
      foreach ($brands as $brand) {
        $select->addOptionHelper($brand->brandId, $brand->name);
      }
      $form->addElement($select);
      $form->setElementsRequired([
        "brandId",
        "name",
        "short",
      ]);

      //afbeelding uploaden
      $uploadpath_root = DIR_UPLOADS . 'stonecolors/';
      $uploader = new Uploader('filename', reconstructQuery(['filename_delete']), $uploadpath_root);
      $uploader->setAllowed([
        'image/jpeg'  => 'jpg',
        'image/pjpeg' => 'jpg',
        'image/png'   => 'png',
        'image/gif'   => 'gif',
        'image/bmp'   => 'bmp',
      ]);

      $form_uploader = new \Gsd\Form\Elements\Uploader($uploader, "Afbeelding", "filename", $color->filename);
      if ($color->filename != "") {
        $form_uploader->setViewUrl('/uploads/rde/stonecolors/' . $color->filename . "?time=" . time());
        $form_uploader->setViewClass("gsd-btn");
        $form_uploader->setShowDeleteCheckbox(true);
      }
      if ($color->colorId == "") {
        $form_uploader->setDisabled(true);
      }
      $form->addElement($form_uploader);

      if (isset($_POST["form"]["save"]) || isset($_POST["form"]["save_to_list"])) {

        $form->setElementsAndObjectValue($_POST);

        if ($form->getElement("filename")->isUploaded()) {
          $newfilename = StringHelper::slugify($color->name) . "_" . $color->colorId . "." . FileHelper::getExtension($color->filename);
          rename($uploadpath_root . $color->filename, $uploadpath_root . $newfilename);
          $color->filename = $newfilename;
          //resize to max 600x600
          ImageHelper::resizeImageGD($uploadpath_root . $color->filename, $uploadpath_root . $color->filename, 250, 250);
        }
        if (isset($_POST["filename_delete"])) {
          $color->removeImageFile();
        }


        if ($form->isValid()) {

          if ($color->discount == "")
            $color->discount = 0;
          $color->save();

          MessageFlashCoordinator::addMessage("Kleur opgeslagen");
          if (isset($_POST["form"]["save_to_list"])) {
            ResponseHelper::redirect(PageMap::getUrl('M_STONES_COLORS'));
          }
          else {
            ResponseHelper::redirect(PageMap::getUrl('M_STONES_COLORS', ['action' => 'coloredit', 'id' => $color->colorId]));
          }
        }

      }

      $this->form = $form;

    }


    public function executeStonelist() {
      if (!isset($_SESSION['stone_search']))
        $_SESSION['stone_search'] = '';
      if (!isset($_SESSION['stone_brand']))
        $_SESSION['stone_brand'] = '';
      if (!isset($_SESSION['stone_type']))
        $_SESSION['stone_type'] = '';
      if (!isset($_SESSION['stone_material']))
        $_SESSION['stone_material'] = '';
      if (!isset($_SESSION['stone_color']))
        $_SESSION['stone_color'] = '';
      if (!isset($_SESSION['stone_display']))
        $_SESSION['stone_display'] = '';
      if (isset($_POST['go'])) {
        $_SESSION['stone_search'] = trim($_POST['stone_search']);
        $_SESSION['stone_brand'] = trim($_POST['stone_brand']);
        $_SESSION['stone_type'] = trim($_POST['stone_type']);
        $_SESSION['stone_material'] = trim($_POST['stone_material']);
        $_SESSION['stone_color'] = trim($_POST['stone_color']);
        $_SESSION['stone_display'] = trim($_POST['stone_display']);
        ResponseHelper::redirect(reconstructQuery());
      }

      $this->pager = new Pager();
      $this->pager->setWriteCount(true);
      $this->pager->handle();

      $filt = " WHERE 1 ";
      if ($_SESSION['stone_brand'] != "") {
        $filt .= " AND brandId=" . $_SESSION['stone_brand'] . " ";
      }
      if ($_SESSION['stone_display'] != "") {
        $filt .= " AND display='" . $_SESSION['stone_display'] . "' ";
      }
      if ($_SESSION['stone_type'] != "") {
        $filt .= " AND type='" . $_SESSION['stone_type'] . "' ";
      }
      if ($_SESSION['stone_material'] != "") {
        $filt .= " AND material='" . $_SESSION['stone_material'] . "' ";
      }
      if ($_SESSION['stone_color'] != "") {
        $filt .= " AND colorId='" . $_SESSION['stone_color'] . "' ";
      }
      if ($_SESSION['stone_search'] != "") {
        $searchval = escapeForDB($_SESSION['stone_search']);
        $filt .= " AND (";
        $filt .= " name LIKE '%" . $searchval . "%' ";
        $filt .= ")";
      }

      $this->pager->count = Stones::count_all_by([], $filt);
      if (!$this->pager->count)
        $this->pager->count = 0;

      $filt .= "ORDER BY brandId, name";
      $filt .= $this->pager->getLimitQuery();
      $stones = Stones::find_all($filt);
      $this->items = $stones;
      $this->categories = StoneCategory::flattenCategoryTree(StoneCategory::getTree());
      $this->brands = AppModel::mapObjectIds(StoneBrands::getBrands(), "brandId");
      $this->colors = StoneColors::getColors($_SESSION['stone_brand']);
    }


    public function executeStoneedit() {

      $stone = new Stones();
      if (isset($_GET["id"])) {
        $stone = Stones::find_by(["stoneId" => $_GET["id"]]);
        if (!$stone) {
          MessageFlashCoordinator::addMessageAlert("Steen niet gevonden: " . $_GET["id"]);
          ResponseHelper::redirect(PageMap::getUrl('M_STONES_STONES'));
        }
      }
      $brands = StoneBrands::getBrands();
      $colors = StoneColors::getColors();
      $sizes = StoneSizes::getSizes();

      $form = new ModelForm();
      $form->addClass("edit-form");
      $form->setEnctype("multipart/form-data");
      $form->buildElementsFromModel($stone);
      $form->setElementsLabel([
        "display"                   => "Online",
        "category_id"               => "Category",
        "type"                      => "Type",
        "material"                  => "Materiaal",
        "brandId"                   => "Merk",
        "colorId"                   => "Kleur",
        "sizeId"                    => "Formaat",
        "endstone"                  => "Eindsteen",
        "name"                      => "Naam",
        "short"                     => "Naam kort",
        //        "details"    => "Omschrijving", //word nergens gebruikt
        "plantext"                  => "Bestekcode",
        "weight"                    => "Gewicht per stuk",
        "weightm1"                  => "Gewicht per m",
        "stock"                     => "Voorraad",
        "minAmountStones"           => "Voorraad stenen minimaal",
        "minAmountPallet"           => "Voorraad pallets minimaal",
        "amountPerPallet"           => "Aantal per pallet",
        "color_model_code"          => "Kleur/model code",
        "standardOrder"             => "Standaard bestelling",
        "modelSpecialForProduction" => "Speciaal productie model",
        "stoneIncreaseGroup"        => "stoneIncreaseGroup",
        "alert"                     => "Waarschuwings bericht",
        "may_order"                 => "Mag besteld worden",
        "may_not_order_message"     => "Bericht wanneer niet te bestellen",
        "endstones_required"        => "Verplicht met eindstenen bestellen",
        "image"                     => "Afbeelding",
        "pdfLocation"               => "PDF",
      ], true);

      $cat_select = new Select("Category", "category_id", $stone->category_id);
      $cat_select->addOptionHelper("", "Selecteer category...");
      $categories = StoneCategory::getTree();
      foreach ($categories as $cat) {
        $option = new Option($cat->id, $cat->name);
        $cat_select->addOption($option);
        if (isset($cat->children)) {
          foreach ($cat->children as $child) {
            $option = new Option($child->id, "&nbsp;&nbsp;&nbsp;" . $child->name);
            $cat_select->addOption($option);
            if (isset($child->children)) {
              foreach ($child->children as $child1) {
                $option = new Option($child1->id, "&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;" . $child1->name);
                $cat_select->addOption($option);
              }
            }
          }
        }
      }
      $form->addElement($cat_select);

      $form->getElement("may_order")->setTextAfterElement(" " . showHelpButton("Met dit vinkje word de tekst van 'Bericht wanneer niet te bestellen' getoond in de wizard. Bij een lege tekst valt hij terug op een standaard tekst."));
      $form->getElement("endstones_required")->setTextAfterElement(" " . showHelpButton("Dit product is niet te bestellen zonder eindstenen. Oftewel glazuren doen we niet."));

      $select = new Select("Merk", "brandId", $stone->brandId);
      $select->addOptionHelper("", "Selecteer merk...");
      foreach ($brands as $brand) {
        $select->addOptionHelper($brand->brandId, $brand->name);
      }
      $form->addElement($select);

      $select = new Select("Materiaal", "material", $stone->material);
      $select->addOptionHelper("", "Selecteer materiaal...");
      foreach (Stones::MATERIALS as $code => $name) {
        $select->addOptionHelper($code, $name);
      }
      $form->addElement($select);

      $select = new Select("Type", "type", $stone->type);
      $select->addOptionHelper("", "Selecteer type...");
      foreach (Stones::TYPES as $code => $name) {
        $select->addOptionHelper($code, $name);
      }
      $form->addElement($select);

      $select = new Select("Kleur", "colorId", $stone->colorId);
      $select->addOptionHelper("", "Selecteer kleur...");
      foreach ($colors as $color) {
        $option = new Option($color->colorId, $color->name);
        $option->addAtribute("data-brandId", $color->brandId);
        $select->addOption($option);
      }
      $form->addElement($select);

      $select = new Select("Formaat", "sizeId", $stone->sizeId);
      $select->addOptionHelper("", "Selecteer formaat...");
      foreach ($sizes as $size) {
        $option = new Option($size->sizeId, $size->name);
        $option->addAtribute("data-brandId", $size->brandId);
        $select->addOption($option);

      }
      $form->addElement($select);

      $form->getElement("endstone")->setOptionTextHelper([
        "false"        => "Geen",
        "left"         => "Linker eindsteen",
        "right"        => "Rechter eindsteen",
        "leftg"        => "Linker groeven",
        "rightg"       => "Rechter groeven",
        "flat"         => "Vlak",
        "standingside" => "Opstaande zijkant",
        "stuc"         => "Stucprofiel",
      ]);

      $select = new Select("Steenopslaggroep", "stoneIncreaseGroup", $stone->stoneIncreaseGroup);
      $select->addOptionHelper("", "Selecteer steenopslaggroep...");
      foreach (Stones::STEENOPSLAGGROEPEN as $gr) {
        $select->addOptionHelper($gr, $gr);
      }
      $form->addElement($select);

      $standardorder = new Checkbox("Standaard bestelling", "standardOrder", $stone->standardOrder);
      $standardorder->setCheckedValue("yes")->setUncheckedValue("no");
      $form->addElement($standardorder);

      //afbeelding uploaden
      $uploadpathpart = "images/";
      if ($stone->brandId == StoneBrands::BRAND_ID_STJORIS) {
        $uploadpathpart = "stjoris/";
      }
      elseif ($stone->brandId == StoneBrands::BRAND_ID_WIENERBERGER) {
        $uploadpathpart = "terca/";
      }
      $uploadpath_root = DIR_ROOT_HTTPDOCS . 'images/thresholds/';
      $uploader = new Uploader('image', reconstructQuery(['image_delete']), $uploadpath_root . $uploadpathpart);
      $uploader->setAllowed([
        'image/jpeg'  => 'jpg',
        'image/pjpeg' => 'jpg',
        'image/png'   => 'png',
        'image/gif'   => 'gif',
        'image/bmp'   => 'bmp',
      ]);

      $form_uploader = new \Gsd\Form\Elements\Uploader($uploader, "Afbeelding", "image", $stone->image);
      if ($stone->image != "") {
        $form_uploader->setViewUrl('//www.raamdorpel.nl/images/thresholds/' . $stone->image . "?time=" . time());
        $form_uploader->setViewClass("gsd-btn");
        $form_uploader->setShowDeleteCheckbox(true);
      }
      if ($stone->brandId == "" || $stone->name == "") {
        $form_uploader->setDisabled(true);
      }
      $form->addElement($form_uploader);

      //pdf uploaden
      $pdf_uploader = new Uploader('pdfLocation', reconstructQuery(['pdfLocation_delete']), DIR_ROOT_HTTPDOCS . 'images/thresholds/pdf/');
      $pdf_uploader->setAllowed([
        'application/pdf' => 'pdf',
      ]);

      $pdf_form_uploader = new \Gsd\Form\Elements\Uploader($pdf_uploader, "PDF", "pdfLocation", $stone->pdfLocation);
      if ($stone->pdfLocation != "") {
        $pdf_form_uploader->setViewUrl($stone->pdfLocation . "?time=" . time());
        $pdf_form_uploader->setViewClass("gsd-btn");
        $pdf_form_uploader->setShowDeleteCheckbox(true);
      }
      if ($stone->brandId == "" || $stone->name == "") {
        $pdf_form_uploader->setDisabled(true);
      }
      $form->addElement($pdf_form_uploader);

      $form->getElement("alert")->setTextAfterElement(' <a href="#" id="alert_txt" class="gsd-btn gsd-btn-secondary">+ TXT LANGERE LEVERTIJD</a> <a href="#" id="alert_empty" class="gsd-btn gsd-btn-secondary">TXT LEGEN</a>');
      $form->getElement("alert")->setStyle("vertical-align: middle;");

      $form->setElementsRequired([
        "name",
        "type",
        "material",
        "category_id",
        "short",
        "brandId",
        "colorId",
        "sizeId",
      ]);

      if (isset($_POST["form"]["save"]) || isset($_POST["form"]["save_to_list"])) {

        $form->setElementsAndObjectValue($_POST);

        if ($form->isValid()) {

          $old_stone = Stones::find_by(["stoneId" => $stone->stoneId]);

          $stone->name = str_replace("  ", " ", trim($stone->name));

          if ($form->getElement("image")->isUploaded()) {
            $newfilename = StringHelper::slugify($stone->name) . "." . FileHelper::getExtension($stone->image);
            rename($uploadpath_root . $uploadpathpart . $stone->image, $uploadpath_root . $uploadpathpart . $newfilename);
            $stone->image = $uploadpathpart . $newfilename;
            //resize to max 600x600
            ImageHelper::resizeImageGD($uploadpath_root . $stone->image, $uploadpath_root . $stone->image, 600, 600);
          }
          if (isset($_POST["image_delete"])) {
            $stone->removeImageFile();
          }

          if ($form->getElement("pdfLocation")->isUploaded()) {
            $newfilename = '/images/thresholds/pdf/' . StringHelper::slugify($stone->name) . "." . FileHelper::getExtension($stone->pdfLocation);
            rename(DIR_ROOT_HTTPDOCS . '/images/thresholds/pdf/' . $stone->pdfLocation, DIR_ROOT_HTTPDOCS . $newfilename);
            $stone->pdfLocation = $newfilename;
          }
          if (isset($_POST["pdfLocation_delete"])) {
            $stone->removePdfFile();
          }

          if (empty($stone->color_model_code)) $stone->color_model_code = null;

          $stone->save();

          $message = "Steen opgeslagen";
          if ($old_stone && $old_stone->category_id != $stone->category_id) {
            //de categorie van een steen word aangepast. Update alle stone_categories van de quotation
            $quots = Quotations::find_all_by(["stoneId" => $stone->stoneId]);
            foreach ($quots as $quot) {
              $quot->stoneCategoryId = $stone->category_id;
              $quot->save();
            }
            $message .= " - " . count($quots) . " offertes zijn geupdate met een nieuwe steen categorie.";
          }

          MessageFlashCoordinator::addMessage($message);
          if (isset($_POST["form"]["save_to_list"])) {
            ResponseHelper::redirect(PageMap::getUrl('M_STONES_STONES'));
          }
          else {
            ResponseHelper::redirect(PageMap::getUrl('M_STONES_STONES', ['action' => 'stoneedit', 'id' => $stone->stoneId]));
          }
        }

      }

      $this->form = $form;

    }

    public function executeShortelementlist() {
      if (!isset($_SESSION['el_search']))
        $_SESSION['el_search'] = '';
      if (isset($_POST['go'])) {
        $_SESSION['el_search'] = trim($_POST['el_search']);
        ResponseHelper::redirect(reconstructQuery());
      }

      $this->pager = new Pager();
      $this->pager->setWriteCount(true);
      $this->pager->handle();

      $filt = " WHERE 1 ";
      if ($_SESSION['el_search'] != "") {
        $searchval = escapeForDB($_SESSION['el_search']);
        $filt .= " AND (";
        $filt .= " elementLength LIKE '%" . $searchval . "%' ";
        $filt .= ")";
      }

      $this->pager->count = SmallElements::count_all_by([], $filt);
      if (!$this->pager->count)
        $this->pager->count = 0;

      $filt .= "ORDER BY elementLength";
      $filt .= $this->pager->getLimitQuery();

      $this->items = SmallElements::find_all($filt);
    }


    public function executeShortelementedit() {

      $element = new SmallElements();
      if (isset($_GET["id"])) {
        $element = SmallElements::find_by(["seId" => $_GET["id"]]);
      }

      $form = new ModelForm();
      $form->addClass("edit-form");
      $form->buildElementsFromModel($element);
      $form->setElementsLabel([
        "elementLength"  => "Element lengte",
        "stoneAmount"    => "Aantal stenen",
        "fitStoneAmount" => "Aantal passtenen",
        "fitStoneLength" => "Lengte passtenen",
      ], true);
      $form->addElement(new Number("Aantal stenen", "stoneAmount", $element->stoneAmount));
      $form->addElement(new Number("Aantal passtenen", "fitStoneAmount", $element->fitStoneAmount));

      $form->setElementsRequired([
        "elementLength",
        "stoneAmount",
        "fitStoneAmount",
        "fitStoneLength",
      ]);

      if (isset($_POST["form"]["save"]) || isset($_POST["form"]["save_to_list"])) {

        $form->setElementsAndObjectValue($_POST);

        if ($form->isValid()) { //normally like this

          $element->save();

          MessageFlashCoordinator::addMessage("Element opgeslagen");
          if (isset($_POST["form"]["save_to_list"])) {
            ResponseHelper::redirect(PageMap::getUrl('M_SHORTELEMENTS'));
          }
          else {
            ResponseHelper::redirect(PageMap::getUrl('M_SHORTELEMENTS', ['action' => 'shortelementedit', 'id' => $element->seId]));
          }
        }

      }

      $this->form = $form;

    }

    public function executeMitrelist() {
      if (!isset($_SESSION['mitre_search']))
        $_SESSION['mitre_search'] = '';
      if (!isset($_SESSION['mitre_display']))
        $_SESSION['mitre_display'] = '';
      if (!isset($_SESSION['mitre_stonelength']))
        $_SESSION['mitre_stonelength'] = '';

      if (isset($_POST['go'])) {
        $_SESSION['mitre_search'] = trim($_POST['mitre_search']);
        $_SESSION['mitre_display'] = trim($_POST['mitre_display']);
        $_SESSION['mitre_stonelength'] = trim($_POST['mitre_stonelength']);
        ResponseHelper::redirect(reconstructQuery());
      }

      $this->pager = new Pager();
      $this->pager->setWriteCount(true);
      $this->pager->handle();

      $filt = " WHERE 1 ";
      if ($_SESSION['mitre_display'] != "") {
        $filt .= " AND display='" . $_SESSION['mitre_display'] . "' ";
      }
      if ($_SESSION['mitre_stonelength'] != "") {
        $filt .= " AND stoneLength='" . $_SESSION['mitre_stonelength'] . "' ";
      }
      if ($_SESSION['mitre_search'] != "") {
        $searchval = escapeForDB($_SESSION['mitre_search']);
        $filt .= " AND (";
        $filt .= " angle LIKE '%" . $searchval . "%' ";
        $filt .= ")";
      }

      $this->pager->count = Mitres::count_all_by([], $filt);
      if (!$this->pager->count)
        $this->pager->count = 0;

      $filt .= "ORDER BY stoneLength, angle";
      $filt .= $this->pager->getLimitQuery();

      foreach (Mitres::find_all("GROUP BY stoneLength ORDER BY stoneLength") as $m) {
        $stonelengths[] = $m->stoneLength;
      }

      $this->stonelengths = $stonelengths;
      $this->items = Mitres::find_all($filt);
    }


    public function executeMitreedit() {

      $mitre = new Mitres();
      if (isset($_GET["id"])) {
        $mitre = Mitres::find_by(["mitreId" => $_GET["id"]]);
      }

      $form = new ModelForm();
      $form->addClass("edit-form");
      $form->buildElementsFromModel($mitre);
      $form->setElementsLabel([
        "stoneLength" => "Steenlengte",
        "angle"       => "Hoek º",
        "shortLength" => "Korte maat mm",
        "heartLength" => "Klik maat mm",
        "longLength"  => "Lange maat mm",
        "stoneCount"  => "Aantal stenen",
        "display"     => "Online",
      ], true);

      $form->setElementsRequired([
        "stoneLength",
        "angle",
        "shortLength",
        "heartLength",
        "longLength",
        "stoneCount",
      ]);

      if (isset($_POST["form"]["save"]) || isset($_POST["form"]["save_to_list"])) {

        $form->setElementsAndObjectValue($_POST);

        if ($form->isValid()) { //normally like this

          $mitre->save();

          MessageFlashCoordinator::addMessage("Element opgeslagen");
          if (isset($_POST["form"]["save_to_list"])) {
            ResponseHelper::redirect(PageMap::getUrl('M_MITRES'));
          }
          else {
            ResponseHelper::redirect(PageMap::getUrl('M_MITRES', ['action' => 'mitreedit', 'id' => $mitre->mitreId]));
          }
        }

      }

      $this->form = $form;

    }


    public function executeWindowsilllist() {
      if (!isset($_SESSION['stone_search']))
        $_SESSION['stone_search'] = '';
      if (!isset($_SESSION['stone_display']))
        $_SESSION['stone_display'] = '';
      if (isset($_POST['go'])) {
        $_SESSION['stone_search'] = trim($_POST['stone_search']);
        $_SESSION['stone_display'] = trim($_POST['stone_display']);
        ResponseHelper::redirect(reconstructQuery());
      }

      $this->pager = new Pager();
      $this->pager->setWriteCount(true);
      $this->pager->handle();

      $filt = " WHERE 1 ";
      if ($_SESSION['stone_display'] != "") {
        $filt .= " AND online='" . $_SESSION['stone_display'] . "' ";
      }
      if ($_SESSION['stone_search'] != "") {
        $searchval = escapeForDB($_SESSION['stone_search']);
        $filt .= " AND (";
        $filt .= " name LIKE '%" . $searchval . "%' ";
        $filt .= ")";
      }

      $this->pager->count = Windowsill::count_all_by([], $filt);
      if (!$this->pager->count)
        $this->pager->count = 0;

      $filt .= "ORDER BY sort, name";
      $filt .= $this->pager->getLimitQuery();
      $this->items = Windowsill::find_all($filt);

    }


    public function executeWindowsilledit() {

      $ws = new Windowsill();
      if (isset($_GET["id"])) {
        $ws = Windowsill::find_by_id($_GET["id"]);
      }

      $form = new ModelForm();
      $form->addClass("edit-form");
      $form->setEnctype("multipart/form-data");
      $form->buildElementsFromModel($ws);
      $form->setElementsLabel([
        "name"          => "Naam",
        "online"        => "Online",
        "x1"            => "Naam x1",
        "x2"            => "Naam x2",
        "x3"            => "Naam x3",
        "x4"            => "Naam x4",
        "x5"            => "Naam x5",
        "x6"            => "Naam x6",
        "mitre_factor"  => "Zaagdeel opslag factor",
        "imagefilename" => "Afbeelding",
      ], true);
      $form->getElement("mitre_factor")->setTextAfterElement(" " . showHelpButton("Een vensterbank kan een zaagdeel opslag hebben. Deze factor bepaald of deze word meegerekend, en hoeveel keer. Zijn er 2 zaagdelen, dan kun je bijvoorbeeld factor 2 invoeren. De daadwerkelijk prijs word ingevoerd als zaagdeel opslag, bij de vensterbank prijzen."));


      //afbeelding uploaden
      $uploadpath_root = DIR_ROOT_HTTPDOCS . 'images/thresholds/vensterbanken/';
      $uploader = new Uploader('imagefilename', reconstructQuery(['imagefilename_delete']), $uploadpath_root);
      $uploader->setAllowed([
        'image/jpeg'  => 'jpg',
        'image/pjpeg' => 'jpg',
        'image/png'   => 'png',
        'image/gif'   => 'gif',
        'image/bmp'   => 'bmp',
      ]);

      $form_uploader = new \Gsd\Form\Elements\Uploader($uploader, "Afbeelding", "imagefilename", $ws->imagefilename);
      if ($ws->imagefilename != "") {
        $form_uploader->setViewUrl('//www.raamdorpel.nl/images/thresholds/vensterbanken/' . $ws->imagefilename . "?time=" . time());
        $form_uploader->setViewClass("gsd-btn");
        $form_uploader->setShowDeleteCheckbox(true);
      }
      if ($ws->name == "") {
        $form_uploader->setDisabled(true);
      }
      $form->addElement($form_uploader);

      //afbeelding small uploaden
      $uploadpath_root = DIR_ROOT_HTTPDOCS . 'images/thresholds/vensterbanken/';
      $uploader = new Uploader('imagesmallfilename', reconstructQuery(['imagesmallfilename_delete']), $uploadpath_root);
      $uploader->setAllowed([
        'image/jpeg'  => 'jpg',
        'image/pjpeg' => 'jpg',
        'image/png'   => 'png',
        'image/gif'   => 'gif',
        'image/bmp'   => 'bmp',
      ]);

      $form_uploader = new \Gsd\Form\Elements\Uploader($uploader, "Afbeelding klein voor lijst", "imagesmallfilename", $ws->imagesmallfilename);
      if ($ws->imagesmallfilename != "") {
        $form_uploader->setViewUrl('//www.raamdorpel.nl/images/thresholds/vensterbanken/' . $ws->imagesmallfilename . "?time=" . time());
        $form_uploader->setViewClass("gsd-btn");
        $form_uploader->setShowDeleteCheckbox(true);
      }
      if ($ws->name == "") {
        $form_uploader->setDisabled(true);
      }
      $form->addElement($form_uploader);

      $form->setElementsRequired([
        "name",
        "x1",
        "x2",
      ]);

      if (isset($_POST["form"]["save"]) || isset($_POST["form"]["save_to_list"])) {

        $form->setElementsAndObjectValue($_POST);

        if ($form->isValid()) {

          $ws->name = str_replace("  ", " ", trim($ws->name));

          if ($form->getElement("imagefilename")->isUploaded()) {
            $newfilename = StringHelper::slugify($ws->name) . "." . FileHelper::getExtension($ws->imagefilename);
            rename($uploadpath_root . $ws->imagefilename, $uploadpath_root . $newfilename);
            $ws->imagefilename = $newfilename;
          }
          if (isset($_POST["imagefilename_delete"])) {
            $ws->removeImageFile();
          }
          if ($form->getElement("imagesmallfilename")->isUploaded()) {
            $newfilename = StringHelper::slugify($ws->name) . "_s." . FileHelper::getExtension($ws->imagesmallfilename);
            rename($uploadpath_root . $ws->imagesmallfilename, $uploadpath_root . $newfilename);
            $ws->imagesmallfilename = $newfilename;
          }
          if (isset($_POST["imagesmallfilename_delete"])) {
            $ws->removeImageSmallFile();
          }
          $ws->save();

          MessageFlashCoordinator::addMessage("Vensterbank opgeslagen");
          if (isset($_POST["form"]["save_to_list"])) {
            ResponseHelper::redirect(PageMap::getUrl('M_WINDOWSILLS'));
          }
          else {
            ResponseHelper::redirect(PageMap::getUrl('M_WINDOWSILLS', ['action' => 'windowsilledit', 'id' => $ws->id]));
          }
        }

      }

      $this->form = $form;

    }


    public function executeWindowsilldelete() {
      if (isset($_GET['delid'])) {
        $ph = Windowsill::find_by_id($_GET['delid']);
        if ($ph) {

          $used = OrderElementWindowsill::find_all_by(["windowsill_id" => $ph->id]);
          if (count($used) == 0) {
            $ph->destroy();
            $_SESSION['flash_message'] = "Item is verwijderd.";
          }
          else {
            $ph->online = 0;
            $ph->save();
            $_SESSION['flash_message'] = "Item is op offline gezet. Deze word namelijk al gebruikt in 1 of meer offertes.";
          }

        }
      }
      ResponseHelper::redirect(reconstructQueryAdd());

    }

    public function executeWindowsillmove() {
      $order = 0;
      foreach ($_GET['sortable'] as $id) {
        if ($id != "") {
          $q = "UPDATE " . Windowsill::getTablename() . " SET sort=" . $order . ' WHERE id=' . escapeForDB($id);
          DBConn::db_link()->query($q);
          $order++;
        }
      }
      $this->template = null; //do not load template
    }

    public function executeCategories() {
      $categories = [];
      $catid = isset($_GET["catid"]) ? $_GET["catid"] : "";
      $category = false;

      if ($catid == "") {
        $categories = StoneCategory::find_all("WHERE parent_id IS NULL ORDER BY sort ASC");
      }
      else {
        $category = StoneCategory::find_by_id($catid);
        $categories = StoneCategory::find_all_by(["parent_id" => $category->id], " ORDER BY sort ASC");
        $parents = StoneCategory::getParents($category);
        if ($parents != null) {
          foreach ($parents as $parent) {
            BreadCrumbs::getInstance()->addItem($parent->name, PageMap::getUrl('M_STONES_CATEGORIES') . '?catid=' . $parent->id);
          }
        }
      }

      $stones = Stones::find_all_by(["category_id" => $catid], "ORDER BY brandId, name");

      $this->catid = $catid;
      $this->category = $category;
      $this->categories = $categories;
      $this->brands = AppModel::mapObjectIds(StoneBrands::getBrands(), "brandId");
      $this->stones = $stones;
    }

    public function executeCategoryedit() {

      $cat = new StoneCategory();
      if (isset($_GET["id"])) {
        $cat = StoneCategory::find_by_id($_GET["id"]);
      }

      $form = new ModelForm();
      $form->addClass("edit-form");
      $form->setEnctype("multipart/form-data");
      $form->buildElementsFromModel($cat);
      $form->setElementsLabel([
        "name"      => "Naam",
        "shortname" => "Naam kort",
        "online"    => "Online",
      ], true);
      $form->setElementsRequired([
        "name",
        "shortname",
      ]);

      //afbeelding uploaden
      $uploadpath_root = DIR_UPLOADS . 'stonecategoryimages/';
      $uploader = new Uploader('imagefilename', reconstructQuery(['imagefilename_delete']), $uploadpath_root);
      $uploader->setAllowed([
        'image/jpeg'  => 'jpg',
        'image/pjpeg' => 'jpg',
        'image/png'   => 'png',
        'image/gif'   => 'gif',
        'image/bmp'   => 'bmp',
      ]);

      $form_uploader = new \Gsd\Form\Elements\Uploader($uploader, "Afbeelding", "imagefilename", $cat->imagefilename);
      if ($cat->imagefilename != "") {
        $form_uploader->setViewUrl('/uploads/rde/stonecategoryimages/' . $cat->imagefilename . "?time=" . time());
        $form_uploader->setViewClass("gsd-btn");
        $form_uploader->setShowDeleteCheckbox(true);
      }
      if ($cat->id == "") {
        $form_uploader->setDisabled(true);
      }
      $form->addElement($form_uploader);


      if (isset($_POST["form"]["save"]) || isset($_POST["form"]["save_to_list"])) {

        $form->setElementsAndObjectValue($_POST, $cat);

        if ($form->getElement("imagefilename")->isUploaded()) {
          $newfilename = StringHelper::slugify($cat->name) . "_" . $cat->id . "." . FileHelper::getExtension($cat->imagefilename);
          rename($uploadpath_root . $cat->imagefilename, $uploadpath_root . $newfilename);
          $cat->imagefilename = $newfilename;
          //resize to max 600x600
          ImageHelper::resizeImageGD($uploadpath_root . $cat->imagefilename, $uploadpath_root . $cat->imagefilename, 600, 600);
        }
        if (isset($_POST["imagefilename_delete"])) {
          $cat->removeImageFile();
        }

        if ($form->isValid()) {
          if (isset($_GET["parent_id"]) && $_GET["parent_id"] != "") {
            $cat->parent_id = $_GET["parent_id"];
          }

          //bestandsnaam afbeelding aanpassen naam category.seo friendly
          $imagefilename = $cat->imagefilename ? (StringHelper::slugify($cat->name) . "." . FileHelper::getExtension($cat->imagefilename)) : "";
          if ($cat->imagefilename != "" && $cat->imagefilename != $imagefilename) {
            rename($uploadpath_root . $cat->imagefilename, $uploadpath_root . $imagefilename);
            $cat->imagefilename = $imagefilename;
          }

          $cat->save();

          $_SESSION['flash_message'] = "Gegevens opgeslagen";
          if (isset($_POST["form"]["save_to_list"])) {
            ResponseHelper::redirect(reconstructQuery(["action", "catid", "id"]) . "&catid=" . $cat->parent_id);
          }
          ResponseHelper::redirect(reconstructQuery(["id"]) . "&id=" . $cat->id);
        }

      }

//      $parents = StoneCategory::getParents($category);
//      if($parents != null) {
//        foreach ($parents as $parent) {
//          BreadCrumbs::getInstance()->addItem($parent->name, PageMap::getUrl('M_STONES_CATEGORIES') . '?catid=' . $parent->id);
//        }
//      }

      $this->form = $form;
    }

    public function executeCategorydelete() {
      if (isset($_GET['delid'])) {
        $sc = StoneCategory::find_by_id($_GET['delid']);
        if ($sc) {
          $sc->destroy();
        }
      }
      ResponseHelper::redirect(reconstructQueryAdd());

    }


    public function executeCategorymove() {
      $order = 0;
      foreach ($_GET['sortable'] as $id) {
        if ($id != "") {
          $q = "UPDATE " . StoneCategory::getTablename() . " SET sort=" . $order . ' WHERE id=' . escapeForDB($id);
          DBConn::db_link()->query($q);
          $order++;
        }
      }
      $this->template = null; //do not load template
    }


  }
