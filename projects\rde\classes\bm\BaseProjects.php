<?php
class BaseProjects extends AppModel
{
  const DB_NAME = 'rde_b1mo';
  const TABLE_NAME = 'projects';
  const OM_CLASS_NAME = 'Projects';
  const columns = ['projectId', 'name', 'size', 'pieceprice', 'euro', 'invoiceId', 'quotationId', 'product_id', 'orderNr', 'showOnProductionPage', 'productFromWebshop', 'webshopOnly', 'stone_id', 'mitre_id', 'glaced_left', 'glaced_right'];
  const field_structure = [
    'projectId'                   => ['type' => 'int', 'length' => '11', 'null' => false],
    'name'                        => ['type' => 'varchar', 'length' => '255', 'null' => false],
    'size'                        => ['type' => 'decimal', 'length' => '8,2', 'null' => false],
    'pieceprice'                  => ['type' => 'decimal', 'length' => '8,2', 'null' => false],
    'euro'                        => ['type' => 'decimal', 'length' => '8,2', 'null' => true],
    'invoiceId'                   => ['type' => 'int', 'length' => '11', 'null' => true],
    'quotationId'                 => ['type' => 'int', 'length' => '11', 'null' => true],
    'product_id'                  => ['type' => 'mediumint', 'length' => '8', 'null' => true],
    'orderNr'                     => ['type' => 'int', 'length' => '11', 'null' => false],
    'showOnProductionPage'        => ['type' => 'boolean', 'length' => '1', 'null' => false],
    'productFromWebshop'          => ['type' => 'boolean', 'length' => '1', 'null' => false],
    'webshopOnly'                 => ['type' => 'boolean', 'length' => '1', 'null' => false],
    'stone_id'                    => ['type' => 'mediumint', 'length' => '11', 'null' => true],
    'mitre_id'                    => ['type' => 'mediumint', 'length' => '11', 'null' => true],
    'glaced_left'                 => ['type' => 'boolean', 'length' => '1', 'null' => false],
    'glaced_right'                => ['type' => 'boolean', 'length' => '1', 'null' => false],
  ];

  protected static $primary_key = ['projectId'];
  protected $auto_increment = 'projectId';

  public $projectId, $name, $size, $pieceprice, $euro, $invoiceId, $quotationId, $product_id, $orderNr, $showOnProductionPage, $productFromWebshop, $webshopOnly, $stone_id, $mitre_id, $glaced_left, $glaced_right;

  function __construct($obj_arr = [], &$error_codes = []) {
    parent::__construct();
    $this->setDefaults();
    if (!empty($obj_arr)) {
      $this->from_array($obj_arr);
      $this->valid_required_fields($error_codes);
    }
  }

  public function valid(&$error_codes = []) {
    $errors = [];
    foreach(self::columns as $column) {
      if ($column != $this->auto_increment)
      {
        call_user_func_array([$this, 'v_' . $column], [&$errors]);
      }
    }
    if(empty($errors)) {
      return true;
    }
    $error_codes = array_merge($error_codes, $errors);
    return false;
  }

  public function valid_required_fields(&$error_codes = []) {
    $errors = [];
    foreach (self::columns as $column) {
      $function_name = "v_$column";
      if ($column!=$this->auto_increment) {
        call_user_func_array([$this, $function_name], [&$errors]);
      }
    }
    if (empty($errors)) {
      return true;
    }
    $error_codes = array_merge($error_codes, $errors);
    return false;
  }

  public function setDefaults() {
    $this->size = 1.00;
    $this->pieceprice = 0.00;
    $this->webshopOnly = 0;
    $this->glaced_left = 0;
    $this->glaced_right = 0;
  }



  public function v_projectId(&$error_codes = []) {
    if (!is_null($this->projectId) && strlen($this->projectId) > 0 && self::valid_int($this->projectId, '11')) {
      return true;
    }
    $error_codes[] = 'projectId';
    return false;
  }

  public function v_name(&$error_codes = []) {
    if (!is_null($this->name) && strlen($this->name) > 0 && self::valid_varchar($this->name, '255')) {
      return true;
    }
    $error_codes[] = 'name';
    return false;
  }

  public function v_size(&$error_codes = []) {
    if (!is_null($this->size) && strlen($this->size) > 0 && self::valid_decimal($this->size, '8,2')) {
      return true;
    }
    $error_codes[] = 'size';
    return false;
  }

  public function v_pieceprice(&$error_codes = []) {
    if (!is_null($this->pieceprice) && strlen($this->pieceprice) > 0 && self::valid_decimal($this->pieceprice, '8,2')) {
      return true;
    }
    $error_codes[] = 'pieceprice';
    return false;
  }

  public function v_euro(&$error_codes = []) {
    if (is_null($this->euro) || strlen($this->euro) == 0 || self::valid_decimal($this->euro, '8,2')) {
      return true;
    }
    $error_codes[] = 'euro';
    return false;
  }

  public function v_invoiceId(&$error_codes = []) {
    if (is_null($this->invoiceId) || strlen($this->invoiceId) == 0 || self::valid_int($this->invoiceId, '11')) {
      return true;
    }
    $error_codes[] = 'invoiceId';
    return false;
  }

  public function v_quotationId(&$error_codes = []) {
    if (is_null($this->quotationId) || strlen($this->quotationId) == 0 || self::valid_int($this->quotationId, '11')) {
      return true;
    }
    $error_codes[] = 'quotationId';
    return false;
  }

  public function v_product_id(&$error_codes = []) {
    if (is_null($this->product_id) || strlen($this->product_id) == 0 || self::valid_mediumint($this->product_id, '8')) {
      return true;
    }
    $error_codes[] = 'product_id';
    return false;
  }

  public function v_orderNr(&$error_codes = []) {
    if (!is_null($this->orderNr) && strlen($this->orderNr) > 0 && self::valid_int($this->orderNr, '11')) {
      return true;
    }
    $error_codes[] = 'orderNr';
    return false;
  }

  public function v_showOnProductionPage(&$error_codes = []) {
    if (!is_null($this->showOnProductionPage) && strlen($this->showOnProductionPage) > 0 && self::valid_tinyint($this->showOnProductionPage, '1')) {
      return true;
    }
    $error_codes[] = 'showOnProductionPage';
    return false;
  }

  public function v_productFromWebshop(&$error_codes = []) {
    if (!is_null($this->productFromWebshop) && strlen($this->productFromWebshop) > 0 && self::valid_tinyint($this->productFromWebshop, '1')) {
      return true;
    }
    $error_codes[] = 'productFromWebshop';
    return false;
  }

  public function v_webshopOnly(&$error_codes = []) {
    if (!is_null($this->webshopOnly) && strlen($this->webshopOnly) > 0 && self::valid_tinyint($this->webshopOnly, '1')) {
      return true;
    }
    $error_codes[] = 'webshopOnly';
    return false;
  }

  public function v_stone_id(&$error_codes = []) {
    if (is_null($this->stone_id) || strlen($this->stone_id) == 0 || self::valid_mediumint($this->stone_id, '11')) {
      return true;
    }
    $error_codes[] = 'stone_id';
    return false;
  }

  public function v_mitre_id(&$error_codes = []) {
    if (is_null($this->mitre_id) || strlen($this->mitre_id) == 0 || self::valid_mediumint($this->mitre_id, '11')) {
      return true;
    }
    $error_codes[] = 'mitre_id';
    return false;
  }

  public function v_glaced_left(&$error_codes = []) {
    if (!is_null($this->glaced_left) && strlen($this->glaced_left) > 0 && self::valid_tinyint($this->glaced_left, '1')) {
      return true;
    }
    $error_codes[] = 'glaced_left';
    return false;
  }

  public function v_glaced_right(&$error_codes = []) {
    if (!is_null($this->glaced_right) && strlen($this->glaced_right) > 0 && self::valid_tinyint($this->glaced_right, '1')) {
      return true;
    }
    $error_codes[] = 'glaced_right';
    return false;
  }

  /**
  *@param array $conditions (required)
  *@param String $raw_sql (optional)
  *@return Projects[]
  **/
  public static function find_all_like($conditions, $raw_sql = '') { return parent::generic_find_all_like($conditions, static::OM_CLASS_NAME, $raw_sql); }
  /**
  *@param array $conditions (required)
  *@param String $raw_sql (optional)
  *@return Projects[]
  **/
  public static function find_all_by($conditions, $raw_sql = '') { return parent::generic_find_all_by($conditions, static::OM_CLASS_NAME, $raw_sql); }
  /**
  *@param String $raw_sql (optional)
  *@return Projects[]
  **/
  public static function find_all($raw_sql = '') { return parent::generic_find_all(static::OM_CLASS_NAME, $raw_sql); }
  /**
  *@param array $conditions (required)
  *@param String $raw_sql (optional)
  *@return Projects
  **/
  public static function find_by($conditions, $raw_sql = '') { return parent::generic_find_by($conditions, static::OM_CLASS_NAME, $raw_sql); }
  /**
  *@param String $id (required)
  *@param String $raw_sql (optional)
  *@return Projects
  **/
  public static function find_by_id($id, $raw_sql = '') { return parent::generic_find_by_id($id, static::OM_CLASS_NAME, $raw_sql); }
  /**
  *@param array $conditions (required)
  *@param String $raw_sql (optional)
  *@return int
  **/
  public static function count_all_by($conditions, $raw_sql = '') { return parent::generic_count_all_by($conditions, static::OM_CLASS_NAME, $raw_sql); }
  /**
  *@param array $conditions (required)
  *@param String $raw_sql (optional)
  *@return bool
  **/
  public static function delete_by($conditions, $raw_sql = '') { return parent::generic_delete_by($conditions, static::OM_CLASS_NAME, $raw_sql); }
}