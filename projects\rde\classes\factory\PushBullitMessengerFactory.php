<?php

  class PushBullitMessengerFactory extends PushBullitMessenger {

    public static function getMessage($callerID, $extension = '') {

      $body = "Nummer: " . $callerID . "\n";
      if ($extension != "") {
        $body .= "Extensie: " . $extension . "\n";
      }

      $susers = SandboxUsers::getByPhone($callerID);

      // If the number that is calling is registered redirect directly to their details.
      $url = "https://www.raamdorpel.nl/cms/crm/crm.php?tab=search&phone=" . $callerID;
      if (count($susers) > 0) {
        $suser = $susers[0];
        if ($suser->getNaam() != "") {
          $body .= $suser->getNaam();
        }
        if (isset($suser->company) && $suser->company->name != "") {
          $body .= ' - ' . $suser->company->name;
        }
        if (count($susers) > 1) {
          $body .= "\nOf " . (count($susers) - 1) . ' andere personen/bedri<PERSON><PERSON> met dit nummer.';
        }
      }
      return [
        'url'     => $url,
        'message' => $body,
      ];

    }

  }
