<section id="basket">
  <div>
    <h1><?php echo $wizard_name ?> - verzendkosten</h1>

    <?php include("_wizardheader.php"); ?>

    <?php writeErrors($errors); ?>

    Selecteer uw verzendmethode en vul indien nodig uw afleveradres in.<br/>
    <br/>


    <div id="step1" class="wizard basket-step-1" v-cloak="">
      <form method="post">

        <div class="form-row">
          <label class="col3 col-form-label"><?php echo __('Afleveradres') ?></label>
          <div class="col1">
            <div v-if="is_valid_address" class="input-validation-icon">
              <img src="<?php echo $site->getTemplateUrl() ?>images/check_green.svg" alt="" width="20" />
            </div>
          </div>
          <div class="col7">
            <div class="select" >
              <select name="addressDeliveryId" v-model="quotation_extra.addressDeliveryId">
                <option value="">Selecteer verzendmethode...</option>
                <option v-for="address in addresses" :value="address.addressId">
                  {{ getAddressFormatted(address) }}
                </option>
              </select>
            </div>
          </div>
          <div class="col1">
            <a class="question-mark qtipa fa fa-info-circle" title="Selecteer uw afleveradres, of maak een nieuwe afleveradres aan. U kunt de raamdorpels ook komen ophalen, hiermee bespaart u verzendkosten."></a>
          </div>
        </div>

        <div v-if="quotation_extra.addressDeliveryId=='NEW'" id="wizard_delivery">
          <div>
            <label class="col3 col-form-label" style="font-weight: bold; padding-bottom: 10px;"><?php echo __('Nieuw afleveradres') ?></label>
            <div class="col9">
            </div>
          </div>

          <div class="form-row">
            <label class="col3 col-form-label"><?php echo __("Straat") ?> <span class="form-arterisk">*</span></label>
            <div class="col1">
              <div v-if="is_valid_street" class="input-validation-icon">
                <img src="<?php echo $site->getTemplateUrl() ?>images/check_green.svg" alt="" width="20" />
              </div>
            </div>
            <div class="col7">
              <input type="text" class="form-input" v-model="quotation.street" :ref="'street'" name="street" placeholder="<?php echo __("Straat") ?>" required @change="validateStreet(true)"/>
            </div>
          </div>

          <div class="form-row">
            <label class="col3 col-form-label"><?php echo __("Nummer + toevoeging") ?> <span class="form-arterisk">*</span></label>
            <div class="col1">
              <div v-if="is_valid_nr" class="input-validation-icon">
                <img src="<?php echo $site->getTemplateUrl() ?>images/check_green.svg" alt="" width="20" />
              </div>
            </div>
            <div class="col3">
              <input type="number" class="form-input" name="nr" v-model="quotation.nr" :ref="'nr'" @change="validateNr(true)" placeholder="<?php echo __("Huisnummer") ?>" required maxlength="5"/>
            </div>
            <div class="col4">
              <input type="text" class="form-input" name="ext" v-model="quotation.ext"  placeholder="<?php echo __("Toevoeging") ?>" maxlength="10" />
            </div>
          </div>

          <div class="form-row">
            <label class="col3 col-form-label"><?php echo __("Postcode + plaats") ?> <span class="form-arterisk">*</span></label>
            <div class="col1">
              <div v-if="is_valid_zipcode && is_valid_domestic" class="input-validation-icon">
                <img src="<?php echo $site->getTemplateUrl() ?>images/check_green.svg" alt="" width="20" />
              </div>
            </div>
            <div class="col3">
              <input type="text" class="form-input" v-model="quotation.zipcode" :ref="'zipcode'"  @change="validateZipcode(true)" name="zipcode" id="zipcode" placeholder="<?php echo __("Postcode") ?>" required maxlength="6"/>
            </div>
            <div class="col4">
              <input type="text" class="form-input" v-model="quotation.domestic" :ref="'domestic'" @change="validateDomestic(true)" name="domestic" id="domestic" placeholder="<?php echo __("Plaats") ?>" required />
            </div>
          </div>

        </div>
        <div id="shippingbar">
          <div id="ship" v-if="quotation_extra.addressDeliveryId!='20357'" >Verzendkosten € <?php echo getLocalePrice($vrachtkosten) ?></div>
          <div id="free" v-if="quotation_extra.addressDeliveryId=='20357'" >Ophalen, geen verzendkosten</div>
        </div>
        <br/>
        <button type="submit" name="prev" id="prev" class="btn" style="float: left;" formnovalidate><i class="fa fa-chevron-left"></i> <?php echo __("Naar winkelmandje"); ?></button>
        <button type="submit" name="next" id="next" class="btn" style="float: right;" :class="{ 'disabled': !all_valid }" @click="validate(true,  $event)"><?php echo __("Doorgaan"); ?> <i class="fa fa-chevron-right"></i></button>

      </form>

    </div>


  </div>
</section>

<script type="text/javascript">

  blockEnterSubmit();

  var firsttime = true;
  var form_submit_error_fields = parseJson('<?php echo json_encode($errors) ?>');
  var quotation = parseJson('<?php echo StringHelper::escapeJson((json_encode(AppModel::plainObject($quotation, ["mitre", "heartClickSize"])))) ?>');
  var quotation_extra = parseJson('<?php echo json_encode(AppModel::plainObject($quotation_extra, [])) ?>');
  var addresses = <?php echo json_encode(AppModel::plainObjects($addresses)) ?>;

  var app = Vue.createApp({
    data() {
      return {
        is_valid: {},
        all_valid: false,
        is_active: {},
        form_submit_error_fields: form_submit_error_fields,
        input_errors: {},

        quotation: quotation,
        quotation_extra: quotation_extra,
        addresses: addresses,

        is_valid_address: false,
        is_valid_street: false,
        is_valid_nr: false,
        is_valid_zipcode: false,
        is_valid_domestic: false,
      }
    },
    mounted() {
      this.validate(false);
      firsttime = false;
    },
    watch: {
      'quotation_extra.addressDeliveryId': function() {
        if(quotation_extra.addressDeliveryId=="NEW") {
          //word zichtbaar gezet, leegmaken velden.
          this.quotation.street = '';
          this.quotation.nr = '';
          this.quotation.zipcode = '';
          this.quotation.domestic = '';
          this.quotation.ext = '';
        }
        this.validate(false);
      },
    },
    computed: {
    },
    methods: {

      //validation
      validate: function (show, event) {
        var allvalid = true;
        this.is_valid_address = true;
        if(this.quotation_extra.addressDeliveryId=="NEW") {
          if(!this.validateStreet(show)) allvalid = false;
          if(!this.validateNr(show)) allvalid = false;
          if(!this.validateZipcode(show)) allvalid = false;
          if(!this.validateDomestic(show)) allvalid = false;
        }
        else if(this.quotation_extra.addressDeliveryId=="") {
          this.is_valid_address = false;
          allvalid = false;
        }

        this.all_valid = allvalid;
        if(!allvalid && event) event.preventDefault();
      },
      validateStreet: function (show) {
        var valid = true;
        if(!this.quotation.street || this.quotation.street=="") {
          valid = false;
          if(show) $(this.$refs.street).addClass('inputerror');
        }
        else {
          if(show) $(this.$refs.street).removeClass('inputerror');
        }
        this.is_valid_street = valid;
        if(show) {
          this.validate(false);
        }
        return valid;
      },
      validateNr: function (show) {
        var valid = true;
        if(!this.quotation.nr || this.quotation.nr=="") {
          valid = false;
          if(show) $(this.$refs.nr).addClass('inputerror');
        }
        else {
          if(show) $(this.$refs.nr).removeClass('inputerror');
        }
        this.is_valid_nr = valid;
        if(show) {
          this.validate(false);
        }
        return valid;
      },
      validateZipcode: function (show) {
        var valid = true;
        if(!this.quotation.zipcode || this.quotation.zipcode=="") {
          valid = false;
          if(show) $(this.$refs.zipcode).addClass('inputerror');
        }
        else {
          if(show) $(this.$refs.zipcode).removeClass('inputerror');
        }
        this.is_valid_zipcode = valid;
        if(show) {
          this.validate(false);
        }
        return valid;
      },
      validateDomestic: function (show) {
        var valid = true;
        if(!this.quotation.domestic || this.quotation.domestic=="") {
          valid = false;
          if(show) $(this.$refs.domestic).addClass('inputerror');
        }
        else {
          if(show) $(this.$refs.domestic).removeClass('inputerror');
        }
        this.is_valid_domestic = valid;
        if(show) {
          this.validate(false);
        }
        return valid;
      },
      //other
      getAddressFormatted: function (address) {
        var str = "";
        if(address.addressId==20357) {
          str = "Ophalen - Raambrug 9, 5531 AG Bladel, Nederland";
        }
        else if(address.addressId=="NEW") {
          str = "Nieuw afleveradres";
        }
        else {
          str = "Afleveren - ";
          if(address.type=="visit") {
            str += "Bezoekadres - ";
          }
          if(address.title!="" && address.title!=null) {
            str += address.title+" - ";
          }
          str += address.street+" "+ address.nr;
          if(address.extension!="" && address.extension!=null) {
            str += " "+address.extension;
          }
          str += ", "+address.zipcode+" "+address.domestic+", NL";
        }
        return str;
      }

    }

  })
  app.mount('#step1');

</script>
