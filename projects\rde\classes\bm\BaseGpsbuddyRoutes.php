<?php
class BaseGpsbuddyRoutes extends AppModel
{
  const DB_NAME = 'rde_route';
  const TABLE_NAME = 'gpsbuddy_routes';
  const OM_CLASS_NAME = 'GpsbuddyRoutes';
  const columns = ['routeId', 'cargoReceiptId', 'taskId', 'bid', 'truckId', 'date', 'called', 'company', 'rank', 'subject', 'text', 'notes', 'city', 'longitude', 'latitude', 'domestic', 'mapExtraPoi', 'poiAddresId'];
  const field_structure = [
    'routeId'                     => ['type' => 'int', 'length' => '7', 'null' => false],
    'cargoReceiptId'              => ['type' => 'int', 'length' => '11', 'null' => true],
    'taskId'                      => ['type' => 'int', 'length' => '8', 'null' => true],
    'bid'                         => ['type' => 'int', 'length' => '9', 'null' => false],
    'truckId'                     => ['type' => 'tinyint', 'length' => '2', 'null' => true],
    'date'                        => ['type' => 'date', 'length' => '', 'null' => false],
    'called'                      => ['type' => 'enum', 'length' => '3', 'null' => false, 'enums' => ['Y','N','X']],
    'company'                     => ['type' => 'enum', 'length' => '2', 'null' => false, 'enums' => ['vdh','rde']],
    'rank'                        => ['type' => 'tinyint', 'length' => '3', 'null' => false],
    'subject'                     => ['type' => 'varchar', 'length' => '150', 'null' => true],
    'text'                        => ['type' => 'varchar', 'length' => '250', 'null' => true],
    'notes'                       => ['type' => 'text', 'length' => '', 'null' => true],
    'city'                        => ['type' => 'varchar', 'length' => '125', 'null' => false],
    'longitude'                   => ['type' => 'float', 'length' => '17,15', 'null' => false],
    'latitude'                    => ['type' => 'float', 'length' => '17,15', 'null' => false],
    'domestic'                    => ['type' => 'varchar', 'length' => '255', 'null' => true],
    'mapExtraPoi'                 => ['type' => 'int', 'length' => '1', 'null' => true],
    'poiAddresId'                 => ['type' => 'int', 'length' => '11', 'null' => true],
  ];

  protected static $primary_key = ['routeId'];
  protected $auto_increment = 'routeId';

  public $routeId, $cargoReceiptId, $taskId, $bid, $truckId, $date, $called, $company, $rank, $subject, $text, $notes, $city, $longitude, $latitude, $domestic, $mapExtraPoi, $poiAddresId;

  function __construct($obj_arr = [], &$error_codes = []) {
    parent::__construct();
    $this->setDefaults();
    if (!empty($obj_arr)) {
      $this->from_array($obj_arr);
      $this->valid_required_fields($error_codes);
    }
  }

  /**
  * Set default object values
  **/
  public function setDefaults() {
    $this->called = 'N';
    $this->company = 'rde';
  }

  /**
   * @param array $conditions (required)
   * @param string $raw_sql (optional)
   * @return GpsbuddyRoutes[]
   **/
  public static function find_all_like($conditions, $raw_sql = '') {
    return parent::generic_find_all_like($conditions, static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param array $conditions (required)
   * @param string $raw_sql (optional)
   * @return GpsbuddyRoutes[]
   **/
  public static function find_all_by($conditions, $raw_sql = '') {
    return parent::generic_find_all_by($conditions, static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param string $raw_sql (optional)
   * @return GpsbuddyRoutes[]
   **/
  public static function find_all($raw_sql = '') {
    return parent::generic_find_all(static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param array $conditions (required)
   * @param string $raw_sql (optional)
   * @return GpsbuddyRoutes
   **/
  public static function find_by($conditions, $raw_sql = '') {
    return parent::generic_find_by($conditions, static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param string $id (required)
   * @param string $raw_sql (optional)
   * @return GpsbuddyRoutes
   **/
  public static function find_by_id($id, $raw_sql = '') {
    return parent::generic_find_by_id($id, static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param array $conditions (required)
   * @param string $raw_sql (optional)
   * @return int
   **/
  public static function count_all_by($conditions, $raw_sql = '') {
    return parent::generic_count_all_by($conditions, static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param array $conditions (required)
   * @param string $raw_sql (optional)
   * @return bool
   **/
  public static function delete_by($conditions, $raw_sql = '') {
    return parent::generic_delete_by($conditions, static::OM_CLASS_NAME, $raw_sql);
  }

}