<?php

  AppModel::loadModelClass('ProductModel');

  class Product extends ProductModel {

    public function getPriceByUser($luser = null, $btw = false, $size = 1, $extra_options = []): float {

      $price = $this->price_part;

      //calculat staffel
      if ($staffelprice = $this->getStaffelPrice($size)) {
        $price = $staffelprice;
      }
      if ($luser == null && isset($_SESSION['userObject'])) {
        $luser = $_SESSION['userObject'];
      }

      $priceincreases = false;
//    unset($_SESSION['priceincreases']);
      if ($luser != null) {
        if (!isset($_SESSION['priceincreases'][$luser->userId])) {
          $_SESSION['priceincreases'][$luser->userId] = SandboxUsers::getPriceIncreases($luser->userId);
        }
        $priceincreases = $_SESSION['priceincreases'][$luser->userId];
      }
      else { //particuliere opslag
        if (!isset($_SESSION['priceincreases']["default"])) {
          $_SESSION['priceincreases']["default"] = SandboxUsers::getPriceIncreases();
        }
        $priceincreases = $_SESSION['priceincreases']["default"];
      }
//    pd($price);
      if ($this->isStone()) { //stenen
        if ($this->discountgroup_id == 1) {
          $price *= $priceincreases["stoneIncreaseGroupA"];
        }
        elseif ($this->discountgroup_id == 6) {
          $price *= $priceincreases["stoneIncreaseGroupB"];
        }
        elseif ($this->discountgroup_id == 8) {
          $price *= $priceincreases["stoneIncreaseGroupC"];
        }
        else { //D of niet gezet (discountgroup_id==9)
          $price *= $priceincreases["stoneIncreaseGroupD"];
        }
      }
      elseif ($this->discountgroup_id == 2) { //voegsel
        $price *= $priceincreases["joint"];
      }
      elseif ($this->discountgroup_id == 3) { //koudglazuur
        $price *= $priceincreases["coldGlaze"];
      }
      elseif ($this->discountgroup_id == 4) { //spacers
        $price *= $priceincreases["spacers"];
      }
      elseif ($this->discountgroup_id == 12) { //standaard lengtes
        if ($luser != null) {
          if (true || !isset($_SESSION['prices_standardlengths'][$luser->userId][$this->id])) {
            $stoneId = substr($this->supplier_code, 6);
            if (!is_numeric($stoneId)) return 1000001;
            $stone = Stones::find_by(["stoneId" => $stoneId]);
            $stone_price = Quotations::getPrices(date("Y-m-d"), $luser->userId, $stone);
            if ($stone_price === false) {
              return 1000002;
            }
            $_SESSION['prices_standardlengths'][$luser->userId][$this->id] = round($stone->getMeterprice($stone_price), 2);
          }
          $price = $_SESSION['prices_standardlengths'][$luser->userId][$this->id];
        }
        else {
          if (!isset($_SESSION['prices_standardlengths']["default"][$this->id])) {
            $stoneId = substr($this->supplier_code, 6);
            if (!is_numeric($stoneId)) return 1000001;
            $stone = Stones::find_by(["stoneId" => $stoneId]);
            $stone_price = Quotations::getPrices(date("Y-m-d"), '', $stone);
            if ($stone_price === false) {
              return 1000002;
            }
            $_SESSION['prices_standardlengths']["default"][$this->id] = round($stone->getMeterprice($stone_price), 2);
          }
          $price = $_SESSION['prices_standardlengths']["default"][$this->id];
        }
      }

      if (count($extra_options) > 0) {
        if (isset($extra_options["option"]) && $extra_options["option"] == "glazed") {
          $price *= $priceincreases["glazeside"];
        }
      }

      if ($btw === true) {
        $price *= 1.21;
      }

      return round($price, 2);
    }

    public function isStone() {
      return $this->discountgroup_id == 1 || $this->discountgroup_id == 6 || $this->discountgroup_id == 8 || $this->discountgroup_id == 9;
    }

    public function getFormattedPrice(): string {
      if ($this->price_on_request) return 'Op aanvraag';

      $price = StringHelper::getPriceDot($this->getPriceByUser());
      $intPart = floor((float)$price);
      $decPart = substr($price, strpos($price, '.') + 1);

      if ($decPart === '00' || (int)$decPart === 0) {
        $decPart = '-';
      }

      return "€ $intPart,$decPart";
    }
  }