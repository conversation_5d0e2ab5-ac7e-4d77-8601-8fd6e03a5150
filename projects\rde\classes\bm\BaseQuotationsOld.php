<?php
class BaseQuotationsOld extends AppModel
{
  const DB_NAME = 'rde_b1mo';
  const TABLE_NAME = 'quotations_old';
  const OM_CLASS_NAME = 'QuotationsOld';
  const columns = ['quotationsOldId', 'oldUserId', 'oldQuotationId', 'userId', 'companyId', 'personId', 'folder', 'filename', 'uploadDate', 'factuurId', 'volgnummer', 'split', 'meters', 'offerteNummer', 'factuurNummer', 'factuurDatum', 'datum', 'data', 'status', 'nextroute', 'nextroute_note', 'opdracht_datum', 'uiterlijke_week', 'opmerkingen', 'lijm', 'aflever_adres', 'aflever_postcode', 'aflever_plaats', 'aflever_gsm', 'aflever_contact', 'bouwmatAlleenLijmen', 'bouwmatAfhaalKorting', 'factuurkenmerk'];
  const field_structure = [
    'quotationsOldId'             => ['type' => 'int', 'length' => '11', 'null' => false],
    'oldUserId'                   => ['type' => 'int', 'length' => '11', 'null' => false],
    'oldQuotationId'              => ['type' => 'int', 'length' => '11', 'null' => false],
    'userId'                      => ['type' => 'int', 'length' => '11', 'null' => true],
    'companyId'                   => ['type' => 'int', 'length' => '11', 'null' => true],
    'personId'                    => ['type' => 'int', 'length' => '11', 'null' => true],
    'folder'                      => ['type' => 'varchar', 'length' => '255', 'null' => false],
    'filename'                    => ['type' => 'varchar', 'length' => '255', 'null' => false],
    'uploadDate'                  => ['type' => 'datetime', 'length' => '', 'null' => false],
    'factuurId'                   => ['type' => 'int', 'length' => '6', 'null' => false],
    'volgnummer'                  => ['type' => 'int', 'length' => '6', 'null' => false],
    'split'                       => ['type' => 'int', 'length' => '2', 'null' => false],
    'meters'                      => ['type' => 'float', 'length' => '99,9', 'null' => false],
    'offerteNummer'               => ['type' => 'varchar', 'length' => '9', 'null' => false],
    'factuurNummer'               => ['type' => 'varchar', 'length' => '40', 'null' => false],
    'factuurDatum'                => ['type' => 'date', 'length' => '', 'null' => true],
    'datum'                       => ['type' => 'date', 'length' => '', 'null' => true],
    'data'                        => ['type' => 'text', 'length' => '', 'null' => false],
    'status'                      => ['type' => 'enum', 'length' => '8', 'null' => false, 'enums' => ['open','prepare','produce','pending','deliver','done','splits','invoice']],
    'nextroute'                   => ['type' => 'enum', 'length' => '2', 'null' => false, 'enums' => ['true','false']],
    'nextroute_note'              => ['type' => 'varchar', 'length' => '255', 'null' => false],
    'opdracht_datum'              => ['type' => 'date', 'length' => '', 'null' => true],
    'uiterlijke_week'             => ['type' => 'int', 'length' => '2', 'null' => true],
    'opmerkingen'                 => ['type' => 'tinytext', 'length' => '', 'null' => false],
    'lijm'                        => ['type' => 'enum', 'length' => '2', 'null' => false, 'enums' => ['Y','N']],
    'aflever_adres'               => ['type' => 'varchar', 'length' => '255', 'null' => false],
    'aflever_postcode'            => ['type' => 'varchar', 'length' => '10', 'null' => false],
    'aflever_plaats'              => ['type' => 'varchar', 'length' => '255', 'null' => false],
    'aflever_gsm'                 => ['type' => 'varchar', 'length' => '18', 'null' => false],
    'aflever_contact'             => ['type' => 'varchar', 'length' => '200', 'null' => false],
    'bouwmatAlleenLijmen'         => ['type' => 'float', 'length' => '99,9', 'null' => false],
    'bouwmatAfhaalKorting'        => ['type' => 'float', 'length' => '99,9', 'null' => false],
    'factuurkenmerk'              => ['type' => 'varchar', 'length' => '255', 'null' => false],
  ];

  protected static $primary_key = ['quotationsOldId'];
  protected $auto_increment = 'quotationsOldId';

  public $quotationsOldId, $oldUserId, $oldQuotationId, $userId, $companyId, $personId, $folder, $filename, $uploadDate, $factuurId, $volgnummer, $split, $meters, $offerteNummer, $factuurNummer, $factuurDatum, $datum, $data, $status, $nextroute, $nextroute_note, $opdracht_datum, $uiterlijke_week, $opmerkingen, $lijm, $aflever_adres, $aflever_postcode, $aflever_plaats, $aflever_gsm, $aflever_contact, $bouwmatAlleenLijmen, $bouwmatAfhaalKorting, $factuurkenmerk;

  function __construct($obj_arr = [], &$error_codes = []) {
    parent::__construct();
    $this->setDefaults();
    if (!empty($obj_arr)) {
      $this->from_array($obj_arr);
      $this->valid_required_fields($error_codes);
    }
  }

  /**
  * Set default object values
  **/
  public function setDefaults() {
    $this->meters = 0;
  }

  /**
   * @param array $conditions (required)
   * @param string $raw_sql (optional)
   * @return QuotationsOld[]
   **/
  public static function find_all_like($conditions, $raw_sql = '') {
    return parent::generic_find_all_like($conditions, static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param array $conditions (required)
   * @param string $raw_sql (optional)
   * @return QuotationsOld[]
   **/
  public static function find_all_by($conditions, $raw_sql = '') {
    return parent::generic_find_all_by($conditions, static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param string $raw_sql (optional)
   * @return QuotationsOld[]
   **/
  public static function find_all($raw_sql = '') {
    return parent::generic_find_all(static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param array $conditions (required)
   * @param string $raw_sql (optional)
   * @return QuotationsOld
   **/
  public static function find_by($conditions, $raw_sql = '') {
    return parent::generic_find_by($conditions, static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param string $id (required)
   * @param string $raw_sql (optional)
   * @return QuotationsOld
   **/
  public static function find_by_id($id, $raw_sql = '') {
    return parent::generic_find_by_id($id, static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param array $conditions (required)
   * @param string $raw_sql (optional)
   * @return int
   **/
  public static function count_all_by($conditions, $raw_sql = '') {
    return parent::generic_count_all_by($conditions, static::OM_CLASS_NAME, $raw_sql);
  }

  /**
   * @param array $conditions (required)
   * @param string $raw_sql (optional)
   * @return bool
   **/
  public static function delete_by($conditions, $raw_sql = '') {
    return parent::generic_delete_by($conditions, static::OM_CLASS_NAME, $raw_sql);
  }

}