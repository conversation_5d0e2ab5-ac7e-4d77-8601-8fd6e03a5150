<?php

  AppModel::loadBaseClass('BaseImessage');

  class ImessageModel extends BaseImessage {

    public function getDate($format = 'd-m-Y', $strftime = false) {
      if ($this->date != "") {
        if (!$strftime) {
          return date($format, strtotime($this->date));
        }
        else {
          return DateTimeHelper::strftime($format, strtotime($this->date));
        }
      }

      return '';
    }

    public function getMessageTeaser() {
      return \StringHelper::getTeaser($this->message, 300);
    }

    public function setRead($user_id) {
      $imr = ImessageRead::find_by(['imessage_id' => $this->id, 'insertUser' => $user_id]);
      if (!$imr) {
        $imr = new ImessageRead();
        $imr->imessage_id = $this->id;
        $imr->insertUser = $user_id;
        $imr->save();
      }
    }

    public static function getMyMessages($user, $homepage = 0) {
      $filt = ['online' => 1, 'homepage' => $homepage];
      if ($user->companyId != "" && $user->companyId != 0) {
        if (in_array($user->company->customerGroupId, CustomerGroups::getIdsByTypeid(2))) {
          $filt['constructiontraders'] = '1';
        }
        else {
          $filt['construction'] = '1';
        }
      }
      else {
        $filt['private'] = '1';
      }
      return Imessage::find_all_by($filt, "ORDER BY date DESC");
    }


    public static function unreadMessagesCount() {
      $filt = ['online' => 1, 'homepage' => 0];
      if ($_SESSION['userObject']->companyId != "" && $_SESSION['userObject']->companyId != "0") {
        if (in_array($_SESSION['userObject']->company->customerGroupId, CustomerGroups::getIdsByTypeid(2))) {
          $filt['constructiontraders'] = '1';
        }
        else {
          $filt['construction'] = '1';
        }
      }
      else {
        $filt['private'] = '1';
      }
      $count = Imessage::count_all_by($filt);
      $readcount = count(ImessageRead::getReadMessages($_SESSION['userObject']->userId));
      return $count - $readcount;
    }

    public function getPossibleReaders() {
      $users = SandboxUsers::find_all_by(['blocked' => 'false', 'blockedbyadmin' => 'false']);
      foreach ($users as $luser) {
        if ($luser->companyId != "" && $luser->companyId != "0") {
          $cids[$luser->companyId] = $luser->companyId;
        }
      }
      $groupIds = CrmCompanies::getCompanyGroupsByCompanyId($cids);
      $private = 0;
      $construction = 0;
      $constructiontraders = 0;
      $constructiontradersIds = CustomerGroups::getIdsByTypeid(2);
      foreach ($users as $user) {
        if (!$user->hasCompany()) {
          $private++;
        }
        else {
          $groupId = $groupIds[$user->companyId];
          if (in_array($groupId, $constructiontradersIds)) {
            $constructiontraders++;
          }
          else {
            $construction++;
          }
        }
      }

      $total = 0;
      if ($this->private == 1) {
        $total += $private;
      }
      if ($this->construction == 1) {
        $total += $construction;
      }
      if ($this->constructiontraders == 1) {
        $total += $constructiontraders;
      }
      return $total;
    }

    public function getReadBy() {
      return ImessageRead::count_all_by(["imessage_id" => $this->id]);
    }

    public function destroy() {
      foreach (ImessageRead::find_all_by(["imessage_id" => $this->id]) as $rap) {
        $rap->destroy();
      }
      parent::destroy();
    }


  }