<?php

  trait quotationStep7Actions {

    public function executeWizardstep7() {
      unset($_SESSION["wizard"]);
      $this->quotation = Quotations::getById($_GET["quotationId"]);
      if (!$this->quotation) {
        MessageFlashCoordinator::addMessageAlert("Offerte niet gevonden of reeds afgesloten.");
        ResponseHelper::redirect($this->wizardurl);
      }
      $this->step = 7;
    }

  }