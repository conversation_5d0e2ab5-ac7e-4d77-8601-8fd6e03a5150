<?php
  TemplateHelper::includePartial('_tabs.php', 'sandbox');
  include("_header.php")
?>

<?php writeErrors($form_person->getErrors(), true); ?>

<form method="post" name="form" id="form" class="edit-form">

  <table class="default_table">
    <tr class="dataTableHeadingRow">
      <td>Item</td>
      <td>Setting</td>
    </tr>
    <?php foreach ($form_person->getElements() as $element): ?>
      <tr class="dataTableRow">
        <td class="head"><?php echo $element->getLabel() ?> <?php if($element->isRequired()): ?><span class="asterisk">*</span><?php endif; ?></td>
        <td><?php $element->render() ?></td>
      </tr>
    <?php endforeach; ?>
  </table>

  <input type="submit" name="cancel" value="Vorige stap"/>
  <input type="submit" name="go" value="Opslaan en afronden" class="gsd-btn gsd-btn-primary"/>

</form>

<script type="text/javascript">
  $(document).ready(function() {

  });
</script>
