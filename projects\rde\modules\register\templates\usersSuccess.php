<section>

  <div class="contenttxt">
    <h1>Gebruikers</h1>
    <div  style="float:right;margin: 0 0 10px 15px">
      <a href="?action=adduser" class="btn btn-primary" title=""><i class="icon-user"></i> NIEUWE GEBRUIKER</a>
    </div>
    Op deze pagina vindt u een overzicht van uw collega's die ook toegang hebben tot het bestel systeem.
    <br/><br/>
    <?php if(count($users) == 0): ?>
      Er zijn geen collega gebruikers gevonden.
    <?php else: ?>

      <table class="download">
        <tbody>
        <tr>
          <th>Naam</th>
          <th>E-mailadres</th>
          <th  style="text-align: right">Verwijderen</th>
        </tr>
        <?php
          /** @var SandboxUsers $user */
          foreach ($users as $user): ?>
            <tr class="trhover">
              <td><?php echo $user->getNaam() ?></td>
              <td><?php echo $user->email ?></td>
              <td style="text-align: right">
                <?php echo BtnHelper::getRemove('?action=userdelete&id='.$user->userId, __("Weet u zeker dat u deze gebuiker wilt verwijderen? De gemaakte offertes blijven bestaan, de gebruiker kan alleen niet meer inloggen.")) ?>
              </td>
            </tr>
          <?php endforeach; ?>
      </table>

    <?php endif; ?>

    <br/><br/>
  </div>

</section>

