//write cookie for user resolution
function writeCookie() {
  var today = new Date();
  var the_date = new Date("December 31, 2023");
  var the_cookie_date = the_date.toGMTString();
  var availheight=screen.availHeight;
  var availwidth=screen.availWidth;
  var colordepth=screen.colorDepth + "bit";
  var pixeldepth=screen.pixelDepth + "bit";
  var the_cookie = "users_resolution="+ screen.width +"x"+ screen.height;
  var the_cookie = the_cookie + ";path=/;expires=" + the_cookie_date;
  document.cookie=the_cookie
}
if (document.cookie.indexOf('users_resolution') != ''){
  writeCookie();
}

$(document).ready(function() {
  $('[data-href]').on('click', function(e) {

    if(!$(e.target).is('a') && !$(e.target).is('i')) {
      window.location = $(this).data('href');
    }
  });

  //Click event to scroll to top
  $('.scrollToTop').click(function(){
    $('html, body').animate({scrollTop : 0},800);
    return false;
  });

  $(window).scroll(function(){
    if ($(this).scrollTop() > 100) {
      $('.scrollToTop').fadeIn();
    } else {
      $('.scrollToTop').fadeOut();
    }

  });


  //menu
  $("#mainmenu li.haschild").hover(

    //Opens drop down
    function (event) {
      event.preventDefault();
      $(this).find(".child").show();
    },

    //Closes drop down
    function (event) {
      event.preventDefault();
      $(this).find(".child").hide();
    }
  );

  $(".nav.action li.haschild").hover(

    //Opens drop down
    function (event) {
      event.preventDefault();
      $(this).find(".child").show();
    },

    //Closes drop down
    function (event) {
      event.preventDefault();
      $(this).find(".child").hide();
    }
  );

  $(window).on("scroll", function() {
    var fromTop = $(window).scrollTop();
    var topLimit = $('.header').height() - $('.nav').height();
    $("body").toggleClass("down", (fromTop > topLimit));
  });


  $('[data-toggle-offscreen]').click(function(){
    var el = $(this).data('toggle-offscreen');
    var fromTop = $(window).scrollTop();

    $(el).toggleClass('off-screen');

  });

  (function() {

    "use strict";

    var toggles = $(".c-hamburger");

    for (var i = toggles.length - 1; i >= 0; i--) {
      var toggle = toggles[i];
      toggleHandler(toggle);
    };

    function toggleHandler(toggle) {
      $(toggle).on( "click", function(e) {
        e.preventDefault();
        ($(this).hasClass("is-active") === true) ? $(this).removeClass("is-active") : $(this).addClass("is-active");
      });
    }

  })();

  $('#mobilemenu li.first-child > a').append('<div class="mobile-exit"><button class="c-hamburger c-hamburger--htx is-active"><span>toggle menu</span></button></div>');

  $(document.body).on('click', '.mobile-exit', function (e) {
    e.preventDefault();
    $(".mobile-menu .c-hamburger").removeClass("is-active");
    $('.mobile-menu-container').toggleClass('off-screen');
  });

  $('.submenutoggle').click(function(e){
    e.preventDefault();
    $(".sidebar").toggleClass("submenu-active");
  });

  $('#mobilemenu .haschild > a').click(function(e){
    e.preventDefault();

    if(e.target.className != 'mobile-close'){

      if($(this).parents('li').hasClass('active')){
        document.location = $(this).attr('href');
      } else {
        $(this).parents('li').find('> a').append('<div class="mobile-close"><i class="fa fa-angle-left"></i></div>');
        $(this).parents('li').toggleClass('active');
      }
    }
  });

  $(document).on('click','.mobile-close',function(e){
    e.preventDefault();
    $(this).parents('li').removeClass('active');
    $(this).parents('li').find('.mobile-close').remove();
  });

});

function capitalizeFirstLetter(string) {
  return string.charAt(0).toUpperCase() + string.slice(1);
}
//checks if required forms are filled
function validateform(form){

  var message = '';
  var cont = true;

  $(form).find('input.required,textarea.required').each(function() {
    var singleValues = jQuery(this).val();

    if(singleValues == ''){
      jQuery(this).css('border','1px solid red');
      var name = jQuery(this).attr('name');
      if(name=="vraagopmerking") {
        name = "Vraag/opmerking";
      }

      message += "- "+capitalizeFirstLetter(name)+' is leeg<br/>';

      cont = false;
    } else {
      jQuery(this).css('border','1px solid #CCCCCC');
    }

    if(jQuery(this).attr('id') == 'email_field'){

      if(validateEmail(jQuery(this).val()) == false){
        cont = false;
        message += '- Ongeldig e-mail adres.<br/>';
      }

    }

    if(jQuery(this).attr('id') == 'phone_field'){

      if(validatePhone(jQuery(this).val()) == false){
        cont = false;
        message += '- Ongeldig telefoonnummer.<br/>';
      }

    }

  });

  if(cont == true){
    jQuery('.requiredMessage').html();
    return true;
  } else {
    jQuery('.requiredMessage').html(message).addClass("alert alert-danger");
    return false;
  }

}

//update if field isn't empty anymore
jQuery(document).ready(function(){

  $('#refresh-captcha').click(function( event ){
    event.preventDefault();
    src = $('#captcha-image').attr('src').split('?');
    $('#captcha-image').attr( 'src', src[0]+'?'+Math.random());
  });

  jQuery(':input.required').keyup(function() {

    jQuery(this).css('border','1px solid #CCCCCC');

  });

  jQuery('.form input[type=text]').focus(function(){
    this.value = '';
  });

});

//validates email adres
function validateEmail(email){

  var emailPattern = /^([A-Za-z0-9_\-\.])+\@([A-Za-z0-9_\-\.])+\.([A-Za-z]{2,4})$/;
  return emailPattern.test(email);

}

//validates if is phone_number
function validatePhone(phone) {

  //phone.replace(/[\(\)\.\-\ ]/g, '');
  var phonePattern = /^[0-9\(\)\-\+]/;

  return phonePattern.test(phone);
}

(function($) {

  $.fn.iModSlider = function(options) {
    var tabs	= $(options.tabs) || false;
    var output	= $(this);
    var sliders = output.length;

    for(i=0;i<sliders;i++){

      new jQuery.iModSlider(output.eq(i).find(options.tabs), output.eq(i).find('> div'), options);

    }

    return this;
  };

  $.iModSlider = function(tabs, output, options) {

    var options				= options || {};
    var mouse_event 		= options.mouse_event || 'click';
    var total_items			= output.length;
    var visible_item		= options.start_item || 0;
    var scroll_item 		= options.scroll_item || false;
    var next 				= $(options.navigationL) || false;
    var prev				= $(options.navigationR) || false;
    var async 				= options.async || false;
    var pause_on_hover 		= options.pause_on_hover;
    var transition_interval = options.transition_interval || 5000;
    var slide_interval		= options.slide_interval || 500;
    var auto_slide			= options.auto_slide;
    var slide_title			= $(options.slide_title) || false;
    var effect				= options.effect || 'fade';

    //set optiosn
    var slider_width 		= options.slider_width || false;
    var timer;
    var transition_ready	= true;

    if(async){
      transition_interval	= transition_interval * rand(8,12);
    }

    //setup
    init();

    //slide function
    function slide(nr) {

      //clearInterval(timer);
      transition_ready = false;

      if(total_items <= 1)
        return false;

      if (typeof nr == "undefined") {
        nr = visible_item + 1;
        nr = nr >= total_items ? 0 : nr;
      }

      if(tabs != false){
        tabs.removeClass('current').filter(":eq(" + nr + ")").addClass('current');
      }

      if(slide_title){
        $(slide_title).html(output.filter(":eq(" + nr + ")").attr('title'));
      }

      if(effect == 'fade'){
        output.stop(true, true).filter(":visible").fadeOut(slide_interval).removeClass('current');
        output.filter(":eq(" + nr + ")").fadeIn(slide_interval,function() {
          transition_ready = true;
          $(this).addClass('current');
        });
      } else {

        if(visible_item > nr){
          val = '+';
        } else {
          output.filter(":eq(" + nr + ")").css('left',''+ slider_width + 'px');
          val = '-';
        }

        //old
        output.filter(":eq(" + visible_item + ")").animate({
            'left': val + (slider_width ) + 'px'},
          slide_interval,
          effect
        );

        //new
        output.filter(":eq(" + nr + ")").animate({
            'left': 0 + 'px'},
          slide_interval,
          effect,
          function(){
            $(output).removeClass('current');
            $(this).addClass('current');
            transition_ready = true;
          }
        );

      }

      visible_item = nr;

      if(scroll_item ){
        _scroll(output.filter(":eq(" + nr + ")"));
      }

    }

    //scroll function
    function _scroll(li){
      var balloon = $('#twitter_content');
      var post = li.children("p");
      pWidth = post.width();

      if (pWidth > parseInt(balloon.css('width'))){

        var offsX = parseInt(balloon.css('width')) - pWidth;

        post.animate({left: offsX - 20 }, transition_interval, 'linear', function(){post.css('left', offsX);});
      }
    }

    //if is asynchrone
    function rand(l,u){
      return Math.floor((Math.random() * (u-l+1))+l) * 0.1 ;
    }

    //start
    function init(){

      output.width(slider_width);

      if(effect == 'fade'){
        output.hide().eq( visible_item ).show().addClass('current');
      } else {
        output.each(function( index ){
          $(this).css('left','+'+ (slider_width * ( visible_item + 1) ) + 'px');
        });
        output.eq(visible_item).addClass('current').css('left','+'+ 0+ 'px');
      }

      $(slide_title).html(output.eq( visible_item ).attr('title'));
      _scroll(output.eq( visible_item ));

    }

    //if there are any tabs
    if(tabs != false){
      tabs.eq( visible_item ).addClass('current');

      if(mouse_event == 'click'){
        tabs.click(function( event ) {

          if ($(this).hasClass('current') || !transition_ready) {
            event.preventDefault();
            return false;
          }

          slide( tabs.index( this) );
        });
      } else {
        tabs.hover(function() {
          if ($(this).hasClass('current')) {
            return false;
          }

          slide( tabs.index( this) );
        });

      }
    }

//check if there is navigation
    if(next != false && prev != false){

      next.click(function(){
        var next_slide = visible_item + 1;
        if(next_slide >= total_items){
          next_slide = 0;
        }
        slide(next_slide);
        clearInterval(timer);
        timer = setInterval(function () {
          slide();
        }, transition_interval);
      });

      prev.click(function(){
        var prev_slide = visible_item - 1;
        if(prev_slide < 0){
          prev_slide = total_items - 1;
        }
        slide(prev_slide);
        clearInterval(timer);
        timer = setInterval(function () {
          slide();
        }, transition_interval);
      });

    }

    //do the slide
    if(auto_slide != false){

      if (transition_interval > 0) {
        timer = setInterval(function () {
          slide();
        }, transition_interval);

        if (pause_on_hover) {
          output.mouseenter(function() {
            clearInterval( timer );

          }).mouseleave(function() {
            clearInterval( timer );
            timer = setInterval(function () {
              slide();
            }, transition_interval);
          });
        }
      }
    }

  };
})(jQuery);


var oAjax = new Object();

oAjax.READY_STATE_UNITIALIZED	= 0;
oAjax.READY_STATE_LOADING		= 1;
oAjax.READY_STATE_LOADED		= 2;
oAjax.READY_STATE_INTERACTIVE	= 3;
oAjax.READY_STATE_COMPLETE		= 4;

oAjax.sendRequest = function(url, params, callback, onerror) {
  this.sURI		= url;
  this.sParams	= (params) ? params : null;
  this.cRequest 	= null;
  this.sCallBack	= callback;
  this.onerror	= (onerror) ? onerror : this.defaultError;
  this.loadXMLDoc(url, params);

}
oAjax.sendRequest.prototype={
  loadXMLDoc:function(p_sURI, p_sParams) {
    if(window.XMLHttpRequest) {
      this.cRequest = new XMLHttpRequest();

    }else if(window.ActiveXObject) {
      this.cRequest = new ActiveXObject('Microsoft.XMLHTTP');

    }

    if(this.cRequest) {
      try {
        var loader = this;
        this.cRequest.onreadystatechange = function() {
          loader.onReadyState.call(loader);
        }

        var method = 'GET';

        if(p_sParams != '') {
          sMethod = 'POST';
        }

        this.cRequest.open(sMethod, p_sURI, true);
        this.cRequest.send(p_sParams);

      }
      catch(err) {
        this.onerror.call(this);

      }
    }
  },
  onReadyState:function() {
    var cRequest = this.cRequest;
    var iReady = cRequest.readyState;

    if(iReady == oAjax.READY_STATE_COMPLETE) {
      var httpStatus = cRequest.status;

      if(httpStatus == 200 || httpStatus == 0) {
        this.sCallBack.call(this);

      }else{
        this.onerror.call(this);

      }
    }
  },
  defaultError:function() {
    alert('Error fetching data!'
      //  +"\n\nreadyState: "+ this.cRequest.readyState
      //  +"\nstatus: "+ this.cRequest.status
      //  +"\nheaders: "+ this.cRequest.getAllResponseHeaders()
    );
  }
}