<?php

  class registerRdeActions extends gsdActions {

    public function preExecute() {
      $this->site = $_SESSION['site'];
      if (is_numeric($this->pageId)) {
        $page = Page::getPageAndContent($this->pageId, $_SESSION['lang']);
        $this->seo_title = $page->content->getSeoTitle();
        $this->seo_description = $page->content->getSeoDescription();
        $this->page = $page;
      }
      $this->cats = Category::getShopCats();
    }


    public function executeLogin() {

      if (isset($_SESSION["userObject"])) {
        ResponseHelper::redirect("/offerte");
      }

      $login_error = false;
      if (isset($_POST["inloggen"])) {
        $login_username = trim($_POST['email']);
        $login_password = trim($_POST['password']);
        $login_error = __('Ongeldig e-mailadres en/of wachtwoord. Als u uw wachtwoord bent vergeten, kunt u ook de wachtwoord vergeten functie gebruiken.');

        if (!($login_username == "" || $login_password == "")) {
          $user = SandboxUsers::find_by(["email" => $_POST['email'], "blocked" => "false"]);

          if ($user != null) {
            if ($user->blockedbyadmin == "true") {
              $login_error = "Uw account is geblokkeerd. Neem contact op met Raamdorpelelementen BV als dit niet juist is.";
            }
            //          elseif(Setting::isLocked($user)) {
            //            $login_error = "Het systeem is momenteel niet beschikbaar vanwege onderhoud. Probeert u het over een half uur nogmaals. Onze excuses voor het ongemak.";
            //          }
            else {
              $valid = false;
              if (Config::isdefined("MASTER_PASSWORD") && $login_password == Config::get("MASTER_PASSWORD")) {
                $valid = true;
              }
              else {
                $passwordenc = SandboxUsers::encryptPassword($login_password, $user->salt);
                if ($passwordenc == $user->password) {
                  $valid = true;
                }
              }

              if ($valid) {
                SandboxUsers::startSession($user);
                if ($user->email == "<EMAIL>" || $user->email == "<EMAIL>") {
                  $_SESSION["userAdmin"] = true;
                }
                if (isset($_GET["redirect"])) {
                  $_SESSION['flash_message'] = __("U bent ingelogd en doorgestuurd.");
                  ResponseHelper::redirect($_GET["redirect"]);
                }
                elseif (isset($this->fromwebshop)) {
                  ResponseHelper::redirect(PageMap::getUrl("M_BASKET") . '?action=pay1');
                }
                else {
                  if (isset($_GET["redirect"])) {
                    $_SESSION['flash_message'] = __("U bent ingelogd en en teruggestuurd naar de open offerte.");
                    ResponseHelper::redirect($_GET["redirect"]);
                  }
                  $_SESSION['flash_message'] = __("U bent ingelogd.");
                  ResponseHelper::redirect("/offerte");
                }
              }
            }
          }
        }

      }
      $this->login_error = $login_error;
    }

    public function executeStart() {
      if (isset($_GET['supplier'])) {
        $_SESSION['register_referral'] = $_GET['supplier'];
        ResponseHelper::redirect(PageMap::getUrl($this->pageId));
      }


      $errors = [];
      $user = new SandboxUsers();
      $user->gender = 'male';
      $user->country = 'NL';

      if (isset($_POST["registeren"])) {

        $user->email = trim($_POST["email"]);
        $user->passwordIn = trim($_POST['password1']);
        $user->private = trim($_POST['private']);
        $user->gender = trim($_POST["gender"]);
        $user->firstName = trim($_POST["firstName"]);
        $user->lastName = trim($_POST["lastName"]);
        $user->phone = trim($_POST["phone"]);
        $user->mobile = trim($_POST["mobile"]);
        $user->invoice_email = trim($_POST["invoice_email"]);

        // fall back to the name of the user if companyName is empty
        $user->companyName = empty($_POST["companyName"]) ? $user->getNaam() : trim($_POST["companyName"]);

        $user->street = trim($_POST["street"]);
        $user->nr = trim($_POST["nr"]);
        $user->extension = trim($_POST["extension"]);
        $user->zipcode = str_replace(' ', '', strtoupper($_POST["zipcode"]));
        $user->domestic = trim($_POST["domestic"]);
        $user->country = trim($_POST["country"]);

        if ($user->nr == "" || !is_numeric($user->nr)) {
          $errors[] = "Huissnummer is leeg of ongeldig";
        }
        if (!ValidationHelper::isEmail($user->email)) {
          $errors['email'] = __("E-mailadres ontbreekt of is incorrect");
        }
        elseif (!SandboxUsers::isEmailUnique($user->email)) {
          //emailadres al in gebruik voor andere account?
          $user_in_db = SandboxUsers::find_by(['email' => $user->email]);
          if ($user_in_db->blockedbyadmin == "true") {
            $errors['email'] = __("Uw e-mailadres is geblokkeerd. Neem contact op met Raamdorpelelementen BV als dit onjuist is.");
          }
          elseif ($user_in_db->blocked == "true") {
            $errors['email'] = __("Dit e-mailadres is reeds bekend in ons systeem, maar nog niet bevestigd. U heeft een e-mail gehad om uw e-mailadres te bevestigen. U kunt ook de wachtwoord vergeten functionaliteit gebruiken op de inloggen pagina om uw account te activeren.");
          }
          else {
            $errors['email'] = __("Dit e-mailadres is reeds bekend in ons syteem. Bent u uw wachtwoord vergeten? Dan kunt u op de inlogpagina met de wachtwoord vergeten functionaliteit uw wachtwoord opnieuw instellen.");
          }
          $errors['email_confirm'] = true;
        }

        $passwordValid = ValidationHelper::isPasswords($_POST['password1'], $_POST['password2']);
        if ($passwordValid !== true) {
          $errors['password1'] = $passwordValid;
          $errors['password2'] = true;
        }

        if (!ValidationHelper::isZip($user->zipcode, strtolower($user->country))) {
          if ($user->country == 'BE') {
            $errors['zipcode'] = 'Een Belgische postcode moet uit 4 cijfers bestaan.';
          }
          elseif ($user->country == 'DE') {
            $errors['zipcode'] = 'Een Duitse postcode moet uit 4 cijfers bestaan.';
          }
          else {
            $errors['zipcode'] = 'Een Nederlandse postcode moet uit 4 cijfers en 2 letters bestaan.';
          }
        }
        if ($user->domestic == "") {
          $errors['domestic'] = 'Plaats is verplicht.';
        }
        if ($user->street == "") {
          $errors['street'] = 'Straat is verplicht.';
        }

        if ($user->invoice_email != "" && !ValidationHelper::isEmail($user->invoice_email)) {
          $errors['email'] = __("Factuur e-mailadres is incorrect");
        }

        if (count($errors) == 0) {

          $user->accounttype = 'purchase';
          $user->ip = $_SERVER["REMOTE_ADDR"];
          $user->salt = SandboxUsers::getSalt();
          $user->payterm = 14;
          //$user->tradeRegNo = ''; //KVK nummer, kwam uit GET paramater in oude systeem maar er was geen input veld...

          $user->phone = StringHelper::cleanPhone($user->phone);
          $user->mobile = StringHelper::cleanPhone($user->mobile);

          if ($user->invoice_email == "") {
            $user->invoice_email = $user->email;
          }

          if (ValidationHelper::isMobile($user->phone) && !ValidationHelper::isMobile($user->mobile)) {
            //mobiel en vaste nummer zijn omgedraaid. Omkeren maar
            $mob = $user->mobile;
            $user->mobile = $user->phone;
            if (ValidationHelper::isPhone($mob)) {
              $user->phone = $mob;
            }
          }

          $user->password = SandboxUsers::encryptPassword($user->passwordIn, $user->salt);

          $user->supplier = '';
          if (isset($_SESSION['register_referral'])) {
            $user->supplier = $_SESSION['register_referral'];
          }

          if ($user->email == $user->invoice_email) {
            $user->invoice_email_confirmed = 1;
          }
          else {
            MailsFactory::sendConfirmInvoiceEmailadres($user);
          }

          if ($user->isPrivate()) {

            //particulier mag direct verder
            $user->blocked = 'false';
            $user->save();

            SandboxUsers::startSession($user);

            if (isset($_GET["fromwebshop"])) {
              MessageFlashCoordinator::addMessage("Uw account is aangemaakt, en u kunt direct verder gaan met bestellen.");
              ResponseHelper::redirect(PageMap::getUrl('M_BASKET') . '?action=pay1');
            }
            else {
              if (isset($_GET["redirect"])) {
                MessageFlashCoordinator::addMessage("Uw account is aangemaakt, en u bent teruggestuurd naar uw open offerte.");
                ResponseHelper::redirect($_GET["redirect"]);
              }
              MessageFlashCoordinator::addMessage("Uw account is aangemaakt, en u kunt direct offertes gaan aanmaken.");
              ResponseHelper::redirect('/offerte');
            }
          }
          else {

            //bedrijf moet zijn email eerst nog bevestigen
            $user->blocked = 'true';
            $user->save();

            MailsFactory::sendConfirmEmailadres($user, $_GET["redirect"] ?? false);

            $url = PageMap::getUrl(207) . '?action=confirmemailsend';
            if (isset($_GET["fromwebshop"])) {
              $url .= "&fromwebshop=1";
            }
            if (isset($_GET["redirect"])) {
              $url .= "&redirect=" . $_GET["redirect"];
            }
            ResponseHelper::redirect($url);
          }

        }

      }

      $this->user = $user;
      $this->errors = $errors;

    }

    public function executeConfirmemail() {
      $valid = false;
      $error = "Het valideren van uw account is mislukt.<br/>Heeft u de complete link uit de e-mail gekopieerd naar de browser?<br/>Lukt het niet, neem dan contact op met Raamdorpelelementen BV.";
      if (isset($_GET["code"])) {
        $user = SandboxUsers::find_by(['password' => $_GET["code"]]);
        if ($user) {
          if ($user->blockedbyadmin == "true") {
            $error = "U account is geblokkeerd.<br/>Neem contact op met Raamdorpelelementen BV als dit onjuist is.";
          }
          else {
            if ($user->blocked == "true") {
              $user->blocked = "false";
              if ($user->invoice_email == $user->email && $user->invoice_email_confirmed == 0) {
                //emailadressen gelijk, dan ook factuur emailadres op bevestigd.
                $user->invoice_email_confirmed = 1;
              }
              $user->save();
            }
            else {
              SandboxUsers::stopSession();
              MessageFlashCoordinator::addMessage("Dit account is al geactiveerd, u kunt inloggen met uw e-mailadres en wachtwoord.");
              ResponseHelper::redirect('/offerte');
            }
            $valid = true;

            //inloggen
            SandboxUsers::startSession($user);

            if (isset($_GET["redirect"])) {
              MessageFlashCoordinator::addMessage("Uw account is gevalideerd, en uw bent direct doorgestuurd naar uw open offerte.");
              ResponseHelper::redirect($_GET["redirect"]);
            }

          }
        }
      }
      if (!$valid) {
        $this->error = $error;
        $this->template = "confirmemailFailed.php";
      }
    }


    public function executeConfirminvoiceemail() {

      if (isset($_GET["code"]) && isset($_GET["email"])) {
        $user = SandboxUsers::find_by(['password' => $_GET["code"]]);
        if ($user) {
          $email = User::decrypt($_GET["email"]);
          if (!$user->hasCompany()) {
            if ($user->invoice_email == $email) {
              $user->invoice_email_confirmed = 1;
              $user->save();
              MessageFlashCoordinator::addMessage("Factuur e-mailadres " . $user->invoice_email . " bevestigd. Wij factureren voortaan op dit e-mailadres.");
              ResponseHelper::redirect(PageMap::getUrl(207));
            }
          }
          else {
            $invoiceparty = CrmInvoiceparties::find_by(["companyId" => $user->companyId, "email" => $email]);
            if ($invoiceparty) {
              $invoiceparty->emailConfirmed = 1;
              $invoiceparty->save();
              MessageFlashCoordinator::addMessage("Factuur e-mailadres " . $invoiceparty->email . " bevestigd. Wij factureren voortaan op dit e-mailadres.");
              ResponseHelper::redirect(PageMap::getUrl(207));
            }
          }
        }
      }
      MessageFlashCoordinator::addMessageAlert("Bevestigen mislukt. Heeft u de complete url in de browser gekopieërd?");
      ResponseHelper::redirect(PageMap::getUrl(207));
    }


    public function executeConfirmemailsend() {
    }

    public function executeMyinfo() {

      if (!isset($_SESSION['userObject'])) {
        ResponseHelper::redirect('/');
      }

      $errors = [];
      $user = SandboxUsers::getUserAndCompany($_SESSION['userObject']->userId);
      $banksaccounts = [];
      $invoiceparty = false;
      if ($user->hasCompany()) {
        $invoiceparty = CrmInvoiceparties::find_by(["companyId" => $user->companyId]);
        if (empty($user->company->visitAddressId)) {
          ResponseHelper::redirectMessage("U kunt niet uw gegevens aanpassen, aangezien uw bedrijf bezoek adres niet is geverifieerd. Klopt dit niet? Neem dan contact op.");
        }
        if (!$invoiceparty) {
          $invoiceparty = new CrmInvoiceparties();
          $invoiceparty->companyId = $user->companyId;
          $invoiceparty->addressId = $user->company->visitAdressId;
          $invoiceparty->invoiceTerm = "14";
        }
        $banksaccounts = CrmBankaccounts::find_all_by(["companyId" => $user->companyId]);
        $countb = count($banksaccounts);
        for ($tel = 0; $tel < 3 - $countb; $tel++) {
          $banksaccounts[] = new CrmBankaccounts();
        }
      }

      if (isset($_POST["go"]) || isset($_POST["go_send"])) {

        $user->email = trim($_POST["email"]);
        $user->passwordIn = trim($_POST['password1']);
        $user->private = trim($_POST['private']);
        $user->gender = trim($_POST["gender"]);
        $user->firstName = trim($_POST["firstName"]);
        $user->lastName = trim($_POST["lastName"]);
        $user->phone = trim($_POST["phone"]);
        $user->mobile = trim($_POST["mobile"]);

        $user->street = trim($_POST["street"]);
        $user->nr = intval(trim($_POST["nr"]));
        $user->extension = trim($_POST["extension"]);
        $user->zipcode = str_replace(' ', '', strtoupper($_POST["zipcode"]));
        $user->domestic = trim($_POST["domestic"]);
        $user->country = trim($_POST["country"]);

        if (!$user->hasCompany()) {
          $user->invoice_email = $_POST["invoice_email"];
        }
        if ($user->firstName == "") {
          $errors[] = "Voornaam is verplicht";
        }
        if ($user->lastName == "") {
          $errors[] = "Achternaam is verplicht";
        }
        if ($user->phone == "") {
          $errors[] = "Telefoonnummer is verplicht";
        }
        elseif (strlen($user->phone) > 15) {
          $errors[] = "Telefoonnummer te lang";
        }
        if (strlen($user->mobile) > 15) {
          $errors[] = "Mobielnummer te lang";
        }
        if ($user->domestic == "") {
          $errors['domestic'] = 'Plaats is verplicht.';
        }
        if ($user->street == "") {
          $errors['street'] = 'Straat is verplicht.';
        }
        if ($user->nr == "" || !is_numeric($user->nr)) {
          $errors[] = "Huissnummer is leeg of ongeldig";
        }
        if (!ValidationHelper::isEmail($user->email)) {
          $errors['email'] = __("E-mailadres ontbreekt of is incorrect");
        }
        elseif (!SandboxUsers::isEmailUnique($user->email, $user->userId)) {  //aangepaste emailadres gebruikt door een ander?
          $errors['email'] = __("Dit e-mailadres is reeds bekend in onze database. U kunt deze niet voor uw account gebruiken.");
          $errors['email_confirm'] = true;
        }

        if (isset($_POST["password_change"])) {
          $passwordValid = ValidationHelper::isPasswords($_POST['password1'], $_POST['password2']);
          if ($passwordValid !== true) {
            $errors['password1'] = $passwordValid;
            $errors['password2'] = true;
          }
        }


        if (!ValidationHelper::isZip($user->zipcode, strtolower($user->country))) {
          if ($user->country == 'BE') {
            $errors['zipcode'] = 'Een Belgische postcode moet uit 4 cijfers bestaan.';
          }
          elseif ($user->country == 'DE') {
            $errors['zipcode'] = 'Een Duitse postcode moet uit 4 cijfers bestaan.';
          }
          else {
            $errors['zipcode'] = 'Een Nederlandse postcode moet uit 4 cijfers en 2 letters bestaan.';
          }
        }

        if (!$user->hasCompany()) {
          if ($user->invoice_email == "") {
            $user->invoice_email = $user->email;
          }
          elseif (!ValidationHelper::isEmail($user->invoice_email)) {
            $errors['invoice_email'] = 'Voer een geldig factuur e-mailadres in.';
          }
        }
        else {

          $user->company->phone = $_POST["company_phone"];

          $invoiceparty->email = trim($_POST["invoiceemail"]);
          if ($invoiceparty->email != "" && !ValidationHelper::isEmail($invoiceparty->email)) {
            $errors['email'] = 'Voer een geldig factuur e-mailadres in.';
          }
          if (isset($_POST["iban"])) {
            foreach ($_POST["iban"] as $tel => $iban) {
              $banksaccounts[$tel]->IBAN = strtoupper(str_replace(" ", "", $iban));
            }
          }
          foreach ($banksaccounts as $banksaccount) {
            if ($banksaccount->IBAN != "" && !ValidationHelper::isIban($banksaccount->IBAN)) {
              $errors['iban'] = 'Voer een geldig iban rekeningnummer in.';
            }
          }
        }

        if (count($errors) == 0) {

          $db_user = SandboxUsers::getUserAndCompany($user->userId);

          $user->ip = $_SERVER["REMOTE_ADDR"];
          $user->phone = StringHelper::cleanPhone($user->phone);
          $user->mobile = StringHelper::cleanPhone($user->mobile);

          if (ValidationHelper::isMobile($user->phone) && !ValidationHelper::isMobile($user->mobile)) {
            //mobiel en vaste nummer zijn omgedraaid. Omkeren maar
            $mob = $user->mobile;
            $user->mobile = $user->phone;
            $user->phone = $mob;
          }

          if (isset($_POST["password_change"])) {
            $user->password = SandboxUsers::encryptPassword($user->passwordIn, $user->salt);
          }

          $user->save();

          if ($user->personId != '') {
            $person = CrmPersons::find_by(['personId' => $user->personId]);
            $user->copyToPerson($person);
          }

          if (!$user->hasCompany()) {
            $changedInvoiceEmail = false;
            if ($user->invoice_email == $user->email) {
              //als beide emailadressen gelijk zijn, dan meteen markeren als bevestigd.
              $user->invoice_email_confirmed = 1;
              $user->save();
            }
            elseif ($db_user->invoice_email != $user->invoice_email) {
              //invoice emailadres gewijzigd
              $user->invoice_email_confirmed = 0;
              $user->save();
              $changedInvoiceEmail = true;
            }
            if ($changedInvoiceEmail || isset($_POST["go_send"]) && $user->invoice_email != "" && $user->invoice_email_confirmed == 0) {
              MailsFactory::sendConfirmInvoiceEmailadres($user, $invoiceparty);
              $_SESSION['flash_message'] = "Uw gegevens zijn aangepast.";
              ResponseHelper::redirect(reconstructQuery(['action']) . "action=confirminvoiceemailsend");
            }
          }
          else {
            $user->company->save();

            $db_invoiceparty = CrmInvoiceparties::find_by(["companyId" => $user->companyId]);
            $changedInvoiceEmail = false;
            if ($db_invoiceparty->email != $invoiceparty->email) {
              //invoice emailadres gewijzigd
              $invoiceparty->emailConfirmed = 0;
              $changedInvoiceEmail = true;
            }
            $invoiceparty->save();

            foreach ($banksaccounts as $banksaccount) {
              if ($banksaccount->IBAN != "") {
                $banksaccount->companyId = $user->companyId;
                $banksaccount->accountnumber = substr($banksaccount->IBAN, -9);
                $banksaccount->save();
              }
              elseif ($banksaccount->accountId != "" && $banksaccount->IBAN == "") {
                $banksaccount->destroy();
              }
            }

            $_SESSION['userObject'] = $user;

            if ($invoiceparty->emailConfirmed == 0 && $invoiceparty->email != "" && ($changedInvoiceEmail || isset($_POST["go_send"]))) {
              //verstuur confirm
              MailsFactory::sendConfirmInvoiceEmailadres($user, $invoiceparty);
              $_SESSION['flash_message'] = "Uw gegevens zijn aangepast.";
              ResponseHelper::redirect(reconstructQuery(['action']) . "action=confirminvoiceemailsend");
            }

          }


          $_SESSION['flash_message'] = "Uw gegevens zijn aangepast.";
          ResponseHelper::redirect(reconstructQuery());

        }

      }

      $this->user = $user;
      $this->errors = $errors;
      $this->banksaccounts = $banksaccounts;
      $this->invoiceparty = $invoiceparty;
    }

    public function executeConfirminvoiceemailsend() {
    }

    public function executeMysettings() {

      if (!isset($_SESSION['userObject'])) {
        ResponseHelper::redirect('/');
      }

      $errors = [];
      $user = SandboxUsers::getUserAndCompany($_SESSION['userObject']->userId);

      if (isset($_POST["go"])) {

        $user->noti_order_mail = isset($_POST["noti_order_mail"]) ? 1 : 0;
        $user->noti_confirm_mail = isset($_POST["noti_confirm_mail"]) ? 1 : 0;
        $user->noti_delivery_mail = isset($_POST["noti_delivery_mail"]) ? 1 : 0;
        $user->noti_produced_mail = isset($_POST["noti_produced_mail"]) ? 1 : 0;
        $user->sms = isset($_POST["sms"]) ? 1 : 0;
        $user->sms_delivered = isset($_POST["sms_delivered"]) ? 1 : 0;

        if (($user->sms == 1 || $user->sms_delivered == 1) && !ValidationHelper::isMobile($user->mobile)) {
          $errors[] = "Het is niet mogelijk SMS aan te zetten als u geen geldig mobiel nummer heeft ingevuld bij mijn gegevens.";
        }

        if (count($errors) == 0) {

          $user->ip = $_SERVER["REMOTE_ADDR"];
          $user->save();

          $_SESSION['userObject'] = $user;

          $_SESSION['flash_message'] = "Uw instellingen zijn aangepast.";
          ResponseHelper::redirect(reconstructQuery());

        }

      }

      $this->user = $user;
      $this->errors = $errors;

    }


    public function executePasswordforgotten() {
      //$_POST['emailforgotten'] = "<EMAIL>";
      $success = false;
      if (!isset($_POST['emailforgotten']) || $_POST['emailforgotten'] == "" || !ValidationHelper::isEmail($_POST['emailforgotten'])) {
        $forgotten_errors = __("Uw e-mailadres is leeg of ongeldig");
        sleep(2); //even slapen om hackers te dissen
      }
      else {
        //ok dus verzenden
        $user = SandboxUsers::find_by(["email" => $_POST['emailforgotten'], "blocked" => "false"]);

        if ($user != null) {
          if ($user->blockedbyadmin == "true") {
            $forgotten_errors = __('Uw emailadres is geblokkeerd. Neem contact op met Raamdorpelelementen BV als dit onjuist is.');
          }
          else {

            MailsFactory::sendForgotPasswordLink($user, $_SESSION['site'], reconstructQueryAdd() . "action=passwordreset");

            $success = true;
          }
        }
        else {
          $forgotten_errors = __('Uw emailadres is niet in onze database gevonden. Neem contact op als dit niet correct is.');
          sleep(2); //even slapen om hackers te dissen
        }
      }

      $result = ["success" => false, "message" => ""];
      if ($success) {
        $result["success"] = true;
        $result["message"] = __('U heeft een e-mail ontvangen met informatie hoe u uw wachtwoord opnieuw kunt instellen.');
      }
      else {
        $result["message"] = $forgotten_errors;
      }
      ResponseHelper::exitAsJson($result);

    }


    /**
     * Collega gebruikers
     */
    public function executeUsers() {

      if (!isset($_SESSION['userObject']) || !$_SESSION['userObject']->hasCompany()) {
        ResponseHelper::redirect('/');
      }

      $users = SandboxUsers::find_all_by(["companyId" => $_SESSION['userObject']->companyId, 'blockedbyadmin' => 'false', 'private' => 'false'], "AND userId!='" . $_SESSION['userObject']->userId . "' ORDER BY firstname");
      $this->users = $users;

    }

    public function executeAdduser() {

      if (!isset($_SESSION['userObject']) || !$_SESSION['userObject']->hasCompany()) {
        ResponseHelper::redirect('/');
      }

      $errors = [];
      $user = new SandboxUsers();
      $user->gender = 'male';
      $user->country = 'NL';

      if (isset($_POST["go"])) {

        $user->email = trim($_POST["email"]);
        $user->passwordIn = trim($_POST['password1']);
        $user->gender = trim($_POST["gender"]);
        $user->firstName = trim($_POST["firstName"]);
        $user->lastName = trim($_POST["lastName"]);
        $user->phone = trim($_POST["phone"]);
        $user->mobile = trim($_POST["mobile"]);

        if (!ValidationHelper::isEmail($user->email)) {
          $errors['email'] = __("E-mailadres ontbreekt of is incorrect");
        }
        elseif (!SandboxUsers::isEmailUnique($user->email)) {
          //emailadres is al aanwezig in de database. Dat kan niet.
          $errors['email'] = __("Dit e-mailadres is reeds bekend in onze database. Het is niet mogelijk om meerdere accounts aan te maken met hetzelfde e-mailadres.");
          $errors['email_confirm'] = true;
        }

        $passwordValid = ValidationHelper::isPasswords($_POST['password1'], $_POST['password2']);
        if ($passwordValid !== true) {
          $errors['password1'] = $passwordValid;
          $errors['password2'] = true;
        }

        if (count($errors) == 0) {

          $user->companyName = $_SESSION['userObject']->companyName;
          $user->companyId = $_SESSION['userObject']->companyId;
          $user->street = $_SESSION['userObject']->street;
          $user->nr = $_SESSION['userObject']->nr;
          $user->extension = $_SESSION['userObject']->extension;
          $user->zipcode = $_SESSION['userObject']->zipcode;
          $user->domestic = $_SESSION['userObject']->domestic;
          $user->country = $_SESSION['userObject']->country;
          $user->payterm = $_SESSION['userObject']->payterm;
          $user->supplier = $_SESSION['userObject']->supplier;
          $user->private = $_SESSION['userObject']->private;

          $user->accounttype = 'purchase';
          $user->blocked = 'true'; //hij moet zijn email eerst nog bevestigen
          $user->ip = $_SERVER["REMOTE_ADDR"];
          $user->salt = SandboxUsers::getSalt();

          $user->phone = StringHelper::cleanPhone($user->phone);
          $user->mobile = StringHelper::cleanPhone($user->mobile);
          if (ValidationHelper::isMobile($user->phone) && !ValidationHelper::isMobile($user->mobile)) {
            //mobiel en vaste nummer zijn omgedraaid. Omkeren maar
            $mob = $user->mobile;
            $user->mobile = $user->phone;
            $user->phone = $mob;
          }

          $user->password = SandboxUsers::encryptPassword($user->passwordIn, $user->salt);

          //create person
          $person = $user->copyToPerson();

          $user->personId = $person->personId;
          $user->save();

          MailsFactory::sendConfirmEmailadresAddUser($user);

          ResponseHelper::redirect(PageMap::getUrl(207) . '?action=confirmemailadduser');

        }

      }

      $this->user = $user;
      $this->errors = $errors;
    }

    public function executeConfirmemailadduser() {
    }

    public function executeUserdelete() {
      if (!isset($_SESSION["userObject"])) { //uitgelogd
        ResponseHelper::redirect('/');
      }
      $user = SandboxUsers::find_by(['userId' => $_GET['id']]);
      if ($user) {
        if ($user->companyId != $_SESSION['userObject']->companyId) {
          $_SESSION['flash_message_red'] = __("Gebruiker niet verwijderd. Gebruiker is niet gevonden, of u heeft geen rechten om deze te verwijderen.");
          ResponseHelper::redirect(reconstructQueryAdd());
        }
        $user->blocked = 'true';
        $user->blockedbyadmin = 'true';
        $user->save();
        $_SESSION['flash_message'] = __("De gebruiker " . $user->getNaam() . " is verwijderd.");
        ResponseHelper::redirect(reconstructQueryAdd());
      }
      $_SESSION['flash_message_red'] = __("Gebruiker niet verwijderd. Gebruiker is niet gevonden, of u heeft geen rechten om deze te verwijderen.");
      ResponseHelper::redirect(reconstructQueryAdd());
    }

    public function executePostcodeapi() {
      header('Content-Type: application/json');
      $postcodeapi = new PostcodeApi();
      echo $postcodeapi->getAllAsJson($_GET["zipcode"], $_GET["nr"]);
      $this->template = null;
    }


    public function executePasswordreset() {

      if (!isset($_GET["hash"]) || !isset($_GET["id"])) {
        ResponseHelper::redirect("/");
      }
      $l_user = SandboxUsers::find_by(["userId" => $_GET["id"]]);
      if (!$l_user) {
        ResponseHelper::redirect("/");
      }

      $up = SandboxUsersPw::find_by(["userId" => $l_user->userId, "hash" => $_GET["hash"]]);
      if (!$up) {
        MessageFlashCoordinator::addMessageAlert(__("U heeft geen wachtwoord reset aangevraagd of uw link is onjuist. Zorg dat u de complete link uit uw e-mail kopieert naar de browser."));
        ResponseHelper::redirect("/");
      }
      if (time() > strtotime($up->validuntilTS)) {
        MessageFlashCoordinator::addMessageAlert(__("U wachtwoord link is verlopen. De link in uw wachtwoord e-mail heeft een beperkte geldigheid. Vraag opnieuw uw wachtwoord op met de wachtwoord vergeten functionaliteit op de inloggen pagina."));
        ResponseHelper::redirect("/");
      }

      $errors = [];
      if (isset($_POST["go_password"])) {
        $passwordValid = ValidationHelper::isPasswords($_POST['password1'], $_POST['password2']);
        if ($passwordValid !== true) {
          $errors['password1'] = $passwordValid;
          $errors['password2'] = true;
        }
        if (count($errors) == 0) {
          $l_user->password = SandboxUsers::encryptPassword($_POST["password1"], $l_user->salt);
          $l_user->blocked = "false";
          $l_user->save();

          $up->destroy();

          MessageFlashCoordinator::addMessage(__("U wachtwoord is opgeslagen. U kunt nu inloggen."));
          ResponseHelper::redirect('/offerte');

        }
      }

      $this->template = "passwordresetSuccess.php";
      $this->template_wrapper_only = true;
      $this->errors = $errors;
    }


  }