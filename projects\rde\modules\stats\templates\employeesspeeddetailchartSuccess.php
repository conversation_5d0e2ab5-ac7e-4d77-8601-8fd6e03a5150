<?php echo TemplateHelper::includeJavascript(URL_INCLUDES . 'jsscripts/amcharts4/core'); ?>
<?php echo TemplateHelper::includeJavascript(URL_INCLUDES . 'jsscripts/amcharts4/charts'); ?>
<?php echo TemplateHelper::includeJavascript(URL_INCLUDES . 'jsscripts/amcharts4/themes/animated'); ?>

<?php
  TemplateHelper::includePartial('_tabs.php',"stats");
?>
<div class="box">
  <form method="post">

    Verlijmdatum van
    <?php echo getDateSelector("employee_glue_from",$_SESSION["employee_glue_from"]) ?>
    tot
    <?php echo getDateSelector("employee_glue_to",$_SESSION["employee_glue_to"]) ?>

    <input type="submit" value="Zoeken" name="search"/>
  </form>
</div>


<h3>Details: <?php echo $employee->name ?></h3>
<?php echo showHelpButton("Deze grafiek geeft de gemiddelde lijmsnelheid in cm per uur. Als een bestelling verlijmt is over meerdere uren, zal deze bij alle uren meetellen. Hierdoor zal het gemiddelde afvlakken. De grafiek toont alleen metingen met de nieuwe productie app.", "Gemiddelde verlijmsnelheid per uur") ?>
<br/><br/>
<div class="rdechart" id="chart"></div>

<script>
  $(document).ready(function() {
    createBarchart("chart", <?php echo json_encode($chartdata) ?>);
  });

  charts = [];

  function createBarchart(id, chartdata) {

    if(charts[id]) {
      charts[id].dispose();
    }
    // Create chart instance
    var chart = am4core.create(id, am4charts.XYChart);
    chart.data = chartdata.data
    chart.language.locale["_decimalSeparator"] = ",";
    chart.language.locale["_thousandSeparator"] = ".";

    /* Create axes */
    var categoryAxis = chart.xAxes.push(new am4charts.CategoryAxis());
    categoryAxis.dataFields.category = "category";
    categoryAxis.renderer.minGridDistance = 30;
    // categoryAxis.numberFormatter = new am4core.NumberFormatter();
    // categoryAxis.numberFormatter.numberFormat = "###.";

    if(chartdata.series.length>1) {
      categoryAxis.renderer.grid.template.location = 0;
      categoryAxis.renderer.cellStartLocation = 0.05;
      categoryAxis.renderer.cellEndLocation = 0.95;
    }

    /* Create value axis */
    var valueAxis = chart.yAxes.push(new am4charts.ValueAxis());
    valueAxis.min = 0;
    valueAxis.extraMax = 0.1;
    // valueAxis.numberFormatter.numberFormat =  '€ #,###.';
    //

    var clustered = chartdata.series.length>1;
    for (var seriek in chartdata.series) {
      var serie = chartdata.series[seriek];
      createSerie(chart,serie, clustered);
    }

    chart.legend = new am4charts.Legend();

    charts[id] = chart;

  }

  function createSerie(chart, value, clustered) {


    var columnSeries = chart.series.push(new am4charts.ColumnSeries());
    columnSeries.dataFields.categoryX = "category";
    columnSeries.dataFields.valueY = value.valuecolumn;
    columnSeries.name = value.name;
    columnSeries.tooltip.label.textAlign = "middle"
    if(clustered) columnSeries.columns.template.width = am4core.percent(100);

    var labelBullet2 = columnSeries.bullets.push(new am4charts.LabelBullet());
    labelBullet2.label.verticalCenter = "bottom";
    labelBullet2.label.dy = -10;
    labelBullet2.label.text = "{valueY}";

    var columnTemplate2 = columnSeries.columns.template;
    // columnTemplate.tooltipText = "[#fff font-size: 15px]{name} in {categoryX}:\n[/][#fff font-size: 20px]{valueY.formatNumber('€ #,###.')}[/] [#fff]{additional}[/]"
    columnTemplate2.tooltipText = "[#fff font-size: 15px]{name} in {categoryX}:\n[/][#fff font-size: 20px]{valueY}[/] [#fff]{additional}[/]"
    columnTemplate2.propertyFields.fillOpacity = "fillOpacity";
    columnTemplate2.propertyFields.stroke = "stroke";
    columnTemplate2.propertyFields.strokeWidth = "strokeWidth";
    columnTemplate2.propertyFields.strokeDasharray = "columnDash";
  }

</script>

<style>
  .inzicht {
    border: 1px solid #F0F0F0;
    padding: 15px;
    min-height: 350px;
    margin-bottom: 15px;
    display: none;
  }
  .inzicht h3 {
    margin-top: 0;
  }

  .rdechart {
    width: 100%;
    height: 450px;
  }
  .rdeloading {
    margin: 0 auto;
    width: 150px;
    display: block;
  }
  .filter {
    padding: 10px 0;
    background-color: #f2f2f229;
  }
</style>