<?php

  AppModel::loadBaseClass('BaseCrmCompanies');

  class CrmCompaniesModel extends BaseCrmCompanies {

    public static function getCompanyGroupsByCompanyId($cids) {
      $query = 'SELECT companyId,customerGroupId FROM ' . CrmCompanies::getTablename() . ' WHERE companyId IN (' . implode(",", $cids) . ') ';
      $result = DBConn::db_link()->query($query);
      $values = [];
      while ($row = $result->fetch_assoc()) {
        $values[$row["companyId"]] = $row["customerGroupId"];
      }
      return $values;
    }

    public function hasVat() {
      if ($this->country == "NL") return true;
      return false;
    }

    public static function getRelatedCompanies($companyname, $zip, $city = '', $street = '', $nr = '') {

      if ($zip == "" && $companyname == "" && $city == "") {
        return [];
      }
      $companies = [];

      $query = "WHERE (";
      if ($zip != "" && $city != "") {
        $query .= "zipcode LIKE '%" . escapeForDB(StringHelper::cleanZip($zip)) . "%' AND domestic LIKE '%" . escapeForDB(StringHelper::cleanZip($city)) . "%'";
      }
      else {
        $query .= "zipcode LIKE '%" . escapeForDB(StringHelper::cleanZip($zip)) . "%' ";
      }
      $query .= ") ";

      $addresses = [];
      $addressObjs = [];
      foreach (CrmAddresses::find_all($query) as $address) {
        $addresses[$address->type][] = $address->addressId;
        $addressObjs[$address->addressId] = $address;
      }

      if (isset($addresses["visit"])) {
        $filt = [];
        $filt["visitAddressId"] = $addresses["visit"];
        $filt["flagForDeletion"] = 0;
        foreach (CrmCompanies::find_all_by($filt, "LIMIT 100") as $comp) {
          if (!isset($companies[$comp->companyId])) {
            $comp->rank = 1;
            $companies[$comp->companyId] = $comp;
          }
          else {
            $companies[$comp->companyId]->rank++;
          }
        }
      }
      if (isset($addresses["post"])) {
        $filt = [];
        $filt["postAddressId"] = $addresses["post"];
        $filt["flagForDeletion"] = 0;
        foreach (CrmCompanies::find_all_by($filt, "LIMIT 100") as $comp) {
          if (!isset($companies[$comp->companyId])) {
            $comp->rank = 1;
            $companies[$comp->companyId] = $comp;
          }
          else {
            $companies[$comp->companyId]->rank++;
          }
        }
      }

      foreach (CrmCompanies::find_all_like(["name" => "%" . $companyname . "%"], "AND flagForDeletion=0") as $comp) {
        if (!isset($companies[$comp->companyId])) {
          $comp->rank = 10;
          $companies[$comp->companyId] = $comp;
        }
        else {
          $companies[$comp->companyId]->rank++;
        }
      }

      foreach ($companies as $company) {
        if (isset($addressObjs[$company->visitAddressId])) {
          $company->visit = $addressObjs[$company->visitAddressId];
        }
        else {
          $company->visit = CrmAddresses::find_by(["addressId" => $company->visitAddressId]);
        }
        if ($company->visit) {
          if ($company->visit->domestic == $city) $company->rank++;
          if ($company->visit->street == $street) $company->rank++;
          if ($company->visit->street == $street && $company->visit->nr == $nr) $company->rank++;
        }
      }

      usort($companies, function ($a, $b) {
        return $b->rank - $a->rank;
      });

      return $companies;
    }

    public function save(&$errors = []) {
      if ($this->from_db == false) {
        $this->dateCreated = date('Y-m-d H:i:s');
      }
      $this->lastUpdate = date('Y-m-d H:i:s');
      return parent::save($errors);
    }

  }