<?php
class BaseContainersQuotations extends AppModel
{
  const DB_NAME = 'rde_b1mo';
  const TABLE_NAME = 'containers_quotations';
  const OM_CLASS_NAME = 'ContainersQuotations';
  const columns = ['containerQuotationId', 'containerId', 'quotationId', 'deliverDate', 'deliverUserId', 'nextroute', 'returnDate', 'returnUserId'];
  const field_structure = [
    'containerQuotationId'        => ['type' => 'int', 'length' => '6', 'null' => false],
    'containerId'                 => ['type' => 'int', 'length' => '6', 'null' => false],
    'quotationId'                 => ['type' => 'int', 'length' => '6', 'null' => false],
    'deliverDate'                 => ['type' => 'date', 'length' => '', 'null' => true],
    'deliverUserId'               => ['type' => 'int', 'length' => '3', 'null' => true],
    'nextroute'                   => ['type' => 'enum', 'length' => '2', 'null' => false, 'enums' => ['true','false']],
    'returnDate'                  => ['type' => 'date', 'length' => '', 'null' => true],
    'returnUserId'                => ['type' => 'int', 'length' => '3', 'null' => true],
  ];

  protected static $primary_key = ['containerQuotationId'];
  protected $auto_increment = 'containerQuotationId';

  public $containerQuotationId, $containerId, $quotationId, $deliverDate, $deliverUserId, $nextroute, $returnDate, $returnUserId;

  function __construct($obj_arr = [], &$error_codes = []) {
    parent::__construct();
    $this->setDefaults();
    if (!empty($obj_arr)) {
      $this->from_array($obj_arr);
      $this->valid_required_fields($error_codes);
    }
  }

  public function valid(&$error_codes = []) {
    $errors = [];
    foreach(self::columns as $column) {
      if ($column != $this->auto_increment)
      {
        call_user_func_array([$this, 'v_' . $column], [&$errors]);
      }
    }
    if(empty($errors)) {
      return true;
    }
    $error_codes = array_merge($error_codes, $errors);
    return false;
  }

  public function valid_required_fields(&$error_codes = []) {
    $errors = [];
    foreach (self::columns as $column) {
      $function_name = "v_$column";
      if ($column!=$this->auto_increment) {
        call_user_func_array([$this, $function_name], [&$errors]);
      }
    }
    if (empty($errors)) {
      return true;
    }
    $error_codes = array_merge($error_codes, $errors);
    return false;
  }

  public function setDefaults() {
    $this->containerId = 0;
    $this->quotationId = 0;
    $this->nextroute = 'false';
  }



  public function v_containerQuotationId(&$error_codes = []) {
    if (!is_null($this->containerQuotationId) && strlen($this->containerQuotationId) > 0 && self::valid_int($this->containerQuotationId, '6')) {
      return true;
    }
    $error_codes[] = 'containerQuotationId';
    return false;
  }

  public function v_containerId(&$error_codes = []) {
    if (!is_null($this->containerId) && strlen($this->containerId) > 0 && self::valid_int($this->containerId, '6')) {
      return true;
    }
    $error_codes[] = 'containerId';
    return false;
  }

  public function v_quotationId(&$error_codes = []) {
    if (!is_null($this->quotationId) && strlen($this->quotationId) > 0 && self::valid_int($this->quotationId, '6')) {
      return true;
    }
    $error_codes[] = 'quotationId';
    return false;
  }

  public function v_deliverDate(&$error_codes = []) {
    if (is_null($this->deliverDate) || strlen($this->deliverDate) == 0 || self::valid_date($this->deliverDate)) {
      return true;
    }
    $error_codes[] = 'deliverDate';
    return false;
  }

  public function v_deliverUserId(&$error_codes = []) {
    if (is_null($this->deliverUserId) || strlen($this->deliverUserId) == 0 || self::valid_int($this->deliverUserId, '3')) {
      return true;
    }
    $error_codes[] = 'deliverUserId';
    return false;
  }

  public function v_nextroute(&$error_codes = []) {
    if ($this->nextroute == 'true') { return true; }
    if ($this->nextroute == 'false') { return true; }
    $error_codes[] = 'nextroute';
    return false;
  }

  public function v_returnDate(&$error_codes = []) {
    if (is_null($this->returnDate) || strlen($this->returnDate) == 0 || self::valid_date($this->returnDate)) {
      return true;
    }
    $error_codes[] = 'returnDate';
    return false;
  }

  public function v_returnUserId(&$error_codes = []) {
    if (is_null($this->returnUserId) || strlen($this->returnUserId) == 0 || self::valid_int($this->returnUserId, '3')) {
      return true;
    }
    $error_codes[] = 'returnUserId';
    return false;
  }

  /**
  *@param array $conditions (required)
  *@param String $raw_sql (optional)
  *@return ContainersQuotations[]
  **/
  public static function find_all_like($conditions, $raw_sql = '') { return parent::generic_find_all_like($conditions, static::OM_CLASS_NAME, $raw_sql); }
  /**
  *@param array $conditions (required)
  *@param String $raw_sql (optional)
  *@return ContainersQuotations[]
  **/
  public static function find_all_by($conditions, $raw_sql = '') { return parent::generic_find_all_by($conditions, static::OM_CLASS_NAME, $raw_sql); }
  /**
  *@param String $raw_sql (optional)
  *@return ContainersQuotations[]
  **/
  public static function find_all($raw_sql = '') { return parent::generic_find_all(static::OM_CLASS_NAME, $raw_sql); }
  /**
  *@param array $conditions (required)
  *@param String $raw_sql (optional)
  *@return ContainersQuotations
  **/
  public static function find_by($conditions, $raw_sql = '') { return parent::generic_find_by($conditions, static::OM_CLASS_NAME, $raw_sql); }
  /**
  *@param String $id (required)
  *@param String $raw_sql (optional)
  *@return ContainersQuotations
  **/
  public static function find_by_id($id, $raw_sql = '') { return parent::generic_find_by_id($id, static::OM_CLASS_NAME, $raw_sql); }
  /**
  *@param array $conditions (required)
  *@param String $raw_sql (optional)
  *@return int
  **/
  public static function count_all_by($conditions, $raw_sql = '') { return parent::generic_count_all_by($conditions, static::OM_CLASS_NAME, $raw_sql); }
  /**
  *@param array $conditions (required)
  *@param String $raw_sql (optional)
  *@return bool
  **/
  public static function delete_by($conditions, $raw_sql = '') { return parent::generic_delete_by($conditions, static::OM_CLASS_NAME, $raw_sql); }
}