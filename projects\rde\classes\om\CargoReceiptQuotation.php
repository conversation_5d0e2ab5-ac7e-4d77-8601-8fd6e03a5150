<?php

  AppModel::loadModelClass('CargoReceiptQuotationModel');

  class CargoReceiptQuotation extends CargoReceiptQuotationModel {

    public static function updateForCargoReceipt($cargo_receipt_id, $quotationIds) {
      //update quotation cargoreceipts.
      if ($cargo_receipt_id != 0) {
        CargoReceiptQuotation::delete_by(['cargoReceiptId' => $cargo_receipt_id]);
        $quotationIds = array_unique($quotationIds);
        foreach ($quotationIds as $quotId) {
          $crq = new CargoReceiptQuotation();
          $crq->cargoReceiptId = $cargo_receipt_id;
          $crq->quotationId = $quotId;
          $crq->save();
        }
      }
    }

  }