<?php

  use gsdfw\domain\mail\service\WrapperService;

  class reviewsRdeActions extends gsdActions {

    public function executeList() {
      $ratings = CustomerRatingEmails::find_all_by(["inActive" => 0, "sent" => 0], "ORDER BY dateStatusChange DESC");
      $this->ratings = $ratings;
    }

    public function executeDelete() {
      $qre = CustomerRatingEmails::find_by_id($_GET["id"]);
      if ($qre) {
        $qre->destroy();
        MessageFlashCoordinator::addMessage("Item verwijderd");
      }
      ResponseHelper::redirect(reconstructQueryAdd());
    }

    public function executeSend() {
      $qre = CustomerRatingEmails::find_by_id($_GET["id"]);

      $message = "<p>Beste " . $qre->firstname . " " . $qre->lastname . ",</p>";
      $message .= "<p><PERSON><PERSON><PERSON> dank voor uw aankoop bij raamdorpel.nl.</p>";

      $message .= "Wij hechten veel waarde aan hoe u onze service heeft ervaren. ";
      $message .= "Om de klanttevredenheid te verbeteren werken wij samen met de onafhankelijke review site klantenvertellen.nl om reviews te verzamelen.<Br/>";

      $message .= "<Br/>Klik op onderstaande link om ons te beoordelen.<br/><Br/>" .
        "<a href='https://www.klantenvertellen.nl/invite-link/1062537/Raamdorpel.nl?lang=nl'>";
      $message .= "Beoordeel ons op Klantenvertellen";
      $message .= "</a>";
      $message .= "<Br/><Br/>";

      $message .= "Als blijk van waardering verloten wij maandelijks een tegoedbon voor gratis transportkosten.<Br/><Br/>";
      $message .= "Dank u wel voor uw tijd.<Br/><Br/>";

      $message .= "<p>Met vriendelijke groet,<br /><Br/>";
      $message .= "Raamdorpel.nl</p><Br/><Br/>";

      $url = SiteHost::getPrimary(1)->getDomainSmart(true);
      $url .= '/contact?action=reviewsignout&id=' . $qre->id . "&code=" . $qre->randomCode;

      $message .= "Als u geen beoordelingsuitnodigingen van ";
      $message .= "<a href='https://www.raamdorpel.nl/'>raamdorpel.nl</a> ";
      $message .= "wilt ontvangen, klik dan alstublieft op ";
      $message .= "<a href='" . $url . "'>afmelden</a>.";
      $message .= "<p>&nbsp;</p>";

      $bericht = (new WrapperService($message))->wrap();

      $subject = 'Gratis levering bij invullen van een beoordeling!';
      $gsdmailer = GsdMailer::build($qre->email, $subject, $bericht);
      $gsdmailer->setFrom("<EMAIL>");
      $gsdmailer->send();

      $qre->sent = 1;
      $qre->save();

      MessageFlashCoordinator::addMessage("Review e-mail verzonden");
      ResponseHelper::redirect(reconstructQueryAdd());
    }

  }