<?php

  use domain\prices\service\SetSmallOrderAddition;
  use domain\quotations\service\GetExtraProducts;

  trait quotationStep4Actions {


    /**
     * Extra producten
     * Deze pagina kan aangeroepen worden vanuit wizard, maar ook standalone als admin
     */
    public function executeWizardstep4() {
      if (!isset($_SESSION["wizard"]['quotation']) || !isset($_SESSION["wizard"]['elements'])) {
        ResponseHelper::redirect($this->wizardurl);
      }

      $errors = [];
      $singlepage = isset($_GET["singlepage"]);

      /** @var Quotations $quotation */
      $quotation = $_SESSION["wizard"]['quotation'];
      $elements = $_SESSION["wizard"]['elements'];
      $stone = Stones::find_by(["stoneId" => $quotation->stoneId]);
      if (!$stone) {
        MessageFlashCoordinator::addMessageAlert("Steen niet gevonden! Kan offerte niet openen. Neem contact op met Raamdorpelelementen.");
        ResponseHelper::redirect($this->wizardurl);
      }
      $extra_products = [];
      if (isset($_SESSION["wizard"]['extra_products'])) {
        $extra_products = $_SESSION["wizard"]['extra_products'];
      }

      $qetExtraProducts = new GetExtraProducts($quotation, $stone);
      $categories = $qetExtraProducts->fetch();
//      dumpe($categories);

      $categoriesFiltered = [];
      foreach ($categories as $category) {
        if ($_SESSION['wizard']['quotation']->brandId == 1 ) {
          if (str_contains($category->content->name, 'Wienerberger')) continue;
        }

        if ($_SESSION['wizard']['quotation']->brandId == 2 ) {
          if (str_contains($category->content->name, 'St. Joris') || str_contains($category->content->name, 'st joris')) continue;
        }

        $categoriesFiltered[] =  $category;
      }

      $prices = [];
      $allprojects = [];
      foreach ($categoriesFiltered as $cat) {
        foreach ($cat->projects as $key => $project) {
          $product = $project->product;
          if (isset($extra_products[$key])) {
            $extra_products[$key]->found = true;
          }
          $allprojects[$key] = $project;
          if ($product->getStaffel() != "") {
            $prices[$key] = ["staffel" => 1, "value" => $product->getStaffel()];
          }
          else {
            $extra_options = [];
            if ($project->glaced_left == 1 || $project->glaced_right == 1) {
              $extra_options = ["option" => "glazed"];
            }
            $prices[$key] = ["staffel" => 0, "value" => StringHelper::getPriceDot($product->getPriceByUser(null, false, 1, $extra_options))];
          }
        }
      }
      foreach ($extra_products as $k => $ep) {
        if (!isset($ep->found)) {
          unset($extra_products[$k]); //niet in productlijst, maar wel in extra_products. Verwijderen.
        }
      }

      //always reload price increases. Als niet ingelogd, dan gewoon particulier prijzen.
      if (isset($_SESSION['userObject'])) {
        $_SESSION['userObject']->priceincrease = SandboxUsers::getPriceIncreases($_SESSION['userObject']->userId);
      }

      if (isset($_POST["next"]) || isset($_POST["prev"])) {
        if (isset($_POST["size"])) {
          $tel = 1;
          foreach ($_POST["size"] as $key => $size) {
            $extra_project = false;
            if (isset($extra_products[$key])) {
              $extra_project = $extra_products[$key];
              $extra_project->product = $allprojects[$key]->product;
            }
            else {
              $extra_project = $allprojects[$key];
            }
            if (!is_numeric($size) || $size < 0) {
              $size = 0;
            }
            $extra_project->size = $size;
            if ($size > 0) {
              $extra_options = [];
              if ($extra_project->glaced_left == 1 || $extra_project->glaced_right == 1) {
                $extra_options = ["option" => "glazed"];
              }
              $extra_project->pieceprice = $extra_project->product->getPriceByUser(null, false, $size, $extra_options);
              $extra_project->euro = $size * $extra_project->pieceprice;
              $extra_project->orderNr = $tel;

              if($extra_project->product_id == SetSmallOrderAddition::SMALL_ORDER_ADDITION_PRODUCT_ID) {
                //Klein ordertoeslag uitzondering, vinkjes webshop uit
                $extra_project->showOnProductionPage = 0;
                $extra_project->productFromWebshop = 0;
                $extra_project->webshopOnly = 0;
              }
              else {
                $extra_project->showOnProductionPage = 1;
                $extra_project->productFromWebshop = 1;
                $extra_project->webshopOnly = 1;
              }

              $extra_products[$key] = $extra_project;
              $tel++;
            }
          }
          $_SESSION["wizard"]['extra_products'] = $extra_products;
        }

        if (isset($_POST["prev"])) {
          if ($this->hasVerstek($quotation, $elements, $stone)) { //er is verstek naar stap 3
            ResponseHelper::redirect(reconstructQuery(["step"]) . "step=3");
          }
          else {
            ResponseHelper::redirect(reconstructQuery(["step"]) . "step=2");
          }
        }
        if (count($errors) == 0) {
          if ($singlepage) {
            ResponseHelper::redirect("?action=extraproductsedit&save=true");
          }
          else {
            ResponseHelper::redirect(reconstructQuery(["step"]) . "step=5");
          }
        }
      }

      $this->singlepage = $singlepage;
      $this->step = 4;
      $this->errors = $errors;
      $this->quotation = $quotation;
      $this->stone = $stone;
      $this->categories = $categoriesFiltered;
      $this->extra_products = $extra_products;
      $this->prices = $prices;
    }

  }