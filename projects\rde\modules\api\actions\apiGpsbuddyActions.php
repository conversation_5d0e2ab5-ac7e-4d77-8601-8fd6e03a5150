<?php

  use gsdfw\domain\gpsbuddy\service\GpsbuddyApi;


  /**
   * Trait apiCmsActions
   * Gpsbuddy actions
   */
  trait apiGpsbuddyActions {

    /**
     * Start GPSBuddySync for routes
     */
    public function executeSyncGpsBuddy() {

      $request_vars = $this->data->getData();

      $go = true;
      $routeids = explode("_", substr($request_vars['routeids'], 0, strlen($request_vars['routeids']) - 1));

      logToFile("gpsbuddy", "Routes send: " . print_r($routeids, true));

      $query = "SELECT R.* FROM " . GpsbuddyRoutes::getTablename() . " R ";
      $query .= "WHERE R.routeId IN(" . implode(',', $routeids) . ") ";
      $query .= "ORDER BY R.truckId ASC, R.rank ASC, R.routeId ASC";
      //logToFile("mysql", $query);
      $oResult = DBConn::db_link()->query($query);

      $gpsbuddy = GpsbuddyApi::getInstance();
      $gpsbuddy->init();
      foreach (GpsbuddyTrucks::getActiveTrucks() as $tr) {
        $gpsbuddy->addTruck($tr->truckId, $tr->gpsbuddyId);
      }
      if ($gpsbuddy->login()) {
        while ($route = $oResult->fetch_object()) {
          //$route->truckId = 3;
          $gpsbuddy->addTask($route->truckId, $route->subject, $route->text, $route->longitude, $route->latitude);
        }
      }

      logToFile("gpsbuddy", "executeSyncGpsBuddy done");

      RestUtils::sendResponseOK("GPSBUDDY SYNC DONE");


    }

    /**
     * Start GPSBuddySync for montages
     */
    public function executeSyncGpsBuddyMontage() {

      $request_vars = $this->data->getData();

      $go = true;
      $montageids = explode("_", substr($request_vars['montageids'], 0, strlen($request_vars['montageids']) - 1));
      $truckid = $request_vars['truck_id'];

      logToFile("gpsbuddy", "executeSyncGpsBuddyMontage send: " . print_r($montageids, true));

      $gpsbuddy = GpsbuddyApi::getInstance();
      $gpsbuddy->init();
      foreach (GpsbuddyTrucks::getActiveTrucks() as $tr) {
        $gpsbuddy->addTruck($tr->truckId, $tr->gpsbuddyId);
      }
      if ($gpsbuddy->login()) {

        $montages = Montage::find_all_by(["id" => $montageids]);
        foreach ($montages as $montage) {
          $address = CrmAddresses::find_by(["addressId" => $montage->company_address_id]);
          //        pd($address);
          if ($go && $address && $address->latitude != "0" && $address->latitude != "") {
            $taskId = $gpsbuddy->addTask($truckid, $montage->name, $montage->remark, $address->longitude, $address->latitude);
          }
        }
      }

      logToFile("gpsbuddy", "executeSyncGpsBuddyMontage done");

      RestUtils::sendResponseOK("GPSBUDDY SYNC MONTAGES DONE");


    }

    /**
     * Start GPSBuddySync for address
     */
    public function executeSyncGpsBuddyAddress() {

      $request_vars = $this->data->getData();

      $go = true;
      $truckId = $request_vars['truckId'];
      $addressId = $request_vars['addressId'];
      $address = CrmAddresses::find_by(["addressId" => $addressId]);
      if (!$address) {
        RestUtils::sendResponseError("Onbekend addres");
      }

      logToFile("gpsbuddy", "executeSyncGpsBuddyPoint start: " . $truckId . " " . $addressId);

      $subject = "Ophalen container: " . $address->domestic;
      $text = $address->getAddressFormatted(", ");
      $longitude = $address->longitude;
      $latitude = $address->latitude;
      if ($address->companyId != "") {
        $company = CrmCompanies::find_by(["companyId" => $address->companyId]);
        if ($company) {
          $text = $company->name . " " . $text;
        }
      }

      if (!DEVELOPMENT) {
        $gpsbuddy = GpsbuddyApi::getInstance();
        $gpsbuddy->init();
        foreach (GpsbuddyTrucks::getActiveTrucks() as $tr) {
          $gpsbuddy->addTruck($tr->truckId, $tr->gpsbuddyId);
        }
        if ($gpsbuddy->login()) {
          $gpsbuddy->addTask($truckId, $subject, $text, $longitude, $latitude);
        }
      }

      logToFile("gpsbuddy", "executeSyncGpsBuddyPoint done");

      RestUtils::sendResponseOK("GPSBUDDY SYNC DONE");

    }

  }