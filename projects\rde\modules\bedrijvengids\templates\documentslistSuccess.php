<?php
  TemplateHelper::includePartial('_tabs.php', 'sandbox');
  include("_header.php")
?>

<?php if ($files): ?>
  <table class="default_table default_table_center">
    <tr class="dataTableHeadingRow">
      <td>Omschrijving</td>
      <td>Datum</td>
      <td>Opmerking</td>
      <td>Delen</td>
      <td>PDF</td>
      <td>Bewerken</td>
      <td>Verwijderen</td>
    </tr>
    <?php
      /** @var Files $file */
      foreach($files as $file): ?>
    <tr class="dataTableRow trhover">
      <td><?php echo $file->title ?></td>
      <td><?php echo $file->getDocumentdate() ?></td>
      <td><?php echo $file->notes ?></td>
      <td><?php echo $file->showInDocuments == 1 ? 'Ja' : 'Nee' ?></td>
      <td><?php echo BtnHelper::getPrintPDF('?action=viewpdf&fileid=' . $file->fileId, __('Bewerk document')) ?></td>
      <td><?php echo BtnHelper::getEdit('?action=documentedit&fileid=' . $file->fileId, __('Bewerk document')) ?></td>
      <td><?php echo BtnHelper::getRemove('?action=documentdelete&fileid=' . $file->fileId, __('Verwijder document')) ?></td>
    </tr>
    <?php endforeach; ?>
  </table>
<?php else: ?>
  <h3>Er zijn geen documenten gevonden bij dit bedrijf.</h3>
<?php endif; ?>

